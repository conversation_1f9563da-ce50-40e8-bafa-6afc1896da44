import { NextResponse } from "next/server"
import { checkDatabaseHealth } from "@/lib/db-health"

export async function GET() {
  try {
    const isHealthy = await checkDatabaseHealth()

    if (isHealthy) {
      return NextResponse.json({
        status: "healthy",
        message: "Database connection is healthy"
      })
    } else {
      return NextResponse.json(
        {
          status: "unhealthy",
          message: "Database connection is not responding"
        },
        { status: 503 }
      )
    }
  } catch (error) {
    console.error("Error checking database health:", error)
    return NextResponse.json(
      {
        status: "error",
        message: `Error checking database health: ${error instanceof Error ? error.message : "Unknown error"}`
      },
      { status: 500 }
    )
  }
}
