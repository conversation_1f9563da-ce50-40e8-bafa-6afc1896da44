// scripts/migrate-sample-class.js
require('dotenv').config({ path: '.env.local' });
const { drizzle } = require('drizzle-orm/postgres-js');
const { sql } = require('drizzle-orm');
const postgres = require('postgres');

async function runMigration() {
  try {
    // Create database connection
    const connectionString = process.env.DATABASE_URL;
    console.log('Connecting to database...');
    
    // Create a connection with a longer timeout
    const client = postgres(connectionString, { 
      max: 1,
      connect_timeout: 30, // 30 seconds timeout
      idle_timeout: 30 // 30 seconds idle timeout
    });
    
    const db = drizzle(client);
    console.log('Connected to database');
    
    // Check if the column already exists
    console.log('Checking if sample_class column exists...');
    const checkResult = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'samples' AND column_name = 'sample_class'
    `);
    
    if (checkResult.length === 0) {
      console.log('Adding sample_class column to samples table...');
      
      // Add the column
      await db.execute(sql`
        ALTER TABLE samples 
        ADD COLUMN sample_class TEXT NOT NULL DEFAULT 'exploratory'
      `);
      
      // Add a comment to the column
      await db.execute(sql`
        COMMENT ON COLUMN samples.sample_class 
        IS 'Classification of sample: exploratory, batch, sequential'
      `);
      
      console.log('Migration completed successfully!');
    } else {
      console.log('sample_class column already exists, no migration needed');
    }
    
    // Close the connection
    await client.end();
    console.log('Database connection closed');
    
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

runMigration();
