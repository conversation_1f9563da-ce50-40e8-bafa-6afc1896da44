// lib/constraints/defaults.ts
// Default constraint configurations and presets

import { Constraint, SamplingOptions } from "./types"
import { createConstraint } from "./builders"

/**
 * Default sampling options for constraint-aware sampling
 */
export const DEFAULT_SAMPLING_OPTIONS: SamplingOptions = {
  strategy: "LHS",
  nSamples: 10,
  respectConstraints: true,
  maxAttempts: 1000,
  tolerance: 1e-6
}

/**
 * Default constraint configurations for different scenarios
 */
export const DEFAULT_CONSTRAINT_CONFIGS = {
  /**
   * Chemical synthesis constraints
   */
  chemical_synthesis: {
    mixture_constraint: {
      type: "ContinuousLinearConstraint",
      name: "Mixture Ratios",
      description: "All components must sum to 100%",
      operator: "=",
      rhs: 1.0,
      coefficients: [] // Will be filled based on number of components
    },
    temperature_safety: {
      type: "ContinuousLinearConstraint",
      name: "Temperature Safety",
      description: "Temperature must stay within safe operating range",
      operator: "<=",
      rhs: 200.0,
      coefficients: [1.0]
    },
    concentration_limit: {
      type: "DiscreteSumConstraint",
      name: "Total Concentration",
      description: "Sum of all concentrations must not exceed limit",
      condition: {
        type: "threshold",
        threshold: 100,
        operator: "<="
      }
    }
  },

  /**
   * Materials science constraints
   */
  materials_science: {
    component_limit: {
      type: "ContinuousCardinalityConstraint",
      name: "Component Limit",
      description: "Limit number of active components",
      max_cardinality: 3
    },
    thermal_stability: {
      type: "DiscreteExcludeConstraint",
      name: "Thermal Stability",
      description: "Exclude high temperature with sensitive materials",
      combiner: "AND",
      conditions: [
        {
          type: "threshold",
          threshold: 150,
          operator: ">"
        }
      ]
    }
  },

  /**
   * Process optimization constraints
   */
  process_optimization: {
    flow_balance: {
      type: "ContinuousLinearConstraint",
      name: "Flow Balance",
      description: "Input and output flows must be balanced",
      operator: "=",
      rhs: 0.0,
      coefficients: [1.0, -1.0]
    },
    energy_efficiency: {
      type: "ContinuousLinearConstraint",
      name: "Energy Efficiency",
      description: "Energy consumption within efficiency limits",
      operator: "<=",
      rhs: 100.0,
      coefficients: [1.0, -0.5]
    },
    resource_limit: {
      type: "ContinuousLinearConstraint",
      name: "Resource Limit",
      description: "Total resource consumption limit",
      operator: "<=",
      rhs: 1000.0,
      coefficients: [] // Will be filled based on resources
    }
  },

  /**
   * Economic optimization constraints
   */
  economic_optimization: {
    budget_constraint: {
      type: "ContinuousLinearConstraint",
      name: "Budget Constraint",
      description: "Total cost must not exceed budget",
      operator: "<=",
      rhs: 10000.0,
      coefficients: [] // Will be filled based on cost factors
    },
    roi_minimum: {
      type: "ContinuousLinearConstraint",
      name: "ROI Minimum",
      description: "Return on investment must exceed minimum",
      operator: ">=",
      rhs: 0.15,
      coefficients: [1.0, -1.0]
    }
  }
}

/**
 * Default constraint validation settings
 */
export const DEFAULT_VALIDATION_SETTINGS = {
  enableRealTimeValidation: true,
  validationTimeout: 5000, // 5 seconds
  showWarnings: true,
  strictMode: false,
  tolerance: 1e-6
}

/**
 * Default constraint UI settings
 */
export const DEFAULT_UI_SETTINGS = {
  showConstraintCategories: true,
  showConstraintTemplates: true,
  showValidationFeedback: true,
  showFeasibilityEstimate: true,
  autoExpandConstraints: false,
  maxConstraintsPerPage: 10
}

/**
 * Create default constraints for a scenario
 */
export function createDefaultConstraints(
  scenario: keyof typeof DEFAULT_CONSTRAINT_CONFIGS,
  parameters: string[]
): Constraint[] {
  const configs = DEFAULT_CONSTRAINT_CONFIGS[scenario]
  const constraints: Constraint[] = []

  switch (scenario) {
    case "chemical_synthesis":
      // Create mixture constraint if we have multiple components
      if (parameters.length >= 2) {
        const mixtureConstraint = createConstraint(
          "ContinuousLinearConstraint",
          parameters.slice(0, Math.min(parameters.length, 4)), // Use up to 4 components
          "Mixture Ratios"
        ) as Constraint & { type: "ContinuousLinearConstraint" }

        mixtureConstraint.operator = "="
        mixtureConstraint.coefficients = mixtureConstraint.parameters.map(
          () => 1.0
        )
        mixtureConstraint.rhs = 1.0
        mixtureConstraint.description = "All components must sum to 100%"

        constraints.push(mixtureConstraint)
      }
      break

    case "materials_science":
      // Create component limit constraint
      if (parameters.length >= 3) {
        const componentLimit = createConstraint(
          "ContinuousCardinalityConstraint",
          parameters,
          "Component Limit"
        ) as Constraint & { type: "ContinuousCardinalityConstraint" }

        componentLimit.max_cardinality = Math.min(3, parameters.length - 1)
        componentLimit.description = "Limit number of active components"

        constraints.push(componentLimit)
      }
      break

    case "process_optimization":
      // Create flow balance if we have flow parameters
      const flowParams = parameters.filter(
        p =>
          p.toLowerCase().includes("flow") ||
          p.toLowerCase().includes("input") ||
          p.toLowerCase().includes("output")
      )

      if (flowParams.length >= 2) {
        const flowBalance = createConstraint(
          "ContinuousLinearConstraint",
          flowParams.slice(0, 2),
          "Flow Balance"
        ) as Constraint & { type: "ContinuousLinearConstraint" }

        flowBalance.operator = "="
        flowBalance.coefficients = [1.0, -1.0]
        flowBalance.rhs = 0.0
        flowBalance.description = "Input and output flows must be balanced"

        constraints.push(flowBalance)
      }
      break

    case "economic_optimization":
      // Create budget constraint
      if (parameters.length >= 2) {
        const budgetConstraint = createConstraint(
          "ContinuousLinearConstraint",
          parameters.slice(0, Math.min(parameters.length, 3)),
          "Budget Constraint"
        ) as Constraint & { type: "ContinuousLinearConstraint" }

        budgetConstraint.operator = "<="
        budgetConstraint.coefficients = budgetConstraint.parameters.map(
          () => 100.0
        ) // Default cost per unit
        budgetConstraint.rhs = 10000.0
        budgetConstraint.description = "Total cost must not exceed budget"

        constraints.push(budgetConstraint)
      }
      break
  }

  return constraints
}

/**
 * Get recommended sampling strategy based on constraints
 */
export function getRecommendedSamplingStrategy(
  constraintCount: number,
  parameterCount: number,
  hasComplexConstraints: boolean = false
): SamplingOptions {
  const baseOptions = { ...DEFAULT_SAMPLING_OPTIONS }

  // Adjust strategy based on problem characteristics
  if (constraintCount === 0) {
    // No constraints - use standard sampling
    baseOptions.respectConstraints = false
    baseOptions.strategy = parameterCount <= 6 ? "LHS" : "sobol"
  } else if (constraintCount <= 2 && !hasComplexConstraints) {
    // Simple constraints - use LHS with constraint awareness
    baseOptions.strategy = "LHS"
    baseOptions.maxAttempts = 500
  } else {
    // Complex constraints - use more robust sampling
    baseOptions.strategy = "sobol"
    baseOptions.maxAttempts = 2000
    baseOptions.tolerance = 1e-8
  }

  // Adjust sample count based on dimensionality
  if (parameterCount <= 3) {
    baseOptions.nSamples = 10
  } else if (parameterCount <= 6) {
    baseOptions.nSamples = 20
  } else {
    baseOptions.nSamples = Math.min(50, parameterCount * 5)
  }

  return baseOptions
}

/**
 * Get constraint difficulty level
 */
export function getConstraintDifficulty(
  constraints: Constraint[]
): "beginner" | "intermediate" | "advanced" {
  if (constraints.length === 0) return "beginner"

  const complexTypes = [
    "DiscreteDependenciesConstraint",
    "DiscreteCustomConstraint",
    "DiscretePermutationInvarianceConstraint"
  ]

  const intermediateTypes = [
    "ContinuousCardinalityConstraint",
    "DiscreteCardinalityConstraint",
    "DiscreteSumConstraint",
    "DiscreteProductConstraint"
  ]

  const hasComplex = constraints.some(c => complexTypes.includes(c.type))
  const hasIntermediate = constraints.some(c =>
    intermediateTypes.includes(c.type)
  )

  if (hasComplex || constraints.length > 5) return "advanced"
  if (hasIntermediate || constraints.length > 2) return "intermediate"
  return "beginner"
}

/**
 * Get constraint performance hints
 */
export function getConstraintPerformanceHints(
  constraints: Constraint[],
  parameters: string[]
): string[] {
  const hints: string[] = []

  if (constraints.length > 5) {
    hints.push(
      "Consider reducing the number of constraints for better performance"
    )
  }

  const linearConstraints = constraints.filter(
    c => c.type === "ContinuousLinearConstraint"
  )
  if (linearConstraints.length > 3) {
    hints.push(
      "Multiple linear constraints may create a highly constrained space"
    )
  }

  const cardinalityConstraints = constraints.filter(
    c =>
      c.type === "ContinuousCardinalityConstraint" ||
      c.type === "DiscreteCardinalityConstraint"
  )
  if (cardinalityConstraints.length > 1) {
    hints.push(
      "Multiple cardinality constraints may conflict - verify compatibility"
    )
  }

  const customConstraints = constraints.filter(
    c => c.type === "DiscreteCustomConstraint"
  )
  if (customConstraints.length > 0) {
    hints.push(
      "Custom constraints may slow down sampling - consider simplification"
    )
  }

  if (constraints.length > 0 && parameters.length > 10) {
    hints.push(
      "High-dimensional problems with constraints may require more sampling attempts"
    )
  }

  return hints
}
