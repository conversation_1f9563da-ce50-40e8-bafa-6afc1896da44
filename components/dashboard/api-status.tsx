"use client"

import { useEffect, useState, useRef } from "react"
import { Activity, Server, Cpu, RefreshCw } from "lucide-react"
import { checkAPIHealthAction } from "@/lib/api/baybe-client"
import { cn } from "@/lib/utils"
import { eventEmitter, EVENTS } from "@/lib/events"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { Button } from "@/components/ui/button"

interface ApiStatusProps {
  isCollapsed: boolean
}

export function ApiStatus({ isCollapsed }: ApiStatusProps) {
  const [apiStatus, setApiStatus] = useState<{
    isSuccess: boolean
    data?: {
      status: string
      using_gpu: boolean
      gpu_info?: {
        name: string
        memory_allocated_mb: number
        memory_reserved_mb: number
        device_count: number
      }
    }
  }>({
    isSuccess: false
  })

  const [loading, setLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isGpuOperationActive, setIsGpuOperationActive] = useState(false)
  const normalIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const activeOperationIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Normal refresh interval (30 seconds)
  const NORMAL_REFRESH_INTERVAL = 30000
  // Active operation refresh interval (3 seconds)
  const ACTIVE_OPERATION_REFRESH_INTERVAL = 3000

  // Function to check API health
  const checkHealth = async () => {
    try {
      setIsRefreshing(true)
      const result = await checkAPIHealthAction()
      setApiStatus(result)
    } catch (error) {
      console.error("Error checking API health:", error)
      setApiStatus({ isSuccess: false })
    } finally {
      setIsRefreshing(false)
      setLoading(false)
    }
  }

  // Function to manually refresh
  const handleManualRefresh = () => {
    checkHealth()
  }

  // Set up normal refresh interval
  useEffect(() => {
    checkHealth()

    // Set up interval to refresh every 30 seconds
    normalIntervalRef.current = setInterval(
      checkHealth,
      NORMAL_REFRESH_INTERVAL
    )

    // Clean up interval on unmount
    return () => {
      if (normalIntervalRef.current) {
        clearInterval(normalIntervalRef.current)
      }
    }
  }, [])

  // Listen for events to trigger updates
  useEffect(() => {
    // Listen for manual update requests
    const updateRequestListener = () => {
      checkHealth()
    }

    // Listen for GPU-intensive operations starting
    const operationStartListener = () => {
      setIsGpuOperationActive(true)

      // Clear normal interval and set up faster interval
      if (normalIntervalRef.current) {
        clearInterval(normalIntervalRef.current)
      }

      // Immediately check status
      checkHealth()

      // Set up faster interval for active operations
      activeOperationIntervalRef.current = setInterval(
        checkHealth,
        ACTIVE_OPERATION_REFRESH_INTERVAL
      )
    }

    // Listen for GPU-intensive operations ending
    const operationEndListener = () => {
      setIsGpuOperationActive(false)

      // Clear active operation interval
      if (activeOperationIntervalRef.current) {
        clearInterval(activeOperationIntervalRef.current)
        activeOperationIntervalRef.current = null
      }

      // Check status one more time
      checkHealth()

      // Restore normal interval
      if (!normalIntervalRef.current) {
        normalIntervalRef.current = setInterval(
          checkHealth,
          NORMAL_REFRESH_INTERVAL
        )
      }
    }

    // Subscribe to events
    const updateRequestUnsubscribe = eventEmitter.on(
      EVENTS.API_STATUS_UPDATE_REQUESTED,
      updateRequestListener
    )

    const operationStartUnsubscribe = eventEmitter.on(
      EVENTS.GPU_INTENSIVE_OPERATION_STARTED,
      operationStartListener
    )

    const operationEndUnsubscribe = eventEmitter.on(
      EVENTS.GPU_INTENSIVE_OPERATION_ENDED,
      operationEndListener
    )

    // Clean up event listeners
    return () => {
      updateRequestUnsubscribe()
      operationStartUnsubscribe()
      operationEndUnsubscribe()

      // Also clean up any intervals
      if (normalIntervalRef.current) {
        clearInterval(normalIntervalRef.current)
      }

      if (activeOperationIntervalRef.current) {
        clearInterval(activeOperationIntervalRef.current)
      }
    }
  }, [])

  const apiAvailable = apiStatus.isSuccess
  const usingGPU = apiStatus.isSuccess && apiStatus.data?.using_gpu

  // Create tooltip content for collapsed view
  const tooltipContent = (
    <div className="space-y-1 text-xs">
      <div className="font-medium">INNOptimizer™ API Status</div>
      <div className="flex items-center">
        <div
          className={cn(
            "mr-1 size-2 rounded-full",
            isRefreshing
              ? "animate-pulse bg-blue-500"
              : loading
                ? "bg-yellow-500"
                : apiAvailable
                  ? "bg-green-500"
                  : "bg-red-500"
          )}
        ></div>
        <span>
          {isRefreshing
            ? "Refreshing..."
            : loading
              ? "Checking..."
              : apiAvailable
                ? "Connected"
                : "Disconnected"}
        </span>
      </div>

      <div className="text-muted-foreground pt-1 text-center text-[10px]">
        {isRefreshing ? "Updating..." : "Click to refresh"}
      </div>
    </div>
  )

  return (
    <div className={cn("border-t p-2", isCollapsed ? "text-center" : "")}>
      {isCollapsed ? (
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                className="flex cursor-pointer flex-col items-center space-y-2"
                onClick={handleManualRefresh}
              >
                <Server
                  className={cn(
                    "size-4",
                    isGpuOperationActive && "text-blue-500"
                  )}
                />
                <div
                  className={cn(
                    "size-2 rounded-full",
                    isRefreshing
                      ? "animate-pulse bg-blue-500"
                      : loading
                        ? "bg-yellow-500"
                        : apiAvailable
                          ? "bg-green-500"
                          : "bg-red-500"
                  )}
                ></div>
              </div>
            </TooltipTrigger>
            <TooltipContent side="right">{tooltipContent}</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : (
        <>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center">
                <Server
                  className={cn(
                    "mr-1 size-4",
                    isGpuOperationActive && "text-blue-500"
                  )}
                />
                <span className="text-xs font-medium">INNOptimizer™ API</span>
              </div>

              <div className="flex items-center">
                <div
                  className={cn(
                    "size-2 rounded-full",
                    isRefreshing
                      ? "animate-pulse bg-blue-500"
                      : loading
                        ? "bg-yellow-500"
                        : apiAvailable
                          ? "bg-green-500"
                          : "bg-red-500"
                  )}
                ></div>
                <span className="ml-1 text-xs">
                  {isRefreshing
                    ? "Refreshing..."
                    : loading
                      ? "Checking..."
                      : apiAvailable
                        ? "Connected"
                        : "Disconnected"}
                </span>
              </div>
            </div>

            <Button
              variant="ghost"
              size="icon"
              className="size-6"
              onClick={handleManualRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw
                className={cn("size-3", isRefreshing && "animate-spin")}
              />
              <span className="sr-only">Refresh</span>
            </Button>
          </div>
        </>
      )}
    </div>
  )
}
