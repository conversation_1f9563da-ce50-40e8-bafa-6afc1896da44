// app/dashboard/optimizations/demo-run/run/page.tsx
"use server"

import { But<PERSON> } from "@/components/ui/button"
import { RunExperimentsDemo } from "@/components/demo/run-experiments-demo"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { DashboardThemeWrapper } from "@/components/dashboard/dashboard-theme-wrapper"

interface RunExperimentsDemoPageProps {
  searchParams: Promise<{ demo?: string; guided?: string }>
}

export default async function RunExperimentsDemoPage({
  searchParams
}: RunExperimentsDemoPageProps) {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  if (!userId) {
    redirect("/login?redirect_url=/dashboard/optimizations/demo-run/run")
  }

  const params = await searchParams
  const isDemo = params.demo === "true"
  const isGuided = params.guided === "true"

  // If not in demo mode, redirect to regular optimizations
  if (!isDemo) {
    redirect("/dashboard/optimizations")
  }

  return (
    <DashboardThemeWrapper>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/dashboard/home">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-1 size-4" />
              Back to Dashboard
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Run Experiments Demo</h1>
        </div>
      </div>

      <RunExperimentsDemo />
    </DashboardThemeWrapper>
  )
}
