/*
This server layout provides the academic survey layout with authentication and verification checks.
It includes a restricted header that prevents navigation to other areas during survey completion.
*/

"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"
import { checkAcademicVerificationStatusAction } from "@/actions/academic-verification-actions"
import AcademicSurveyHeader from "@/components/survey/academic-survey-header"
import { AcademicSurveyInterceptor } from "@/components/survey/navigation-interceptor"

export default async function AcademicSurveyLayout({
  children
}: {
  children: React.ReactNode
}) {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🔍 ACADEMIC SURVEY LAYOUT - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  })

  // If user is not authenticated, redirect to login
  if (!userId) {
    redirect("/login?redirect_url=/academic-survey")
  }

  // Check if the user has been verified
  const verificationStatus = await checkAcademicVerificationStatusAction()

  // If the user hasn't been verified, redirect to the academic signup page
  if (
    !verificationStatus.isSuccess ||
    !verificationStatus.status ||
    verificationStatus.status !== "approved"
  ) {
    redirect("/academic-signup")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <AcademicSurveyInterceptor />
      <AcademicSurveyHeader />
      <main className="flex-1">{children}</main>
    </div>
  )
}
