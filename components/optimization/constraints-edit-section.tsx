"use client"

import { ConstraintManager } from "@/components/constraints/constraint-manager"
import { Constraint, Parameter } from "@/lib/constraints"

interface ParameterConfig {
  name: string
  type: "NumericalContinuous" | "NumericalDiscrete" | "CategoricalParameter"
  bounds?: [number, number]
  values?: (number | string)[]
  encoding?: string
  tolerance?: number
}

interface ConstraintsEditSectionProps {
  constraints: Constraint[]
  availableParameters: string[] | ParameterConfig[]
  onSave: (updatedConstraints: Constraint[]) => void
  onCancel: () => void
  className?: string
}

export function ConstraintsEditSection({
  constraints,
  availableParameters,
  onSave,
  onCancel,
  className = ""
}: ConstraintsEditSectionProps) {
  // Convert availableParameters to Parameter objects with proper type information
  const parameters: Parameter[] = availableParameters.map(param => {
    if (typeof param === "string") {
      // Fallback for legacy string-only parameter names
      return {
        name: param,
        type: "NumericalContinuous" as const,
        bounds: [0, 1]
      }
    } else {
      // Use actual parameter configuration with proper type casting
      const baseParam = {
        name: param.name,
        type: param.type,
        tolerance: param.tolerance,
        encoding: param.encoding as "OHE" | "LE" | undefined
      }

      // Handle bounds for continuous parameters
      if (param.type === "NumericalContinuous" && param.bounds) {
        return {
          ...baseParam,
          bounds: param.bounds
        }
      }

      // Handle values for discrete/categorical parameters with proper type casting
      if (
        (param.type === "NumericalDiscrete" ||
          param.type === "CategoricalParameter") &&
        param.values
      ) {
        const values =
          param.type === "NumericalDiscrete"
            ? param.values
                .map(v => (typeof v === "number" ? v : parseFloat(v)))
                .filter(v => !isNaN(v))
            : param.values.map(v => String(v))

        return {
          ...baseParam,
          values: values as number[] | string[]
        }
      }

      return baseParam
    }
  })

  return (
    <div className={className}>
      <ConstraintManager
        constraints={constraints}
        availableParameters={parameters}
        onSave={onSave}
        onCancel={onCancel}
        readOnly={false}
      />
    </div>
  )
}
