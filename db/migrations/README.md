# Database Migrations

This directory contains database migrations for the Optimizer application.

## Migration History

- `0000_flaky_doctor_spectrum.sql`: Initial schema setup with profiles and measurements tables
- `0001_add_target_values_column.sql`: Added target_values column to measurements table
- `0002_add_batch_id_column.sql`: Added batch_id column to measurements table
- `0003_add_user_surveys_table.sql`: Added user_surveys table for storing signup survey responses

## Running Migrations

There are several ways to run migrations:

### Using Drizzle Kit

```bash
# Push all schema changes to the database
npm run db:push

# Generate new migrations
npm run db:generate

# Apply migrations
npm run db:migrate
```

### Using Custom Scripts

For specific migrations, you can use the custom scripts in the `scripts` directory:

```bash
# Run a specific migration
node scripts/run-user-surveys-migration.js
```

## Database Schema Changes

When changing database providers or setting up a new database, ensure all migrations are applied in order. The `schema.ts` file contains the complete database schema and can be used as a reference.

## Adding New Migrations

1. Create a new SQL migration file in this directory
2. Update the `_journal.json` file in the `meta` directory
3. Update the `schema.ts` file to include the new schema changes
4. Document the migration in this README
