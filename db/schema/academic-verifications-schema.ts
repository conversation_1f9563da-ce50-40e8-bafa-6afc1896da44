/*
Defines the database schema for academic verification requests.
*/

import { pgTable, text, timestamp } from "drizzle-orm/pg-core"

export const academicVerificationsTable = pgTable("academic_verifications", {
  userId: text("user_id").primaryKey().notNull(),
  fullName: text("full_name").notNull(),
  email: text("email").notNull(),
  institutionalEmail: text("institutional_email").notNull(),
  institution: text("institution").notNull(),
  role: text("role").notNull(),
  verificationStatus: text("verification_status").default("pending").notNull(),
  verificationMethod: text("verification_method"),
  verificationTimestamp: timestamp("verification_timestamp"),
  rejectionReason: text("rejection_reason"),
  verificationToken: text("verification_token"),
  verificationTokenExpiry: timestamp("verification_token_expiry"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date())
})

export type InsertAcademicVerification =
  typeof academicVerificationsTable.$inferInsert
export type SelectAcademicVerification =
  typeof academicVerificationsTable.$inferSelect
