/**
 * Auth utilities to handle custom session claims
 * Fixes the issue where Clerk's auth() returns the Clerk ID instead of our custom external ID
 */

import { auth } from "@clerk/nextjs/server"

/**
 * Get the correct user ID from Clerk session
 * Uses custom session claims if available, falls back to default Clerk ID
 */
export async function getActualUserId(): Promise<string | null> {
  const authResult = await auth()
  const { userId, sessionClaims } = authResult

  // Use custom userId from session claims if available, otherwise fall back to Clerk ID
  const actualUserId = (sessionClaims?.userId as string) || userId

  return actualUserId
}

/**
 * Get the full auth result with corrected user ID
 */
export async function getAuthWithCorrectUserId() {
  const authResult = await auth()
  const actualUserId = await getActualUserId()

  return {
    ...authResult,
    userId: actualUserId,
    originalUserId: authResult.userId,
    isUsingCustomClaims: !!authResult.sessionClaims?.userId
  }
}
