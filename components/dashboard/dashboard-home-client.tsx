"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { useLoading } from "@/contexts/loading-context"
import { ArrowRight, BarChart3 } from "lucide-react"

export function DashboardHomeClient() {
  const router = useRouter()
  const { startLoading } = useLoading()

  // Handle navigation with loading overlay
  const handleNavigateWithLoading = (path: string) => {
    startLoading()

    // Add a small delay before navigation to ensure the loading overlay appears
    setTimeout(() => {
      router.push(path)
    }, 100) // 100ms delay (reduced from 300ms)
  }

  return (
    <Card className="from-primary/5 to-background border bg-gradient-to-br shadow-md">
      <CardHeader>
        <CardTitle className="flex items-center text-xl">
          <BarChart3 className="text-primary mr-2 size-6" />
          Optimization Dashboard
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="mb-4 md:mb-0 md:max-w-[60%]">
            <p className="text-muted-foreground mb-2">
              Create and manage your optimization experiments. Track progress,
              analyze results, and improve your research outcomes with our
              powerful optimization tools.
            </p>
            <p className="text-muted-foreground text-sm">
              Access all your experiments, create new optimizations, and
              visualize your results in one place.
            </p>
          </div>
          <Button
            onClick={() =>
              handleNavigateWithLoading("/dashboard/optimizations")
            }
          >
            <ArrowRight className="mr-2 size-4" />
            Go to Optimizations
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
