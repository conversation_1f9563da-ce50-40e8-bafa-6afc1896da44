"use client"

import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { <PERSON>Chart, BarChart3, Git<PERSON>ranch, Layers } from "lucide-react"
import { Button } from "@/components/ui/button"

interface OverviewDashboardProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  onNavigateToTab: (tab: string, section?: string) => void
}

export function OverviewDashboard({
  optimization,
  measurements,
  onNavigateToTab
}: OverviewDashboardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <AreaChart className="text-primary mr-2 size-5" />
          Analysis Tools
        </CardTitle>
        <CardDescription>
          Explore different analysis methods for your optimization
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Analysis Navigation Cards */}
          <Card
            className="hover:bg-muted/50 cursor-pointer transition-colors"
            onClick={() => onNavigateToTab("analysis", "parameters")}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-md flex items-center">
                <BarChart3 className="text-primary mr-2 size-4" />
                Parameter Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                Understand parameter relationships and importance
              </p>
              <Button
                variant="link"
                className="mt-2 h-auto p-0 text-xs"
                onClick={e => {
                  e.stopPropagation()
                  onNavigateToTab("analysis", "parameters")
                }}
              >
                Explore parameter analysis
              </Button>
            </CardContent>
          </Card>

          <Card
            className="hover:bg-muted/50 cursor-pointer transition-colors"
            onClick={() => onNavigateToTab("analysis", "surface")}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-md flex items-center">
                <Layers className="text-primary mr-2 size-4" />
                Surface Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                Visualize how parameters affect target variables
              </p>
              <Button
                variant="link"
                className="mt-2 h-auto p-0 text-xs"
                onClick={e => {
                  e.stopPropagation()
                  onNavigateToTab("analysis", "surface")
                }}
              >
                Explore surface analysis
              </Button>
            </CardContent>
          </Card>

          <Card
            className="hover:bg-muted/50 cursor-pointer transition-colors"
            onClick={() => onNavigateToTab("analysis", "multi-target")}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-md flex items-center">
                <GitBranch className="text-primary mr-2 size-4" />
                Multi-Target Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                Understand trade-offs between multiple objectives
              </p>
              <Button
                variant="link"
                className="mt-2 h-auto p-0 text-xs"
                onClick={e => {
                  e.stopPropagation()
                  onNavigateToTab("analysis", "multi-target")
                }}
              >
                Explore multi-target analysis
              </Button>
            </CardContent>
          </Card>

          <Card
            className="hover:bg-muted/50 cursor-pointer transition-colors"
            onClick={() => onNavigateToTab("analysis", "advanced")}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-md flex items-center">
                <AreaChart className="text-primary mr-2 size-4" />
                Advanced Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                Explore sophisticated analysis techniques
              </p>
              <Button
                variant="link"
                className="mt-2 h-auto p-0 text-xs"
                onClick={e => {
                  e.stopPropagation()
                  onNavigateToTab("analysis", "advanced")
                }}
              >
                Explore advanced analytics
              </Button>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  )
}
