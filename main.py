import psycopg2
from dotenv import load_dotenv
import os

load_dotenv()

# Connect to the database
try:
    connection = psycopg2.connect(
        dsn=os.getenv("DATABASE_URL"),
        connect_timeout=60
    )

    print("Connection successful!")
    
    cursor = connection.cursor()
    cursor.execute("SELECT NOW();")
    result = cursor.fetchone()
    print("Current Time:", result)

    cursor.close()
    connection.close()
    print("Connection closed.")

except Exception as e:
    print(f"Failed to connect: {e}")
