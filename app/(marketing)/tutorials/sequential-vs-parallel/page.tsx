import { <PERSON><PERSON><PERSON> } from "next"
import { BRAND } from "@/lib/constants"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import OptimizationStrategy3DWrapper from "@/components/tutorials/optimization-strategy-3d-wrapper"

export const metadata: Metadata = {
  title: `Sequential or Parallel? What strategy should I use when running Bayesian Optimization? | ${BRAND.NAME}`,
  description:
    "Learn about sequential and parallel strategies for Bayesian Optimization and which approach is best for your needs."
}

export default async function SequentialVsParallelPage() {
  return (
    <div className="container mx-auto max-w-4xl px-4 py-12">
      <Link
        href="/tutorials"
        className="text-muted-foreground hover:text-foreground mb-8 flex items-center gap-1 text-sm transition-colors"
      >
        <ArrowLeft className="size-4" />
        Back to Tutorials
      </Link>

      <h1 className="mb-6 text-4xl font-bold">
        Sequential or Parallel? What strategy should I use when running Bayesian
        Optimization?
      </h1>

      <div className="prose prose-slate dark:prose-invert max-w-none">
        <h2>Understanding Bayesian Optimization</h2>
        <p>
          Bayesian optimization is a powerful technique for optimizing
          expensive-to-evaluate functions, commonly used in experimental design.
          It constructs a probabilistic model—typically a Gaussian process—of
          the target function based on observed data.
        </p>
        <p>
          This model guides the selection of new points to evaluate by
          optimizing an acquisition function (e.g., expected improvement or
          upper confidence bound), balancing exploration (searching uncertain
          regions) and exploitation (refining known promising areas).
        </p>
        <p>
          Your goal is to find the global optimum efficiently, which could mean
          minimizing the number of function evaluations (sample efficiency) or
          reducing total time (wall-clock efficiency), depending on your
          priorities. The interactive 3D visualization below demonstrates how
          different optimization strategies navigate this challenge.
        </p>

        <OptimizationStrategy3DWrapper />

        <h2>Strategy 1: Sequential Approach</h2>
        <p>
          In the sequential approach, you begin with 10 initial samples from
          Latin hypercube sampling. Then, you perform one experiment at a time:
          after each experiment, you obtain the result, update the Gaussian
          process model with this new data, and use the updated model to suggest
          the next experiment. This process repeats, with each suggestion
          informed by all prior observations. You can see this sequential
          sampling behavior by selecting the "Sequential" strategy in the 3D
          visualization above and using the step slider to observe how points
          are added one at a time.
        </p>

        <div className="bg-card my-8 rounded-lg border p-6 shadow-sm">
          <h3 className="mb-4 text-xl font-semibold">
            Sequential Strategy Visualization
          </h3>
          <div className="overflow-x-auto">
            <div className="flex min-w-[600px] flex-col space-y-4">
              {/* Initial samples */}
              <div className="flex items-center">
                <div className="w-32 font-medium">Initial Samples:</div>
                <div className="flex space-x-2">
                  {Array(10)
                    .fill(0)
                    .map((_, i) => (
                      <div
                        key={i}
                        className="flex size-10 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-200"
                      >
                        {i + 1}
                      </div>
                    ))}
                </div>
              </div>

              {/* Sequential steps */}
              {Array(5)
                .fill(0)
                .map((_, i) => (
                  <div key={i} className="flex items-center">
                    <div className="w-32 font-medium">Step {i + 1}:</div>
                    <div className="flex items-center space-x-2">
                      <div className="flex size-10 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-200">
                        {11 + i}
                      </div>
                      <div className="text-muted-foreground">
                        → Update model → Suggest next point
                      </div>
                    </div>
                  </div>
                ))}

              {/* Timeline */}
              <div className="mt-6 flex flex-col">
                <div className="font-medium">
                  Timeline (assuming each experiment takes time t):
                </div>
                <div className="mt-2 h-8 w-full rounded-md bg-slate-100 dark:bg-slate-800">
                  <div className="flex h-full items-center">
                    {Array(10)
                      .fill(0)
                      .map((_, i) => (
                        <div
                          key={i}
                          className="relative flex-1 border-r border-slate-300 last:border-r-0 dark:border-slate-600"
                        >
                          <div className="absolute -bottom-6 left-1/2 -translate-x-1/2 text-xs">
                            t
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
                <div className="mt-8 text-sm text-slate-600 dark:text-slate-400">
                  Total time: 10t (sequential experiments) + initial setup time
                </div>
              </div>
            </div>
          </div>
        </div>

        <ul>
          <li>
            <strong>How it works:</strong> For a budget of 20 total evaluations,
            you would conduct the initial 10, followed by 10 sequential
            experiments, updating the model 10 times.
          </li>
          <li>
            <strong>Advantages:</strong> Each new point is selected using the
            most current model, incorporating all previous data. This makes the
            search highly directed, potentially leading to better sample
            efficiency—finding the global optimum with fewer function
            evaluations.
          </li>
          <li>
            <strong>Time consideration:</strong> If each experiment takes time
            t, the additional 10 experiments require 10t in total time, assuming
            no parallelism.
          </li>
        </ul>

        <h2>Strategy 2: Parallel Approach</h2>
        <p>
          In the parallel approach, you also start with 10 initial samples from
          Latin hypercube sampling. However, instead of one experiment at a
          time, you receive multiple suggestions (e.g., a batch of 5) to
          evaluate simultaneously. After running the batch, you update the model
          with all results at once and request the next batch. To visualize this
          approach, switch to the "Parallel" strategy in the 3D visualization
          and observe how points are added in batches rather than individually.
        </p>

        <div className="bg-card my-8 rounded-lg border p-6 shadow-sm">
          <h3 className="mb-4 text-xl font-semibold">
            Parallel Strategy Visualization
          </h3>
          <div className="overflow-x-auto">
            <div className="flex min-w-[600px] flex-col space-y-4">
              {/* Initial samples */}
              <div className="flex items-center">
                <div className="w-32 font-medium">Initial Samples:</div>
                <div className="flex space-x-2">
                  {Array(10)
                    .fill(0)
                    .map((_, i) => (
                      <div
                        key={i}
                        className="flex size-10 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-200"
                      >
                        {i + 1}
                      </div>
                    ))}
                </div>
              </div>

              {/* Batch 1 */}
              <div className="flex items-center">
                <div className="w-32 font-medium">Batch 1:</div>
                <div className="flex items-center">
                  <div className="flex space-x-2">
                    {Array(5)
                      .fill(0)
                      .map((_, i) => (
                        <div
                          key={i}
                          className="flex size-10 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-200"
                        >
                          {11 + i}
                        </div>
                      ))}
                  </div>
                  <div className="text-muted-foreground ml-4">
                    → Update model with all 5 results
                  </div>
                </div>
              </div>

              {/* Batch 2 */}
              <div className="flex items-center">
                <div className="w-32 font-medium">Batch 2:</div>
                <div className="flex items-center">
                  <div className="flex space-x-2">
                    {Array(5)
                      .fill(0)
                      .map((_, i) => (
                        <div
                          key={i}
                          className="flex size-10 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-200"
                        >
                          {16 + i}
                        </div>
                      ))}
                  </div>
                  <div className="text-muted-foreground ml-4">
                    → Update model with all 5 results
                  </div>
                </div>
              </div>

              {/* Timeline */}
              <div className="mt-6 flex flex-col">
                <div className="font-medium">
                  Timeline (assuming each experiment takes time t):
                </div>
                <div className="mt-2 h-8 w-full rounded-md bg-slate-100 dark:bg-slate-800">
                  <div className="flex h-full items-center">
                    {Array(2)
                      .fill(0)
                      .map((_, i) => (
                        <div
                          key={i}
                          className="relative flex-1 border-r border-slate-300 last:border-r-0 dark:border-slate-600"
                        >
                          <div className="absolute -bottom-6 left-1/2 -translate-x-1/2 text-xs">
                            t
                          </div>
                          <div className="absolute -top-6 left-1/2 -translate-x-1/2 text-xs">
                            Batch {i + 1}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
                <div className="mt-8 text-sm text-slate-600 dark:text-slate-400">
                  Total time: 2t (parallel batches) + initial setup time
                </div>
              </div>
            </div>
          </div>
        </div>

        <ul>
          <li>
            <strong>How it works:</strong> For a budget of 20 evaluations, you
            might do the initial 10, then two batches of 5. Each batch is
            evaluated in parallel, and the model is updated twice—once after
            each batch.
          </li>
          <li>
            <strong>Advantages:</strong> If your experimental setup allows
            parallel evaluations, and each experiment takes time t, a batch of 5
            takes only t (assuming perfect parallelism), so two batches take 2t.
            This significantly reduces total time compared to 10t in the
            sequential approach.
          </li>
          <li>
            <strong>Trade-off:</strong> Within a batch, all points are selected
            based on the same model, without incorporating results from other
            points in that batch. This may make the suggestions less informed
            than in the sequential approach, potentially requiring more
            evaluations to achieve the same optimization level.
          </li>
        </ul>

        <h2>Comparing Convergence and Effectiveness</h2>
        <p>
          Which method "converges faster and more effectively finds the global
          optimum." "Faster" could mean fewer function evaluations (sample
          efficiency) or less total time (wall-clock time), while
          "effectiveness" relates to the quality of the solution found. Let's
          break this down:
        </p>

        <div className="bg-card my-8 rounded-lg border p-6 shadow-sm">
          <h3 className="mb-4 text-xl font-semibold">Strategy Comparison</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead>
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Criteria
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Sequential Approach
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Parallel Approach
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                    Sample Efficiency
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm">
                    <div className="flex items-center">
                      <div className="mr-2 size-4 rounded-full bg-green-500"></div>
                      <span>Higher</span>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm">
                    <div className="flex items-center">
                      <div className="mr-2 size-4 rounded-full bg-yellow-500"></div>
                      <span>Moderate</span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                    Wall-Clock Time
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm">
                    <div className="flex items-center">
                      <div className="mr-2 size-4 rounded-full bg-red-500"></div>
                      <span>Slower (10t)</span>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm">
                    <div className="flex items-center">
                      <div className="mr-2 size-4 rounded-full bg-green-500"></div>
                      <span>Faster (2t)</span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                    Model Updates
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm">
                    10 updates
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm">
                    2 updates
                  </td>
                </tr>
                <tr>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                    Best For
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm">
                    Complex problems with many local optima
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm">
                    Time-sensitive applications with parallel capacity
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div className="mt-6">
            <h4 className="mb-2 text-lg font-medium">
              Convergence Visualization
            </h4>
            <div className="flex flex-col space-y-4 md:flex-row md:space-x-6 md:space-y-0">
              <div className="flex-1 rounded-lg border p-4">
                <h5 className="mb-2 font-medium">Sequential</h5>
                <div className="relative h-40 w-full">
                  {/* Stylized convergence curve for sequential */}
                  <div className="absolute bottom-0 left-0 size-full">
                    <svg viewBox="0 0 100 100" className="size-full">
                      <defs>
                        <linearGradient
                          id="seqGradient"
                          x1="0%"
                          y1="0%"
                          x2="100%"
                          y2="0%"
                        >
                          <stop
                            offset="0%"
                            stopColor="#3b82f6"
                            stopOpacity="0.2"
                          />
                          <stop
                            offset="100%"
                            stopColor="#3b82f6"
                            stopOpacity="0.8"
                          />
                        </linearGradient>
                      </defs>
                      <path
                        d="M0,100 C20,80 40,50 60,30 C70,20 80,15 100,10"
                        fill="none"
                        stroke="url(#seqGradient)"
                        strokeWidth="3"
                      />
                      <text x="5" y="95" fontSize="8" fill="currentColor">
                        Iterations
                      </text>
                      <text
                        x="0"
                        y="10"
                        fontSize="8"
                        fill="currentColor"
                        transform="rotate(90 0,10)"
                      >
                        Performance
                      </text>
                    </svg>
                  </div>
                  <div className="absolute bottom-2 right-2 rounded-md bg-blue-100 px-2 py-1 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    Steady, reliable improvement
                  </div>
                </div>
              </div>

              <div className="flex-1 rounded-lg border p-4">
                <h5 className="mb-2 font-medium">Parallel</h5>
                <div className="relative h-40 w-full">
                  {/* Stylized convergence curve for parallel */}
                  <div className="absolute bottom-0 left-0 size-full">
                    <svg viewBox="0 0 100 100" className="size-full">
                      <defs>
                        <linearGradient
                          id="parGradient"
                          x1="0%"
                          y1="0%"
                          x2="100%"
                          y2="0%"
                        >
                          <stop
                            offset="0%"
                            stopColor="#8b5cf6"
                            stopOpacity="0.2"
                          />
                          <stop
                            offset="100%"
                            stopColor="#8b5cf6"
                            stopOpacity="0.8"
                          />
                        </linearGradient>
                      </defs>
                      <path
                        d="M0,100 C10,70 20,60 30,40 L50,40 L70,25 L100,15"
                        fill="none"
                        stroke="url(#parGradient)"
                        strokeWidth="3"
                      />
                      <text x="5" y="95" fontSize="8" fill="currentColor">
                        Time
                      </text>
                      <text
                        x="0"
                        y="10"
                        fontSize="8"
                        fill="currentColor"
                        transform="rotate(90 0,10)"
                      >
                        Performance
                      </text>
                    </svg>
                  </div>
                  <div className="absolute bottom-2 right-2 rounded-md bg-purple-100 px-2 py-1 text-xs text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    Step-wise improvement
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <h3>Sample Efficiency (Fewer Function Evaluations)</h3>
        <ul>
          <li>
            <strong>Sequential:</strong> Generally more sample-efficient. Each
            point benefits from all prior data, allowing precise adjustments to
            the search. For a fixed budget (e.g., 20 evaluations), this often
            yields a better optimum because the model evolves incrementally,
            avoiding redundant or suboptimal sampling.
          </li>
          <li>
            <strong>Parallel:</strong> Slightly less sample-efficient. Batch
            points are chosen simultaneously, so later points in a batch do not
            benefit from earlier results within the same batch.
          </li>
          <li>
            <strong>Verdict:</strong> For a fixed number of evaluations,
            sequential typically outperforms parallel slightly in finding the
            global optimum.
          </li>
        </ul>

        <h3>Wall-Clock Time (Total Time)</h3>
        <ul>
          <li>
            <strong>Sequential:</strong> Slow if experiments are time-consuming
            and must be run one-by-one. Total time scales linearly with the
            number of sequential steps (e.g., 10t for 10 experiments).
          </li>
          <li>
            <strong>Parallel:</strong> Much faster if parallelization is
            feasible. Evaluating a batch takes the time of one experiment (e.g.,
            t per batch), so total time depends on the number of batches (e.g.,
            2t for two batches of 5). In experimental design, where experiments
            often dominate the timeline, this is a major advantage.
          </li>
          <li>
            <strong>Verdict:</strong> Parallel converges faster in wall-clock
            time when experiments can be run simultaneously.
          </li>
        </ul>

        <h3>Effectiveness in Finding the Global Optimum</h3>
        <p>
          Both methods can theoretically converge to the global optimum given
          enough evaluations, as Bayesian optimization leverages the Gaussian
          process to model the function globally. The interactive visualization
          demonstrates this convergence, with both strategies ultimately finding
          the optimal regions (highlighted in green). The difference lies in how
          quickly and reliably they do so:
        </p>
        <ul>
          <li>
            <strong>Sequential:</strong> May excel in complex problems with many
            local optima, as it refines the model step-by-step, reducing the
            risk of over-exploring suboptimal regions.
          </li>
          <li>
            <strong>Parallel:</strong> Can be equally effective with proper
            batch selection (e.g., ensuring diversity or maximizing joint
            improvement), though large batches without updates might miss fine
            details. For moderate batch sizes (e.g., 5), the difference is often
            minimal.
          </li>
        </ul>
      </div>
    </div>
  )
}
