/*
The root server layout for the app.
*/

import {
  createProfileAction,
  getProfileByUserIdAction
} from "@/actions/db/profiles-actions"
import { Toaster } from "@/components/ui/toaster"
import { CookieScript } from "@/components/utilities/cookie-script"
import { Providers } from "@/components/utilities/providers"
import { TailwindIndicator } from "@/components/utilities/tailwind-indicator"
import { AuthCookieProtection } from "@/components/utilities/auth-cookie-protection"
import { BRAND } from "@/lib/constants"
import { cn } from "@/lib/utils"
import { ClerkProvider } from "@clerk/nextjs"

import { getActualUserId } from "@/lib/auth-utils"
import { Analytics } from "@vercel/analytics/next"
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true
}

export const metadata: Metadata = {
  metadataBase: new URL(BRAND.DOMAIN),
  title: BRAND.SEO_TITLE,
  description: BRAND.SEO_DESCRIPTION,

  // Open Graph (Facebook, LinkedIn, WhatsApp, etc.)
  openGraph: {
    title: BRAND.SEO_TITLE,
    description: BRAND.SEO_DESCRIPTION,
    url: BRAND.DOMAIN,
    siteName: BRAND.NAME,
    images: [
      {
        url: "/social-preview.png",
        width: 1200,
        height: 630,
        alt: `${BRAND.NAME} - ${BRAND.SEO_DESCRIPTION}`
      }
    ],
    locale: "en_US",
    type: "website"
  },

  // Twitter Card
  twitter: {
    card: "summary_large_image",
    title: BRAND.SEO_TITLE,
    description: BRAND.SEO_DESCRIPTION,
    images: ["/social-preview.png"]
  },

  // Additional SEO meta tags
  keywords: [
    "optimization",
    "bayesian optimization",
    "bayesian optimization tool",
    "experimental design",
    "design of experiments",
    "multi-objective optimization",
    "chemical process optimization",
    "pharmaceutical research",
    "scientific research",
    "machine learning",
    "data science",
    "process optimization",
    "reaction optimization",
    "AI-driven optimization",
    "automated experimentation",
    "surrogate modeling",
    "Gaussian process optimization",
    "hyperparameter tuning",
    "optimization algorithms",
    "experimental data analysis",
    "laboratory automation",
    "continuous flow chemistry",
    "chemical engineering",
    "experimental planning",
    "optimization software",
    "scientific computing"
  ],
  authors: [{ name: BRAND.NAME }],
  robots: "index, follow",

  // Verification and additional meta
  other: {
    "theme-color": "#000000",
    "color-scheme": "light"
  },

  // Canonical URL
  alternates: {
    canonical: BRAND.DOMAIN
  }
}

export default async function RootLayout({
  children
}: {
  children: React.ReactNode
}) {
  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  if (userId) {
    try {
      const profileRes = await getProfileByUserIdAction(userId)

      // Only create a profile if it truly doesn't exist (not on database errors)
      if (!profileRes.isSuccess && profileRes.message === "Profile not found") {
        await createProfileAction({ userId })
      }
    } catch (error) {
      // Log the error but don't create a profile on errors
      console.error("Error checking user profile:", error)
    }
  }

  return (
    <ClerkProvider
      publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      signInUrl="/login"
      signUpUrl="/signup"
    >
      <html lang="en" suppressHydrationWarning>
        <head>
          <script
            dangerouslySetInnerHTML={{
              __html: `
                // Protect authentication cookies before any other scripts load
                (function() {
                  if (typeof window !== 'undefined') {
                    // Override document.cookie setter to prevent deletion of auth cookies
                    const originalCookieDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie') ||
                                                   Object.getOwnPropertyDescriptor(HTMLDocument.prototype, 'cookie');

                    if (originalCookieDescriptor && originalCookieDescriptor.set) {
                      const originalSetter = originalCookieDescriptor.set;

                      Object.defineProperty(document, 'cookie', {
                        set: function(value) {
                          // Check if this is trying to delete an auth cookie
                          if (typeof value === 'string' && value.includes('=;') && value.includes('expires=')) {
                            const cookieName = value.split('=')[0].trim();
                            const isAuthCookie = cookieName.includes('clerk') ||
                                               cookieName.includes('session') ||
                                               cookieName.startsWith('__clerk') ||
                                               cookieName.startsWith('__session');

                            if (isAuthCookie) {
                              return; // Don't delete auth cookies
                            }
                          }

                          // Use original setter for other cookies
                          originalSetter.call(this, value);
                        },
                        get: originalCookieDescriptor.get,
                        configurable: true
                      });
                    }
                  }
                })();
              `
            }}
          />
        </head>
        <body
          className={cn(
            "bg-background mx-auto min-h-screen w-full scroll-smooth antialiased",
            inter.className
          )}
        >
          <Providers
            attribute="class"
            defaultTheme="light"
            enableSystem={false}
            disableTransitionOnChange
          >
            <AuthCookieProtection />
            {children}

            <TailwindIndicator />

            <Toaster />

            {/* CookieScript Cookie Consent Widget */}
            <CookieScript />

            {/* Vercel Analytics */}
            <Analytics />
          </Providers>
        </body>
      </html>
    </ClerkProvider>
  )
}
