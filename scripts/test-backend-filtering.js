#!/usr/bin/env node

// Test script to reproduce the backend filtering issue
// Usage: node scripts/test-backend-filtering.js

require('dotenv').config({ path: '.env.local' });

const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  API_KEY: process.env.API_KEY || 'your-api-key-here'
};

async function testBackendFiltering() {
  console.log('🧪 TESTING BACKEND FILTERING ISSUE');
  console.log('=====================================');

  const optimizerId = 'user_30EFn0yPauqlMMorn5hfsn3of5X_Test_CRUD_filtering__1757672159887';

  // First, try to unload and reload the campaign to sync measurements
  console.log('🔄 Attempting to unload and reload campaign...');
  try {
    const unloadResponse = await fetch(`${API_CONFIG.BASE_URL}/optimizations/${optimizerId}/unload`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_CONFIG.API_KEY
      }
    });

    if (unloadResponse.ok) {
      console.log('✅ Campaign unloaded successfully');
    } else {
      console.log('⚠️ Failed to unload campaign (might not be loaded)');
    }
  } catch (error) {
    console.log('⚠️ Error unloading campaign:', error.message);
  }
  
  // Test the bounds update with preview_only=true
  const requestBody = {
    parameter_bounds: {
      x4: [30, 100]  // Change x4 bounds from [0, 100] to [30, 100]
    },
    preview_only: true
  };
  
  console.log('📤 Sending request via frontend proxy:');
  console.log('URL:', `http://localhost:3000/api/optimizations/${optimizerId}/bounds`);
  console.log('Body:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('📥 Backend response status:', response.status);
    
    const data = await response.json();
    console.log('📥 Backend response data:');
    console.log(JSON.stringify(data, null, 2));
    
    // Analyze the response
    if (data.status === 'success') {
      console.log('\n🔍 ANALYSIS:');
      console.log(`- Measurements retained: ${data.measurements_kept}`);
      console.log(`- Measurements dropped: ${data.measurements_dropped}`);
      console.log(`- Total measurements: ${data.total_measurements}`);
      
      if (data.filtered_measurements_details) {
        console.log('\n📊 FILTERED MEASUREMENTS DETAILS:');
        data.filtered_measurements_details.forEach((measurement, index) => {
          console.log(`${index + 1}. Measurement ID: ${measurement.id || 'N/A'}`);
          console.log(`   Exclusion reason: ${measurement.exclusion_reason}`);
          if (measurement.parameters) {
            console.log(`   Parameters:`, measurement.parameters);
          }
        });
      }
      
      // Check for the specific problematic measurement
      if (data.filtered_measurements_details) {
        const problematicMeasurement = data.filtered_measurements_details.find(m => 
          m.parameters && Math.abs(m.parameters.x4 - 27.097245441269315) < 1e-10
        );
        
        if (problematicMeasurement) {
          console.log('\n🎯 FOUND PROBLEMATIC MEASUREMENT:');
          console.log('Exclusion reason:', problematicMeasurement.exclusion_reason);
          console.log('Parameters:', problematicMeasurement.parameters);
          
          // Check if x1 is very small
          const x1Value = problematicMeasurement.parameters.x1;
          if (x1Value && Math.abs(x1Value) < 1e-15) {
            console.log(`\n🔬 VERY SMALL x1 VALUE DETECTED:`);
            console.log(`x1 = ${x1Value}`);
            console.log(`x1 (scientific) = ${x1Value.toExponential()}`);
            console.log(`Is NaN: ${Number.isNaN(x1Value)}`);
            console.log(`Is Finite: ${Number.isFinite(x1Value)}`);
            console.log(`Absolute value: ${Math.abs(x1Value)}`);
            
            if (problematicMeasurement.exclusion_reason.includes('NaN or infinite')) {
              console.log('\n❌ BUG CONFIRMED: Very small number incorrectly classified as NaN/infinite');
            } else {
              console.log('\n✅ CORRECT: Measurement filtered for bounds violation, not NaN/infinite');
            }
          }
        }
      }
    } else {
      console.log('❌ Backend request failed:', data.message || data.error);
    }
    
  } catch (error) {
    console.error('❌ Error testing backend filtering:', error);
  }
}

// Run the test
testBackendFiltering().catch(console.error);
