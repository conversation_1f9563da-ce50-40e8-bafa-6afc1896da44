/*
This client component provides a subscription promotion banner for the sidebar.
*/

"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { <PERSON>rk<PERSON>, ArrowUpRight, X, Crown } from "lucide-react"
import { cn } from "@/lib/utils"
import Link from "next/link"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"

type SubscriptionTier =
  | "free"
  | "trial"
  | "trial-expired"
  | "basic"
  | "advanced"
  | "monthly"
  | "yearly"
  | "premium"
  | "premium-monthly"
  | "premium-yearly"

interface SubscriptionPromoProps {
  currentTier: SubscriptionTier
  trialDaysLeft?: number
  isCollapsed: boolean
}

export function SubscriptionPromo({
  currentTier,
  trialDaysLeft = 30,
  isCollapsed
}: SubscriptionPromoProps) {
  const [isDismissed, setIsDismissed] = useState(false)

  if (
    isDismissed ||
    currentTier === "monthly" ||
    currentTier === "yearly" ||
    currentTier === "premium" ||
    currentTier === "premium-monthly" ||
    currentTier === "premium-yearly"
  )
    return null

  // Define tier details matching actual pricing cards
  const tiers = {
    free: {
      name: "Free",
      color: "bg-gray-500",
      next: "premium-monthly",
      cta: "Upgrade to Premium",
      message: "Limited access",
      benefit:
        "Unlimited access + Priority support + Advanced analytics + Unlimited optimizations",
      urgency: "Unlock all premium features"
    },
    trial: {
      name: "Trial",
      color: "bg-amber-500",
      next: "premium-monthly",
      cta: "Subscribe to Continue",
      message: `${trialDaysLeft} days remaining`,
      benefit:
        "Unlimited access + Priority support + Advanced analytics + Unlimited optimizations",
      urgency:
        trialDaysLeft <= 3
          ? "Trial ending soon!"
          : "Don't lose access to premium features"
    },
    "trial-expired": {
      name: "Trial Expired",
      color: "bg-orange-500",
      next: "premium-monthly",
      cta: "Subscribe Now",
      message: "Trial has ended",
      benefit:
        "Unlimited access + Priority support + Advanced analytics + Unlimited optimizations",
      urgency: "Continue with premium features"
    },
    basic: {
      name: "Basic",
      color: "bg-blue-500",
      next: "premium-monthly",
      cta: "Upgrade to Premium",
      message: "Limited features",
      benefit:
        "Unlimited access + Priority support + Advanced analytics + Unlimited optimizations",
      urgency: "Unlock all features with premium"
    },
    advanced: {
      name: "Advanced",
      color: "bg-purple-500",
      next: "premium-monthly",
      cta: "Upgrade to Premium",
      message: "Enhanced features",
      benefit:
        "Unlimited access + Priority support + Advanced analytics + Unlimited optimizations",
      urgency: "Get the most out of your optimizations"
    },
    monthly: {
      name: "Premium Monthly",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "€79/month",
      benefit: "",
      urgency: ""
    },
    yearly: {
      name: "Premium Yearly",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "€799/year (Save 17%)",
      benefit: "",
      urgency: ""
    },
    premium: {
      name: "Premium",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "Unlimited access",
      benefit: "",
      urgency: ""
    },
    "premium-monthly": {
      name: "Premium Monthly",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "€79/month",
      benefit: "",
      urgency: ""
    },
    "premium-yearly": {
      name: "Premium Yearly",
      color: "bg-emerald-500",
      next: null,
      cta: "",
      message: "€799/year (Save 17%)",
      benefit: "",
      urgency: ""
    }
  }

  const currentTierInfo = tiers[currentTier]

  if (isCollapsed) {
    return (
      <div className="border-t p-2">
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Link href="/dashboard/billing">
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "h-8 w-full justify-center",
                    currentTier === "trial"
                      ? "hover:bg-amber-50"
                      : currentTier === "trial-expired"
                        ? "hover:bg-orange-50"
                        : "hover:bg-blue-50"
                  )}
                >
                  {currentTier === "trial" ? (
                    <Crown className="size-4 text-amber-500" />
                  ) : currentTier === "trial-expired" ? (
                    <ArrowUpRight className="size-5 text-orange-500" />
                  ) : (
                    <ArrowUpRight className="size-4 text-blue-500" />
                  )}
                </Button>
              </Link>
            </TooltipTrigger>
            <TooltipContent side="right">
              <div className="text-xs">
                <div className="font-medium">{currentTierInfo.name}</div>
                {currentTier === "trial" && (
                  <div className="text-amber-600">{trialDaysLeft} days left</div>
                )}
                <div className="text-muted-foreground">{currentTierInfo.cta}</div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    )
  }

  return (
    <div className="border-t p-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {currentTier === "trial" ? (
            <Crown className="size-4 text-amber-500" />
          ) : currentTier === "trial-expired" ? (
            <Crown className="size-5 text-orange-500" />
          ) : (
            <Sparkles className="size-4 text-blue-500" />
          )}

          <div className="flex items-center space-x-1">
            <span className={cn(
              "font-medium",
              currentTier === "trial-expired" ? "text-sm" : "text-xs"
            )}>
              {currentTier === "trial" ? "Trial" : currentTierInfo.name}
            </span>
            {currentTier === "trial" && (
              <span
                className={cn(
                  "text-xs",
                  trialDaysLeft <= 3 ? "text-red-500" : "text-amber-600"
                )}
              >
                • {trialDaysLeft} days left
              </span>
            )}
            {currentTier === "trial-expired" && (
              <span className="text-sm font-medium text-orange-600">• Upgrade now</span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="/dashboard/billing">
                  <Button variant="ghost" size="icon" className="size-6">
                    <ArrowUpRight className="size-3" />
                    <span className="sr-only">{currentTierInfo.cta}</span>
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent side="right">
                <div className="text-xs font-medium">{currentTierInfo.cta}</div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

        </div>
      </div>
    </div>
  )
}
