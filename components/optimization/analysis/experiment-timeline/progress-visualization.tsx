"use client"

import { useState, use<PERSON>ffect, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import {
  TrendingUp,
  RefreshCw,
  AlertCircle,
  Maximize2,
  Info
} from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
// These imports might be used elsewhere in the application
// @ts-ignore - Suppressing unused imports warning
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import dynamic from "next/dynamic"

// Import Plotly dynamically to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), {
  ssr: false,
  // This is needed to fix the type error with Plot component props
  loading: () => (
    <div className="bg-muted size-full animate-pulse rounded-md"></div>
  )
})

// Define PlotData type for better type safety
interface PlotData {
  x: number[] | string[]
  y: number[]
  mode: "lines" | "markers" | "lines+markers"
  type: "scatter"
  name: string
  line?: {
    width?: number
    dash?: "solid" | "dot" | "dash"
    color?: string
  }
  marker?: {
    size?: number
  }
  hoverinfo?: "text" | "none"
  text?: string[]
  yaxis?: string
  showlegend?: boolean
}

// Types
interface ProcessedExperiment {
  index: number
  experimentNumber: string // e.g., "1.2" for batch 1, experiment 2
  batchNumber: number
  targetValues: Record<string, number>
  bestValues: Record<string, number> // Running best for each target
  improvement: Record<string, number> // Improvement from previous best
  timestamp?: Date
}

// Extend the SelectMeasurement type to include timestamp
interface ExtendedMeasurement extends SelectMeasurement {
  timestamp?: string | Date
  metadata?: {
    experimentNumber?: string
    batchNumber?: number
  }
}

interface ProgressVisualizationProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  targetNames: string[]
  onExperimentSelect?: (experimentIndex: number) => void
  selectedExperiment?: number
}

export function ProgressVisualization({
  optimization,
  measurements,
  targetNames,
  onExperimentSelect,
  // This prop is received but not used in this component
  // eslint-disable-next-line no-unused-vars
  selectedExperiment
}: ProgressVisualizationProps) {
  // State
  const [processedData, setProcessedData] = useState<
    ProcessedExperiment[] | null
  >(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [showBestValues, setShowBestValues] = useState<boolean>(true)
  const [showMovingAverage, setShowMovingAverage] = useState<boolean>(false)
  const [movingAverageWindow, setMovingAverageWindow] = useState<number>(5)
  const [selectedTargets, setSelectedTargets] = useState<string[]>([])
  const [normalizeValues, setNormalizeValues] = useState<boolean>(false)

  // Get target modes (MAX or MIN)
  const targetModes = useMemo(() => {
    const modes: Record<string, "MAX" | "MIN"> = {}

    // Default to the optimization's target mode
    const defaultMode =
      (optimization.targetMode as string)?.toUpperCase() === "MIN"
        ? "MIN"
        : "MAX"

    // Set default mode for all targets
    targetNames.forEach(target => {
      modes[target] = defaultMode
    })

    // Check if target configuration is available in optimization config
    if (optimization.config && typeof optimization.config === "object") {
      const config = optimization.config as any
      if (config.target_config && Array.isArray(config.target_config)) {
        config.target_config.forEach((targetConfig: any) => {
          if (targetConfig.name && targetConfig.mode) {
            modes[targetConfig.name] =
              targetConfig.mode.toUpperCase() === "MIN" ? "MIN" : "MAX"
          }
        })
      }
    }

    return modes
  }, [optimization, targetNames])

  // Process measurements data
  const processExperiments = () => {
    setIsLoading(true)
    setError(null)

    try {
      if (measurements.length === 0) {
        throw new Error("No measurements available for analysis")
      }

      const processed: ProcessedExperiment[] = []

      // Track best values for each target
      const bestValues: Record<string, number> = {}
      targetNames.forEach(target => {
        bestValues[target] =
          targetModes[target] === "MIN" ? Infinity : -Infinity
      })

      // Sort measurements by creation date to ensure chronological order (oldest first)
      const sortedMeasurements = [...measurements].sort((a, b) => {
        const dateA = new Date(a.createdAt).getTime()
        const dateB = new Date(b.createdAt).getTime()
        return dateA - dateB
      })

      // Process each measurement in chronological order
      sortedMeasurements.forEach((measurement, index) => {
        // Extract experiment number and batch number
        let experimentNumber = `${index + 1}`
        let batchNumber = 1

        // Try to parse experiment number from metadata if available
        // Cast to ExtendedMeasurement to access metadata and timestamp
        const extMeasurement = measurement as ExtendedMeasurement
        if (
          extMeasurement.metadata &&
          typeof extMeasurement.metadata === "object"
        ) {
          if (extMeasurement.metadata.experimentNumber) {
            experimentNumber = extMeasurement.metadata.experimentNumber
          }
          if (extMeasurement.metadata.batchNumber) {
            batchNumber = extMeasurement.metadata.batchNumber
          }
        }

        // Extract target values
        const targetValues: Record<string, number> = {}
        const currentBestValues: Record<string, number> = { ...bestValues }
        const improvement: Record<string, number> = {}

        if (measurement.targetValues) {
          // Multi-target case
          const targetValuesObj = measurement.targetValues as Record<
            string,
            string | number
          >

          targetNames.forEach(target => {
            // Convert to string first to ensure parseFloat works correctly
            const valueStr = String(targetValuesObj[target])
            const value = parseFloat(valueStr)
            if (!isNaN(value)) {
              targetValues[target] = value

              // Update best value if better
              const isBetter =
                targetModes[target] === "MIN"
                  ? value < bestValues[target]
                  : value > bestValues[target]

              if (isBetter) {
                // Calculate improvement
                improvement[target] =
                  targetModes[target] === "MIN"
                    ? bestValues[target] - value
                    : value - bestValues[target]

                // Update best value
                bestValues[target] = value
              } else {
                improvement[target] = 0
              }
            }
          })
        } else {
          // Single target case
          const value = parseFloat(measurement.targetValue)
          if (!isNaN(value) && targetNames.includes(optimization.targetName)) {
            targetValues[optimization.targetName] = value

            // Update best value if better
            const isBetter =
              targetModes[optimization.targetName] === "MIN"
                ? value < bestValues[optimization.targetName]
                : value > bestValues[optimization.targetName]

            if (isBetter) {
              // Calculate improvement
              improvement[optimization.targetName] =
                targetModes[optimization.targetName] === "MIN"
                  ? bestValues[optimization.targetName] - value
                  : value - bestValues[optimization.targetName]

              // Update best value
              bestValues[optimization.targetName] = value
            } else {
              improvement[optimization.targetName] = 0
            }
          }
        }

        // Create processed experiment
        processed.push({
          index,
          experimentNumber,
          batchNumber,
          targetValues,
          bestValues: { ...currentBestValues },
          improvement,
          timestamp: extMeasurement.timestamp
            ? new Date(extMeasurement.timestamp)
            : undefined
        })
      })

      setProcessedData(processed)

      // Initialize selected targets if empty
      if (selectedTargets.length === 0 && targetNames.length > 0) {
        setSelectedTargets([targetNames[0]])
      }

      console.log("Processed experiment data:", {
        experimentCount: processed.length,
        targetCount: targetNames.length,
        targetModes
      })
    } catch (err) {
      console.error("Error processing experiment data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // Calculate moving averages
  const calculateMovingAverage = (
    values: number[],
    window: number
  ): number[] => {
    const result: number[] = []

    for (let i = 0; i < values.length; i++) {
      let sum = 0
      let count = 0

      // Calculate average of window elements centered on i
      const halfWindow = Math.floor(window / 2)
      const start = Math.max(0, i - halfWindow)
      const end = Math.min(values.length - 1, i + halfWindow)

      for (let j = start; j <= end; j++) {
        if (!isNaN(values[j])) {
          sum += values[j]
          count++
        }
      }

      result.push(count > 0 ? sum / count : NaN)
    }

    return result
  }

  // Generate plot data
  const plotData = useMemo(() => {
    if (!processedData || selectedTargets.length === 0) return []

    const traces: PlotData[] = []
    // Use actual experiment numbers for the x-axis instead of sequential indices
    const experimentIndices = processedData.map(exp => {
      // Parse the experiment number as a number for the x-axis
      // If it's not a valid number, fall back to the index + 1
      const parsedNumber = parseInt(exp.experimentNumber)
      return isNaN(parsedNumber) ? exp.index + 1 : parsedNumber
    })

    // Find min/max values for normalization
    const targetRanges: Record<string, { min: number; max: number }> = {}
    selectedTargets.forEach(target => {
      const values = processedData
        .map(exp => exp.targetValues[target])
        .filter(val => !isNaN(val))

      targetRanges[target] = {
        min: Math.min(...values),
        max: Math.max(...values)
      }
    })

    // Create traces for each selected target
    selectedTargets.forEach(target => {
      // Get target values
      const values = processedData.map(exp => {
        const val = exp.targetValues[target]

        // Normalize if needed
        if (normalizeValues && !isNaN(val)) {
          const range = targetRanges[target]
          if (range.max !== range.min) {
            return (val - range.min) / (range.max - range.min)
          }
        }

        return val
      })

      // Create main trace
      traces.push({
        x: experimentIndices,
        y: values,
        mode: "lines+markers" as const,
        type: "scatter" as const,
        name: `${target} ${targetModes[target] === "MIN" ? "(Min)" : "(Max)"}`,
        line: {
          width: 2
        },
        marker: {
          size: 6
        },
        hoverinfo: "text" as const,
        text: processedData.map(exp => {
          const val = exp.targetValues[target]
          return !isNaN(val)
            ? `Experiment ${exp.experimentNumber}<br>${target}: ${val.toFixed(4)}`
            : `Experiment ${exp.experimentNumber}<br>${target}: N/A`
        })
      })

      // Add best values trace if enabled
      if (showBestValues) {
        const bestValues = processedData.map(exp => {
          const val = exp.bestValues[target]

          // Normalize if needed
          if (normalizeValues && !isNaN(val)) {
            const range = targetRanges[target]
            if (range.max !== range.min) {
              return (val - range.min) / (range.max - range.min)
            }
          }

          return val
        })

        traces.push({
          x: experimentIndices,
          y: bestValues,
          mode: "lines" as const,
          type: "scatter" as const,
          name: `Best ${target}`,
          line: {
            width: 2,
            dash: "dot" as const
          },
          hoverinfo: "text" as const,
          text: processedData.map(exp => {
            const val = exp.bestValues[target]
            return !isNaN(val)
              ? `Experiment ${exp.experimentNumber}<br>Best ${target}: ${val.toFixed(4)}`
              : `Experiment ${exp.experimentNumber}<br>Best ${target}: N/A`
          })
        })
      }

      // Add moving average trace if enabled
      if (showMovingAverage) {
        const movingAvg = calculateMovingAverage(values, movingAverageWindow)

        traces.push({
          x: experimentIndices,
          y: movingAvg,
          mode: "lines" as const,
          type: "scatter" as const,
          name: `${target} (MA-${movingAverageWindow})`,
          line: {
            width: 2,
            color: "rgba(0, 0, 0, 0.5)"
          },
          hoverinfo: "text" as const,
          text: processedData.map((exp, i) => {
            const val = movingAvg[i]
            return !isNaN(val)
              ? `Experiment ${exp.experimentNumber}<br>${target} MA-${movingAverageWindow}: ${val.toFixed(4)}`
              : `Experiment ${exp.experimentNumber}<br>${target} MA-${movingAverageWindow}: N/A`
          })
        })
      }
    })

    // Add batch separators
    const batchTransitions: number[] = []
    let currentBatch = -1

    processedData.forEach((exp, i) => {
      if (exp.batchNumber !== currentBatch) {
        currentBatch = exp.batchNumber
        batchTransitions.push(i + 1)
      }
    })

    // Add vertical lines for batch separators
    batchTransitions.slice(1).forEach(index => {
      traces.push({
        x: [index, index],
        y: [0, 1],
        mode: "lines" as const,
        type: "scatter" as const,
        name: `Batch ${index}`,
        line: {
          color: "rgba(0, 0, 0, 0.3)",
          width: 1,
          dash: "dash" as const
        },
        hoverinfo: "none" as const,
        yaxis: "y2" as const,
        showlegend: false
      })
    })

    return traces
  }, [
    processedData,
    selectedTargets,
    showBestValues,
    showMovingAverage,
    movingAverageWindow,
    normalizeValues,
    targetModes
  ])

  // Get plot layout
  const plotLayout = useMemo(() => {
    return {
      title: "Optimization Progress",
      autosize: true,
      margin: { l: 60, r: 20, t: 50, b: 80 }, // Increased bottom margin for legend
      xaxis: {
        title: {
          text: "Experiment",
          standoff: 15 // Add standoff to prevent overlap with legend
        },
        tickmode: "auto" as const,
        nticks: 10
      },
      yaxis: {
        title: {
          text: normalizeValues ? "Normalized Target Value" : "Target Value",
          standoff: 5, // Add some standoff
          font: {
            size: 12
          }
        },
        automargin: true
      },
      yaxis2: {
        overlaying: "y" as const,
        range: [0, 1],
        showticklabels: false,
        showgrid: false
      },
      showlegend: true,
      legend: {
        orientation: "h" as const,
        y: -0.3, // Moved further down to avoid overlap with x-axis title
        xanchor: "center", // Center the legend horizontally
        x: 0.5 // Center position
      },
      hovermode: "closest" as const
    }
  }, [normalizeValues])

  // Handle experiment selection
  const handlePlotClick = (event: any) => {
    if (event && event.points && event.points.length > 0 && processedData) {
      const point = event.points[0]
      const pointIndex = point.pointIndex

      // Get the actual experiment index from the processed data
      if (pointIndex >= 0 && pointIndex < processedData.length) {
        const experimentIndex = processedData[pointIndex].index

        if (experimentIndex >= 0 && experimentIndex < measurements.length) {
          console.log(
            `Selected experiment ${processedData[pointIndex].experimentNumber}`
          )

          if (onExperimentSelect) {
            onExperimentSelect(experimentIndex)
          }
        }
      }
    }
  }

  // Handle target selection
  const toggleTarget = (target: string) => {
    setSelectedTargets(prev => {
      if (prev.includes(target)) {
        return prev.filter(t => t !== target)
      } else {
        return [...prev, target]
      }
    })
  }

  // Effect to process data when measurements change
  useEffect(() => {
    if (measurements.length > 0) {
      processExperiments()
    }
  }, [measurements.length, targetNames.length])

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="flex items-center text-lg font-medium">
            <TrendingUp className="text-primary mr-2 size-5" />
            Progress Visualization
          </h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={processExperiments}
              disabled={isLoading}
            >
              {isLoading ? (
                <RefreshCw className="size-4 animate-spin" />
              ) : (
                <RefreshCw className="size-4" />
              )}
              <span className="ml-2 hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>

        <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <Label>Target Selection</Label>
            <div className="flex flex-wrap gap-2">
              {targetNames.map(target => (
                <Button
                  key={target}
                  variant={
                    selectedTargets.includes(target) ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => toggleTarget(target)}
                >
                  {target}
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Switch
                id="show-best-values"
                checked={showBestValues}
                onCheckedChange={setShowBestValues}
              />
              <Label htmlFor="show-best-values">Show Best Values</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="show-moving-average"
                checked={showMovingAverage}
                onCheckedChange={setShowMovingAverage}
              />
              <Label htmlFor="show-moving-average">Show Moving Average</Label>
            </div>

            {showMovingAverage && (
              <div className="pt-2">
                <Label htmlFor="ma-window" className="text-xs">
                  Window Size: {movingAverageWindow}
                </Label>
                <Slider
                  id="ma-window"
                  min={3}
                  max={15}
                  step={2}
                  value={[movingAverageWindow]}
                  onValueChange={value => setMovingAverageWindow(value[0])}
                  className="mt-1"
                />
              </div>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Switch
                id="normalize-values"
                checked={normalizeValues}
                onCheckedChange={setNormalizeValues}
              />
              <Label htmlFor="normalize-values">Normalize Values</Label>
            </div>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="size-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex h-[400px] items-center justify-center">
            <Skeleton className="size-full rounded-md" />
          </div>
        ) : processedData && processedData.length > 0 ? (
          <div className="space-y-4">
            <div className="h-[400px] w-full overflow-hidden rounded-md border">
              {/* @ts-ignore */}
              <Plot
                data={plotData}
                layout={plotLayout}
                config={{ responsive: true, displayModeBar: false }}
                style={{ width: "100%", height: "100%" }}
                onClick={handlePlotClick}
              />
            </div>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full">
                  <Maximize2 className="mr-2 size-4" />
                  Expand View
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Progress Visualization</DialogTitle>
                </DialogHeader>
                <div className="h-[600px] w-full">
                  <Plot
                    data={plotData}
                    layout={{
                      ...plotLayout,
                      height: 600
                    }}
                    config={{ responsive: true }}
                    style={{ width: "100%", height: "100%" }}
                    onClick={handlePlotClick}
                  />
                </div>
              </DialogContent>
            </Dialog>

            {selectedTargets.length === 0 && (
              <Alert>
                <Info className="size-4" />
                <AlertTitle>No Targets Selected</AlertTitle>
                <AlertDescription>
                  Please select at least one target to visualize progress.
                </AlertDescription>
              </Alert>
            )}
          </div>
        ) : (
          <div className="flex h-[400px] flex-col items-center justify-center p-4 text-center">
            <TrendingUp className="text-muted-foreground mb-4 size-12" />
            <h3 className="text-lg font-medium">No progress data</h3>
            <p className="text-muted-foreground mt-2 max-w-md text-sm">
              There might not be enough measurements to visualize progress, or
              the measurements might not have valid target values.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
