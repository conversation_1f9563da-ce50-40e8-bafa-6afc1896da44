#!/usr/bin/env node

/**
 * Test script to investigate target filtering behavior
 * Similar to parameter filtering investigation but focused on target bounds
 */

require('dotenv').config({ path: '.env.local' });

async function testTargetFiltering() {
  console.log('🎯 TESTING TARGET FILTERING BEHAVIOR');
  console.log('====================================');
  
  // Find an optimization to test with
  const optimizationName = "Test CRUD Filtering2";
  
  try {
    // Get optimization details from database
    const { drizzle } = require("drizzle-orm/postgres-js")
    const postgres = require("postgres")
    const { eq, and } = require("drizzle-orm")
    const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

    // Database schema definitions
    const optimizationsTable = pgTable("optimizations", {
      id: uuid("id").primaryKey(),
      optimizerId: text("optimizer_id").notNull(),
      name: text("name").notNull(),
      status: text("status").notNull(),
      config: jsonb("config"),
      createdAt: timestamp("created_at").defaultNow(),
      updatedAt: timestamp("updated_at").defaultNow(),
    });

    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }

    const client = postgres(connectionString);
    const db = drizzle(client);

    const optimizationsList = await db.select().from(optimizationsTable);
    const opt = optimizationsList.find(o => o.name === optimizationName);

    if (!opt) {
      console.error(`❌ Optimization "${optimizationName}" not found`);
      client.end();
      return;
    }
    
    console.log(`✅ Found optimization: ${opt.name}`);
    console.log(`  - ID: ${opt.id}`);
    console.log(`  - Optimizer ID: ${opt.optimizerId}`);
    
    // Get current configuration
    console.log('\n📊 CURRENT TARGET CONFIGURATION:');
    const targetConfig = opt.config.target_config;
    if (Array.isArray(targetConfig)) {
      targetConfig.forEach((target, index) => {
        console.log(`  ${index + 1}. ${target.name}:`);
        console.log(`     - Mode: ${target.mode}`);
        console.log(`     - Type: ${target.type}`);
        console.log(`     - Bounds: [${target.bounds[0]}, ${target.bounds[1]}]`);
        console.log(`     - Transformation: ${target.transformation}`);
      });
    } else {
      console.log(`  Single target: ${JSON.stringify(targetConfig, null, 2)}`);
    }
    
    // Test 1: Get current backend measurements to see target values
    console.log('\n🔍 STEP 1: ANALYZING CURRENT TARGET VALUES');
    console.log('==========================================');
    
    const backendResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/measurements`);
    if (!backendResponse.ok) {
      throw new Error(`Failed to get backend measurements: ${backendResponse.statusText}`);
    }
    
    const backendData = await backendResponse.json();
    const measurements = backendData.measurements || [];
    
    console.log(`Backend has ${measurements.length} measurements`);
    
    if (measurements.length > 0) {
      // Analyze target value distributions
      const targetStats = {};
      
      measurements.forEach((measurement, index) => {
        Object.keys(measurement).forEach(key => {
          if (key.startsWith('Target ') || key.toLowerCase().includes('target')) {
            if (!targetStats[key]) {
              targetStats[key] = {
                values: [],
                min: Infinity,
                max: -Infinity,
                hasNaN: false,
                hasInf: false
              };
            }
            
            const value = measurement[key];
            targetStats[key].values.push(value);
            
            if (Number.isNaN(value)) {
              targetStats[key].hasNaN = true;
            } else if (!Number.isFinite(value)) {
              targetStats[key].hasInf = true;
            } else {
              targetStats[key].min = Math.min(targetStats[key].min, value);
              targetStats[key].max = Math.max(targetStats[key].max, value);
            }
          }
        });
      });
      
      console.log('\n📈 TARGET VALUE ANALYSIS:');
      Object.entries(targetStats).forEach(([targetName, stats]) => {
        console.log(`\n  ${targetName}:`);
        console.log(`    - Total values: ${stats.values.length}`);
        console.log(`    - Range: [${stats.min}, ${stats.max}]`);
        console.log(`    - Has NaN: ${stats.hasNaN}`);
        console.log(`    - Has Infinite: ${stats.hasInf}`);
        
        // Show some example values
        const sampleValues = stats.values.slice(0, 5);
        console.log(`    - Sample values: [${sampleValues.join(', ')}]`);
        
        // Check for very small or very large values that might be problematic
        const verySmall = stats.values.filter(v => Number.isFinite(v) && Math.abs(v) < 1e-10 && v !== 0);
        const veryLarge = stats.values.filter(v => Number.isFinite(v) && Math.abs(v) > 1e10);
        
        if (verySmall.length > 0) {
          console.log(`    - Very small values: ${verySmall.length} (might be flagged incorrectly)`);
          console.log(`      Examples: [${verySmall.slice(0, 3).map(v => v.toExponential()).join(', ')}]`);
        }
        
        if (veryLarge.length > 0) {
          console.log(`    - Very large values: ${veryLarge.length} (might cause overflow)`);
          console.log(`      Examples: [${veryLarge.slice(0, 3).join(', ')}]`);
        }
      });
    }
    
    // Test 2: Test target bounds filtering
    console.log('\n🧪 STEP 2: TESTING TARGET BOUNDS FILTERING');
    console.log('==========================================');
    
    // Test changing Target 1 bounds from [0, 100] to [40, 80]
    const testTargetBounds = {
      "Target 1": [40, 80]
    };
    
    console.log(`📤 Testing Target 1 bounds change to [40, 80]...`);
    
    const targetFilterResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target_bounds: testTargetBounds,
        preview_only: true
      })
    });
    
    if (!targetFilterResponse.ok) {
      const errorText = await targetFilterResponse.text();
      console.error(`❌ Target filtering test failed: ${targetFilterResponse.statusText} - ${errorText}`);
      return;
    }
    
    const targetFilterResult = await targetFilterResponse.json();
    console.log(`📥 Backend response:`, JSON.stringify(targetFilterResult, null, 2));
    
    // Test 3: Analyze the filtering results
    console.log('\n📊 STEP 3: ANALYZING TARGET FILTERING RESULTS');
    console.log('==============================================');
    
    if (targetFilterResult.dropped_measurements) {
      console.log(`\n🔍 DROPPED MEASUREMENTS ANALYSIS:`);
      console.log(`Total dropped: ${targetFilterResult.dropped_measurements.length}`);
      
      // Group by exclusion reason
      const reasonGroups = {};
      targetFilterResult.dropped_measurements.forEach(measurement => {
        const reason = measurement.exclusion_reason || 'Unknown';
        if (!reasonGroups[reason]) {
          reasonGroups[reason] = [];
        }
        reasonGroups[reason].push(measurement);
      });
      
      Object.entries(reasonGroups).forEach(([reason, measurements]) => {
        console.log(`\n  Reason: "${reason}"`);
        console.log(`  Count: ${measurements.length}`);
        
        // Show a few examples
        measurements.slice(0, 3).forEach((measurement, index) => {
          console.log(`    ${index + 1}. Target 1: ${measurement['Target 1']}, Target 2: ${measurement['Target 2']}`);
        });
        
        // Check if this reason involves NaN/infinite detection
        if (reason.toLowerCase().includes('nan') || reason.toLowerCase().includes('infinite')) {
          console.log(`    🚨 POTENTIAL BUG: NaN/infinite detection in target filtering!`);
          
          // Analyze the flagged values
          measurements.forEach((measurement, index) => {
            const target1 = measurement['Target 1'];
            const target2 = measurement['Target 2'];
            
            const target1Valid = Number.isFinite(target1) && !Number.isNaN(target1);
            const target2Valid = Number.isFinite(target2) && !Number.isNaN(target2);
            
            if (target1Valid && target2Valid) {
              console.log(`      ❌ FALSE POSITIVE: Measurement ${index + 1} has valid targets but flagged as NaN/infinite`);
              console.log(`         Target 1: ${target1} (valid), Target 2: ${target2} (valid)`);
            }
          });
        }
      });
    }
    
    // Test 4: Compare expected vs actual filtering
    console.log('\n🔍 STEP 4: EXPECTED VS ACTUAL FILTERING COMPARISON');
    console.log('==================================================');
    
    if (measurements.length > 0) {
      let expectedRetained = 0;
      let expectedFiltered = 0;
      
      measurements.forEach(measurement => {
        const target1 = measurement['Target 1'];
        
        // Check if Target 1 is within new bounds [40, 80]
        if (Number.isFinite(target1) && !Number.isNaN(target1)) {
          if (target1 >= 40 && target1 <= 80) {
            expectedRetained++;
          } else {
            expectedFiltered++;
          }
        } else {
          expectedFiltered++; // NaN/infinite should be filtered
        }
      });
      
      console.log(`\n📊 EXPECTED FILTERING RESULTS:`);
      console.log(`  - Should be retained: ${expectedRetained}`);
      console.log(`  - Should be filtered: ${expectedFiltered}`);
      console.log(`  - Total: ${expectedRetained + expectedFiltered}`);
      
      console.log(`\n📊 ACTUAL FILTERING RESULTS:`);
      console.log(`  - Actually retained: ${targetFilterResult.valid_count || 0}`);
      console.log(`  - Actually filtered: ${targetFilterResult.dropped_count || 0}`);
      console.log(`  - Total: ${(targetFilterResult.valid_count || 0) + (targetFilterResult.dropped_count || 0)}`);
      
      const retainedMatch = expectedRetained === (targetFilterResult.valid_count || 0);
      const filteredMatch = expectedFiltered === (targetFilterResult.dropped_count || 0);
      
      if (retainedMatch && filteredMatch) {
        console.log(`✅ FILTERING RESULTS MATCH EXPECTATIONS`);
      } else {
        console.log(`❌ FILTERING RESULTS DO NOT MATCH EXPECTATIONS`);
        if (!retainedMatch) {
          console.log(`  - Retained mismatch: expected ${expectedRetained}, got ${targetFilterResult.valid_count || 0}`);
        }
        if (!filteredMatch) {
          console.log(`  - Filtered mismatch: expected ${expectedFiltered}, got ${targetFilterResult.dropped_count || 0}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error during target filtering test:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

// Run the test
testTargetFiltering().catch(console.error);
