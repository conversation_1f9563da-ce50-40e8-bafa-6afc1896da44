"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { toast } from "@/components/ui/use-toast"
import {
  Upload,
  Download,
  FileText,
  CheckCircle,
  AlertCircle,
  Loader2,
  X,
  Eye
} from "lucide-react"
import {
  uploadDatasetWorkflowAction,
  generateTemplateAction,
  generateExcelTemplateAction,
  validateDatasetAction
} from "@/actions/dataset-upload-actions"
import { DataValidationError } from "@/types/dataset-types"

interface DatasetUploadProps {
  optimizationId: string
  optimizationName: string
  onUploadSuccess: (samples: any[], batchId: string) => void
}

export function DatasetUpload({
  optimizationId,
  optimizationName,
  onUploadSuccess
}: DatasetUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const [isGeneratingTemplate, setIsGeneratingTemplate] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [validationResult, setValidationResult] = useState<any>(null)
  const [uploadProgress, setUploadProgress] = useState(0)

  // Handle file drop
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0]
      if (!file) return

      setUploadedFile(file)
      setValidationResult(null)

      // Read file content
      const reader = new FileReader()
      reader.onload = async e => {
        let csvContent: string

        if (file.name.endsWith(".xlsx")) {
          // Handle Excel files - convert to CSV format
          try {
            const ExcelJS = (await import("exceljs")).default
            const workbook = new ExcelJS.Workbook()
            await workbook.xlsx.load(e.target?.result as ArrayBuffer)

            const worksheet = workbook.getWorksheet(1) // Get first worksheet
            if (!worksheet) {
              toast({
                title: "Error",
                description: "No data found in Excel file",
                variant: "destructive"
              })
              return
            }

            // Convert to CSV format
            const csvRows: string[] = []
            worksheet.eachRow((row, rowNumber) => {
              const values = row.values as any[]
              // Skip the first element (it's undefined in ExcelJS)
              const cleanValues = values
                .slice(1)
                .map(val =>
                  val === null || val === undefined ? "" : String(val)
                )
              csvRows.push(cleanValues.join(","))
            })

            csvContent = csvRows.join("\n")
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to parse Excel file",
              variant: "destructive"
            })
            return
          }
        } else {
          // Handle CSV files
          csvContent = e.target?.result as string
        }

        if (!csvContent) return

        // Validate the file
        setIsValidating(true)
        try {
          console.log(
            "[FRONTEND] CSV content being sent for validation:",
            csvContent.substring(0, 200) + "..."
          )
          const result = await validateDatasetAction(optimizationId, csvContent)
          console.log("[FRONTEND] Validation result:", result)
          if (result.isSuccess && result.data) {
            setValidationResult(result.data)
          } else {
            toast({
              title: "Validation failed",
              description: result.message,
              variant: "destructive"
            })
          }
        } catch (error) {
          console.error("[FRONTEND] Validation error:", error)
          toast({
            title: "Error",
            description: "Failed to validate file",
            variant: "destructive"
          })
        } finally {
          setIsValidating(false)
        }
      }

      // Read file based on type
      if (file.name.endsWith(".xlsx")) {
        reader.readAsArrayBuffer(file)
      } else {
        reader.readAsText(file)
      }
    },
    [optimizationId]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "text/csv": [".csv"],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xlsx"
      ]
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024 // 10MB
  })

  // Handle upload
  const handleUpload = async () => {
    if (!uploadedFile || !validationResult) return

    const reader = new FileReader()
    reader.onload = async e => {
      let csvContent: string

      if (uploadedFile.name.endsWith(".xlsx")) {
        // Handle Excel files - convert to CSV format
        try {
          const ExcelJS = (await import("exceljs")).default
          const workbook = new ExcelJS.Workbook()
          await workbook.xlsx.load(e.target?.result as ArrayBuffer)

          const worksheet = workbook.getWorksheet(1) // Get first worksheet
          if (!worksheet) {
            toast({
              title: "Error",
              description: "No data found in Excel file",
              variant: "destructive"
            })
            return
          }

          // Convert to CSV format
          const csvRows: string[] = []
          worksheet.eachRow((row, rowNumber) => {
            const values = row.values as any[]
            // Skip the first element (it's undefined in ExcelJS)
            const cleanValues = values
              .slice(1)
              .map(val =>
                val === null || val === undefined ? "" : String(val)
              )
            csvRows.push(cleanValues.join(","))
          })

          csvContent = csvRows.join("\n")
        } catch (error) {
          toast({
            title: "Error",
            description: "Failed to parse Excel file",
            variant: "destructive"
          })
          return
        }
      } else {
        // Handle CSV files
        csvContent = e.target?.result as string
      }

      if (!csvContent) return

      setIsUploading(true)
      setUploadProgress(0)

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90))
      }, 200)

      try {
        console.log(
          "[FRONTEND] Starting upload with CSV content:",
          csvContent.substring(0, 200) + "..."
        )
        const result = await uploadDatasetWorkflowAction(
          optimizationId,
          csvContent
        )

        console.log("[FRONTEND] Upload result:", result)

        clearInterval(progressInterval)
        setUploadProgress(100)

        if (result.isSuccess && result.data) {
          console.log("[FRONTEND] Upload successful, data:", result.data)
          toast({
            title: "Upload successful",
            description: `Successfully uploaded ${result.data.validRows} experiments`
          })

          // Call success callback
          onUploadSuccess(result.data.uploadedSamples, result.data.batchId)

          // Reset state
          setUploadedFile(null)
          setValidationResult(null)
        } else {
          toast({
            title: "Upload failed",
            description: result.message,
            variant: "destructive"
          })
        }
      } catch (error) {
        clearInterval(progressInterval)
        toast({
          title: "Error",
          description: "Failed to upload dataset",
          variant: "destructive"
        })
      } finally {
        setIsUploading(false)
        setUploadProgress(0)
      }
    }

    // Read file based on type
    if (uploadedFile.name.endsWith(".xlsx")) {
      reader.readAsArrayBuffer(uploadedFile)
    } else {
      reader.readAsText(uploadedFile)
    }
  }

  // Generate and download CSV template
  const handleDownloadTemplate = async () => {
    setIsGeneratingTemplate(true)
    try {
      const result = await generateTemplateAction(optimizationId)

      if (result.isSuccess && result.data) {
        // Create and download file
        const blob = new Blob([result.data.csvContent], { type: "text/csv" })
        const url = URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url
        a.download = result.data.filename
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        toast({
          title: "Template downloaded",
          description: "CSV template has been downloaded successfully"
        })
      } else {
        toast({
          title: "Download failed",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate template",
        variant: "destructive"
      })
    } finally {
      setIsGeneratingTemplate(false)
    }
  }

  // Generate and download Excel template
  const handleDownloadExcelTemplate = async () => {
    setIsGeneratingTemplate(true)
    try {
      console.log("[FRONTEND] Starting Excel template download")
      const result = await generateExcelTemplateAction(optimizationId)
      console.log("[FRONTEND] Excel template action result:", result)

      if (result.isSuccess && result.data) {
        console.log(
          "[FRONTEND] Received buffer array length:",
          result.data.buffer.length
        )

        // Convert array back to Uint8Array
        const uint8Array = new Uint8Array(result.data.buffer)
        console.log(
          "[FRONTEND] Converted to Uint8Array, size:",
          uint8Array.byteLength
        )

        // Create and download Excel file
        const blob = new Blob([uint8Array], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        })

        console.log("[FRONTEND] Created blob size:", blob.size)

        const url = URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url
        a.download = result.data.filename
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        console.log(
          "[FRONTEND] Excel file download triggered:",
          result.data.filename
        )

        toast({
          title: "Excel template downloaded",
          description:
            "Excel template with data validation and dropdown menus has been downloaded"
        })
      } else {
        console.error(
          "[FRONTEND] Excel template generation failed:",
          result.message
        )
        toast({
          title: "Download failed",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("[FRONTEND] Excel template download error:", error)
      toast({
        title: "Error",
        description: "Failed to generate Excel template",
        variant: "destructive"
      })
    } finally {
      setIsGeneratingTemplate(false)
    }
  }

  // Clear uploaded file
  const clearFile = () => {
    setUploadedFile(null)
    setValidationResult(null)
  }

  return (
    <div className="space-y-4">
      {/* Template Download Section */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="flex items-center font-medium">
                <Download className="mr-2 size-4" />
                Download Template
              </h3>
              <p className="text-muted-foreground mt-1 text-sm">
                Get the correct format for your optimization parameters
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleDownloadExcelTemplate}
                disabled={isGeneratingTemplate}
                variant="default"
                size="sm"
              >
                {isGeneratingTemplate ? (
                  <Loader2 className="mr-2 size-4 animate-spin" />
                ) : (
                  <Download className="mr-2 size-4" />
                )}
                Excel Template
              </Button>
              <Button
                onClick={handleDownloadTemplate}
                disabled={isGeneratingTemplate}
                variant="outline"
                size="sm"
              >
                {isGeneratingTemplate ? (
                  <Loader2 className="mr-2 size-4 animate-spin" />
                ) : (
                  <Download className="mr-2 size-4" />
                )}
                CSV Template
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File Upload Section */}
      <Card>
        <CardContent className="space-y-4 pt-6">
          <div className="flex items-center">
            <Upload className="mr-2 size-4" />
            <h3 className="font-medium">Upload Dataset</h3>
          </div>
          {/* Upload Area */}
          <div
            {...getRootProps()}
            className={`cursor-pointer rounded-lg border-2 border-dashed p-6 text-center transition-colors ${
              isDragActive
                ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
                : "border-gray-300 hover:border-gray-400"
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto mb-3 size-8 text-gray-400" />
            {isDragActive ? (
              <p className="text-blue-600 dark:text-blue-400">
                Drop the file here...
              </p>
            ) : (
              <div>
                <p className="mb-1 text-gray-600 dark:text-gray-400">
                  Drag and drop a CSV or Excel file here, or click to select
                </p>
                <p className="text-xs text-gray-500">
                  Supports CSV and Excel (.xlsx) files (max 10MB)
                </p>
              </div>
            )}
          </div>

          {/* Uploaded File Info */}
          {uploadedFile && (
            <div className="flex items-center justify-between rounded-lg bg-gray-50 p-3 dark:bg-gray-800">
              <div className="flex items-center">
                <FileText className="mr-2 size-4 text-blue-500" />
                <span className="text-sm font-medium">{uploadedFile.name}</span>
                <Badge variant="secondary" className="ml-2">
                  {(uploadedFile.size / 1024).toFixed(1)} KB
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFile}
                className="text-gray-500 hover:text-red-500"
              >
                <X className="size-4" />
              </Button>
            </div>
          )}

          {/* Validation Status */}
          {isValidating && (
            <div className="flex items-center justify-center p-3">
              <Loader2 className="mr-2 size-4 animate-spin" />
              <span className="text-sm">Validating file...</span>
            </div>
          )}

          {/* Validation Results */}
          {validationResult && (
            <div className="space-y-3">
              <Alert
                className={
                  validationResult.isValid
                    ? "border-green-200 py-3"
                    : "border-yellow-200 py-3"
                }
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {validationResult.isValid ? (
                      <CheckCircle className="size-4 text-green-500" />
                    ) : (
                      <AlertCircle className="size-4 text-yellow-500" />
                    )}
                    <span className="ml-2 text-sm font-medium">
                      {validationResult.isValid
                        ? "Validation Passed"
                        : "Validation Issues Found"}
                    </span>
                  </div>
                  <div className="text-muted-foreground flex items-center gap-3 text-xs">
                    <span>Total: {validationResult.totalRows}</span>
                    <span>Valid: {validationResult.validRows}</span>
                    {validationResult.errors.length > 0 && (
                      <span className="text-yellow-600">
                        Issues: {validationResult.errors.length}
                      </span>
                    )}
                  </div>
                </div>
              </Alert>

              {/* Error Details */}
              {validationResult.errors.length > 0 && (
                <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:bg-yellow-950">
                  <div className="mb-2 flex items-center">
                    <AlertCircle className="mr-2 size-4 text-yellow-500" />
                    <span className="text-sm font-medium">
                      Issues Found ({validationResult.errors.length})
                    </span>
                  </div>
                  <div className="max-h-32 space-y-1 overflow-y-auto">
                    {validationResult.errors
                      .slice(0, 5)
                      .map((error: DataValidationError, index: number) => (
                        <div key={index} className="text-xs">
                          <span className="font-medium">
                            Row {error.row}, {error.column}:
                          </span>
                          <span className="ml-1">{error.message}</span>
                        </div>
                      ))}
                    {validationResult.errors.length > 5 && (
                      <p className="text-muted-foreground text-xs">
                        ... and {validationResult.errors.length - 5} more issues
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Upload Progress */}
          {isUploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Uploading dataset...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {/* Upload Button */}
          {validationResult && validationResult.validRows > 0 && (
            <Button
              onClick={handleUpload}
              disabled={isUploading}
              className="w-full"
            >
              {isUploading ? (
                <Loader2 className="mr-2 size-4 animate-spin" />
              ) : (
                <Upload className="mr-2 size-4" />
              )}
              Upload {validationResult.validRows} Experiments
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <div className="bg-muted/50 rounded-lg border p-3">
        <div className="mb-2 flex items-center">
          <FileText className="mr-2 size-4" />
          <span className="text-sm font-medium">Upload Instructions</span>
        </div>
        <ul className="text-muted-foreground space-y-1 text-xs">
          <li>
            • <strong>Excel Template (Recommended):</strong> Includes data
            validation, dropdown menus, and protected headers
          </li>
          <li>
            • <strong>CSV Template:</strong> Simple format for basic spreadsheet
            applications
          </li>
          <li>• Each experiment should be on a separate line</li>
          <li>• Include values for all parameters and targets</li>
          <li>
            • Use dropdown menus for categorical parameters (Excel template
            only)
          </li>
          <li>
            • Use numbers for numerical values and exact text for categories
          </li>
          <li>• Ensure all values are within allowed ranges</li>
          <li>• Do not leave any fields blank</li>
        </ul>
      </div>
    </div>
  )
}
