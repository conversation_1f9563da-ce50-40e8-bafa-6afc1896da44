// app/api/feedback/route.ts
import { NextRequest, NextResponse } from "next/server"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import {
  createFeedbackAction,
  getUserFeedbackAction
} from "@/actions/db/feedback-actions"
import { z } from "zod"
import { feedbackCategories } from "@/db/schema/feedback-schema"

// Validation schema for feedback submission
const feedbackSchema = z.object({
  category: z.enum(feedbackCategories),
  title: z
    .string()
    .min(3, "Title must be at least 3 characters")
    .max(100, "Title must be less than 100 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  rating: z.number().min(1).max(5).optional(),
  metadata: z.record(z.any()).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 FEEDBACK API POST - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return NextResponse.json(
        { error: "You must be signed in to submit feedback" },
        { status: 401 }
      )
    }

    // Parse and validate the request body
    const body = await request.json()

    try {
      const validatedData = feedbackSchema.parse(body)

      // Submit the feedback
      const result = await createFeedbackAction(validatedData)

      if (result.isSuccess) {
        return NextResponse.json(result, { status: 201 })
      } else {
        return NextResponse.json({ error: result.message }, { status: 400 })
      }
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: validationError.errors },
          { status: 400 }
        )
      }
      throw validationError
    }
  } catch (error) {
    console.error("Error handling feedback submission:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Check authentication
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 FEEDBACK API GET - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return NextResponse.json(
        { error: "You must be signed in to view feedback" },
        { status: 401 }
      )
    }

    // Get the user's feedback
    const result = await getUserFeedbackAction()

    if (result.isSuccess) {
      return NextResponse.json(result)
    } else {
      return NextResponse.json({ error: result.message }, { status: 400 })
    }
  } catch (error) {
    console.error("Error retrieving feedback:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
