/*
This server page provides detailed documentation about optimization results.
*/

"use server"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  ArrowLeft,
  Trophy,
  ArrowUp,
  ArrowDown,
  Target,
  Calculator,
  BarChart4,
  Sigma
} from "lucide-react"
import Link from "next/link"

export default async function OptimizationResultsHelpPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/help">
            <ArrowLeft className="mr-2 size-4" />
            Back to Help Center
          </Link>
        </Button>
      </div>

      <h1 className="flex items-center gap-2 text-3xl font-bold">
        <Trophy className="size-8 text-yellow-500" />
        Understanding Optimization Results
      </h1>

      <p className="text-muted-foreground text-lg">
        Learn how to interpret the results of your optimizations and understand
        how the Best Results are calculated.
      </p>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="size-5 text-yellow-500" />
              Best Results Overview
            </CardTitle>
            <CardDescription>
              How the system identifies the best parameter values
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              The Best Results card shows the optimal parameter values found so
              far based on your optimization objectives. This is determined
              differently depending on whether you're running a single-target or
              multi-target optimization.
            </p>

            <div className="bg-muted space-y-2 rounded-md p-4">
              <h3 className="font-medium">Key Components:</h3>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>Best target value(s) achieved</li>
                <li>Parameter values that produced the best result</li>
                <li>
                  For multi-target: composite score and target contributions
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart4 className="size-5 text-blue-500" />
              Single-Target Optimization
            </CardTitle>
            <CardDescription>
              How best results are determined for single-target optimizations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              For single-target optimizations, the system simply identifies the
              experiment that produced the highest (for MAX) or lowest (for MIN)
              target value.
            </p>

            <div className="bg-muted space-y-2 rounded-md p-4">
              <div className="flex items-center gap-2 font-medium">
                <ArrowUp className="size-4 text-green-500" /> MAX Mode
              </div>
              <p className="text-sm">
                The experiment with the highest target value is considered best.
              </p>
            </div>

            <div className="bg-muted space-y-2 rounded-md p-4">
              <div className="flex items-center gap-2 font-medium">
                <ArrowDown className="size-4 text-green-500" /> MIN Mode
              </div>
              <p className="text-sm">
                The experiment with the lowest target value is considered best.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="size-5 text-purple-500" />
              Multi-Target Optimization
            </CardTitle>
            <CardDescription>
              How best results are calculated for multi-target optimizations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <p>
              Multi-target optimizations are more complex because they involve
              balancing multiple objectives. The system calculates a composite
              score that represents the overall performance across all targets.
            </p>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="flex items-center gap-2 font-medium">
                  <Calculator className="size-4" />
                  Normalization Process
                </h3>
                <p className="text-sm">
                  Each target value is normalized to a 0-1 scale to make
                  different targets comparable:
                </p>
                <div className="bg-background rounded p-2 font-mono text-sm">
                  normalized = (value - min) / (max - min)
                </div>
                <p className="text-sm">
                  For MIN targets, the normalized value is inverted:
                </p>
                <div className="bg-background rounded p-2 font-mono text-sm">
                  normalized = 1 - normalized
                </div>
                <p className="text-muted-foreground text-sm">
                  This ensures that higher normalized values always represent
                  "better" performance.
                </p>
              </div>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="flex items-center gap-2 font-medium">
                  <Sigma className="size-4" />
                  Composite Score Calculation
                </h3>
                <p className="text-sm">
                  The composite score is calculated as a weighted sum of
                  normalized target values:
                </p>
                <div className="bg-background rounded p-2 font-mono text-sm">
                  score = Σ(normalized_value × weight)
                </div>
                <p className="text-sm">
                  Current implementation uses equal weights for all targets:
                </p>
                <div className="bg-background rounded p-2 font-mono text-sm">
                  weight = 1 / number_of_targets
                </div>
                <p className="text-muted-foreground text-sm">
                  The composite score ranges from 0 to 1, where 1 represents the
                  theoretical best possible performance.
                </p>
              </div>
            </div>

            <div className="bg-primary/10 space-y-2 rounded-md p-4">
              <h3 className="font-medium">Target Contributions</h3>
              <p className="text-sm">For each target, the system displays:</p>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>
                  Raw value with direction indicator (↑ for MAX, ↓ for MIN)
                </li>
                <li>Weight percentage (currently equal for all targets)</li>
                <li>Contribution percentage to the composite score</li>
                <li>Progress bar showing the normalized value</li>
              </ul>
              <p className="text-muted-foreground text-sm">
                This helps you understand how each target contributes to the
                overall optimization performance.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Best Parameters</CardTitle>
            <CardDescription>
              Understanding the parameter values display
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              The Best Parameters section shows the exact parameter values that
              produced the best result. These are the values you should use to
              achieve the optimal performance based on your optimization
              objectives.
            </p>

            <div className="bg-muted space-y-2 rounded-md p-4">
              <h3 className="font-medium">Parameter Display:</h3>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>Numerical parameters show appropriate decimal precision</li>
                <li>Categorical parameters show the selected category</li>
                <li>Parameters are grouped for easy reading</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Interpreting Results</CardTitle>
            <CardDescription>
              Tips for understanding your optimization results
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-muted space-y-2 rounded-md p-4">
              <h3 className="font-medium">For Single-Target Optimizations:</h3>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>
                  Focus on the best target value and its corresponding
                  parameters
                </li>
                <li>
                  Check the Optimization Progress chart to see improvement over
                  time
                </li>
                <li>
                  Use Parameter Impact Analysis to understand parameter
                  importance
                </li>
              </ul>
            </div>

            <div className="bg-muted space-y-2 rounded-md p-4">
              <h3 className="font-medium">For Multi-Target Optimizations:</h3>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>Look at the composite score for overall performance</li>
                <li>
                  Examine individual target contributions to understand
                  trade-offs
                </li>
                <li>
                  Consider if the current target weights align with your
                  priorities
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
