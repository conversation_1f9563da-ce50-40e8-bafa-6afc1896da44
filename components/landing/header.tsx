/*
This client component provides the header for the app.
*/

"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  SignedIn,
  SignedOut,
  SignInButton,
  SignUpButton,
  UserButton
} from "@clerk/nextjs"
import { motion } from "framer-motion"
import {
  Menu,
  Receipt,
  X,
  ArrowRight,
  LayoutDashboard,
  Shield,
  CheckCircle
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useLoading } from "@/contexts/loading-context"
import ResourcesDropdown from "./resources-dropdown"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { BRAND } from "@/lib/constants"

const navLinks = [
  { href: "/pricing", label: "Pricing" },
  {
    href: "/security",
    label: "Security",
    icon: Shield,
    isSecurityLink: true,
    description: "Enterprise-grade security & compliance"
  }
]

// Resource links are now defined in the ResourcesDropdown component

const signedInLinks = [{ href: "/dashboard", label: "Dashboard" }]

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const router = useRouter()
  const { startLoading } = useLoading()

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape" && isMenuOpen) {
      setIsMenuOpen(false)
    }
  }

  // Handle dashboard navigation with loading overlay
  const handleDashboardClick = (
    e: React.MouseEvent<HTMLAnchorElement>,
    path: string
  ) => {
    e.preventDefault()
    startLoading()

    // Add a small delay before navigation to ensure the loading overlay appears
    setTimeout(() => {
      router.push(path)
    }, 100) // 100ms delay (reduced from 300ms)
  }

  useEffect(() => {
    // Only run on client side
    if (typeof window === "undefined") return

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }

    // Set initial scroll state
    setIsScrolled(window.scrollY > 0)

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      onKeyDown={handleKeyDown}
      className={`sticky top-0 z-50 transition-colors ${
        isScrolled
          ? "bg-background/80 shadow-sm backdrop-blur-sm"
          : "bg-background"
      }`}
    >
      <div className="container mx-auto flex max-w-7xl items-center justify-between p-4">
        <motion.div
          className="flex items-center hover:cursor-pointer hover:opacity-80"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Link
            href="/"
            className="from-primary bg-gradient-to-r to-blue-600 bg-clip-text text-xl font-bold text-transparent"
            prefetch={false}
          >
            {BRAND.NAME}
          </Link>
        </motion.div>

        <nav className="absolute left-1/2 hidden -translate-x-1/2 items-center space-x-4 md:flex">
          {navLinks.map(link => (
            <motion.div
              key={link.href}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {link.isSecurityLink ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        href={link.href}
                        className="group flex items-center gap-2 rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-2 text-sm font-medium text-blue-700 shadow-sm transition-all hover:border-blue-300 hover:from-blue-100 hover:to-indigo-100 hover:shadow-md"
                        prefetch={false}
                      >
                        <Shield className="size-4 text-blue-600" />
                        {link.label}
                        <CheckCircle className="size-3 text-blue-500 opacity-75" />
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="font-medium">{link.description}</p>
                      <p className="text-muted-foreground text-xs">
                        SOC 2 • GDPR • AWS Security
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <Link
                  href={link.href}
                  className="text-muted-foreground hover:text-foreground rounded-full px-3 py-1 transition"
                  prefetch={false}
                >
                  {link.label}
                </Link>
              )}
            </motion.div>
          ))}

          <ResourcesDropdown />
        </nav>

        <div className="flex items-center space-x-4">
          <SignedOut>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/dashboard"
                    prefetch={false}
                    onClick={e => handleDashboardClick(e, "/dashboard")}
                  >
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        className="from-primary hover:from-primary/90 bg-gradient-to-r to-blue-600 shadow-sm hover:to-blue-700"
                        type="button"
                      >
                        Dashboard <ArrowRight className="ml-1 size-4" />
                      </Button>
                    </motion.div>
                  </Link>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Access your experiments and optimization tools</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </SignedOut>

          <SignedIn>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href="/dashboard/home"
                    prefetch={false}
                    onClick={e => handleDashboardClick(e, "/dashboard/home")}
                  >
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        className="from-primary hover:from-primary/90 bg-gradient-to-r to-blue-600 shadow-sm hover:to-blue-700"
                        type="button"
                      >
                        Dashboard <ArrowRight className="ml-1 size-4" />
                      </Button>
                    </motion.div>
                  </Link>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Access your experiments and optimization tools</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <UserButton />
          </SignedIn>

          <motion.div
            className="md:hidden"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMenu}
              aria-label="Toggle menu"
              aria-expanded={isMenuOpen}
              className="min-h-[44px] min-w-[44px]"
              type="button"
            >
              {isMenuOpen ? (
                <X className="size-6" />
              ) : (
                <Menu className="size-6" />
              )}
            </Button>
          </motion.div>
        </div>
      </div>

      {isMenuOpen && (
        <>
          {/* Overlay to close menu when clicking outside */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black/20 md:hidden"
            onClick={toggleMenu}
          />
          <motion.nav
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-background/95 text-foreground relative z-50 border-t p-4 shadow-lg backdrop-blur-sm md:hidden"
          >
            <ul className="space-y-1">
              <li>
                <Link
                  href="/"
                  className="hover:bg-muted block flex min-h-[44px] items-center rounded-lg p-3 text-base font-medium transition-colors"
                  onClick={toggleMenu}
                  prefetch={false}
                >
                  Home
                </Link>
              </li>
              {navLinks.map(link => (
                <li key={link.href}>
                  {link.isSecurityLink ? (
                    <Link
                      href={link.href}
                      className="flex items-center gap-2 rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-3 text-sm font-medium text-blue-700 transition-all hover:border-blue-300 hover:from-blue-100 hover:to-indigo-100"
                      onClick={toggleMenu}
                      prefetch={false}
                    >
                      <Shield className="size-4 text-blue-600" />
                      {link.label}
                      <CheckCircle className="size-3 text-blue-500 opacity-75" />
                    </Link>
                  ) : (
                    <Link
                      href={link.href}
                      className="hover:bg-muted block flex min-h-[44px] items-center rounded-lg p-3 text-base font-medium transition-colors"
                      onClick={toggleMenu}
                      prefetch={false}
                    >
                      {link.label}
                    </Link>
                  )}
                </li>
              ))}

              <li className="pb-2 pt-4">
                <span className="text-muted-foreground px-3 text-sm font-semibold uppercase tracking-wider">
                  Resources
                </span>
              </li>
              {/* Import resource links from the ResourcesDropdown component */}
              <li>
                <Link
                  href="/tutorials"
                  className="hover:bg-muted block flex min-h-[44px] items-center rounded-lg p-3 text-base font-medium transition-colors"
                  onClick={toggleMenu}
                  prefetch={false}
                >
                  Tutorials
                </Link>
              </li>
              {/* Case Studies - Hidden as requested */}
              {/*
            <li className="pl-2">
              <Link
                href="/case-studies"
                className="block hover:underline"
                onClick={toggleMenu}
                prefetch={false}
              >
                Case Studies
              </Link>
            </li>
            */}
              <li>
                <Link
                  href="/features"
                  className="hover:bg-muted block flex min-h-[44px] items-center rounded-lg p-3 text-base font-medium transition-colors"
                  onClick={toggleMenu}
                  prefetch={false}
                >
                  Features
                </Link>
              </li>
              <li>
                <Link
                  href="/faq"
                  className="hover:bg-muted block flex min-h-[44px] items-center rounded-lg p-3 text-base font-medium transition-colors"
                  onClick={toggleMenu}
                  prefetch={false}
                >
                  FAQ
                </Link>
              </li>

              <SignedIn>
                <li className="pb-2 pt-4">
                  <span className="text-muted-foreground px-3 text-sm font-semibold uppercase tracking-wider">
                    Dashboard
                  </span>
                </li>
                {signedInLinks.map(link => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="hover:bg-muted block flex min-h-[44px] items-center rounded-lg p-3 text-base font-medium transition-colors"
                      onClick={e => {
                        toggleMenu()
                        handleDashboardClick(e, "/dashboard/home")
                      }}
                      prefetch={false}
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </SignedIn>
            </ul>
          </motion.nav>
        </>
      )}
    </motion.header>
  )
}
