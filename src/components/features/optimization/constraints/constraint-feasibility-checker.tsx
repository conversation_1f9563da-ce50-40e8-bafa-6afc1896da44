"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertTriangle, CheckCircle, Info } from "lucide-react"
import { Constraint } from "@/lib/constraints"
import { Parameter } from "@/types/optimization-types"

interface ConstraintFeasibilityCheckerProps {
  constraints: Constraint[]
  parameters: Parameter[]
  onFeasibilityCheck?: (result: FeasibilityResult) => void
}

interface FeasibilityResult {
  feasible: boolean
  message: string
  issues?: string[]
  suggestions?: string[]
  note?: string
}

export function ConstraintFeasibilityChecker({
  constraints,
  parameters,
  onFeasibilityCheck
}: ConstraintFeasibilityCheckerProps) {
  const [isChecking, setIsChecking] = useState(false)
  const [result, setResult] = useState<FeasibilityResult | null>(null)

  const checkFeasibility = async () => {
    if (constraints.length === 0) {
      const emptyResult = {
        feasible: true,
        message: "No constraints to check"
      }
      setResult(emptyResult)
      onFeasibilityCheck?.(emptyResult)
      return
    }

    setIsChecking(true)
    try {
      const response = await fetch("/api/proxy/constraints/check-feasibility", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          constraints,
          parameters
        })
      })

      if (!response.ok) {
        throw new Error(`Feasibility check failed: ${response.statusText}`)
      }

      const feasibilityResult = await response.json()
      setResult(feasibilityResult)
      onFeasibilityCheck?.(feasibilityResult)
    } catch (error) {
      console.error("Error checking constraint feasibility:", error)
      const errorResult = {
        feasible: false,
        message:
          error instanceof Error ? error.message : "Feasibility check failed"
      }
      setResult(errorResult)
      onFeasibilityCheck?.(errorResult)
    } finally {
      setIsChecking(false)
    }
  }

  const getAlertVariant = () => {
    if (!result) return "default"
    if (!result.feasible) return "destructive"
    if (result.note) return "default"
    return "default"
  }

  const getIcon = () => {
    if (!result) return <Info className="size-4" />
    if (!result.feasible) return <AlertTriangle className="size-4" />
    return <CheckCircle className="size-4" />
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="size-5" />
          Constraint Feasibility Checker
        </CardTitle>
        <CardDescription>
          Check if your constraints are mathematically feasible with the given
          parameter bounds. This can help prevent "No feasible point found"
          errors during optimization.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Button
            onClick={checkFeasibility}
            disabled={isChecking || constraints.length === 0}
            variant="outline"
          >
            {isChecking && <Loader2 className="mr-2 size-4 animate-spin" />}
            Check Feasibility
          </Button>
          <Badge variant="secondary">
            {constraints.length} constraint{constraints.length !== 1 ? "s" : ""}
          </Badge>
          <Badge variant="outline">
            {parameters.length} parameter{parameters.length !== 1 ? "s" : ""}
          </Badge>
        </div>

        {result && (
          <Alert variant={getAlertVariant()}>
            {getIcon()}
            <AlertTitle>
              {result.feasible
                ? "Constraints Appear Feasible"
                : "Feasibility Issues Detected"}
            </AlertTitle>
            <AlertDescription className="space-y-2">
              <p>{result.message}</p>

              {result.issues && result.issues.length > 0 && (
                <div className="space-y-1">
                  <p className="font-medium">Issues found:</p>
                  <ul className="list-inside list-disc space-y-1">
                    {result.issues.map((issue, index) => (
                      <li key={index} className="text-sm">
                        {issue}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {result.suggestions && result.suggestions.length > 0 && (
                <div className="space-y-1">
                  <p className="font-medium">Suggestions:</p>
                  <ul className="list-inside list-disc space-y-1">
                    {result.suggestions.map((suggestion, index) => (
                      <li
                        key={index}
                        className="text-sm text-blue-700 dark:text-blue-300"
                      >
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {result.note && (
                <p className="text-muted-foreground text-sm italic">
                  Note: {result.note}
                </p>
              )}
            </AlertDescription>
          </Alert>
        )}

        {constraints.length === 0 && (
          <Alert>
            <Info className="size-4" />
            <AlertTitle>No Constraints</AlertTitle>
            <AlertDescription>
              Add some constraints to check their feasibility. Without
              constraints, all parameter combinations within bounds are
              feasible.
            </AlertDescription>
          </Alert>
        )}

        <div className="text-muted-foreground space-y-2 text-sm">
          <p>
            <strong>What this checks:</strong>
          </p>
          <ul className="ml-4 list-inside list-disc space-y-1">
            <li>
              Linear constraints: Whether the constraint equation can be
              satisfied within parameter bounds
            </li>
            <li>Coefficient-parameter mismatches</li>
            <li>
              Impossible constraint conditions (e.g., minimum LHS &gt; RHS for ≤
              constraints)
            </li>
          </ul>
          <p className="text-xs">
            <strong>Note:</strong> This is a basic feasibility check. Complex
            interactions between multiple constraints may still cause issues
            that aren't detected here.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
