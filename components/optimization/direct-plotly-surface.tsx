"use client"

import { useEffect, useRef } from "react"
import { Grid3X3 } from "lucide-react"

// Interface for the component props
interface DirectPlotlySurfaceProps {
  x: number[]
  y: number[]
  z: number[][]
  xLabel: string
  yLabel: string
  zLabel: string
  colorScale: string
  title: string
}

export function DirectPlotlySurface({
  x,
  y,
  z,
  xLabel,
  yLabel,
  zLabel,
  colorScale,
  title
}: DirectPlotlySurfaceProps) {
  // Reference to the div element where the plot will be rendered
  const plotRef = useRef<HTMLDivElement>(null)

  // Generate a unique ID for this instance
  const uniqueId = useRef(`plot-${Math.random().toString(36).substring(2, 9)}`)

  // Function to create or update the plot
  const createOrUpdatePlot = () => {
    if (!plotRef.current) return

    // Log the current state
    console.log(`Creating/updating plot with colorScale: ${colorScale}`)

    // Check if Plotly is available
    if (typeof window !== "undefined" && window.Plotly) {
      try {
        // Create the data for the surface plot
        const data = [
          {
            type: "surface",
            x,
            y,
            z,
            colorscale: colorScale,
            contours: {
              z: {
                show: true,
                usecolormap: true,
                highlightcolor: "#fff",
                project: { z: true }
              }
            },
            hovertemplate:
              `${xLabel}: %{x}<br>` +
              `${yLabel}: %{y}<br>` +
              `${zLabel}: %{z}<extra></extra>`
          }
        ]

        // Create the layout
        const layout = {
          title,
          autosize: true,
          margin: { l: 65, r: 50, b: 65, t: 90 },
          scene: {
            xaxis: { title: xLabel },
            yaxis: { title: yLabel },
            zaxis: { title: zLabel },
            camera: {
              eye: { x: 1.5, y: 1.5, z: 1 }
            }
          }
        }

        // Create a new plot
        window.Plotly.newPlot(plotRef.current, data, layout, {
          displayModeBar: true,
          responsive: true
        })

        console.log(`Plot created/updated with colorScale: ${colorScale}`)
      } catch (error) {
        console.error("Error creating/updating plot:", error)
      }
    } else {
      console.error("Plotly is not available")
    }
  }

  // Effect to create the plot when the component mounts
  useEffect(() => {
    // Load Plotly.js dynamically
    const loadPlotly = async () => {
      if (typeof window !== "undefined" && !window.Plotly) {
        try {
          console.log("Loading Plotly.js...")
          // @ts-ignore
          window.Plotly = await import("plotly.js-dist")
          console.log("Plotly.js loaded successfully")
          createOrUpdatePlot()
        } catch (error) {
          console.error("Error loading Plotly.js:", error)
        }
      } else {
        createOrUpdatePlot()
      }
    }

    loadPlotly()

    // Cleanup function to remove the plot when the component unmounts
    return () => {
      if (typeof window !== "undefined" && window.Plotly && plotRef.current) {
        try {
          window.Plotly.purge(plotRef.current)
          console.log(`Plot ${uniqueId.current} purged`)
        } catch (error) {
          console.error("Error purging plot:", error)
        }
      }
    }
  }, [x, y, z, xLabel, yLabel, zLabel, colorScale, title])

  // Effect to handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== "undefined" && window.Plotly && plotRef.current) {
        window.Plotly.Plots.resize(plotRef.current)
      }
    }

    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  return (
    <div
      ref={plotRef}
      id={uniqueId.current}
      className="size-full"
      style={{ minHeight: "500px" }}
    />
  )
}
