/*
Configures Tailwind CSS for the app.
*/

import type { Config } from "tailwindcss"

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}"
  ],
  prefix: "",
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    container: { center: true, padding: "2rem", screens: { "2xl": "1400px" } },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))"
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))"
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))"
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))"
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))"
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))"
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))"
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))"
        }
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)"
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" }
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" }
        },
        gradient: { to: { backgroundPosition: "var(--bg-size) 0" } },
        "scientific-pulse": {
          "0%, 100%": {
            opacity: "0.4",
            transform: "scale(1)"
          },
          "50%": {
            opacity: "0.8",
            transform: "scale(1.05)"
          }
        },
        "data-flow": {
          "0%": {
            transform: "translateX(-100%) scaleX(0)",
            opacity: "0"
          },
          "50%": {
            opacity: "1",
            transform: "translateX(0%) scaleX(1)"
          },
          "100%": {
            transform: "translateX(100%) scaleX(0)",
            opacity: "0"
          }
        },
        "research-glow": {
          "0%, 100%": {
            boxShadow: "0 0 20px rgba(59, 130, 246, 0.1)"
          },
          "50%": {
            boxShadow: "0 0 30px rgba(59, 130, 246, 0.2), 0 0 40px rgba(147, 51, 234, 0.1)"
          }
        },
        "lightning-pulse": {
          "0%, 100%": {
            opacity: "0.3",
            filter: "drop-shadow(0 0 4px rgba(59, 130, 246, 0.4))"
          },
          "25%": {
            opacity: "0.8",
            filter: "drop-shadow(0 0 8px rgba(59, 130, 246, 0.8))"
          },
          "50%": {
            opacity: "1",
            filter: "drop-shadow(0 0 12px rgba(59, 130, 246, 1)) drop-shadow(0 0 20px rgba(147, 51, 234, 0.5))"
          },
          "75%": {
            opacity: "0.6",
            filter: "drop-shadow(0 0 6px rgba(59, 130, 246, 0.6))"
          }
        },
        "electric-spark": {
          "0%": {
            strokeDashoffset: "0",
            opacity: "0.8"
          },
          "50%": {
            strokeDashoffset: "-15",
            opacity: "1"
          },
          "100%": {
            strokeDashoffset: "-30",
            opacity: "0.8"
          }
        }
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        gradient: "gradient 8s linear infinite",
        "scientific-pulse": "scientific-pulse 3s ease-in-out infinite",
        "data-flow": "data-flow 4s ease-in-out infinite",
        "research-glow": "research-glow 4s ease-in-out infinite",
        "lightning-pulse": "lightning-pulse 2s ease-in-out infinite",
        "electric-spark": "electric-spark 1.5s linear infinite"
      }
    }
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography"),
    require("@tailwindcss/postcss")
  ]
} satisfies Config

export default config
