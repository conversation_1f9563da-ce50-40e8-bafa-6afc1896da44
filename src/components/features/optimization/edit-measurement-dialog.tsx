"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Edit, Save, X, AlertCircle } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { updateMeasurementWithDependenciesAction } from "@/actions/measurement-dependency-actions"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface EditMeasurementDialogProps {
  measurement: SelectMeasurement
  optimization: SelectOptimization
  onMeasurementUpdated: (updatedMeasurement: SelectMeasurement) => void
  trigger?: React.ReactNode
}

export function EditMeasurementDialog({
  measurement,
  optimization,
  onMeasurementUpdated,
  trigger
}: EditMeasurementDialogProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    targetValue: measurement.targetValue,
    targetValues: measurement.targetValues || {},
    parameters: measurement.parameters || {}
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setFormData({
        targetValue: measurement.targetValue,
        targetValues: measurement.targetValues || {},
        parameters: measurement.parameters || {}
      })
      setErrors({})
    }
  }, [open, measurement])

  // Get parameter names from optimization config or measurement parameters as fallback
  const getParameterNames = () => {
    // First try to get from optimization config
    if (optimization.config && typeof optimization.config === "object") {
      const configParams = (optimization.config as any).parameters

      // Check if parameters is an array (correct structure)
      if (Array.isArray(configParams)) {
        const paramNames = configParams
          .filter(param => param && typeof param === "object" && param.name)
          .map(param => param.name)
        if (paramNames.length > 0) {
          return paramNames
        }
      }

      // Fallback: check if parameters is an object (legacy structure)
      if (
        configParams &&
        typeof configParams === "object" &&
        !Array.isArray(configParams)
      ) {
        const paramNames = Object.keys(configParams)
        if (paramNames.length > 0) {
          return paramNames
        }
      }
    }

    // Fallback: get from measurement parameters
    if (measurement.parameters && typeof measurement.parameters === "object") {
      return Object.keys(measurement.parameters).filter(
        key => !key.startsWith("_")
      )
    }

    return []
  }

  const parameterNames = getParameterNames()

  // Check if this is a multi-target optimization
  const isMultiTarget =
    measurement.targetValues &&
    typeof measurement.targetValues === "object" &&
    Object.keys(measurement.targetValues).length > 1

  // Get other target names (excluding the primary target)
  const getOtherTargets = () => {
    if (!isMultiTarget) return []

    // First try to get from optimization config
    if (optimization.config && typeof optimization.config === "object") {
      const targetConfig = (optimization.config as any).target_config

      // Check if target_config is an array (multi-target)
      if (Array.isArray(targetConfig)) {
        const targetNames = targetConfig
          .filter(target => target && typeof target === "object" && target.name)
          .map(target => target.name)
          .filter(name => name !== optimization.targetName)
        if (targetNames.length > 0) {
          return targetNames
        }
      }
    }

    // Fallback: get from measurement targetValues
    const targetValues = measurement.targetValues as Record<string, number>
    if (!targetValues || typeof targetValues !== "object") return []

    return Object.keys(targetValues).filter(
      name => name !== optimization.targetName
    )
  }

  const otherTargets = getOtherTargets()

  // Debug logging (remove in production)
  console.log("Edit Dialog Debug:", {
    parameterNames,
    otherTargets,
    primaryTarget: optimization.targetName,
    isMultiTarget,
    measurementParams: measurement.parameters,
    measurementTargetValues: measurement.targetValues,
    optimizationConfig: optimization.config,
    configParameters: (optimization.config as any)?.parameters,
    configTargetConfig: (optimization.config as any)?.target_config
  })

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Validate primary target value
    if (!formData.targetValue || isNaN(parseFloat(formData.targetValue))) {
      newErrors.targetValue = "Target value must be a valid number"
    }

    // Validate multi-target values
    if (isMultiTarget) {
      const targetValues = formData.targetValues as Record<string, number>

      // Validate primary target in targetValues
      if (
        targetValues[optimization.targetName] === undefined ||
        isNaN(Number(targetValues[optimization.targetName]))
      ) {
        newErrors[`target_${optimization.targetName}`] =
          "Primary target value must be a valid number"
      }

      // Validate other targets
      otherTargets.forEach(targetName => {
        if (
          targetValues[targetName] === undefined ||
          isNaN(Number(targetValues[targetName]))
        ) {
          newErrors[`target_${targetName}`] =
            "Target value must be a valid number"
        }
      })
    }

    // Validate parameters
    parameterNames.forEach(paramName => {
      const value = (formData.parameters as Record<string, any>)[paramName]
      if (value === undefined || value === null || value === "") {
        newErrors[`param_${paramName}`] = "Parameter value is required"
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    // Store original data for potential rollback
    const originalData = {
      targetValue: measurement.targetValue,
      targetValues: measurement.targetValues,
      parameters: measurement.parameters
    }

    try {
      // Prepare the update data
      const updates: any = {
        parameters: formData.parameters
      }

      if (isMultiTarget) {
        // For multi-target, update both targetValue and targetValues
        const targetValues = formData.targetValues as Record<string, number>
        updates.targetValues = targetValues
        updates.targetValue = String(targetValues[optimization.targetName])
      } else {
        // For single target, update only targetValue
        updates.targetValue = formData.targetValue
      }

      const result = await updateMeasurementWithDependenciesAction(
        measurement.id,
        updates,
        optimization.id,
        optimization.optimizerId
      )

      if (result.isSuccess && result.data) {
        toast({
          title: "Success",
          description:
            "Measurement updated successfully. Dependencies have been recalculated."
        })
        onMeasurementUpdated(result.data)
        setOpen(false)
      } else {
        // Handle specific error cases
        let errorMessage = result.message
        if (result.message.includes("permission")) {
          errorMessage = "You don't have permission to update this measurement"
        } else if (result.message.includes("validation")) {
          errorMessage =
            "The measurement data is invalid. Please check your inputs."
        } else if (result.message.includes("not found")) {
          errorMessage =
            "This measurement no longer exists. It may have been deleted by another user."
        }

        toast({
          title: "Update Failed",
          description: errorMessage,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error updating measurement:", error)

      // Provide user-friendly error messages
      let errorMessage =
        "An unexpected error occurred while updating the measurement"
      if (error instanceof Error) {
        if (
          error.message.includes("network") ||
          error.message.includes("fetch")
        ) {
          errorMessage =
            "Network error. Please check your connection and try again."
        } else if (error.message.includes("timeout")) {
          errorMessage = "The request timed out. Please try again."
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleParameterChange = (paramName: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [paramName]: isNaN(Number(value)) ? value : Number(value)
      }
    }))

    // Clear error for this parameter
    if (errors[`param_${paramName}`]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[`param_${paramName}`]
        return newErrors
      })
    }
  }

  const handleTargetChange = (targetName: string, value: string) => {
    const numValue = parseFloat(value)

    if (targetName === optimization.targetName) {
      // Update primary target
      setFormData(prev => ({
        ...prev,
        targetValue: value,
        targetValues: isMultiTarget
          ? {
              ...prev.targetValues,
              [targetName]: numValue
            }
          : prev.targetValues
      }))
    } else {
      // Update other targets
      setFormData(prev => ({
        ...prev,
        targetValues: {
          ...prev.targetValues,
          [targetName]: numValue
        }
      }))
    }

    // Clear error for this target
    const errorKey =
      targetName === optimization.targetName
        ? "targetValue"
        : `target_${targetName}`
    if (errors[errorKey]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[errorKey]
        return newErrors
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="ghost" size="sm">
            <Edit className="size-4" />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Measurement</DialogTitle>
          <DialogDescription>
            Update the parameters and target values for this measurement.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Measurement Info */}
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {measurement.isRecommended ? "API Generated" : "Manual Entry"}
            </Badge>
            <span className="text-muted-foreground text-sm">
              Created: {new Date(measurement.createdAt).toLocaleString()}
            </span>
          </div>

          {/* Warning for API-generated measurements */}
          {measurement.isRecommended && (
            <Alert>
              <AlertCircle className="size-4" />
              <AlertDescription>
                This measurement was generated by the optimization algorithm.
                Editing it may affect the optimization's learning process.
              </AlertDescription>
            </Alert>
          )}

          {/* Target Values Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Target Values</h3>

            {/* Primary Target */}
            <div className="space-y-2">
              <Label htmlFor="primary-target">
                {optimization.targetName} (Primary Target)
                <span className="text-muted-foreground ml-1 text-xs">
                  (Original: {measurement.targetValue})
                </span>
              </Label>
              <Input
                id="primary-target"
                type="number"
                step="any"
                value={formData.targetValue}
                onChange={e =>
                  handleTargetChange(optimization.targetName, e.target.value)
                }
                className={errors.targetValue ? "border-red-500" : ""}
                placeholder={`Enter ${optimization.targetName} value`}
              />
              {errors.targetValue && (
                <p className="text-sm text-red-500">{errors.targetValue}</p>
              )}
            </div>

            {/* Other Targets */}
            {otherTargets.map(targetName => {
              const originalValue = (
                measurement.targetValues as Record<string, number>
              )?.[targetName]
              const currentValue = (
                formData.targetValues as Record<string, number>
              )[targetName]
              return (
                <div key={targetName} className="space-y-2">
                  <Label htmlFor={`target-${targetName}`}>
                    {targetName}
                    <span className="text-muted-foreground ml-1 text-xs">
                      (Original:{" "}
                      {originalValue !== undefined
                        ? originalValue
                        : "undefined"}
                      )
                    </span>
                  </Label>
                  <Input
                    id={`target-${targetName}`}
                    type="number"
                    step="any"
                    value={
                      currentValue !== undefined ? String(currentValue) : ""
                    }
                    onChange={e =>
                      handleTargetChange(targetName, e.target.value)
                    }
                    className={
                      errors[`target_${targetName}`] ? "border-red-500" : ""
                    }
                    placeholder={`Enter ${targetName} value`}
                  />
                  {errors[`target_${targetName}`] && (
                    <p className="text-sm text-red-500">
                      {errors[`target_${targetName}`]}
                    </p>
                  )}
                </div>
              )
            })}
          </div>

          {/* Parameters Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Parameters</h3>
            {parameterNames.length === 0 ? (
              <Alert>
                <AlertCircle className="size-4" />
                <AlertDescription>
                  No parameters found. This might indicate an issue with the
                  optimization configuration.
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm font-medium">
                      Debug Info
                    </summary>
                    <pre className="bg-muted mt-1 overflow-auto rounded p-2 text-xs">
                      {JSON.stringify(
                        {
                          optimizationConfig: optimization.config,
                          measurementParams: measurement.parameters
                        },
                        null,
                        2
                      )}
                    </pre>
                  </details>
                </AlertDescription>
              </Alert>
            ) : (
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {parameterNames.map(paramName => {
                  const originalValue = (
                    measurement.parameters as Record<string, any>
                  )?.[paramName]
                  const currentValue = (
                    formData.parameters as Record<string, any>
                  )[paramName]
                  return (
                    <div key={paramName} className="space-y-2">
                      <Label htmlFor={`param-${paramName}`}>
                        {paramName}
                        <span className="text-muted-foreground ml-1 text-xs">
                          (Original:{" "}
                          {originalValue !== undefined
                            ? String(originalValue)
                            : "undefined"}
                          )
                        </span>
                      </Label>
                      <Input
                        id={`param-${paramName}`}
                        type="text"
                        value={
                          currentValue !== undefined ? String(currentValue) : ""
                        }
                        onChange={e =>
                          handleParameterChange(paramName, e.target.value)
                        }
                        className={
                          errors[`param_${paramName}`] ? "border-red-500" : ""
                        }
                        placeholder={`Enter ${paramName} value`}
                      />
                      {errors[`param_${paramName}`] && (
                        <p className="text-sm text-red-500">
                          {errors[`param_${paramName}`]}
                        </p>
                      )}
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isLoading}
          >
            <X className="mr-2 size-4" />
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className="mr-2 size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 size-4" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
