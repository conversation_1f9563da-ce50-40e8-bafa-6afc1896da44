-- Migration: 0003_create_suggestions_table.sql
-- Create suggestions table for storing suggested experiments

-- Create the table
CREATE TABLE IF NOT EXISTS suggestions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  optimization_id UUID NOT NULL REFERENCES optimizations(id) ON DELETE CASCADE,
  parameters JSONB NOT NULL,
  batch_id TEXT NOT NULL,
  suggestion_index INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  target_values JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  last_accessed_at TIMESTAMP WITH TIME ZONE,
  submitted_at TIMESTAMP WITH TIME ZONE
);

-- Create index for faster lookups by optimization_id
CREATE INDEX IF NOT EXISTS idx_suggestions_optimization_id ON suggestions(optimization_id);

-- Create index for faster lookups by batch_id
CREATE INDEX IF NOT EXISTS idx_suggestions_batch_id ON suggestions(batch_id);

-- Create index for faster lookups by status
CREATE INDEX IF NOT EXISTS idx_suggestions_status ON suggestions(status);

-- Add a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_suggestions_updated_at
BEFORE UPDATE ON suggestions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
