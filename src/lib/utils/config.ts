// lib/config.ts
/**
 * Application configuration
 * This file centralizes environment-specific configuration
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_BAYBE_API_URL || "",
  API_KEY: process.env.BAYBE_API_KEY || ""
}

// Authentication Configuration
export const AUTH_CONFIG = {
  // JWT token expiration in minutes
  TOKEN_EXPIRATION: 30,
  // Routes that require authentication
  PROTECTED_ROUTES: ["/dashboard", "/dashboard/*", "/api/protected/*"],
  // Routes that are public
  PUBLIC_ROUTES: [
    "/",
    "/login",
    "/signup",
    "/about",
    "/features",
    "/pricing",
    "/contact",
    "/api/public/*"
  ]
}

// Feature Flags
export const FEATURES = {
  ENABLE_INSIGHTS: process.env.NEXT_PUBLIC_ENABLE_INSIGHTS === "true",
  ENABLE_FILE_UPLOAD: process.env.NEXT_PUBLIC_ENABLE_FILE_UPLOAD === "true",
  ENABLE_ADVANCED_VISUALIZATION:
    process.env.NEXT_PUBLIC_ENABLE_ADVANCED_VISUALIZATION === "true"
}

// Error Messages
export const ERROR_MESSAGES = {
  AUTHENTICATION: {
    NOT_AUTHENTICATED: "You must be signed in to access this resource",
    SESSION_EXPIRED: "Your session has expired. Please sign in again",
    INVALID_TOKEN: "Invalid authentication token"
  },
  API: {
    CONNECTION_ERROR:
      "Could not connect to the API. Please check your internet connection",
    TIMEOUT:
      "The request timed out. For large batch sizes (>20), this is expected. Please try with a smaller batch size or try again later",
    SERVER_ERROR: "An unexpected server error occurred. Please try again later"
  },
  OPTIMIZATION: {
    CREATE_FAILED: "Failed to create optimization",
    LOAD_FAILED: "Failed to load optimization",
    SUGGESTION_FAILED: "Failed to get suggestions",
    MEASUREMENT_FAILED: "Failed to add measurement"
  }
}

// Timeouts (in milliseconds)
export const TIMEOUTS = {
  API_REQUEST: 30000, // 30 seconds
  OPTIMIZATION_CREATION: 60000, // 60 seconds
  SUGGESTION_GENERATION: 45000, // 45 seconds base timeout
  SUGGESTION_GENERATION_PER_ITEM: 3000, // 3 seconds per suggestion for batch requests (increased from 1s)
  SUGGESTION_GENERATION_MAX: 600000 // 10 minutes maximum for large batches (increased from 2 min)

  // Batch size timeout reference (based on our measurements with 20% buffer):
  // Batch size 1-2: ~5 seconds
  // Batch size 5: ~5 seconds
  // Batch size 10: ~10 seconds
  // Batch size 20: ~25 seconds
  // Batch size 50: ~180 seconds (3 minutes)
  // Batch size 100: ~450 seconds (7.5 minutes)
}
