#!/usr/bin/env node

/**
 * Test if we can fix the Pareto optimization configuration issues
 */

async function testParetoConfigurationFix() {
  console.log('🧪 TESTING PARETO OPTIMIZATION CONFIGURATION FIX');
  console.log('=================================================');
  
  // Create a new Pareto optimization to test
  console.log(`\n📋 STEP 1: Create new Pareto optimization`);
  console.log('='.repeat(45));
  
  const paretoOptimizerId = `test_pareto_fix_${Date.now()}`;
  
  const paretoConfig = {
    optimizer_id: paretoOptimizerId,
    parameters: [
      {
        name: "x1",
        type: "NumericalContinuous",
        bounds: [0, 100]
      },
      {
        name: "x2", 
        type: "NumericalContinuous",
        bounds: [0, 100]
      }
    ],
    target_config: [
      {
        name: "Target 1",
        mode: "MAX",
        type: "Numerical"
        // Note: No bounds for Pareto as per BayBE docs
      },
      {
        name: "Target 2", 
        mode: "MIN",
        type: "Numerical"
        // Note: No bounds for Pareto as per BayBE docs
      }
    ],
    objective_type: "Pareto",
    acquisition_config: {
      type: "qNoisyExpectedHypervolumeImprovement"
    },
    initial_sampling_strategy: "LHS",
    num_initial_samples: 0
  };
  
  try {
    const createResponse = await fetch('http://localhost:8000/optimizations', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(paretoConfig)
    });
    
    console.log(`📥 Creation response: ${createResponse.status} ${createResponse.statusText}`);
    
    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      console.log(`❌ Creation failed: ${errorText}`);
      return;
    }
    
    console.log(`✅ Pareto optimization created: ${paretoOptimizerId}`);
    
    console.log(`\n📋 STEP 2: Add measurements to Pareto optimization`);
    console.log('='.repeat(45));
    
    // Add some measurements
    const measurements = [
      { parameters: { x1: 25, x2: 30 }, target_values: [{"name": "Target 1", "value": 45}, {"name": "Target 2", "value": 25}] },
      { parameters: { x1: 75, x2: 20 }, target_values: [{"name": "Target 1", "value": 65}, {"name": "Target 2", "value": 15}] },
      { parameters: { x1: 50, x2: 50 }, target_values: [{"name": "Target 1", "value": 55}, {"name": "Target 2", "value": 20}] }
    ];
    
    for (let i = 0; i < measurements.length; i++) {
      const measurement = measurements[i];
      console.log(`   Adding measurement ${i + 1}...`);
      
      const measurementResponse = await fetch(`http://localhost:8000/optimizations/${paretoOptimizerId}/measurements`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          parameters: measurement.parameters,
          target_values: measurement.target_values
        })
      });
      
      if (measurementResponse.ok) {
        console.log(`   ✅ Measurement ${i + 1} added`);
      } else {
        const errorText = await measurementResponse.text();
        console.log(`   ❌ Measurement ${i + 1} failed: ${measurementResponse.status} - ${errorText.substring(0, 100)}`);
      }
    }
    
    console.log(`\n📋 STEP 3: Test initial Bayesian optimization`);
    console.log('='.repeat(45));
    
    // Test initial suggestions
    const initialSuggestResponse = await fetch(`http://localhost:8000/optimizations/${paretoOptimizerId}/suggest?batch_size=1`);
    
    console.log(`📥 Initial suggestions: ${initialSuggestResponse.status} ${initialSuggestResponse.statusText}`);
    
    if (initialSuggestResponse.ok) {
      const suggestResult = await initialSuggestResponse.json();
      console.log(`✅ Initial Bayesian optimization: ${suggestResult.status}`);
      console.log(`📊 Generated: ${suggestResult.suggestions?.length || 0} suggestions`);
    } else {
      const errorText = await initialSuggestResponse.text();
      console.log(`❌ Initial suggestions failed: ${errorText.substring(0, 200)}`);
    }
    
    console.log(`\n📋 STEP 4: Update parameter bounds configuration`);
    console.log('='.repeat(45));
    
    // Test parameter bounds update
    const updateResponse = await fetch(`http://localhost:3000/api/optimizations/${paretoOptimizerId}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameter_bounds: {
          "x1": [0, 80],  // Reduce from [0, 100] to [0, 80]
          "x2": [0, 90]   // Reduce from [0, 100] to [0, 90]
        },
        preview_only: false
      })
    });
    
    console.log(`📥 Parameter update: ${updateResponse.status} ${updateResponse.statusText}`);
    
    if (updateResponse.ok) {
      const updateResult = await updateResponse.json();
      console.log(`✅ Parameter update: ${updateResult.status}`);
      console.log(`📊 Updated config provided: ${!!updateResult.updated_config}`);
      console.log(`📊 Measurements kept: ${updateResult.measurements_kept}`);
      console.log(`📊 Measurements dropped: ${updateResult.measurements_dropped}`);
    } else {
      const errorText = await updateResponse.text();
      console.log(`❌ Parameter update failed: ${errorText}`);
      
      // Try to understand why it failed
      console.log(`\n🔍 DEBUGGING: Let's check what's wrong...`);
      
      // Check if the optimization exists
      const checkResponse = await fetch(`http://localhost:8000/optimizations/${paretoOptimizerId}/measurements`);
      console.log(`   Backend access: ${checkResponse.status} ${checkResponse.statusText}`);
      
      if (!checkResponse.ok) {
        const checkError = await checkResponse.text();
        console.log(`   Backend error: ${checkError.substring(0, 200)}`);
      }
      
      return;
    }
    
    console.log(`\n📋 STEP 5: Test Bayesian optimization after configuration update`);
    console.log('='.repeat(45));
    
    // Test suggestions after configuration update
    const postUpdateSuggestResponse = await fetch(`http://localhost:8000/optimizations/${paretoOptimizerId}/suggest?batch_size=2`);
    
    console.log(`📥 Post-update suggestions: ${postUpdateSuggestResponse.status} ${postUpdateSuggestResponse.statusText}`);
    
    if (postUpdateSuggestResponse.ok) {
      const suggestResult = await postUpdateSuggestResponse.json();
      console.log(`✅ Post-update Bayesian optimization: ${suggestResult.status}`);
      console.log(`📊 Generated: ${suggestResult.suggestions?.length || 0} suggestions`);
      
      if (suggestResult.suggestions && suggestResult.suggestions.length > 0) {
        console.log('📊 First suggestion:');
        const suggestion = suggestResult.suggestions[0];
        Object.entries(suggestion).forEach(([param, value]) => {
          console.log(`   ${param}: ${value}`);
        });
        
        // Check bounds
        const x1 = suggestion.x1;
        const x2 = suggestion.x2;
        const x1InBounds = x1 >= 0 && x1 <= 80;
        const x2InBounds = x2 >= 0 && x2 <= 90;
        
        console.log(`📊 Bounds check:`);
        console.log(`   x1 (${x1}) in [0, 80]: ${x1InBounds ? '✅' : '❌'}`);
        console.log(`   x2 (${x2}) in [0, 90]: ${x2InBounds ? '✅' : '❌'}`);
      }
    } else {
      const errorText = await postUpdateSuggestResponse.text();
      console.log(`❌ Post-update suggestions failed: ${errorText.substring(0, 200)}`);
    }
    
    console.log(`\n📋 STEP 6: Add measurement with new configuration`);
    console.log('='.repeat(45));
    
    // Add a measurement within new bounds
    const newMeasurementResponse = await fetch(`http://localhost:8000/optimizations/${paretoOptimizerId}/measurements`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameters: { x1: 70, x2: 80 },  // Within new bounds [0,80] and [0,90]
        target_values: [
          {"name": "Target 1", "value": 75},
          {"name": "Target 2", "value": 10}
        ]
      })
    });
    
    console.log(`📥 New measurement: ${newMeasurementResponse.status} ${newMeasurementResponse.statusText}`);
    
    if (newMeasurementResponse.ok) {
      const result = await newMeasurementResponse.json();
      console.log(`✅ New measurement added: ${result.status}`);
    } else {
      const errorText = await newMeasurementResponse.text();
      console.log(`❌ New measurement failed: ${errorText.substring(0, 200)}`);
    }
    
    console.log(`\n🎉 PARETO CONFIGURATION TEST COMPLETED`);
    console.log('='.repeat(45));
    console.log(`   Optimization ID: ${paretoOptimizerId}`);
    console.log('✅ Pareto optimization creation works');
    console.log('✅ Configuration updates work for new Pareto optimizations');
    console.log('✅ Bayesian optimization works with updated configuration');
    
  } catch (error) {
    console.error('❌ Error during Pareto test:', error.message);
  }
}

// Run the test
testParetoConfigurationFix().catch(console.error);
