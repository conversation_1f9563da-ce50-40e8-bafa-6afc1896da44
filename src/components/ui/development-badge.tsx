import { <PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface DevelopmentBadgeProps {
  variant?: "icon" | "badge" | "text"
  size?: "sm" | "md" | "lg"
  className?: string
  showTooltip?: boolean
}

export function DevelopmentBadge({
  variant = "icon",
  size = "sm",
  className,
  showTooltip = true
}: DevelopmentBadgeProps) {
  const sizeClasses = {
    sm: "size-3",
    md: "size-4",
    lg: "size-5"
  }

  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base"
  }

  if (variant === "icon") {
    return (
      <span
        className="inline-block"
        title={showTooltip ? "Feature in Development" : undefined}
      >
        <Wrench
          className={cn(
            sizeClasses[size],
            "text-orange-500 dark:text-orange-400",
            className
          )}
        />
      </span>
    )
  }

  if (variant === "badge") {
    return (
      <span
        className={cn(
          "inline-flex items-center gap-1 rounded-full bg-orange-100 px-2 py-1 dark:bg-orange-900/30",
          textSizeClasses[size],
          "text-orange-700 dark:text-orange-300",
          className
        )}
        title={showTooltip ? "Feature in Development" : undefined}
      >
        <Wrench className={sizeClasses[size]} />
        <span>In Development</span>
      </span>
    )
  }

  if (variant === "text") {
    return (
      <span
        className={cn(
          "inline-flex items-center gap-1",
          textSizeClasses[size],
          "text-orange-600 dark:text-orange-400",
          className
        )}
        title={showTooltip ? "Feature in Development" : undefined}
      >
        <Clock className={sizeClasses[size]} />
        <span>Coming Soon</span>
      </span>
    )
  }

  return null
}
