// actions/db/samples-actions.ts
"use server"

import { db } from "@/db/db"
import { samplesTable, InsertSample, SelectSample } from "@/db/schema/samples-schema"
import { eq, and, desc, asc, ne } from "drizzle-orm"
import { inArray } from "drizzle-orm/expressions"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { getOptimizationByIdAction } from "./optimizations-actions"

export type ActionState<T = undefined> = {
  isSuccess: boolean
  message: string
  data?: T
}

/**
 * Create a new sample in the database
 */
export async function createSampleAction(
  sample: InsertSample
): Promise<ActionState<SelectSample>> {
  try {
    // Verify user has access to the optimization
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 CREATE SAMPLE ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to create samples"
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(sample.optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      console.log("❌ CREATE SAMPLE ACTION - Optimization not found:", optResult.message)
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Create the sample
    const [newSample] = await db
      .insert(samplesTable)
      .values(sample)
      .returning()

    return {
      isSuccess: true,
      message: "Sample created successfully",
      data: newSample
    }
  } catch (error) {
    console.error("Error creating sample:", error)
    return {
      isSuccess: false,
      message: `Failed to create sample: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Create multiple samples in the database
 */
export async function createSamplesAction(
  samples: InsertSample[]
): Promise<ActionState<SelectSample[]>> {
  try {
    // Verify user has access to the optimization
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 CREATE SAMPLES ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to create samples"
      }
    }

    if (samples.length === 0) {
      return {
        isSuccess: true,
        message: "No samples to create",
        data: []
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(samples[0].optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      console.log("❌ CREATE SAMPLES ACTION - Optimization not found:", optResult.message)
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Create the samples
    const newSamples = await db
      .insert(samplesTable)
      .values(samples)
      .returning()

    return {
      isSuccess: true,
      message: `${newSamples.length} samples created successfully`,
      data: newSamples
    }
  } catch (error) {
    console.error("Error creating samples:", error)
    return {
      isSuccess: false,
      message: `Failed to create samples: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Get a sample by ID
 */
export async function getSampleByIdAction(
  sampleId: string
): Promise<ActionState<SelectSample>> {
  try {
    // Verify user is signed in
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 SAMPLES ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to get samples"
      }
    }

    // Get the sample
    const sample = await db
      .select()
      .from(samplesTable)
      .where(eq(samplesTable.id, sampleId))
      .limit(1)

    if (sample.length === 0) {
      return {
        isSuccess: false,
        message: "Sample not found"
      }
    }

    // Verify the user has access to the optimization
    const optResult = await getOptimizationByIdAction(sample[0].optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: "Access denied to this sample"
      }
    }

    return {
      isSuccess: true,
      message: "Sample retrieved successfully",
      data: sample[0]
    }
  } catch (error) {
    console.error("Error getting sample:", error)
    return {
      isSuccess: false,
      message: `Failed to get sample: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Get samples by batch ID
 */
export async function getSamplesByBatchAction(
  batchId: string
): Promise<ActionState<SelectSample[]>> {
  try {
    // Verify user is signed in
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 GET SAMPLES BY BATCH ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to get samples"
      }
    }

    // Get the samples for this batch
    const samples = await db
      .select()
      .from(samplesTable)
      .where(eq(samplesTable.batchId, batchId))
      .orderBy(asc(samplesTable.sampleIndex))

    // If no samples found, return early
    if (samples.length === 0) {
      return {
        isSuccess: true,
        message: "No samples found for this batch",
        data: []
      }
    }

    // Verify the user has access to the optimization
    const optResult = await getOptimizationByIdAction(samples[0].optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: "Access denied to these samples"
      }
    }

    return {
      isSuccess: true,
      message: `Retrieved ${samples.length} samples`,
      data: samples
    }
  } catch (error) {
    console.error("Error getting samples by batch:", error)
    return {
      isSuccess: false,
      message: `Failed to get samples: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Get samples by optimization ID and status
 */
export async function getSamplesByOptimizationAction(
  optimizationId: string,
  status?: string | string[]
): Promise<ActionState<SelectSample[]>> {
  try {
    // Verify user is signed in
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 GET SAMPLES BY OPTIMIZATION ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to get samples"
      }
    }

    // Verify the user has access to the optimization
    const optResult = await getOptimizationByIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: "Optimization not found or access denied"
      }
    }

    // Build and execute the query based on status filter
    let samples: SelectSample[];

    if (status) {
      if (Array.isArray(status)) {
        samples = await db
          .select()
          .from(samplesTable)
          .where(
            and(
              eq(samplesTable.optimizationId, optimizationId),
              inArray(samplesTable.status, status)
            )
          )
          .orderBy(desc(samplesTable.createdAt));
      } else {
        samples = await db
          .select()
          .from(samplesTable)
          .where(
            and(
              eq(samplesTable.optimizationId, optimizationId),
              eq(samplesTable.status, status)
            )
          )
          .orderBy(desc(samplesTable.createdAt));
      }
    } else {
      samples = await db
        .select()
        .from(samplesTable)
        .where(eq(samplesTable.optimizationId, optimizationId))
        .orderBy(desc(samplesTable.createdAt));
    }

    return {
      isSuccess: true,
      message: `Retrieved ${samples.length} samples`,
      data: samples
    }
  } catch (error) {
    console.error("Error getting samples by optimization:", error)
    return {
      isSuccess: false,
      message: `Failed to get samples: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Update a sample's status
 */
export async function updateSampleStatusAction(
  sampleId: string,
  status: string
): Promise<ActionState<SelectSample>> {
  try {
    // Verify user is signed in
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to update samples"
      }
    }

    // Get the sample to verify access
    const sampleResult = await getSampleByIdAction(sampleId)
    if (!sampleResult.isSuccess || !sampleResult.data) {
      return {
        isSuccess: false,
        message: sampleResult.message
      }
    }

    // Update the sample status
    const [updatedSample] = await db
      .update(samplesTable)
      .set({
        status,
        updatedAt: new Date(),
        ...(status === "submitted" ? { submittedAt: new Date() } : {})
      })
      .where(eq(samplesTable.id, sampleId))
      .returning()

    return {
      isSuccess: true,
      message: "Sample status updated successfully",
      data: updatedSample
    }
  } catch (error) {
    console.error("Error updating sample status:", error)
    return {
      isSuccess: false,
      message: `Failed to update sample status: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Update a sample's target values
 */
export async function updateSampleTargetValuesAction(
  sampleId: string,
  targetValues: Record<string, number>
): Promise<ActionState<SelectSample>> {
  try {
    // Verify user is signed in
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to update samples"
      }
    }

    // Get the sample to verify access
    const sampleResult = await getSampleByIdAction(sampleId)
    if (!sampleResult.isSuccess || !sampleResult.data) {
      return {
        isSuccess: false,
        message: sampleResult.message
      }
    }

    // Update the sample target values
    const [updatedSample] = await db
      .update(samplesTable)
      .set({
        targetValues,
        updatedAt: new Date(),
        lastAccessedAt: new Date()
      })
      .where(eq(samplesTable.id, sampleId))
      .returning()

    return {
      isSuccess: true,
      message: "Sample target values updated successfully",
      data: updatedSample
    }
  } catch (error) {
    console.error("Error updating sample target values:", error)
    return {
      isSuccess: false,
      message: `Failed to update sample target values: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Delete a sample
 */
export async function deleteSampleAction(
  sampleId: string
): Promise<ActionState<void>> {
  try {
    // Verify user is signed in
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 DELETE SAMPLE ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to delete samples"
      }
    }

    // Get the sample to verify access
    const sampleResult = await getSampleByIdAction(sampleId)
    if (!sampleResult.isSuccess || !sampleResult.data) {
      return {
        isSuccess: false,
        message: sampleResult.message
      }
    }

    // Delete the sample
    await db
      .delete(samplesTable)
      .where(eq(samplesTable.id, sampleId))

    return {
      isSuccess: true,
      message: "Sample deleted successfully"
    }
  } catch (error) {
    console.error("Error deleting sample:", error)
    return {
      isSuccess: false,
      message: `Failed to delete sample: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Delete samples by batch ID
 */
export async function deleteSamplesByBatchAction(
  batchId: string
): Promise<ActionState<void>> {
  try {
    // Verify user is signed in
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to delete samples"
      }
    }

    // Get the samples to verify access
    const samplesResult = await getSamplesByBatchAction(batchId)
    if (!samplesResult.isSuccess || !samplesResult.data || samplesResult.data.length === 0) {
      return {
        isSuccess: false,
        message: samplesResult.message
      }
    }

    // Delete the samples
    await db
      .delete(samplesTable)
      .where(eq(samplesTable.batchId, batchId))

    return {
      isSuccess: true,
      message: "Samples deleted successfully"
    }
  } catch (error) {
    console.error("Error deleting samples by batch:", error)
    return {
      isSuccess: false,
      message: `Failed to delete samples: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Delete samples by status
 */
export async function deleteSamplesByStatusAction(
  optimizationId: string,
  status: string | string[]
): Promise<ActionState<{ count: number }>> {
  try {
    // Verify user is signed in
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to delete samples"
      }
    }

    // Verify the user has access to the optimization
    const optResult = await getOptimizationByIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: "Optimization not found or access denied"
      }
    }

    // Get the samples to count them before deletion
    const samplesResult = await getSamplesByOptimizationAction(optimizationId, status)
    if (!samplesResult.isSuccess) {
      return {
        isSuccess: false,
        message: samplesResult.message
      }
    }

    const count = samplesResult.data?.length || 0

    // If no samples found, return early
    if (count === 0) {
      return {
        isSuccess: true,
        message: "No samples found with the specified status",
        data: { count: 0 }
      }
    }

    // Delete the samples
    if (Array.isArray(status)) {
      await db
        .delete(samplesTable)
        .where(
          and(
            eq(samplesTable.optimizationId, optimizationId),
            inArray(samplesTable.status, status)
          )
        )
    } else {
      await db
        .delete(samplesTable)
        .where(
          and(
            eq(samplesTable.optimizationId, optimizationId),
            eq(samplesTable.status, status)
          )
        )
    }

    return {
      isSuccess: true,
      message: `${count} samples deleted successfully`,
      data: { count }
    }
  } catch (error) {
    console.error("Error deleting samples by status:", error)
    return {
      isSuccess: false,
      message: `Failed to delete samples: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
