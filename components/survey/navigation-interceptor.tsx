/*
This client component intercepts navigation attempts and prevents unauthorized access
during academic verification and survey flows.
*/

"use client"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useUser } from "@clerk/nextjs"
import { toast } from "@/components/ui/use-toast"

interface NavigationInterceptorProps {
  allowedPaths: string[]
  redirectTo: string
  message?: string
}

export default function NavigationInterceptor({
  allowedPaths,
  redirectTo,
  message = "Please complete the required steps before accessing other areas."
}: NavigationInterceptorProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { isLoaded, user } = useUser()

  useEffect(() => {
    if (!isLoaded || !user) return

    // Check if current path is allowed
    const isAllowedPath = allowedPaths.some(
      path => pathname === path || pathname.startsWith(path)
    )

    if (!isAllowedPath) {
      console.log(
        `Navigation interceptor: Blocking access to ${pathname}, redirecting to ${redirectTo}`
      )

      // Show toast notification
      toast({
        title: "Access Restricted",
        description: message,
        variant: "destructive"
      })

      // Redirect to allowed path
      router.replace(redirectTo)
    }
  }, [pathname, isLoaded, user, router, allowedPaths, redirectTo, message])

  // Intercept browser navigation events
  useEffect(() => {
    if (!isLoaded || !user) return

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // This will show a confirmation dialog when user tries to navigate away
      e.preventDefault()
      e.returnValue =
        "Are you sure you want to leave? Your progress may be lost."
      return "Are you sure you want to leave? Your progress may be lost."
    }

    const handlePopState = (e: PopStateEvent) => {
      // Check if the destination is allowed
      const targetPath = window.location.pathname
      const isAllowedPath = allowedPaths.some(
        path => targetPath === path || targetPath.startsWith(path)
      )

      if (!isAllowedPath) {
        e.preventDefault()
        console.log(
          `Navigation interceptor: Blocking browser navigation to ${targetPath}`
        )

        // Push the current state back to prevent navigation
        window.history.pushState(null, "", pathname)

        toast({
          title: "Navigation Blocked",
          description: message,
          variant: "destructive"
        })
      }
    }

    // Add event listeners
    window.addEventListener("beforeunload", handleBeforeUnload)
    window.addEventListener("popstate", handlePopState)

    // Cleanup
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload)
      window.removeEventListener("popstate", handlePopState)
    }
  }, [isLoaded, user, pathname, allowedPaths, message])

  return null // This component doesn't render anything
}

// Specific interceptor for academic signup
export function AcademicSignupInterceptor() {
  return (
    <NavigationInterceptor
      allowedPaths={[
        "/academic-signup",
        "/academic-survey",
        "/verify-email",
        "/api/",
        "/login",
        "/signup"
      ]}
      redirectTo="/academic-signup"
      message="Please complete your academic verification before accessing other areas."
    />
  )
}

// Specific interceptor for academic survey
export function AcademicSurveyInterceptor() {
  return (
    <NavigationInterceptor
      allowedPaths={["/academic-survey", "/api/", "/login", "/signup"]}
      redirectTo="/academic-survey"
      message="Please complete the academic survey to access the dashboard and other features."
    />
  )
}
