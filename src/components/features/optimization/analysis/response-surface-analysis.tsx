"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { Layers } from "lucide-react"
import { SurfaceAnalysis } from "@/components/optimization/surface-analysis"

interface ResponseSurfaceAnalysisProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  parameterNames: string[]
  targetName: string
  targetMode: "MAX" | "MIN" | "MULTI"
  availableTargets: string[]
}

export function ResponseSurfaceAnalysis({
  optimization,
  measurements,
  parameterNames,
  targetName,
  targetMode,
  availableTargets
}: ResponseSurfaceAnalysisProps) {
  // Default to surface tab since we moved SOBOL to main tabs
  const [activeTab, setActiveTab] = useState("surface")

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Layers className="text-primary mr-2 size-5" />
          Response Surface Analysis
        </CardTitle>
        <CardDescription>
          Visualize how parameters affect target variables
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 grid grid-cols-1">
            <TabsTrigger value="surface">3D Surface</TabsTrigger>
          </TabsList>

          {/* 3D Surface Plot */}
          <TabsContent value="surface" className="space-y-4">
            <SurfaceAnalysis
              measurements={measurements}
              parameterNames={parameterNames}
              targetName={targetName}
              targetMode={targetMode}
              availableTargets={availableTargets}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
