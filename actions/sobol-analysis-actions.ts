"use server"

import { ActionState } from "@/types"
import { API_BASE_URL } from "@/lib/api-utils"

interface SobolContourParams {
  optimizerId: string
  param1Name: string
  param2Name: string
  targetName?: string  // Optional target name for multi-target optimizations
  gridSize?: number
  numSamples?: number
  experimentStartIdx?: number  // Optional start index for experiment range
  experimentEndIdx?: number  // Optional end index for experiment range
}

interface SobolContourData {
  x_values: number[]
  y_values: number[]
  z_values: number[][]
  uncertainty: number[][]
  sobol_first_order: Record<string, number>
  sobol_first_order_ci?: Record<string, [number, number]>
  sobol_second_order: number
  sobol_second_order_ci?: [number, number]
  sobol_total: Record<string, number>
  sobol_total_ci?: Record<string, [number, number]>
  param1_type: string
  param2_type: string
  param1_is_categorical: boolean
  param2_is_categorical: boolean
  param1_categories?: string[]
  param2_categories?: string[]
  target_name: string  // The name of the target used for analysis
  experiment_start_idx?: number  // Start index of experiment range used for analysis
  experiment_end_idx?: number  // End index of experiment range used for analysis
  fixed_params?: string[]  // List of parameters that have converged
  fixed_params_message?: string  // Message about fixed parameters
}

export async function getSobolContourDataAction({
  optimizerId,
  param1Name,
  param2Name,
  targetName,
  gridSize = 50,
  numSamples = 1024,
  experimentStartIdx,
  experimentEndIdx
}: SobolContourParams): Promise<ActionState<SobolContourData>> {
  try {
    const response = await fetch(
      `${API_BASE_URL}/sobol-analysis/${optimizerId}/contour`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          param1_name: param1Name,
          param2_name: param2Name,
          target_name: targetName,  // Include target name if provided
          grid_size: gridSize,
          num_samples: numSamples,
          experiment_start_idx: experimentStartIdx,  // Include experiment range if provided
          experiment_end_idx: experimentEndIdx
        })
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      return {
        isSuccess: false,
        message: errorData.detail || "Failed to get SOBOL contour data"
      }
    }

    const data = await response.json()

    if (data.status === "error") {
      return {
        isSuccess: false,
        message: data.message || "Failed to get SOBOL contour data"
      }
    }

    return {
      isSuccess: true,
      message: "SOBOL contour data retrieved successfully",
      data: {
        x_values: data.x_values,
        y_values: data.y_values,
        z_values: data.z_values,
        uncertainty: data.uncertainty,
        sobol_first_order: data.sobol_first_order,
        sobol_first_order_ci: data.sobol_first_order_ci,
        sobol_second_order: data.sobol_second_order,
        sobol_second_order_ci: data.sobol_second_order_ci,
        sobol_total: data.sobol_total,
        sobol_total_ci: data.sobol_total_ci,
        param1_type: data.param1_type,
        param2_type: data.param2_type,
        param1_is_categorical: data.param1_is_categorical,
        param2_is_categorical: data.param2_is_categorical,
        param1_categories: data.param1_categories,
        param2_categories: data.param2_categories,
        target_name: data.target_name,
        experiment_start_idx: data.experiment_start_idx,
        experiment_end_idx: data.experiment_end_idx,
        fixed_params: data.fixed_params,
        fixed_params_message: data.fixed_params_message
      }
    }
  } catch (error) {
    console.error("Error getting SOBOL contour data:", error)
    return {
      isSuccess: false,
      message: error instanceof Error ? error.message : "Unknown error"
    }
  }
}
