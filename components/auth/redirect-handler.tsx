"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

interface RedirectHandlerProps {
  redirectUrl: string
}

/**
 * Component to handle redirects after authentication
 * This component will check localStorage for a stored plan type
 * and add it to the redirect URL if needed
 */
export function RedirectHandler({ redirectUrl }: RedirectHandlerProps) {
  const router = useRouter()

  useEffect(() => {
    // Only run on the client side
    if (typeof window === "undefined") return

    // Get the stored plan type from localStorage
    const storedPlanType = localStorage.getItem("selectedPlanType")
    console.log("RedirectHandler: Stored plan type:", storedPlanType)

    // If we have a stored plan type and the redirect URL is the checkout API,
    // add the plan parameter to the URL
    let finalRedirectUrl = redirectUrl
    if (storedPlanType && redirectUrl.includes("/api/stripe/checkout")) {
      finalRedirectUrl = `${redirectUrl}?plan=${storedPlanType}`
      console.log("RedirectHandler: Redirecting to:", finalRedirectUrl)
    }

    // Redirect to the final URL
    router.push(finalRedirectUrl)

    // Clean up localStorage
    if (storedPlanType) {
      localStorage.removeItem("selectedPlanType")
    }
  }, [redirectUrl, router])

  return null
}
