// lib/constraints/templates.ts
// Predefined constraint templates for common use cases

import { ConstraintTemplate } from "./types"

/**
 * Predefined constraint templates for common use cases
 */
export const CONSTRAINT_TEMPLATES: ConstraintTemplate[] = [
  {
    id: "mixture_ratios",
    name: "Mixture Ratios",
    description: "Components must sum to 100% (mixture constraint)",
    category: "Chemical/Materials",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["component_a", "component_b", "component_c"],
      operator: "=",
      coefficients: [1.0, 1.0, 1.0],
      rhs: 1.0,
      description: "All components must sum to 1.0 (100%)"
    },
    parameterPlaceholders: ["component_a", "component_b", "component_c"]
  },
  {
    id: "budget_constraint",
    name: "Budget Constraint",
    description: "Total cost must not exceed budget limit",
    category: "Economic",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["material_1", "material_2", "labor"],
      operator: "<=",
      coefficients: [10.0, 15.0, 25.0],
      rhs: 1000.0,
      description: "Total cost ≤ budget"
    },
    parameterPlaceholders: ["material_1", "material_2", "labor"]
  },
  {
    id: "parameter_comparison",
    name: "Parameter Comparison",
    description: "One parameter must be strictly less than another",
    category: "Comparison",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["x19", "x20"],
      operator: "<",
      coefficients: [1.0, -1.0],
      rhs: 0.0,
      description: "x19 < x20"
    },
    parameterPlaceholders: ["x19", "x20"]
  },
  {
    id: "resource_limit",
    name: "Resource Limit",
    description: "Total resource consumption must stay within limits",
    category: "Resource Management",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["resource_1", "resource_2"],
      operator: "<=",
      coefficients: [2.0, 3.0],
      rhs: 100.0,
      description: "2*resource_1 + 3*resource_2 ≤ 100"
    },
    parameterPlaceholders: ["resource_1", "resource_2"]
  },
  {
    id: "temperature_pressure_safety",
    name: "Temperature-Pressure Safety",
    description: "Safety constraint for temperature and pressure combination",
    category: "Safety",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["temperature", "pressure"],
      operator: "<=",
      coefficients: [0.1, 1.0],
      rhs: 150.0,
      description: "0.1*temperature + pressure ≤ 150 (safety limit)"
    },
    parameterPlaceholders: ["temperature", "pressure"]
  },
  {
    id: "max_components",
    name: "Maximum Active Components",
    description: "Limit the number of active components in a mixture",
    category: "Cardinality",
    constraint: {
      type: "ContinuousCardinalityConstraint",
      parameters: ["comp_1", "comp_2", "comp_3", "comp_4"],
      max_cardinality: 3,
      description: "Use at most 3 components"
    },
    parameterPlaceholders: ["comp_1", "comp_2", "comp_3", "comp_4"]
  },
  {
    id: "min_components",
    name: "Minimum Active Components",
    description: "Ensure minimum number of components are used",
    category: "Cardinality",
    constraint: {
      type: "ContinuousCardinalityConstraint",
      parameters: ["ingredient_1", "ingredient_2", "ingredient_3"],
      min_cardinality: 2,
      description: "Use at least 2 ingredients"
    },
    parameterPlaceholders: ["ingredient_1", "ingredient_2", "ingredient_3"]
  },
  {
    id: "exclude_high_temp_sensitive",
    name: "Exclude High Temperature with Sensitive Materials",
    description:
      "Prevent high temperature when using temperature-sensitive materials",
    category: "Safety",
    constraint: {
      type: "DiscreteExcludeConstraint",
      parameters: ["temperature", "material_type"],
      combiner: "AND",
      conditions: [
        {
          type: "threshold",
          threshold: 80,
          operator: ">"
        }
      ],
      description: "Avoid high temperature with sensitive materials"
    },
    parameterPlaceholders: ["temperature", "material_type"]
  },
  {
    id: "catalyst_selection",
    name: "Catalyst Selection Limit",
    description: "Select exactly one catalyst from available options",
    category: "Selection",
    constraint: {
      type: "DiscreteCardinalityConstraint",
      parameters: ["catalyst_a", "catalyst_b", "catalyst_c"],
      min_cardinality: 1,
      max_cardinality: 1,
      description: "Select exactly one catalyst"
    },
    parameterPlaceholders: ["catalyst_a", "catalyst_b", "catalyst_c"]
  },
  {
    id: "solvent_no_duplicates",
    name: "No Duplicate Solvents",
    description: "Each solvent slot must contain a different solvent",
    category: "Uniqueness",
    constraint: {
      type: "DiscreteNoLabelDuplicatesConstraint",
      parameters: ["solvent_1", "solvent_2", "solvent_3"],
      description: "Each solvent must be different"
    },
    parameterPlaceholders: ["solvent_1", "solvent_2", "solvent_3"]
  },
  {
    id: "total_concentration",
    name: "Total Concentration Limit",
    description: "Sum of all concentrations must not exceed limit",
    category: "Chemical/Materials",
    constraint: {
      type: "DiscreteSumConstraint",
      parameters: ["conc_1", "conc_2", "conc_3"],
      condition: {
        type: "threshold",
        threshold: 100,
        operator: "<="
      },
      description: "Total concentration ≤ 100"
    },
    parameterPlaceholders: ["conc_1", "conc_2", "conc_3"]
  },
  {
    id: "reaction_efficiency",
    name: "Reaction Efficiency Product",
    description: "Product of efficiency factors must exceed minimum",
    category: "Performance",
    constraint: {
      type: "DiscreteProductConstraint",
      parameters: ["efficiency_1", "efficiency_2"],
      condition: {
        type: "threshold",
        threshold: 0.8,
        operator: ">="
      },
      description: "efficiency_1 × efficiency_2 ≥ 0.8"
    },
    parameterPlaceholders: ["efficiency_1", "efficiency_2"]
  },
  {
    id: "ph_buffer_dependency",
    name: "pH Buffer Dependency",
    description: "Buffer is only relevant when pH control is enabled",
    category: "Dependencies",
    constraint: {
      type: "DiscreteDependenciesConstraint",
      parameters: ["ph_control", "buffer_type", "buffer_concentration"],
      affected_parameters: [["buffer_type", "buffer_concentration"]],
      description: "Buffer parameters depend on pH control"
    },
    parameterPlaceholders: ["ph_control", "buffer_type", "buffer_concentration"]
  },
  {
    id: "flow_rate_balance",
    name: "Flow Rate Balance",
    description: "Input and output flow rates must be balanced",
    category: "Process Control",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["input_flow", "output_flow"],
      operator: "=",
      coefficients: [1.0, -1.0],
      rhs: 0.0,
      description: "input_flow = output_flow"
    },
    parameterPlaceholders: ["input_flow", "output_flow"]
  },
  {
    id: "energy_efficiency",
    name: "Energy Efficiency Constraint",
    description: "Energy consumption must be within efficiency limits",
    category: "Sustainability",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["power_consumption", "throughput"],
      operator: "<=",
      coefficients: [1.0, -0.5],
      rhs: 50.0,
      description: "power_consumption ≤ 0.5*throughput + 50"
    },
    parameterPlaceholders: ["power_consumption", "throughput"]
  }
]

/**
 * Get templates by category
 */
export function getTemplatesByCategory(category: string): ConstraintTemplate[] {
  return CONSTRAINT_TEMPLATES.filter(template => template.category === category)
}

/**
 * Get all template categories
 */
export function getTemplateCategories(): string[] {
  const categories = new Set(
    CONSTRAINT_TEMPLATES.map(template => template.category)
  )
  return Array.from(categories).sort()
}

/**
 * Find template by ID
 */
export function getTemplateById(id: string): ConstraintTemplate | undefined {
  return CONSTRAINT_TEMPLATES.find(template => template.id === id)
}

/**
 * Search templates by name or description
 */
export function searchTemplates(query: string): ConstraintTemplate[] {
  const lowerQuery = query.toLowerCase()
  return CONSTRAINT_TEMPLATES.filter(
    template =>
      template.name.toLowerCase().includes(lowerQuery) ||
      template.description.toLowerCase().includes(lowerQuery)
  )
}

/**
 * Get templates compatible with parameter types
 */
export function getCompatibleTemplates(
  parameterTypes: string[],
  constraintType?: string
): ConstraintTemplate[] {
  return CONSTRAINT_TEMPLATES.filter(template => {
    // Filter by constraint type if specified
    if (constraintType && template.constraint.type !== constraintType) {
      return false
    }

    // Check parameter type compatibility
    const templateConstraintType = template.constraint.type
    const hasContinuous = parameterTypes.includes("NumericalContinuous")
    const hasDiscrete =
      parameterTypes.includes("NumericalDiscrete") ||
      parameterTypes.includes("CategoricalParameter")

    if (templateConstraintType?.startsWith("Continuous")) {
      return hasContinuous
    } else if (templateConstraintType?.startsWith("Discrete")) {
      return hasDiscrete
    }

    return true
  })
}

/**
 * Get recommended templates for common scenarios
 */
export function getRecommendedTemplates(
  scenario: string
): ConstraintTemplate[] {
  const scenarioMap: Record<string, string[]> = {
    chemical_synthesis: [
      "mixture_ratios",
      "temperature_pressure_safety",
      "total_concentration",
      "ph_buffer_dependency"
    ],
    materials_science: [
      "max_components",
      "exclude_high_temp_sensitive",
      "energy_efficiency"
    ],
    process_optimization: [
      "flow_rate_balance",
      "resource_limit",
      "reaction_efficiency"
    ],
    formulation: [
      "mixture_ratios",
      "solvent_no_duplicates",
      "catalyst_selection"
    ],
    economic_optimization: [
      "budget_constraint",
      "resource_limit",
      "energy_efficiency"
    ]
  }

  const templateIds = scenarioMap[scenario] || []
  return templateIds
    .map(id => getTemplateById(id))
    .filter(Boolean) as ConstraintTemplate[]
}
