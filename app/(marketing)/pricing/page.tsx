/*
This server page displays pricing options for the product, integrating Stripe payment links.
*/

"use server"

import { PricingSection } from "@/components/pricing/pricing-section"
import { auth } from "@clerk/nextjs/server"

export default async function PricingPage() {
  const { userId } = await auth()

  return (
    <div className="container mx-auto max-w-5xl px-4 py-12">
      <PricingSection
        userId={userId}
        showHeader={true}
        showAcademicAccess={true}
        showFooterNote={true}
      />
    </div>
  )
}
