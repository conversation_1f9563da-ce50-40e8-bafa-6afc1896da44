"use client"

import { useState, useCallback, useRef } from "react"
import { StructuredError, ValidationResult, ErrorContext } from "./types"
import { ErrorFactory } from "./factory"

export interface ErrorState {
  errors: StructuredError[]
  hasErrors: boolean
  hasWarnings: boolean
  errorCount: number
  warningCount: number
}

export function useErrorHandling() {
  const [errorState, setErrorState] = useState<ErrorState>({
    errors: [],
    hasErrors: false,
    hasWarnings: false,
    errorCount: 0,
    warningCount: 0
  })

  const errorContextRef = useRef<ErrorContext>({})

  const updateErrorState = useCallback((errors: StructuredError[]) => {
    const errorCount = errors.filter(e => e.severity === "error").length
    const warningCount = errors.filter(e => e.severity === "warning").length

    setErrorState({
      errors,
      hasErrors: errorCount > 0,
      hasWarnings: warningCount > 0,
      errorCount,
      warningCount
    })
  }, [])

  const addError = useCallback((error: StructuredError) => {
    setErrorState(prev => {
      const newErrors = [...prev.errors, error]
      const errorCount = newErrors.filter(e => e.severity === "error").length
      const warningCount = newErrors.filter(
        e => e.severity === "warning"
      ).length

      return {
        errors: newErrors,
        hasErrors: errorCount > 0,
        hasWarnings: warningCount > 0,
        errorCount,
        warningCount
      }
    })
  }, [])

  const removeError = useCallback((errorId: string) => {
    setErrorState(prev => {
      const newErrors = prev.errors.filter(e => e.id !== errorId)
      const errorCount = newErrors.filter(e => e.severity === "error").length
      const warningCount = newErrors.filter(
        e => e.severity === "warning"
      ).length

      return {
        errors: newErrors,
        hasErrors: errorCount > 0,
        hasWarnings: warningCount > 0,
        errorCount,
        warningCount
      }
    })
  }, [])

  const clearErrors = useCallback(() => {
    setErrorState({
      errors: [],
      hasErrors: false,
      hasWarnings: false,
      errorCount: 0,
      warningCount: 0
    })
  }, [])

  const clearErrorsByField = useCallback((field: string) => {
    setErrorState(prev => {
      const newErrors = prev.errors.filter(e => e.field !== field)
      const errorCount = newErrors.filter(e => e.severity === "error").length
      const warningCount = newErrors.filter(
        e => e.severity === "warning"
      ).length

      return {
        errors: newErrors,
        hasErrors: errorCount > 0,
        hasWarnings: warningCount > 0,
        errorCount,
        warningCount
      }
    })
  }, [])

  const setErrorContext = useCallback((context: Partial<ErrorContext>) => {
    errorContextRef.current = { ...errorContextRef.current, ...context }
  }, [])

  const handleBackendError = useCallback(
    (backendError: any, additionalContext?: Partial<ErrorContext>) => {
      const context = { ...errorContextRef.current, ...additionalContext }
      const structuredError = ErrorFactory.fromBackendError(
        backendError,
        context
      )
      addError(structuredError)
      return structuredError
    },
    [addError]
  )

  const validateAndSetErrors = useCallback(
    (validationResult: ValidationResult) => {
      const allErrors = [
        ...validationResult.errors,
        ...validationResult.warnings,
        ...validationResult.infos
      ]
      updateErrorState(allErrors)
      return validationResult.isValid
    },
    [updateErrorState]
  )

  const getErrorsByField = useCallback(
    (field: string) => {
      return errorState.errors.filter(e => e.field === field)
    },
    [errorState.errors]
  )

  const getErrorsByCategory = useCallback(
    (category: string) => {
      return errorState.errors.filter(e => e.category === category)
    },
    [errorState.errors]
  )

  const hasErrorsForField = useCallback(
    (field: string) => {
      return errorState.errors.some(
        e => e.field === field && e.severity === "error"
      )
    },
    [errorState.errors]
  )

  const hasWarningsForField = useCallback(
    (field: string) => {
      return errorState.errors.some(
        e => e.field === field && e.severity === "warning"
      )
    },
    [errorState.errors]
  )

  return {
    // State
    ...errorState,

    // Actions
    addError,
    removeError,
    clearErrors,
    clearErrorsByField,
    setErrorContext,
    handleBackendError,
    validateAndSetErrors,

    // Queries
    getErrorsByField,
    getErrorsByCategory,
    hasErrorsForField,
    hasWarningsForField
  }
}

// Hook for form field validation
export function useFieldValidation(fieldName: string) {
  const {
    getErrorsByField,
    hasErrorsForField,
    hasWarningsForField,
    clearErrorsByField
  } = useErrorHandling()

  const fieldErrors = getErrorsByField(fieldName)
  const hasErrors = hasErrorsForField(fieldName)
  const hasWarnings = hasWarningsForField(fieldName)

  const clearFieldErrors = useCallback(() => {
    clearErrorsByField(fieldName)
  }, [fieldName, clearErrorsByField])

  return {
    fieldErrors,
    hasErrors,
    hasWarnings,
    clearFieldErrors
  }
}

// Hook for validation with debouncing
export function useDebouncedValidation(
  validationFn: (value: any) => Promise<ValidationResult> | ValidationResult,
  delay: number = 300
) {
  const [isValidating, setIsValidating] = useState(false)
  const [validationResult, setValidationResult] =
    useState<ValidationResult | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const validate = useCallback(
    async (value: any) => {
      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      setIsValidating(true)

      timeoutRef.current = setTimeout(async () => {
        try {
          const result = await validationFn(value)
          setValidationResult(result)
        } catch (error) {
          console.error("Validation error:", error)
          setValidationResult({
            isValid: false,
            errors: [ErrorFactory.fromBackendError(error)],
            warnings: [],
            infos: []
          })
        } finally {
          setIsValidating(false)
        }
      }, delay)
    },
    [validationFn, delay]
  )

  const clearValidation = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsValidating(false)
    setValidationResult(null)
  }, [])

  return {
    validate,
    clearValidation,
    isValidating,
    validationResult
  }
}
