"use client"

import { motion } from "framer-motion"
import {
  User,
  Database,
  Shield,
  Cloud,
  Lock,
  Eye,
  ArrowRight,
  ArrowDown
} from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"

const dataFlowSteps = [
  {
    id: 1,
    title: "Data Input",
    icon: User,
    description: "User enters optimization parameters and measurements",
    security: ["Input validation", "Authentication required", "Rate limiting"],
    location: "Browser (Client-side)"
  },
  {
    id: 2,
    title: "Secure Transmission",
    icon: Lock,
    description: "Data encrypted and transmitted via HTTPS/TLS 1.3",
    security: [
      "TLS 1.3 encryption",
      "Certificate pinning",
      "Perfect Forward Secrecy"
    ],
    location: "Network Layer"
  },
  {
    id: 3,
    title: "API Processing",
    icon: Shield,
    description: "Server validates, authenticates, and processes requests",
    security: ["JWT validation", "API key verification", "Input sanitization"],
    location: "Application Server"
  },
  {
    id: 4,
    title: "Database Storage",
    icon: Database,
    description: "Data stored with encryption and user isolation",
    security: ["AES-256 encryption", "Row-level security", "Access logging"],
    location: "PostgreSQL Database"
  },
  {
    id: 5,
    title: "Optimization Processing",
    icon: Cloud,
    description: "Bayesian optimization algorithms process data",
    security: [
      "Isolated processing",
      "Memory encryption",
      "Secure computation"
    ],
    location: "Optimization Engine"
  },
  {
    id: 6,
    title: "Results Delivery",
    icon: Eye,
    description: "Encrypted results returned to authenticated user",
    security: ["Response encryption", "User authorization", "Audit logging"],
    location: "Client Interface"
  }
]

const dataFlowPrinciples = [
  {
    title: "End-to-End Encryption",
    description: "Data is encrypted from browser to database and back"
  },
  {
    title: "Zero Trust Architecture",
    description: "Every request is authenticated and authorized"
  },
  {
    title: "Data Isolation",
    description: "Complete separation between different users' data"
  },
  {
    title: "Audit Logging",
    description: "All data access and processing is logged"
  }
]

export function DataFlowDiagram() {
  return (
    <section id="data-flow" className="bg-gray-50 py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Data Flow & Processing
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            Understand how your data flows through our system with complete
            security and privacy protection at every step.
          </p>
        </motion.div>

        <div className="mb-12">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {dataFlowSteps.map((step, index) => (
              <motion.div
                key={step.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative"
              >
                <Card className="h-full">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="flex size-12 items-center justify-center rounded-full bg-blue-100">
                        <step.icon className="size-6 text-blue-600" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="flex size-6 items-center justify-center rounded-full bg-blue-600 text-xs font-bold text-white">
                            {step.id}
                          </span>
                          <CardTitle className="text-lg">
                            {step.title}
                          </CardTitle>
                        </div>
                        <CardDescription className="text-sm text-gray-500">
                          {step.location}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="mb-4 text-sm text-gray-700">
                      {step.description}
                    </p>
                    <div>
                      <h4 className="mb-2 text-sm font-semibold text-gray-900">
                        Security Measures:
                      </h4>
                      <ul className="space-y-1">
                        {step.security.map((measure, measureIndex) => (
                          <li key={measureIndex} className="flex items-start">
                            <div className="mr-2 mt-1.5 size-1.5 shrink-0 rounded-full bg-green-600" />
                            <span className="text-xs text-gray-600">
                              {measure}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                {/* Arrow for flow indication - mobile */}
                {index < dataFlowSteps.length - 1 && (
                  <div className="absolute -bottom-3 left-1/2 -translate-x-1/2 lg:hidden">
                    <ArrowDown className="size-6 text-blue-400" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                Data Flow Security Principles
              </CardTitle>
              <CardDescription>
                Core security principles governing data flow in our system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {dataFlowPrinciples.map((principle, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="flex size-8 shrink-0 items-center justify-center rounded-full bg-green-100">
                      <Shield className="size-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {principle.title}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {principle.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
          className="mt-8"
        >
          <Card className="bg-gradient-to-r from-blue-50 to-green-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="mb-4 text-xl font-semibold text-gray-900">
                  Data Flow Metrics
                </h3>
                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-blue-600">
                      6
                    </div>
                    <div className="text-sm text-gray-600">
                      Security Checkpoints
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-green-600">
                      100%
                    </div>
                    <div className="text-sm text-gray-600">
                      Encrypted Transit
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-purple-600">
                      Zero
                    </div>
                    <div className="text-sm text-gray-600">Data Leakage</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-orange-600">
                      Complete
                    </div>
                    <div className="text-sm text-gray-600">Audit Trail</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
