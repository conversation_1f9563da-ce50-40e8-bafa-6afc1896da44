/*
Configures the TypeScript compiler options for the app.
*/

{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }],
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["./components/*", "./src/components/*", "./src/components/ui/*", "./src/components/features/*", "./src/components/features/auth/*", "./src/components/features/optimization/*", "./src/components/features/dashboard/*", "./src/components/features/analytics/*", "./src/components/features/landing/*", "./src/components/forms/*", "./src/components/layout/*", "./src/components/shared/*"],
      "@/lib/*": ["./lib/*", "./src/lib/*", "./src/lib/utils/*", "./src/lib/api/*", "./src/lib/services/*", "./src/lib/hooks/*", "./src/lib/validation/*", "./src/lib/types/*"]
    },
    "target": "ES2017"
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules", "src/test/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "tests/**/*", "vitest.config.ts"]
}
