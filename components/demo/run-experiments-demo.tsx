"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import {
  ArrowLeft,
  Play,
  Sparkles,
  Target,
  Users,
  Clock,
  Lightbulb,
  Beaker,
  BarChart
} from "lucide-react"
import { DemoModeWrapper } from "./demo-mode-wrapper"
import { RunExperiment } from "@/components/optimization/run-experiment"
import { runExperimentsDemoSteps } from "./run-experiments-demo-steps"
import { getSurveyResponsesAction } from "@/actions/survey-actions"

// Demo optimization data - simulates an existing optimization ready for experiments
const demoOptimization = {
  id: "demo-optimization-id",
  optimizerId: "demo-optimizer-id",
  name: "Demo Drug Formulation Optimization",
  description:
    "Interactive demo showing how to run experiments and analyze results",
  userId: "demo-user",
  status: "active" as const,
  targetName: "Bioavailability",
  targetMode: "MAX" as const,
  objectiveType: "SingleTarget" as const,
  createdAt: new Date(),
  updatedAt: new Date(),
  measurementCount: 0, // Start with no measurements for demo
  config: {
    parameters: [
      {
        name: "API Concentration",
        type: "NumericalContinuous" as const,
        bounds: [1, 20]
      },
      {
        name: "pH Level",
        type: "NumericalContinuous" as const,
        bounds: [3, 9]
      },
      {
        name: "Excipient Ratio",
        type: "NumericalContinuous" as const,
        bounds: [0.1, 2.0]
      }
    ],
    target_config: {
      name: "Bioavailability",
      mode: "MAX" as const
    },
    objective_type: "SingleTarget" as const,
    recommender_config: {
      type: "BotorchRecommender" as const,
      n_restarts: 10,
      n_raw_samples: 64
    },
    acquisition_config: {
      type: "qExpectedImprovement" as const
    }
  }
}

interface SurveyData {
  industry?: string
  role?: string
  useCase?: string
}

export function RunExperimentsDemo() {
  const router = useRouter()
  const [isDemo, setIsDemo] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [surveyData, setSurveyData] = useState<SurveyData>({})

  // Load user survey data for personalization
  useEffect(() => {
    const loadSurveyData = async () => {
      try {
        const result = await getSurveyResponsesAction()
        if (result.isSuccess && result.responses) {
          setSurveyData(result.responses)
        }
      } catch (error) {
        console.error("Error loading survey data:", error)
      }
    }
    loadSurveyData()
  }, [])

  const handleExitDemo = () => {
    setIsDemo(false)
    // Remove demo params from URL
    router.replace("/dashboard/home")
  }

  const handleDemoComplete = () => {
    console.log("=== RUN EXPERIMENTS DEMO COMPLETION ===")
    toast({
      title: "🎉 Experiments Demo Completed!",
      description:
        "Great job! You've learned how to run experiments and analyze results. Redirecting to dashboard...",
      duration: 5000
    })

    // Redirect to home page after a short delay
    setTimeout(() => {
      console.log("Redirecting to /dashboard/home")
      router.push("/dashboard/home")
    }, 1500)
  }

  const handleStartDemo = () => {
    setIsDemo(true)
    toast({
      title: "🧪 Experiments Demo Activated",
      description:
        "Follow the guided tour to learn how to run experiments and analyze results!"
    })
  }

  const handleSkipToReal = () => {
    router.push("/dashboard/optimizations")
  }

  // If not in demo mode, show the demo introduction
  if (!isDemo) {
    return (
      <div className="mx-auto max-w-4xl py-6">
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push("/dashboard/home")}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 size-4" />
            Back to Dashboard
          </Button>
          <h1 className="text-3xl font-bold">Run Experiments Demo</h1>
          <p className="text-muted-foreground mt-2">
            Learn how to run experiments and analyze optimization results
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Demo Introduction */}
          <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="rounded-lg bg-purple-600 p-2">
                  <Beaker className="size-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-lg">
                    Interactive Experiments Demo
                  </CardTitle>
                  <div className="mt-1 flex items-center gap-2">
                    <Badge className="bg-purple-600 text-xs text-white">
                      🧪 Real Workflow
                    </Badge>
                    {surveyData.industry && (
                      <Badge variant="outline" className="text-xs">
                        {surveyData.industry}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Experience the complete experiment workflow from generating
                samples to analyzing results with a pre-configured optimization.
              </p>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Target className="size-4 text-purple-600" />
                  <span>
                    <strong>Demo:</strong> Drug Formulation Optimization
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Users className="size-4 text-green-600" />
                  <span>Real experiment interface</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="size-4 text-orange-600" />
                  <span>~4 minutes guided tour</span>
                </div>
              </div>

              <Button
                onClick={handleStartDemo}
                className="w-full bg-purple-600 hover:bg-purple-700"
              >
                <Sparkles className="mr-2 size-4" />
                Start Experiments Demo
              </Button>
            </CardContent>
          </Card>

          {/* Skip Option */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                Browse Real Optimizations
              </CardTitle>
              <p className="text-muted-foreground text-sm">
                Skip the demo and view your actual optimizations
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Go directly to your optimizations list to run experiments on
                real optimizations you've created.
              </p>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <span>✓ Your real optimizations</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span>✓ Full experiment capabilities</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span>✓ Real data and results</span>
                </div>
              </div>

              <Button
                variant="outline"
                onClick={handleSkipToReal}
                className="w-full"
              >
                <BarChart className="mr-2 size-4" />
                View My Optimizations
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Tips Section */}
        {surveyData.industry && surveyData.industry !== "skipped" && (
          <Card className="mt-6 border-amber-200 bg-amber-50 dark:bg-amber-950/20">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Lightbulb className="mt-0.5 size-5 text-amber-600" />
                <div>
                  <h3 className="font-medium text-amber-800 dark:text-amber-200">
                    Tailored for {surveyData.industry}
                  </h3>
                  <p className="mt-1 text-sm text-amber-700 dark:text-amber-300">
                    This demo uses examples relevant to {surveyData.industry},
                    showing how to optimize parameters commonly used in your
                    field.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  // Demo mode - wrap the run experiment component with demo functionality
  return (
    <DemoModeWrapper
      isDemo={isDemo}
      onExitDemo={handleExitDemo}
      steps={runExperimentsDemoSteps}
      className="min-h-screen"
      currentWizardStep={currentStep}
      onStepChange={setCurrentStep}
      onDemoComplete={handleDemoComplete}
    >
      <div className="mx-auto max-w-6xl py-6" data-demo="experiments-container">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">{demoOptimization.name}</h1>
              <p className="text-muted-foreground mt-1">
                🧪 Demo Mode - Learn the experiment workflow
              </p>
            </div>
            <Badge className="bg-purple-600 text-white">Experiments Demo</Badge>
          </div>
        </div>

        <RunExperiment optimization={demoOptimization as any} />
      </div>
    </DemoModeWrapper>
  )
}
