import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, createMockParameter } from '../test-utils'
import { ParameterQuickEdit } from '@/components/optimization/parameter-quick-edit'

describe('ParameterQuickEdit', () => {
  const mockOnSave = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders edit trigger button', () => {
    const parameter = createMockParameter({
      name: 'temperature',
      type: 'NumericalContinuous',
      bounds: [20, 100]
    })

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
      />
    )

    const editButton = screen.getByRole('button')
    expect(editButton).toBeInTheDocument()
  })

  it('opens popover when edit button is clicked', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'temperature',
      type: 'NumericalContinuous',
      bounds: [20, 100]
    })

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
      />
    )

    const editButton = screen.getByRole('button')
    await user.click(editButton)

    await waitFor(() => {
      expect(screen.getByText('temperature')).toBeInTheDocument()
      expect(screen.getByText('NumericalContinuous')).toBeInTheDocument()
    })
  })

  it('shows parameter type badge in popover', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'catalyst',
      type: 'CategoricalParameter',
      values: ['A', 'B', 'C']
    })

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
      />
    )

    const editButton = screen.getByRole('button')
    await user.click(editButton)

    await waitFor(() => {
      expect(screen.getByText('CategoricalParameter')).toBeInTheDocument()
    })
  })

  it('allows editing bounds for continuous parameters', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'pressure',
      type: 'NumericalContinuous',
      bounds: [1.0, 5.0]
    })

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
      />
    )

    const editButton = screen.getByRole('button')
    await user.click(editButton)

    await waitFor(() => {
      const lowerBoundInput = screen.getByDisplayValue('1')
      const upperBoundInput = screen.getByDisplayValue('5')
      
      expect(lowerBoundInput).toBeInTheDocument()
      expect(upperBoundInput).toBeInTheDocument()
    })
  })

  it('allows editing values for discrete parameters', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'levels',
      type: 'NumericalDiscrete',
      values: [10, 20, 30],
      tolerance: 1.0
    })

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
      />
    )

    const editButton = screen.getByRole('button')
    await user.click(editButton)

    await waitFor(() => {
      const valuesInput = screen.getByDisplayValue('10, 20, 30')
      const toleranceInput = screen.getByDisplayValue('1')
      
      expect(valuesInput).toBeInTheDocument()
      expect(toleranceInput).toBeInTheDocument()
    })
  })

  it('allows editing categories for categorical parameters', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'material',
      type: 'CategoricalParameter',
      values: ['steel', 'aluminum', 'copper'],
      encoding: 'OHE'
    })

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
      />
    )

    const editButton = screen.getByRole('button')
    await user.click(editButton)

    await waitFor(() => {
      const categoriesInput = screen.getByDisplayValue('steel, aluminum, copper')
      expect(categoriesInput).toBeInTheDocument()
    })
  })

  it('saves changes when save button is clicked', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'temperature',
      type: 'NumericalContinuous',
      bounds: [20, 80]
    })

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
      />
    )

    const editButton = screen.getByRole('button')
    await user.click(editButton)

    await waitFor(async () => {
      const upperBoundInput = screen.getByDisplayValue('80')
      await user.clear(upperBoundInput)
      await user.type(upperBoundInput, '90')

      const saveButton = screen.getByRole('button', { name: /save/i })
      await user.click(saveButton)
    })

    expect(mockOnSave).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'temperature',
        type: 'NumericalContinuous',
        bounds: [20, 90]
      })
    )
  })

  it('cancels changes when cancel button is clicked', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'pressure',
      type: 'NumericalContinuous',
      bounds: [1, 5]
    })

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
      />
    )

    const editButton = screen.getByRole('button')
    await user.click(editButton)

    await waitFor(async () => {
      const upperBoundInput = screen.getByDisplayValue('5')
      await user.clear(upperBoundInput)
      await user.type(upperBoundInput, '10')

      const cancelButton = screen.getByRole('button', { name: /cancel/i })
      await user.click(cancelButton)
    })

    expect(mockOnSave).not.toHaveBeenCalled()
  })

  it('closes popover after saving', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'test_param',
      type: 'NumericalContinuous',
      bounds: [0, 1]
    })

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
      />
    )

    const editButton = screen.getByRole('button')
    await user.click(editButton)

    await waitFor(async () => {
      const saveButton = screen.getByRole('button', { name: /save/i })
      await user.click(saveButton)
    })

    await waitFor(() => {
      expect(screen.queryByText('test_param')).not.toBeInTheDocument()
    })
  })

  it('is disabled when disabled prop is true', () => {
    const parameter = createMockParameter()

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
        disabled={true}
      />
    )

    const editButton = screen.getByRole('button')
    expect(editButton).toBeDisabled()
  })

  it('renders custom trigger when provided', () => {
    const parameter = createMockParameter()
    const customTrigger = <button data-testid="custom-trigger">Custom Edit</button>

    render(
      <ParameterQuickEdit
        parameter={parameter}
        onSave={mockOnSave}
        trigger={customTrigger}
      />
    )

    expect(screen.getByTestId('custom-trigger')).toBeInTheDocument()
    expect(screen.getByText('Custom Edit')).toBeInTheDocument()
  })
})
