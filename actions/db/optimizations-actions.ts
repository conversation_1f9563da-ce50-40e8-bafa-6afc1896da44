// actions/db/optimizations-actions.ts
"use server"

import { db } from "@/db/db";
import {
  optimizationsTable,
  measurementsTable,
  InsertOptimization,
  SelectOptimization,
  InsertMeasurement,
  SelectMeasurement
} from "@/db/schema/optimizations-schema";
import { ActionState } from "@/types";
import { eq, desc, sql, count } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";
import { getActualUserId } from "@/lib/auth-utils";

// Create a new optimization
export async function createOptimizationDBAction(
  optimization: InsertOptimization
): Promise<ActionState<SelectOptimization>> {
  try {
    console.log("Creating optimization in database:", optimization);

    // Validate required fields
    if (!optimization.userId) {
      throw new Error("User ID is required");
    }

    if (!optimization.name) {
      throw new Error("Name is required");
    }

    if (!optimization.optimizerId) {
      throw new Error("Optimizer ID is required");
    }

    // Insert into database
    const [newOptimization] = await db.insert(optimizationsTable)
      .values(optimization)
      .returning();

    console.log("Optimization created successfully:", newOptimization);

    return {
      isSuccess: true,
      message: "Optimization created successfully",
      data: newOptimization
    };
  } catch (error) {
    console.error("Error creating optimization in database:", error);
    return {
      isSuccess: false,
      message: `Failed to create optimization in database: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

// Get all optimizations for a user
export async function getOptimizationsAction(
  userId: string
): Promise<ActionState<SelectOptimization[]>> {
  try {
    console.log("🔍 OPTIMIZATIONS DB - Getting optimizations for userId:", userId);

    const optimizations = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.userId, userId))
      .orderBy(desc(optimizationsTable.createdAt));

    console.log("✅ OPTIMIZATIONS DB - Found", optimizations.length, "optimizations for userId:", userId);

    if (optimizations.length > 0) {
      console.log("📋 OPTIMIZATIONS DB - Sample optimization userIds:",
        optimizations.slice(0, 3).map(opt => ({ id: opt.id, userId: opt.userId, name: opt.name }))
      );
    }

    return {
      isSuccess: true,
      message: "Optimizations retrieved successfully",
      data: optimizations
    };
  } catch (error) {
    console.error("❌ OPTIMIZATIONS DB - Error getting optimizations:", error);
    return {
      isSuccess: false,
      message: "Failed to get optimizations"
    };
  }
}

export async function getOptimizationsActionTest(): Promise<ActionState<SelectOptimization[]>> {
  try {
    const optimizations = await db.select().from(optimizationsTable)
      .orderBy(desc(optimizationsTable.createdAt));

    return {
      isSuccess: true,
      message: "Optimizations retrieved successfully",
      data: optimizations
    };
  } catch (error) {
    console.error("Error getting optimizations:", error)
    return {
      isSuccess: false,
      message: "Failed to get optimizations"
    };
  }
}


// Get a single optimization by ID
export async function getOptimizationByIdAction(
  id: string
): Promise<ActionState<SelectOptimization>> {
  try {
    const [optimization] = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.id, id))
      .limit(1);

    if (!optimization) {
      return {
        isSuccess: false,
        message: "Optimization not found"
      };
    }

    return {
      isSuccess: true,
      message: "Optimization retrieved successfully",
      data: optimization
    };
  } catch (error) {
    console.error("Error getting optimization:", error);
    return {
      isSuccess: false,
      message: "Failed to get optimization"
    };
  }
}

// Get an optimization by its optimizer ID
export async function getOptimizationByOptimizerIdAction(
  optimizerId: string
): Promise<ActionState<SelectOptimization>> {
  try {
    const [optimization] = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.optimizerId, optimizerId))
      .limit(1);

    if (!optimization) {
      return {
        isSuccess: false,
        message: "Optimization not found"
      };
    }

    return {
      isSuccess: true,
      message: "Optimization retrieved successfully",
      data: optimization
    };
  } catch (error) {
    console.error("Error getting optimization:", error);
    return {
      isSuccess: false,
      message: "Failed to get optimization"
    };
  }
}

// Update an optimization
export async function updateOptimizationAction(
  id: string,
  data: Partial<InsertOptimization>
): Promise<ActionState<SelectOptimization>> {
  try {
    const [updatedOptimization] = await db.update(optimizationsTable)
      .set(data)
      .where(eq(optimizationsTable.id, id))
      .returning();

    if (!updatedOptimization) {
      return {
        isSuccess: false,
        message: "Optimization not found"
      };
    }

    return {
      isSuccess: true,
      message: "Optimization updated successfully",
      data: updatedOptimization
    };
  } catch (error) {
    console.error("Error updating optimization:", error);
    return {
      isSuccess: false,
      message: "Failed to update optimization"
    };
  }
}

// Update optimization configuration by optimizer ID
export async function updateOptimizationConfigByOptimizerIdAction(
  optimizerId: string,
  config: any
): Promise<ActionState<SelectOptimization>> {
  try {
    const [updatedOptimization] = await db.update(optimizationsTable)
      .set({
        config: config,
        updatedAt: new Date()
      })
      .where(eq(optimizationsTable.optimizerId, optimizerId))
      .returning();

    if (!updatedOptimization) {
      return {
        isSuccess: false,
        message: "Optimization not found"
      };
    }

    return {
      isSuccess: true,
      message: "Optimization configuration updated successfully",
      data: updatedOptimization
    };
  } catch (error) {
    console.error("Error updating optimization configuration:", error);
    return {
      isSuccess: false,
      message: "Failed to update optimization configuration"
    };
  }
}

// Delete an optimization
export async function deleteOptimizationAction(
  id: string
): Promise<ActionState<void>> {
  try {
    await db.delete(optimizationsTable)
      .where(eq(optimizationsTable.id, id));

    return {
      isSuccess: true,
      message: "Optimization deleted successfully",
      data: undefined
    };
  } catch (error) {
    console.error("Error deleting optimization:", error);
    return {
      isSuccess: false,
      message: "Failed to delete optimization"
    };
  }
}

// Create a new measurement
export async function createMeasurementAction(
  measurement: InsertMeasurement
): Promise<ActionState<SelectMeasurement>> {
  try {
    console.log("Creating measurement in database:", measurement);

    // Validate required fields
    if (!measurement.optimizationId) {
      throw new Error("Optimization ID is required");
    }

    if (!measurement.parameters) {
      throw new Error("Parameters are required");
    }

    if (!measurement.targetValue && !measurement.targetValues) {
      throw new Error("Target value is required");
    }

    // Insert into database
    const [newMeasurement] = await db.insert(measurementsTable)
      .values(measurement)
      .returning();

    console.log("Measurement created successfully:", newMeasurement);

    return {
      isSuccess: true,
      message: "Measurement created successfully",
      data: newMeasurement
    };
  } catch (error) {
    console.error("Error creating measurement:", error);
    return {
      isSuccess: false,
      message: `Failed to create measurement: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

// Get all measurements for an optimization
export async function getMeasurementsAction(
  optimizationId: string
): Promise<ActionState<SelectMeasurement[]>> {
  try {
    const measurements = await db.select().from(measurementsTable)
      .where(eq(measurementsTable.optimizationId, optimizationId))
      .orderBy(desc(measurementsTable.createdAt));

    return {
      isSuccess: true,
      message: "Measurements retrieved successfully",
      data: measurements
    };
  } catch (error) {
    console.error("Error getting measurements:", error);
    return {
      isSuccess: false,
      message: "Failed to get measurements"
    };
  }
}

// Update a measurement
export async function updateMeasurementAction(
  measurementId: string,
  updates: Partial<InsertMeasurement>
): Promise<ActionState<SelectMeasurement>> {
  const authResult = await auth();
  const { userId: clerkUserId } = authResult;

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId();

  console.log("🔍 UPDATE MEASUREMENT ACTION - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  });

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to update measurements"
    };
  }

  try {
    // First, get the existing measurement to verify ownership
    const existingMeasurement = await db.select().from(measurementsTable)
      .where(eq(measurementsTable.id, measurementId))
      .limit(1);

    if (existingMeasurement.length === 0) {
      return {
        isSuccess: false,
        message: "Measurement not found"
      };
    }

    // Get the optimization to verify user ownership
    const optimization = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.id, existingMeasurement[0].optimizationId))
      .limit(1);

    if (optimization.length === 0 || optimization[0].userId !== userId) {
      return {
        isSuccess: false,
        message: "You don't have permission to update this measurement"
      };
    }

    // Validate updates
    if (updates.parameters !== undefined) {
      if (typeof updates.parameters !== 'object' || updates.parameters === null) {
        return {
          isSuccess: false,
          message: "Parameters must be a valid object"
        };
      }

      // Check for empty parameters object
      if (Object.keys(updates.parameters).length === 0) {
        return {
          isSuccess: false,
          message: "Parameters cannot be empty"
        };
      }
    }

    if (updates.targetValue !== undefined) {
      if (typeof updates.targetValue !== 'string' || updates.targetValue.trim() === '') {
        return {
          isSuccess: false,
          message: "Target value must be a non-empty string"
        };
      }

      const numValue = parseFloat(updates.targetValue);
      if (isNaN(numValue) || !isFinite(numValue)) {
        return {
          isSuccess: false,
          message: "Target value must be a valid finite number"
        };
      }
    }

    if (updates.targetValues !== undefined) {
      if (typeof updates.targetValues !== 'object' || updates.targetValues === null) {
        return {
          isSuccess: false,
          message: "Target values must be a valid object"
        };
      }

      // Validate all target values are numbers
      for (const [key, value] of Object.entries(updates.targetValues)) {
        if (typeof value !== 'number' || isNaN(value) || !isFinite(value)) {
          return {
            isSuccess: false,
            message: `Target value for '${key}' must be a valid finite number`
          };
        }
      }
    }

    // Update the measurement
    const [updatedMeasurement] = await db.update(measurementsTable)
      .set({
        ...updates,
        updatedAt: new Date()
      })
      .where(eq(measurementsTable.id, measurementId))
      .returning();

    return {
      isSuccess: true,
      message: "Measurement updated successfully",
      data: updatedMeasurement
    };
  } catch (error) {
    console.error("Error updating measurement:", error);
    return {
      isSuccess: false,
      message: `Failed to update measurement: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

// Delete a measurement
export async function deleteMeasurementAction(
  measurementId: string
): Promise<ActionState<void>> {
  const authResult = await auth();
  const { userId: clerkUserId } = authResult;

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId();

  console.log("🔍 DELETE MEASUREMENT ACTION - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  });

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to delete measurements"
    };
  }

  try {
    // First, get the existing measurement to verify ownership
    const existingMeasurement = await db.select().from(measurementsTable)
      .where(eq(measurementsTable.id, measurementId))
      .limit(1);

    if (existingMeasurement.length === 0) {
      return {
        isSuccess: false,
        message: "Measurement not found"
      };
    }

    // Get the optimization to verify user ownership
    const optimization = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.id, existingMeasurement[0].optimizationId))
      .limit(1);

    if (optimization.length === 0 || optimization[0].userId !== userId) {
      return {
        isSuccess: false,
        message: "You don't have permission to delete this measurement"
      };
    }

    // Check if this is the only measurement (optional safety check)
    const allMeasurements = await db.select().from(measurementsTable)
      .where(eq(measurementsTable.optimizationId, existingMeasurement[0].optimizationId));

    if (allMeasurements.length === 1) {
      console.warn(`Deleting the only measurement for optimization ${existingMeasurement[0].optimizationId}`);
      // We allow this but log a warning
    }

    // Delete the measurement
    const deleteResult = await db.delete(measurementsTable)
      .where(eq(measurementsTable.id, measurementId))
      .returning({ id: measurementsTable.id });

    if (deleteResult.length === 0) {
      return {
        isSuccess: false,
        message: "Measurement could not be deleted - it may have been already deleted"
      };
    }

    return {
      isSuccess: true,
      message: "Measurement deleted successfully",
      data: undefined
    };
  } catch (error) {
    console.error("Error deleting measurement:", error);
    return {
      isSuccess: false,
      message: `Failed to delete measurement: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

// Get measurement counts for all optimizations
export async function getMeasurementCountsAction(userId?: string): Promise<ActionState<Record<string, number>>> {
  try {
    // Create the query based on whether userId is provided
    const results = userId
      ? await db
          .select({
            optimizationId: measurementsTable.optimizationId,
            count: count(measurementsTable.id)
          })
          .from(measurementsTable)
          .innerJoin(
            optimizationsTable,
            eq(measurementsTable.optimizationId, optimizationsTable.id)
          )
          .where(eq(optimizationsTable.userId, userId))
          .groupBy(measurementsTable.optimizationId)
      : await db
          .select({
            optimizationId: measurementsTable.optimizationId,
            count: count(measurementsTable.id)
          })
          .from(measurementsTable)
          .groupBy(measurementsTable.optimizationId);

    // Convert to a map of optimization ID -> count
    const countMap: Record<string, number> = {};
    results.forEach(result => {
      countMap[result.optimizationId] = Number(result.count);
    });

    return {
      isSuccess: true,
      message: "Measurement counts retrieved successfully",
      data: countMap
    };
  } catch (error) {
    console.error("Error getting measurement counts:", error);
    return {
      isSuccess: false,
      message: "Failed to get measurement counts"
    };
  }
}

// Get total number of measurements for a user
export async function getTotalMeasurementsCountAction(userId: string): Promise<ActionState<number>> {
  try {
    // Get the total count of measurements for the user
    const result = await db
      .select({
        count: count(measurementsTable.id)
      })
      .from(measurementsTable)
      .innerJoin(
        optimizationsTable,
        eq(measurementsTable.optimizationId, optimizationsTable.id)
      )
      .where(eq(optimizationsTable.userId, userId));

    // Extract the count from the result
    const totalCount = result.length > 0 ? Number(result[0].count) : 0;

    return {
      isSuccess: true,
      message: "Total measurements count retrieved successfully",
      data: totalCount
    };
  } catch (error) {
    console.error("Error getting total measurements count:", error);
    return {
      isSuccess: false,
      message: "Failed to get total measurements count"
    };
  }
}

// Get latest measurement for each optimization
export async function getLatestMeasurementsAction(userId?: string): Promise<ActionState<Record<string, SelectMeasurement>>> {
  try {
    // Get the max createdAt for each optimization based on whether userId is provided
    const latestTimestamps = userId
      ? await db
          .select({
            optimizationId: measurementsTable.optimizationId,
            maxCreatedAt: sql<string>`MAX(${measurementsTable.createdAt})`
          })
          .from(measurementsTable)
          .innerJoin(
            optimizationsTable,
            eq(measurementsTable.optimizationId, optimizationsTable.id)
          )
          .where(eq(optimizationsTable.userId, userId))
          .groupBy(measurementsTable.optimizationId)
      : await db
          .select({
            optimizationId: measurementsTable.optimizationId,
            maxCreatedAt: sql<string>`MAX(${measurementsTable.createdAt})`
          })
          .from(measurementsTable)
          .groupBy(measurementsTable.optimizationId);

    // Then, for each optimization, we get the measurement with that timestamp
    const latestMeasurements: Record<string, SelectMeasurement> = {};

    // For each optimization with its latest timestamp
    for (const { optimizationId, maxCreatedAt } of latestTimestamps) {
      const [measurement] = await db
        .select()
        .from(measurementsTable)
        .where(
          sql`${measurementsTable.optimizationId} = ${optimizationId} AND
              ${measurementsTable.createdAt} = ${maxCreatedAt}`
        )
        .limit(1);

      if (measurement) {
        latestMeasurements[optimizationId] = measurement;
      }
    }

    return {
      isSuccess: true,
      message: "Latest measurements retrieved successfully",
      data: latestMeasurements
    };
  } catch (error) {
    console.error("Error getting latest measurements:", error);
    return {
      isSuccess: false,
      message: "Failed to get latest measurements"
    };
  }
}

// Get improvement percentages for optimizations (first vs last measurement)
export async function getOptimizationImprovementsAction(
  userId: string
): Promise<ActionState<Record<string, number>>> {
  try {
    // Get all optimizations for the user
    const optimizations = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.userId, userId));

    const improvements: Record<string, number> = {};

    // For each optimization, calculate improvement
    for (const optimization of optimizations) {
      // Get first and last measurements for this optimization
      const measurements = await db.select().from(measurementsTable)
        .where(eq(measurementsTable.optimizationId, optimization.id))
        .orderBy(measurementsTable.createdAt);

      if (measurements.length >= 2) {
        const firstMeasurement = measurements[0];
        const lastMeasurement = measurements[measurements.length - 1];

        const firstValue = parseFloat(firstMeasurement.targetValue);
        const lastValue = parseFloat(lastMeasurement.targetValue);

        if (!isNaN(firstValue) && !isNaN(lastValue) && firstValue !== 0) {
          // Calculate improvement based on target mode (MAX or MIN)
          let improvementPercent: number;

          if (optimization.targetMode === "MAX") {
            // For maximization: improvement = ((last - first) / first) * 100
            improvementPercent = ((lastValue - firstValue) / Math.abs(firstValue)) * 100;
          } else {
            // For minimization: improvement = ((first - last) / first) * 100
            improvementPercent = ((firstValue - lastValue) / Math.abs(firstValue)) * 100;
          }

          improvements[optimization.id] = Math.round(improvementPercent * 10) / 10; // Round to 1 decimal
        } else {
          improvements[optimization.id] = 0;
        }
      } else {
        // Not enough measurements to calculate improvement
        improvements[optimization.id] = 0;
      }
    }

    return {
      isSuccess: true,
      message: "Optimization improvements calculated successfully",
      data: improvements
    };
  } catch (error) {
    console.error("Error calculating optimization improvements:", error);
    return {
      isSuccess: false,
      message: "Failed to calculate optimization improvements"
    };
  }
}

// Get dashboard statistics for a user
export async function getDashboardStatsAction(
  userId: string
): Promise<ActionState<{
  totalExperiments: number;
  monthlyGrowth: number;
  activeOptimizations: number;
  completedOptimizations: number;
  successRate: number;
  recentActivity: number;
}>> {
  try {
    // Get total experiments count
    const totalExperimentsResult = await getTotalMeasurementsCountAction(userId);
    const totalExperiments = totalExperimentsResult.isSuccess ? totalExperimentsResult.data : 0;

    // Get all optimizations for the user
    const optimizations = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.userId, userId))
      .orderBy(desc(optimizationsTable.createdAt));

    const activeOptimizations = optimizations.filter(opt =>
      opt.status === "active" || opt.status === "draft"
    ).length;

    const completedOptimizations = optimizations.filter(opt =>
      opt.status === "completed"
    ).length;

    // Calculate success rate
    const successRate = optimizations.length > 0
      ? Math.round((completedOptimizations / optimizations.length) * 100)
      : 0;

    // Calculate monthly growth (experiments created in last 30 days vs previous 30 days)
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    // Get experiments from last 30 days
    const recentExperiments = await db
      .select({ count: count(measurementsTable.id) })
      .from(measurementsTable)
      .innerJoin(optimizationsTable, eq(measurementsTable.optimizationId, optimizationsTable.id))
      .where(
        sql`${optimizationsTable.userId} = ${userId} AND ${measurementsTable.createdAt} >= ${thirtyDaysAgo.toISOString()}`
      );

    // Get experiments from 30-60 days ago
    const previousExperiments = await db
      .select({ count: count(measurementsTable.id) })
      .from(measurementsTable)
      .innerJoin(optimizationsTable, eq(measurementsTable.optimizationId, optimizationsTable.id))
      .where(
        sql`${optimizationsTable.userId} = ${userId} AND ${measurementsTable.createdAt} >= ${sixtyDaysAgo.toISOString()} AND ${measurementsTable.createdAt} < ${thirtyDaysAgo.toISOString()}`
      );

    const recentCount = recentExperiments[0]?.count ? Number(recentExperiments[0].count) : 0;
    const previousCount = previousExperiments[0]?.count ? Number(previousExperiments[0].count) : 0;

    // Calculate monthly growth percentage
    let monthlyGrowth = 0;
    if (previousCount > 0) {
      monthlyGrowth = Math.round(((recentCount - previousCount) / previousCount) * 100);
    } else if (recentCount > 0) {
      monthlyGrowth = 100; // If no previous data but recent data exists, show 100% growth
    }

    // Get recent activity (optimizations created in last 7 days)
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const recentActivity = optimizations.filter(opt =>
      new Date(opt.createdAt) >= sevenDaysAgo
    ).length;

    return {
      isSuccess: true,
      message: "Dashboard statistics retrieved successfully",
      data: {
        totalExperiments,
        monthlyGrowth,
        activeOptimizations,
        completedOptimizations,
        successRate,
        recentActivity
      }
    };
  } catch (error) {
    console.error("Error getting dashboard statistics:", error);
    return {
      isSuccess: false,
      message: "Failed to get dashboard statistics"
    };
  }
}