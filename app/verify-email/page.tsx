"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { verifyEmailTokenAction } from "@/actions/academic-verification-actions"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Loader2, CheckCircle, XCircle } from "lucide-react"
import { useAuth } from "@clerk/nextjs"

export default function VerifyEmailPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get("token")
  const { isSignedIn } = useAuth()

  const [isVerifying, setIsVerifying] = useState(true)
  const [verificationResult, setVerificationResult] = useState<{
    isSuccess: boolean
    message: string
  } | null>(null)

  useEffect(() => {
    async function verifyToken() {
      if (!token) {
        setVerificationResult({
          isSuccess: false,
          message: "No verification token provided"
        })
        setIsVerifying(false)
        return
      }

      try {
        const result = await verifyEmailTokenAction(token)
        setVerificationResult(result)

        // If verification was successful and user is signed in, redirect to academic survey
        if (result.isSuccess && isSignedIn) {
          setTimeout(() => {
            router.push("/academic-survey")
          }, 3000) // Redirect after 3 seconds
        }
      } catch (error) {
        console.error("Error verifying token:", error)
        setVerificationResult({
          isSuccess: false,
          message: "An error occurred during verification"
        })
      } finally {
        setIsVerifying(false)
      }
    }

    verifyToken()
  }, [token, router, isSignedIn])

  return (
    <div className="container mx-auto flex min-h-screen flex-col items-center justify-center px-4 py-12">
      <Card className="mx-auto w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">
            Email Verification
          </CardTitle>
          <CardDescription>
            Verifying your academic email address
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {isVerifying ? (
            <div className="flex flex-col items-center justify-center space-y-4 py-8">
              <Loader2 className="text-primary size-12 animate-spin" />
              <p className="text-muted-foreground text-center">
                Verifying your email address...
              </p>
            </div>
          ) : verificationResult?.isSuccess ? (
            <div className="flex flex-col items-center justify-center space-y-4 py-8">
              <CheckCircle className="size-12 text-green-500" />
              <h3 className="text-xl font-medium text-green-500">
                Verification Successful
              </h3>
              <p className="text-muted-foreground text-center">
                {verificationResult.message}
              </p>
              <div className="mt-2 rounded-md bg-blue-50 p-3 dark:bg-blue-900/20">
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Congratulations! Your account now has a 90-day academic trial.
                  Enjoy extended access to all premium features.
                </p>
              </div>
              {isSignedIn ? (
                <p className="text-muted-foreground text-center text-sm">
                  You will be redirected to the academic survey in a few
                  seconds...
                </p>
              ) : (
                <div className="flex w-full flex-col space-y-4 pt-4">
                  <p className="text-muted-foreground text-center text-sm">
                    Please sign in to continue with the academic survey.
                  </p>
                  <Button
                    onClick={() =>
                      router.push("/login?redirect_url=/academic-survey")
                    }
                  >
                    Sign In
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center space-y-4 py-8">
              <XCircle className="size-12 text-red-500" />
              <h3 className="text-xl font-medium text-red-500">
                Verification Failed
              </h3>
              <p className="text-muted-foreground text-center">
                {verificationResult?.message ||
                  "An error occurred during verification"}
              </p>
              <div className="flex w-full flex-col space-y-4 pt-4">
                <Button onClick={() => router.push("/academic-signup")}>
                  Return to Academic Signup
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
