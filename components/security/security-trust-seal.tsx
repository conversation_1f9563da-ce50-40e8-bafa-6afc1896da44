"use client"

import { motion } from "framer-motion"
import { Shield, Lock, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"

interface SecurityTrustSealProps {
  variant?: "compact" | "full"
  showLink?: boolean
}

export function SecurityTrustSeal({
  variant = "compact",
  showLink = true
}: SecurityTrustSealProps) {
  if (variant === "compact") {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="inline-flex items-center gap-2 rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 px-3 py-2 text-sm font-medium text-blue-700 shadow-sm"
            >
              <Shield className="size-4 text-blue-600" />
              <span>Secured</span>
              <CheckCircle className="size-3 text-blue-500" />
            </motion.div>
          </TooltipTrigger>
          <TooltipContent>
            <p className="font-medium">Enterprise Security</p>
            <p className="text-muted-foreground text-xs">
              SOC 2 • GDPR • AWS Security
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="rounded-xl border border-blue-200 bg-gradient-to-br from-blue-50 via-white to-indigo-50 p-6 shadow-sm"
    >
      <div className="flex items-center gap-4">
        <div className="flex size-12 items-center justify-center rounded-full bg-blue-100">
          <Shield className="size-6 text-blue-600" />
        </div>
        <div className="flex-1">
          <h3 className="flex items-center gap-2 font-semibold text-blue-800">
            Enterprise Security
            <CheckCircle className="size-4 text-blue-600" />
          </h3>
          <p className="text-sm text-blue-700">
            Bank-level encryption • SOC 2 Compliant • GDPR Ready
          </p>
        </div>
        {showLink && (
          <Button
            asChild
            variant="outline"
            size="sm"
            className="border-blue-300 text-blue-700 hover:bg-blue-100"
          >
            <Link href="/security">
              <Lock className="mr-1 size-3" />
              Learn More
            </Link>
          </Button>
        )}
      </div>
    </motion.div>
  )
}
