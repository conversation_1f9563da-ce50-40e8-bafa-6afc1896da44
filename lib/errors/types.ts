// Error classification system for configuration management
export enum ErrorCategory {
  VALIDATION = "validation",
  CONSTRAINT = "constraint",
  BOUNDS = "bounds",
  PARAMETER = "parameter",
  TARGET = "target",
  ACQUISITION = "acquisition",
  MEASUREMENT = "measurement",
  SYSTEM = "system",
  NETWORK = "network",
  CONCURRENCY = "concurrency"
}

export enum ErrorSeverity {
  ERROR = "error",
  WARNING = "warning",
  INFO = "info"
}

export enum ErrorCode {
  // Validation errors
  VALIDATION_REQUIRED_FIELD = "VALIDATION_001",
  VALIDATION_INVALID_FORMAT = "VALIDATION_002",
  VALIDATION_OUT_OF_RANGE = "VALIDATION_003",
  VALIDATION_TYPE_MISMATCH = "VALIDATION_004",

  // Parameter errors
  PARAMETER_BOUNDS_INVALID = "PARAMETER_001",
  PARAMETER_VALUES_EMPTY = "PARAMETER_002",
  PARAMETER_TYPE_CHANGE_INVALID = "PARAMETER_003",
  PARAMETER_NAME_DUPLICATE = "PARAMETER_004",
  PARAMETER_BOUNDS_TOO_NARROW = "PARAMETER_005",
  PARAMETER_VALUES_DUPLICATE = "PARAMETER_006",

  // Target errors
  TARGET_BOUNDS_INVALID = "TARGET_001",
  TARGET_MODE_INCOMPATIBLE = "TARGET_002",
  TARGET_WEIGHT_INVALID = "TARGET_003",
  TARGET_TRANSFORMATION_INVALID = "TARGET_004",

  // Constraint errors
  CONSTRAINT_SYNTAX_ERROR = "CONSTRAINT_001",
  CONSTRAINT_PARAMETER_NOT_FOUND = "CONSTRAINT_002",
  CONSTRAINT_INFEASIBLE = "CONSTRAINT_003",
  CONSTRAINT_CONFLICTING = "CONSTRAINT_004",
  CONSTRAINT_REDUNDANT = "CONSTRAINT_005",

  // Measurement errors
  MEASUREMENT_ALL_FILTERED = "MEASUREMENT_001",
  MEASUREMENT_INSUFFICIENT_DATA = "MEASUREMENT_002",
  MEASUREMENT_BOUNDS_VIOLATION = "MEASUREMENT_003",
  MEASUREMENT_CONSTRAINT_VIOLATION = "MEASUREMENT_004",

  // System errors
  SYSTEM_CAMPAIGN_NOT_FOUND = "SYSTEM_001",
  SYSTEM_SAVE_FAILED = "SYSTEM_002",
  SYSTEM_LOAD_FAILED = "SYSTEM_003",
  SYSTEM_MEMORY_ERROR = "SYSTEM_004",

  // Network errors
  NETWORK_CONNECTION_FAILED = "NETWORK_001",
  NETWORK_TIMEOUT = "NETWORK_002",
  NETWORK_SERVER_ERROR = "NETWORK_003",

  // Concurrency errors
  CONCURRENCY_CONFLICT = "CONCURRENCY_001",
  CONCURRENCY_LOCK_TIMEOUT = "CONCURRENCY_002",
  CONCURRENCY_VERSION_MISMATCH = "CONCURRENCY_003"
}

export interface QuickFix {
  id: string
  label: string
  description: string
  action: QuickFixAction
  parameters?: Record<string, any>
}

export interface QuickFixAction {
  type:
    | "adjust_bounds"
    | "remove_constraint"
    | "modify_parameter"
    | "reset_to_default"
    | "expand_range"
    | "remove_duplicates"
  target: string
  value?: any
}

export interface ErrorResolution {
  description: string
  steps: string[]
  quickFixes?: QuickFix[]
  documentationUrl?: string
  relatedErrors?: string[]
}

export interface StructuredError {
  id: string
  code: ErrorCode
  category: ErrorCategory
  severity: ErrorSeverity
  field?: string
  fieldPath?: string[]
  currentValue?: any
  expectedValue?: any
  expectedRange?: [number, number]
  message: string
  technicalMessage?: string
  resolution: ErrorResolution
  context?: Record<string, any>
  timestamp: Date
  source: "frontend" | "backend" | "validation"
}

export interface ErrorContext {
  optimizationId?: string
  userId?: string
  sessionId?: string
  operation?: string
  entityType?: string
  entityId?: string
  additionalData?: Record<string, any>
}

export interface ValidationResult {
  isValid: boolean
  errors: StructuredError[]
  warnings: StructuredError[]
  infos: StructuredError[]
}

// Utility function to generate unique error IDs
export function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
