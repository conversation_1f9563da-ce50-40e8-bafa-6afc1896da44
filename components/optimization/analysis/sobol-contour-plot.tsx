"use client"

import React, { useState, useEffect } from "react"
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>lider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  AlertCircle,
  RefreshCw,
  Download,
  Grid3X3,
  Check,
  X,
  Plus,
  Target,
  ChevronDown,
  ChevronUp,
  BarChart3
} from "lucide-react"
import { getSobolContourDataAction } from "@/actions/sobol-analysis-actions"
import { SelectOptimization } from "@/db/schema/optimizations-schema"
import { API_CONFIG } from "@/lib/config"
import dynamic from "next/dynamic"

// Dynamically import Plotly to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

interface SobolContourPlotProps {
  optimization: SelectOptimization
  parameterNames: string[]
  targetMode?: "MAX" | "MIN" | "MULTI"
  availableTargets?: string[] // Available targets for multi-target optimizations
}

export function SobolContourPlot({
  optimization,
  parameterNames,
  targetMode = "MAX",
  availableTargets = []
}: SobolContourPlotProps) {
  const [param1, setParam1] = useState<string>("")
  const [param2, setParam2] = useState<string>("")
  const [selectedTarget, setSelectedTarget] = useState<string>("")
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [errorType, setErrorType] = useState<
    | "general"
    | "insufficient_data"
    | "constant_predictions"
    | "interval_too_small"
    | null
  >(null)
  const [plotData, setPlotData] = useState<any>(null)

  // Function to analyze error message and determine error type
  const analyzeError = (
    errorMessage: string
  ):
    | "general"
    | "insufficient_data"
    | "constant_predictions"
    | "interval_too_small" => {
    const message = errorMessage.toLowerCase()

    if (
      message.includes("insufficient measurements") ||
      message.includes("minimum 5 required")
    ) {
      return "insufficient_data"
    }

    if (
      message.includes("constant predictions") ||
      message.includes("returning constant predictions")
    ) {
      return "constant_predictions"
    }

    if (
      message.includes("minimum") &&
      (message.includes("experiment") || message.includes("data"))
    ) {
      return "interval_too_small"
    }

    return "general"
  }

  // Plot configuration
  const [resolution, setResolution] = useState<number>(50)
  const [smoothing, setSmoothing] = useState<boolean>(true)

  // Experiment range selection
  const [useExperimentRange, setUseExperimentRange] = useState<boolean>(false)
  const [experimentStartIdx, setExperimentStartIdx] = useState<
    number | undefined
  >(undefined)
  const [experimentEndIdx, setExperimentEndIdx] = useState<number | undefined>(
    undefined
  )
  const [totalExperiments, setTotalExperiments] = useState<number>(0)

  // Comparative analysis state
  const [compareMode, setCompareMode] = useState<boolean>(false)
  const [targetResults, setTargetResults] = useState<Record<string, any>>({})
  const [selectedTargetsForComparison, setSelectedTargetsForComparison] =
    useState<string[]>([])
  const [showConfidenceIntervals, setShowConfidenceIntervals] =
    useState<boolean>(true)

  // Enhanced target selection state
  const [showTargetSelector, setShowTargetSelector] = useState<boolean>(true)

  // Track when experiment range changes
  const [lastRefreshRange, setLastRefreshRange] = useState<{
    start?: number
    end?: number
  } | null>(null)

  // Check if the current experiment range is different from the last refresh
  const isRangeChanged =
    useExperimentRange &&
    (lastRefreshRange === null ||
      lastRefreshRange.start !== experimentStartIdx ||
      lastRefreshRange.end !== experimentEndIdx)

  // Determine if this is a multi-target optimization
  const isMultiTarget = targetMode === "MULTI" && availableTargets.length > 0

  // Set default parameters when component mounts
  useEffect(() => {
    if (parameterNames.length >= 2) {
      setParam1(parameterNames[0])
      setParam2(parameterNames[1])
    }
  }, [parameterNames])

  // Set default target when component mounts
  useEffect(() => {
    if (isMultiTarget && availableTargets.length > 0) {
      setSelectedTarget(availableTargets[0])
    } else {
      // For single target, use the primary target
      setSelectedTarget(optimization.targetName)
    }
  }, [isMultiTarget, availableTargets, optimization.targetName])

  // Auto-enable compare mode when multiple targets are selected
  useEffect(() => {
    if (selectedTargetsForComparison.length >= 2) {
      setCompareMode(true)
    } else if (selectedTargetsForComparison.length === 0) {
      setCompareMode(false)
    }
  }, [selectedTargetsForComparison.length])

  // Fetch the total number of experiments when component mounts
  useEffect(() => {
    const fetchTotalExperiments = async () => {
      try {
        // This is a simplified approach - in a real implementation, you would
        // fetch this data from the API or database
        const response = await fetch(
          `${API_CONFIG.BASE_URL}/optimizations/${optimization.optimizerId}/measurements`
        )
        if (response.ok) {
          const data = await response.json()
          if (data.measurements && Array.isArray(data.measurements)) {
            setTotalExperiments(data.measurements.length)
          }
        }
      } catch (error) {
        console.error("Error fetching total experiments:", error)
      }
    }

    fetchTotalExperiments()
  }, [optimization.optimizerId])

  // Fetch contour data when parameters, selected target, resolution, or experiment range changes
  useEffect(() => {
    if (param1 && param2 && param1 !== param2 && selectedTarget) {
      fetchContourData()
    }
  }, [
    param1,
    param2,
    selectedTarget,
    resolution,
    useExperimentRange,
    experimentStartIdx,
    experimentEndIdx
  ])

  // Function to fetch data for comparison targets without affecting the main plot
  const fetchComparisonTargetData = async (targetName: string) => {
    if (!param1 || !param2 || param1 === param2) return

    try {
      const requestParams = {
        optimizerId: optimization.optimizerId,
        param1Name: param1,
        param2Name: param2,
        targetName: targetName,
        gridSize: resolution,
        experimentStartIdx: useExperimentRange ? experimentStartIdx : undefined,
        experimentEndIdx: useExperimentRange ? experimentEndIdx : undefined
      }

      const result = await getSobolContourDataAction(requestParams)

      if (result.isSuccess && result.data) {
        // Store results for comparison
        setTargetResults(prev => ({
          ...prev,
          [targetName]: result.data
        }))
      }
    } catch (err) {
      console.error(`Error fetching data for target ${targetName}:`, err)
    }
  }

  const fetchContourData = async () => {
    setLoading(true)
    setError(null)
    setErrorType(null)

    try {
      // Prepare request parameters
      const requestParams = {
        optimizerId: optimization.optimizerId,
        param1Name: param1,
        param2Name: param2,
        targetName: selectedTarget, // Include the selected target
        gridSize: resolution, // Include the resolution/grid size
        experimentStartIdx: useExperimentRange ? experimentStartIdx : undefined,
        experimentEndIdx: useExperimentRange ? experimentEndIdx : undefined
      }

      // Log the request parameters
      console.log("Sobol Analysis Request Parameters:", requestParams)
      console.log("Experiment Range Enabled:", useExperimentRange)
      console.log("Experiment Start Index:", experimentStartIdx)
      console.log("Experiment End Index:", experimentEndIdx)

      const result = await getSobolContourDataAction(requestParams)

      // Add detailed logging of the API response
      console.log("Sobol Analysis API Response:", result)

      if (result.isSuccess && result.data) {
        // Log the sensitivity indices specifically
        console.log("Sobol First Order:", result.data.sobol_first_order)
        console.log("Sobol Second Order:", result.data.sobol_second_order)
        console.log("Sobol Total:", result.data.sobol_total)

        if (result.data.sobol_first_order_ci) {
          console.log("Sobol First Order CI:", result.data.sobol_first_order_ci)
        }
        if (result.data.sobol_second_order_ci) {
          console.log(
            "Sobol Second Order CI:",
            result.data.sobol_second_order_ci
          )
        }
        if (result.data.sobol_total_ci) {
          console.log("Sobol Total CI:", result.data.sobol_total_ci)
        }

        setPlotData(result.data)

        // Update the last refresh range
        if (useExperimentRange) {
          setLastRefreshRange({
            start: experimentStartIdx,
            end: experimentEndIdx
          })
        } else {
          setLastRefreshRange(null)
        }

        // Store results for comparison if in compare mode
        if (compareMode) {
          setTargetResults(prev => ({
            ...prev,
            [selectedTarget]: result.data
          }))

          // Add to selected targets for comparison if not already there
          if (!selectedTargetsForComparison.includes(selectedTarget)) {
            setSelectedTargetsForComparison(prev => [...prev, selectedTarget])
          }
        }
      } else {
        const errorMessage = result.message || "Failed to fetch contour data"
        setError(errorMessage)
        setErrorType(analyzeError(errorMessage))
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "An error occurred while fetching contour data"
      setError(errorMessage)
      setErrorType(analyzeError(errorMessage))
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = () => {
    if (param1 && param2 && param1 !== param2 && selectedTarget) {
      fetchContourData()
    }
  }

  // Function to handle plot download
  const handleDownload = () => {
    try {
      // Find the plot element
      const plotElement = document.querySelector(
        ".js-plotly-plot"
      ) as HTMLElement
      if (!plotElement) {
        console.error("Plot element not found for download")
        return
      }

      // Generate a descriptive filename
      const filename = `sobol-contour-${param1}-${param2}-${selectedTarget}.png`

      // Use Plotly's toImage function
      // @ts-ignore - Plotly is added to window by react-plotly
      if (window.Plotly && typeof window.Plotly.toImage === "function") {
        window.Plotly.toImage(plotElement, {
          format: "png",
          width: 1200,
          height: 800
        })
          .then((dataUrl: string) => {
            const link = document.createElement("a")
            link.download = filename
            link.href = dataUrl
            link.click()
          })
          .catch((err: Error) => {
            console.error("Error generating plot image:", err)
          })
      } else {
        console.error("Plotly.toImage function not available")
      }
    } catch (error) {
      console.error("Error in handleDownload:", error)
    }
  }

  const renderPlot = () => {
    if (!plotData) return null

    const {
      x_values,
      y_values,
      z_values,
      uncertainty,
      sobol_first_order,
      sobol_second_order,
      sobol_total,
      param1_type,
      param2_type,
      param1_is_categorical,
      param2_is_categorical,
      param1_categories,
      param2_categories
    } = plotData

    // Create x-axis and y-axis labels based on parameter types
    const xaxis = {
      title: {
        text: param1
      },
      type: param1_is_categorical ? "category" : "linear",
      tickvals: param1_is_categorical
        ? x_values.map((_: number, i: number) => i)
        : undefined,
      ticktext:
        param1_is_categorical && param1_categories
          ? param1_categories
          : undefined
    }

    const yaxis = {
      title: {
        text: param2
      },
      type: param2_is_categorical ? "category" : "linear",
      tickvals: param2_is_categorical
        ? y_values.map((_: number, i: number) => i)
        : undefined,
      ticktext:
        param2_is_categorical && param2_categories
          ? param2_categories
          : undefined
    }

    // Create contour plot data
    const contourData = [
      {
        type: "contour",
        x: x_values,
        y: y_values,
        z: z_values,
        colorscale: "Viridis",
        contours: {
          coloring: "heatmap",
          showlabels: true
        },
        colorbar: {
          title: "Predicted Value",
          titleside: "right"
        },
        hovertemplate:
          `${param1}: %{x}<br>` +
          `${param2}: %{y}<br>` +
          `Predicted Value: %{z}<br>` +
          `<extra></extra>`
      },
      {
        type: "contour",
        x: x_values,
        y: y_values,
        z: uncertainty,
        colorscale: "Greys",
        opacity: 0.3,
        showscale: false,
        contours: {
          coloring: "lines",
          showlabels: false
        },
        line: {
          width: 0.5,
          dash: "dash"
        },
        name: "Uncertainty",
        hoverinfo: "skip",
        visible: "legendonly"
      }
    ]

    // Plot layout
    const layout = {
      title: {
        text: `SOBOL Contour Analysis: ${param1} vs ${param2} (Target: ${plotData.target_name})${
          plotData.experiment_start_idx !== undefined ||
          plotData.experiment_end_idx !== undefined
            ? ` [Experiments: ${plotData.experiment_start_idx || 0} - ${
                plotData.experiment_end_idx !== undefined
                  ? plotData.experiment_end_idx
                  : "end"
              }]`
            : ""
        }`,
        font: {
          size: 16
        }
      },
      xaxis,
      yaxis,
      height: 500,
      margin: { l: 60, r: 50, b: 60, t: 80 },
      annotations: [
        {
          x: 0.5,
          y: 1.05,
          xref: "paper",
          yref: "paper",
          text: `Interaction Strength (Sobol' Index): ${sobol_second_order != null ? sobol_second_order.toFixed(4) : "N/A"}`,
          showarrow: false,
          font: {
            size: 12
          }
        }
      ]
    }

    return (
      <Plot
        data={contourData}
        layout={layout}
        config={{ responsive: true }}
        style={{ width: "100%", height: "100%" }}
      />
    )
  }

  const renderEnhancedErrorAlert = () => {
    if (!error) return null

    const getErrorContent = () => {
      switch (errorType) {
        case "insufficient_data":
          return {
            title: "Insufficient Data for Interval Analysis",
            description: (
              <div className="space-y-3">
                <p>
                  The selected experiment range contains too few experiments for
                  reliable Sobol sensitivity analysis.
                </p>
                <div className="rounded-md bg-blue-50 p-3 dark:bg-blue-900/20">
                  <p className="mb-2 font-medium text-blue-900 dark:text-blue-100">
                    💡 Recommendations:
                  </p>
                  <ul className="space-y-1 text-sm text-blue-800 dark:text-blue-200">
                    <li>
                      • <strong>Remove experiment range filters</strong> to use
                      all {totalExperiments} experiments
                    </li>
                    <li>
                      • <strong>Expand the range</strong> to include at least
                      5-7 experiments
                    </li>
                    <li>
                      • <strong>Add more experiments</strong> to your
                      optimization for better interval analysis
                    </li>
                  </ul>
                </div>
                <p className="text-muted-foreground text-sm">
                  Minimum 5 experiments required for filtered analysis. For
                  datasets with fewer experiments, full dataset analysis is
                  recommended.
                </p>
              </div>
            ),
            variant: "default" as const,
            icon: "📊"
          }

        case "constant_predictions":
          return {
            title: "Model Predictions Are Constant",
            description: (
              <div className="space-y-3">
                <p>
                  The surrogate model is returning identical predictions for all
                  parameter combinations, making sensitivity analysis
                  impossible.
                </p>
                <div className="rounded-md bg-amber-50 p-3 dark:bg-amber-900/20">
                  <p className="mb-2 font-medium text-amber-900 dark:text-amber-100">
                    🔧 Solutions:
                  </p>
                  <ul className="space-y-1 text-sm text-amber-800 dark:text-amber-200">
                    <li>
                      • <strong>Use full dataset</strong> instead of experiment
                      range filtering
                    </li>
                    <li>
                      • <strong>Add more diverse experiments</strong> with
                      different parameter values
                    </li>
                    <li>
                      • <strong>Check data quality</strong> - ensure target
                      values vary across experiments
                    </li>
                    <li>
                      • <strong>Verify parameter ranges</strong> - ensure
                      parameters have meaningful variation
                    </li>
                  </ul>
                </div>
                <p className="text-muted-foreground text-sm">
                  This typically occurs with very small datasets or when all
                  experiments have similar results.
                </p>
              </div>
            ),
            variant: "default" as const,
            icon: "⚠️"
          }

        case "interval_too_small":
          return {
            title: "Experiment Range Too Small",
            description: (
              <div className="space-y-3">
                <p>
                  The selected experiment range doesn't contain enough data
                  points for meaningful analysis.
                </p>
                <div className="rounded-md bg-green-50 p-3 dark:bg-green-900/20">
                  <p className="mb-2 font-medium text-green-900 dark:text-green-100">
                    ✅ Quick Fixes:
                  </p>
                  <ul className="space-y-1 text-sm text-green-800 dark:text-green-200">
                    <li>
                      • <strong>Expand range:</strong> Include experiments{" "}
                      {Math.max(0, (experimentStartIdx || 0) - 2)}-
                      {Math.min(
                        totalExperiments - 1,
                        (experimentEndIdx || totalExperiments - 1) + 2
                      )}
                    </li>
                    <li>
                      • <strong>Use full dataset:</strong> Disable experiment
                      range filtering
                    </li>
                    <li>
                      • <strong>Try different ranges:</strong> First half vs
                      second half comparison
                    </li>
                  </ul>
                </div>
              </div>
            ),
            variant: "default" as const,
            icon: "📏"
          }

        default:
          return {
            title: "Analysis Error",
            description: (
              <div className="space-y-3">
                <p>{error}</p>
                <div className="rounded-md bg-gray-50 p-3 dark:bg-gray-900/20">
                  <p className="mb-2 font-medium text-gray-900 dark:text-gray-100">
                    🔍 Troubleshooting:
                  </p>
                  <ul className="space-y-1 text-sm text-gray-800 dark:text-gray-200">
                    <li>
                      • Check that both parameters have different values across
                      experiments
                    </li>
                    <li>• Ensure the target has meaningful variation</li>
                    <li>
                      • Try using the full dataset without experiment range
                      filtering
                    </li>
                    <li>
                      • Verify that the optimization has sufficient measurements
                    </li>
                  </ul>
                </div>
              </div>
            ),
            variant: "destructive" as const,
            icon: "❌"
          }
      }
    }

    const { title, description, variant, icon } = getErrorContent()

    return (
      <Alert variant={variant} className="mb-6">
        <AlertCircle className="size-4" />
        <AlertTitle className="flex items-center gap-2">
          <span>{icon}</span>
          {title}
        </AlertTitle>
        <AlertDescription className="mt-2">{description}</AlertDescription>
      </Alert>
    )
  }

  const renderFixedParamsAlert = () => {
    if (
      !plotData ||
      !plotData.fixed_params ||
      plotData.fixed_params.length === 0
    )
      return null

    return (
      <Alert className="mb-4 bg-blue-50 dark:bg-blue-900/20">
        <AlertCircle className="size-4" />
        <AlertTitle>Parameter Convergence Detected</AlertTitle>
        <AlertDescription>
          {plotData.fixed_params_message ||
            `The following parameters have converged to fixed values: ${plotData.fixed_params.join(", ")}.
            Their sensitivity indices are set to 0 as they no longer contribute to output variation.`}
        </AlertDescription>
      </Alert>
    )
  }

  const renderSensitivityInfo = () => {
    if (!plotData) return null

    const {
      sobol_first_order,
      sobol_first_order_ci,
      sobol_second_order,
      sobol_second_order_ci,
      sobol_total,
      sobol_total_ci
    } = plotData

    // Create data for bar chart visualization of sensitivity indices
    const barData = [
      {
        type: "bar",
        x: [
          `${param1} (First)`,
          `${param2} (First)`,
          `${param1}×${param2} (Interaction)`,
          `${param1} (Total)`,
          `${param2} (Total)`
        ],
        y: [
          sobol_first_order && sobol_first_order[param1] != null
            ? sobol_first_order[param1]
            : 0,
          sobol_first_order && sobol_first_order[param2] != null
            ? sobol_first_order[param2]
            : 0,
          sobol_second_order != null ? sobol_second_order : 0,
          sobol_total && sobol_total[param1] != null ? sobol_total[param1] : 0,
          sobol_total && sobol_total[param2] != null ? sobol_total[param2] : 0
        ],
        error_y: showConfidenceIntervals
          ? {
              type: "data",
              array: [
                sobol_first_order_ci &&
                sobol_first_order_ci[param1] &&
                sobol_first_order_ci[param1][0] != null &&
                sobol_first_order_ci[param1][1] != null
                  ? (sobol_first_order_ci[param1][1] -
                      sobol_first_order_ci[param1][0]) /
                    2
                  : 0,
                sobol_first_order_ci &&
                sobol_first_order_ci[param2] &&
                sobol_first_order_ci[param2][0] != null &&
                sobol_first_order_ci[param2][1] != null
                  ? (sobol_first_order_ci[param2][1] -
                      sobol_first_order_ci[param2][0]) /
                    2
                  : 0,
                sobol_second_order_ci &&
                sobol_second_order_ci[0] != null &&
                sobol_second_order_ci[1] != null
                  ? (sobol_second_order_ci[1] - sobol_second_order_ci[0]) / 2
                  : 0,
                sobol_total_ci &&
                sobol_total_ci[param1] &&
                sobol_total_ci[param1][0] != null &&
                sobol_total_ci[param1][1] != null
                  ? (sobol_total_ci[param1][1] - sobol_total_ci[param1][0]) / 2
                  : 0,
                sobol_total_ci &&
                sobol_total_ci[param2] &&
                sobol_total_ci[param2][0] != null &&
                sobol_total_ci[param2][1] != null
                  ? (sobol_total_ci[param2][1] - sobol_total_ci[param2][0]) / 2
                  : 0
              ],
              visible: true
            }
          : undefined,
        marker: {
          color: [
            "#1f77b4", // param1 first order
            "#ff7f0e", // param2 first order
            "#2ca02c", // interaction
            "#d62728", // param1 total
            "#9467bd" // param2 total
          ]
        }
      }
    ]

    return (
      <>
        <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">First-Order Sensitivity</CardTitle>
              <CardDescription>Direct effect of each parameter</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{param1}:</span>
                  <span className="text-sm">
                    {sobol_first_order && sobol_first_order[param1] != null
                      ? sobol_first_order[param1].toFixed(4)
                      : "N/A"}
                    {showConfidenceIntervals &&
                      sobol_first_order_ci &&
                      sobol_first_order_ci[param1] &&
                      sobol_first_order_ci[param1][0] !== null &&
                      sobol_first_order_ci[param1][1] !== null && (
                        <span className="text-muted-foreground ml-1 text-xs">
                          [
                          {sobol_first_order_ci[param1][0]?.toFixed(4) || "N/A"}
                          ,{" "}
                          {sobol_first_order_ci[param1][1]?.toFixed(4) || "N/A"}
                          ]
                        </span>
                      )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{param2}:</span>
                  <span className="text-sm">
                    {sobol_first_order && sobol_first_order[param2] != null
                      ? sobol_first_order[param2].toFixed(4)
                      : "N/A"}
                    {showConfidenceIntervals &&
                      sobol_first_order_ci &&
                      sobol_first_order_ci[param2] &&
                      sobol_first_order_ci[param2][0] !== null &&
                      sobol_first_order_ci[param2][1] !== null && (
                        <span className="text-muted-foreground ml-1 text-xs">
                          [
                          {sobol_first_order_ci[param2][0]?.toFixed(4) || "N/A"}
                          ,{" "}
                          {sobol_first_order_ci[param2][1]?.toFixed(4) || "N/A"}
                          ]
                        </span>
                      )}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Interaction Sensitivity</CardTitle>
              <CardDescription>
                Effect of parameter interactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">
                    {param1} × {param2}:
                  </span>
                  <span className="text-sm">
                    {sobol_second_order != null
                      ? sobol_second_order.toFixed(4)
                      : "N/A"}
                    {showConfidenceIntervals &&
                      sobol_second_order_ci &&
                      sobol_second_order_ci[0] !== null &&
                      sobol_second_order_ci[1] !== null && (
                        <span className="text-muted-foreground ml-1 text-xs">
                          [{sobol_second_order_ci[0]?.toFixed(4) || "N/A"},{" "}
                          {sobol_second_order_ci[1]?.toFixed(4) || "N/A"}]
                        </span>
                      )}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Total Sensitivity</CardTitle>
              <CardDescription>
                Total effect including interactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{param1}:</span>
                  <span className="text-sm">
                    {sobol_total && sobol_total[param1] != null
                      ? sobol_total[param1].toFixed(4)
                      : "N/A"}
                    {showConfidenceIntervals &&
                      sobol_total_ci &&
                      sobol_total_ci[param1] &&
                      sobol_total_ci[param1][0] !== null &&
                      sobol_total_ci[param1][1] !== null && (
                        <span className="text-muted-foreground ml-1 text-xs">
                          [{sobol_total_ci[param1][0]?.toFixed(4) || "N/A"},{" "}
                          {sobol_total_ci[param1][1]?.toFixed(4) || "N/A"}]
                        </span>
                      )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{param2}:</span>
                  <span className="text-sm">
                    {sobol_total && sobol_total[param2] != null
                      ? sobol_total[param2].toFixed(4)
                      : "N/A"}
                    {showConfidenceIntervals &&
                      sobol_total_ci &&
                      sobol_total_ci[param2] &&
                      sobol_total_ci[param2][0] !== null &&
                      sobol_total_ci[param2][1] !== null && (
                        <span className="text-muted-foreground ml-1 text-xs">
                          [{sobol_total_ci[param2][0]?.toFixed(4) || "N/A"},{" "}
                          {sobol_total_ci[param2][1]?.toFixed(4) || "N/A"}]
                        </span>
                      )}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Add a bar chart visualization of sensitivity indices */}
        <Card className="mt-4">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">
              Sensitivity Indices Visualization
            </CardTitle>
            <CardDescription>
              Visual comparison of all sensitivity indices
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <Plot
                data={barData}
                layout={{
                  title: "Sobol Sensitivity Indices",
                  yaxis: {
                    title: "Sensitivity Index",
                    // Add auto-ranging to ensure small values are visible
                    autorange: true
                  },
                  margin: { l: 50, r: 30, b: 80, t: 50 }
                }}
                config={{ responsive: true }}
                style={{ width: "100%", height: "100%" }}
              />
            </div>
          </CardContent>
        </Card>
      </>
    )
  }

  // Enhanced target selector component
  const renderEnhancedTargetSelector = () => {
    if (!isMultiTarget || availableTargets.length === 0) return null

    const handleTargetToggle = (targetName: string, checked: boolean) => {
      if (checked) {
        // Add target to comparison
        if (!selectedTargetsForComparison.includes(targetName)) {
          setSelectedTargetsForComparison(prev => [...prev, targetName])
        }
        // DON'T change the main plot target - let user control that separately
      } else {
        // Remove target from comparison
        setSelectedTargetsForComparison(prev =>
          prev.filter(t => t !== targetName)
        )
        // Remove from results
        setTargetResults(prev => {
          const newResults = { ...prev }
          delete newResults[targetName]
          return newResults
        })
      }
    }

    const handleSelectAll = () => {
      setSelectedTargetsForComparison([...availableTargets])
      availableTargets.forEach(target => {
        if (!targetResults[target]) {
          // Fetch data for targets that don't have results yet
          setSelectedTarget(target)
        }
      })
    }

    const handleClearAll = () => {
      setSelectedTargetsForComparison([])
      setTargetResults({})
    }

    return (
      <Card className="border-primary/20 mt-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="text-primary size-5" />
              <CardTitle className="text-lg">
                Target Comparison Selection
              </CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                disabled={
                  selectedTargetsForComparison.length ===
                  availableTargets.length
                }
              >
                <Check className="mr-1 size-3" />
                All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAll}
                disabled={selectedTargetsForComparison.length === 0}
              >
                <X className="mr-1 size-3" />
                Clear
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowTargetSelector(!showTargetSelector)}
              >
                {showTargetSelector ? (
                  <ChevronUp className="size-4" />
                ) : (
                  <ChevronDown className="size-4" />
                )}
              </Button>
            </div>
          </div>
          <CardDescription>
            Select multiple targets to compare their Sobol sensitivity indices.
            This won't affect the main contour plot above - use the "Target
            Variable (Main Plot)" dropdown to change that.
          </CardDescription>
        </CardHeader>
        {showTargetSelector && (
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
              {availableTargets.map(target => {
                const isSelected = selectedTargetsForComparison.includes(target)
                const hasResults = !!targetResults[target]

                return (
                  <div
                    key={target}
                    className={`rounded-lg border p-3 transition-all duration-200 ${
                      isSelected
                        ? "border-primary bg-primary/5 shadow-sm"
                        : "border-border hover:border-primary/50"
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        id={`target-${target}`}
                        checked={isSelected}
                        onCheckedChange={checked =>
                          handleTargetToggle(target, checked as boolean)
                        }
                        className="mt-0.5"
                      />
                      <div className="min-w-0 flex-1">
                        <label
                          htmlFor={`target-${target}`}
                          className="block cursor-pointer text-sm font-medium"
                        >
                          {target}
                        </label>
                        <div className="mt-1 flex items-center space-x-2">
                          {isSelected && (
                            <Badge variant="secondary" className="text-xs">
                              For Comparison
                            </Badge>
                          )}
                          {hasResults && (
                            <Badge variant="outline" className="text-xs">
                              <Check className="mr-1 size-2" />
                              Data Ready
                            </Badge>
                          )}
                          {selectedTarget === target && (
                            <Badge variant="default" className="text-xs">
                              Main Plot
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {selectedTargetsForComparison.length > 0 && (
              <div className="bg-muted/50 mt-4 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {selectedTargetsForComparison.length} target
                    {selectedTargetsForComparison.length !== 1 ? "s" : ""}{" "}
                    selected
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Enable compare mode automatically when targets are selected
                      if (!compareMode) {
                        setCompareMode(true)
                      }

                      // Fetch data for all selected targets that don't have results yet
                      selectedTargetsForComparison.forEach(target => {
                        if (!targetResults[target]) {
                          fetchComparisonTargetData(target)
                        }
                      })
                    }}
                    disabled={selectedTargetsForComparison.length < 2}
                  >
                    <Plus className="mr-1 size-3" />
                    Analyze Selected ({selectedTargetsForComparison.length})
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        )}
      </Card>
    )
  }

  const renderComparativeAnalysis = () => {
    if (
      Object.keys(targetResults).length === 0 ||
      selectedTargetsForComparison.length === 0
    )
      return null

    // Create data for bar chart comparison
    const barChartData = selectedTargetsForComparison
      .map(target => {
        const result = targetResults[target]
        if (!result) return null

        return {
          target,
          [`${param1} (First Order)`]:
            result.sobol_first_order && result.sobol_first_order[param1] != null
              ? result.sobol_first_order[param1]
              : 0,
          [`${param2} (First Order)`]:
            result.sobol_first_order && result.sobol_first_order[param2] != null
              ? result.sobol_first_order[param2]
              : 0,
          [`${param1} × ${param2} (Interaction)`]:
            result.sobol_second_order != null ? result.sobol_second_order : 0,
          [`${param1} (Total)`]:
            result.sobol_total && result.sobol_total[param1] != null
              ? result.sobol_total[param1]
              : 0,
          [`${param2} (Total)`]:
            result.sobol_total && result.sobol_total[param2] != null
              ? result.sobol_total[param2]
              : 0
        }
      })
      .filter(Boolean)

    // Prepare data for Plotly
    const plotData = [
      // First order for param1
      {
        type: "bar",
        name: `${param1} (First Order)`,
        x: barChartData.map(d => d?.target),
        y: barChartData.map(d => d?.[`${param1} (First Order)`] || 0),
        error_y: showConfidenceIntervals
          ? {
              type: "data",
              array: barChartData.map(d => {
                if (!d || !d.target) return 0
                const result = targetResults[d.target]
                if (
                  !result ||
                  !result.sobol_first_order_ci ||
                  !result.sobol_first_order_ci[param1] ||
                  result.sobol_first_order_ci[param1][0] == null ||
                  result.sobol_first_order_ci[param1][1] == null
                )
                  return 0
                return (
                  (result.sobol_first_order_ci[param1][1] -
                    result.sobol_first_order_ci[param1][0]) /
                  2
                )
              }),
              visible: true
            }
          : undefined
      },
      // First order for param2
      {
        type: "bar",
        name: `${param2} (First Order)`,
        x: barChartData.map(d => d?.target),
        y: barChartData.map(d => d?.[`${param2} (First Order)`] || 0),
        error_y: showConfidenceIntervals
          ? {
              type: "data",
              array: barChartData.map(d => {
                if (!d || !d.target) return 0
                const result = targetResults[d.target]
                if (
                  !result ||
                  !result.sobol_first_order_ci ||
                  !result.sobol_first_order_ci[param2] ||
                  result.sobol_first_order_ci[param2][0] == null ||
                  result.sobol_first_order_ci[param2][1] == null
                )
                  return 0
                return (
                  (result.sobol_first_order_ci[param2][1] -
                    result.sobol_first_order_ci[param2][0]) /
                  2
                )
              }),
              visible: true
            }
          : undefined
      },
      // Interaction
      {
        type: "bar",
        name: `${param1} × ${param2} (Interaction)`,
        x: barChartData.map(d => d?.target),
        y: barChartData.map(
          d => d?.[`${param1} × ${param2} (Interaction)`] || 0
        ),
        error_y: showConfidenceIntervals
          ? {
              type: "data",
              array: barChartData.map(d => {
                if (!d || !d.target) return 0
                const result = targetResults[d.target]
                if (
                  !result ||
                  !result.sobol_second_order_ci ||
                  result.sobol_second_order_ci[0] == null ||
                  result.sobol_second_order_ci[1] == null
                )
                  return 0
                return (
                  (result.sobol_second_order_ci[1] -
                    result.sobol_second_order_ci[0]) /
                  2
                )
              }),
              visible: true
            }
          : undefined
      }
    ]

    return (
      <div className="mt-8">
        <div className="mb-4 flex items-center space-x-2">
          <div className="bg-border h-px flex-1"></div>
          <Badge variant="outline" className="px-3 py-1">
            Target Comparison Results
          </Badge>
          <div className="bg-border h-px flex-1"></div>
        </div>

        <Card className="border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="text-primary mr-2 size-5" />
              Comparative Sobol Analysis
            </CardTitle>
            <CardDescription>
              Sensitivity indices comparison across{" "}
              {selectedTargetsForComparison.length} selected targets:{" "}
              {selectedTargetsForComparison.join(", ")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[400px]">
              <Plot
                data={plotData}
                layout={{
                  barmode: "group",
                  title: "Sobol Indices Comparison Across Targets",
                  xaxis: { title: "Target" },
                  yaxis: { title: "Sensitivity Index" },
                  legend: { orientation: "h", y: -0.2 }
                }}
                config={{ responsive: true }}
                style={{ width: "100%", height: "100%" }}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="space-y-1">
          <CardTitle className="flex items-center text-lg">
            <Grid3X3 className="text-primary mr-2 size-5" />
            SOBOL Contour Analysis
            {useExperimentRange && (
              <Badge variant="outline" className="ml-2 text-xs">
                Experiments{" "}
                {experimentStartIdx !== undefined ? experimentStartIdx : 0} -{" "}
                {experimentEndIdx !== undefined
                  ? experimentEndIdx
                  : totalExperiments}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Visualize parameter interactions using Sobol' sensitivity analysis
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            disabled={!plotData}
          >
            <Download className="mr-2 size-4" />
            Export
          </Button>
          <Button
            variant={isRangeChanged ? "default" : "outline"}
            size="sm"
            onClick={handleRefresh}
            disabled={
              loading ||
              !param1 ||
              !param2 ||
              param1 === param2 ||
              !selectedTarget
            }
          >
            {loading ? (
              <RefreshCw className="mr-2 size-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 size-4" />
            )}
            {isRangeChanged ? "Apply Range" : "Refresh"}
            {isRangeChanged && (
              <Badge variant="outline" className="bg-primary/20 ml-2">
                !
              </Badge>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3">
          {/* Parameter selection */}
          <div className="space-y-2">
            <Label htmlFor="param1">X-Axis Parameter</Label>
            <Select value={param1} onValueChange={setParam1}>
              <SelectTrigger id="param1">
                <SelectValue placeholder="Select parameter" />
              </SelectTrigger>
              <SelectContent>
                {parameterNames.map(name => (
                  <SelectItem key={`p1-${name}`} value={name}>
                    {name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="param2">Y-Axis Parameter</Label>
            <Select value={param2} onValueChange={setParam2}>
              <SelectTrigger id="param2">
                <SelectValue placeholder="Select parameter" />
              </SelectTrigger>
              <SelectContent>
                {parameterNames.map(name => (
                  <SelectItem key={`p2-${name}`} value={name}>
                    {name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Target selection for main plot */}
          <div className="space-y-2">
            <Label>Target Variable (Main Plot)</Label>
            {isMultiTarget ? (
              <Select value={selectedTarget} onValueChange={setSelectedTarget}>
                <SelectTrigger>
                  <SelectValue placeholder="Select target for main plot" />
                </SelectTrigger>
                <SelectContent>
                  {availableTargets.map(name => (
                    <SelectItem key={`main-target-${name}`} value={name}>
                      {name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <div className="text-muted-foreground rounded-md border px-3 py-2 text-sm">
                {optimization.targetName}
              </div>
            )}
          </div>
        </div>

        <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3">
          {/* Plot configuration */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="resolution">Resolution</Label>
              <span className="text-muted-foreground text-xs">
                {resolution}×{resolution}
              </span>
            </div>
            <Slider
              id="resolution"
              min={20}
              max={100}
              step={10}
              value={[resolution]}
              onValueChange={values => setResolution(values[0])}
            />
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="smoothing"
                checked={smoothing}
                onCheckedChange={setSmoothing}
              />
              <Label htmlFor="smoothing">Smoothing</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="confidence-intervals"
                checked={showConfidenceIntervals}
                onCheckedChange={setShowConfidenceIntervals}
              />
              <Label htmlFor="confidence-intervals">Confidence Intervals</Label>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="experiment-range"
                checked={useExperimentRange}
                disabled={totalExperiments < 5}
                onCheckedChange={checked => {
                  setUseExperimentRange(checked)
                  if (!checked) {
                    // Reset experiment range when turning off the toggle
                    setExperimentStartIdx(undefined)
                    setExperimentEndIdx(undefined)
                    // Trigger a refresh to get all experiments
                    fetchContourData()
                  }
                }}
              />
              <Label
                htmlFor="experiment-range"
                className="flex items-center gap-2 text-sm"
              >
                Filter by Experiment Range
                {totalExperiments < 5 && (
                  <span className="text-xs font-medium text-amber-600 dark:text-amber-400">
                    (⚠️ {totalExperiments} experiments - full dataset
                    recommended)
                  </span>
                )}
              </Label>
            </div>

            {useExperimentRange && (
              <div className="flex items-center space-x-3">
                <div className="space-y-1">
                  <Label
                    htmlFor="start-idx"
                    className="text-muted-foreground text-xs"
                  >
                    Start
                  </Label>
                  <Select
                    value={
                      experimentStartIdx !== undefined
                        ? experimentStartIdx.toString()
                        : "0"
                    }
                    onValueChange={value => {
                      const newStart = parseInt(value)
                      setExperimentStartIdx(newStart)
                      // Ensure end index is greater than or equal to start index
                      if (
                        experimentEndIdx !== undefined &&
                        newStart > experimentEndIdx
                      ) {
                        setExperimentEndIdx(newStart)
                      }
                    }}
                  >
                    <SelectTrigger id="start-idx" className="h-8 w-20 text-sm">
                      <SelectValue placeholder="0" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from(
                        {
                          length: totalExperiments > 0 ? totalExperiments : 100
                        },
                        (_, i) => (
                          <SelectItem
                            key={i}
                            value={i.toString()}
                            className="text-sm"
                          >
                            {i}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-1">
                  <Label
                    htmlFor="end-idx"
                    className="text-muted-foreground text-xs"
                  >
                    End
                  </Label>
                  <Select
                    value={
                      experimentEndIdx !== undefined
                        ? experimentEndIdx.toString()
                        : totalExperiments.toString()
                    }
                    onValueChange={value => {
                      setExperimentEndIdx(parseInt(value))
                    }}
                  >
                    <SelectTrigger id="end-idx" className="h-8 w-20 text-sm">
                      <SelectValue placeholder="Max" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from(
                        {
                          length:
                            totalExperiments > 0
                              ? totalExperiments -
                                (experimentStartIdx !== undefined
                                  ? experimentStartIdx
                                  : 0)
                              : 100
                        },
                        (_, i) => {
                          const value =
                            (experimentStartIdx !== undefined
                              ? experimentStartIdx
                              : 0) + i
                          return (
                            <SelectItem
                              key={value}
                              value={value.toString()}
                              className="text-sm"
                            >
                              {value}
                            </SelectItem>
                          )
                        }
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Error Display */}
        {renderEnhancedErrorAlert()}

        {loading ? (
          <div className="space-y-4">
            <Skeleton className="h-[500px] w-full" />
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <Skeleton className="h-[120px] w-full" />
              <Skeleton className="h-[120px] w-full" />
              <Skeleton className="h-[120px] w-full" />
            </div>
          </div>
        ) : (
          <>
            {plotData ? (
              <>
                {/* 1. Fixed Parameters Alert */}
                {renderFixedParamsAlert()}

                {/* 2. Main Contour Plot */}
                {renderPlot()}

                {/* 3. Sensitivity Indices Visualization */}
                {renderSensitivityInfo()}

                {/* 4. Enhanced Target Selector (only for multi-target) */}
                {isMultiTarget && renderEnhancedTargetSelector()}

                {/* 5. Comparative Analysis (when multiple targets selected) */}
                {compareMode &&
                  selectedTargetsForComparison.length >= 2 &&
                  renderComparativeAnalysis()}
              </>
            ) : (
              <div className="flex h-[500px] w-full flex-col items-center justify-center rounded-md border p-4 text-center">
                <Grid3X3 className="text-muted-foreground mb-4 size-12" />
                <h3 className="text-lg font-medium">
                  {param1 && param2 && param1 === param2
                    ? "Please select two different parameters"
                    : !param1 || !param2
                      ? "Select parameters to generate contour plot"
                      : !selectedTarget
                        ? "Select a target variable"
                        : "Ready to generate contour plot"}
                </h3>
                <p className="text-muted-foreground mt-2 max-w-md text-sm">
                  {param1 && param2 && param1 !== param2 && selectedTarget
                    ? "Click the Refresh button to generate the Sobol' contour plot."
                    : "Select the parameters and target variable to visualize their relationships and sensitivity."}
                </p>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
