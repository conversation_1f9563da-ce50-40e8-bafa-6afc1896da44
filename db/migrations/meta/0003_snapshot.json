{"id": "02c17777-aa67-41d3-8c85-805edbde048f", "prevId": "b7b2b6d4-cf4b-4b19-a5b8-fb73b1284b34", "version": "7", "dialect": "postgresql", "tables": {"public.profiles": {"name": "profiles", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": true, "notNull": true}, "membership": {"name": "membership", "type": "membership", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'free'"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "trial_started_at": {"name": "trial_started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "trial_ends_at": {"name": "trial_ends_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "has_trial_expired": {"name": "has_trial_expired", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.measurements": {"name": "measurements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "optimization_id": {"name": "optimization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parameters": {"name": "parameters", "type": "jsonb", "primaryKey": false, "notNull": true}, "target_value": {"name": "target_value", "type": "text", "primaryKey": false, "notNull": true}, "target_values": {"name": "target_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_recommended": {"name": "is_recommended", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "batch_id": {"name": "batch_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"measurements_optimization_id_optimizations_id_fk": {"name": "measurements_optimization_id_optimizations_id_fk", "tableFrom": "measurements", "tableTo": "optimizations", "columnsFrom": ["optimization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.optimizations": {"name": "optimizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "optimizer_id": {"name": "optimizer_id", "type": "text", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": true}, "target_name": {"name": "target_name", "type": "text", "primaryKey": false, "notNull": true}, "target_mode": {"name": "target_mode", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"optimizations_optimizer_id_unique": {"name": "optimizations_optimizer_id_unique", "nullsNotDistinct": false, "columns": ["optimizer_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.suggestions": {"name": "suggestions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "optimization_id": {"name": "optimization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parameters": {"name": "parameters", "type": "jsonb", "primaryKey": false, "notNull": true}, "batch_id": {"name": "batch_id", "type": "text", "primaryKey": false, "notNull": true}, "suggestion_index": {"name": "suggestion_index", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "target_values": {"name": "target_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"suggestions_optimization_id_optimizations_id_fk": {"name": "suggestions_optimization_id_optimizations_id_fk", "tableFrom": "suggestions", "tableTo": "optimizations", "columnsFrom": ["optimization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.samples": {"name": "samples", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "optimization_id": {"name": "optimization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parameters": {"name": "parameters", "type": "jsonb", "primaryKey": false, "notNull": true}, "batch_id": {"name": "batch_id", "type": "text", "primaryKey": false, "notNull": true}, "sample_index": {"name": "sample_index", "type": "integer", "primaryKey": false, "notNull": true}, "sampling_method": {"name": "sampling_method", "type": "text", "primaryKey": false, "notNull": true, "default": "'LHS'"}, "seed": {"name": "seed", "type": "integer", "primaryKey": false, "notNull": false}, "sample_class": {"name": "sample_class", "type": "text", "primaryKey": false, "notNull": true, "default": "'exploratory'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "target_values": {"name": "target_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"samples_optimization_id_optimizations_id_fk": {"name": "samples_optimization_id_optimizations_id_fk", "tableFrom": "samples", "tableTo": "optimizations", "columnsFrom": ["optimization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.optimization_status_history": {"name": "optimization_status_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "optimization_id": {"name": "optimization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "previous_status": {"name": "previous_status", "type": "text", "primaryKey": false, "notNull": true}, "new_status": {"name": "new_status", "type": "text", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"optimization_status_history_optimization_id_optimizations_id_fk": {"name": "optimization_status_history_optimization_id_optimizations_id_fk", "tableFrom": "optimization_status_history", "tableTo": "optimizations", "columnsFrom": ["optimization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.feedback": {"name": "feedback", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'new'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.membership": {"name": "membership", "schema": "public", "values": ["free", "pro"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}