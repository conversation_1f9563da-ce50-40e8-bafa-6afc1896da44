#!/usr/bin/env node

/**
 * Check if the newly created optimizations exist in the database
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, like } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function checkNewOptimizations() {
  console.log('🔍 CHECKING NEW OPTIMIZATIONS IN DATABASE');
  console.log('=========================================');
  
  let client;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    // Look for optimizations created by our test script
    const testOptimizations = await db.select().from(optimizationsTable)
      .where(like(optimizationsTable.optimizerId, '%test_multi_target_config_%'))
      .orderBy(optimizationsTable.createdAt);
    
    console.log(`📋 Found ${testOptimizations.length} test optimizations in database`);
    
    if (testOptimizations.length === 0) {
      console.log('❌ No test optimizations found in database');
      console.log('   This explains why database_updated: false');
      console.log('   The optimizations were created in the backend but not saved to the database');
      return;
    }
    
    testOptimizations.forEach((opt, index) => {
      console.log(`\n📋 Test Optimization ${index + 1}:`);
      console.log(`   ID: ${opt.optimizerId}`);
      console.log(`   Name: ${opt.name}`);
      console.log(`   Status: ${opt.status}`);
      console.log(`   Created: ${opt.createdAt}`);
      
      if (opt.config) {
        console.log(`   Objective Type: ${opt.config.objective_type}`);
        console.log(`   Acquisition Function: ${opt.config.acquisition_config?.type}`);
        
        if (opt.config.target_config) {
          if (Array.isArray(opt.config.target_config)) {
            console.log(`   Targets: ${opt.config.target_config.length}`);
            opt.config.target_config.forEach((target, targetIndex) => {
              console.log(`     ${targetIndex + 1}. ${target.name}: ${target.mode} [${target.bounds?.join(', ') || 'no bounds'}]`);
            });
          } else {
            console.log(`   Target: ${opt.config.target_config.name} (${opt.config.target_config.mode})`);
          }
        }
      } else {
        console.log('   ❌ No configuration in database');
      }
    });
    
    // Test if we can update one of these optimizations
    if (testOptimizations.length > 0) {
      const testOpt = testOptimizations[0];
      console.log(`\n🔧 TESTING DATABASE UPDATE FOR: ${testOpt.optimizerId.slice(-20)}`);
      console.log('='.repeat(60));
      
      try {
        const updateResponse = await fetch(`http://localhost:3000/api/optimizations/${testOpt.optimizerId}/bounds`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            parameter_bounds: {
              "x1": [0, 90]
            },
            preview_only: false
          })
        });
        
        console.log(`📥 Update response status: ${updateResponse.status} ${updateResponse.statusText}`);
        
        if (updateResponse.ok) {
          const updateResult = await updateResponse.json();
          console.log(`✅ Update successful: ${updateResult.status}`);
          console.log(`📊 Database updated: ${updateResult.database_updated}`);
          console.log(`📊 Updated config provided: ${!!updateResult.updated_config}`);
          
          if (updateResult.database_error) {
            console.log(`❌ Database error: ${updateResult.database_error}`);
          }
        } else {
          const errorText = await updateResponse.text();
          console.log(`❌ Update failed: ${errorText}`);
        }
        
      } catch (error) {
        console.log(`❌ Error testing update: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error during database check:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

// Run the check
checkNewOptimizations().catch(console.error);
