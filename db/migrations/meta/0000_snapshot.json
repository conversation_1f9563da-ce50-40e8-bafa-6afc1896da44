{"id": "6dfbd8d5-2e10-4999-8d39-6f9a7e4804c6", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.profiles": {"name": "profiles", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": true, "notNull": true}, "membership": {"name": "membership", "type": "membership", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'free'"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.measurements": {"name": "measurements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "optimization_id": {"name": "optimization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parameters": {"name": "parameters", "type": "jsonb", "primaryKey": false, "notNull": true}, "target_value": {"name": "target_value", "type": "text", "primaryKey": false, "notNull": true}, "is_recommended": {"name": "is_recommended", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"measurements_optimization_id_optimizations_id_fk": {"name": "measurements_optimization_id_optimizations_id_fk", "tableFrom": "measurements", "tableTo": "optimizations", "columnsFrom": ["optimization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.optimizations": {"name": "optimizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "optimizer_id": {"name": "optimizer_id", "type": "text", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": true}, "target_name": {"name": "target_name", "type": "text", "primaryKey": false, "notNull": true}, "target_mode": {"name": "target_mode", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"optimizations_optimizer_id_unique": {"name": "optimizations_optimizer_id_unique", "nullsNotDistinct": false, "columns": ["optimizer_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.membership": {"name": "membership", "schema": "public", "values": ["free", "pro"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}