"use server"

import { QuickStartWizardTest } from "@/components/dashboard/quick-start-wizard-test"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"
import { BookO<PERSON>, Sparkles } from "lucide-react"
import { DashboardThemeWrapper } from "@/components/dashboard/dashboard-theme-wrapper"

export default async function LearningResourcesPage() {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult
  const userId = await getActualUserId()

  if (!userId) {
    redirect("/dashboard")
  }

  return (
    <DashboardThemeWrapper>
      {/* Enhanced Header */}
      <div className="flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between">
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="flex size-14 items-center justify-center rounded-2xl bg-white shadow-lg">
                <BookOpen className="size-7" style={{ color: '#2596be' }} />
              </div>
              <div className="border-3 absolute -bottom-1 -right-1 flex size-6 items-center justify-center rounded-full border-white bg-white shadow-sm">
                <Sparkles className="size-3" style={{ color: '#2596be' }} />
              </div>
            </div>
            <div>
              <h1 className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-4xl font-bold text-transparent">
                Learning Resources
              </h1>
              <p className="mt-1 text-lg font-medium text-gray-600">
                Master optimization with our interactive guides and tutorials
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Full-Width QuickStart Wizard */}
      <div className="w-full">
        <QuickStartWizardTest userId={userId} />
      </div>
    </DashboardThemeWrapper>
  )
}