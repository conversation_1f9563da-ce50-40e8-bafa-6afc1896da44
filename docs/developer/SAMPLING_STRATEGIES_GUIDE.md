# Sampling Strategies Guide

## Overview

BOapp6 now supports three different initial sampling strategies for generating your first set of experiments. This guide helps you choose the best strategy for your optimization problem.

## Available Strategies

### 🎯 Sobol Sequences (Recommended)
**Best choice for most optimization problems**

Sobol sequences are advanced quasi-random sequences that provide superior space coverage compared to traditional methods.

**When to use:**
- ✅ High-dimensional problems (4+ parameters)
- ✅ When you want reproducible results
- ✅ Limited sample budget (need maximum information from fewer samples)
- ✅ Complex optimization landscapes

**Advantages:**
- 🔹 **Superior space-filling**: More uniform coverage of parameter space
- 🔹 **Deterministic**: Same seed always produces identical results
- 🔹 **Scalable**: Excellent performance in high dimensions
- 🔹 **Efficient**: Better convergence with fewer samples

**Tips:**
- Use power-of-2 sample sizes (8, 16, 32, 64) for optimal performance
- Set a seed for reproducible experiments
- Ideal for 4+ parameters

### 📊 Latin Hypercube Sampling (LHS)
**Traditional and reliable choice**

LHS ensures good coverage of each parameter's range by dividing each dimension into equal intervals.

**When to use:**
- ✅ 2-6 parameter problems
- ✅ When you need guaranteed coverage of each parameter range
- ✅ Traditional Design of Experiments (DOE) workflows
- ✅ When following established protocols

**Advantages:**
- 🔹 **Proven method**: Well-established in optimization literature
- 🔹 **Good marginal coverage**: Ensures each parameter is well-sampled
- 🔹 **Balanced**: Good compromise between exploration and efficiency

**Tips:**
- Works well with any sample size
- Good default choice for medium-dimensional problems
- Widely understood and accepted method

### 🎲 Random Sampling
**Simple and unbiased**

Completely random points within the parameter space.

**When to use:**
- ✅ Quick prototyping and testing
- ✅ Very simple problems (1-2 parameters)
- ✅ When other methods are not available
- ✅ Baseline comparisons

**Advantages:**
- 🔹 **Simple**: No complex algorithms
- 🔹 **Fast**: Quickest to generate
- 🔹 **Unbiased**: No systematic patterns

**Disadvantages:**
- 🔸 **Poor coverage**: May miss important regions
- 🔸 **Inefficient**: Requires more samples for good coverage
- 🔸 **Variable quality**: Results can vary significantly

## Quick Selection Guide

| Your Situation | Recommended Strategy | Sample Size |
|---------------|---------------------|-------------|
| 2-3 parameters, traditional approach | **LHS** | 8-16 |
| 2-3 parameters, best performance | **Sobol** | 8-16 |
| 4-6 parameters | **Sobol** | 16-32 |
| 7+ parameters | **Sobol** | 32-64 |
| Quick testing | **Random** | Any |
| Reproducible research | **Sobol** with seed | Power of 2 |

## Sample Size Recommendations

### For Sobol Sequences
- **Optimal**: Use power-of-2 sizes (8, 16, 32, 64, 128)
- **Minimum**: 2^(dimensions) for basic coverage
- **Recommended**: 4-8 times the number of parameters

### For LHS and Random
- **Minimum**: 2 × number of parameters
- **Recommended**: 5-10 times the number of parameters
- **Maximum**: Limited by computational budget

## Practical Examples

### Example 1: Chemical Reaction Optimization
**Problem**: 5 parameters (temperature, pressure, concentration, time, catalyst)
**Recommendation**: Sobol sequences with 32 samples
**Reasoning**: High-dimensional, need efficient exploration

### Example 2: Simple A/B Testing
**Problem**: 2 parameters (price, discount)
**Recommendation**: LHS with 8-12 samples
**Reasoning**: Low-dimensional, traditional approach works well

### Example 3: Machine Learning Hyperparameters
**Problem**: 8 parameters (learning rate, batch size, layers, etc.)
**Recommendation**: Sobol sequences with 64 samples
**Reasoning**: High-dimensional, need systematic exploration

## Advanced Tips

### Reproducibility
- Always set a **seed** for reproducible results
- Document the seed value in your experiment notes
- Use the same seed for comparison studies

### Sample Size Strategy
1. **Start small**: Begin with 8-16 samples for initial exploration
2. **Analyze results**: Look at parameter coverage and initial trends
3. **Scale up**: Add more samples if needed (powers of 2 for Sobol)

### Quality Assessment
Monitor these indicators for good sampling:
- **Parameter coverage**: Each parameter should span most of its range
- **Distribution**: Points should be well-distributed, not clustered
- **Reproducibility**: Same settings should give identical samples

## Common Mistakes to Avoid

❌ **Using too few samples**: Less than 2× parameters often insufficient
❌ **Ignoring dimensionality**: Random sampling in high dimensions is inefficient
❌ **Not setting seeds**: Makes experiments non-reproducible
❌ **Wrong sample sizes**: Non-power-of-2 for Sobol reduces optimality
❌ **One-size-fits-all**: Different problems need different strategies

## Getting Started

1. **Choose your strategy** based on the guide above
2. **Set sample size** following the recommendations
3. **Set a seed** for reproducibility (optional but recommended)
4. **Generate samples** and review the distribution
5. **Run experiments** and analyze results
6. **Iterate** with more samples if needed

## Need Help?

If you're unsure which strategy to use:
1. **Default choice**: Start with Sobol sequences
2. **Sample size**: Use 4-8 times your parameter count
3. **Power of 2**: Round up to nearest power of 2 for Sobol
4. **Set seed**: Use any number (42 is popular!)

The system will automatically fall back to simpler methods if advanced options aren't available, so you can always start with the recommended approach.
