#!/usr/bin/env node

/**
 * This script resets the Railway database by dropping all tables and recreating them
 * Run with: node scripts/reset-railway-db.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create a temporary .env file with modified DATABASE_URL
function createTempEnvFile() {
  console.log('Creating temporary .env file with modified DATABASE_URL...');
  
  // Read the current .env.local file
  const envPath = path.join(process.cwd(), '.env.local');
  let envContent = '';
  
  try {
    envContent = fs.readFileSync(envPath, 'utf8');
  } catch (error) {
    console.error('Error reading .env.local file:', error.message);
    process.exit(1);
  }
  
  // Modify the DATABASE_URL to disable SSL verification
  const modifiedContent = envContent.replace(
    /DATABASE_URL=(.+)sslmode=require/g, 
    'DATABASE_URL=$1sslmode=no-verify'
  );
  
  // Write to a temporary file
  const tempEnvPath = path.join(process.cwd(), '.env.temp');
  fs.writeFileSync(tempEnvPath, modifiedContent);
  
  console.log('Temporary .env file created successfully');
  return tempEnvPath;
}

// Clean up the temporary file
function cleanupTempFile(tempEnvPath) {
  console.log('Cleaning up temporary files...');
  try {
    fs.unlinkSync(tempEnvPath);
    console.log('Temporary .env file removed successfully');
  } catch (error) {
    console.error('Error removing temporary .env file:', error.message);
  }
}

// Main function to reset the database
async function resetDatabase() {
  console.log('Starting database reset process...');
  
  // Create temporary .env file
  const tempEnvPath = createTempEnvFile();
  
  try {
    // Run Drizzle commands with the temporary .env file
    console.log('\nDropping all tables...');
    execSync(`DATABASE_URL=$(grep DATABASE_URL ${tempEnvPath} | cut -d '=' -f2-) npx drizzle-kit drop --yes`, {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'development'
      }
    });
    
    console.log('\nPushing schema to database...');
    execSync(`DATABASE_URL=$(grep DATABASE_URL ${tempEnvPath} | cut -d '=' -f2-) npx drizzle-kit push`, {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'development'
      }
    });
    
    console.log('\n✅ Database reset completed successfully!');
  } catch (error) {
    console.error('\n❌ Error resetting database:', error.message);
  } finally {
    // Clean up
    cleanupTempFile(tempEnvPath);
  }
}

// Confirm before proceeding
console.log('\n⚠️  WARNING: This will delete ALL data in the database ⚠️');
console.log('Press Ctrl+C to cancel or wait 5 seconds to continue...');

setTimeout(() => {
  console.log('\nProceeding with database reset...');
  resetDatabase();
}, 5000);
