"use client"

import { useState, useEffect, useCallback } from "react"
import { versionManager } from "./manager"
import {
  ConfigurationVersion,
  VersionConflict,
  ConflictResolution,
  VersionDiff,
  VersionLock,
  ConfigurationChange
} from "./types"

export interface UseVersioningOptions {
  optimizationId: string
  userId: string
  userName: string
  autoSave?: boolean
  autoSaveInterval?: number
  conflictDetection?: boolean
}

export function useVersioning(options: UseVersioningOptions) {
  const [versions, setVersions] = useState<ConfigurationVersion[]>([])
  const [activeVersion, setActiveVersion] =
    useState<ConfigurationVersion | null>(null)
  const [conflicts, setConflicts] = useState<VersionConflict[]>([])
  const [currentLock, setCurrentLock] = useState<VersionLock | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  /**
   * Load versions for the optimization
   */
  const loadVersions = useCallback(async () => {
    setIsLoading(true)
    try {
      const loadedVersions = versionManager.getVersions(options.optimizationId)
      setVersions(loadedVersions)

      const active = versionManager.getActiveVersion(options.optimizationId)
      setActiveVersion(active)

      if (options.conflictDetection) {
        const loadedConflicts = versionManager.getConflicts(
          options.optimizationId
        )
        setConflicts(loadedConflicts)
      }
    } catch (error) {
      console.error("Failed to load versions:", error)
    } finally {
      setIsLoading(false)
    }
  }, [options.optimizationId, options.conflictDetection])

  /**
   * Create a new version
   */
  const createVersion = useCallback(
    async (
      configuration: any,
      changes: ConfigurationChange[],
      changeSummary: string
    ): Promise<ConfigurationVersion> => {
      try {
        const newVersion = await versionManager.createVersion(
          options.optimizationId,
          configuration,
          changes,
          {
            userId: options.userId,
            userName: options.userName
          },
          changeSummary,
          activeVersion?.version
        )

        // Refresh versions
        await loadVersions()
        setLastSaved(new Date())

        return newVersion
      } catch (error) {
        console.error("Failed to create version:", error)
        throw error
      }
    },
    [
      options.optimizationId,
      options.userId,
      options.userName,
      activeVersion,
      loadVersions
    ]
  )

  /**
   * Rollback to a specific version
   */
  const rollbackToVersion = useCallback(
    async (targetVersion: number): Promise<ConfigurationVersion> => {
      try {
        const rollbackVersion = await versionManager.rollbackToVersion(
          options.optimizationId,
          targetVersion,
          {
            userId: options.userId,
            userName: options.userName
          }
        )

        await loadVersions()
        return rollbackVersion
      } catch (error) {
        console.error("Failed to rollback to version:", error)
        throw error
      }
    },
    [options.optimizationId, options.userId, options.userName, loadVersions]
  )

  /**
   * Compare two versions
   */
  const compareVersions = useCallback(
    (fromVersion: number, toVersion: number): VersionDiff | null => {
      return versionManager.compareVersions(
        options.optimizationId,
        fromVersion,
        toVersion
      )
    },
    [options.optimizationId]
  )

  /**
   * Lock the current version for editing
   */
  const lockVersion = useCallback(
    async (
      version: number,
      lockReason: string = "Editing configuration",
      durationMinutes: number = 30
    ): Promise<VersionLock> => {
      try {
        const lock = await versionManager.lockVersion(
          options.optimizationId,
          version,
          {
            userId: options.userId,
            userName: options.userName,
            sessionId: "current_session", // Would get from session context
            lockReason,
            lockType: "write",
            durationMinutes
          }
        )

        setCurrentLock(lock)
        return lock
      } catch (error) {
        console.error("Failed to lock version:", error)
        throw error
      }
    },
    [options.optimizationId, options.userId, options.userName]
  )

  /**
   * Release the current lock
   */
  const releaseLock = useCallback(async () => {
    if (currentLock) {
      try {
        await versionManager.unlockVersion(currentLock.id)
        setCurrentLock(null)
      } catch (error) {
        console.error("Failed to release lock:", error)
        throw error
      }
    }
  }, [currentLock])

  /**
   * Check if a version is locked
   */
  const checkVersionLock = useCallback(
    (version: number): VersionLock | null => {
      return versionManager.isVersionLocked(options.optimizationId, version)
    },
    [options.optimizationId]
  )

  /**
   * Resolve a conflict
   */
  const resolveConflict = useCallback(
    async (
      conflictId: string,
      resolutionStrategy:
        | "accept_mine"
        | "accept_theirs"
        | "merge_changes"
        | "custom",
      resolutionDetails: Array<{
        field: string
        chosenValue: any
        chosenVersion: number
        reason: string
      }>,
      notes: string = ""
    ): Promise<void> => {
      try {
        const resolution: ConflictResolution = {
          id: `resolution_${Date.now()}`,
          conflictId,
          resolvedBy: {
            userId: options.userId,
            userName: options.userName
          },
          resolvedAt: new Date(),
          resolutionStrategy,
          resolutionDetails,
          notes
        }

        await versionManager.resolveConflict(conflictId, resolution)
        await loadVersions() // Refresh to get updated conflict status
      } catch (error) {
        console.error("Failed to resolve conflict:", error)
        throw error
      }
    },
    [options.userId, options.userName, loadVersions]
  )

  /**
   * Get version history with pagination
   */
  const getVersionHistory = useCallback(
    (limit: number = 20, offset: number = 0): ConfigurationVersion[] => {
      return versionManager.queryVersions({
        optimizationId: options.optimizationId,
        limit,
        offset,
        sortBy: "timestamp",
        sortOrder: "desc"
      })
    },
    [options.optimizationId]
  )

  /**
   * Get pending conflicts
   */
  const getPendingConflicts = useCallback((): VersionConflict[] => {
    return conflicts.filter(c => c.status === "pending")
  }, [conflicts])

  /**
   * Check if there are unsaved changes
   */
  const hasUnsavedChanges = useCallback(
    (currentConfig: any): boolean => {
      if (!activeVersion) return true

      return (
        JSON.stringify(activeVersion.configuration) !==
        JSON.stringify(currentConfig)
      )
    },
    [activeVersion]
  )

  /**
   * Auto-save functionality
   */
  const autoSave = useCallback(
    async (
      configuration: any,
      changes: ConfigurationChange[]
    ): Promise<void> => {
      if (!options.autoSave) return

      try {
        await createVersion(configuration, changes, "Auto-save")
      } catch (error) {
        console.warn("Auto-save failed:", error)
      }
    },
    [options.autoSave, createVersion]
  )

  /**
   * Get version statistics
   */
  const getVersionStatistics = useCallback(() => {
    const totalVersions = versions.length
    const authors = new Set(versions.map(v => v.author.userId))
    const totalAuthors = authors.size

    const changeTypes = versions.reduce(
      (acc, version) => {
        acc[version.changeType] = (acc[version.changeType] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const pendingConflicts = conflicts.filter(
      c => c.status === "pending"
    ).length
    const totalConflicts = conflicts.length
    const conflictRate =
      totalConflicts > 0 ? (pendingConflicts / totalConflicts) * 100 : 0

    return {
      totalVersions,
      totalAuthors,
      changeTypes,
      conflictRate,
      lastModified: versions.length > 0 ? versions[0].timestamp : null,
      mostActiveAuthor:
        versions.length > 0
          ? versions.reduce(
              (acc, v) => {
                acc[v.author.userName] = (acc[v.author.userName] || 0) + 1
                return acc
              },
              {} as Record<string, number>
            )
          : {}
    }
  }, [versions, conflicts])

  /**
   * Export version history
   */
  const exportVersionHistory = useCallback(() => {
    const history = {
      optimizationId: options.optimizationId,
      exportedAt: new Date(),
      versions: versions.map(v => ({
        version: v.version,
        timestamp: v.timestamp,
        author: v.author.userName,
        changeSummary: v.changeSummary,
        changeCount: v.changeDetails.length
      })),
      conflicts: conflicts.map(c => ({
        id: c.id,
        type: c.conflictType,
        status: c.status,
        detectedAt: c.detectedAt,
        conflictingVersions: c.conflictingVersions
      }))
    }

    const blob = new Blob([JSON.stringify(history, null, 2)], {
      type: "application/json"
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `version-history-${options.optimizationId}-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [options.optimizationId, versions, conflicts])

  // Load versions on mount
  useEffect(() => {
    loadVersions()
  }, [loadVersions])

  // Auto-save interval
  useEffect(() => {
    if (!options.autoSave || !options.autoSaveInterval) return

    const interval = setInterval(() => {
      // Auto-save logic would go here
      // This would need to be triggered by configuration changes
    }, options.autoSaveInterval)

    return () => clearInterval(interval)
  }, [options.autoSave, options.autoSaveInterval])

  // Cleanup lock on unmount
  useEffect(() => {
    return () => {
      if (currentLock) {
        versionManager.unlockVersion(currentLock.id).catch(console.error)
      }
    }
  }, [currentLock])

  return {
    // State
    versions,
    activeVersion,
    conflicts,
    currentLock,
    isLoading,
    lastSaved,

    // Actions
    createVersion,
    rollbackToVersion,
    compareVersions,
    lockVersion,
    releaseLock,
    resolveConflict,
    loadVersions,
    autoSave,

    // Utilities
    checkVersionLock,
    getVersionHistory,
    getPendingConflicts,
    hasUnsavedChanges,
    getVersionStatistics,
    exportVersionHistory
  }
}
