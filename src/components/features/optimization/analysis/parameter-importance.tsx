"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { BarChart3, RefreshCw, AlertCircle, AlertTriangle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { SelectOptimization } from "@/db/schema/optimizations-schema"
import { API_CONFIG } from "@/lib/config"

interface ParameterImportanceProps {
  optimization: SelectOptimization
  topN?: number
}

interface FeatureImportance {
  feature: string
  importance: number
}

export function ParameterImportance({
  optimization,
  topN = 10
}: ParameterImportanceProps) {
  const [featureImportance, setFeatureImportance] = useState<
    FeatureImportance[]
  >([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [warning, setWarning] = useState<string | null>(null)

  const fetchFeatureImportance = async () => {
    setIsLoading(true)
    setError(null)
    setWarning(null)

    // Use the actual optimization ID if available, otherwise use a test ID
    // The optimization.id is a UUID in the database, but we need the actual optimizer ID
    // which is stored in optimization.optimizerId
    const optimizerId = optimization?.optimizerId || "test_optimizer"

    // Log the request details for debugging

    // Use the proxy endpoint instead of direct API call
    const proxyUrl = `/api/proxy/feature-importance?optimizer_id=${optimizerId}&top_n=${topN}`

    console.log("Fetching feature importance from proxy:", proxyUrl)
    console.log("Optimization ID:", optimizerId)
    console.log("Full optimization object:", optimization)

    try {
      const response = await fetch(proxyUrl)

      console.log("Response status:", response.status, response.statusText)

      if (!response.ok) {
        let errorMessage = "Failed to fetch feature importance"
        try {
          const errorData = await response.json()
          errorMessage = errorData.detail || errorMessage
        } catch (parseError) {
          console.error("Error parsing error response:", parseError)
        }
        throw new Error(errorMessage)
      }

      const data = await response.json()
      console.log("Received data:", data)

      // Check if feature_importance exists in the response
      if (data.feature_importance) {
        setFeatureImportance(data.feature_importance)
      } else {
        console.warn("No feature_importance in response:", data)
        setFeatureImportance([])
      }

      // Check if there's a message from the backend
      if (data.message) {
        console.log("Backend message:", data.message)

        // Show the message as a warning
        setWarning(data.message)

        // If it's a status warning, show it with a more user-friendly message
        if (data.status === "warning") {
          setWarning(data.message)
        }
        // Show a warning if using fallback implementation
        else if (data.message.includes("fallback")) {
          setWarning(data.message)
        }
      } else if (
        !data.feature_importance ||
        data.feature_importance.length === 0
      ) {
        // If no message but also no data, show a default warning
        setWarning(
          "No feature importance data available. This may happen when there aren't enough measurements yet."
        )
      }
    } catch (err) {
      console.error("Error fetching feature importance:", err)

      // Provide more detailed error information
      if (err instanceof Error) {
        console.error("Error message:", err.message)
        console.error("Error stack:", err.stack)
        setError(err.message)
      } else {
        console.error("Unknown error type:", typeof err)
        setError("An unknown error occurred")
      }
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (optimization?.id && optimization?.optimizerId) {
      fetchFeatureImportance()
    } else {
      console.log(
        "No optimization ID or optimizer ID available, skipping feature importance fetch"
      )
      if (!optimization?.id) {
        console.log("Missing optimization ID")
      }
      if (!optimization?.optimizerId) {
        console.log("Missing optimizer ID")
      }
    }
  }, [optimization?.id, optimization?.optimizerId, topN])

  // Calculate the maximum importance value for scaling
  const maxImportance =
    featureImportance.length > 0
      ? Math.max(...featureImportance.map(f => f.importance))
      : 0

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="flex items-center text-lg font-medium">
            <BarChart3 className="text-primary mr-2 size-5" />
            Parameter Importance (SHAP)
          </h3>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchFeatureImportance}
            disabled={isLoading}
          >
            {isLoading ? (
              <RefreshCw className="size-4 animate-spin" />
            ) : (
              <RefreshCw className="size-4" />
            )}
            <span className="ml-2">Refresh</span>
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="size-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {warning && (
          <Alert className="mb-4 bg-yellow-50 text-yellow-900 dark:bg-yellow-950 dark:text-yellow-100">
            <AlertTriangle className="size-4" />
            <AlertTitle>Note</AlertTitle>
            <AlertDescription>{warning}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="space-y-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center gap-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 flex-1" />
              </div>
            ))}
          </div>
        ) : featureImportance.length > 0 ? (
          <div className="space-y-3">
            {featureImportance.map((feature, index) => (
              <div key={index} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="font-medium">{feature.feature}</span>
                  <span className="text-muted-foreground">
                    {feature.importance.toFixed(4)}
                  </span>
                </div>
                <div className="bg-muted h-2 w-full overflow-hidden rounded-full">
                  <div
                    className="bg-primary h-full rounded-full"
                    style={{
                      width: `${(feature.importance / maxImportance) * 100 || 0}%`
                    }}
                  />
                </div>
              </div>
            ))}
            {featureImportance.every(f => f.importance === 0) && (
              <Alert className="mt-4 bg-yellow-50 text-yellow-900 dark:bg-yellow-950 dark:text-yellow-100">
                <AlertTriangle className="size-4" />
                <AlertTitle>Note</AlertTitle>
                <AlertDescription>
                  All parameters have zero importance. This may happen when
                  there's not enough data or when the surrogate model hasn't
                  been trained yet.
                </AlertDescription>
              </Alert>
            )}
          </div>
        ) : (
          <div className="flex h-[200px] flex-col items-center justify-center p-4 text-center">
            <BarChart3 className="text-muted-foreground mb-4 size-12" />
            <h3 className="text-lg font-medium">
              No parameter importance data
            </h3>
            <p className="text-muted-foreground mt-2 max-w-md text-sm">
              There might not be enough measurements to calculate parameter
              importance, or the optimization might not use a surrogate model.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
