"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog"
import { toast } from "@/components/ui/use-toast"
import { deleteOptimizationAction } from "@/actions/db/optimizations-actions"

interface DeleteOptimizationButtonProps {
  optimizationId: string
  optimizationName: string
}

export function DeleteOptimizationButton({
  optimizationId,
  optimizationName
}: DeleteOptimizationButtonProps) {
  const router = useRouter()
  const [isDeleting, setIsDeleting] = useState(false)
  const [isOpen, setIsOpen] = useState(false)

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      const result = await deleteOptimizationAction(optimizationId)

      if (result.isSuccess) {
        toast({
          title: "Optimization deleted",
          description:
            "The optimization and all its experiments have been deleted successfully."
        })
        router.push("/dashboard/optimizations")
      } else {
        toast({
          title: "Error deleting optimization",
          description: result.message || "Failed to delete optimization",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error deleting optimization:", error)
      toast({
        title: "Error",
        description:
          "An unexpected error occurred while deleting the optimization",
        variant: "destructive"
      })
    } finally {
      setIsDeleting(false)
      setIsOpen(false)
    }
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="text-destructive hover:bg-destructive/10 hover:text-destructive"
        >
          <Trash2 className="size-4" />
          <span className="sr-only">Delete Optimization</span>
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Optimization</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete &quot;{optimizationName}&quot;? This
            action cannot be undone and will permanently delete this
            optimization and all its experiments.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={e => {
              e.preventDefault()
              handleDelete()
            }}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
