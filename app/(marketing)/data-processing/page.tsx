import { Metadata } from "next"
import { DataProcessingHero } from "@/components/security/data-processing-hero"
import { DataCollection } from "@/components/security/data-collection"
import { DataFlowDiagram } from "@/components/security/data-flow-diagram"
import { DataRetention } from "@/components/security/data-retention"
import { OptimizationDataProcessing } from "@/components/security/optimization-data-processing"
import { UserRights } from "@/components/security/user-rights"
import { DataExportTool } from "@/components/security/data-export-tool"

export const metadata: Metadata = {
  title: "Data Processing Transparency | INNOptimizer™",
  description:
    "Complete transparency about how we collect, process, store, and protect your data in INNOptimizer™.",
  keywords: [
    "data processing",
    "data collection",
    "data retention",
    "GDPR",
    "privacy",
    "transparency",
    "user rights"
  ],
  openGraph: {
    title: "Data Processing Transparency | INNOptimizer™",
    description: "Complete transparency about how we handle your data.",
    type: "website"
  }
}

export default function DataProcessingPage() {
  return (
    <div className="min-h-screen">
      <DataProcessingHero />
      <DataCollection />
      <DataFlowDiagram />
      <OptimizationDataProcessing />
      <DataRetention />
      <UserRights />
      <DataExportTool />
    </div>
  )
}
