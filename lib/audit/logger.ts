import {
  AuditEvent,
  AuditEventType,
  AuditSeverity,
  AuditContext,
  AuditChange,
  AuditMetadata
} from "./types"

export class AuditLogger {
  private static instance: AuditLogger
  private events: AuditEvent[] = []
  private maxEvents = 1000 // Keep last 1000 events in memory

  private constructor() {}

  static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger()
    }
    return AuditLogger.instance
  }

  /**
   * Log an audit event
   */
  async logEvent(
    eventType: AuditEventType,
    title: string,
    description: string,
    context: Partial<AuditContext>,
    options: {
      severity?: AuditSeverity
      changes?: AuditChange[]
      metadata?: Partial<AuditMetadata>
    } = {}
  ): Promise<AuditEvent> {
    const event: AuditEvent = {
      id: this.generateEventId(),
      eventType,
      severity: options.severity || AuditSeverity.INFO,
      title,
      description,
      context: this.buildContext(context),
      changes: options.changes,
      metadata: this.buildMetadata(options.metadata),
      createdAt: new Date()
    }

    // Store in memory
    this.addEventToMemory(event)

    // Send to backend (async, don't wait)
    this.sendToBackend(event).catch(error => {
      console.warn("Failed to send audit event to backend:", error)
    })

    return event
  }

  /**
   * Log configuration change
   */
  async logConfigurationChange(
    optimizationId: string,
    changeType: "create" | "update" | "delete",
    changes: AuditChange[],
    context: Partial<AuditContext> = {},
    metadata: Partial<AuditMetadata> = {}
  ): Promise<AuditEvent> {
    const eventType =
      changeType === "create"
        ? AuditEventType.CONFIGURATION_CREATED
        : changeType === "update"
          ? AuditEventType.CONFIGURATION_UPDATED
          : AuditEventType.CONFIGURATION_DELETED

    const title = `Configuration ${changeType}d`
    const description = `Configuration ${changeType}d with ${changes.length} change(s)`

    return this.logEvent(
      eventType,
      title,
      description,
      { ...context, optimizationId },
      {
        severity: AuditSeverity.INFO,
        changes,
        metadata: {
          ...metadata,
          triggeredBy: "user",
          userInterface: "web"
        }
      }
    )
  }

  /**
   * Log parameter change
   */
  async logParameterChange(
    optimizationId: string,
    parameterName: string,
    changeType: "bounds" | "values" | "type",
    oldValue: any,
    newValue: any,
    context: Partial<AuditContext> = {}
  ): Promise<AuditEvent> {
    const eventType =
      changeType === "bounds"
        ? AuditEventType.PARAMETER_BOUNDS_CHANGED
        : changeType === "values"
          ? AuditEventType.PARAMETER_VALUES_CHANGED
          : AuditEventType.PARAMETER_TYPE_CHANGED

    const changes: AuditChange[] = [
      {
        field: parameterName,
        oldValue,
        newValue,
        changeType: "update"
      }
    ]

    return this.logEvent(
      eventType,
      `Parameter ${changeType} changed`,
      `Parameter "${parameterName}" ${changeType} changed from ${JSON.stringify(oldValue)} to ${JSON.stringify(newValue)}`,
      { ...context, optimizationId },
      {
        severity: AuditSeverity.INFO,
        changes,
        metadata: {
          triggeredBy: "user",
          userInterface: "web"
        }
      }
    )
  }

  /**
   * Log measurement filtering
   */
  async logMeasurementFiltering(
    optimizationId: string,
    totalMeasurements: number,
    retainedMeasurements: number,
    filteredMeasurements: number,
    context: Partial<AuditContext> = {}
  ): Promise<AuditEvent> {
    const retentionPercentage = Math.round(
      (retainedMeasurements / totalMeasurements) * 100
    )
    const severity =
      retentionPercentage < 50
        ? AuditSeverity.WARNING
        : retentionPercentage < 80
          ? AuditSeverity.INFO
          : AuditSeverity.INFO

    return this.logEvent(
      AuditEventType.MEASUREMENTS_FILTERED,
      "Measurements filtered",
      `${filteredMeasurements} of ${totalMeasurements} measurements filtered (${retentionPercentage}% retained)`,
      { ...context, optimizationId },
      {
        severity,
        metadata: {
          triggeredBy: "user",
          userInterface: "web",
          impact: {
            measurementsAffected: filteredMeasurements,
            dataLoss: filteredMeasurements > 0,
            performanceImpact:
              retentionPercentage < 50
                ? "high"
                : retentionPercentage < 80
                  ? "medium"
                  : "low"
          }
        }
      }
    )
  }

  /**
   * Log validation error
   */
  async logValidationError(
    optimizationId: string,
    errorMessage: string,
    field: string,
    context: Partial<AuditContext> = {}
  ): Promise<AuditEvent> {
    return this.logEvent(
      AuditEventType.VALIDATION_ERROR,
      "Validation error",
      `Validation failed for ${field}: ${errorMessage}`,
      { ...context, optimizationId },
      {
        severity: AuditSeverity.ERROR,
        metadata: {
          triggeredBy: "user",
          userInterface: "web",
          errorMessage,
          errorCode: "VALIDATION_ERROR"
        }
      }
    )
  }

  /**
   * Get events from memory
   */
  getEvents(optimizationId?: string, limit = 100): AuditEvent[] {
    let events = this.events

    if (optimizationId) {
      events = events.filter(
        event => event.context.optimizationId === optimizationId
      )
    }

    return events
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit)
  }

  /**
   * Clear events from memory
   */
  clearEvents(): void {
    this.events = []
  }

  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private buildContext(partial: Partial<AuditContext>): AuditContext {
    return {
      timestamp: new Date(),
      optimizationId: partial.optimizationId || "",
      userId: partial.userId || "anonymous",
      sessionId: partial.sessionId || this.getSessionId(),
      userAgent: partial.userAgent || navigator?.userAgent,
      ipAddress: partial.ipAddress || "unknown",
      campaignId: partial.campaignId
    }
  }

  private buildMetadata(partial: Partial<AuditMetadata> = {}): AuditMetadata {
    return {
      triggeredBy: partial.triggeredBy || "user",
      userInterface: partial.userInterface || "web",
      version: partial.version || process.env.NEXT_PUBLIC_APP_VERSION,
      environment: partial.environment || process.env.NODE_ENV,
      requestId: partial.requestId || this.generateRequestId(),
      ...partial
    }
  }

  private addEventToMemory(event: AuditEvent): void {
    this.events.push(event)

    // Keep only the last maxEvents
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents)
    }
  }

  private async sendToBackend(event: AuditEvent): Promise<void> {
    try {
      const response = await fetch("/api/audit/events", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(event)
      })

      if (!response.ok) {
        throw new Error(`Failed to send audit event: ${response.statusText}`)
      }
    } catch (error) {
      // Store failed events for retry (implement if needed)
      console.warn("Failed to send audit event to backend:", error)
    }
  }

  private getSessionId(): string {
    // Try to get from sessionStorage or generate new one
    if (typeof window !== "undefined") {
      let sessionId = sessionStorage.getItem("audit_session_id")
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        sessionStorage.setItem("audit_session_id", sessionId)
      }
      return sessionId
    }
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// Export singleton instance
export const auditLogger = AuditLogger.getInstance()
