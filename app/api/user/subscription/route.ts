import { auth } from "@clerk/nextjs/server"
import { NextResponse } from "next/server"
import { getProfileByUserIdAction } from "@/actions/db/profiles-actions"
import { getActualUserId } from "@/lib/auth-utils"
import { checkTrialStatusAction } from "@/actions/trial-actions"

export async function GET() {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🔍 SUBSCRIPTION API - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  })

  if (!userId) {
    console.log("❌ SUBSCRIPTION API - No user ID, returning unauthorized")
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    console.log("🔍 SUBSCRIPTION API - Getting profile for userId:", userId)
    // Get user profile
    const profileResult = await getProfileByUserIdAction(userId)

    // If user has a Stripe subscription
    if (
      profileResult.isSuccess &&
      profileResult.data &&
      profileResult.data.stripeSubscriptionId
    ) {
      return NextResponse.json({
        tier: profileResult.data.membership || "pro",
        status: "active",
        stripeSubscriptionId: profileResult.data.stripeSubscriptionId,
        stripeCustomerId: profileResult.data.stripeCustomerId,
        subscriptionType: profileResult.data.subscriptionType || null
      })
    }

    // Check trial status
    const trialResult = await checkTrialStatusAction(userId)

    if (trialResult.isSuccess && trialResult.data?.isActive) {
      // Return actual trial data from database
      return NextResponse.json({
        tier: "trial",
        trialEndsAt: trialResult.data.trialEndsAt,
        trialDaysLeft: trialResult.data.daysLeft,
        status: "active"
      })
    }

    // Check if user had a trial that expired
    if (
      profileResult.isSuccess &&
      profileResult.data &&
      profileResult.data.hasTrialExpired === "true" &&
      profileResult.data.trialStartedAt
    ) {
      return NextResponse.json({
        tier: "trial-expired",
        status: "active"
      })
    }

    // If no trial or subscription, return free tier
    return NextResponse.json({
      tier:
        profileResult.isSuccess && profileResult.data
          ? profileResult.data.membership || "free"
          : "free",
      status: "active"
    })
  } catch (error) {
    console.error("Error getting subscription data:", error)

    // Return a fallback response in case of error
    return NextResponse.json(
      {
        tier: "free",
        status: "active",
        error: "Failed to retrieve subscription data"
      },
      { status: 500 }
    )
  }
}
