// app/api/proxy/feature-importance/route.ts
import { NextRequest, NextResponse } from "next/server"
import { API_CONFIG } from "@/lib/config"

/**
 * Proxy endpoint for feature importance to avoid CORS issues
 */
export async function GET(request: NextRequest) {
  try {
    // Get the optimizer ID and top_n from the query parameters
    const { searchParams } = new URL(request.url)
    const optimizerId = searchParams.get("optimizer_id")
    const topN = searchParams.get("top_n") || "10"

    if (!optimizerId) {
      return NextResponse.json(
        {
          error: "Missing optimizer_id parameter",
          feature_importance: [] // Provide empty array to prevent client-side errors
        },
        { status: 400 }
      )
    }

    // Get the API URL from the centralized config
    const apiUrl = `${API_CONFIG.BASE_URL}/optimizations/${optimizerId}/insights/feature-importance?top_n=${topN}`

    console.log("Proxy: Fetching from", apiUrl)
    console.log(
      "With API key:",
      API_CONFIG.API_KEY ? "[API key is set]" : "[API key is missing]"
    )

    // Make the request to the API
    const response = await fetch(apiUrl, {
      headers: {
        "X-API-Key": API_CONFIG.API_KEY,
        Accept: "application/json",
        "Content-Type": "application/json"
      },
      // Add a reasonable timeout
      signal: AbortSignal.timeout(10000)
    })

    // Handle specific error cases
    if (response.status === 400) {
      const errorData = await response.json()
      console.log("API returned 400 error:", errorData)

      // Check for the specific "no measurements" error
      if (
        errorData.detail &&
        errorData.detail.includes("No measurements available")
      ) {
        return NextResponse.json({
          status: "warning",
          message:
            "Not enough measurements to calculate feature importance. Add more measurements and try again.",
          feature_importance: []
        })
      }
    }

    // Get the response as text first for debugging
    const responseText = await response.text()
    console.log("Proxy: Raw response:", responseText)

    // Try to parse the response as JSON
    let data
    try {
      data = JSON.parse(responseText)
      console.log("Proxy: Parsed JSON:", data)
    } catch (parseError) {
      console.error("Proxy: Failed to parse JSON:", parseError)
      return NextResponse.json(
        {
          error: "Failed to parse API response as JSON",
          raw: responseText,
          feature_importance: [] // Provide empty array to prevent client-side errors
        },
        { status: 500 }
      )
    }

    // Ensure the response has a feature_importance field even if it's empty
    if (!data.feature_importance) {
      data.feature_importance = []
    }

    // Return the data
    return NextResponse.json(data)
  } catch (error) {
    console.error("Proxy error:", error)

    // Create a safe response with error information
    return NextResponse.json(
      {
        status: "error",
        error: "Failed to fetch feature importance",
        message: error instanceof Error ? error.message : String(error),
        feature_importance: [] // Provide empty array to prevent client-side errors
      },
      { status: 500 }
    )
  }
}
