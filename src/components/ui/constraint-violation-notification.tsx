// components/ui/constraint-violation-notification.tsx
"use client"

import React, { useState } from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import {
  AlertTriangle,
  CheckCircle,
  Info,
  ChevronDown,
  ChevronRight,
  Target,
  TrendingUp,
  AlertCircle
} from "lucide-react"
import { cn } from "@/lib/utils"

interface ConstraintViolationNotificationProps {
  constraintViolations?: number
  feasibleSamples?: number
  totalAttempts?: number
  requestedSamples: number
  hasConstraints: boolean
  className?: string
}

export function ConstraintViolationNotification({
  constraintViolations,
  feasibleSamples,
  totalAttempts,
  requestedSamples,
  hasConstraints,
  className = ""
}: ConstraintViolationNotificationProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Don't show anything if there are no constraints
  if (!hasConstraints) {
    return null
  }

  // Don't show if we don't have the necessary data
  if (
    constraintViolations === undefined ||
    feasibleSamples === undefined ||
    totalAttempts === undefined
  ) {
    return null
  }

  const successRate =
    totalAttempts > 0 ? (feasibleSamples / totalAttempts) * 100 : 0
  const hasViolations = constraintViolations > 0
  const gotRequestedSamples = feasibleSamples >= requestedSamples

  // Determine notification type and styling
  const getNotificationType = () => {
    if (!hasViolations && gotRequestedSamples) {
      return "success"
    } else if (hasViolations && gotRequestedSamples) {
      return "warning"
    } else {
      return "error"
    }
  }

  const notificationType = getNotificationType()

  const getIcon = () => {
    switch (notificationType) {
      case "success":
        return <CheckCircle className="size-4 text-green-600" />
      case "warning":
        return <AlertTriangle className="size-4 text-amber-600" />
      case "error":
        return <AlertCircle className="size-4 text-red-600" />
    }
  }

  const getTitle = () => {
    switch (notificationType) {
      case "success":
        return "All Constraints Satisfied"
      case "warning":
        return "Constraints Partially Satisfied"
      case "error":
        return "Constraint Satisfaction Issues"
    }
  }

  const getDescription = () => {
    switch (notificationType) {
      case "success":
        return `Successfully generated ${feasibleSamples} samples that satisfy all constraints.`
      case "warning":
        return `Generated ${feasibleSamples} valid samples, but ${constraintViolations} attempts violated constraints.`
      case "error":
        return `Only generated ${feasibleSamples} of ${requestedSamples} requested samples due to constraint violations.`
    }
  }

  const getVariant = () => {
    switch (notificationType) {
      case "success":
        return "default"
      case "warning":
        return "default"
      case "error":
        return "destructive"
    }
  }

  const getRecommendations = () => {
    const recommendations = []

    if (successRate < 50) {
      recommendations.push("Consider relaxing constraint tolerances")
      recommendations.push("Review constraint feasibility")
    }

    if (successRate < 20) {
      recommendations.push("Constraints may be too restrictive")
      recommendations.push("Consider using different sampling strategies")
    }

    if (!gotRequestedSamples) {
      recommendations.push("Increase max attempts for better results")
      recommendations.push("Consider generating samples in smaller batches")
    }

    return recommendations
  }

  return (
    <Alert variant={getVariant()} className={cn("mb-4", className)}>
      <div className="flex items-start gap-3">
        {getIcon()}
        <div className="min-w-0 flex-1">
          <div className="flex items-center justify-between">
            <AlertTitle className="text-sm font-medium">
              {getTitle()}
            </AlertTitle>
            <div className="flex items-center gap-2">
              <Badge
                variant={
                  notificationType === "success" ? "default" : "secondary"
                }
                className="text-xs"
              >
                {successRate.toFixed(1)}% success rate
              </Badge>
              {(hasViolations || !gotRequestedSamples) && (
                <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="size-6 p-0">
                      {isExpanded ? (
                        <ChevronDown className="size-3" />
                      ) : (
                        <ChevronRight className="size-3" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                </Collapsible>
              )}
            </div>
          </div>

          <AlertDescription className="mt-1 text-sm">
            {getDescription()}
          </AlertDescription>

          {/* Statistics Summary */}
          <div className="mt-3 grid grid-cols-3 gap-4 text-xs">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1">
                <Target className="size-3" />
                <span className="font-medium">{feasibleSamples}</span>
              </div>
              <div className="text-muted-foreground">Valid Samples</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1">
                <AlertTriangle className="size-3" />
                <span className="font-medium">{constraintViolations}</span>
              </div>
              <div className="text-muted-foreground">Violations</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1">
                <TrendingUp className="size-3" />
                <span className="font-medium">{totalAttempts}</span>
              </div>
              <div className="text-muted-foreground">Total Attempts</div>
            </div>
          </div>

          {/* Success Rate Progress Bar */}
          <div className="mt-3">
            <div className="mb-1 flex items-center justify-between text-xs">
              <span className="text-muted-foreground">
                Constraint Satisfaction Rate
              </span>
              <span className="font-medium">{successRate.toFixed(1)}%</span>
            </div>
            <Progress
              value={successRate}
              className="h-2"
              // Use different colors based on success rate
              style={
                {
                  "--progress-background":
                    successRate >= 80
                      ? "#10b981"
                      : successRate >= 50
                        ? "#f59e0b"
                        : "#ef4444"
                } as React.CSSProperties
              }
            />
          </div>

          {/* Expandable Details and Recommendations */}
          {(hasViolations || !gotRequestedSamples) && (
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleContent className="mt-4 space-y-3">
                {/* Detailed Statistics */}
                <div className="bg-muted/50 rounded-md p-3">
                  <h4 className="mb-2 text-xs font-medium">
                    Detailed Statistics
                  </h4>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      Requested Samples:{" "}
                      <span className="font-medium">{requestedSamples}</span>
                    </div>
                    <div>
                      Generated Samples:{" "}
                      <span className="font-medium">{feasibleSamples}</span>
                    </div>
                    <div>
                      Constraint Violations:{" "}
                      <span className="font-medium">
                        {constraintViolations}
                      </span>
                    </div>
                    <div>
                      Total Attempts:{" "}
                      <span className="font-medium">{totalAttempts}</span>
                    </div>
                  </div>
                </div>

                {/* Recommendations */}
                {getRecommendations().length > 0 && (
                  <div className="rounded-md bg-blue-50 p-3">
                    <h4 className="mb-2 flex items-center gap-1 text-xs font-medium">
                      <Info className="size-3" />
                      Recommendations
                    </h4>
                    <ul className="space-y-1 text-xs">
                      {getRecommendations().map((recommendation, index) => (
                        <li key={index} className="flex items-start gap-1">
                          <span className="mt-0.5 text-blue-600">•</span>
                          <span>{recommendation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CollapsibleContent>
            </Collapsible>
          )}
        </div>
      </div>
    </Alert>
  )
}
