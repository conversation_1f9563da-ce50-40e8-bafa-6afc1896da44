#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to convert SVG social media assets to PNG format
 * This script converts the SVG files to high-quality PNG images for social media
 */

const fs = require('fs');
const path = require('path');

// Check if sharp is available (optional dependency)
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.log('Sharp not found. Please install it to generate PNG images:');
  console.log('npm install sharp --save-dev');
  process.exit(1);
}

const publicDir = path.join(__dirname, '../public');

const conversions = [
  {
    input: path.join(publicDir, 'social-preview.svg'),
    output: path.join(publicDir, 'social-preview.png'),
    width: 1200,
    height: 630
  },
  {
    input: path.join(publicDir, 'twitter-card.svg'),
    output: path.join(publicDir, 'twitter-card.png'),
    width: 1200,
    height: 600
  },
  {
    input: path.join(publicDir, 'social-square.svg'),
    output: path.join(publicDir, 'social-square.png'),
    width: 800,
    height: 800
  }
];

async function convertSvgToPng() {
  console.log('🎨 Converting SVG social media assets to PNG...\n');

  for (const conversion of conversions) {
    try {
      if (!fs.existsSync(conversion.input)) {
        console.log(`❌ SVG file not found: ${conversion.input}`);
        continue;
      }

      await sharp(conversion.input)
        .resize(conversion.width, conversion.height)
        .png({ quality: 95, compressionLevel: 6 })
        .toFile(conversion.output);

      console.log(`✅ Generated: ${path.basename(conversion.output)}`);
    } catch (error) {
      console.log(`❌ Error converting ${path.basename(conversion.input)}:`, error.message);
    }
  }

  console.log('\n🎉 Social media image generation complete!');
  console.log('\nGenerated files:');
  console.log('- social-preview.png (1200x630) - Facebook, LinkedIn, general sharing');
  console.log('- twitter-card.png (1200x600) - Twitter/X cards');
  console.log('- social-square.png (800x800) - Square format for various platforms');
}

// Run the conversion
convertSvgToPng().catch(console.error);
