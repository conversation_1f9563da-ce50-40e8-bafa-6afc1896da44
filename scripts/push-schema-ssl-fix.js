#!/usr/bin/env node

/**
 * This script pushes the schema to the database with SSL verification disabled
 * Run with: node scripts/push-schema-ssl-fix.js
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create a temporary .env file with modified DATABASE_URL
function createTempEnvFile() {
  console.log('Creating temporary .env file with modified DATABASE_URL...');
  
  // Read the current .env.local file
  const envPath = path.join(process.cwd(), '.env.local');
  let envContent = '';
  
  try {
    envContent = fs.readFileSync(envPath, 'utf8');
  } catch (error) {
    console.error('Error reading .env.local file:', error.message);
    process.exit(1);
  }
  
  // Modify the DATABASE_URL to disable SSL verification
  const modifiedContent = envContent.replace(
    /DATABASE_URL=(.+)sslmode=require/g, 
    'DATABASE_URL=$1sslmode=no-verify'
  );
  
  // Write to a temporary file
  const tempEnvPath = path.join(process.cwd(), '.env.temp');
  fs.writeFileSync(tempEnvPath, modifiedContent);
  
  console.log('Temporary .env file created successfully');
  return tempEnvPath;
}

// Clean up the temporary file
function cleanupTempFile(tempEnvPath) {
  console.log('Cleaning up temporary files...');
  try {
    fs.unlinkSync(tempEnvPath);
    console.log('Temporary .env file removed successfully');
  } catch (error) {
    console.error('Error removing temporary .env file:', error.message);
  }
}

// Main function to push the schema
async function pushSchema() {
  console.log('Starting schema push process...');
  
  // Create temporary .env file
  const tempEnvPath = createTempEnvFile();
  
  try {
    // Extract the modified DATABASE_URL from the temp file
    const tempEnvContent = fs.readFileSync(tempEnvPath, 'utf8');
    const dbUrlMatch = tempEnvContent.match(/DATABASE_URL=(.+)/);
    
    if (!dbUrlMatch) {
      throw new Error('Could not find DATABASE_URL in temporary .env file');
    }
    
    const modifiedDbUrl = dbUrlMatch[1];
    
    // Run Drizzle push command with the modified DATABASE_URL
    console.log('\nPushing schema to database...');
    execSync('npx drizzle-kit push', {
      stdio: 'inherit',
      env: {
        ...process.env,
        DATABASE_URL: modifiedDbUrl
      }
    });
    
    console.log('\n✅ Schema push completed successfully!');
  } catch (error) {
    console.error('\n❌ Error pushing schema:', error.message);
  } finally {
    // Clean up
    cleanupTempFile(tempEnvPath);
  }
}

// Start the process
pushSchema();
