"use client"

import { useState, useEffect, useCallback } from "react"
import { auditLogger } from "./logger"
import {
  AuditEvent,
  AuditEventType,
  AuditSeverity,
  AuditContext,
  AuditChange,
  AuditMetadata
} from "./types"

export interface UseAuditLoggingOptions {
  optimizationId: string
  userId?: string
  autoLog?: boolean
}

export function useAuditLogging(options: UseAuditLoggingOptions) {
  const [events, setEvents] = useState<AuditEvent[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Base context for all events
  const baseContext: Partial<AuditContext> = {
    optimizationId: options.optimizationId,
    userId: options.userId
  }

  /**
   * Log a generic audit event
   */
  const logEvent = useCallback(
    async (
      eventType: AuditEventType,
      title: string,
      description: string,
      additionalContext: Partial<AuditContext> = {},
      eventOptions: {
        severity?: AuditSeverity
        changes?: AuditChange[]
        metadata?: Partial<AuditMetadata>
      } = {}
    ): Promise<AuditEvent> => {
      const context = { ...baseContext, ...additionalContext }
      const event = await auditLogger.logEvent(
        eventType,
        title,
        description,
        context,
        eventOptions
      )

      // Update local state
      setEvents(prev => [event, ...prev.slice(0, 99)]) // Keep last 100 events

      return event
    },
    [baseContext]
  )

  /**
   * Log configuration changes
   */
  const logConfigurationChange = useCallback(
    async (
      changeType: "create" | "update" | "delete",
      changes: AuditChange[],
      additionalContext: Partial<AuditContext> = {},
      metadata: Partial<AuditMetadata> = {}
    ): Promise<AuditEvent> => {
      const context = { ...baseContext, ...additionalContext }
      const event = await auditLogger.logConfigurationChange(
        options.optimizationId,
        changeType,
        changes,
        context,
        metadata
      )

      setEvents(prev => [event, ...prev.slice(0, 99)])
      return event
    },
    [baseContext, options.optimizationId]
  )

  /**
   * Log parameter changes
   */
  const logParameterChange = useCallback(
    async (
      parameterName: string,
      changeType: "bounds" | "values" | "type",
      oldValue: any,
      newValue: any,
      additionalContext: Partial<AuditContext> = {}
    ): Promise<AuditEvent> => {
      const context = { ...baseContext, ...additionalContext }
      const event = await auditLogger.logParameterChange(
        options.optimizationId,
        parameterName,
        changeType,
        oldValue,
        newValue,
        context
      )

      setEvents(prev => [event, ...prev.slice(0, 99)])
      return event
    },
    [baseContext, options.optimizationId]
  )

  /**
   * Log target changes
   */
  const logTargetChange = useCallback(
    async (
      targetName: string,
      changeType: "bounds" | "weight" | "mode",
      oldValue: any,
      newValue: any,
      additionalContext: Partial<AuditContext> = {}
    ): Promise<AuditEvent> => {
      const eventType =
        changeType === "bounds"
          ? AuditEventType.TARGET_BOUNDS_CHANGED
          : changeType === "weight"
            ? AuditEventType.TARGET_WEIGHT_CHANGED
            : AuditEventType.TARGET_MODE_CHANGED

      const changes: AuditChange[] = [
        {
          field: targetName,
          oldValue,
          newValue,
          changeType: "update"
        }
      ]

      return logEvent(
        eventType,
        `Target ${changeType} changed`,
        `Target "${targetName}" ${changeType} changed from ${JSON.stringify(oldValue)} to ${JSON.stringify(newValue)}`,
        additionalContext,
        {
          severity: AuditSeverity.INFO,
          changes,
          metadata: {
            triggeredBy: "user",
            userInterface: "web"
          }
        }
      )
    },
    [logEvent]
  )

  /**
   * Log constraint changes
   */
  const logConstraintChange = useCallback(
    async (
      constraintName: string,
      changeType: "add" | "update" | "remove" | "enable" | "disable",
      oldValue: any,
      newValue: any,
      additionalContext: Partial<AuditContext> = {}
    ): Promise<AuditEvent> => {
      const eventType =
        changeType === "add"
          ? AuditEventType.CONSTRAINT_ADDED
          : changeType === "update"
            ? AuditEventType.CONSTRAINT_UPDATED
            : changeType === "remove"
              ? AuditEventType.CONSTRAINT_REMOVED
              : changeType === "enable"
                ? AuditEventType.CONSTRAINT_ENABLED
                : AuditEventType.CONSTRAINT_DISABLED

      const changes: AuditChange[] = [
        {
          field: constraintName,
          oldValue,
          newValue,
          changeType:
            changeType === "add"
              ? "create"
              : changeType === "remove"
                ? "delete"
                : "update"
        }
      ]

      return logEvent(
        eventType,
        `Constraint ${changeType}d`,
        `Constraint "${constraintName}" ${changeType}d`,
        additionalContext,
        {
          severity: AuditSeverity.INFO,
          changes,
          metadata: {
            triggeredBy: "user",
            userInterface: "web"
          }
        }
      )
    },
    [logEvent]
  )

  /**
   * Log measurement filtering
   */
  const logMeasurementFiltering = useCallback(
    async (
      totalMeasurements: number,
      retainedMeasurements: number,
      filteredMeasurements: number,
      additionalContext: Partial<AuditContext> = {}
    ): Promise<AuditEvent> => {
      const context = { ...baseContext, ...additionalContext }
      const event = await auditLogger.logMeasurementFiltering(
        options.optimizationId,
        totalMeasurements,
        retainedMeasurements,
        filteredMeasurements,
        context
      )

      setEvents(prev => [event, ...prev.slice(0, 99)])
      return event
    },
    [baseContext, options.optimizationId]
  )

  /**
   * Log validation errors
   */
  const logValidationError = useCallback(
    async (
      errorMessage: string,
      field: string,
      additionalContext: Partial<AuditContext> = {}
    ): Promise<AuditEvent> => {
      const context = { ...baseContext, ...additionalContext }
      const event = await auditLogger.logValidationError(
        options.optimizationId,
        errorMessage,
        field,
        context
      )

      setEvents(prev => [event, ...prev.slice(0, 99)])
      return event
    },
    [baseContext, options.optimizationId]
  )

  /**
   * Log user actions
   */
  const logUserAction = useCallback(
    async (
      action: string,
      description: string,
      additionalContext: Partial<AuditContext> = {},
      metadata: Partial<AuditMetadata> = {}
    ): Promise<AuditEvent> => {
      return logEvent(
        AuditEventType.USER_ACTION,
        action,
        description,
        additionalContext,
        {
          severity: AuditSeverity.INFO,
          metadata: {
            triggeredBy: "user",
            userInterface: "web",
            ...metadata
          }
        }
      )
    },
    [logEvent]
  )

  /**
   * Load events from memory
   */
  const loadEvents = useCallback(
    async (limit = 100) => {
      setIsLoading(true)
      try {
        const loadedEvents = auditLogger.getEvents(
          options.optimizationId,
          limit
        )
        setEvents(loadedEvents)
      } catch (error) {
        console.error("Failed to load audit events:", error)
      } finally {
        setIsLoading(false)
      }
    },
    [options.optimizationId]
  )

  /**
   * Clear events
   */
  const clearEvents = useCallback(() => {
    setEvents([])
    auditLogger.clearEvents()
  }, [])

  // Load events on mount
  useEffect(() => {
    if (options.autoLog !== false) {
      loadEvents()
    }
  }, [loadEvents, options.autoLog])

  return {
    events,
    isLoading,
    logEvent,
    logConfigurationChange,
    logParameterChange,
    logTargetChange,
    logConstraintChange,
    logMeasurementFiltering,
    logValidationError,
    logUserAction,
    loadEvents,
    clearEvents
  }
}
