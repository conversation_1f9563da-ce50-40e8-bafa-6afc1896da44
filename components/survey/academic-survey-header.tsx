/*
This client component provides a restricted header for the academic survey page.
It prevents navigation to other areas during survey completion.
*/

"use client"

import { But<PERSON> } from "@/components/ui/button"
import { UserButton } from "@clerk/nextjs"
import { motion } from "framer-motion"
import { GraduationCap, Lock } from "lucide-react"
import Link from "next/link"
import { useState, useEffect } from "react"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { BRAND } from "@/lib/constants"

export default function AcademicSurveyHeader() {
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Prevent navigation attempts during survey
  const handleRestrictedNavigation = (e: React.MouseEvent) => {
    e.preventDefault()
    // Could add a toast notification here if desired
  }

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className={`sticky top-0 z-50 transition-colors ${
        isScrolled
          ? "bg-background/80 shadow-sm backdrop-blur-sm"
          : "bg-background"
      }`}
    >
      <div className="container mx-auto flex max-w-7xl items-center justify-between p-4">
        {/* Logo and App name */}
        <motion.div
          className="flex items-center gap-2"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <GraduationCap className="size-6 text-blue-600" />
          <span className="from-primary bg-gradient-to-r to-blue-600 bg-clip-text text-xl font-bold text-transparent">
            {BRAND.NAME}
          </span>
          <span className="text-muted-foreground text-sm">Academic Survey</span>
        </motion.div>

        {/* Center message */}
        <div className="hidden items-center gap-2 md:flex">
          <Lock className="size-4 text-amber-500" />
          <span className="text-muted-foreground text-sm">
            Complete survey to access dashboard
          </span>
        </div>

        {/* Right side - User button only */}
        <div className="flex items-center space-x-4">
          {/* Disabled dashboard button with tooltip */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  className="cursor-not-allowed opacity-50"
                  onClick={handleRestrictedNavigation}
                  disabled
                >
                  Dashboard
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Complete the academic survey to access dashboard</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <UserButton />
        </div>
      </div>
    </motion.header>
  )
}
