#!/bin/bash

# Script to run database migrations for the BOapp-frontend

# Navigate to the project directory
cd "$(dirname "$0")/.."

# Check if the database exists
if [ ! -f "./data/db.sqlite" ]; then
  echo "Database file not found. Creating a new database..."
  mkdir -p ./data
  touch ./data/db.sqlite
fi

# Run the migrations
echo "Running database migrations..."
npx drizzle-kit push:sqlite

echo "Migrations completed successfully!"
