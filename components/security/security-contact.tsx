"use client"

import { motion } from "framer-motion"
import {
  Mail,
  Shield,
  Alert<PERSON>riangle,
  FileText,
  Phone,
  MessageSquare
} from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

const contactOptions = [
  {
    icon: AlertTriangle,
    title: "Security Issues",
    description: "Report security vulnerabilities or incidents",
    contact: "<EMAIL>",
    response: "Business days",
    type: "email",
    priority: "high"
  },
  {
    icon: Shield,
    title: "Privacy & Data Rights",
    description: "Data protection and privacy inquiries",
    contact: "<EMAIL>",
    response: "Business days",
    type: "email",
    priority: "normal"
  }
]

const securityResources = [
  {
    title: "Security Policy",
    description: "Our comprehensive information security policy",
    link: "/security-policy"
  },
  {
    title: "Privacy Policy",
    description: "How we collect, use, and protect your data",
    link: "/privacy-policy"
  },
  {
    title: "Data Processing",
    description: "Detailed information about data processing",
    link: "/data-processing"
  },
  {
    title: "Incident Response",
    description: "Our security incident response procedures",
    link: "/security-policy#incident-response"
  }
]

export function SecurityContact() {
  return (
    <section className="bg-gray-50 py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Security Contact & Resources
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            Have security questions or concerns? Our security team is here to
            help. We take all security matters seriously and respond promptly.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mb-12"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                Contact Our Security Team
              </CardTitle>
              <CardDescription>
                We respond to all security inquiries during business hours
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {contactOptions.map((option, index) => (
                  <div
                    key={option.title}
                    className={`rounded-lg p-4 ${
                      option.priority === "high"
                        ? "border border-red-200 bg-red-50"
                        : "border border-blue-200 bg-blue-50"
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div
                        className={`flex size-10 items-center justify-center rounded-lg ${
                          option.priority === "high"
                            ? "bg-red-100"
                            : "bg-blue-100"
                        }`}
                      >
                        <option.icon
                          className={`size-5 ${
                            option.priority === "high"
                              ? "text-red-600"
                              : "text-blue-600"
                          }`}
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">
                          {option.title}
                        </h4>
                        <p className="mb-2 text-sm text-gray-600">
                          {option.description}
                        </p>
                        <a
                          href={`mailto:${option.contact}`}
                          className={`text-sm font-medium hover:underline ${
                            option.priority === "high"
                              ? "text-red-600"
                              : "text-blue-600"
                          }`}
                        >
                          {option.contact}
                        </a>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-500">
                  We aim to respond to all inquiries within 1-3 business days
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Security Resources</CardTitle>
              <CardDescription>
                Additional security documentation and policies
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {securityResources.map((resource, index) => (
                  <div
                    key={resource.title}
                    className="flex items-center justify-between rounded-lg bg-gray-50 p-3"
                  >
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {resource.title}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {resource.description}
                      </p>
                    </div>
                    <Button asChild variant="ghost" size="sm">
                      <Link href={resource.link}>
                        <FileText className="size-4" />
                      </Link>
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-12 text-center"
        >
          <Card className="bg-gradient-to-r from-red-50 to-orange-50">
            <CardContent className="pt-6">
              <h3 className="mb-4 text-xl font-semibold text-gray-900">
                Responsible Disclosure
              </h3>
              <p className="mb-6 text-gray-600">
                We appreciate security researchers who help us maintain the
                security of our platform. If you discover a security
                vulnerability, please report it responsibly.
              </p>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-red-600">
                    Report
                  </div>
                  <div className="text-sm text-gray-600">
                    <EMAIL>
                  </div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-orange-600">
                    Response
                  </div>
                  <div className="text-sm text-gray-600">Business days</div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-green-600">
                    Recognition
                  </div>
                  <div className="text-sm text-gray-600">
                    Security hall of fame
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
