// lib/constraints/utils.ts
// Utility functions for constraint management

import { Constraint, Parameter, ConstraintCategory } from "./types"

/**
 * Generate a unique constraint ID
 */
export function generateConstraintId(): string {
  return `constraint_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

/**
 * Get constraint category information
 */
export function getConstraintCategory(
  constraintType: string
): keyof typeof CONSTRAINT_CATEGORIES {
  return CONSTRAINT_TYPE_CATEGORIES[constraintType] || "custom"
}

/**
 * Check if a constraint type is continuous
 */
export function isContinuousConstraint(constraintType: string): boolean {
  const continuousTypes = [
    "ContinuousLinearConstraint",
    "ContinuousCardinalityConstraint"
  ]
  return continuousTypes.includes(constraintType)
}

/**
 * Check if a constraint type is discrete
 */
export function isDiscreteConstraint(constraintType: string): boolean {
  const discreteTypes = [
    "DiscreteCardinalityConstraint",
    "DiscreteExcludeConstraint",
    "DiscreteCustomConstraint",
    "DiscreteDependenciesConstraint",
    "DiscreteLinkedParametersConstraint",
    "DiscreteNoLabelDuplicatesConstraint",
    "DiscretePermutationInvarianceConstraint",
    "DiscreteProductConstraint",
    "DiscreteSumConstraint"
  ]
  return discreteTypes.includes(constraintType)
}

/**
 * Check if a parameter type is continuous
 */
export function isContinuousParameter(parameterType: string): boolean {
  return parameterType === "NumericalContinuous"
}

/**
 * Check if a parameter type is discrete
 */
export function isDiscreteParameter(parameterType: string): boolean {
  return (
    parameterType === "NumericalDiscrete" ||
    parameterType === "CategoricalParameter"
  )
}

/**
 * Validate constraint-parameter type compatibility
 */
export function validateConstraintParameterCompatibility(
  constraint: Constraint,
  parameters: Parameter[]
): { valid: boolean; error?: string } {
  const constraintParams = constraint.parameters
  const parameterLookup = new Map(parameters.map(p => [p.name, p]))

  // Check if all constraint parameters exist
  const missingParams = constraintParams.filter(
    name => !parameterLookup.has(name)
  )
  if (missingParams.length > 0) {
    return {
      valid: false,
      error: `Parameters not found: ${missingParams.join(", ")}`
    }
  }

  // Get parameter types for constraint parameters
  const paramTypes = constraintParams.map(
    name => parameterLookup.get(name)!.type
  )

  // Check type compatibility
  const hasContinuous = paramTypes.some(isContinuousParameter)
  const hasDiscrete = paramTypes.some(isDiscreteParameter)

  if (hasContinuous && hasDiscrete) {
    return {
      valid: false,
      error:
        "Cannot mix continuous and discrete parameter types in the same constraint"
    }
  }

  // Check constraint type compatibility
  const isContinuousConstraintType = isContinuousConstraint(constraint.type)
  const isDiscreteConstraintType = isDiscreteConstraint(constraint.type)

  if (isContinuousConstraintType && hasDiscrete) {
    return {
      valid: false,
      error: `Continuous constraint type "${constraint.type}" cannot be used with discrete parameters`
    }
  }

  if (isDiscreteConstraintType && hasContinuous) {
    return {
      valid: false,
      error: `Discrete constraint type "${constraint.type}" cannot be used with continuous parameters`
    }
  }

  return { valid: true }
}

/**
 * Get available parameters for a constraint type
 */
export function getAvailableParametersForConstraint(
  constraintType: string,
  parameters: Parameter[]
): Parameter[] {
  if (isContinuousConstraint(constraintType)) {
    return parameters.filter(p => isContinuousParameter(p.type))
  } else if (isDiscreteConstraint(constraintType)) {
    return parameters.filter(p => isDiscreteParameter(p.type))
  }
  return parameters
}

/**
 * Format constraint for display
 */
export function formatConstraintDisplay(constraint: Constraint): string {
  switch (constraint.type) {
    case "ContinuousLinearConstraint":
      const terms = constraint.parameters
        .map((param, i) => {
          const coeff = constraint.coefficients[i]
          const sign = coeff >= 0 ? "+" : ""
          return `${sign}${coeff}*${param}`
        })
        .join(" ")
      return `${terms} ${constraint.operator} ${constraint.rhs}`

    case "ContinuousCardinalityConstraint":
    case "DiscreteCardinalityConstraint":
      const minCard = constraint.min_cardinality ?? 0
      const maxCard = constraint.max_cardinality ?? "∞"
      return `${minCard} ≤ active parameters ≤ ${maxCard}`

    case "DiscreteExcludeConstraint":
      return `Exclude combinations (${constraint.combiner})`

    default:
      return constraint.name || constraint.type.replace("Constraint", "")
  }
}

/**
 * Get constraint type metadata
 */
export function getConstraintTypeMetadata(constraintType: string) {
  return (
    (CONSTRAINT_TYPE_METADATA as any)[constraintType] || {
      label: constraintType,
      description: "Custom constraint",
      difficulty: "advanced",
      example: "Custom constraint example"
    }
  )
}

// Constraint categories for UI organization
export const CONSTRAINT_CATEGORIES: Record<string, ConstraintCategory> = {
  linear: {
    name: "Linear Constraints",
    description: "Mathematical relationships between parameters",
    icon: "Calculator",
    color: "blue"
  },
  cardinality: {
    name: "Cardinality Constraints",
    description: "Control number of active parameters",
    icon: "Hash",
    color: "green"
  },
  exclusion: {
    name: "Exclusion Rules",
    description: "Exclude invalid parameter combinations",
    icon: "X",
    color: "red"
  },
  mathematical: {
    name: "Mathematical",
    description: "Sum, product, and other mathematical constraints",
    icon: "Sigma",
    color: "purple"
  },
  dependency: {
    name: "Dependencies",
    description: "Parameter dependencies and relationships",
    icon: "GitBranch",
    color: "orange"
  },
  custom: {
    name: "Custom",
    description: "User-defined constraint functions",
    icon: "Code",
    color: "gray"
  }
}

// Constraint type to category mapping
export const CONSTRAINT_TYPE_CATEGORIES: Record<
  string,
  keyof typeof CONSTRAINT_CATEGORIES
> = {
  ContinuousLinearConstraint: "linear",
  ContinuousCardinalityConstraint: "cardinality",
  DiscreteCardinalityConstraint: "cardinality",
  DiscreteExcludeConstraint: "exclusion",
  DiscreteSumConstraint: "mathematical",
  DiscreteProductConstraint: "mathematical",
  DiscreteDependenciesConstraint: "dependency",
  DiscreteLinkedParametersConstraint: "dependency",
  DiscreteNoLabelDuplicatesConstraint: "exclusion",
  DiscretePermutationInvarianceConstraint: "dependency",
  DiscreteCustomConstraint: "custom"
}

// Constraint type metadata
export const CONSTRAINT_TYPE_METADATA = {
  ContinuousLinearConstraint: {
    label: "Linear Constraint",
    description: "Mathematical relationships like ax + by ≤ c, x < y, etc.",
    difficulty: "beginner",
    example: "x6 + x15 ≤ 1.0, x19 < x20"
  },
  DiscreteExcludeConstraint: {
    label: "Exclusion Rule",
    description: "Exclude invalid parameter combinations",
    difficulty: "beginner",
    example: "Avoid high temperature with sensitive catalyst"
  },
  ContinuousCardinalityConstraint: {
    label: "Cardinality (Continuous)",
    description: "Control number of active continuous parameters",
    difficulty: "intermediate",
    example: "Use at most 3 components in mixture"
  },
  DiscreteCardinalityConstraint: {
    label: "Cardinality (Discrete)",
    description: "Control number of active discrete parameters",
    difficulty: "intermediate",
    example: "Select exactly 2 catalysts"
  },
  DiscreteSumConstraint: {
    label: "Sum Constraint",
    description: "Sum of discrete parameters meets condition",
    difficulty: "intermediate",
    example: "Total catalyst amount ≤ 10"
  },
  DiscreteProductConstraint: {
    label: "Product Constraint",
    description: "Product of discrete parameters meets condition",
    difficulty: "intermediate",
    example: "Pressure × Temperature ≤ 1000"
  },
  DiscreteDependenciesConstraint: {
    label: "Dependencies",
    description: "Parameters depend on other parameters",
    difficulty: "advanced",
    example: "Catalyst only relevant when reaction enabled"
  },
  DiscreteLinkedParametersConstraint: {
    label: "Linked Parameters",
    description: "Parameters are linked together",
    difficulty: "advanced",
    example: "Temperature and pressure must be coordinated"
  },
  DiscreteNoLabelDuplicatesConstraint: {
    label: "No Duplicates",
    description: "Prevent duplicate values across parameters",
    difficulty: "intermediate",
    example: "Each solvent slot must be different"
  },
  DiscretePermutationInvarianceConstraint: {
    label: "Permutation Invariance",
    description: "Order of parameters doesn't matter",
    difficulty: "advanced",
    example: "Component order is irrelevant"
  },
  DiscreteCustomConstraint: {
    label: "Custom Function",
    description: "User-defined constraint function",
    difficulty: "advanced",
    example: "sqrt(temp) * pressure^3 ≤ 5.6"
  }
} as const
