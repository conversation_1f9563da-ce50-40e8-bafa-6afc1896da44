// Simple event system for cross-component communication

type EventCallback = (...args: any[]) => void

class EventEmitter {
  private events: Record<string, EventCallback[]> = {}

  // Subscribe to an event
  on(event: string, callback: EventCallback): () => void {
    if (!this.events[event]) {
      this.events[event] = []
    }
    this.events[event].push(callback)

    // Return unsubscribe function
    return () => {
      this.events[event] = this.events[event].filter(cb => cb !== callback)
    }
  }

  // Emit an event
  emit(event: string, ...args: any[]): void {
    if (this.events[event]) {
      this.events[event].forEach(callback => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error)
        }
      })
    }
  }
}

// Create a singleton instance
export const eventEmitter = new EventEmitter()

// Define event names as constants
export const EVENTS = {
  API_STATUS_UPDATE_REQUESTED: "api-status-update-requested",
  GPU_INTENSIVE_OPERATION_STARTED: "gpu-intensive-operation-started",
  GPU_INTENSIVE_OPERATION_ENDED: "gpu-intensive-operation-ended"
}
