// actions/db/suggestions-actions.ts
"use server"

import { db } from "@/db/db"
import { suggestionsTable, InsertSuggestion, SelectSuggestion } from "@/db/schema"
import { eq, and, desc, asc } from "drizzle-orm"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { getOptimizationByIdAction } from "./optimizations-actions"

export type ActionState<T = undefined> = {
  isSuccess: boolean
  message: string
  data?: T
}

/**
 * Create a new suggestion in the database
 */
export async function createSuggestionAction(
  suggestion: InsertSuggestion
): Promise<ActionState<SelectSuggestion>> {
  try {
    // Verify user has access to the optimization
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 CREATE SUGGESTION ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to create suggestions"
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(suggestion.optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Create the suggestion
    const [newSuggestion] = await db
      .insert(suggestionsTable)
      .values(suggestion)
      .returning()

    return {
      isSuccess: true,
      message: "Suggestion created successfully",
      data: newSuggestion
    }
  } catch (error) {
    console.error("Error creating suggestion:", error)
    return {
      isSuccess: false,
      message: `Failed to create suggestion: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Create multiple suggestions in the database
 */
export async function createSuggestionsAction(
  suggestions: InsertSuggestion[]
): Promise<ActionState<SelectSuggestion[]>> {
  try {
    // Verify user has access to the optimization
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to create suggestions"
      }
    }

    if (suggestions.length === 0) {
      return {
        isSuccess: false,
        message: "No suggestions provided"
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(suggestions[0].optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Create the suggestions
    const newSuggestions = await db
      .insert(suggestionsTable)
      .values(suggestions)
      .returning()

    return {
      isSuccess: true,
      message: `${newSuggestions.length} suggestions created successfully`,
      data: newSuggestions
    }
  } catch (error) {
    console.error("Error creating suggestions:", error)
    return {
      isSuccess: false,
      message: `Failed to create suggestions: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Get all suggestions for an optimization
 */
export async function getSuggestionsAction(
  optimizationId: string
): Promise<ActionState<SelectSuggestion[]>> {
  try {
    // Verify user has access to the optimization
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to get suggestions"
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Get the suggestions
    const suggestions = await db
      .select()
      .from(suggestionsTable)
      .where(eq(suggestionsTable.optimizationId, optimizationId))
      .orderBy(desc(suggestionsTable.createdAt))

    return {
      isSuccess: true,
      message: `${suggestions.length} suggestions found`,
      data: suggestions
    }
  } catch (error) {
    console.error("Error getting suggestions:", error)
    return {
      isSuccess: false,
      message: `Failed to get suggestions: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Get suggestions by status for an optimization
 */
export async function getSuggestionsByStatusAction(
  optimizationId: string,
  status: string
): Promise<ActionState<SelectSuggestion[]>> {
  try {
    // Verify user has access to the optimization
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to get suggestions"
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Get the suggestions with the specified status
    const suggestions = await db
      .select()
      .from(suggestionsTable)
      .where(
        and(
          eq(suggestionsTable.optimizationId, optimizationId),
          eq(suggestionsTable.status, status)
        )
      )
      .orderBy(desc(suggestionsTable.createdAt))

    return {
      isSuccess: true,
      message: `${suggestions.length} ${status} suggestions found`,
      data: suggestions
    }
  } catch (error) {
    console.error(`Error getting ${status} suggestions:`, error)
    return {
      isSuccess: false,
      message: `Failed to get ${status} suggestions: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Get suggestions by batch ID
 */
export async function getSuggestionsByBatchAction(
  batchId: string
): Promise<ActionState<SelectSuggestion[]>> {
  try {
    // Verify user is signed in
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to get suggestions"
      }
    }

    // Get the suggestions for this batch
    const suggestions = await db
      .select()
      .from(suggestionsTable)
      .where(eq(suggestionsTable.batchId, batchId))
      .orderBy(asc(suggestionsTable.suggestionIndex))

    // If no suggestions found, return early
    if (suggestions.length === 0) {
      return {
        isSuccess: true,
        message: "No suggestions found for this batch",
        data: []
      }
    }

    // Verify the user has access to the optimization
    const optResult = await getOptimizationByIdAction(suggestions[0].optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    return {
      isSuccess: true,
      message: `${suggestions.length} suggestions found for batch ${batchId}`,
      data: suggestions
    }
  } catch (error) {
    console.error("Error getting suggestions by batch:", error)
    return {
      isSuccess: false,
      message: `Failed to get suggestions by batch: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Update a suggestion's status
 */
export async function updateSuggestionStatusAction(
  suggestionId: string,
  status: string
): Promise<ActionState<SelectSuggestion>> {
  try {
    // Verify user has access to the suggestion
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to update suggestions"
      }
    }

    // Get the suggestion to verify access
    const [suggestion] = await db
      .select()
      .from(suggestionsTable)
      .where(eq(suggestionsTable.id, suggestionId))

    if (!suggestion) {
      return {
        isSuccess: false,
        message: "Suggestion not found"
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(suggestion.optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Update timestamps based on status
    const updates: any = { status };

    if (status === 'in_progress') {
      updates.lastAccessedAt = new Date();
    } else if (status === 'submitted') {
      updates.submittedAt = new Date();
    }

    // Update the suggestion
    const [updatedSuggestion] = await db
      .update(suggestionsTable)
      .set(updates)
      .where(eq(suggestionsTable.id, suggestionId))
      .returning()

    return {
      isSuccess: true,
      message: "Suggestion status updated successfully",
      data: updatedSuggestion
    }
  } catch (error) {
    console.error("Error updating suggestion status:", error)
    return {
      isSuccess: false,
      message: `Failed to update suggestion status: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Update a suggestion's target values
 */
export async function updateSuggestionTargetValuesAction(
  suggestionId: string,
  targetValues: Record<string, string>
): Promise<ActionState<SelectSuggestion>> {
  try {
    // Verify user has access to the suggestion
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to update suggestions"
      }
    }

    // Get the suggestion to verify access
    const [suggestion] = await db
      .select()
      .from(suggestionsTable)
      .where(eq(suggestionsTable.id, suggestionId))

    if (!suggestion) {
      return {
        isSuccess: false,
        message: "Suggestion not found"
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(suggestion.optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Update the suggestion with target values and set status to in_progress
    const [updatedSuggestion] = await db
      .update(suggestionsTable)
      .set({
        targetValues,
        status: 'in_progress',
        lastAccessedAt: new Date()
      })
      .where(eq(suggestionsTable.id, suggestionId))
      .returning()

    return {
      isSuccess: true,
      message: "Suggestion target values updated successfully",
      data: updatedSuggestion
    }
  } catch (error) {
    console.error("Error updating suggestion target values:", error)
    return {
      isSuccess: false,
      message: `Failed to update suggestion target values: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Delete a suggestion
 */
export async function deleteSuggestionAction(
  suggestionId: string
): Promise<ActionState<void>> {
  try {
    // Verify user has access to the suggestion
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to delete suggestions"
      }
    }

    // Get the suggestion to verify access
    const [suggestion] = await db
      .select()
      .from(suggestionsTable)
      .where(eq(suggestionsTable.id, suggestionId))

    if (!suggestion) {
      return {
        isSuccess: false,
        message: "Suggestion not found"
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(suggestion.optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Delete the suggestion
    await db
      .delete(suggestionsTable)
      .where(eq(suggestionsTable.id, suggestionId))

    return {
      isSuccess: true,
      message: "Suggestion deleted successfully"
    }
  } catch (error) {
    console.error("Error deleting suggestion:", error)
    return {
      isSuccess: false,
      message: `Failed to delete suggestion: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
