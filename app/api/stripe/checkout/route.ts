/*
This API route creates a Stripe checkout session for users who have just signed up.
It handles the redirection from signup to checkout for unauthenticated users who want to subscribe.
*/

import { stripe } from "@/lib/stripe"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { NextRequest, NextResponse } from "next/server"

export async function GET(req: NextRequest) {
  try {
    // Get the authenticated user
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 STRIPE CHECKOUT API - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    // If no user is authenticated, redirect to login
    if (!userId) {
      return NextResponse.redirect(new URL("/login", req.url))
    }

    // Get the plan type from the query parameters
    const searchParams = req.nextUrl.searchParams
    let planType = searchParams.get("plan")

    console.log(
      "Checkout API received plan type:",
      planType,
      "from URL:",
      req.url
    )
    console.log(
      "All search params:",
      Object.fromEntries(searchParams.entries())
    )

    // If plan type is not provided in the URL, try to determine it from the referrer
    if (!planType) {
      const referer = req.headers.get("referer") || ""
      console.log("Checkout API referer:", referer)

      // Check if the referer contains a plan parameter
      if (referer.includes("plan=monthly")) {
        planType = "monthly"
        console.log("Determined plan type from referer:", planType)
      } else if (referer.includes("plan=yearly")) {
        planType = "yearly"
        console.log("Determined plan type from referer:", planType)
      }
    }

    // If we still don't have a plan type, default to monthly
    if (!planType) {
      planType = "monthly"
      console.log("Using default plan type:", planType)
    }

    // Validate the plan type
    if (!["monthly", "yearly"].includes(planType)) {
      console.error("Invalid plan type:", planType)
      return NextResponse.redirect(new URL("/pricing", req.url))
    }

    // Get the appropriate price ID based on the plan type
    // Fallback to hardcoded values if environment variables are not set
    let priceId: string

    if (planType === "monthly") {
      priceId =
        process.env.STRIPE_PRICE_ID_MONTHLY || "price_1OvXXXXXXXXXXXXXXXXXXXXX"
      console.log("Using monthly price ID:", priceId)
    } else {
      priceId =
        process.env.STRIPE_PRICE_ID_YEARLY || "price_1OvXXXXXXXXXXXXXXXXXXXXX"
      console.log("Using yearly price ID:", priceId)
    }

    // Extract price IDs from payment links if environment variables are not set
    if (priceId.includes("XXXXXXX")) {
      // Try to extract from payment links
      const monthlyLink =
        process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_MONTHLY || ""
      const yearlyLink =
        process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_YEARLY || ""

      console.log("Payment links:", { monthlyLink, yearlyLink })

      // Use direct API call instead of payment links
      return NextResponse.redirect(
        new URL(planType === "monthly" ? monthlyLink : yearlyLink)
      )
    }

    // Create a Stripe checkout session
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
    console.log("Base URL for redirects:", baseUrl)

    const session = await stripe.checkout.sessions.create({
      billing_address_collection: "auto",
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: "subscription",
      success_url: `${baseUrl}/welcome?checkout_success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${baseUrl}/pricing?checkout_canceled=true`,
      client_reference_id: userId,
      // Add metadata to track the subscription type
      subscription_data: {
        metadata: {
          userId,
          planType
        }
      },
      // Add additional metadata to the session
      metadata: {
        userId,
        planType,
        source: "direct_checkout"
      }
    })

    console.log("Created checkout session:", {
      id: session.id,
      url: session.url,
      client_reference_id: session.client_reference_id
    })

    // Redirect to the Stripe checkout URL
    if (session.url) {
      return NextResponse.redirect(new URL(session.url))
    } else {
      return NextResponse.json(
        { error: "Failed to create checkout session" },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Error creating checkout session:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
