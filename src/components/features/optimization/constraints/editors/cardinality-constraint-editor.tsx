"use client"

import { Hash, Info } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { MultiSelect } from "@/components/ui/multi-select"
import {
  ContinuousCardinalityConstraint,
  DiscreteCardinalityConstraint
} from "@/lib/constraints"

type CardinalityConstraint =
  | ContinuousCardinalityConstraint
  | DiscreteCardinalityConstraint

interface CardinalityConstraintEditorProps {
  constraint: CardinalityConstraint
  availableParameters: string[]
  onChange: (updates: Partial<CardinalityConstraint>) => void
}

export function CardinalityConstraintEditor({
  constraint,
  availableParameters,
  onChange
}: CardinalityConstraintEditorProps) {
  const isContinuous = constraint.type === "ContinuousCardinalityConstraint"

  const handleParametersChange = (parameters: string[]) => {
    onChange({ parameters })
  }

  const handleMinCardinalityChange = (value: string) => {
    const numValue = parseInt(value) || 0
    onChange({ min_cardinality: numValue })
  }

  const handleMaxCardinalityChange = (value: string) => {
    const numValue = parseInt(value) || constraint.parameters.length
    onChange({ max_cardinality: numValue })
  }

  const handleRelativeThresholdChange = (value: string) => {
    const numValue = parseFloat(value) || 0.001
    onChange({ relative_threshold: numValue })
  }

  const getConstraintDescription = () => {
    const minCard = constraint.min_cardinality ?? 0
    const maxCard = constraint.max_cardinality ?? constraint.parameters.length
    const paramCount = constraint.parameters.length

    if (minCard === maxCard) {
      return `Exactly ${minCard} parameter${minCard !== 1 ? "s" : ""} must be active`
    } else if (minCard > 0 && maxCard < paramCount) {
      return `Between ${minCard} and ${maxCard} parameters must be active`
    } else if (minCard > 0) {
      return `At least ${minCard} parameter${minCard !== 1 ? "s" : ""} must be active`
    } else if (maxCard < paramCount) {
      return `At most ${maxCard} parameter${maxCard !== 1 ? "s" : ""} can be active`
    } else {
      return "No cardinality restrictions"
    }
  }

  const parameterOptions = availableParameters.map(param => ({
    value: param,
    label: param
  }))

  return (
    <div className="space-y-6">
      {/* Constraint Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Hash className="size-4" />
            Cardinality Constraint Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted rounded-md p-3 text-lg">
            {getConstraintDescription()}
          </div>
          <p className="text-muted-foreground mt-2 text-sm">
            {isContinuous
              ? "Controls how many continuous parameters have non-zero values"
              : "Controls how many discrete parameters are actively selected"}
          </p>
        </CardContent>
      </Card>

      {/* Parameter Selection */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-base font-medium">Parameters</Label>
          <Badge variant="outline">
            {constraint.parameters.length} selected
          </Badge>
        </div>

        <MultiSelect
          options={parameterOptions}
          value={constraint.parameters}
          onValueChange={handleParametersChange}
          placeholder="Select parameters to control..."
          maxCount={10}
        />
      </div>

      {/* Cardinality Settings */}
      <div className="space-y-4">
        <Label className="text-base font-medium">Cardinality Limits</Label>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="min-cardinality">Minimum Active</Label>
            <Input
              id="min-cardinality"
              type="number"
              min="0"
              max={constraint.parameters.length}
              value={constraint.min_cardinality ?? 0}
              onChange={e => handleMinCardinalityChange(e.target.value)}
              placeholder="0"
            />
            <p className="text-muted-foreground text-xs">
              Minimum number of parameters that must be active
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="max-cardinality">Maximum Active</Label>
            <Input
              id="max-cardinality"
              type="number"
              min="1"
              max={constraint.parameters.length}
              value={constraint.max_cardinality ?? constraint.parameters.length}
              onChange={e => handleMaxCardinalityChange(e.target.value)}
              placeholder={constraint.parameters.length.toString()}
            />
            <p className="text-muted-foreground text-xs">
              Maximum number of parameters that can be active
            </p>
          </div>
        </div>

        {/* Continuous-specific settings */}
        {isContinuous && (
          <div className="space-y-2">
            <Label htmlFor="relative-threshold">Relative Threshold</Label>
            <Input
              id="relative-threshold"
              type="number"
              step="0.001"
              min="0"
              max="1"
              value={
                (constraint as ContinuousCardinalityConstraint)
                  .relative_threshold ?? 0.001
              }
              onChange={e => handleRelativeThresholdChange(e.target.value)}
              placeholder="0.001"
            />
            <p className="text-muted-foreground text-xs">
              Threshold below which a parameter is considered inactive (default:
              0.001)
            </p>
          </div>
        )}
      </div>

      {/* Validation Alert */}
      {constraint.min_cardinality !== undefined &&
        constraint.max_cardinality !== undefined &&
        constraint.min_cardinality > constraint.max_cardinality && (
          <Alert variant="destructive">
            <AlertDescription>
              Minimum cardinality cannot be greater than maximum cardinality
            </AlertDescription>
          </Alert>
        )}

      {/* Information */}
      <Alert>
        <Info className="size-4" />
        <AlertDescription>
          <strong>How it works:</strong>{" "}
          {isContinuous
            ? "For continuous parameters, a parameter is considered 'active' if its value is above the relative threshold."
            : "For discrete parameters, a parameter is considered 'active' if it's selected in the current configuration."}
        </AlertDescription>
      </Alert>

      {/* Common Examples */}
      <Card className="bg-muted/30">
        <CardHeader>
          <CardTitle className="text-sm">Common Use Cases</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div>
            <strong>Mixture formulation:</strong> Use exactly 3 components in a
            blend
          </div>
          <div>
            <strong>Feature selection:</strong> Select at most 5 features for
            analysis
          </div>
          <div>
            <strong>Diversity requirement:</strong> Use at least 2 different
            solvents
          </div>
          <div>
            <strong>Simplicity constraint:</strong> Limit to maximum 4 active
            parameters
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
