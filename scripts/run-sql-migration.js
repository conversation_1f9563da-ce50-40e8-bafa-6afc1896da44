#!/usr/bin/env node

/**
 * This script runs a specific SQL migration file directly against the database.
 * Usage: node scripts/run-sql-migration.js <migration-file>
 * Example: node scripts/run-sql-migration.js db/migrations/0005_create_academic_verifications.sql
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

// Get the migration file from command line arguments
const migrationFile = process.argv[2];

if (!migrationFile) {
  console.error('❌ Please provide a migration file path');
  console.error('Usage: node scripts/run-sql-migration.js <migration-file>');
  process.exit(1);
}

// Check if the file exists
if (!fs.existsSync(migrationFile)) {
  console.error(`❌ Migration file not found: ${migrationFile}`);
  process.exit(1);
}

// Read the migration SQL
const sql = fs.readFileSync(migrationFile, 'utf8');

// Get database connection string from environment variables
const databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Create a PostgreSQL client
const client = new Client({
  connectionString: databaseUrl,
  ssl: {
    rejectUnauthorized: false
  }
});

async function runMigration() {
  try {
    console.log(`🔄 Running migration: ${migrationFile}`);
    
    // Connect to the database
    await client.connect();
    console.log('✅ Connected to database');
    
    // Run the migration
    await client.query(sql);
    
    console.log('✅ Migration completed successfully');
  } catch (error) {
    console.error('❌ Error running migration:', error.message);
    process.exit(1);
  } finally {
    // Close the database connection
    await client.end();
    console.log('👋 Disconnected from database');
  }
}

// Run the migration
runMigration();
