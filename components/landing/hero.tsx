/*
This client component provides the hero section for the landing page.
*/

"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Section } from "@/components/ui/section"
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion"
import {
  Rocket,
  LayoutDashboard,
  ArrowRight,
  Zap,
  LineChart,
  Lightbulb,
  Target
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { SignedIn, SignedOut } from "@clerk/nextjs"
import { useRef, useState } from "react"
import { useLoading } from "@/contexts/loading-context"
import { BRAND } from "@/lib/constants"

// Animation variants
const titleVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.8,
      delay: 0.4,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  }
}

const subtitleVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      delay: 0.6,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  }
}

const buttonVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      delay: 0.8,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  },
  hover: {
    scale: 1.05,
    boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.5)",
    transition: {
      duration: 0.3,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  },
  tap: {
    scale: 0.98,
    boxShadow: "0 5px 15px -5px rgba(59, 130, 246, 0.4)",
    transition: {
      duration: 0.15,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  }
}

const featureBadgeVariants = {
  hidden: { opacity: 0, scale: 0.8, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.5,
      delay: 0.8 + i * 0.1,
      ease: "easeOut"
    }
  }),
  hover: {
    scale: 1.05,
    y: -5,
    boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.2)",
    transition: { duration: 0.2 }
  }
}

// Key features to highlight
const keyFeatures = [
  {
    icon: <Zap className="size-4 text-blue-500" />,
    text: "AI-Powered"
  },
  {
    icon: <LineChart className="size-4 text-indigo-500" />,
    text: "Data-Driven"
  },
  {
    icon: <Lightbulb className="size-4 text-purple-500" />,
    text: "Intelligent"
  },
  {
    icon: <Target className="size-4 text-pink-500" />,
    text: "Precise"
  }
]

export const HeroSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null)
  const [isHovering, setIsHovering] = useState(false)

  const router = useRouter()
  const { startLoading } = useLoading()
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"]
  })

  // Handle dashboard navigation with loading overlay
  const handleDashboardClick = (
    e: React.MouseEvent<HTMLAnchorElement>,
    path: string
  ) => {
    e.preventDefault()
    startLoading()

    // Add a small delay before navigation to ensure the loading overlay appears
    setTimeout(() => {
      router.push(path)
    }, 100) // 100ms delay (reduced from 300ms)
  }

  // Parallax effects
  const titleY = useTransform(scrollYProgress, [0, 0.5], [0, -30])

  return (
    <Section
      ref={sectionRef}
      className="flex items-start justify-center pb-4 pt-8 md:pt-12 lg:pt-16"
      containerSize="lg"
      containerClassName="text-center h-full"
    >
      <div className="hero-content flex size-full flex-col items-center justify-start">
        <motion.div
          initial="hidden"
          animate="visible"
          className="hero-high-position mx-auto flex w-full max-w-2xl shrink-0 flex-col items-center justify-center gap-3 px-4 md:gap-5 lg:gap-6"
          style={{ y: titleY }}
        >
          {/* Animated badge above title with lightning effect */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-1 md:mb-3"
          >
            <div className="relative inline-block">
              {/* Multi-layer lightning border effect */}
              <div className="lightning-pulse absolute inset-0 rounded-full">
                <svg
                  className="absolute inset-0 size-full"
                  viewBox="0 0 120 120"
                  style={{ overflow: "visible" }}
                >
                  <defs>
                    {/* Primary lightning gradient */}
                    <linearGradient
                      id="lightningGradient1"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="0%"
                    >
                      <stop offset="0%" stopColor="rgba(59, 130, 246, 0)" />
                      <stop offset="30%" stopColor="rgba(59, 130, 246, 0.3)" />
                      <stop offset="50%" stopColor="rgba(59, 130, 246, 1)" />
                      <stop offset="70%" stopColor="rgba(147, 51, 234, 0.8)" />
                      <stop offset="100%" stopColor="rgba(59, 130, 246, 0)" />
                    </linearGradient>

                    {/* Secondary spark gradient */}
                    <linearGradient
                      id="lightningGradient2"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="0%"
                    >
                      <stop offset="0%" stopColor="rgba(147, 51, 234, 0)" />
                      <stop offset="50%" stopColor="rgba(147, 51, 234, 1)" />
                      <stop offset="100%" stopColor="rgba(147, 51, 234, 0)" />
                    </linearGradient>

                    {/* Glow filter */}
                    <filter id="glow">
                      <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                      <feMerge>
                        <feMergeNode in="coloredBlur" />
                        <feMergeNode in="SourceGraphic" />
                      </feMerge>
                    </filter>
                  </defs>

                  {/* Main lightning circle */}
                  <circle
                    cx="60"
                    cy="60"
                    r="55"
                    fill="none"
                    stroke="url(#lightningGradient1)"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeDasharray="8 4 2 4"
                    filter="url(#glow)"
                    className="electric-spark animate-spin"
                    style={{
                      animationDuration: "3s",
                      transformOrigin: "60px 60px"
                    }}
                  />

                  {/* Secondary spark circle */}
                  <circle
                    cx="60"
                    cy="60"
                    r="55"
                    fill="none"
                    stroke="url(#lightningGradient2)"
                    strokeWidth="1"
                    strokeLinecap="round"
                    strokeDasharray="4 8 1 8"
                    className="animate-spin"
                    style={{
                      animationDuration: "2s",
                      animationDirection: "reverse",
                      transformOrigin: "60px 60px"
                    }}
                  />

                  {/* Outer glow ring */}
                  <circle
                    cx="60"
                    cy="60"
                    r="58"
                    fill="none"
                    stroke="rgba(59, 130, 246, 0.2)"
                    strokeWidth="0.5"
                    strokeDasharray="1 2"
                    className="animate-spin"
                    style={{
                      animationDuration: "4s",
                      transformOrigin: "60px 60px"
                    }}
                  />
                </svg>
              </div>

              {/* Floating spark particles */}
              <div className="pointer-events-none absolute inset-0">
                <div
                  className="absolute left-8 top-2 size-1 animate-ping rounded-full bg-blue-400"
                  style={{ animationDelay: "0s", animationDuration: "2s" }}
                ></div>
                <div
                  className="absolute right-4 top-6 size-0.5 animate-ping rounded-full bg-purple-400"
                  style={{ animationDelay: "0.5s", animationDuration: "1.5s" }}
                ></div>
                <div
                  className="absolute bottom-3 left-4 size-0.5 animate-ping rounded-full bg-blue-300"
                  style={{ animationDelay: "1s", animationDuration: "2.5s" }}
                ></div>
                <div
                  className="absolute bottom-5 right-8 size-1 animate-ping rounded-full bg-purple-300"
                  style={{ animationDelay: "1.5s", animationDuration: "1.8s" }}
                ></div>
              </div>

              {/* Main badge content */}
              <span className="bg-primary/10 text-primary border-primary/20 relative inline-flex items-center rounded-full border px-4 py-2 text-sm font-medium shadow-lg backdrop-blur-sm">
                <span className="mr-2 flex size-2">
                  <span className="bg-primary absolute inline-flex size-2 animate-ping rounded-full opacity-75"></span>
                  <span className="bg-primary relative inline-flex size-2 rounded-full"></span>
                </span>
                New: Advanced Optimization Engine
              </span>
            </div>
          </motion.div>

          <motion.h1
            variants={titleVariants}
            className="relative text-balance text-2xl font-bold sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            <span className="from-primary bg-gradient-to-r to-blue-600 bg-clip-text text-transparent">
              {BRAND.NAME}
            </span>
            {/* Subtle icon animations on hover */}
            <AnimatePresence>
              {isHovering && (
                <>
                  <motion.div
                    className="absolute -right-6 top-1/2 -translate-y-1/2 text-blue-500/70"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Target className="size-5" />
                  </motion.div>
                  <motion.div
                    className="absolute -left-6 top-1/2 -translate-y-1/2 text-indigo-500/70"
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <LineChart className="size-5" />
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </motion.h1>

          <motion.div
            variants={subtitleVariants}
            className="text-muted-foreground max-w-xl text-balance px-2 text-sm leading-normal md:text-base md:leading-normal lg:text-lg"
          >
            <span className="text-foreground font-medium">
              Revolutionize your research
            </span>{" "}
            with intelligent experimental design that delivers breakthrough
            results
            <span className="text-blue-500"> up to 70% faster</span>.
          </motion.div>

          {/* Feature badges */}
          <motion.div className="mt-3 flex flex-wrap justify-center gap-2 md:mt-4 md:gap-3">
            {keyFeatures.map((feature, i) => (
              <motion.div
                key={i}
                custom={i}
                variants={featureBadgeVariants}
                whileHover="hover"
                className="bg-background/50 flex items-center gap-1 rounded-full px-2 py-1 text-xs backdrop-blur-sm md:gap-1.5 md:px-3 md:text-sm"
              >
                {feature.icon}
                {feature.text}
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            variants={buttonVariants}
            className="mt-3 flex w-full flex-col gap-3 sm:w-auto sm:flex-row sm:gap-4 md:mt-5 lg:mt-6"
          >
            <SignedOut>
              <Link href="/signup" prefetch={false}>
                <motion.div whileHover="hover" whileTap="tap">
                  <Button className="from-primary hover:from-primary/90 min-h-[48px] w-full bg-gradient-to-r to-blue-600 p-4 text-sm font-semibold shadow-lg shadow-blue-500/30 hover:to-blue-700 sm:min-w-40 md:min-w-48 md:px-8 md:py-6 md:text-base lg:text-lg">
                    <Rocket className="mr-2 size-5" />
                    Start for Free
                    <ArrowRight className="ml-2 size-4" />
                  </Button>
                </motion.div>
              </Link>
            </SignedOut>

            <SignedIn>
              <Link
                href="/dashboard/optimizations"
                prefetch={false}
                onClick={e =>
                  handleDashboardClick(e, "/dashboard/optimizations")
                }
              >
                <motion.div whileHover="hover" whileTap="tap">
                  <Button className="from-primary hover:from-primary/90 min-h-[48px] w-full bg-gradient-to-r to-blue-600 p-4 text-sm font-semibold shadow-lg shadow-blue-500/30 hover:to-blue-700 sm:min-w-40 md:min-w-48 md:px-8 md:py-6 md:text-base lg:text-lg">
                    <LayoutDashboard className="mr-2 size-5" />
                    Go to Dashboard
                  </Button>
                </motion.div>
              </Link>
            </SignedIn>
          </motion.div>
        </motion.div>
      </div>
    </Section>
  )
}
