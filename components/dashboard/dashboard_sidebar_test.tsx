/*
This client component provides the dashboard sidebar with navigation, account section,
collapsible functionality, and subscription promotion.
*/

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useUser, useClerk } from "@clerk/nextjs"
import { useLoading } from "@/contexts/loading-context"
import { BRAND } from "@/lib/constants"

import {
  BarChart,
  HelpCircle,
  History,
  LogOut,
  ChevronDown,
  LayoutDashboard,
  FileCog,
  Sun,
  Moon,
  ChevronLeft,
  ChevronRight,
  MessageSquarePlus,
  Sparkles,
  X,
  BellRing,
  User,
  Wrench,
  BookOpen,
  ArrowRight
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useTheme } from "next-themes"
import { motion } from "framer-motion"
import Image from "next/image"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { SubscriptionPromo } from "@/components/dashboard/subscription-promo"
import { ApiStatus } from "@/components/dashboard/api-status"
import { FeedbackDialog } from "@/components/feedback/feedback-dialog"
import { isFeatureEnabled, isFeatureInDevelopment } from "@/lib/feature-flags"
import { DevelopmentBadge } from "@/components/ui/development-badge"
// Define a simplified user type to avoid import issues
type UserType = {
  fullName?: string | null
  emailAddresses?: Array<{ emailAddress: string }>
}

// Client-side only component to prevent hydration mismatch
function UserNameDisplay({ user }: { user: UserType | null | undefined }) {
  const [displayName, setDisplayName] = useState("")

  useEffect(() => {
    // Only run on client-side to prevent hydration mismatch
    if (user) {
      setDisplayName(
        user.fullName || user.emailAddresses?.[0]?.emailAddress || ""
      )
    }
  }, [user])

  return <p className="truncate text-sm font-medium">{displayName}</p>
}

const dashboardLinks = [
  {
    href: "/dashboard/home",
    label: "Home",
    icon: <LayoutDashboard className="size-5" />
  },
  {
    href: "/dashboard/optimizations",
    label: "Optimizations",
    icon: <FileCog className="size-5" />
  },
  {
    href: "/dashboard/learning",
    label: "Learning Resources",
    icon: <BookOpen className="size-5" />
  }
]

// Beta Feedback Note Component
// Beta Feedback Note Component
function BetaFeedbackNote({ isCollapsed }: { isCollapsed: boolean }) {
  const [isDismissed, setIsDismissed] = useState(false)
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false)

  if (isDismissed) return null

  if (isCollapsed) {
    return (
      <div className="px-2 py-3">
        <Button
          variant="outline"
          className="relative h-10 w-full border-2 border-dashed border-blue-600 p-0"
          onClick={() => setFeedbackDialogOpen(true)}
        >
          <div className="flex size-full items-center justify-center">
            <Sparkles className="size-5 text-blue-600" />
          </div>
        </Button>

        {/* Feedback Dialog */}
        <FeedbackDialog
          isOpen={feedbackDialogOpen}
          onClose={() => setFeedbackDialogOpen(false)}
          initialMetadata={{ source: "beta_note" }}
        />
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative m-3 overflow-hidden rounded-lg border-2 border-dashed border-blue-600 p-3"
    >
      <button
        className="text-muted-foreground hover:bg-accent hover:text-accent-foreground absolute right-2 top-2 rounded-full p-0.5"
        onClick={() => setIsDismissed(true)}
      >
        <X className="size-3" />
      </button>

      <div className="mb-2 flex justify-center">
        <motion.div
          animate={{
            scale: [1, 1.05, 1]
          }}
          transition={{
            repeat: Infinity,
            duration: 2,
            ease: "easeInOut"
          }}
          className="relative size-24 overflow-hidden rounded-lg"
        >
          <div className="flex size-full items-center justify-center bg-blue-600 text-white">
            <div className="text-center">
              <Sparkles className="mx-auto mb-1 size-8" />
              <div className="text-sm font-bold">BETA</div>
            </div>
          </div>
        </motion.div>
      </div>

      <h4 className="mb-1 text-center text-sm font-semibold">Beta Product</h4>
      <p className="text-muted-foreground mb-3 text-center text-xs">
        Your feedback is highly important to improve the {BRAND.NAME}. We're
        actively enhancing the product based on your valuable input.
      </p>

      <Button
        size="lg"
        className="w-full gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 
                  px-8 shadow-lg transition-all duration-300 
                  hover:scale-105 hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl"
        onClick={() => setFeedbackDialogOpen(true)}
      >
        <MessageSquarePlus className="size-5" />
        Share Feedback
        <ArrowRight className="size-4" />
      </Button>

      {/* Feedback Dialog */}
      <FeedbackDialog
        isOpen={feedbackDialogOpen}
        onClose={() => setFeedbackDialogOpen(false)}
        initialMetadata={{ source: "beta_note" }}
      />
    </motion.div>
  )
}

export function DashboardSidebar() {
  const { user } = useUser()
  const { signOut } = useClerk()
  const pathname = usePathname()
  const { theme, setTheme } = useTheme()
  const [collapsed, setCollapsed] = useState(false)
  const router = useRouter()
  const { startLoading } = useLoading()
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false)

  // Handle navigation with loading overlay
  const handleNavigateWithLoading = (
    e: React.MouseEvent<HTMLAnchorElement>,
    path: string
  ) => {
    e.preventDefault()
    startLoading()

    // Add a small delay before navigation to ensure the loading overlay appears
    setTimeout(() => {
      router.push(path)
    }, 100) // 100ms delay (reduced from 300ms)
  }

  // This will be replaced with actual user subscription data in a real implementation
  const [userTier, setUserTier] = useState<
    | "free"
    | "trial"
    | "trial-expired"
    | "basic"
    | "advanced"
    | "premium"
    | "premium-monthly"
    | "premium-yearly"
  >("trial")
  const [trialDaysLeft, setTrialDaysLeft] = useState(30)

  // Fetch subscription info from API
  useEffect(() => {
    const fetchSubscriptionInfo = async () => {
      try {
        const response = await fetch("/api/user/subscription")
        if (response.ok) {
          const data = await response.json()
          console.log("Subscription data:", data) // Log for debugging

          // Map the subscription tier to our UI tiers
          if (data.tier === "trial") {
            setUserTier("trial")

            // Use the actual trial days left from the database
            // If it's undefined or invalid, calculate from trialEndsAt
            if (
              typeof data.trialDaysLeft === "number" &&
              !isNaN(data.trialDaysLeft)
            ) {
              setTrialDaysLeft(data.trialDaysLeft)
            } else if (data.trialEndsAt) {
              // Calculate days left from trialEndsAt
              const now = new Date()
              const trialEndDate = new Date(data.trialEndsAt)
              const daysLeft = Math.max(
                0,
                Math.round(
                  (trialEndDate.getTime() - now.getTime()) /
                    (1000 * 60 * 60 * 24)
                )
              )
              setTrialDaysLeft(daysLeft)
            } else {
              // Fallback to default
              setTrialDaysLeft(30)
            }
          } else if (data.tier === "trial-expired") {
            setUserTier("trial-expired")
            setTrialDaysLeft(0)
          } else if (data.tier === "pro") {
            // Set the tier based on subscription type
            if (data.subscriptionType === "monthly") {
              setUserTier("premium-monthly")
            } else if (data.subscriptionType === "yearly") {
              setUserTier("premium-yearly")
            } else {
              // Default to premium if subscription type is not specified
              setUserTier("premium")
            }
          } else if (data.tier === "free") {
            setUserTier("free")
          } else {
            setUserTier("basic")
          }
        }
      } catch (error) {
        console.error("Error fetching subscription info:", error)
        // Default to trial if there's an error
        setUserTier("trial")
        setTrialDaysLeft(30)
      }
    }

    fetchSubscriptionInfo()
  }, [])

  // Check if we're on mobile and collapse sidebar if needed
  useEffect(() => {
    const checkMobile = () => {
      if (window.innerWidth < 768) {
        setCollapsed(true)
      }
    }

    // Check initially
    checkMobile()

    // Add event listener
    window.addEventListener("resize", checkMobile)

    // Clean up
    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  // Get user initials for avatar fallback - client-side only
  const [initials, setInitials] = useState("U")

  useEffect(() => {
    // Only run this on the client side to prevent hydration mismatch
    if (user) {
      if (user.firstName || user.lastName) {
        setInitials(
          `${user.firstName?.charAt(0) || ""}${user.lastName?.charAt(0) || ""}`
        )
      } else if (user.emailAddresses && user.emailAddresses.length > 0) {
        setInitials(user.emailAddresses[0].emailAddress.charAt(0).toUpperCase())
      }
    }
  }, [user])

  // Get plan name based on tier
  const getPlanName = () => {
    switch (userTier) {
      case "free":
        return "Free Plan"
      case "trial":
        return "Trial"
      case "trial-expired":
        return "Trial Expired"
      case "basic":
        return "Basic Plan"
      case "advanced":
        return "Advanced Plan"
      case "premium":
        return "Premium Plan"
      case "premium-monthly":
        return "Premium Monthly"
      case "premium-yearly":
        return "Premium Yearly"
      default:
        return "Free Plan"
    }
  }

  return (
    <div
      className={cn(
        "bg-sidebar border-sidebar-border flex h-screen flex-col border-r transition-all duration-300",
        collapsed ? "w-[80px]" : "w-72"
      )}
    >
      {/* Logo and App name */}
      <div
        className={cn(
          "flex items-center gap-2 p-4 transition-all",
          collapsed ? "justify-center" : ""
        )}
      >
        <Image
          src="/icon.svg" // file should be in /public/icon.svg
          alt="App logo"
          width={34}
          height={34}
          className="shrink-0"
        />
        {!collapsed && (
          <span className="space-x-2 whitespace-nowrap text-xl font-bold">
            {BRAND.NAME}
          </span>
        )}
      </div>

      {/* Collapse/expand button */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "my-1 mr-2 size-8 self-end p-0",
          collapsed && "mr-0 self-center"
        )}
        onClick={() => setCollapsed(!collapsed)}
      >
        {collapsed ? (
          <ChevronRight className="size-4" />
        ) : (
          <ChevronLeft className="size-4" />
        )}
      </Button>

      {/* Main navigation */}
      <nav className="flex-1 p-2">
        <ul className="space-y-1">
          {dashboardLinks.map(link => (
            <li key={link.href}>
              <TooltipProvider delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link
                      href={link.href}
                      onClick={e => handleNavigateWithLoading(e, link.href)}
                      className={cn(
                        "flex items-center gap-3 rounded-md py-2 text-sm font-medium transition-colors",
                        pathname === link.href
                          ? "bg-accent text-accent-foreground"
                          : "hover:bg-accent/50 hover:text-accent-foreground",
                        collapsed ? "justify-center px-2" : "px-3"
                      )}
                    >
                      {link.icon}
                      {!collapsed && (
                        <span className="truncate">{link.label}</span>
                      )}
                    </Link>
                  </TooltipTrigger>
                  {collapsed && (
                    <TooltipContent side="right">{link.label}</TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            </li>
          ))}
        </ul>
      </nav>

      {/* Beta Feedback Note */}
      <BetaFeedbackNote isCollapsed={collapsed} />

      {/* Subscription promo section */}
      <SubscriptionPromo
        currentTier={userTier}
        trialDaysLeft={trialDaysLeft}
        isCollapsed={collapsed}
      />

      {/* API Status section */}
      <ApiStatus isCollapsed={collapsed} />

      {/* Feedback button */}
      <div className="border-t p-2">
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "flex w-full items-center",
                  collapsed ? "justify-center px-2" : "px-3"
                )}
                onClick={() => setFeedbackDialogOpen(true)}
              >
                <MessageSquarePlus
                  className={cn("size-4", collapsed ? "" : "mr-2")}
                />
                {!collapsed && <span>Provide Feedback</span>}
              </Button>
            </TooltipTrigger>
            {collapsed && (
              <TooltipContent side="right">Provide Feedback</TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* User account section */}
      <div className="mt-auto border-t p-2">
        <DropdownMenu>
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      "hover:bg-accent/50 w-full justify-start truncate",
                      collapsed ? "px-2" : "px-3"
                    )}
                  >
                    <div
                      className={cn(
                        "flex items-center",
                        collapsed ? "w-full justify-center" : "w-full"
                      )}
                    >
                      <Avatar className={cn("size-8", collapsed ? "" : "mr-2")}>
                        <AvatarImage
                          src={user?.imageUrl}
                          alt={user?.fullName || ""}
                        />
                        <AvatarFallback>{initials}</AvatarFallback>
                      </Avatar>
                      {!collapsed && (
                        <>
                          <div className="flex-1 text-left">
                            <UserNameDisplay user={user} />
                            <p className="text-muted-foreground text-xs">
                              {getPlanName()}
                            </p>
                          </div>
                          <ChevronDown className="size-4" />
                        </>
                      )}
                    </div>
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              {collapsed && (
                <TooltipContent side="right">
                  <UserNameDisplay user={user} />
                  <span className="text-muted-foreground text-xs">
                    {getPlanName()}
                  </span>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem asChild>
              <Link
                href="/dashboard/profile"
                onClick={e =>
                  handleNavigateWithLoading(e, "/dashboard/profile")
                }
                className="flex cursor-pointer items-center font-medium"
              >
                <User className="mr-2 size-4" />
                <span>My Account</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />

            {/* Theme Toggle */}
            <DropdownMenuItem className="focus:bg-background cursor-default">
              <div className="flex w-full flex-col space-y-1">
                <span className="mb-1 text-sm font-medium">Theme</span>
                <div className="flex flex-col space-y-1">
                  <label className="flex cursor-pointer items-center space-x-2">
                    <input
                      type="radio"
                      name="theme"
                      checked={theme === "light"}
                      onChange={() => setTheme("light")}
                      className="size-4"
                    />
                    <span className="flex items-center text-sm">
                      <Sun className="mr-1 size-4" /> Light
                    </span>
                  </label>
                  <label className="flex cursor-pointer items-center space-x-2">
                    <input
                      type="radio"
                      name="theme"
                      checked={theme === "dark"}
                      onChange={() => setTheme("dark")}
                      className="size-4"
                    />
                    <span className="flex items-center text-sm">
                      <Moon className="mr-1 size-4" /> Dark
                    </span>
                  </label>
                </div>
              </div>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem asChild>
              <Link
                href="/dashboard/help"
                onClick={e => handleNavigateWithLoading(e, "/dashboard/help")}
                className="flex cursor-pointer items-center"
              >
                <HelpCircle className="mr-2 size-4" />
                <span>Help Center</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link
                href="/dashboard/history"
                onClick={e =>
                  handleNavigateWithLoading(e, "/dashboard/history")
                }
                className="flex cursor-pointer items-center"
              >
                <History className="mr-2 size-4" />
                <span className="flex items-center gap-1">
                  History
                  {isFeatureInDevelopment("historyTabContent") && (
                    <DevelopmentBadge variant="icon" size="sm" />
                  )}
                </span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link
                href="/dashboard/billing"
                onClick={e =>
                  handleNavigateWithLoading(e, "/dashboard/billing")
                }
                className="flex cursor-pointer items-center"
              >
                <BarChart className="mr-2 size-4" />
                <span>Billing</span>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={() => {
                signOut(() => router.push("/"))
              }}
              className="flex cursor-pointer items-center text-red-500"
            >
              <LogOut className="mr-2 size-4" />
              <span>Sign out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Feedback Dialog */}
      <FeedbackDialog
        isOpen={feedbackDialogOpen}
        onClose={() => setFeedbackDialogOpen(false)}
        initialMetadata={{ currentPath: pathname }}
      />
    </div>
  )
}