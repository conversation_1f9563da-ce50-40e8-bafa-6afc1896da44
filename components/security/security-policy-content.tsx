"use client"

import { motion } from "framer-motion"
import {
  Shield,
  FileText,
  Calendar,
  User,
  AlertTriangle,
  Download
} from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

export function SecurityPolicyContent() {
  const handleDownloadDraft = () => {
    // Create the full policy content as text
    const policyContent = `
INNOptimizer™ Information Security Policy
========================================

DRAFT VERSION 1.0
Document Status: DRAFT - Under Review
Effective Date: TBD
Owner: Chief Technology Officer
Approved By: Pending Executive Approval

========================================

1. EXECUTIVE SUMMARY

This Information Security Policy establishes the framework for protecting information assets, systems, and data within INNOptimizer™. This policy applies to all employees, contractors, partners, and users of our Bayesian optimization platform.

Our Commitment: We are committed to maintaining the confidentiality, integrity, and availability of all information entrusted to us while enabling scientific innovation through secure optimization services.

2. SCOPE AND APPLICABILITY

2.1 Scope
This policy covers:
- All INNOptimizer™ systems, applications, and infrastructure
- Customer optimization data and experimental results
- Employee and user personal information
- Business operations and intellectual property
- Third-party integrations and service providers

2.2 Applicability
This policy applies to:
- All INNOptimizer™ employees and contractors
- All users of the optimization platform
- Third-party service providers with access to our systems
- Business partners and collaborators

3. INFORMATION SECURITY GOVERNANCE

3.1 Security Organization
- Chief Technology Officer: Overall security accountability
- Security Team: Day-to-day security operations and incident response
- Development Team: Secure coding practices and security by design
- Operations Team: Infrastructure security and monitoring

3.2 Roles and Responsibilities

Management Responsibilities:
- Provide adequate resources for information security
- Ensure compliance with legal and regulatory requirements
- Review and approve security policies and procedures
- Support security awareness and training programs

Employee Responsibilities:
- Comply with all security policies and procedures
- Report security incidents and vulnerabilities
- Protect confidential information and access credentials
- Complete mandatory security training

User Responsibilities:
- Use strong, unique passwords for account access
- Report suspicious activities or security concerns
- Comply with acceptable use policies
- Protect optimization data and experimental results

4. INFORMATION CLASSIFICATION AND HANDLING

4.1 Data Classification

Public Information:
- Marketing materials and public documentation
- Published research and case studies
- General product information

Internal Information:
- Business operations data
- Employee information (non-sensitive)
- System configuration details
- Performance metrics and analytics

Confidential Information:
- Customer optimization experiments and results
- User personal information and account data
- Business strategies and financial information
- Source code and technical specifications

Restricted Information:
- Authentication credentials and API keys
- Encryption keys and security certificates
- Legal documents and contracts
- Sensitive personal information (academic verification)

4.2 Data Handling Requirements

Confidential Data:
- Encrypt in transit and at rest using AES-256
- Implement access controls and user authentication
- Log all access and processing activities
- Retain according to defined retention schedules

Restricted Data:
- Apply strongest encryption and access controls
- Limit access to authorized personnel only
- Implement multi-factor authentication
- Conduct regular access reviews and audits

5. ACCESS CONTROL AND AUTHENTICATION

5.1 User Access Management
- Principle of Least Privilege: Users receive minimum access necessary
- Role-Based Access Control: Access based on job functions and responsibilities
- Regular Access Reviews: Quarterly review of user access rights
- Prompt Deprovisioning: Immediate removal of access upon termination

5.2 Authentication Requirements
- Strong Passwords: Minimum 12 characters with complexity requirements
- Multi-Factor Authentication: Required for administrative and sensitive access
- Session Management: Automatic timeout and secure session handling
- Account Lockout: Protection against brute force attacks

5.3 Privileged Access Management
- Administrative Accounts: Separate from regular user accounts
- Privileged Access Monitoring: Enhanced logging and monitoring
- Just-in-Time Access: Temporary elevation for specific tasks
- Regular Privilege Reviews: Monthly review of administrative access

6. TECHNICAL SECURITY CONTROLS

6.1 Network Security
- Firewall Protection: Network segmentation and traffic filtering
- Intrusion Detection: Real-time monitoring for malicious activities
- VPN Access: Secure remote access for employees
- Network Monitoring: Continuous monitoring of network traffic

6.2 Endpoint Security
- Antivirus Protection: Real-time malware detection and prevention
- Patch Management: Regular security updates and patches
- Device Encryption: Full disk encryption for all devices
- Mobile Device Management: Security controls for mobile devices

6.3 Application Security
- Secure Development: Security by design principles
- Code Reviews: Regular security code reviews
- Vulnerability Scanning: Automated security testing
- Penetration Testing: Annual third-party security assessments

6.4 Data Protection
- Encryption Standards: AES-256 for data at rest, TLS 1.3 for data in transit
- Key Management: Hardware Security Module (HSM) for key protection
- Data Loss Prevention: Monitoring and prevention of data exfiltration
- Backup Security: Encrypted and geographically distributed backups

7. INCIDENT RESPONSE AND MANAGEMENT

7.1 Incident Response Team
- Incident Commander: Overall incident coordination
- Technical Lead: Technical investigation and remediation
- Communications Lead: Internal and external communications
- Legal Counsel: Legal and regulatory compliance

7.2 Incident Response Process
1. Detection and Analysis: Identify and assess security incidents
2. Containment: Isolate affected systems and prevent spread
3. Eradication: Remove threats and vulnerabilities
4. Recovery: Restore systems and services
5. Lessons Learned: Post-incident review and improvement

7.3 Incident Classification
- Critical: Significant impact on operations or data breach
- High: Moderate impact with potential for escalation
- Medium: Limited impact with contained scope
- Low: Minimal impact with no immediate threat

7.4 Response Timeframes
- Critical Incidents: Initial response within 1 business day
- High Incidents: Initial response within 2 business days
- Medium Incidents: Initial response within 3 business days
- Low Incidents: Initial response within 5 business days

8. BUSINESS CONTINUITY AND DISASTER RECOVERY

8.1 Business Continuity Planning
- Risk Assessment: Regular assessment of business continuity risks
- Continuity Plans: Documented procedures for business continuity
- Testing: Annual testing of continuity plans
- Recovery Objectives: Defined RTO and RPO for critical systems

8.2 Disaster Recovery
- Backup Strategy: Regular automated backups with geographic distribution
- Recovery Procedures: Documented recovery procedures for all systems
- Alternative Sites: Backup infrastructure for critical operations
- Communication Plans: Emergency communication procedures

9. COMPLIANCE AND LEGAL REQUIREMENTS

9.1 Regulatory Compliance
- GDPR: European General Data Protection Regulation compliance framework
- CCPA: California Consumer Privacy Act compliance framework
- SOC 2: Service Organization Control 2 Type II readiness program
- ISO 27001: Information Security Management System aligned practices

9.2 Data Protection Rights
- Right to Access: Users can view all personal data we hold
- Right to Rectification: Users can correct inaccurate information
- Right to Erasure: Users can request deletion of personal data
- Right to Portability: Users can export data in standard formats
- Right to Restrict Processing: Users can limit data processing
- Right to Object: Users can object to certain processing activities

9.3 Legal Obligations
- Data Breach Notification: Notification within 72 hours of discovery
- Data Protection Impact Assessments: For high-risk processing activities
- Privacy by Design: Privacy considerations in all system designs
- Record Keeping: Detailed records of processing activities

10. VENDOR AND THIRD-PARTY MANAGEMENT

10.1 Vendor Security Requirements
- Security Assessments: Evaluation of vendor security practices
- Contractual Requirements: Security clauses in all vendor contracts
- Ongoing Monitoring: Regular review of vendor security posture
- Incident Coordination: Joint incident response procedures

10.2 Approved Vendors
- Clerk: Authentication and user management services
- Stripe: Payment processing services
- Amazon Web Services (AWS): Database hosting and cloud infrastructure services
- Vercel: Application hosting and deployment

11. SECURITY AWARENESS AND TRAINING

11.1 Security Training Program
- New Employee Training: Security orientation for all new hires
- Annual Training: Mandatory annual security awareness training
- Role-Specific Training: Specialized training for technical roles
- Phishing Simulation: Regular phishing awareness exercises

11.2 Security Communications
- Security Bulletins: Regular security updates and alerts
- Incident Notifications: Communication of security incidents
- Policy Updates: Notification of policy changes
- Best Practices: Sharing of security best practices

12. MONITORING AND AUDITING

12.1 Security Monitoring
- 24/7 Monitoring: Continuous monitoring of security events
- SIEM System: Security Information and Event Management
- Threat Intelligence: Integration of threat intelligence feeds
- Automated Alerting: Real-time alerts for security incidents

12.2 Audit and Compliance
- Internal Audits: Regular internal security audits
- External Audits: Annual third-party security assessments
- Compliance Reviews: Quarterly compliance assessments
- Penetration Testing: Annual penetration testing

13. POLICY ENFORCEMENT AND VIOLATIONS

13.1 Policy Violations
Violations of this policy may result in:
- Verbal or written warnings
- Suspension of system access
- Disciplinary action up to and including termination
- Legal action for criminal violations

13.2 Reporting Violations
- Security Team: <EMAIL>
- Anonymous Reporting: Available through internal systems
- No Retaliation: Protection for good faith reporting

14. POLICY REVIEW AND UPDATES

14.1 Review Schedule
- Annual Review: Comprehensive policy review and update
- Quarterly Assessment: Review of policy effectiveness
- Incident-Driven Updates: Updates based on security incidents
- Regulatory Changes: Updates for new legal requirements

14.2 Approval Process
- Policy Owner: CTO reviews and approves policy changes
- Executive Approval: CEO approval for major policy changes
- Communication: All policy changes communicated to stakeholders
- Training Updates: Training materials updated for policy changes

15. CONTACT INFORMATION

Security Contacts:
- Security Issues: <EMAIL> (business days response)
- Privacy & Data Rights: <EMAIL> (business days response)

Emergency Contacts:
- Security Hotline: [Emergency Phone Number]
- After-Hours Contact: [Emergency Contact Information]

========================================

Document Control:
- Version: 1.0 (DRAFT)
- Last Updated: ${new Date().toLocaleDateString()}
- Next Review: [Date + 1 Year]
- Document Owner: Chief Technology Officer
- Approved By: Pending Executive Approval

========================================

DRAFT NOTICE: This is a draft document under review.
Final version will be published upon executive approval.

This Security Policy is a living document that will be regularly
updated to reflect changes in our business, technology, and
regulatory environment. All employees and stakeholders are
responsible for understanding and complying with this policy.
`

    // Create and download the file
    const blob = new Blob([policyContent], { type: "text/plain" })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = `INNOptimizer_Security_Policy_DRAFT_v1.0_${new Date().toISOString().split("T")[0]}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="relative min-h-screen">
      {/* Draft Watermark */}
      <div className="pointer-events-none fixed inset-0 z-10 flex items-center justify-center">
        <div
          className="-rotate-45 select-none font-bold text-gray-200 opacity-10"
          style={{ fontSize: "8rem", letterSpacing: "0.2em" }}
        >
          DRAFT
        </div>
      </div>

      {/* Header Section */}
      <section className="relative z-20 bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-16">
        <div className="container mx-auto max-w-4xl px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="mx-auto mb-6 flex size-16 items-center justify-center rounded-full bg-blue-100">
              <Shield className="size-8 text-blue-600" />
            </div>
            <h1 className="mb-4 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
              Information Security Policy
            </h1>
            <p className="mx-auto mb-6 max-w-2xl text-lg text-gray-600">
              Comprehensive security governance framework for INNOptimizer™
              platform
            </p>

            {/* Document Info */}
            <div className="mb-8 flex flex-wrap justify-center gap-4">
              <Badge variant="outline" className="flex items-center gap-2">
                <FileText className="size-4" />
                Version 1.0 (Draft)
              </Badge>
              <Badge variant="outline" className="flex items-center gap-2">
                <Calendar className="size-4" />
                Effective: TBD
              </Badge>
              <Badge variant="outline" className="flex items-center gap-2">
                <User className="size-4" />
                Owner: CTO
              </Badge>
            </div>

            {/* Draft Notice */}
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="pt-6">
                <div className="flex items-center justify-center gap-3">
                  <AlertTriangle className="size-5 text-yellow-600" />
                  <p className="font-medium text-yellow-800">
                    This is a draft document under review. Final version will be
                    published upon approval.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Table of Contents */}
      <section className="relative z-20 bg-gray-50 py-12">
        <div className="container mx-auto max-w-4xl px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Table of Contents</CardTitle>
                <CardDescription>
                  Navigate through our comprehensive security policy sections
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                  {[
                    "1. Executive Summary",
                    "2. Scope and Applicability",
                    "3. Information Security Governance",
                    "4. Information Classification",
                    "5. Access Control & Authentication",
                    "6. Technical Security Controls",
                    "7. Incident Response",
                    "8. Business Continuity",
                    "9. Compliance & Legal Requirements",
                    "10. Vendor Management",
                    "11. Security Training",
                    "12. Monitoring & Auditing",
                    "13. Policy Enforcement",
                    "14. Policy Review",
                    "15. Contact Information"
                  ].map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center rounded p-2 hover:bg-gray-50"
                    >
                      <div className="mr-3 size-2 rounded-full bg-blue-600" />
                      <span className="text-sm text-gray-700">{item}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Policy Sections Preview */}
      <section className="relative z-20 py-12">
        <div className="container mx-auto max-w-4xl px-4">
          <div className="space-y-8">
            {/* Executive Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl">
                    1. Executive Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="prose prose-sm max-w-none">
                  <p>
                    This Information Security Policy establishes the framework
                    for protecting information assets, systems, and data within
                    INNOptimizer™. This policy applies to all employees,
                    contractors, partners, and users of our Bayesian
                    optimization platform.
                  </p>
                  <p className="font-semibold text-blue-600">
                    Our Commitment: We are committed to maintaining the
                    confidentiality, integrity, and availability of all
                    information entrusted to us while enabling scientific
                    innovation through secure optimization services.
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            {/* Key Highlights */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl">Policy Highlights</CardTitle>
                  <CardDescription>
                    Key security principles and commitments outlined in this
                    policy
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <h4 className="mb-3 font-semibold text-gray-900">
                        Data Protection
                      </h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• AES-256 encryption for data at rest</li>
                        <li>• TLS 1.3 for data in transit</li>
                        <li>• Complete user data isolation</li>
                        <li>• Automated retention management</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="mb-3 font-semibold text-gray-900">
                        Access Controls
                      </h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Multi-factor authentication</li>
                        <li>• Role-based access control</li>
                        <li>• Principle of least privilege</li>
                        <li>• Regular access reviews</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="mb-3 font-semibold text-gray-900">
                        Compliance
                      </h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• GDPR and CCPA compliance framework</li>
                        <li>• SOC 2 Type II readiness program</li>
                        <li>• ISO 27001 aligned practices</li>
                        <li>• Ongoing compliance assessments</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="mb-3 font-semibold text-gray-900">
                        Incident Response
                      </h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Continuous security monitoring</li>
                        <li>• Business days response commitment</li>
                        <li>• Dedicated security team</li>
                        <li>• Comprehensive recovery procedures</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl">Security Contacts</CardTitle>
                  <CardDescription>
                    Contact information for security-related inquiries and
                    incidents
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                      <h4 className="mb-2 font-semibold text-red-900">
                        Security Issues
                      </h4>
                      <p className="mb-1 text-sm text-red-700">
                        <EMAIL>
                      </p>
                      <p className="text-xs text-red-600">
                        Vulnerabilities & incidents
                      </p>
                    </div>
                    <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                      <h4 className="mb-2 font-semibold text-blue-900">
                        Privacy & Data Rights
                      </h4>
                      <p className="mb-1 text-sm text-blue-700">
                        <EMAIL>
                      </p>
                      <p className="text-xs text-blue-600">
                        Data protection inquiries
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 text-center">
                    <p className="text-sm text-gray-500">
                      We aim to respond to all inquiries within 1-3 business
                      days
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Download Notice */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Card className="bg-gradient-to-r from-blue-50 to-indigo-50">
                <CardContent className="pt-6">
                  <div className="text-center">
                    <h3 className="mb-4 text-xl font-semibold text-gray-900">
                      Full Policy Document
                    </h3>
                    <p className="mb-6 text-gray-600">
                      Download the complete draft Information Security Policy
                      document. This is the current draft version under review
                      by executive management.
                    </p>
                    <div className="flex flex-wrap justify-center gap-4">
                      <Button
                        onClick={handleDownloadDraft}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Download className="mr-2 size-4" />
                        Download Draft Policy (TXT)
                      </Button>
                      <Button disabled className="bg-gray-400">
                        <FileText className="mr-2 size-4" />
                        Download Final Policy (Coming Soon)
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
