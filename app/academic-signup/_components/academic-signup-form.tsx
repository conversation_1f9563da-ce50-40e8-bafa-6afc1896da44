"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAcademicSignupGuard } from "@/hooks/use-academic-navigation-guard"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { useToast } from "@/components/ui/use-toast"
import {
  submitAcademicVerificationAction,
  AcademicVerificationRequest
} from "@/actions/academic-verification-actions"
import { useUser } from "@clerk/nextjs"
import { Loader2, CheckCircle, AlertCircle, Clock } from "lucide-react"
import { <PERSON><PERSON>, AlertDescription, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/alert"
import { SelectAcademicVerification } from "@/db/schema/academic-verifications-schema"

const formSchema = z.object({
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  institution: z
    .string()
    .min(2, "Institution name must be at least 2 characters"),
  role: z.enum(["student", "researcher", "professor", "other"], {
    required_error: "Please select your role"
  }),
  institutionalEmail: z
    .string()
    .email("Please enter a valid institutional email address")
  // For testing purposes, we'll accept any email domain
  /*
    .refine(email => {
      const domain = email.split('@')[1];
      return domain && !domain.endsWith('.gmail.com') && !domain.endsWith('.yahoo.com') &&
             !domain.endsWith('.hotmail.com') && !domain.endsWith('.outlook.com');
    }, {
      message: "Please use an institutional email address, not a personal one"
    })
    */
})

type FormValues = z.infer<typeof formSchema>

interface AcademicSignupFormProps {
  existingData?: SelectAcademicVerification | null
  verificationStatus?: string | null
}

export default function AcademicSignupForm({
  existingData,
  verificationStatus
}: AcademicSignupFormProps) {
  const { toast } = useToast()
  const router = useRouter()

  // Enable navigation guard to prevent bypassing signup flow
  useAcademicSignupGuard()
  const { user, isLoaded } = useUser()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Clear academic intent flag when form loads
  useEffect(() => {
    if (typeof window !== "undefined") {
      const hadAcademicIntent = localStorage.getItem("academicSignupIntent")
      if (hadAcademicIntent) {
        localStorage.removeItem("academicSignupIntent")
      }
    }
  }, [])

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: existingData?.fullName || "",
      email: existingData?.email || "",
      institution: existingData?.institution || "",
      role:
        (existingData?.role as
          | "student"
          | "researcher"
          | "professor"
          | "other") || undefined,
      institutionalEmail: existingData?.institutionalEmail || ""
    }
  })

  // Pre-fill form with user data if available
  useEffect(() => {
    if (isLoaded && user && !existingData) {
      form.setValue("fullName", user.fullName || "")
      form.setValue("email", user.primaryEmailAddress?.emailAddress || "")
    }
  }, [isLoaded, user, form, existingData])

  async function onSubmit(values: FormValues) {
    if (!isLoaded || !user) {
      toast({
        title: "Authentication Error",
        description:
          "Please wait for authentication to complete or refresh the page.",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)
    try {
      const result = await submitAcademicVerificationAction(
        values as AcademicVerificationRequest,
        user.id // Pass the client-side user ID
      )

      if (result && result.isSuccess) {
        if (result.emailSent) {
          // Check if we're in development mode with a verification link
          if (result.verificationLink) {
            toast({
              title: "Development Mode",
              description:
                "Verification email sent to developer's email (<EMAIL>). You can also click here to verify directly.",
              variant: "default",
              action: (
                <div className="flex items-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      window.open(result.verificationLink, "_blank")
                    }
                  >
                    Verify Now
                  </Button>
                </div>
              ),
              duration: 10000 // Show for 10 seconds
            })
          } else {
            toast({
              title: "Verification email sent",
              description:
                "Please check your institutional email for a verification link to complete the process.",
              variant: "default"
            })
          }
        } else {
          toast({
            title: "Application submitted",
            description:
              result.message ||
              "Your academic verification request has been submitted successfully.",
            variant: "default"
          })
        }

        // Refresh the page to show the updated verification status
        router.refresh()
      } else {
        toast({
          title: "Submission failed",
          description:
            (result && result.message) ||
            "There was an error submitting your application. Please try again.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("FORM SUBMISSION - Error submitting form:", error)
      toast({
        title: "Submission error",
        description: "An unexpected error occurred. Please try again later.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Render verification status alert if applicable
  const renderVerificationStatus = () => {
    if (!verificationStatus) return null

    switch (verificationStatus) {
      case "approved":
        return (
          <Alert className="mb-6 bg-green-50 dark:bg-green-900/20">
            <CheckCircle className="size-4 text-green-600 dark:text-green-400" />
            <AlertTitle>Verification Approved</AlertTitle>
            <AlertDescription>
              Your academic status has been verified. You can now proceed to the
              academic survey.
            </AlertDescription>
            <Button
              className="mt-2 w-full"
              onClick={() => router.push("/academic-survey")}
            >
              Continue to Survey
            </Button>
          </Alert>
        )
      case "domain_verified":
        return (
          <Alert className="mb-6 bg-yellow-50 dark:bg-yellow-900/20">
            <Clock className="size-4 text-yellow-600 dark:text-yellow-400" />
            <AlertTitle>Email Verification Required</AlertTitle>
            <AlertDescription>
              Your domain has been pre-verified, but you still need to verify
              your email address. Please check your institutional email for a
              verification link. If you didn't receive the email, you can resend
              it.
            </AlertDescription>
            <Button
              className="mt-2 w-full"
              variant="outline"
              onClick={async () => {
                try {
                  const { resendVerificationEmailAction } = await import(
                    "@/actions/academic-verification-actions"
                  )
                  const result = await resendVerificationEmailAction()

                  if (result.isSuccess) {
                    toast({
                      title: "Verification email sent",
                      description:
                        "Please check your institutional email for the verification link.",
                      variant: "default"
                    })
                  } else {
                    toast({
                      title: "Error",
                      description:
                        result.message || "Failed to resend verification email",
                      variant: "destructive"
                    })
                  }
                } catch (error) {
                  console.error("Error resending verification email:", error)
                  toast({
                    title: "Error",
                    description: "An unexpected error occurred",
                    variant: "destructive"
                  })
                }
              }}
            >
              Resend Verification Email
            </Button>
          </Alert>
        )
      case "pending":
        return (
          <Alert className="mb-6 bg-yellow-50 dark:bg-yellow-900/20">
            <Clock className="size-4 text-yellow-600 dark:text-yellow-400" />
            <AlertTitle>Verification Pending</AlertTitle>
            <AlertDescription>
              Your verification request is being processed. Please check your
              institutional email for a verification link. If you didn't receive
              the email, you can resend it.
            </AlertDescription>
            <Button
              className="mt-2 w-full"
              variant="outline"
              onClick={async () => {
                try {
                  const { resendVerificationEmailAction } = await import(
                    "@/actions/academic-verification-actions"
                  )
                  const result = await resendVerificationEmailAction()

                  if (result.isSuccess) {
                    toast({
                      title: "Verification email sent",
                      description:
                        "Please check your institutional email for the verification link.",
                      variant: "default"
                    })
                  } else {
                    toast({
                      title: "Error",
                      description:
                        result.message || "Failed to resend verification email",
                      variant: "destructive"
                    })
                  }
                } catch (error) {
                  console.error("Error resending verification email:", error)
                  toast({
                    title: "Error",
                    description: "An unexpected error occurred",
                    variant: "destructive"
                  })
                }
              }}
            >
              Resend Verification Email
            </Button>
          </Alert>
        )
      case "rejected":
        return (
          <Alert className="mb-6 bg-red-50 dark:bg-red-900/20">
            <AlertCircle className="size-4 text-red-600 dark:text-red-400" />
            <AlertTitle>Verification Rejected</AlertTitle>
            <AlertDescription>
              Your verification request was not approved. Please update your
              information and try again, or contact support for assistance.
            </AlertDescription>
          </Alert>
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Academic trial information banner */}
      <Alert className="mb-6 bg-blue-50 dark:bg-blue-900/20">
        <AlertTitle>Extended Academic Access</AlertTitle>
        <AlertDescription>
          Academic users receive a 90-day trial period instead of the standard
          30-day trial. Complete the verification process below to qualify for
          extended access.
        </AlertDescription>
      </Alert>

      {renderVerificationStatus()}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input placeholder="Your full name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Personal Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    type="email"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="institution"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Academic Institution</FormLabel>
                <FormControl>
                  <Input
                    placeholder="University or research institution name"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Academic Role</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="student">Student</SelectItem>
                    <SelectItem value="researcher">Researcher</SelectItem>
                    <SelectItem value="professor">Professor</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="institutionalEmail"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Institutional Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    type="email"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Please provide your academic email address for verification
                  purposes. We'll use this to verify your academic status.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full"
            disabled={
              !isLoaded ||
              !user ||
              isSubmitting ||
              verificationStatus === "approved"
            }
          >
            {!isLoaded || !user ? (
              <>
                <Loader2 className="mr-2 size-4 animate-spin" />
                Loading...
              </>
            ) : isSubmitting ? (
              <>
                <Loader2 className="mr-2 size-4 animate-spin" />
                Submitting...
              </>
            ) : (
              "Submit Application"
            )}
          </Button>
        </form>
      </Form>
    </div>
  )
}
