"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  Rocket,
  Play,
  BookOpen,
  Zap,
  Clock,
  Lightbulb,
  Users,
  TrendingUp,
  Video,
  ExternalLink,
  Beaker,
  GraduationCap,
  MousePointer
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { createOptimizationWorkflowAction } from "@/actions/optimization-workflow-actions"
import { getSurveyResponsesAction } from "@/actions/survey-actions"
import { InteractiveVideoTour } from "./interactive-video-tour"
import { isFeatureEnabled, isFeatureInDevelopment } from "@/lib/feature-flags"
import { DevelopmentBadge } from "@/components/ui/development-badge"

interface QuickStartWizardProps {
  userId: string
}

interface SurveyData {
  industry?: string
  role?: string
  useCase?: string
}

interface VideoTour {
  id: string
  title: string
  description: string
  duration: string
  thumbnail: string
  videoUrl: string
  category: "overview" | "tutorial" | "advanced"
}

// Interactive video tours with UI automation
const interactiveTours = [
  {
    id: "create-optimization",
    title: "Creating Your First Optimization",
    description:
      "Watch as we create a complete optimization from start to finish",
    totalDuration: 45000,
    steps: [
      {
        id: "step-1",
        type: "click" as const,
        selector: "[data-tour='create-button']",
        description: "Click the 'Create Optimization' button",
        duration: 2000,
        x: 200,
        y: 100
      },
      {
        id: "step-2",
        type: "type" as const,
        selector: "[data-tour='name-input']",
        text: "My First Optimization",
        description: "Enter a name for your optimization",
        duration: 3000
      },
      {
        id: "step-3",
        type: "click" as const,
        selector: "[data-tour='target-dropdown']",
        description: "Select your optimization target",
        duration: 2000,
        x: 300,
        y: 200
      }
    ]
  }
]

// Traditional video tours for reference
const videoTours: VideoTour[] = [
  {
    id: "platform-overview",
    title: "Platform Overview (3 min)",
    description: "See how Bayesian Optimization works in action",
    duration: "3:24",
    thumbnail: "/videos/thumbnails/overview.jpg",
    videoUrl: "/videos/platform-overview.mp4",
    category: "overview"
  },
  {
    id: "first-optimization",
    title: "Create Your First Optimization",
    description: "Step-by-step walkthrough of the creation process",
    duration: "5:12",
    thumbnail: "/videos/thumbnails/first-optimization.jpg",
    videoUrl: "/videos/first-optimization.mp4",
    category: "tutorial"
  }
]

// Industry-specific templates
const industryTemplates = {
  "Pharmaceutical/Biotech": {
    examples: ["Drug formulation", "Reaction conditions", "Protein expression"],
    defaultTarget: "Yield",
    defaultParams: ["Temperature", "pH", "Concentration"]
  },
  Manufacturing: {
    examples: ["Process efficiency", "Quality control", "Cost reduction"],
    defaultTarget: "Efficiency",
    defaultParams: ["Speed", "Temperature", "Pressure"]
  },
  "Technology/Software": {
    examples: ["Algorithm tuning", "System performance", "User experience"],
    defaultTarget: "Performance",
    defaultParams: ["Learning rate", "Batch size", "Regularization"]
  },
  "Academic/Research": {
    examples: ["Experimental design", "Model optimization", "Data analysis"],
    defaultTarget: "Accuracy",
    defaultParams: ["Parameter A", "Parameter B", "Parameter C"]
  },
  Finance: {
    examples: [
      "Portfolio optimization",
      "Risk management",
      "Trading strategies"
    ],
    defaultTarget: "Return",
    defaultParams: ["Allocation", "Risk factor", "Time horizon"]
  }
}

export function QuickStartWizard({ userId }: QuickStartWizardProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [surveyData, setSurveyData] = useState<SurveyData>({})
  const [selectedVideo, setSelectedVideo] = useState<VideoTour | null>(null)
  const [isVideoDialogOpen, setIsVideoDialogOpen] = useState(false)
  const [showInteractiveTour, setShowInteractiveTour] = useState(false)

  // Load user survey data for personalization
  useEffect(() => {
    const loadSurveyData = async () => {
      try {
        const result = await getSurveyResponsesAction()
        if (result.isSuccess && result.responses) {
          setSurveyData(result.responses)
        }
      } catch (error) {
        console.error("Error loading survey data:", error)
      }
    }
    loadSurveyData()
  }, [])

  // Get personalized template based on survey data
  const getPersonalizedTemplate = () => {
    const industry = surveyData.industry
    if (industry && industry in industryTemplates) {
      return industryTemplates[industry as keyof typeof industryTemplates]
    }
    return industryTemplates["Academic/Research"] // Default fallback
  }

  const template = getPersonalizedTemplate()

  // Handle video selection and playback
  const handleVideoSelect = (video: VideoTour) => {
    setSelectedVideo(video)
    setIsVideoDialogOpen(true)
  }

  // Handle video completion tracking
  const handleVideoComplete = () => {
    toast({
      title: "🎉 Great job!",
      description: "Ready to create your first optimization?"
    })
  }

  // Handle realistic demo launch
  const handleRealisticDemo = () => {
    if (!isFeatureEnabled("createOptimizationDemo")) {
      toast({
        title: "🔧 Feature in Development",
        description:
          "The Create Optimization demo is currently being enhanced. Coming soon!",
        variant: "default"
      })
      return
    }
    // Navigate to optimization creation with demo mode
    router.push("/dashboard/optimizations/create?demo=true&guided=true")
  }

  // Handle run experiments demo launch
  const handleRunExperimentsDemo = () => {
    if (!isFeatureEnabled("runExperimentsDemo")) {
      toast({
        title: "🔧 Feature in Development",
        description:
          "The Run Experiments demo is currently being enhanced. Coming soon!",
        variant: "default"
      })
      return
    }
    // Navigate to run experiments with demo mode
    router.push("/dashboard/optimizations/demo-run/run?demo=true&guided=true")
  }

  // Handle video selection with feature flag check
  const handleVideoSelectWithCheck = (video: VideoTour) => {
    const featureKey =
      video.id === "platform-overview"
        ? "platformOverviewVideo"
        : "createFirstOptimizationVideo"

    if (!isFeatureEnabled(featureKey)) {
      toast({
        title: "🔧 Feature in Development",
        description: `The ${video.title} video is currently being produced. Coming soon!`,
        variant: "default"
      })
      return
    }

    handleVideoSelect(video)
  }

  // Handle quick start with template
  const handleQuickStart = async () => {
    setIsLoading(true)
    try {
      // Use template data to create a quick optimization
      const config = {
        parameters: template.defaultParams.map((name, index) => ({
          name,
          type: "NumericalContinuous" as const,
          bounds: [0, 100] // Default bounds
        })),
        target_config: {
          name: template.defaultTarget,
          mode: "MAX" as const
        },
        objective_type: "SingleTarget" as const,
        recommender_config: {
          type: "BotorchRecommender" as const,
          n_restarts: 10,
          n_raw_samples: 64
        },
        acquisition_config: {
          type: "qExpectedImprovement" as const
        }
      }

      const result = await createOptimizationWorkflowAction(
        `My First ${surveyData.industry || "Research"} Optimization`,
        `Quick start optimization for ${template.examples[0]}`,
        config
      )

      if (result.isSuccess && result.data) {
        toast({
          title: "🎉 Optimization Created!",
          description:
            "Your first optimization is ready. Let's start experimenting!"
        })
        router.push(`/dashboard/optimizations/${result.data.id}/run`)
      } else {
        throw new Error(result.message || "Failed to create optimization")
      }
    } catch (error) {
      console.error("Error creating quick optimization:", error)
      toast({
        title: "Error",
        description: "Failed to create optimization. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle custom optimization creation
  const handleCustomOptimization = () => {
    router.push("/dashboard/optimizations/create")
  }

  // Handle tutorial navigation
  const handleViewTutorials = () => {
    router.push("/tutorials")
  }

  // Show interactive tour if selected
  if (showInteractiveTour) {
    return (
      <div className="flex h-[400px] flex-col">
        <InteractiveVideoTour
          tour={interactiveTours[0]}
          onComplete={handleVideoComplete}
          className="flex-1"
        />
        <div className="flex justify-center pt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowInteractiveTour(false)}
          >
            Back to Options
          </Button>
        </div>
      </div>
    )
  }

  // Create a unified list of learning resources
  const learningResources = [
    // Interactive Demos
    {
      id: "create-optimization-demo",
      type: "interactive" as const,
      title: "Create Optimization",
      description: "Learn to set up your first optimization",
      duration: "5 min",
      icon: Play,
      badge: "Live",
      badgeColor: "from-primary to-blue-600",
      onClick: handleRealisticDemo,
      hoverColor: "hover:border-blue-300 dark:hover:border-blue-600",
      iconBg: "from-blue-50 to-blue-100",
      iconColor: "text-blue-600"
    },
    {
      id: "run-experiments-demo",
      type: "interactive" as const,
      title: "Run Experiments",
      description: "Experience the experiment workflow",
      duration: "4 min",
      icon: Beaker,
      badge: "New",
      badgeColor: "from-purple-500 to-pink-600",
      onClick: handleRunExperimentsDemo,
      hoverColor: "hover:border-blue-300 dark:hover:border-blue-600",
      iconBg: "from-blue-50 to-blue-100",
      iconColor: "text-blue-600"
    },
    // Video Guides
    ...videoTours.map(video => ({
      id: video.id,
      type: "video" as const,
      title: video.title.replace(/\s*\(\d+\s*min\)/, ""),
      description: video.description,
      duration: video.duration,
      icon: Play,
      badge: "Video",
      badgeColor: "from-green-600 to-emerald-600",
      onClick: () => handleVideoSelectWithCheck(video),
      hoverColor: "hover:border-green-300 dark:hover:border-green-600",
      iconBg: "from-green-50 to-emerald-100",
      iconColor: "text-green-600"
    }))
  ]

  return (
    <>
      {/* Combined Learning Resources Card */}
      <Card className="border-0 bg-gradient-to-br from-purple-50/50 to-indigo-50/30 shadow-lg lg:col-span-2 dark:from-purple-950/50 dark:to-indigo-950/30">
        <CardHeader className="bg-gradient-to-r from-purple-600 to-indigo-600 pb-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-white/20 p-2 backdrop-blur-sm">
                <GraduationCap className="size-5" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold">
                  Learning Resources
                </CardTitle>
                <CardDescription className="text-sm text-purple-100">
                  {surveyData.industry && surveyData.industry !== "skipped"
                    ? `Interactive demos and video guides tailored for ${surveyData.industry}`
                    : "Interactive demos and comprehensive video guides to get you started"}
                </CardDescription>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-4">
          {/* Unified Learning Resources List */}
          <div className="space-y-3">
            {learningResources.map(resource => (
              <div
                key={resource.id}
                className={`group flex cursor-pointer items-center gap-3 rounded-lg border border-gray-200 bg-white p-3 transition-all duration-200 hover:shadow-sm dark:border-slate-700 dark:bg-slate-800 ${resource.hoverColor}`}
                onClick={resource.onClick}
              >
                <div className="relative shrink-0">
                  <div
                    className={`flex size-12 items-center justify-center rounded-lg bg-gradient-to-br ${resource.iconBg} dark:from-slate-700 dark:to-slate-600`}
                  >
                    <div className="flex size-6 items-center justify-center rounded-full bg-white/90 shadow-sm transition-transform group-hover:scale-110 dark:bg-slate-800/90">
                      <resource.icon
                        className={`ml-0.5 size-3 ${resource.iconColor}`}
                      />
                    </div>
                  </div>
                  <div className="absolute -right-1 -top-1">
                    <Badge
                      variant="secondary"
                      className={`border-0 bg-gradient-to-r ${resource.type === "interactive" ? "from-blue-600 to-blue-600" : "from-green-600 to-green-600"} px-1 py-0 text-xs text-white`}
                    >
                      {resource.duration}
                    </Badge>
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2">
                    <h5 className="truncate text-sm font-medium text-gray-900 dark:text-white">
                      {resource.title}
                    </h5>
                    {/* Show development badge for features in development */}
                    {((resource.id === "create-optimization-demo" &&
                      isFeatureInDevelopment("createOptimizationDemo")) ||
                      (resource.id === "run-experiments-demo" &&
                        isFeatureInDevelopment("runExperimentsDemo")) ||
                      (resource.id === "platform-overview" &&
                        isFeatureInDevelopment("platformOverviewVideo")) ||
                      (resource.id === "first-optimization" &&
                        isFeatureInDevelopment(
                          "createFirstOptimizationVideo"
                        ))) && <DevelopmentBadge variant="icon" size="sm" />}
                  </div>
                  <p className="truncate text-xs text-gray-500 dark:text-gray-400">
                    {resource.description}
                  </p>
                </div>
                <div className="flex shrink-0 items-center gap-2">
                  <Badge
                    className={`border-0 bg-gradient-to-r ${resource.badgeColor} px-2 py-1 text-xs text-white`}
                  >
                    {resource.badge}
                  </Badge>
                  {resource.type === "interactive" ? (
                    <MousePointer className="size-4 text-blue-500" />
                  ) : (
                    <ExternalLink className="size-4 text-gray-400 transition-colors group-hover:text-green-600" />
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* View All Button */}
          <div className="mt-4 border-t border-gray-200 pt-4 dark:border-slate-700">
            <Button
              onClick={handleViewTutorials}
              variant="outline"
              size="lg"
              className="w-full border-purple-200 text-purple-700 hover:border-purple-300 hover:bg-purple-50 dark:border-purple-800 dark:text-purple-400 dark:hover:bg-purple-950/20"
            >
              <Video className="mr-2 size-5" />
              View All Learning Resources
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Modern Video Dialog */}
      <Dialog open={isVideoDialogOpen} onOpenChange={setIsVideoDialogOpen}>
        <DialogContent className="max-w-4xl border-0 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950">
          <DialogHeader className="text-center">
            <DialogTitle className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-2xl font-bold text-transparent">
              {selectedVideo?.title}
            </DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-300">
              {selectedVideo?.description}
            </DialogDescription>
          </DialogHeader>

          <div className="relative aspect-video w-full overflow-hidden rounded-xl border border-green-200 bg-gradient-to-br from-slate-100 to-green-100 shadow-2xl dark:border-green-800 dark:from-slate-900/50 dark:to-green-900/50">
            {selectedVideo ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="space-y-6 p-8 text-center">
                  <div className="relative">
                    <div className="mx-auto flex size-24 items-center justify-center rounded-full bg-gradient-to-br from-green-600 to-emerald-600 shadow-2xl">
                      <Play className="ml-1 size-12 text-white" />
                    </div>
                    <div className="absolute -right-2 -top-2 animate-pulse">
                      <Badge className="border-0 bg-gradient-to-r from-green-500 to-emerald-500 text-white">
                        {selectedVideo.duration}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                      Ready to Watch?
                    </h3>
                    <p className="mx-auto max-w-md text-gray-600 dark:text-gray-300">
                      This comprehensive tutorial will guide you through every
                      step with real examples and best practices.
                    </p>
                  </div>

                  <div className="flex justify-center gap-3">
                    <Button
                      onClick={handleVideoComplete}
                      size="lg"
                      className="border-0 bg-gradient-to-r from-green-600 to-emerald-600 text-white shadow-lg transition-all duration-200 hover:from-green-700 hover:to-emerald-700 hover:shadow-xl hover:shadow-green-500/30"
                    >
                      <Play className="mr-2 size-5" />
                      Start Learning
                    </Button>
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => setIsVideoDialogOpen(false)}
                      className="border-slate-300 hover:bg-slate-50 dark:border-slate-600 dark:hover:bg-slate-900/20"
                    >
                      Maybe Later
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <p className="text-gray-500">No video selected</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
