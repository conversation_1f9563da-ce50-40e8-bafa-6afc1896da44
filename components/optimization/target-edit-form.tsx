"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Save, X, RotateCcw, Target, ArrowUp, ArrowDown } from "lucide-react"

interface TargetConfig {
  name: string
  mode: "MAX" | "MIN"
  bounds?: [number, number]
  weight?: number
  type?: "Numerical" | "Categorical"
  rawBoundsInput?: { [key: number]: string } // Store raw bound inputs for editing
}

interface TargetEditFormProps {
  target: TargetConfig
  originalTarget?: TargetConfig // For reset functionality
  onSave: (updatedTarget: TargetConfig) => void
  onCancel: () => void
  isMultiTarget?: boolean
  isPareto?: boolean // Hide weights for Pareto objectives
  className?: string
}

export function TargetEditForm({
  target,
  originalTarget,
  onSave,
  onCancel,
  isMultiTarget = false,
  isPareto = false,
  className = ""
}: TargetEditFormProps) {
  const [editedTarget, setEditedTarget] = useState<TargetConfig>(target)
  const [hasChanges, setHasChanges] = useState(false)

  // Reset form when target changes
  useEffect(() => {
    const initialTarget = { ...target }
    // Initialize rawBoundsInput with the current bounds if they exist
    if (target.bounds) {
      initialTarget.rawBoundsInput = {
        0: target.bounds[0]?.toString() ?? "",
        1: target.bounds[1]?.toString() ?? ""
      }
    }
    setEditedTarget(initialTarget)
    setHasChanges(false)
  }, [target])

  // Check for changes
  useEffect(() => {
    const changed = JSON.stringify(editedTarget) !== JSON.stringify(target)
    setHasChanges(changed)
  }, [editedTarget, target])

  const handleModeChange = (mode: "MAX" | "MIN") => {
    setEditedTarget(prev => ({
      ...prev,
      mode
    }))
  }

  const handleBoundChange = (index: 0 | 1, value: string) => {
    console.log("🔧 Target Edit - Bound change:", {
      index,
      value,
      type: typeof value
    })

    // Store the raw input to allow partial editing (like backspacing)
    setEditedTarget(prev => {
      const newBounds = [...(prev.bounds || [0, 0])] as [number, number]

      // Allow empty string for editing, but convert to 0 for storage
      if (value === "") {
        newBounds[index] = 0
      } else {
        const numValue = parseFloat(value)
        if (!isNaN(numValue)) {
          newBounds[index] = numValue
        } else {
          // For invalid input, keep the previous value but allow the input to show
          // This enables partial editing like typing "-" or "1."
          return {
            ...prev,
            rawBoundsInput: {
              ...prev.rawBoundsInput,
              [index]: value
            }
          }
        }
      }

      console.log("🔧 Target Edit - New bounds set:", newBounds)
      return {
        ...prev,
        bounds: newBounds,
        rawBoundsInput: {
          ...prev.rawBoundsInput,
          [index]: value
        }
      }
    })
  }

  const handleWeightChange = (value: string) => {
    const numValue = parseFloat(value)
    // Accept percentage values (1-100) and convert to decimal (0.01-1.0) for storage
    if (!isNaN(numValue) && numValue >= 1 && numValue <= 100) {
      setEditedTarget(prev => ({
        ...prev,
        weight: numValue / 100 // Convert percentage to decimal for backend
      }))
    }
  }

  const handleSave = () => {
    onSave(editedTarget)
  }

  const handleReset = () => {
    // Reset to original target if provided, otherwise to current target
    const resetTarget = originalTarget || target
    console.log("🔄 TARGET RESET - Resetting to:", resetTarget)
    console.log("🔄 TARGET RESET - Current edited target:", editedTarget)
    setEditedTarget(resetTarget)
    setHasChanges(false)
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="space-y-4 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="size-4 text-green-600" />
            <h4 className="font-medium">{target.name}</h4>
            <Badge variant="outline" className="text-xs">
              {target.type || "Numerical"}
            </Badge>
          </div>
          {hasChanges && (
            <Badge variant="secondary" className="text-xs">
              Modified
            </Badge>
          )}
        </div>

        <div className="text-muted-foreground text-sm">
          Configure optimization mode, bounds, and other target settings.
        </div>

        {/* Optimization Mode */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Optimization Mode</Label>
          <Select value={editedTarget.mode} onValueChange={handleModeChange}>
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="MAX">
                <div className="flex items-center gap-2">
                  <ArrowUp className="size-3 text-green-500" />
                  Maximize
                </div>
              </SelectItem>
              <SelectItem value="MIN">
                <div className="flex items-center gap-2">
                  <ArrowDown className="size-3 text-green-500" />
                  Minimize
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Target Bounds */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">
            Target Bounds (Optional)
          </Label>
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
            <div className="space-y-1">
              <Label className="text-muted-foreground text-xs">Minimum</Label>
              <Input
                type="number"
                value={
                  editedTarget.rawBoundsInput?.[0] ??
                  editedTarget.bounds?.[0] ??
                  ""
                }
                onChange={e => {
                  console.log(
                    "🔧 Target Edit - Min input onChange:",
                    e.target.value
                  )
                  handleBoundChange(0, e.target.value)
                }}
                onKeyDown={e => {
                  console.log(
                    "🔧 Target Edit - Min key pressed:",
                    e.key,
                    "Code:",
                    e.code
                  )
                }}
                step="any"
                placeholder="Min value"
                className="h-8"
              />
            </div>
            <div className="space-y-1">
              <Label className="text-muted-foreground text-xs">Maximum</Label>
              <Input
                type="number"
                value={
                  editedTarget.rawBoundsInput?.[1] ??
                  editedTarget.bounds?.[1] ??
                  ""
                }
                onChange={e => {
                  console.log(
                    "🔧 Target Edit - Max input onChange:",
                    e.target.value
                  )
                  handleBoundChange(1, e.target.value)
                }}
                onKeyDown={e => {
                  console.log(
                    "🔧 Target Edit - Max key pressed:",
                    e.key,
                    "Code:",
                    e.code
                  )
                }}
                step="any"
                placeholder="Max value"
                className="h-8"
              />
            </div>
          </div>
          <div className="text-muted-foreground text-xs">
            Optional bounds help filter measurements and guide optimization
          </div>
        </div>

        {/* Weight (for multi-target Desirability objectives only) */}
        {isMultiTarget && !isPareto && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Weight (%)</Label>
            <div className="relative">
              <Input
                type="number"
                value={
                  editedTarget.weight
                    ? Math.round(editedTarget.weight * 100)
                    : 50
                }
                onChange={e => handleWeightChange(e.target.value)}
                step="1"
                min="1"
                max="100"
                placeholder="50"
                className="h-8"
              />
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                <span className="text-sm text-gray-500">%</span>
              </div>
            </div>
            <div className="text-muted-foreground text-xs">
              Relative importance in multi-target optimization (1% - 100%)
            </div>
          </div>
        )}

        {/* Pareto objective info */}
        {isMultiTarget && isPareto && (
          <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
            <p className="text-sm font-medium text-blue-800">
              ℹ️ Pareto Objective: Weights are not used in Pareto optimization.
              The algorithm finds the optimal trade-off frontier without
              pre-specified weights.
            </p>
          </div>
        )}

        {/* Current Configuration Summary */}
        <div className="bg-muted/30 rounded-md p-3">
          <div className="text-muted-foreground mb-1 text-xs font-medium">
            Current Configuration:
          </div>
          <div className="space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <span className="font-medium">
                {editedTarget.mode === "MAX" ? "Maximize" : "Minimize"}
              </span>
              {editedTarget.mode === "MAX" ? (
                <ArrowUp className="size-3 text-green-500" />
              ) : (
                <ArrowDown className="size-3 text-green-500" />
              )}
            </div>
            {editedTarget.bounds && (
              <div className="text-muted-foreground text-xs">
                Bounds: {editedTarget.bounds[0]} to {editedTarget.bounds[1]}
              </div>
            )}
            {isMultiTarget && !isPareto && (
              <div className="text-muted-foreground text-xs">
                Weight:{" "}
                {editedTarget.weight
                  ? Math.round(editedTarget.weight * 100)
                  : 50}
                %
              </div>
            )}
            {isMultiTarget && isPareto && (
              <div className="text-muted-foreground text-xs">
                Objective: Pareto (no weights)
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2 pt-2 sm:flex-row sm:items-center sm:justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            disabled={!hasChanges}
            className="h-8 w-full sm:w-auto"
          >
            <RotateCcw className="mr-1 size-3" />
            Reset
          </Button>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              className="h-8 flex-1 sm:flex-none"
            >
              <X className="mr-1 size-3" />
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
              className="h-8 flex-1 sm:flex-none"
            >
              <Save className="mr-1 size-3" />
              Save
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
