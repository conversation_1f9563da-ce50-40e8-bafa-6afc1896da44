#!/usr/bin/env node

// Investigation script for CRUD filtering discrepancy - Test CRUD Filtering2
// Usage: node scripts/investigate-crud-filtering2.js

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, and } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema definitions
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

const samplesTable = pgTable("samples", {
  id: uuid("id").primaryKey(),
  optimizationId: uuid("optimization_id").notNull(),
  parameters: jsonb("parameters").notNull(),
  batchId: text("batch_id").notNull(),
  sampleIndex: text("sample_index").notNull(),
  samplingMethod: text("sampling_method").notNull(),
  seed: text("seed"),
  sampleClass: text("sample_class").notNull(),
  status: text("status").notNull(),
  targetValues: jsonb("target_values"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  lastAccessedAt: timestamp("last_accessed_at"),
  submittedAt: timestamp("submitted_at")
});

const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  API_KEY: process.env.API_KEY || 'your-api-key-here'
};

async function investigateCRUDFiltering() {
  console.log('🔍 INVESTIGATING CRUD FILTERING DISCREPANCY - Test CRUD Filtering2');
  console.log('====================================================================');
  
  // Connect to database
  const connectionString = process.env.DATABASE_URL;
  if (!connectionString) {
    console.log("DATABASE_URL environment variable is required");
    return;
  }

  const sql = postgres(connectionString);
  const db = drizzle(sql);

  try {
    // Find the "Test CRUD Filtering2" optimization
    let optimization = await db
      .select()
      .from(optimizationsTable)
      .where(eq(optimizationsTable.name, "Test CRUD Filtering2"))
      .limit(1);

    if (optimization.length === 0) {
      // Try to find the most recent one with CRUD in the name
      const allOpts = await db.select().from(optimizationsTable).orderBy(optimizationsTable.createdAt);
      console.log(`📋 Found ${allOpts.length} optimizations in database:`);
      allOpts.forEach((opt, index) => {
        console.log(`  ${index + 1}. "${opt.name}" (ID: ${opt.optimizerId})`);
      });
      
      const found = allOpts.reverse().find(opt => 
        opt.name.toLowerCase().includes("crud filtering")
      );
      
      if (found) {
        console.log(`🔍 Found CRUD filtering optimization: "${found.name}"`);
        optimization = [found];
      } else {
        console.log("❌ No optimization with 'CRUD filtering' in name found");
        return;
      }
    }

    const opt = optimization[0];
    console.log("✅ Found optimization:");
    console.log(`  - ID: ${opt.id}`);
    console.log(`  - Optimizer ID: ${opt.optimizerId}`);
    console.log(`  - Name: ${opt.name}`);
    console.log(`  - Status: ${opt.status}`);
    console.log(`  - Created: ${opt.createdAt}`);
    console.log(`  - Updated: ${opt.updatedAt}`);

    console.log("\n📊 CONFIGURATION:");
    console.log(`  - Config: ${JSON.stringify(opt.config, null, 2)}`);

    // Get samples from database
    const samples = await db
      .select()
      .from(samplesTable)
      .where(eq(samplesTable.optimizationId, opt.id))
      .orderBy(samplesTable.createdAt);

    console.log(`\n📈 DATABASE SAMPLES (${samples.length} total):`);

    samples.forEach((sample, index) => {
      console.log(`\n  ${index + 1}. Sample ${sample.id}:`);
      console.log(`     - Parameters: ${JSON.stringify(sample.parameters, null, 6)}`);
      console.log(`     - Target Values: ${JSON.stringify(sample.targetValues, null, 6)}`);
      console.log(`     - Status: ${sample.status}`);
      console.log(`     - Sample Class: ${sample.sampleClass}`);
      console.log(`     - Batch ID: ${sample.batchId}`);
      console.log(`     - Created: ${sample.createdAt}`);
    });

    // STEP 1: Check backend campaign measurements BEFORE filtering
    console.log('\n🔍 STEP 1: CHECKING BACKEND CAMPAIGN MEASUREMENTS (BEFORE)');
    console.log('===========================================================');
    
    const backendMeasurementsBefore = await getBackendMeasurements(opt.optimizerId);
    console.log(`Backend campaign has ${backendMeasurementsBefore.length} measurements`);

    // Only count submitted samples for comparison
    const submittedSamples = samples.filter(s => s.status === 'submitted' || s.targetValues);
    console.log(`Database has ${submittedSamples.length} submitted samples (with target values)`);

    if (backendMeasurementsBefore.length !== submittedSamples.length) {
      console.log(`❌ DISCREPANCY: Database has ${submittedSamples.length} submitted samples, backend has ${backendMeasurementsBefore.length}`);
    } else {
      console.log(`✅ MATCH: Both database and backend have ${submittedSamples.length} measurements`);
    }

    // STEP 2: Test x3 parameter bounds change from [0, 100] to [40, 100]
    console.log('\n🧪 STEP 2: TESTING x3 BOUNDS CHANGE [0, 100] → [40, 100]');
    console.log('========================================================');
    
    const filteringResult = await testParameterBoundsChange(opt.optimizerId, 'x3', [40, 100]);
    
    // STEP 3: Analyze database measurements for x3 filtering
    console.log('\n📊 STEP 3: DATABASE ANALYSIS FOR x3 FILTERING');
    console.log('==============================================');
    
    let shouldBeRetained = 0;
    let shouldBeFiltered = 0;
    let hasNaNInfinite = 0;

    // Only analyze submitted samples with target values
    submittedSamples.forEach((sample, index) => {
      const params = sample.parameters;
      const x3Value = params.x3;

      console.log(`\n${index + 1}. Sample ${sample.id}:`);
      console.log(`   x3 = ${x3Value}`);

      // Check for NaN/infinite values
      const hasNaN = Object.values(params).some(v => Number.isNaN(v) || !Number.isFinite(v));

      if (hasNaN) {
        hasNaNInfinite++;
        console.log(`   ❌ HAS NaN/INFINITE - Should be filtered`);
        Object.entries(params).forEach(([key, value]) => {
          if (Number.isNaN(value) || !Number.isFinite(value)) {
            console.log(`     - ${key}: ${value} (${Number.isNaN(value) ? 'NaN' : 'Infinite'})`);
          }
        });
      } else if (x3Value < 40) {
        shouldBeFiltered++;
        console.log(`   ❌ x3=${x3Value} < 40 - Should be filtered`);
      } else {
        shouldBeRetained++;
        console.log(`   ✅ x3=${x3Value} >= 40 - Should be retained`);
      }
    });

    console.log(`\n📈 DATABASE ANALYSIS SUMMARY:`);
    console.log(`  - Total submitted samples: ${submittedSamples.length}`);
    console.log(`  - Should be retained: ${shouldBeRetained}`);
    console.log(`  - Should be filtered (x3 < 40): ${shouldBeFiltered}`);
    console.log(`  - Should be filtered (NaN/Infinite): ${hasNaNInfinite}`);
    console.log(`  - Total should be filtered: ${shouldBeFiltered + hasNaNInfinite}`);

    console.log(`\n🔍 BACKEND RESPONSE COMPARISON:`);
    console.log(`  - Backend reported: ${filteringResult.retained} retained, ${filteringResult.filtered} filtered`);
    console.log(`  - Database analysis: ${shouldBeRetained} should be retained, ${shouldBeFiltered + hasNaNInfinite} should be filtered`);
    
    if (filteringResult.retained !== shouldBeRetained || filteringResult.filtered !== (shouldBeFiltered + hasNaNInfinite)) {
      console.log(`❌ MISMATCH: Database analysis does not match backend response`);
      
      // STEP 4: Check backend measurements AFTER filtering
      console.log('\n🔍 STEP 4: CHECKING BACKEND CAMPAIGN MEASUREMENTS (AFTER)');
      console.log('==========================================================');
      
      const backendMeasurementsAfter = await getBackendMeasurements(opt.optimizerId);
      console.log(`Backend campaign now has ${backendMeasurementsAfter.length} measurements`);
      
      if (backendMeasurementsAfter.length !== filteringResult.retained) {
        console.log(`❌ ANOTHER DISCREPANCY: Backend reported ${filteringResult.retained} retained, but campaign has ${backendMeasurementsAfter.length}`);
      }
      
    } else {
      console.log(`✅ MATCH: Database analysis matches backend response`);
    }

  } catch (error) {
    console.error('❌ Error during investigation:', error);
  } finally {
    await sql.end();
  }
}

async function getBackendMeasurements(optimizerId) {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/optimizations/${optimizerId}/measurements`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_CONFIG.API_KEY
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.measurements || [];
    } else {
      console.log(`❌ Failed to get backend measurements: ${response.status}`);
      return [];
    }
  } catch (error) {
    console.log(`❌ Error getting backend measurements: ${error.message}`);
    return [];
  }
}

async function testParameterBoundsChange(optimizerId, paramName, newBounds) {
  try {
    const requestBody = {
      parameter_bounds: {
        [paramName]: newBounds
      },
      preview_only: true
    };
    
    console.log(`📤 Testing ${paramName} bounds change to [${newBounds[0]}, ${newBounds[1]}]...`);
    
    const response = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`📥 Backend response:`, JSON.stringify(data, null, 2));
      
      return {
        retained: data.valid_count || data.measurements_kept || 0,
        filtered: data.dropped_count || data.measurements_dropped || 0,
        total: data.impact_summary?.total_measurements || 0,
        details: data
      };
    } else {
      console.log(`❌ Backend request failed: ${response.status}`);
      return { retained: 0, filtered: 0, total: 0, details: null };
    }
  } catch (error) {
    console.log(`❌ Error testing bounds change: ${error.message}`);
    return { retained: 0, filtered: 0, total: 0, details: null };
  }
}

// Run the investigation
investigateCRUDFiltering().catch(console.error);
