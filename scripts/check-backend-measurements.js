#!/usr/bin/env node

// <PERSON>ript to check backend campaign measurements vs database measurements
// Usage: node scripts/check-backend-measurements.js

require('dotenv').config({ path: '.env.local' });

const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  API_KEY: process.env.API_KEY || 'your-api-key-here'
};

async function checkBackendMeasurements() {
  console.log('🔍 CHECKING BACKEND CAMPAIGN MEASUREMENTS');
  console.log('==========================================');
  
  const optimizerId = 'user_30EFn0yPauqlMMorn5hfsn3of5X_Test_CRUD_filtering__1757672159887';
  
  try {
    // Get campaign info from backend
    console.log('📤 Getting campaign info from backend...');
    const infoResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/info`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('📥 Backend info response status:', infoResponse.status);
    
    if (!infoResponse.ok) {
      console.log('❌ Failed to get campaign info');
      return;
    }
    
    const infoData = await infoResponse.json();
    console.log('📥 Campaign info received');
    
    // Check measurements in the campaign
    if (infoData.measurements) {
      console.log(`\n📊 BACKEND CAMPAIGN MEASUREMENTS (${infoData.measurements.length} total):`);
      
      infoData.measurements.forEach((measurement, index) => {
        console.log(`\n${index + 1}. Measurement:`);
        console.log(`   Parameters:`, measurement.parameters || measurement);
        if (measurement.target_values) {
          console.log(`   Target Values:`, measurement.target_values);
        }
        if (measurement.targets) {
          console.log(`   Targets:`, measurement.targets);
        }
      });
      
      // Check for the specific problematic measurement
      const problematicMeasurement = infoData.measurements.find(m => {
        const params = m.parameters || m;
        return params.x4 && Math.abs(params.x4 - 27.097245441269315) < 1e-10;
      });
      
      if (problematicMeasurement) {
        console.log('\n🎯 FOUND PROBLEMATIC MEASUREMENT IN BACKEND:');
        console.log('Parameters:', problematicMeasurement.parameters || problematicMeasurement);
        
        const params = problematicMeasurement.parameters || problematicMeasurement;
        const x1Value = params.x1;
        
        if (x1Value !== undefined) {
          console.log(`\n🔬 x1 VALUE ANALYSIS:`);
          console.log(`x1 = ${x1Value}`);
          console.log(`x1 (scientific) = ${x1Value.toExponential()}`);
          console.log(`Is NaN: ${Number.isNaN(x1Value)}`);
          console.log(`Is Finite: ${Number.isFinite(x1Value)}`);
          console.log(`Absolute value: ${Math.abs(x1Value)}`);
          console.log(`Is very small (< 1e-15): ${Math.abs(x1Value) < 1e-15}`);
        }
      } else {
        console.log('\n❌ PROBLEMATIC MEASUREMENT NOT FOUND IN BACKEND');
        console.log('This explains why the backend is not filtering it!');
      }
      
    } else {
      console.log('❌ No measurements found in campaign info');
    }
    
    // Also try to get measurements directly
    console.log('\n📤 Getting measurements directly from backend...');
    const measurementsResponse = await fetch(`${API_CONFIG.BASE_URL}/optimizations/${optimizerId}/measurements`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_CONFIG.API_KEY
      }
    });
    
    if (measurementsResponse.ok) {
      const measurementsData = await measurementsResponse.json();
      console.log(`📥 Direct measurements response: ${measurementsData.measurements?.length || 0} measurements`);
      
      if (measurementsData.measurements && measurementsData.measurements.length > 0) {
        console.log('\n📊 DIRECT BACKEND MEASUREMENTS:');
        measurementsData.measurements.forEach((measurement, index) => {
          console.log(`${index + 1}. Parameters:`, measurement.parameters || measurement);
        });
      }
    } else {
      console.log('❌ Failed to get measurements directly from backend');
    }
    
  } catch (error) {
    console.error('❌ Error checking backend measurements:', error);
  }
}

// Run the check
checkBackendMeasurements().catch(console.error);
