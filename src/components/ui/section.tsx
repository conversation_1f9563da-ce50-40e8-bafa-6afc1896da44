import { cn } from "@/lib/utils"
import { ReactNode, forwardRef } from "react"
import { Container } from "./container"

interface SectionProps {
  children: ReactNode
  className?: string
  containerClassName?: string
  as?:
    | "div"
    | "section"
    | "article"
    | "main"
    | "header"
    | "footer"
    | "aside"
    | "nav"
  containerSize?: "sm" | "md" | "lg" | "xl" | "full"
  id?: string
}

/**
 * A responsive section component with consistent vertical spacing
 *
 * @param children - The content to be contained
 * @param className - Additional classes to apply to the section
 * @param containerClassName - Additional classes to apply to the container inside the section
 * @param as - The HTML element to render the section as (default: section)
 * @param containerSize - The maximum width of the container (default: xl)
 * @param id - Optional ID for the section
 */
export const Section = forwardRef<HTMLElement, SectionProps>(
  (
    {
      children,
      className,
      containerClassName,
      as: Component = "section",
      containerSize = "xl",
      id
    }: SectionProps,
    ref
  ) => {
    return (
      <Component
        ref={ref as any}
        id={id}
        className={cn("py-12 md:py-16 lg:py-20", className)}
      >
        <Container
          size={containerSize}
          className={cn("h-full", containerClassName)}
        >
          {children}
        </Container>
      </Component>
    )
  }
)

Section.displayName = "Section"
