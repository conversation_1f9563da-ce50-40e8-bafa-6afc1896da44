/*
Third-Party Notices page
*/

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { ExternalLink, ArrowLeft, Package, Shield } from "lucide-react"

export default function ThirdPartyNoticesPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/help/api-reference">
            <ArrowLeft className="mr-1 size-4" />
            Back to API Reference
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Third-Party Notices</h1>
        <p className="text-muted-foreground">
          This application incorporates components from the projects listed
          below. The original copyright notices and licenses are provided for
          each component.
        </p>
      </div>

      {/* BayBE - Primary Attribution */}
      <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="size-5 text-blue-600" />
            BayBE (Bayesian Backend)
          </CardTitle>
          <CardDescription>Core Bayesian optimization engine</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
            <div>
              <strong>Source:</strong>{" "}
              <a
                href="https://github.com/emdgroup/baybe"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                github.com/emdgroup/baybe
              </a>
            </div>
            <div>
              <strong>License:</strong> Apache License 2.0
            </div>
            <div className="md:col-span-2">
              <strong>Copyright:</strong> Copyright © 2022-2025 Merck KGaA,
              Darmstadt, Germany and/or its affiliates. All rights reserved.
            </div>
          </div>

          <div className="bg-muted rounded-lg p-3 text-sm">
            <p className="mb-2 font-medium">License Notice:</p>
            <p className="text-muted-foreground">
              Licensed under the Apache License, Version 2.0 (the "License");
              you may not use this file except in compliance with the License.
              You may obtain a copy of the License at{" "}
              <a
                href="http://www.apache.org/licenses/LICENSE-2.0"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                http://www.apache.org/licenses/LICENSE-2.0
              </a>
            </p>
            <p className="text-muted-foreground mt-2">
              Unless required by applicable law or agreed to in writing,
              software distributed under the License is distributed on an "AS
              IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
              express or implied.
            </p>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <a
                href="https://emdgroup.github.io/baybe/stable/misc/license_link.html"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1"
              >
                <ExternalLink className="size-3" />
                Full License Text
              </a>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <a
                href="https://emdgroup.github.io/baybe/stable/"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1"
              >
                <ExternalLink className="size-3" />
                Documentation
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Other Major Dependencies */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="size-5" />
            Other Major Dependencies
          </CardTitle>
          <CardDescription>
            Additional open-source libraries used in this application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            {[
              {
                name: "FastAPI",
                license: "MIT License",
                description: "Web framework for the backend API"
              },
              {
                name: "PyTorch",
                license: "BSD-3-Clause License",
                description: "Machine learning framework"
              },
              {
                name: "BoTorch",
                license: "MIT License",
                description: "Bayesian optimization library (used by BayBE)"
              },
              {
                name: "GPyTorch",
                license: "MIT License",
                description: "Gaussian process library"
              },
              {
                name: "Next.js",
                license: "MIT License",
                description: "React framework for the frontend"
              },
              {
                name: "React",
                license: "MIT License",
                description: "UI library"
              },
              {
                name: "NumPy",
                license: "BSD License",
                description: "Numerical computing library"
              },
              {
                name: "Pandas",
                license: "BSD-3-Clause License",
                description: "Data manipulation library"
              },
              {
                name: "Scikit-learn",
                license: "BSD-3-Clause License",
                description: "Machine learning library"
              }
            ].map((dep, index) => (
              <div
                key={index}
                className="flex items-center justify-between rounded-lg border p-3"
              >
                <div>
                  <div className="font-medium">{dep.name}</div>
                  <div className="text-muted-foreground text-sm">
                    {dep.description}
                  </div>
                </div>
                <Badge variant="outline">{dep.license}</Badge>
              </div>
            ))}
          </div>

          <Separator />

          <div className="text-muted-foreground text-sm">
            <p>
              <strong>Note:</strong> For Python dependencies, see{" "}
              <code className="bg-muted rounded px-1 py-0.5">
                requirements.txt
              </code>{" "}
              in the backend directory. For JavaScript/Node.js dependencies, see{" "}
              <code className="bg-muted rounded px-1 py-0.5">package.json</code>{" "}
              in the frontend directory.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* License Compliance */}
      <Card>
        <CardHeader>
          <CardTitle>License Compliance</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">
            This project complies with all applicable open-source licenses. If
            you believe there is a license compliance issue, please contact the
            project maintainers.
          </p>
          <p className="text-muted-foreground mt-2 text-sm">
            <strong>Note:</strong> This file contains notices for third-party
            software included in this project. The licenses for these components
            are provided for informational purposes. Your use of these
            components is subject to the terms and conditions of their
            respective licenses.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
