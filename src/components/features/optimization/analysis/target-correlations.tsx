"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Network, RefreshCw, AlertCircle, Maximize2, Info } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import dynamic from "next/dynamic"

// Import Plotly dynamically to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

interface TargetCorrelationsProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  targetNames: string[]
  onTargetSelect?: (target: string) => void
  selectedTarget?: string
}

interface TargetCorrelationData {
  targetValues: Record<string, number[]>
  targetRanges: Record<
    string,
    { min: number; max: number; isMaximize: boolean }
  >
  correlationMatrix: number[][]
}

export function TargetCorrelations({
  optimization,
  measurements,
  targetNames,
  onTargetSelect,
  selectedTarget
}: TargetCorrelationsProps) {
  const [correlationData, setCorrelationData] =
    useState<TargetCorrelationData | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [displayMode, setDisplayMode] = useState<string>("heatmap")
  const [selectedCell, setSelectedCell] = useState<{
    row: number
    col: number
  } | null>(null)
  const [correlationType, setCorrelationType] = useState<string>("pearson")

  // Calculate correlation data
  const calculateCorrelationData = () => {
    setIsLoading(true)
    setError(null)

    try {
      if (targetNames.length < 2) {
        throw new Error(
          "At least two targets are required for correlation analysis"
        )
      }

      // Extract target values
      const targetValues: Record<string, number[]> = {}
      const targetRanges: Record<
        string,
        { min: number; max: number; isMaximize: boolean }
      > = {}

      // Initialize target arrays
      targetNames.forEach(target => {
        targetValues[target] = []

        // Determine if target should be maximized or minimized
        // Default to maximize if not specified
        let isMaximize = true

        // Check if target configuration is available in optimization config
        if (optimization.config && typeof optimization.config === "object") {
          const config = optimization.config as any
          if (config.target_config && Array.isArray(config.target_config)) {
            const targetConfig = config.target_config.find(
              (t: any) => t.name === target
            )
            if (targetConfig && targetConfig.mode) {
              isMaximize = targetConfig.mode.toUpperCase() === "MAX"
            }
          }
        }

        targetRanges[target] = { min: Infinity, max: -Infinity, isMaximize }
      })

      // Collect values
      measurements.forEach(measurement => {
        // Extract target values
        if (measurement.targetValues) {
          const targetValuesObj = measurement.targetValues as Record<
            string,
            number
          >
          targetNames.forEach(target => {
            // Check if the value is a number or a string and handle accordingly
            const rawValue = targetValuesObj[target]
            const value =
              typeof rawValue === "number"
                ? rawValue
                : parseFloat(String(rawValue))
            if (!isNaN(value)) {
              targetValues[target].push(value)

              // Update ranges
              targetRanges[target].min = Math.min(
                targetRanges[target].min,
                value
              )
              targetRanges[target].max = Math.max(
                targetRanges[target].max,
                value
              )
            } else {
              // Push NaN to maintain index alignment
              targetValues[target].push(NaN)
            }
          })
        } else if (targetNames.includes(optimization.targetName)) {
          // Single target case
          const rawValue = measurement.targetValue
          const value =
            typeof rawValue === "number"
              ? rawValue
              : parseFloat(String(rawValue))
          if (!isNaN(value)) {
            targetValues[optimization.targetName].push(value)

            // Update ranges
            targetRanges[optimization.targetName].min = Math.min(
              targetRanges[optimization.targetName].min,
              value
            )
            targetRanges[optimization.targetName].max = Math.max(
              targetRanges[optimization.targetName].max,
              value
            )

            // Push NaN for other targets to maintain index alignment
            targetNames.forEach(target => {
              if (target !== optimization.targetName) {
                targetValues[target].push(NaN)
              }
            })
          }
        }
      })

      // Calculate correlation matrix
      const matrix: number[][] = []

      for (let i = 0; i < targetNames.length; i++) {
        const row: number[] = []
        const name1 = targetNames[i]
        const values1 = targetValues[name1]

        for (let j = 0; j < targetNames.length; j++) {
          const name2 = targetNames[j]
          const values2 = targetValues[name2]

          // Calculate correlation
          const correlation = calculateCorrelation(
            values1,
            values2,
            correlationType
          )
          row.push(correlation)
        }

        matrix.push(row)
      }

      setCorrelationData({
        targetValues,
        targetRanges,
        correlationMatrix: matrix
      })

      console.log("Target correlation data calculated:", {
        targetCount: targetNames.length,
        measurementCount: measurements.length,
        correlationType
      })
    } catch (err) {
      console.error("Error calculating target correlation data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // Calculate correlation coefficient
  const calculateCorrelation = (
    values1: number[],
    values2: number[],
    method: string
  ): number => {
    // Filter out NaN values while maintaining index alignment
    const validPairs: [number, number][] = []
    for (let i = 0; i < values1.length; i++) {
      if (!isNaN(values1[i]) && !isNaN(values2[i])) {
        validPairs.push([values1[i], values2[i]])
      }
    }

    // Extract valid values
    const validValues1 = validPairs.map(pair => pair[0])
    const validValues2 = validPairs.map(pair => pair[1])

    if (validValues1.length < 2) {
      return 0
    }

    if (method === "pearson") {
      return calculatePearsonCorrelation(validValues1, validValues2)
    } else if (method === "spearman") {
      return calculateSpearmanCorrelation(validValues1, validValues2)
    }

    return 0
  }

  // Calculate Pearson correlation
  const calculatePearsonCorrelation = (
    values1: number[],
    values2: number[]
  ): number => {
    const n = values1.length

    // Calculate means
    const mean1 = values1.reduce((sum, val) => sum + val, 0) / n
    const mean2 = values2.reduce((sum, val) => sum + val, 0) / n

    // Calculate covariance and variances
    let covariance = 0
    let variance1 = 0
    let variance2 = 0

    for (let i = 0; i < n; i++) {
      const diff1 = values1[i] - mean1
      const diff2 = values2[i] - mean2

      covariance += diff1 * diff2
      variance1 += diff1 * diff1
      variance2 += diff2 * diff2
    }

    // Avoid division by zero
    if (variance1 === 0 || variance2 === 0) {
      return 0
    }

    return covariance / Math.sqrt(variance1 * variance2)
  }

  // Calculate Spearman correlation
  const calculateSpearmanCorrelation = (
    values1: number[],
    values2: number[]
  ): number => {
    const n = values1.length

    // Create arrays of [value, index] pairs
    const pairs1 = values1.map((value, index) => ({ value, index }))
    const pairs2 = values2.map((value, index) => ({ value, index }))

    // Sort by value
    pairs1.sort((a, b) => a.value - b.value)
    pairs2.sort((a, b) => a.value - b.value)

    // Assign ranks
    const ranks1: number[] = new Array(n).fill(0)
    const ranks2: number[] = new Array(n).fill(0)

    for (let i = 0; i < n; i++) {
      ranks1[pairs1[i].index] = i + 1
      ranks2[pairs2[i].index] = i + 1
    }

    // Calculate Pearson correlation of ranks
    return calculatePearsonCorrelation(ranks1, ranks2)
  }

  // Generate heatmap data
  const heatmapData = useMemo(() => {
    if (!correlationData) return null

    const { correlationMatrix } = correlationData

    return {
      z: correlationMatrix,
      x: targetNames,
      y: targetNames,
      type: "heatmap" as const,
      colorscale: "RdBu" as const,
      zmin: -1,
      zmax: 1,
      hoverongaps: false,
      hoverinfo: "text" as const,
      text: correlationMatrix.map((row, i) =>
        row.map(
          (value, j) =>
            `${targetNames[i]} vs ${targetNames[j]}<br>Correlation: ${value.toFixed(3)}`
        )
      )
    }
  }, [correlationData, targetNames])

  // Generate network graph data
  const networkData = useMemo(() => {
    if (!correlationData) return []

    const { correlationMatrix } = correlationData
    const n = targetNames.length

    // Create nodes
    const nodes = {
      x: [] as number[],
      y: [] as number[],
      mode: "markers+text" as const,
      type: "scatter" as const,
      marker: {
        size: 20,
        color: "rgba(31, 119, 180, 0.8)"
      },
      text: targetNames,
      textposition: "bottom center" as const,
      hoverinfo: "text" as const,
      name: "Targets"
    }

    // Position nodes in a circle
    for (let i = 0; i < n; i++) {
      const angle = (2 * Math.PI * i) / n
      nodes.x.push(Math.cos(angle))
      nodes.y.push(Math.sin(angle))
    }

    // Create edges
    const edges = []

    for (let i = 0; i < n; i++) {
      for (let j = i + 1; j < n; j++) {
        const correlation = correlationMatrix[i][j]
        const absCorrelation = Math.abs(correlation)

        // Only show edges with correlation above threshold
        if (absCorrelation > 0.1) {
          const color =
            correlation > 0
              ? "rgba(44, 160, 44, 0.8)"
              : "rgba(214, 39, 40, 0.8)"
          const width = absCorrelation * 5 // Scale width by correlation strength

          edges.push({
            x: [nodes.x[i], nodes.x[j], null],
            y: [nodes.y[i], nodes.y[j], null],
            mode: "lines" as const,
            type: "scatter" as const,
            line: {
              color,
              width
            },
            hoverinfo: "text" as const,
            text: [
              `${targetNames[i]} vs ${targetNames[j]}<br>Correlation: ${correlation.toFixed(3)}`
            ],
            name: `${targetNames[i]} - ${targetNames[j]}`
          })
        }
      }
    }

    return [nodes, ...edges]
  }, [correlationData, targetNames])

  // Generate scatterplot data for selected cell
  const scatterplotData = useMemo(() => {
    if (!correlationData || !selectedCell) return null

    const { targetValues } = correlationData

    const target1 = targetNames[selectedCell.row]
    const target2 = targetNames[selectedCell.col]

    // Skip if same target
    if (target1 === target2) return null

    // Get values for the selected targets
    const xValues: number[] = []
    const yValues: number[] = []

    // Collect valid pairs
    for (let i = 0; i < targetValues[target1].length; i++) {
      const x = targetValues[target2][i]
      const y = targetValues[target1][i]

      if (!isNaN(x) && !isNaN(y)) {
        xValues.push(x)
        yValues.push(y)
      }
    }

    return {
      x: xValues,
      y: yValues,
      mode: "markers" as const,
      type: "scatter" as const,
      marker: {
        color: "rgba(31, 119, 180, 0.7)",
        size: 10
      },
      name: `${target1} vs ${target2}`,
      hoverinfo: "text" as const,
      text: xValues.map(
        (x, i) => `${target2}: ${x}<br>${target1}: ${yValues[i]}`
      )
    }
  }, [correlationData, selectedCell, targetNames])

  // Handle cell click
  const handleCellClick = (event: any) => {
    if (event && event.points && event.points.length > 0) {
      const point = event.points[0]
      setSelectedCell({ row: point.y, col: point.x })

      // If target selection callback is provided
      if (onTargetSelect) {
        const targetName = targetNames[point.x]
        onTargetSelect(targetName)
      }
    }
  }

  // Effect to calculate correlation data when measurements change
  useEffect(() => {
    if (measurements.length > 0 && targetNames.length >= 2) {
      calculateCorrelationData()
    }
  }, [measurements.length, targetNames.length, correlationType])

  // Effect to highlight selected target
  useEffect(() => {
    if (selectedTarget && targetNames.includes(selectedTarget)) {
      const targetIndex = targetNames.indexOf(selectedTarget)
      if (targetIndex >= 0) {
        // Highlight the target by selecting a cell in its column
        setSelectedCell({ row: 0, col: targetIndex })
      }
    }
  }, [selectedTarget, targetNames])

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="flex items-center text-lg font-medium">
            <Network className="text-primary mr-2 size-5" />
            Target Correlations
          </h3>
          <div className="flex gap-2">
            <Tabs
              value={correlationType}
              onValueChange={setCorrelationType}
              className="w-auto"
            >
              <TabsList className="grid w-auto grid-cols-2">
                <TabsTrigger value="pearson">Pearson</TabsTrigger>
                <TabsTrigger value="spearman">Spearman</TabsTrigger>
              </TabsList>
            </Tabs>
            <Tabs
              value={displayMode}
              onValueChange={setDisplayMode}
              className="w-auto"
            >
              <TabsList className="grid w-auto grid-cols-2">
                <TabsTrigger value="heatmap">Heatmap</TabsTrigger>
                <TabsTrigger value="network">Network</TabsTrigger>
              </TabsList>
            </Tabs>
            <Button
              variant="outline"
              size="sm"
              onClick={calculateCorrelationData}
              disabled={isLoading}
            >
              {isLoading ? (
                <RefreshCw className="size-4 animate-spin" />
              ) : (
                <RefreshCw className="size-4" />
              )}
              <span className="ml-2 hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="size-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {targetNames.length < 2 && (
          <Alert className="mb-4">
            <Info className="size-4" />
            <AlertTitle>Single Target Optimization</AlertTitle>
            <AlertDescription>
              Correlation analysis requires at least two target variables. This
              optimization has only one target.
            </AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex h-[400px] items-center justify-center">
            <Skeleton className="size-full rounded-md" />
          </div>
        ) : correlationData && targetNames.length >= 2 ? (
          <div className="space-y-4">
            <div className="h-[400px] w-full overflow-hidden rounded-md border">
              {displayMode === "heatmap" ? (
                <Plot
                  data={heatmapData ? [heatmapData] : []}
                  layout={{
                    title: `${correlationType === "pearson" ? "Pearson" : "Spearman"} Correlation Matrix`,
                    autosize: true,
                    margin: { l: 80, r: 20, t: 50, b: 80 },
                    xaxis: {
                      tickangle: 45,
                      automargin: true
                    },
                    yaxis: {
                      automargin: true
                    },
                    coloraxis: {
                      colorbar: {
                        title: "Correlation",
                        titleside: "right"
                      }
                    }
                  }}
                  config={{ responsive: true, displayModeBar: false }}
                  style={{ width: "100%", height: "100%" }}
                  onClick={handleCellClick}
                />
              ) : (
                <Plot
                  data={networkData}
                  layout={{
                    title: `Target Correlation Network (${correlationType === "pearson" ? "Pearson" : "Spearman"})`,
                    autosize: true,
                    showlegend: false,
                    margin: { l: 20, r: 20, t: 50, b: 20 },
                    xaxis: {
                      showgrid: false,
                      zeroline: false,
                      showticklabels: false,
                      range: [-1.2, 1.2]
                    },
                    yaxis: {
                      showgrid: false,
                      zeroline: false,
                      showticklabels: false,
                      range: [-1.2, 1.2]
                    },
                    annotations: [
                      {
                        x: 1,
                        y: -1,
                        xref: "paper",
                        yref: "paper",
                        text: "Green: Positive correlation<br>Red: Negative correlation<br>Line width: Correlation strength",
                        showarrow: false,
                        align: "right",
                        bgcolor: "rgba(255, 255, 255, 0.8)",
                        bordercolor: "rgba(0, 0, 0, 0.2)",
                        borderwidth: 1,
                        borderpad: 4
                      }
                    ]
                  }}
                  config={{ responsive: true, displayModeBar: false }}
                  style={{ width: "100%", height: "100%" }}
                />
              )}
            </div>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full">
                  <Maximize2 className="mr-2 size-4" />
                  Expand View
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Target Correlations</DialogTitle>
                </DialogHeader>
                <div className="h-[600px] w-full">
                  {displayMode === "heatmap" ? (
                    <Plot
                      data={heatmapData ? [heatmapData] : []}
                      layout={{
                        title: `${correlationType === "pearson" ? "Pearson" : "Spearman"} Correlation Matrix`,
                        autosize: true,
                        margin: { l: 80, r: 20, t: 50, b: 80 },
                        xaxis: {
                          tickangle: 45,
                          automargin: true
                        },
                        yaxis: {
                          automargin: true
                        },
                        coloraxis: {
                          colorbar: {
                            title: "Correlation",
                            titleside: "right"
                          }
                        },
                        height: 600
                      }}
                      config={{ responsive: true }}
                      style={{ width: "100%", height: "100%" }}
                      onClick={handleCellClick}
                    />
                  ) : (
                    <Plot
                      data={networkData}
                      layout={{
                        title: `Target Correlation Network (${correlationType === "pearson" ? "Pearson" : "Spearman"})`,
                        autosize: true,
                        showlegend: false,
                        margin: { l: 20, r: 20, t: 50, b: 20 },
                        xaxis: {
                          showgrid: false,
                          zeroline: false,
                          showticklabels: false,
                          range: [-1.2, 1.2]
                        },
                        yaxis: {
                          showgrid: false,
                          zeroline: false,
                          showticklabels: false,
                          range: [-1.2, 1.2]
                        },
                        height: 600,
                        annotations: [
                          {
                            x: 1,
                            y: -1,
                            xref: "paper",
                            yref: "paper",
                            text: "Green: Positive correlation<br>Red: Negative correlation<br>Line width: Correlation strength",
                            showarrow: false,
                            align: "right",
                            bgcolor: "rgba(255, 255, 255, 0.8)",
                            bordercolor: "rgba(0, 0, 0, 0.2)",
                            borderwidth: 1,
                            borderpad: 4
                          }
                        ]
                      }}
                      config={{ responsive: true }}
                      style={{ width: "100%", height: "100%" }}
                    />
                  )}
                </div>
              </DialogContent>
            </Dialog>

            {displayMode === "heatmap" && selectedCell && scatterplotData && (
              <div className="mt-4">
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="text-md font-medium">
                    Scatterplot: {targetNames[selectedCell.row]} vs{" "}
                    {targetNames[selectedCell.col]}
                  </h4>
                </div>
                <div className="h-[250px] w-full overflow-hidden rounded-md border">
                  <Plot
                    data={scatterplotData ? [scatterplotData] : []}
                    layout={{
                      autosize: true,
                      margin: { l: 60, r: 20, t: 30, b: 60 },
                      xaxis: {
                        title: targetNames[selectedCell.col]
                      },
                      yaxis: {
                        title: targetNames[selectedCell.row]
                      },
                      showlegend: false
                    }}
                    config={{ responsive: true, displayModeBar: false }}
                    style={{ width: "100%", height: "100%" }}
                  />
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex h-[400px] flex-col items-center justify-center p-4 text-center">
            <Network className="text-muted-foreground mb-4 size-12" />
            <h3 className="text-lg font-medium">No correlation data</h3>
            <p className="text-muted-foreground mt-2 max-w-md text-sm">
              There might not be enough measurements with multiple targets, or
              the targets might not have numeric values.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
