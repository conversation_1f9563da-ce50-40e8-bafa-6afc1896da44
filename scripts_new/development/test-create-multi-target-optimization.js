#!/usr/bin/env node

/**
 * Test creating a new Multi-Target optimization to verify configuration updates work
 */

async function testCreateMultiTargetOptimization() {
  console.log('🧪 TESTING MULTI-TARGET OPTIMIZATION CREATION & CONFIGURATION UPDATES');
  console.log('======================================================================');
  
  const optimizerId = `test_multi_target_config_${Date.now()}`;
  
  try {
    // Test 1: Create Multi-Target Desirability optimization
    console.log('\n📋 STEP 1: Creating Multi-Target Desirability optimization...');
    
    const desirabilityConfig = {
      optimizer_id: optimizerId,
      parameters: [
        {
          name: "x1",
          type: "NumericalContinuous",
          bounds: [0, 100]
        },
        {
          name: "x2",
          type: "NumericalContinuous",
          bounds: [0, 100]
        }
      ],
      target_config: [
        {
          name: "Target 1",
          mode: "MAX",
          type: "Numerical",
          bounds: [0, 100],
          transformation: "LINEAR",
          weight: 0.6
        },
        {
          name: "Target 2",
          mode: "MIN",
          type: "Numerical",
          bounds: [0, 50],
          transformation: "LINEAR",
          weight: 0.4
        }
      ],
      objective_type: "Desirability",
      scalarizer: "GEOM_MEAN",
      weights: [0.6, 0.4],
      acquisition_config: {
        type: "qExpectedImprovement"
      },
      initial_sampling_strategy: "LHS",
      num_initial_samples: 0
    };
    
    const createResponse = await fetch('http://localhost:8000/optimizations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(desirabilityConfig)
    });
    
    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      console.error(`❌ Creation failed: ${createResponse.status} ${createResponse.statusText}`);
      console.error(`❌ Error details: ${errorText}`);
      return;
    }
    
    const createResult = await createResponse.json();
    console.log('✅ Multi-Target Desirability optimization created successfully');
    console.log(`   Optimizer ID: ${optimizerId}`);
    
    // Test 2: Add some measurements
    console.log('\n📋 STEP 2: Adding measurements...');
    
    const measurements = [
      { parameters: { x1: 25, x2: 30 }, target_values: [{"name": "Target 1", "value": 45}, {"name": "Target 2", "value": 25}] },
      { parameters: { x1: 75, x2: 20 }, target_values: [{"name": "Target 1", "value": 65}, {"name": "Target 2", "value": 15}] }
    ];
    
    for (let i = 0; i < measurements.length; i++) {
      const measurement = measurements[i];
      console.log(`   Adding measurement ${i + 1}...`);
      
      const measurementResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          parameters: measurement.parameters,
          target_values: measurement.target_values
        })
      });
      
      if (measurementResponse.ok) {
        console.log(`   ✅ Measurement ${i + 1} added successfully`);
      } else {
        const errorText = await measurementResponse.text();
        console.log(`   ❌ Measurement ${i + 1} failed: ${measurementResponse.status} - ${errorText}`);
      }
    }
    
    // Test 3: Test configuration updates
    console.log('\n📋 STEP 3: Testing target bounds configuration update...');
    
    const updateResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target_bounds: {
          "Target 1": [0, 150],
          "Target 2": [0, 75]
        },
        preview_only: false
      })
    });
    
    console.log(`   📥 Update response status: ${updateResponse.status} ${updateResponse.statusText}`);
    
    if (updateResponse.ok) {
      const updateResult = await updateResponse.json();
      console.log('   ✅ Target bounds update successful');
      console.log(`   📊 Database updated: ${updateResult.database_updated}`);
      console.log(`   📊 Updated config provided: ${!!updateResult.updated_config}`);
      
      if (updateResult.updated_targets) {
        console.log('   📊 Updated targets:');
        if (Array.isArray(updateResult.updated_targets)) {
          updateResult.updated_targets.forEach((target, index) => {
            console.log(`     ${index + 1}. ${target.name}: [${target.bounds?.join(', ') || 'no bounds'}]`);
          });
        } else {
          console.log(`     Single target: ${updateResult.updated_targets.name} [${updateResult.updated_targets.bounds?.join(', ') || 'no bounds'}]`);
        }
      }
    } else {
      const errorText = await updateResponse.text();
      console.log(`   ❌ Update failed: ${errorText}`);
    }
    
    // Test 4: Test parameter bounds update
    console.log('\n📋 STEP 4: Testing parameter bounds configuration update...');
    
    const paramUpdateResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        parameter_bounds: {
          "x1": [0, 80]
        },
        preview_only: false
      })
    });
    
    console.log(`   📥 Parameter update response status: ${paramUpdateResponse.status} ${paramUpdateResponse.statusText}`);
    
    if (paramUpdateResponse.ok) {
      const paramUpdateResult = await paramUpdateResponse.json();
      console.log('   ✅ Parameter bounds update successful');
      console.log(`   📊 Database updated: ${paramUpdateResult.database_updated}`);
      console.log(`   📊 Updated config provided: ${!!paramUpdateResult.updated_config}`);
    } else {
      const errorText = await paramUpdateResponse.text();
      console.log(`   ❌ Parameter update failed: ${errorText}`);
    }
    
    // Test 5: Verify final configuration
    console.log('\n📋 STEP 5: Verifying final configuration...');
    
    const measurementsResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`);
    
    if (measurementsResponse.ok) {
      const measurementsData = await measurementsResponse.json();
      console.log(`   ✅ Final measurements: ${measurementsData.measurements?.length || 0}`);
      
      if (measurementsData.target_info) {
        console.log('   📊 Final target info:');
        measurementsData.target_info.forEach((target, index) => {
          console.log(`     ${index + 1}. ${target.name}: ${target.mode}`);
        });
      }
    } else {
      console.log(`   ❌ Cannot verify final configuration: ${measurementsResponse.status}`);
    }
    
    // Test 6: Create a Pareto optimization
    console.log('\n📋 STEP 6: Creating Multi-Target Pareto optimization...');

    const paretoOptimizerId = `test_pareto_config_${Date.now()}`;

    const paretoConfig = {
      optimizer_id: paretoOptimizerId,
      parameters: [
        {
          name: "x1",
          type: "NumericalContinuous",
          bounds: [0, 100]
        },
        {
          name: "x2",
          type: "NumericalContinuous",
          bounds: [0, 100]
        }
      ],
      target_config: [
        {
          name: "Target 1",
          mode: "MAX",
          type: "Numerical"
          // Note: No bounds or transformation for Pareto as per BayBE docs
        },
        {
          name: "Target 2",
          mode: "MIN",
          type: "Numerical"
          // Note: No bounds or transformation for Pareto as per BayBE docs
        }
      ],
      objective_type: "Pareto",
      acquisition_config: {
        type: "qNoisyExpectedHypervolumeImprovement"
      },
      initial_sampling_strategy: "LHS",
      num_initial_samples: 0
    };

    const paretoCreateResponse = await fetch('http://localhost:8000/optimizations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(paretoConfig)
    });

    if (paretoCreateResponse.ok) {
      console.log('✅ Multi-Target Pareto optimization created successfully');
      console.log(`   Optimizer ID: ${paretoOptimizerId}`);

      // Add measurements to Pareto optimization
      console.log('\n📋 STEP 7: Adding measurements to Pareto optimization...');

      const paretoMeasurements = [
        { parameters: { x1: 25, x2: 30 }, target_values: [{"name": "Target 1", "value": 45}, {"name": "Target 2", "value": 25}] },
        { parameters: { x1: 75, x2: 20 }, target_values: [{"name": "Target 1", "value": 65}, {"name": "Target 2", "value": 15}] }
      ];

      for (let i = 0; i < paretoMeasurements.length; i++) {
        const measurement = paretoMeasurements[i];
        console.log(`   Adding Pareto measurement ${i + 1}...`);

        const measurementResponse = await fetch(`http://localhost:8000/optimizations/${paretoOptimizerId}/measurements`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            parameters: measurement.parameters,
            target_values: measurement.target_values
          })
        });

        if (measurementResponse.ok) {
          console.log(`   ✅ Pareto measurement ${i + 1} added successfully`);
        } else {
          const errorText = await measurementResponse.text();
          console.log(`   ❌ Pareto measurement ${i + 1} failed: ${measurementResponse.status} - ${errorText}`);
        }
      }

      // Test Pareto configuration updates
      console.log('\n📋 STEP 8: Testing Pareto parameter bounds configuration update...');

      const paretoUpdateResponse = await fetch(`http://localhost:3000/api/optimizations/${paretoOptimizerId}/bounds`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          parameter_bounds: {
            "x1": [0, 80]
          },
          preview_only: false
        })
      });

      console.log(`   📥 Pareto update response status: ${paretoUpdateResponse.status} ${paretoUpdateResponse.statusText}`);

      if (paretoUpdateResponse.ok) {
        const paretoUpdateResult = await paretoUpdateResponse.json();
        console.log('   ✅ Pareto parameter bounds update successful');
        console.log(`   📊 Database updated: ${paretoUpdateResult.database_updated}`);
        console.log(`   📊 Updated config provided: ${!!paretoUpdateResult.updated_config}`);
      } else {
        const errorText = await paretoUpdateResponse.text();
        console.log(`   ❌ Pareto parameter update failed: ${errorText}`);
      }

    } else {
      const errorText = await paretoCreateResponse.text();
      console.log(`❌ Pareto creation failed: ${paretoCreateResponse.status} ${paretoCreateResponse.statusText}`);
      console.log(`❌ Error details: ${errorText}`);
    }

    console.log('\n🎉 COMPREHENSIVE MULTI-TARGET CONFIGURATION UPDATE TEST COMPLETED');
    console.log(`   Desirability Optimization ID: ${optimizerId}`);
    console.log(`   Pareto Optimization ID: ${paretoOptimizerId}`);
    console.log('   You can now test these optimizations in the UI to verify configuration updates work');
    
  } catch (error) {
    console.error('❌ Error during multi-target test:', error.message);
  }
}

// Run the test
testCreateMultiTargetOptimization().catch(console.error);
