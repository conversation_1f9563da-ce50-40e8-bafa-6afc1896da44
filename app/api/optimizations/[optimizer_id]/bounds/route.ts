import { NextRequest, NextResponse } from "next/server"
import { API_CONFIG } from "@/lib/config"

type Params = {
  optimizer_id: string
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<Params> }
) {
  try {
    const body = await request.json()

    // Await the params before using them
    const { optimizer_id } = await params

    // Validate required fields
    if (!optimizer_id) {
      return NextResponse.json(
        { error: "Missing optimizer_id parameter" },
        { status: 400 }
      )
    }

    // Validate request body structure
    const {
      parameter_bounds,
      updated_parameters,
      target_bounds,
      target_config,
      acquisition_config,
      recommender_config,
      parameter_order,
      constraints,
      preview_only,
      suggestion_count
    } = body

    if (
      !parameter_bounds &&
      !updated_parameters &&
      !target_bounds &&
      !target_config &&
      !acquisition_config &&
      !recommender_config &&
      !constraints
    ) {
      return NextResponse.json(
        {
          error:
            "At least one configuration update must be provided (parameter_bounds, updated_parameters, target_bounds, target_config, acquisition_config, recommender_config, or constraints)"
        },
        { status: 400 }
      )
    }

    // Use the API URL from the centralized config
    const apiUrl = `${API_CONFIG.BASE_URL}/optimizations/${optimizer_id}/bounds`

    console.log("Configuration Update Proxy: Sending to", apiUrl)
    console.log("Configuration Update Proxy: Request body:", {
      parameter_bounds,
      updated_parameters,
      target_bounds,
      target_config,
      acquisition_config,
      recommender_config,
      parameter_order,
      constraints,
      preview_only,
      suggestion_count
    })

    // Debug: Log the exact parameter types being sent
    if (updated_parameters) {
      console.log("Parameter types being sent:")
      updated_parameters.forEach((param: any, index: number) => {
        console.log(`  ${index + 1}. ${param.name}: ${param.type}`)
      })
    }

    // Prepare the request body - use target_config if provided, otherwise fall back to target_bounds
    const requestBody: any = {
      preview_only: preview_only || false
    }

    // Only include suggestion_count if explicitly provided
    if (suggestion_count !== undefined && suggestion_count > 0) {
      requestBody.suggestion_count = suggestion_count
    }

    // Only include fields that are actually provided (not undefined)
    console.log(
      "Debug: parameter_bounds type:",
      typeof parameter_bounds,
      "value:",
      parameter_bounds
    )
    console.log(
      "Debug: updated_parameters type:",
      typeof updated_parameters,
      "value:",
      updated_parameters
    )

    if (parameter_bounds !== undefined) {
      requestBody.parameter_bounds = parameter_bounds
    }

    if (updated_parameters !== undefined) {
      console.log("Adding updated_parameters to request body")
      requestBody.updated_parameters = updated_parameters
    } else {
      console.log("Skipping updated_parameters (undefined)")
    }

    // Add target configuration (prefer target_config over target_bounds)
    if (target_config) {
      requestBody.target_config = target_config
    } else if (target_bounds) {
      requestBody.target_bounds = target_bounds
    }

    // Add acquisition function configuration
    if (acquisition_config) {
      requestBody.acquisition_config = acquisition_config
    }

    // Add recommender configuration
    if (recommender_config) {
      requestBody.recommender_config = recommender_config
    }

    // Add parameter order
    if (parameter_order) {
      requestBody.parameter_order = parameter_order
    }

    // Add constraints
    if (constraints) {
      requestBody.constraints = constraints
    }

    console.log("Configuration Update Proxy: Final request body:", requestBody)
    console.log(
      "Configuration Update Proxy: Final request body keys:",
      Object.keys(requestBody)
    )

    // Make the PUT request to the backend API
    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "X-API-Key": API_CONFIG.API_KEY
      },
      body: JSON.stringify(requestBody)
    })

    console.log(`Backend response status: ${response.status}`)

    // Get the response data
    const data = await response.json()

    console.log(`Backend response data:`, data)

    // Forward the response from the backend
    if (!response.ok) {
      return NextResponse.json(
        {
          error: data.detail || data.message || "Backend request failed",
          status: "error"
        },
        { status: response.status }
      )
    }

    // 🔧 CRITICAL FIX: Save updated configuration to database if backend provides it
    if (data.status === "success" && data.updated_config && !preview_only) {
      console.log("🔧 Configuration Update: Saving updated config to database")
      console.log("   Updated config:", data.updated_config)

      try {
        // Import the database update function
        const { updateOptimizationConfigByOptimizerIdAction } = await import(
          "@/actions/db/optimizations-actions"
        )

        const dbUpdateResult =
          await updateOptimizationConfigByOptimizerIdAction(
            optimizer_id,
            data.updated_config
          )

        if (dbUpdateResult.isSuccess) {
          console.log("✅ Database configuration updated successfully")
          data.database_updated = true
        } else {
          console.warn(
            "⚠️ Failed to update database configuration:",
            dbUpdateResult.message
          )
          data.database_updated = false
          data.database_error = dbUpdateResult.message
        }
      } catch (dbError) {
        console.error("❌ Error updating database configuration:", dbError)
        data.database_updated = false
        data.database_error =
          dbError instanceof Error ? dbError.message : "Unknown database error"
      }
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Error in bounds update proxy:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
        status: "error"
      },
      { status: 500 }
    )
  }
}

// Optional: Add GET method for retrieving current bounds
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<Params> }
) {
  try {
    // Await the params before using them
    const { optimizer_id } = await params

    if (!optimizer_id) {
      return NextResponse.json(
        { error: "Missing optimizer_id parameter" },
        { status: 400 }
      )
    }

    // Use the campaign info endpoint to get current bounds
    const apiUrl = `${API_CONFIG.BASE_URL}/optimizations/${optimizer_id}/info`

    console.log("Get Bounds Proxy: Fetching from", apiUrl)

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-API-Key": API_CONFIG.API_KEY
      }
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        {
          error: data.detail || data.message || "Backend request failed",
          status: "error"
        },
        { status: response.status }
      )
    }

    // Extract bounds information from campaign info
    const campaignInfo = data.info || {}
    const parameters = campaignInfo.parameters || []
    const targetConfig = campaignInfo.target_config || {}

    // Format parameter bounds
    const parameterBounds: Record<string, [number, number]> = {}
    parameters.forEach((param: any) => {
      if (param.type === "NumericalContinuous" && param.bounds) {
        parameterBounds[param.name] = param.bounds
      }
    })

    // Format target bounds
    const targetBounds: Record<string, [number, number]> = {}
    if (Array.isArray(targetConfig)) {
      // Multi-target case
      targetConfig.forEach((target: any) => {
        if (target.bounds) {
          targetBounds[target.name] = target.bounds
        }
      })
    } else if (targetConfig.bounds) {
      // Single target case
      const targetName = targetConfig.name || "Target"
      targetBounds[targetName] = targetConfig.bounds
    }

    return NextResponse.json({
      status: "success",
      parameter_bounds: parameterBounds,
      target_bounds: targetBounds,
      message: "Current bounds retrieved successfully"
    })
  } catch (error) {
    console.error("Error in get bounds proxy:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
        status: "error"
      },
      { status: 500 }
    )
  }
}
