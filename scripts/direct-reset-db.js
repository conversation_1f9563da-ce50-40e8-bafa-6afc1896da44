#!/usr/bin/env node

/**
 * This script directly connects to the database and drops all tables
 * Run with: node scripts/direct-reset-db.js
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const { Client } = require('pg');
const readline = require('readline');

// Get the database URL from environment variables
let databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Modify the connection string to disable SSL verification
databaseUrl = databaseUrl.replace('sslmode=require', 'sslmode=no-verify');
console.log(`🔍 Using database: ${databaseUrl.replace(/:[^:]*@/, ':****@')}`);

// Create a client with SSL disabled
const client = new Client({
  connectionString: databaseUrl,
  ssl: {
    rejectUnauthorized: false
  }
});

// Function to ask for confirmation
function askForConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function resetDatabase() {
  try {
    // Connect to the database
    await client.connect();
    console.log('✅ Connected to database');
    
    // Get all tables in the public schema
    const tablesResult = await client.query(`
      SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    `);
    
    const tables = tablesResult.rows.map(row => row.tablename);
    console.log(`Found ${tables.length} tables: ${tables.join(', ')}`);
    
    if (tables.length === 0) {
      console.log('No tables to drop.');
    } else {
      // Ask for confirmation
      const confirmed = await askForConfirmation(
        `\n⚠️  WARNING: This will delete ALL data in ${tables.length} tables. Continue? (y/n): `
      );
      
      if (!confirmed) {
        console.log('Operation cancelled by user.');
        await client.end();
        process.exit(0);
      }
      
      // Drop all tables
      console.log('Dropping all tables...');
      
      // Disable foreign key checks
      await client.query('SET session_replication_role = replica;');
      
      // Generate DROP TABLE statements for all tables
      const dropQuery = `DROP TABLE IF EXISTS ${tables.map(t => `"${t}"`).join(', ')} CASCADE;`;
      await client.query(dropQuery);
      
      // Re-enable foreign key checks
      await client.query('SET session_replication_role = DEFAULT;');
      
      console.log('✅ All tables dropped successfully');
    }
    
    // Close the connection
    await client.end();
    console.log('✅ Database connection closed');
    
    console.log('\n✅ Database reset completed successfully!');
    console.log('\nNow run the following command to recreate the schema:');
    console.log('npx drizzle-kit push');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error resetting database:', error);
    
    // Try to close the connection
    try {
      await client.end();
    } catch (closeError) {
      console.error('Error closing database connection:', closeError);
    }
    
    process.exit(1);
  }
}

// Start the process
resetDatabase();
