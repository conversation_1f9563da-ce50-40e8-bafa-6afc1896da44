"use client"

import { useState, useCallback } from "react"
import { Constraint } from "@/lib/constraints"
import { Parameter } from "@/types"

interface ValidationResult {
  valid: boolean
  error?: string
  message?: string
}

interface ConstraintValidationResult extends ValidationResult {
  constraintId: string
}

interface GlobalValidationResult {
  valid: boolean
  errors: ConstraintValidationResult[]
  warnings: string[]
  summary: string
}

export function useConstraintValidation() {
  const [isValidating, setIsValidating] = useState(false)
  const [validationResults, setValidationResults] = useState<
    Record<string, ValidationResult>
  >({})

  const validateSingleConstraint = useCallback(
    async (
      constraint: Constraint,
      parameters: Parameter[]
    ): Promise<ValidationResult> => {
      try {
        setIsValidating(true)

        // Extract parameter names and types
        const parameterNames = parameters
          .map(p => p.name)
          .filter(name => name.trim() !== "")
        const parameterTypes = parameters.reduce(
          (acc, p) => {
            if (p.name.trim() !== "") {
              acc[p.name] = p.type
            }
            return acc
          },
          {} as Record<string, string>
        )

        // Prepare constraint for validation
        const constraintConfig = {
          ...constraint,
          type: constraint.type,
          parameters: constraint.parameters
        }

        // Call backend validation API
        const response = await fetch("/api/proxy/constraints/validate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            constraint_config: constraintConfig,
            parameter_names: parameterNames,
            parameter_types: parameterTypes
          })
        })

        if (!response.ok) {
          throw new Error(`Validation request failed: ${response.statusText}`)
        }

        const result = await response.json()

        return {
          valid: result.valid,
          error: result.error,
          message: result.message
        }
      } catch (error) {
        console.error("Constraint validation error:", error)
        return {
          valid: false,
          error: error instanceof Error ? error.message : "Validation failed"
        }
      } finally {
        setIsValidating(false)
      }
    },
    []
  )

  const validateAllConstraints = useCallback(
    async (
      constraints: Constraint[],
      parameters: Parameter[]
    ): Promise<GlobalValidationResult> => {
      try {
        setIsValidating(true)

        const parameterNames = parameters
          .map(p => p.name)
          .filter(name => name.trim() !== "")
        const errors: ConstraintValidationResult[] = []
        const warnings: string[] = []

        // Validate each constraint individually
        for (const constraint of constraints) {
          if (!constraint.id) continue

          const result = await validateSingleConstraint(constraint, parameters)

          if (!result.valid) {
            errors.push({
              constraintId: constraint.id,
              valid: false,
              error: result.error,
              message: result.message
            })
          }

          // Update individual validation results
          setValidationResults(prev => ({
            ...prev,
            [constraint.id!]: result
          }))
        }

        // Check for potential incompatibilities between constraints
        const incompatibilityWarnings = checkConstraintIncompatibilities(
          constraints,
          parameters
        )
        warnings.push(...incompatibilityWarnings)

        // Generate summary
        let summary = ""
        if (errors.length === 0 && warnings.length === 0) {
          summary = "All constraints are valid and compatible."
        } else if (errors.length > 0) {
          summary = `${errors.length} constraint${errors.length > 1 ? "s have" : " has"} validation errors.`
        } else if (warnings.length > 0) {
          summary = `${warnings.length} potential compatibility warning${warnings.length > 1 ? "s" : ""} detected.`
        }

        return {
          valid: errors.length === 0,
          errors,
          warnings,
          summary
        }
      } catch (error) {
        console.error("Global constraint validation error:", error)
        return {
          valid: false,
          errors: [],
          warnings: [],
          summary: "Validation failed due to an error."
        }
      } finally {
        setIsValidating(false)
      }
    },
    [validateSingleConstraint]
  )

  const checkConstraintIncompatibilities = useCallback(
    (constraints: Constraint[], parameters: Parameter[]): string[] => {
      const warnings: string[] = []

      // Check for conflicting linear constraints
      const linearConstraints = constraints.filter(
        c => c.type === "ContinuousLinearConstraint"
      )
      if (linearConstraints.length > 1) {
        // Check if multiple linear constraints use the same parameters
        const parameterUsage = new Map<string, string[]>()

        linearConstraints.forEach(constraint => {
          constraint.parameters.forEach(param => {
            if (!parameterUsage.has(param)) {
              parameterUsage.set(param, [])
            }
            parameterUsage
              .get(param)!
              .push(constraint.name || constraint.id || "Unnamed")
          })
        })

        parameterUsage.forEach((constraintNames, param) => {
          if (constraintNames.length > 1) {
            warnings.push(
              `Parameter "${param}" is used in multiple linear constraints (${constraintNames.join(", ")}). Ensure these constraints are not conflicting.`
            )
          }
        })
      }

      // Check for redundant cardinality constraints
      const cardinalityConstraints = constraints.filter(
        c =>
          c.type === "ContinuousCardinalityConstraint" ||
          c.type === "DiscreteCardinalityConstraint"
      )
      if (cardinalityConstraints.length > 1) {
        warnings.push(
          "Multiple cardinality constraints detected. Ensure they don't create impossible conditions."
        )
      }

      // Check for parameter type mismatches
      const parameterTypes = parameters.reduce(
        (acc, p) => {
          if (p.name.trim() !== "") {
            acc[p.name] = p.type
          }
          return acc
        },
        {} as Record<string, string>
      )

      constraints.forEach(constraint => {
        const continuousTypes = ["NumericalContinuous"]
        const discreteTypes = ["NumericalDiscrete", "CategoricalParameter"]

        const constraintParams = constraint.parameters.filter(
          p => parameterTypes[p]
        )
        const hasContinuous = constraintParams.some(p =>
          continuousTypes.includes(parameterTypes[p])
        )
        const hasDiscrete = constraintParams.some(p =>
          discreteTypes.includes(parameterTypes[p])
        )

        if (hasContinuous && hasDiscrete) {
          warnings.push(
            `Constraint "${constraint.name || constraint.id}" mixes continuous and discrete parameters. This may cause validation errors.`
          )
        }
      })

      return warnings
    },
    []
  )

  const clearValidationResults = useCallback(() => {
    setValidationResults({})
  }, [])

  const getConstraintValidation = useCallback(
    (constraintId: string): ValidationResult | undefined => {
      return validationResults[constraintId]
    },
    [validationResults]
  )

  return {
    validateSingleConstraint,
    validateAllConstraints,
    clearValidationResults,
    getConstraintValidation,
    isValidating,
    validationResults
  }
}
