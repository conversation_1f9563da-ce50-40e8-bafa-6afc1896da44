# Acquisition Function Rules Fix

## Problem Description
The CRUD Configuration tab was not respecting the acquisition function rules that were correctly implemented in the Create Optimization form. Users could modify acquisition functions to incompatible types regardless of the optimization's objective type.

## Rules That Should Be Enforced
- **Single Target Objective**: qEI, qPI, qUCB
- **Multi Target Objective (Desirability)**: qEI, qPI, qUCB  
- **Multi Target Objective (Pareto)**: qNEHVI, qLogNEHVI, qLogParEGO

## Changes Made

### 1. Added Objective Type Detection Utility (`lib/optimization-utils.ts`)
- Created `getObjectiveType()` function to determine objective type from optimization configuration
- Handles explicit `objective_type` field and fallback detection from target structure and acquisition functions
- Returns: `"SINGLE"`, `"MULTI_DESIRABILITY"`, or `"MULTI_PARETO"`

### 2. Updated CRUD Configuration Dialog (`components/optimization/configuration-update-dialog.tsx`)

#### Type Definitions
- Extended `AcquisitionConfig` interface to include all Pareto acquisition functions:
  - Added `qNoisyExpectedHypervolumeImprovement`
  - Added `qLogNoisyExpectedHypervolumeImprovement` 
  - Added `qLogNParEGO`
- Added additional parameters: `ref_point`, `weights`, `rho`, `prune_baseline`

#### Acquisition Function Filtering
- Added `getAvailableAcquisitionFunctions()` helper function
- Dynamically filters acquisition functions based on detected objective type
- Replaces hardcoded dropdown options with filtered options

#### Enhanced UI
- Added objective type detection and display
- Shows informational message about detected objective type
- Added parameter controls for Pareto-specific acquisition functions:
  - Reference Point input for qNEHVI and qLogNEHVI
  - Objective Weights input for qLogParEGO
- Updated descriptions to include Pareto acquisition function explanations

#### Configuration Validation
- Validates existing acquisition function against objective type on dialog open
- Automatically switches to compatible default if current function is invalid
- Preserves all acquisition function parameters during initialization

### 3. Imports and Dependencies
- Added import for `getObjectiveType` utility function
- Maintained compatibility with existing configuration structure

## Testing
- Build passes successfully with TypeScript validation
- All acquisition function parameters are properly typed
- Configuration saving includes all new acquisition function parameters

## Backward Compatibility
- Existing optimizations continue to work
- Legacy configurations are automatically validated and corrected if needed
- No breaking changes to API or database schema

## Files Modified
1. `lib/optimization-utils.ts` - Added objective type detection
2. `components/optimization/configuration-update-dialog.tsx` - Updated CRUD interface

## Verification Steps
1. Create optimizations with different objective types
2. Open Configuration tab for each optimization type
3. Verify only compatible acquisition functions are shown
4. Test parameter controls for Pareto acquisition functions
5. Confirm configuration saves correctly with new parameters
