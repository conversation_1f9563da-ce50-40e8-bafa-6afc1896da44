import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockOptimizationData, mockFetch, mockApiResponses } from '../test-utils'

// Mock the optimization results component
const MockOptimizationResults = ({ 
  isEditMode, 
  onEditModeChange, 
  measurements = mockOptimizationData.measurements 
}: {
  isEditMode: boolean
  onEditModeChange: (enabled: boolean) => void
  measurements?: any[]
}) => {
  return (
    <div data-testid="optimization-results">
      <div data-testid="edit-mode-toggle">
        <label htmlFor="edit-mode">Edit Mode</label>
        <input
          id="edit-mode"
          type="checkbox"
          checked={isEditMode}
          onChange={(e) => onEditModeChange(e.target.checked)}
          disabled={measurements.length === 0}
        />
      </div>
      
      {isEditMode && (
        <div data-testid="edit-mode-controls">
          <button data-testid="parameter-edit-btn">Edit Parameter</button>
          <button data-testid="target-edit-btn">Edit Target</button>
          <button data-testid="preview-apply-btn">Preview & Apply</button>
        </div>
      )}
      
      <div data-testid="parameters-table">
        {mockOptimizationData.parameters.map((param, index) => (
          <div key={index} data-testid={`parameter-row-${param.name}`}>
            <span>{param.name}</span>
            {isEditMode && (
              <button data-testid={`quick-edit-${param.name}`}>
                Quick Edit
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

describe('Edit Mode Functionality', () => {
  const mockOnEditModeChange = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders edit mode toggle', () => {
    render(
      <MockOptimizationResults
        isEditMode={false}
        onEditModeChange={mockOnEditModeChange}
      />
    )

    expect(screen.getByLabelText('Edit Mode')).toBeInTheDocument()
    expect(screen.getByRole('checkbox')).not.toBeChecked()
  })

  it('enables edit mode when toggle is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <MockOptimizationResults
        isEditMode={false}
        onEditModeChange={mockOnEditModeChange}
      />
    )

    const toggle = screen.getByRole('checkbox')
    await user.click(toggle)

    expect(mockOnEditModeChange).toHaveBeenCalledWith(true)
  })

  it('disables edit mode when toggle is clicked again', async () => {
    const user = userEvent.setup()
    
    render(
      <MockOptimizationResults
        isEditMode={true}
        onEditModeChange={mockOnEditModeChange}
      />
    )

    const toggle = screen.getByRole('checkbox')
    await user.click(toggle)

    expect(mockOnEditModeChange).toHaveBeenCalledWith(false)
  })

  it('disables edit mode toggle when no measurements exist', () => {
    render(
      <MockOptimizationResults
        isEditMode={false}
        onEditModeChange={mockOnEditModeChange}
        measurements={[]}
      />
    )

    const toggle = screen.getByRole('checkbox')
    expect(toggle).toBeDisabled()
  })

  it('shows edit controls when edit mode is enabled', () => {
    render(
      <MockOptimizationResults
        isEditMode={true}
        onEditModeChange={mockOnEditModeChange}
      />
    )

    expect(screen.getByTestId('edit-mode-controls')).toBeInTheDocument()
    expect(screen.getByTestId('parameter-edit-btn')).toBeInTheDocument()
    expect(screen.getByTestId('target-edit-btn')).toBeInTheDocument()
    expect(screen.getByTestId('preview-apply-btn')).toBeInTheDocument()
  })

  it('hides edit controls when edit mode is disabled', () => {
    render(
      <MockOptimizationResults
        isEditMode={false}
        onEditModeChange={mockOnEditModeChange}
      />
    )

    expect(screen.queryByTestId('edit-mode-controls')).not.toBeInTheDocument()
  })

  it('shows quick edit buttons for parameters in edit mode', () => {
    render(
      <MockOptimizationResults
        isEditMode={true}
        onEditModeChange={mockOnEditModeChange}
      />
    )

    mockOptimizationData.parameters.forEach(param => {
      expect(screen.getByTestId(`quick-edit-${param.name}`)).toBeInTheDocument()
    })
  })

  it('hides quick edit buttons when edit mode is disabled', () => {
    render(
      <MockOptimizationResults
        isEditMode={false}
        onEditModeChange={mockOnEditModeChange}
      />
    )

    mockOptimizationData.parameters.forEach(param => {
      expect(screen.queryByTestId(`quick-edit-${param.name}`)).not.toBeInTheDocument()
    })
  })

  it('renders all parameter rows', () => {
    render(
      <MockOptimizationResults
        isEditMode={false}
        onEditModeChange={mockOnEditModeChange}
      />
    )

    mockOptimizationData.parameters.forEach(param => {
      expect(screen.getByTestId(`parameter-row-${param.name}`)).toBeInTheDocument()
      expect(screen.getByText(param.name)).toBeInTheDocument()
    })
  })
})

describe('Edit Mode State Management', () => {
  it('maintains edit mode state across re-renders', () => {
    const { rerender } = render(
      <MockOptimizationResults
        isEditMode={true}
        onEditModeChange={vi.fn()}
      />
    )

    expect(screen.getByRole('checkbox')).toBeChecked()
    expect(screen.getByTestId('edit-mode-controls')).toBeInTheDocument()

    rerender(
      <MockOptimizationResults
        isEditMode={true}
        onEditModeChange={vi.fn()}
      />
    )

    expect(screen.getByRole('checkbox')).toBeChecked()
    expect(screen.getByTestId('edit-mode-controls')).toBeInTheDocument()
  })

  it('properly updates UI when edit mode state changes', () => {
    const { rerender } = render(
      <MockOptimizationResults
        isEditMode={false}
        onEditModeChange={vi.fn()}
      />
    )

    expect(screen.getByRole('checkbox')).not.toBeChecked()
    expect(screen.queryByTestId('edit-mode-controls')).not.toBeInTheDocument()

    rerender(
      <MockOptimizationResults
        isEditMode={true}
        onEditModeChange={vi.fn()}
      />
    )

    expect(screen.getByRole('checkbox')).toBeChecked()
    expect(screen.getByTestId('edit-mode-controls')).toBeInTheDocument()
  })
})
