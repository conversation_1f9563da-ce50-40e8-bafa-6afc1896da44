// types/surface-color-types.ts
// Color scheme types for 3D surface plots

/**
 * Available color schemes for surface plots
 * Using only 6 proven Plotly color schemes for reliability
 */
export type SurfaceColorScheme =
  | "viridis"
  | "plasma"
  | "blues"
  | "reds"
  | "rdylbu"
  | "rdbu"

/**
 * Color scheme configuration
 */
export interface ColorSchemeConfig {
  id: SurfaceColorScheme
  name: string
  category: "Sequential" | "Diverging"
  description: string
  colorblindFriendly: boolean
  recommendedFor: string[]
}

/**
 * Available surface color schemes with metadata
 */
export const SURFACE_COLOR_SCHEMES: ColorSchemeConfig[] = [
  // Sequential color schemes
  {
    id: "viridis",
    name: "Viridis",
    category: "Sequential",
    description: "Perceptually uniform, excellent for scientific data",
    colorblindFriendly: true,
    recommendedFor: ["general", "scientific", "continuous"]
  },
  {
    id: "plasma",
    name: "Plasma",
    category: "Sequential",
    description: "High contrast, good for highlighting peaks",
    colorblindFriendly: true,
    recommendedFor: ["peaks", "maxima", "temperature"]
  },
  {
    id: "blues",
    name: "Blues",
    category: "Sequential",
    description: "Blue gradient, good for water/depth data",
    colorblindFriendly: true,
    recommendedFor: ["water", "depth", "concentration"]
  },
  {
    id: "reds",
    name: "Reds",
    category: "Sequential",
    description: "Red gradient, good for temperature/intensity",
    colorblindFriendly: false,
    recommendedFor: ["temperature", "intensity", "risk"]
  },

  // Diverging color schemes
  {
    id: "rdylbu",
    name: "Red-Yellow-Blue",
    category: "Diverging",
    description: "Classic diverging scale with neutral center",
    colorblindFriendly: false,
    recommendedFor: ["correlation", "deviation", "change"]
  },
  {
    id: "rdbu",
    name: "Red-Blue",
    category: "Diverging",
    description: "Blue-white-red diverging scale",
    colorblindFriendly: false,
    recommendedFor: ["temperature_diff", "positive_negative"]
  }
]

/**
 * Get the default color scheme
 */
export function getDefaultColorScheme(): SurfaceColorScheme {
  return "viridis"
}

/**
 * Get color schemes by category
 */
export function getColorSchemesByCategory() {
  const categories: Record<string, SurfaceColorScheme[]> = {
    Sequential: [],
    Diverging: []
  }

  SURFACE_COLOR_SCHEMES.forEach(scheme => {
    categories[scheme.category].push(scheme.id)
  })

  return categories
}

/**
 * Get colorblind-friendly color schemes
 */
export function getColorblindFriendlySchemes(): SurfaceColorScheme[] {
  return SURFACE_COLOR_SCHEMES.filter(scheme => scheme.colorblindFriendly).map(
    scheme => scheme.id
  )
}

/**
 * Map color scheme names to Plotly's expected format
 */
export function getPlotlyColorScale(scheme: SurfaceColorScheme): string {
  const plotlyScaleMap: Record<SurfaceColorScheme, string> = {
    viridis: "Viridis",
    plasma: "Plasma",
    blues: "Blues",
    reds: "Reds",
    rdylbu: "RdYlBu",
    rdbu: "RdBu"
  }

  return plotlyScaleMap[scheme] || "Viridis"
}

/**
 * Get recommended color scheme based on data characteristics
 */
export function getRecommendedColorScheme(
  data: number[][]
): SurfaceColorScheme {
  if (!data || data.length === 0) return "viridis"

  // Flatten the data to analyze
  const flatData = data.flat().filter(val => !isNaN(val))
  if (flatData.length === 0) return "viridis"

  const min = Math.min(...flatData)
  const max = Math.max(...flatData)
  const hasNegativeValues = min < 0
  const hasPositiveValues = max > 0

  // If data crosses zero, use diverging scale
  if (hasNegativeValues && hasPositiveValues) {
    return "rdbu" // Red-Blue diverging scale
  }

  // For temperature-like data (all positive), use reds
  if (min >= 0 && max > 50) {
    return "reds"
  }

  // For depth/concentration data, use blues
  if (min >= 0 && max <= 100) {
    return "blues"
  }

  // Default to viridis for general scientific use
  return "viridis"
}
