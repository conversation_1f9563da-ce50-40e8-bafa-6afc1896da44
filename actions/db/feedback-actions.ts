// actions/db/feedback-actions.ts
"use server"

import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { db } from "@/db/db"
import { feedbackTable } from "@/db/schema"
import { eq } from "drizzle-orm"
import { InsertFeedback, SelectFeedback } from "@/db/schema/feedback-schema"
import { ActionState } from "@/types"

/**
 * Create a new feedback entry in the database
 */
export async function createFeedbackAction(
  feedback: Omit<InsertFeedback, "userId">
): Promise<ActionState<SelectFeedback>> {
  try {
    // Get the current user
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 CREATE FEEDBACK ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to submit feedback"
      }
    }

    // Create the feedback with the user ID
    const [newFeedback] = await db
      .insert(feedbackTable)
      .values({
        ...feedback,
        userId
      })
      .returning()

    return {
      isSuccess: true,
      message: "Feedback submitted successfully",
      data: newFeedback
    }
  } catch (error) {
    console.error("Error creating feedback:", error)
    return {
      isSuccess: false,
      message: `Error submitting feedback: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    }
  }
}

/**
 * Get all feedback for the current user
 */
export async function getUserFeedbackAction(): Promise<
  ActionState<SelectFeedback[]>
> {
  try {
    // Get the current user
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to view feedback"
      }
    }

    // Get all feedback for this user
    const feedback = await db
      .select()
      .from(feedbackTable)
      .where(eq(feedbackTable.userId, userId))
      .orderBy(feedbackTable.createdAt)

    return {
      isSuccess: true,
      message: `Retrieved ${feedback.length} feedback items`,
      data: feedback
    }
  } catch (error) {
    console.error("Error getting user feedback:", error)
    return {
      isSuccess: false,
      message: `Error retrieving feedback: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    }
  }
}

/**
 * Get a specific feedback item by ID
 */
export async function getFeedbackByIdAction(
  feedbackId: string
): Promise<ActionState<SelectFeedback>> {
  try {
    // Get the current user
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 FEEDBACK ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to view feedback"
      }
    }

    // Get the feedback
    const [feedback] = await db
      .select()
      .from(feedbackTable)
      .where(eq(feedbackTable.id, feedbackId))
      .limit(1)

    if (!feedback) {
      return {
        isSuccess: false,
        message: "Feedback not found"
      }
    }

    // Ensure the user has access to this feedback
    if (feedback.userId !== userId) {
      return {
        isSuccess: false,
        message: "You do not have permission to view this feedback"
      }
    }

    return {
      isSuccess: true,
      message: "Feedback retrieved successfully",
      data: feedback
    }
  } catch (error) {
    console.error("Error getting feedback by ID:", error)
    return {
      isSuccess: false,
      message: `Error retrieving feedback: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    }
  }
}

/**
 * Update the status of a feedback item
 */
export async function updateFeedbackStatusAction(
  feedbackId: string,
  status: string
): Promise<ActionState<void>> {
  try {
    // Get the current user
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to update feedback"
      }
    }

    // Get the feedback to check ownership
    const feedbackResult = await getFeedbackByIdAction(feedbackId)
    if (!feedbackResult.isSuccess || !feedbackResult.data) {
      return {
        isSuccess: false,
        message: feedbackResult.message
      }
    }

    // Update the feedback status
    await db
      .update(feedbackTable)
      .set({
        status,
        updatedAt: new Date(),
        // If status is 'resolved', set resolvedAt
        ...(status === "resolved" ? { resolvedAt: new Date() } : {})
      })
      .where(eq(feedbackTable.id, feedbackId))

    return {
      isSuccess: true,
      data: undefined,
      message: "Feedback status updated successfully"
    }
  } catch (error) {
    console.error("Error updating feedback status:", error)
    return {
      isSuccess: false,
      message: `Error updating feedback status: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    }
  }
}
