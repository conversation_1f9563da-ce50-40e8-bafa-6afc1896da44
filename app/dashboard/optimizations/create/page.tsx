// app/dashboard/optimizations/create/page.tsx
"use server"

import { CreateOptimizationWizard } from "@/components/optimization/create-optimization-wizard"
import { CreateOptimizationDemo } from "@/components/demo/create-optimization-demo"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { DashboardThemeWrapper } from "@/components/dashboard/dashboard-theme-wrapper"

interface CreateOptimizationPageProps {
  searchParams: Promise<{ demo?: string; guided?: string }>
}

export default async function CreateOptimizationPage({
  searchParams
}: CreateOptimizationPageProps) {
  const { userId } = await auth()

  if (!userId) {
    redirect("/login?redirect_url=/dashboard/optimizations/create")
  }

  const params = await searchParams
  const isDemo = params.demo === "true"
  const isGuided = params.guided === "true"

  if (isDemo) {
    return <CreateOptimizationDemo isGuided={isGuided} />
  }

  return (
    <DashboardThemeWrapper>
      <h1 className="mb-6 text-3xl font-bold">Create New Optimization</h1>
      <CreateOptimizationWizard />
    </DashboardThemeWrapper>
  )
}
