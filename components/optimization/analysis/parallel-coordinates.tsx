"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import {
  <PERSON><PERSON>hart,
  RefreshCw,
  AlertCircle,
  Maximize2,
  Info
} from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import dynamic from "next/dynamic"

// Import Plotly dynamically to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

interface ParallelCoordinatesProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  parameterNames: string[]
  targetNames: string[]
  onTargetSelect?: (target: string) => void
  selectedTarget?: string
}

interface ParallelCoordinatesData {
  dimensions: Array<{
    label: string
    values: number[]
    range: [number, number]
    isParameter: boolean
    isTarget: boolean
    isMaximize?: boolean
  }>
  experimentIndices: number[]
}

export function ParallelCoordinates({
  optimization,
  measurements,
  parameterNames,
  targetNames,
  onTargetSelect,
  selectedTarget
}: ParallelCoordinatesProps) {
  const [parallelData, setParallelData] =
    useState<ParallelCoordinatesData | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [includeParameters, setIncludeParameters] = useState<boolean>(true)
  const [includeTargets, setIncludeTargets] = useState<boolean>(true)
  const [colorByTarget, setColorByTarget] = useState<string | null>(null)

  // Calculate parallel coordinates data
  const calculateParallelData = () => {
    setIsLoading(true)
    setError(null)

    try {
      // Extract parameter and target values
      const dimensions: ParallelCoordinatesData["dimensions"] = []
      const experimentIndices: number[] = []

      // Initialize parameter dimensions
      if (includeParameters) {
        parameterNames.forEach(param => {
          dimensions.push({
            label: param,
            values: [],
            range: [Infinity, -Infinity],
            isParameter: true,
            isTarget: false
          })
        })
      }

      // Initialize target dimensions
      if (includeTargets) {
        targetNames.forEach(target => {
          // Determine if target should be maximized or minimized
          // Default to maximize if not specified
          let isMaximize = true

          // Check if target configuration is available in optimization config
          if (optimization.config && typeof optimization.config === "object") {
            const config = optimization.config as any
            if (config.target_config && Array.isArray(config.target_config)) {
              const targetConfig = config.target_config.find(
                (t: any) => t.name === target
              )
              if (targetConfig && targetConfig.mode) {
                isMaximize = targetConfig.mode.toUpperCase() === "MAX"
              }
            }
          }

          dimensions.push({
            label: target,
            values: [],
            range: [Infinity, -Infinity],
            isParameter: false,
            isTarget: true,
            isMaximize
          })
        })
      }

      // Collect values
      measurements.forEach((measurement, index) => {
        experimentIndices.push(index + 1) // 1-based indices for display

        // Extract parameter values
        if (includeParameters) {
          parameterNames.forEach((param, paramIndex) => {
            // Add type assertion to handle unknown type
            const params = measurement.parameters as Record<
              string,
              string | number
            >
            const value = parseFloat(String(params[param]))
            if (!isNaN(value)) {
              dimensions[paramIndex].values.push(value)

              // Update ranges
              dimensions[paramIndex].range[0] = Math.min(
                dimensions[paramIndex].range[0],
                value
              )
              dimensions[paramIndex].range[1] = Math.max(
                dimensions[paramIndex].range[1],
                value
              )
            } else {
              // Push NaN to maintain index alignment
              dimensions[paramIndex].values.push(NaN)
            }
          })
        }

        // Extract target values
        if (includeTargets) {
          const targetOffset = includeParameters ? parameterNames.length : 0

          if (measurement.targetValues) {
            const targetValuesObj = measurement.targetValues as Record<
              string,
              string | number
            >
            targetNames.forEach((target, targetIndex) => {
              const value = parseFloat(String(targetValuesObj[target]))
              if (!isNaN(value)) {
                dimensions[targetOffset + targetIndex].values.push(value)

                // Update ranges
                dimensions[targetOffset + targetIndex].range[0] = Math.min(
                  dimensions[targetOffset + targetIndex].range[0],
                  value
                )
                dimensions[targetOffset + targetIndex].range[1] = Math.max(
                  dimensions[targetOffset + targetIndex].range[1],
                  value
                )
              } else {
                // Push NaN to maintain index alignment
                dimensions[targetOffset + targetIndex].values.push(NaN)
              }
            })
          } else if (targetNames.includes(optimization.targetName)) {
            // Single target case
            const targetIndex = targetNames.indexOf(optimization.targetName)
            const value = parseFloat(measurement.targetValue)
            if (!isNaN(value)) {
              dimensions[targetOffset + targetIndex].values.push(value)

              // Update ranges
              dimensions[targetOffset + targetIndex].range[0] = Math.min(
                dimensions[targetOffset + targetIndex].range[0],
                value
              )
              dimensions[targetOffset + targetIndex].range[1] = Math.max(
                dimensions[targetOffset + targetIndex].range[1],
                value
              )
            } else {
              // Push NaN to maintain index alignment
              dimensions[targetOffset + targetIndex].values.push(NaN)
            }

            // Push NaN for other targets to maintain index alignment
            targetNames.forEach((target, idx) => {
              if (target !== optimization.targetName) {
                dimensions[targetOffset + idx].values.push(NaN)
              }
            })
          }
        }
      })

      // Filter out dimensions with no valid values
      const validDimensions = dimensions.filter(dim => {
        return (
          dim.values.some(v => !isNaN(v)) &&
          dim.range[0] !== Infinity &&
          dim.range[1] !== -Infinity
        )
      })

      // Ensure ranges have some width to avoid Plotly errors
      validDimensions.forEach(dim => {
        if (dim.range[0] === dim.range[1]) {
          dim.range[0] -= 0.5
          dim.range[1] += 0.5
        }
      })

      setParallelData({
        dimensions: validDimensions,
        experimentIndices
      })

      // Set color by target if not already set
      if (!colorByTarget && targetNames.length > 0) {
        setColorByTarget(targetNames[0])
      }

      console.log("Parallel coordinates data calculated:", {
        dimensionCount: validDimensions.length,
        measurementCount: measurements.length
      })
    } catch (err) {
      console.error("Error calculating parallel coordinates data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // Generate parallel coordinates plot data
  const parallelPlotData = useMemo(() => {
    if (!parallelData) return []

    const { dimensions, experimentIndices } = parallelData

    // Find color dimension if colorByTarget is set
    let colorValues: number[] = []
    let colorRange: [number, number] = [0, 1]
    let colorTitle = ""

    if (colorByTarget) {
      const colorDim = dimensions.find(dim => dim.label === colorByTarget)
      if (colorDim) {
        colorValues = colorDim.values
        colorRange = colorDim.range
        colorTitle = `${colorByTarget} ${colorDim.isMaximize ? "(Maximize)" : "(Minimize)"}`
      }
    }

    // Create parallel coordinates trace
    return [
      {
        type: "parcoords" as const,
        line: {
          color: colorValues.length > 0 ? colorValues : undefined,
          colorscale: "Viridis" as const,
          cmin: colorRange[0],
          cmax: colorRange[1],
          showscale: colorValues.length > 0,
          colorbar: {
            title: colorTitle
          }
        },
        dimensions: dimensions.map(dim => ({
          label: dim.isTarget
            ? `${dim.label} ${dim.isMaximize ? "(Max)" : "(Min)"}`
            : dim.label,
          values: dim.values,
          range: dim.range,
          constraintrange: dim.range // Allow filtering on the full range initially
        }))
      }
    ]
  }, [parallelData, colorByTarget])

  // Handle target selection
  const handleTargetSelect = (target: string) => {
    setColorByTarget(target)

    if (onTargetSelect) {
      onTargetSelect(target)
    }
  }

  // Effect to calculate parallel data when measurements change
  useEffect(() => {
    if (
      measurements.length > 0 &&
      (parameterNames.length > 0 || targetNames.length > 0)
    ) {
      calculateParallelData()
    }
  }, [
    measurements.length,
    parameterNames.length,
    targetNames.length,
    includeParameters,
    includeTargets
  ])

  // Effect to update colorByTarget when selectedTarget changes
  useEffect(() => {
    if (selectedTarget && targetNames.includes(selectedTarget)) {
      setColorByTarget(selectedTarget)
    }
  }, [selectedTarget, targetNames])

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="flex items-center text-lg font-medium">
            <LineChart className="text-primary mr-2 size-5" />
            Parallel Coordinates
          </h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={calculateParallelData}
              disabled={isLoading}
            >
              {isLoading ? (
                <RefreshCw className="size-4 animate-spin" />
              ) : (
                <RefreshCw className="size-4" />
              )}
              <span className="ml-2 hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>

        <div className="mb-4 flex flex-wrap gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="include-parameters"
              checked={includeParameters}
              onCheckedChange={setIncludeParameters}
            />
            <Label htmlFor="include-parameters">Parameters</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="include-targets"
              checked={includeTargets}
              onCheckedChange={setIncludeTargets}
            />
            <Label htmlFor="include-targets">Targets</Label>
          </div>
          {targetNames.length > 0 && (
            <div className="flex items-center space-x-2">
              <Label htmlFor="color-by">Color by:</Label>
              <select
                id="color-by"
                className="border-input bg-background h-8 rounded-md border px-3 py-1 text-sm"
                value={colorByTarget || ""}
                onChange={e => handleTargetSelect(e.target.value)}
              >
                <option value="">None</option>
                {targetNames.map(target => (
                  <option key={target} value={target}>
                    {target}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="size-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex h-[400px] items-center justify-center">
            <Skeleton className="size-full rounded-md" />
          </div>
        ) : parallelData && parallelData.dimensions.length > 0 ? (
          <div className="space-y-4">
            <div className="h-[400px] w-full overflow-hidden rounded-md border">
              <Plot
                data={parallelPlotData}
                layout={{
                  autosize: true,
                  title: "Parallel Coordinates Plot",
                  margin: { l: 80, r: 80, t: 50, b: 30 }
                }}
                config={{ responsive: true, displayModeBar: false }}
                style={{ width: "100%", height: "100%" }}
              />
            </div>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full">
                  <Maximize2 className="mr-2 size-4" />
                  Expand View
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Parallel Coordinates</DialogTitle>
                </DialogHeader>
                <div className="h-[600px] w-full">
                  <Plot
                    data={parallelPlotData}
                    layout={{
                      autosize: true,
                      title: "Parallel Coordinates Plot",
                      margin: { l: 80, r: 80, t: 50, b: 30 },
                      height: 600
                    }}
                    config={{ responsive: true }}
                    style={{ width: "100%", height: "100%" }}
                  />
                </div>
              </DialogContent>
            </Dialog>

            <Alert>
              <Info className="size-4" />
              <AlertTitle>Interactive Filtering</AlertTitle>
              <AlertDescription>
                Click and drag on any axis to filter the data. This allows you
                to explore relationships between parameters and targets
                interactively.
              </AlertDescription>
            </Alert>
          </div>
        ) : (
          <div className="flex h-[400px] flex-col items-center justify-center p-4 text-center">
            <LineChart className="text-muted-foreground mb-4 size-12" />
            <h3 className="text-lg font-medium">No data available</h3>
            <p className="text-muted-foreground mt-2 max-w-md text-sm">
              There might not be enough measurements with valid parameter or
              target values.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
