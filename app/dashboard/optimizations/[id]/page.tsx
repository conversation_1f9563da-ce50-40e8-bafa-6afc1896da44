// app/dashboard/optimizations/[id]/page.tsx
"use server"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { OptimizationResults } from "@/components/optimization/optimization-results"
import { DeleteOptimizationButton } from "@/components/optimization/delete-optimization-button"
import { auth } from "@clerk/nextjs/server"
import { redirect, notFound } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"
import {
  getOptimizationByIdAction,
  getMeasurementsAction
} from "@/actions/db/optimizations-actions"
import { getBestPointWorkflowAction } from "@/actions/optimization-workflow-actions"
import { checkOptimizationExists } from "@/actions/optimization-actions"
import Link from "next/link"
import { Beaker, ArrowLeft } from "lucide-react"
import { DashboardThemeWrapper } from "@/components/dashboard/dashboard-theme-wrapper"

interface OptimizationDetailsPageProps {
  params: Promise<{ id: string }>
}

export default async function OptimizationDetailsPage({
  params
}: OptimizationDetailsPageProps) {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🔍 OPTIMIZATION DETAILS - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  })

  if (!userId) {
    console.log("❌ OPTIMIZATION DETAILS - No user ID, redirecting to login")
    redirect("/login")
  }

  const { id } = await params

  // Get the optimization from the database
  const optimizationResult = await getOptimizationByIdAction(id)

  if (!optimizationResult.isSuccess || !optimizationResult.data) {
    notFound()
  }

  const optimization = optimizationResult.data

  // Make sure the optimization belongs to the current user
  if (optimization.userId !== userId) {
    redirect("/dashboard/optimizations")
  }

  // Get measurements for this optimization
  const measurementsResult = await getMeasurementsAction(optimization.id)
  const measurements = measurementsResult.isSuccess
    ? measurementsResult.data
    : []

  // Get the current best point from the API
  let bestPoint = undefined
  try {
    console.log(
      "Getting best point for optimizer ID:",
      optimization.optimizerId
    )

    // For newly created optimizations, we need to wait a bit for the backend to initialize
    const isNewOptimization =
      Date.now() - new Date(optimization.createdAt).getTime() < 60000 // Less than 1 minute old

    if (isNewOptimization) {
      console.log(
        "This is a newly created optimization. Adding delay before checking best point."
      )
      // Add a small delay to ensure the optimization is fully created in the backend
      await new Promise(resolve => setTimeout(resolve, 3000))
    }

    // Check if the optimization exists in the backend
    const existsResult = await checkOptimizationExists(optimization.optimizerId)
    if (!existsResult.isSuccess || !existsResult.data) {
      console.error(
        "Optimization does not exist in the backend:",
        optimization.optimizerId
      )
      // Continue without the best point data
    } else {
      // If the optimization exists, get the best point
      const bestPointResult = await getBestPointWorkflowAction(
        optimization.optimizerId
      )

      if (bestPointResult.isSuccess) {
        bestPoint = bestPointResult.data
        console.log("Successfully retrieved best point:", bestPoint)
      } else {
        console.log(
          "No best point available yet. This is normal for new optimizations without measurements."
        )
      }
    }
  } catch (error) {
    console.error("Error getting best point:", error)
    // Continue without the best point data
  }

  return (
    <DashboardThemeWrapper>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/dashboard/optimizations">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-1 size-4" />
              Back
            </Button>
          </Link>
          <DeleteOptimizationButton
            optimizationId={optimization.id}
            optimizationName={optimization.name}
          />
        </div>
        <Link href={`/dashboard/optimizations/${id}/run`}>
          <Button>
            <Beaker className="mr-2 size-4" />
            Run Experiments
          </Button>
        </Link>
      </div>

      <OptimizationResults
        optimization={optimization}
        measurements={measurements}
        initialBestPoint={bestPoint}
      />
    </DashboardThemeWrapper>
  )
}
