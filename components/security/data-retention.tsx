"use client"

import { motion } from "framer-motion"
import {
  Clock,
  Trash2,
  Archive,
  Shield,
  Calendar,
  AlertCircle
} from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const retentionPolicies = [
  {
    icon: Clock,
    category: "Account Data",
    retention: "Account Lifetime",
    description:
      "Essential account information retained while account is active",
    dataTypes: ["Email address", "Account preferences", "Subscription status"],
    deletion: "Deleted within 30 days of account closure",
    legal: "Required for service provision and legal compliance"
  },
  {
    icon: Archive,
    category: "Optimization Data",
    retention: "2 Years (Configurable)",
    description: "Experiment data and optimization results with user control",
    dataTypes: ["Experiment parameters", "Results", "Optimization history"],
    deletion: "User-configurable or automatic after retention period",
    legal: "Retained for reproducibility and scientific integrity"
  },
  {
    icon: Shield,
    category: "Security Logs",
    retention: "12 Months",
    description: "Security and audit logs for compliance and incident response",
    dataTypes: ["Access logs", "Authentication events", "Security incidents"],
    deletion: "Automatically purged after 12 months",
    legal: "Required for security monitoring and compliance"
  },
  {
    icon: Calendar,
    category: "Usage Analytics",
    retention: "12 Months",
    description: "Anonymized usage data for service improvement",
    dataTypes: ["Feature usage", "Performance metrics", "Error logs"],
    deletion: "Automatically anonymized and aggregated",
    legal: "Used for service improvement and optimization"
  }
]

const retentionPrinciples = [
  {
    title: "Data Minimization",
    description:
      "We retain only data necessary for legitimate business purposes"
  },
  {
    title: "User Control",
    description:
      "Users can configure retention periods for their optimization data"
  },
  {
    title: "Automatic Deletion",
    description: "Data is automatically deleted when retention periods expire"
  },
  {
    title: "Secure Disposal",
    description: "All deleted data is securely destroyed and unrecoverable"
  },
  {
    title: "Legal Compliance",
    description:
      "Retention policies comply with applicable data protection laws"
  },
  {
    title: "Transparency",
    description:
      "Clear communication about what data is retained and for how long"
  }
]

const retentionSchedule = [
  { period: "Real-time", action: "Data validation and processing" },
  { period: "Daily", action: "Backup creation and verification" },
  { period: "Weekly", action: "Data integrity checks and cleanup" },
  { period: "Monthly", action: "Retention policy enforcement" },
  { period: "Quarterly", action: "Data audit and compliance review" },
  { period: "Annually", action: "Policy review and updates" }
]

export function DataRetention() {
  return (
    <section className="bg-gray-50 py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Data Retention & Deletion
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            We maintain clear data retention policies to ensure your data is
            kept only as long as necessary and securely deleted when no longer
            needed.
          </p>
        </motion.div>

        <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-2">
          {retentionPolicies.map((policy, index) => (
            <motion.div
              key={policy.category}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="flex size-12 items-center justify-center rounded-lg bg-orange-100">
                      <policy.icon className="size-6 text-orange-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">
                        {policy.category}
                      </CardTitle>
                      <Badge variant="outline">{policy.retention}</Badge>
                    </div>
                  </div>
                  <CardDescription className="text-gray-600">
                    {policy.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="mb-2 text-sm font-semibold text-gray-900">
                        Data Types:
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {policy.dataTypes.map((type, typeIndex) => (
                          <Badge
                            key={typeIndex}
                            variant="secondary"
                            className="text-xs"
                          >
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="mb-1 text-sm font-semibold text-gray-900">
                        Deletion Policy:
                      </h4>
                      <p className="text-sm text-gray-600">{policy.deletion}</p>
                    </div>
                    <div>
                      <h4 className="mb-1 text-sm font-semibold text-gray-900">
                        Legal Basis:
                      </h4>
                      <p className="text-sm text-gray-600">{policy.legal}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mb-12"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                Data Retention Principles
              </CardTitle>
              <CardDescription>
                Core principles governing our data retention practices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {retentionPrinciples.map((principle, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="flex size-8 shrink-0 items-center justify-center rounded-full bg-blue-100">
                      <Trash2 className="size-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {principle.title}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {principle.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="mb-8"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                Data Lifecycle Management
              </CardTitle>
              <CardDescription>
                Automated processes for data retention and deletion
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {retentionSchedule.map((schedule, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg bg-gray-50 p-3"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex size-8 items-center justify-center rounded-full bg-green-100">
                        <Clock className="size-4 text-green-600" />
                      </div>
                      <div>
                        <span className="font-medium text-gray-900">
                          {schedule.period}
                        </span>
                      </div>
                    </div>
                    <span className="text-sm text-gray-600">
                      {schedule.action}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <Card className="bg-gradient-to-r from-orange-50 to-red-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <AlertCircle className="mx-auto mb-4 size-12 text-orange-600" />
                <h3 className="mb-4 text-xl font-semibold text-gray-900">
                  Important Notice
                </h3>
                <p className="mb-6 text-gray-600">
                  You have full control over your data retention settings. You
                  can configure retention periods, request early deletion, or
                  export your data at any time.
                </p>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-blue-600">
                      Configure
                    </div>
                    <div className="text-sm text-gray-600">
                      Retention Settings
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-green-600">
                      Request
                    </div>
                    <div className="text-sm text-gray-600">Early Deletion</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-purple-600">
                      Export
                    </div>
                    <div className="text-sm text-gray-600">Your Data</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
