"use client"

import { useState } from "react"
import { Code, Alert<PERSON><PERSON>gle, CheckCircle, HelpCircle } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import {
  <PERSON>ltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { MultiSelect } from "@/components/ui/multi-select"
import { DiscreteCustomConstraint } from "@/lib/constraints"

interface CustomConstraintEditorProps {
  constraint: DiscreteCustomConstraint
  availableParameters: string[]
  onChange: (updates: Partial<DiscreteCustomConstraint>) => void
}

export function CustomConstraintEditor({
  constraint,
  availableParameters,
  onChange
}: CustomConstraintEditorProps) {
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean
    error?: string
  }>({ isValid: true })

  const handleParametersChange = (parameters: string[]) => {
    onChange({ parameters })
  }

  const handleValidatorChange = (validator: string) => {
    onChange({ validator })
    validateFunction(validator)
  }

  const validateFunction = (functionStr: string) => {
    if (!functionStr.trim()) {
      setValidationResult({ isValid: false, error: "Function cannot be empty" })
      return
    }

    // Basic syntax validation
    try {
      // Check for dangerous keywords
      const dangerous = ["import", "exec", "eval", "open", "file", "__"]
      const hasDangerous = dangerous.some(keyword =>
        functionStr.toLowerCase().includes(keyword)
      )

      if (hasDangerous) {
        setValidationResult({
          isValid: false,
          error: "Function contains potentially unsafe operations"
        })
        return
      }

      // Check for basic syntax issues
      if (
        !functionStr.includes("return") &&
        !functionStr.match(/^[^=]*[<>=!]+/)
      ) {
        setValidationResult({
          isValid: false,
          error:
            "Function should return a boolean value or use comparison operators"
        })
        return
      }

      setValidationResult({ isValid: true })
    } catch (error) {
      setValidationResult({
        isValid: false,
        error: "Invalid function syntax"
      })
    }
  }

  const insertExample = (example: string) => {
    onChange({ validator: example })
    validateFunction(example)
  }

  const parameterOptions = availableParameters.map(param => ({
    value: param,
    label: param
  }))

  const examples = [
    {
      name: "Temperature-Pressure Safety",
      code: "temperature * pressure <= 1000",
      description: "Product of temperature and pressure must not exceed 1000"
    },
    {
      name: "Mixture Ratio",
      code: "component_A + component_B + component_C == 1.0",
      description: "Components must sum to exactly 1.0"
    },
    {
      name: "Minimum Efficiency",
      code: "sqrt(temperature) * catalyst_loading >= 5.0",
      description:
        "Square root of temperature times catalyst loading must be at least 5"
    },
    {
      name: "Complex Relationship",
      code: "log(pressure + 1) * exp(temperature / 100) <= 50",
      description: "Logarithmic-exponential relationship constraint"
    }
  ]

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Constraint Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Code className="size-4" />
              Custom Constraint Function
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="bg-muted min-h-[60px] rounded-md p-3 font-mono text-sm">
                {constraint.validator ||
                  "// Enter your constraint function here"}
              </div>
              <div className="flex items-center gap-2">
                {validationResult.isValid ? (
                  <div className="flex items-center gap-1 text-green-600">
                    <CheckCircle className="size-4" />
                    <span className="text-sm">Function syntax looks valid</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-1 text-red-600">
                    <AlertTriangle className="size-4" />
                    <span className="text-sm">{validationResult.error}</span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Parameter Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-base font-medium">Parameters</Label>
            <Badge variant="outline">
              {constraint.parameters.length} selected
            </Badge>
          </div>

          <MultiSelect
            options={parameterOptions}
            value={constraint.parameters}
            onValueChange={handleParametersChange}
            placeholder="Select parameters used in the function..."
            maxCount={10}
          />
          <p className="text-muted-foreground text-sm">
            Select all parameters that your function references
          </p>
        </div>

        {/* Function Editor */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Label htmlFor="validator" className="text-base font-medium">
              Constraint Function
            </Label>
            <Tooltip>
              <TooltipTrigger>
                <HelpCircle className="text-muted-foreground size-4" />
              </TooltipTrigger>
              <TooltipContent side="right" className="max-w-[300px]">
                <div className="space-y-2 text-sm">
                  <p>
                    <strong>Available functions:</strong>
                  </p>
                  <p>Math: sqrt, exp, log, sin, cos, abs, min, max</p>
                  <p>
                    <strong>Operators:</strong> +, -, *, /, **, &lt;, &gt;,
                    &lt;=, &gt;=, ==, !=
                  </p>
                  <p>
                    <strong>Return:</strong> Must return True/False or use
                    comparison
                  </p>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>

          <Textarea
            id="validator"
            value={constraint.validator || ""}
            onChange={e => handleValidatorChange(e.target.value)}
            placeholder="Enter your constraint function (e.g., temperature * pressure <= 1000)"
            rows={4}
            className="font-mono"
          />

          <p className="text-muted-foreground text-sm">
            Write a Python expression that returns True when the constraint is
            satisfied. Use parameter names directly in your expression.
          </p>
        </div>

        {/* Examples */}
        <Card className="bg-muted/30">
          <CardHeader>
            <CardTitle className="text-sm">Example Functions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {examples.map((example, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{example.name}</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => insertExample(example.code)}
                    className="text-xs"
                  >
                    Use This
                  </Button>
                </div>
                <div className="bg-background rounded border p-2 font-mono text-xs">
                  {example.code}
                </div>
                <p className="text-muted-foreground text-xs">
                  {example.description}
                </p>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Safety Warning */}
        <Alert>
          <AlertTriangle className="size-4" />
          <AlertDescription>
            <strong>Safety Note:</strong> Custom functions are executed on the
            server. Only use trusted mathematical expressions. Dangerous
            operations like file access or imports are blocked.
          </AlertDescription>
        </Alert>

        {/* Function Guidelines */}
        <Card className="border-blue-200 bg-blue-50/50">
          <CardHeader>
            <CardTitle className="text-sm text-blue-800">
              Function Guidelines
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm text-blue-700">
            <div>
              <strong>✓ Allowed:</strong> Mathematical operations, comparisons,
              math functions
            </div>
            <div>
              <strong>✓ Return:</strong> Boolean value (True/False) or
              comparison expression
            </div>
            <div>
              <strong>✓ Parameters:</strong> Use parameter names directly (e.g.,
              temperature, pressure)
            </div>
            <div>
              <strong>✗ Forbidden:</strong> File operations, imports, exec/eval,
              system calls
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  )
}
