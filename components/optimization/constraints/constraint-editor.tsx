"use client"

import { useState, useEffect } from "react"
import { Save, X, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { LinearConstraintEditor } from "./editors/linear-constraint-editor"
import { CardinalityConstraintEditor } from "./editors/cardinality-constraint-editor"
import { ExcludeConstraintEditor } from "./editors/exclude-constraint-editor"
import { SumProductConstraintEditor } from "./editors/sum-product-constraint-editor"
import { CustomConstraintEditor } from "./editors/custom-constraint-editor"
import { DependenciesConstraintEditor } from "./editors/dependencies-constraint-editor"
import { Constraint } from "@/lib/constraints"

interface ConstraintEditorProps {
  constraint: Constraint
  availableParameters: string[]
  onSave: (constraint: Constraint) => void
  onCancel: () => void
}

export function ConstraintEditor({
  constraint,
  availableParameters,
  onSave,
  onCancel
}: ConstraintEditorProps) {
  const [editedConstraint, setEditedConstraint] =
    useState<Constraint>(constraint)
  const [validationError, setValidationError] = useState<string>("")

  useEffect(() => {
    setEditedConstraint(constraint)
  }, [constraint])

  const handleSave = () => {
    // Basic validation
    if (!editedConstraint.name?.trim()) {
      setValidationError("Constraint name is required")
      return
    }

    if (editedConstraint.parameters.length === 0) {
      setValidationError("At least one parameter must be selected")
      return
    }

    // Check if all parameters exist
    const missingParams = editedConstraint.parameters.filter(
      param => !availableParameters.includes(param)
    )
    if (missingParams.length > 0) {
      setValidationError(`Unknown parameters: ${missingParams.join(", ")}`)
      return
    }

    // Type-specific validation
    const typeValidation = validateConstraintType(editedConstraint)
    if (typeValidation) {
      setValidationError(typeValidation)
      return
    }

    // Parameter type compatibility validation is handled by the backend
    // Frontend doesn't have access to actual parameter types, so we skip this check

    setValidationError("")
    onSave(editedConstraint)
  }

  const validateConstraintType = (constraint: Constraint): string => {
    switch (constraint.type) {
      case "ContinuousLinearConstraint": {
        const linear = constraint as any
        if (!linear.operator) return "Operator is required"
        if (!linear.coefficients || linear.coefficients.length === 0)
          return "Coefficients are required"
        if (linear.coefficients.length !== constraint.parameters.length) {
          return "Number of coefficients must match number of parameters"
        }
        if (linear.rhs === undefined || linear.rhs === null)
          return "Right-hand side value is required"
        break
      }
      case "ContinuousCardinalityConstraint":
      case "DiscreteCardinalityConstraint": {
        const cardinality = constraint as any
        if (
          cardinality.min_cardinality !== undefined &&
          cardinality.max_cardinality !== undefined
        ) {
          if (cardinality.min_cardinality > cardinality.max_cardinality) {
            return "Minimum cardinality cannot be greater than maximum cardinality"
          }
        }
        break
      }
      case "DiscreteExcludeConstraint": {
        const exclude = constraint as any
        if (!exclude.conditions || exclude.conditions.length === 0) {
          return "At least one condition is required"
        }
        if (exclude.conditions.length !== constraint.parameters.length) {
          return "Number of conditions must match number of parameters"
        }
        break
      }
      case "DiscreteCustomConstraint": {
        const custom = constraint as any
        if (!custom.validator?.trim()) return "Validator function is required"
        break
      }
    }
    return ""
  }

  const updateConstraint = (updates: Partial<Constraint>) => {
    setEditedConstraint(prev => ({ ...prev, ...updates }) as Constraint)
    setValidationError("")
  }

  const renderConstraintEditor = () => {
    switch (editedConstraint.type) {
      case "ContinuousLinearConstraint":
        return (
          <LinearConstraintEditor
            constraint={editedConstraint as any}
            availableParameters={availableParameters}
            onChange={updateConstraint}
          />
        )

      case "ContinuousCardinalityConstraint":
      case "DiscreteCardinalityConstraint":
        return (
          <CardinalityConstraintEditor
            constraint={editedConstraint as any}
            availableParameters={availableParameters}
            onChange={updateConstraint}
          />
        )

      case "DiscreteExcludeConstraint":
        return (
          <ExcludeConstraintEditor
            constraint={editedConstraint as any}
            availableParameters={availableParameters}
            onChange={updateConstraint}
          />
        )

      case "DiscreteSumConstraint":
      case "DiscreteProductConstraint":
        return (
          <SumProductConstraintEditor
            constraint={editedConstraint as any}
            availableParameters={availableParameters}
            onChange={updateConstraint}
          />
        )

      case "DiscreteCustomConstraint":
        return (
          <CustomConstraintEditor
            constraint={editedConstraint as any}
            availableParameters={availableParameters}
            onChange={updateConstraint}
          />
        )

      case "DiscreteDependenciesConstraint":
        return (
          <DependenciesConstraintEditor
            constraint={editedConstraint as any}
            availableParameters={availableParameters}
            onChange={updateConstraint}
          />
        )

      default:
        return (
          <Alert>
            <AlertCircle className="size-4" />
            <AlertDescription>
              Editor for constraint type "{editedConstraint.type}" is not yet
              implemented.
            </AlertDescription>
          </Alert>
        )
    }
  }

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="constraint-name">Constraint Name</Label>
          <Input
            id="constraint-name"
            value={editedConstraint.name || ""}
            onChange={e => updateConstraint({ name: e.target.value })}
            placeholder="Enter constraint name..."
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="constraint-description">Description (Optional)</Label>
          <Textarea
            id="constraint-description"
            value={editedConstraint.description || ""}
            onChange={e => updateConstraint({ description: e.target.value })}
            placeholder="Describe what this constraint does..."
            rows={2}
          />
        </div>
      </div>

      <Separator />

      {/* Type-Specific Editor */}
      <div className="space-y-4">
        <h4 className="font-medium">Constraint Configuration</h4>
        {renderConstraintEditor()}
      </div>

      {/* Validation Error */}
      {validationError && (
        <Alert variant="destructive">
          <AlertCircle className="size-4" />
          <AlertDescription>{validationError}</AlertDescription>
        </Alert>
      )}

      {/* Actions */}
      <div className="flex justify-end gap-2 border-t pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          <X className="mr-2 size-4" />
          Cancel
        </Button>
        <Button type="button" onClick={handleSave}>
          <Save className="mr-2 size-4" />
          Save Constraint
        </Button>
      </div>
    </div>
  )
}
