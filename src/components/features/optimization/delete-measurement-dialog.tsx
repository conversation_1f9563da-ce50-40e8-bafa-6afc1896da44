"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Trash2, AlertTriangle, Clock } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { deleteMeasurementWithDependenciesAction } from "@/actions/measurement-dependency-actions"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { formatDistanceToNow } from "date-fns"

interface DeleteMeasurementDialogProps {
  measurement: SelectMeasurement
  optimization: SelectOptimization
  experimentNumber: string
  onMeasurementDeleted: (measurementId: string) => void
  trigger?: React.ReactNode
  totalMeasurements: number
}

export function DeleteMeasurementDialog({
  measurement,
  optimization,
  experimentNumber,
  onMeasurementDeleted,
  trigger,
  totalMeasurements
}: DeleteMeasurementDialogProps) {
  const [open, setOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async () => {
    setIsDeleting(true)

    try {
      const result = await deleteMeasurementWithDependenciesAction(
        measurement.id,
        optimization.id,
        optimization.optimizerId
      )

      if (result.isSuccess) {
        toast({
          title: "Success",
          description:
            "Measurement deleted successfully. Dependencies have been recalculated."
        })
        onMeasurementDeleted(measurement.id)
        setOpen(false)
      } else {
        // Handle specific error cases
        let errorMessage = result.message
        if (result.message.includes("permission")) {
          errorMessage = "You don't have permission to delete this measurement"
        } else if (result.message.includes("not found")) {
          errorMessage =
            "This measurement no longer exists. It may have been deleted by another user."
        } else if (result.message.includes("dependency")) {
          errorMessage =
            "Cannot delete measurement due to dependencies. Please try again later."
        }

        toast({
          title: "Deletion Failed",
          description: errorMessage,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error deleting measurement:", error)

      // Provide user-friendly error messages
      let errorMessage =
        "An unexpected error occurred while deleting the measurement"
      if (error instanceof Error) {
        if (
          error.message.includes("network") ||
          error.message.includes("fetch")
        ) {
          errorMessage =
            "Network error. Please check your connection and try again."
        } else if (error.message.includes("timeout")) {
          errorMessage = "The request timed out. Please try again."
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsDeleting(false)
    }
  }

  // Check if this is the only measurement
  const isOnlyMeasurement = totalMeasurements === 1

  // Check if this is a recent measurement (within last 24 hours)
  const isRecent =
    new Date().getTime() - new Date(measurement.createdAt).getTime() <
    24 * 60 * 60 * 1000

  // Check if this is an API-generated measurement
  const isAPIGenerated = measurement.isRecommended

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        {trigger || (
          <Button
            variant="ghost"
            size="sm"
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="size-4" />
          </Button>
        )}
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="size-5 text-red-500" />
            Delete Measurement
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete Experiment {experimentNumber}? This
            action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          {/* Measurement Details */}
          <div className="space-y-2 rounded-lg border p-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">Experiment {experimentNumber}</span>
              <Badge variant="outline">
                {isAPIGenerated ? "API Generated" : "Manual Entry"}
              </Badge>
            </div>
            <div className="text-muted-foreground text-sm">
              <div className="flex items-center gap-1">
                <Clock className="size-3" />
                Created{" "}
                {formatDistanceToNow(new Date(measurement.createdAt), {
                  addSuffix: true
                })}
              </div>
            </div>
            <div className="text-sm">
              <span className="font-medium">Target Value:</span>{" "}
              {measurement.targetValue}
            </div>
          </div>

          {/* Warnings */}
          <div className="space-y-2">
            {isOnlyMeasurement && (
              <Alert className="border-red-200 bg-red-50">
                <AlertTriangle className="size-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  <strong>Warning:</strong> This is the only measurement in your
                  optimization. Deleting it will leave your optimization without
                  any data.
                </AlertDescription>
              </Alert>
            )}

            {isAPIGenerated && (
              <Alert className="border-orange-200 bg-orange-50">
                <AlertTriangle className="size-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  <strong>Impact:</strong> This measurement was generated by the
                  optimization algorithm. Deleting it may affect the
                  optimization's learning process and future suggestions.
                </AlertDescription>
              </Alert>
            )}

            {isRecent && (
              <Alert className="border-blue-200 bg-blue-50">
                <AlertTriangle className="size-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  <strong>Recent Data:</strong> This measurement was created
                  recently. Consider if you might want to edit it instead of
                  deleting it.
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Additional Impact Information */}
          <div className="text-muted-foreground space-y-1 text-sm">
            <p>
              <strong>What happens when you delete this measurement:</strong>
            </p>
            <ul className="ml-2 list-inside list-disc space-y-1">
              <li>The measurement data will be permanently removed</li>
              <li>Experiment numbers may be reordered</li>
              <li>The optimization's best point may be recalculated</li>
              {isAPIGenerated && (
                <li>The algorithm's learning history will be affected</li>
              )}
            </ul>
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {isDeleting ? (
              <>
                <div className="mr-2 size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 size-4" />
                Delete Measurement
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
