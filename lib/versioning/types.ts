export interface ConfigurationVersion {
  id: string
  optimizationId: string
  version: number
  parentVersion?: number
  timestamp: Date
  author: {
    userId: string
    userName: string
    userEmail?: string
  }
  changeType: "create" | "update" | "rollback" | "merge"
  changeSummary: string
  changeDetails: ConfigurationChange[]
  configuration: any // The full configuration at this version
  metadata: VersionMetadata
  tags: string[]
  isActive: boolean
  isMerged: boolean
  conflictResolution?: ConflictResolution
}

export interface ConfigurationChange {
  id: string
  type: "parameter" | "target" | "constraint" | "acquisition" | "recommender"
  action: "add" | "remove" | "modify"
  field: string
  oldValue: any
  newValue: any
  impact: ChangeImpact
  validation: ChangeValidation
}

export interface ChangeImpact {
  measurementsAffected: number
  dataLossRisk: "none" | "low" | "medium" | "high"
  performanceImpact: "none" | "minimal" | "moderate" | "significant"
  compatibilityImpact: "none" | "minor" | "major" | "breaking"
  estimatedDuration: number
}

export interface ChangeValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
  recommendations: string[]
}

export interface VersionMetadata {
  source: "ui" | "api" | "import" | "migration"
  environment: "development" | "staging" | "production"
  clientVersion: string
  serverVersion: string
  sessionId: string
  requestId: string
  checksum: string
  size: number
  compressionUsed: boolean
}

export interface VersionConflict {
  id: string
  optimizationId: string
  baseVersion: number
  conflictingVersions: number[]
  conflictType: "concurrent_edit" | "merge_conflict" | "validation_conflict"
  conflictFields: ConflictField[]
  detectedAt: Date
  status: "pending" | "resolved" | "ignored"
  resolution?: ConflictResolution
}

export interface ConflictField {
  field: string
  baseValue: any
  conflictingValues: Array<{
    version: number
    value: any
    author: string
    timestamp: Date
  }>
  conflictReason: string
  suggestedResolution: "use_latest" | "use_base" | "merge" | "manual"
}

export interface ConflictResolution {
  id: string
  conflictId: string
  resolvedBy: {
    userId: string
    userName: string
  }
  resolvedAt: Date
  resolutionStrategy:
    | "accept_mine"
    | "accept_theirs"
    | "merge_changes"
    | "custom"
  resolutionDetails: Array<{
    field: string
    chosenValue: any
    chosenVersion: number
    reason: string
  }>
  notes: string
}

export interface VersionBranch {
  id: string
  optimizationId: string
  name: string
  description: string
  baseVersion: number
  headVersion: number
  versions: number[]
  createdBy: {
    userId: string
    userName: string
  }
  createdAt: Date
  lastModified: Date
  status: "active" | "merged" | "abandoned"
  mergeTarget?: string // Target branch for merging
}

export interface VersionDiff {
  fromVersion: number
  toVersion: number
  changes: ConfigurationChange[]
  summary: {
    totalChanges: number
    addedItems: number
    removedItems: number
    modifiedItems: number
    impactLevel: "low" | "medium" | "high"
  }
  compatibility: {
    isBackwardCompatible: boolean
    isForwardCompatible: boolean
    migrationRequired: boolean
    migrationSteps: string[]
  }
}

export interface VersionQuery {
  optimizationId?: string
  authorId?: string
  fromDate?: Date
  toDate?: Date
  changeType?: string[]
  tags?: string[]
  isActive?: boolean
  limit?: number
  offset?: number
  sortBy?: "timestamp" | "version" | "author"
  sortOrder?: "asc" | "desc"
}

export interface VersionStatistics {
  totalVersions: number
  activeVersions: number
  totalAuthors: <AUTHORS>
  averageChangesPerVersion: number
  mostActiveAuthor: string
  changeFrequency: {
    daily: number
    weekly: number
    monthly: number
  }
  changeTypes: Record<string, number>
  conflictRate: number
  resolutionTime: {
    average: number
    median: number
    max: number
  }
}

export interface VersioningConfig {
  maxVersionsToKeep: number
  autoCleanupEnabled: boolean
  cleanupThresholdDays: number
  conflictDetectionEnabled: boolean
  autoMergeEnabled: boolean
  autoMergeRules: AutoMergeRule[]
  notificationSettings: {
    onConflict: boolean
    onNewVersion: boolean
    onMerge: boolean
  }
}

export interface AutoMergeRule {
  id: string
  name: string
  description: string
  conditions: Array<{
    field: string
    operator: "equals" | "not_equals" | "contains" | "matches"
    value: any
  }>
  action: "accept_latest" | "accept_base" | "reject" | "custom"
  priority: number
  enabled: boolean
}

export interface VersionLock {
  id: string
  optimizationId: string
  version: number
  lockedBy: {
    userId: string
    userName: string
    sessionId: string
  }
  lockedAt: Date
  lockType: "read" | "write" | "exclusive"
  lockReason: string
  expiresAt: Date
  isActive: boolean
}

export interface VersionEvent {
  id: string
  optimizationId: string
  version: number
  eventType:
    | "created"
    | "updated"
    | "deleted"
    | "locked"
    | "unlocked"
    | "merged"
    | "conflict_detected"
    | "conflict_resolved"
  timestamp: Date
  actor: {
    userId: string
    userName: string
  }
  details: any
  metadata: {
    source: string
    userAgent?: string
    ipAddress?: string
  }
}
