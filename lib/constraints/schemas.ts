// lib/constraints/schemas.ts
// Zod schemas for constraint validation

import { z } from "zod"

// Base constraint condition schema
export const constraintConditionSchema = z.object({
  type: z.enum(["threshold", "subselection"]),
  threshold: z.number().optional(),
  operator: z.enum([">=", "<=", ">", "<", "=", "=="]).optional(),
  tolerance: z.number().optional(),
  selection: z.array(z.union([z.string(), z.number()])).optional()
})

// Base constraint schema
export const baseConstraintSchema = z.object({
  id: z.string().optional(),
  type: z.string(),
  parameters: z.array(z.string()),
  name: z.string().optional(),
  description: z.string().optional()
})

// Continuous Linear Constraint schema
export const continuousLinearConstraintSchema = baseConstraintSchema.extend({
  type: z.literal("ContinuousLinearConstraint"),
  operator: z.enum([">=", "<=", "=", ">", "<"]),
  coefficients: z.array(z.number()),
  rhs: z.number()
})

// Continuous Cardinality Constraint schema
export const continuousCardinalityConstraintSchema =
  baseConstraintSchema.extend({
    type: z.literal("ContinuousCardinalityConstraint"),
    min_cardinality: z.number().int().min(0).optional(),
    max_cardinality: z.number().int().min(0).optional(),
    relative_threshold: z.number().min(0).max(1).optional()
  })

// Discrete Cardinality Constraint schema
export const discreteCardinalityConstraintSchema = baseConstraintSchema.extend({
  type: z.literal("DiscreteCardinalityConstraint"),
  min_cardinality: z.number().int().min(0).optional(),
  max_cardinality: z.number().int().min(0).optional()
})

// Discrete Exclude Constraint schema
export const discreteExcludeConstraintSchema = baseConstraintSchema.extend({
  type: z.literal("DiscreteExcludeConstraint"),
  combiner: z.enum(["AND", "OR"]),
  conditions: z.array(constraintConditionSchema)
})

// Discrete Sum Constraint schema
export const discreteSumConstraintSchema = baseConstraintSchema.extend({
  type: z.literal("DiscreteSumConstraint"),
  condition: constraintConditionSchema
})

// Discrete Product Constraint schema
export const discreteProductConstraintSchema = baseConstraintSchema.extend({
  type: z.literal("DiscreteProductConstraint"),
  condition: constraintConditionSchema
})

// Discrete Dependencies Constraint schema
export const discreteDependenciesConstraintSchema = baseConstraintSchema.extend(
  {
    type: z.literal("DiscreteDependenciesConstraint"),
    affected_parameters: z.array(z.array(z.string()))
  }
)

// Discrete Linked Parameters Constraint schema
export const discreteLinkedParametersConstraintSchema =
  baseConstraintSchema.extend({
    type: z.literal("DiscreteLinkedParametersConstraint"),
    dependencies: z.record(z.any())
  })

// Discrete No Label Duplicates Constraint schema
export const discreteNoLabelDuplicatesConstraintSchema =
  baseConstraintSchema.extend({
    type: z.literal("DiscreteNoLabelDuplicatesConstraint")
  })

// Discrete Permutation Invariance Constraint schema
export const discretePermutationInvarianceConstraintSchema =
  baseConstraintSchema.extend({
    type: z.literal("DiscretePermutationInvarianceConstraint"),
    dependencies: z.record(z.any())
  })

// Discrete Custom Constraint schema
export const discreteCustomConstraintSchema = baseConstraintSchema.extend({
  type: z.literal("DiscreteCustomConstraint"),
  validator: z.string()
})

// Union schema for all constraints
export const constraintSchema = z.discriminatedUnion("type", [
  continuousLinearConstraintSchema,
  continuousCardinalityConstraintSchema,
  discreteCardinalityConstraintSchema,
  discreteExcludeConstraintSchema,
  discreteSumConstraintSchema,
  discreteProductConstraintSchema,
  discreteDependenciesConstraintSchema,
  discreteLinkedParametersConstraintSchema,
  discreteNoLabelDuplicatesConstraintSchema,
  discretePermutationInvarianceConstraintSchema,
  discreteCustomConstraintSchema
])

// Parameter schema
export const parameterSchema = z.object({
  name: z.string().min(1),
  type: z.enum([
    "NumericalDiscrete",
    "NumericalContinuous",
    "CategoricalParameter"
  ]),
  values: z.array(z.union([z.number(), z.string()])).optional(),
  bounds: z.tuple([z.number(), z.number()]).optional(),
  tolerance: z.number().optional(),
  encoding: z.enum(["OHE", "LE"]).optional()
})

// Constraint template schema
export const constraintTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  category: z.string(),
  constraint: z.record(z.any()), // Use a more flexible schema for templates
  parameterPlaceholders: z.array(z.string())
})

// Sampling options schema
export const samplingOptionsSchema = z.object({
  strategy: z.enum(["LHS", "random", "sobol"]),
  nSamples: z.number().int().min(1),
  seed: z.number().int().optional(),
  respectConstraints: z.boolean().optional().default(false),
  maxAttempts: z.number().int().min(1).optional().default(1000),
  tolerance: z.number().min(0).optional().default(1e-6)
})

// Constraint context schema
export const constraintContextSchema = z.object({
  parameters: z.array(parameterSchema),
  constraints: z.array(constraintSchema),
  validationEnabled: z.boolean().optional().default(true),
  samplingEnabled: z.boolean().optional().default(false)
})
