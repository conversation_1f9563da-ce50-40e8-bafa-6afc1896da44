"use client"

import { useState, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import {
  ChevronDown,
  ChevronRight,
  BookOpen,
  FileText,
  Lightbulb,
  HelpCircle
} from "lucide-react"
import { BRAND } from "@/lib/constants"

// Define the resource links with icons
const resourceLinks = [
  {
    href: "/tutorials",
    label: "Tutorials",
    description: `Learn how to use ${BRAND.NAME} effectively`,
    icon: <BookOpen className="text-primary size-4" />
  },
  // Case Studies - Hidden as requested
  // {
  //   href: "/case-studies",
  //   label: "Case Studies",
  //   description: `See how others are using ${BRAND.NAME}`,
  //   icon: <FileText className="text-primary size-4" />
  // },
  {
    href: "/features",
    label: "Features",
    description: `Explore all ${BRAND.NAME} capabilities`,
    icon: <Lightbulb className="text-primary size-4" />
  },
  {
    href: "/faq",
    label: "FAQ",
    description: `Frequently asked questions about ${BRAND.NAME}`,
    icon: <HelpCircle className="text-primary size-4" />
  }
]

export default function ResourcesDropdown() {
  const [isOpen, setIsOpen] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleMouseEnter = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current)
    setIsOpen(true)
  }

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(false)
    }, 300) // Small delay to prevent accidental closing
  }

  return (
    <motion.div
      className="relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <button
        className="text-muted-foreground hover:text-foreground flex items-center rounded-full px-3 py-1 transition"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        Resources{" "}
        <ChevronDown
          className="ml-1 size-4 transition-transform duration-200"
          style={{ transform: isOpen ? "rotate(180deg)" : "rotate(0deg)" }}
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute left-0 top-full z-50 mt-2 w-64 overflow-hidden rounded-lg border border-gray-200 bg-white/90 shadow-lg backdrop-blur-lg"
          >
            <div className="p-2">
              {resourceLinks.map((link, index) => (
                <Link
                  key={link.href}
                  href={link.href}
                  onClick={() => setIsOpen(false)}
                  className="group flex items-start gap-3 rounded-md p-2 transition-colors hover:bg-gray-100"
                >
                  <div className="mt-0.5 shrink-0">{link.icon}</div>
                  <div>
                    <div className="flex items-center font-medium text-gray-900">
                      {link.label}
                      <ChevronRight className="ml-1 size-3 opacity-0 transition-all group-hover:translate-x-1 group-hover:opacity-100" />
                    </div>
                    <div className="text-xs text-gray-500">
                      {link.description}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
