# Images Directory

This directory contains images used throughout the application.

## Beta Feedback Note Image

To add a custom image for the Beta Feedback Note in the sidebar:

1. Add your AI-generated image to this directory (recommended size: 200x200 pixels)
2. Name it something like `beta-feedback.png` or `ai-assistant.png`
3. Update the Image component in `components/dashboard/dashboard-sidebar.tsx` to use your image:

```tsx
<Image
  src="/images/your-image-name.png"  // Replace with your image filename
  alt="Beta Product"
  fill
  className="object-cover"
/>
```

## Image Guidelines

- Keep images optimized for web (use PNG or WebP format)
- Maintain reasonable file sizes (under 100KB if possible)
- Use appropriate dimensions for their intended use
- Include alt text for accessibility
