#!/usr/bin/env node

// Debug script to test NaN/infinite detection logic
// Usage: node scripts/debug-nan-detection.js

console.log('🔬 DEBUGGING NaN/INFINITE DETECTION LOGIC');
console.log('==========================================');

// Test the exact values from the backend response
const testValues = [
  1.7618285302889447e-17,
  2.218205477698236e-14,
  4.068900774997063e-16,
  8.697120898239025e-16,
  1.2907589493765803e-13,
  1.2152475206694824e-13,
  1.1007503207538516e-9,
  0,
  100,
  NaN,
  Infinity,
  -Infinity
];

console.log('\n📊 TESTING VALUES:');
testValues.forEach((value, index) => {
  console.log(`\n${index + 1}. Value: ${value}`);
  console.log(`   Type: ${typeof value}`);
  console.log(`   Is NaN: ${Number.isNaN(value)}`);
  console.log(`   Is Finite: ${Number.isFinite(value)}`);
  console.log(`   Is Infinite: ${!Number.isFinite(value) && !Number.isNaN(value)}`);
  console.log(`   Scientific notation: ${value.toExponential()}`);
  console.log(`   Absolute value: ${Math.abs(value)}`);
  console.log(`   Very small (< 1e-15): ${Math.abs(value) < 1e-15}`);
  
  // Test pandas-like operations
  const isNaN_pandas = Number.isNaN(value);
  const isInf_numpy = !Number.isFinite(value) && !Number.isNaN(value);
  const problematic = isNaN_pandas || isInf_numpy;
  
  console.log(`   Would be flagged as problematic: ${problematic}`);
  
  if (problematic) {
    console.log(`   ❌ WOULD BE FILTERED as NaN/infinite`);
  } else {
    console.log(`   ✅ WOULD NOT BE FILTERED for NaN/infinite`);
  }
});

// Test specific problematic values from the backend response
console.log('\n🎯 TESTING SPECIFIC PROBLEMATIC VALUES FROM BACKEND:');

const problematicValues = [
  { name: 'x2', value: 1.7618285302889447e-17 },
  { name: 'x2', value: 0 },
  { name: 'x2', value: 2.218205477698236e-14 },
  { name: 'x3', value: 4.068900774997063e-16 },
  { name: 'x2', value: 8.697120898239025e-16 },
  { name: 'x1', value: 1.2907589493765803e-13 },
  { name: 'x3', value: 6.371205646393818e-15 },
  { name: 'x2', value: 1.2152475206694824e-13 },
  { name: 'x2', value: 1.1007503207538516e-9 }
];

problematicValues.forEach((item, index) => {
  const { name, value } = item;
  console.log(`\n${index + 1}. ${name} = ${value}`);
  
  // Exact logic from the backend
  const isNaN_check = Number.isNaN(value);
  const isInf_check = !Number.isFinite(value) && !Number.isNaN(value);
  const shouldBeFiltered = isNaN_check || isInf_check;
  
  console.log(`   Number.isNaN(): ${isNaN_check}`);
  console.log(`   !Number.isFinite() && !Number.isNaN(): ${isInf_check}`);
  console.log(`   Should be filtered: ${shouldBeFiltered}`);
  
  if (shouldBeFiltered) {
    console.log(`   ❌ BUG: Valid number incorrectly flagged as NaN/infinite!`);
  } else {
    console.log(`   ✅ CORRECT: Valid number not flagged`);
  }
});

console.log('\n🔍 CONCLUSION:');
console.log('If any valid numbers are being flagged as NaN/infinite,');
console.log('the issue is NOT in the JavaScript detection logic.');
console.log('The problem must be in the backend Python/pandas logic.');
