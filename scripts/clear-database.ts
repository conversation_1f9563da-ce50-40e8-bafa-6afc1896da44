// scripts/clear-database.ts
// <PERSON>ript to clear all data from the database

// Load environment variables
import * as dotenv from 'dotenv'
dotenv.config()

import { db } from "@/db/db"
import {
  profilesTable,
  optimizationsTable,
  measurementsTable,
  suggestionsTable,
  samplesTable,
  optimizationStatusHistoryTable
} from "@/db/schema"

async function clearDatabase() {
  console.log("Starting database cleanup...")

  try {
    // Delete data from tables in reverse order of dependencies
    console.log("Deleting samples...")
    await db.delete(samplesTable)

    console.log("Deleting measurements...")
    await db.delete(measurementsTable)

    console.log("Deleting suggestions...")
    await db.delete(suggestionsTable)

    console.log("Deleting optimization status history...")
    await db.delete(optimizationStatusHistoryTable)

    console.log("Deleting optimizations...")
    await db.delete(optimizationsTable)

    console.log("Deleting profiles...")
    await db.delete(profilesTable)

    console.log("Database cleanup completed successfully!")
  } catch (error) {
    console.error("Error during database cleanup:", error)
  } finally {
    process.exit(0)
  }
}

// Run the cleanup
clearDatabase()
