#!/usr/bin/env node

/**
 * Test that configuration changes persist in the backend campaign
 * and that Bayesian optimization works with the new configuration
 */

async function testConfigurationPersistence() {
  console.log('🧪 TESTING CONFIGURATION PERSISTENCE IN BACKEND CAMPAIGN');
  console.log('======================================================');
  
  // Use the Multi-Target Desirability optimization we created earlier
  const optimizerId = "test_multi_target_config_1757684763526";
  
  try {
    console.log(`\n📋 STEP 1: Get initial configuration from backend`);
    console.log('='.repeat(50));
    
    // Get initial measurements and configuration
    const initialMeasurementsResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`);
    
    if (!initialMeasurementsResponse.ok) {
      console.log(`❌ Cannot access optimization: ${initialMeasurementsResponse.status} ${initialMeasurementsResponse.statusText}`);
      return;
    }
    
    const initialData = await initialMeasurementsResponse.json();
    console.log(`✅ Initial measurements: ${initialData.measurements?.length || 0}`);
    
    if (initialData.target_info) {
      console.log('📊 Initial target configuration:');
      initialData.target_info.forEach((target, index) => {
        console.log(`   ${index + 1}. ${target.name}: ${target.mode}`);
      });
    }
    
    console.log(`\n📋 STEP 2: Update target bounds configuration`);
    console.log('='.repeat(50));
    
    // Update target bounds through the frontend API
    const updateResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target_bounds: {
          "Target 1": [0, 200],  // Change from [0, 150] to [0, 200]
          "Target 2": [0, 100]   // Change from [0, 75] to [0, 100]
        },
        preview_only: false
      })
    });
    
    console.log(`📥 Update response status: ${updateResponse.status} ${updateResponse.statusText}`);
    
    if (updateResponse.ok) {
      const updateResult = await updateResponse.json();
      console.log(`✅ Configuration update: ${updateResult.status}`);
      console.log(`📊 Updated config provided: ${!!updateResult.updated_config}`);
      
      if (updateResult.updated_targets) {
        console.log('📊 Updated targets from response:');
        if (Array.isArray(updateResult.updated_targets)) {
          updateResult.updated_targets.forEach((target, index) => {
            console.log(`   ${index + 1}. ${target.name}: [${target.bounds?.join(', ') || 'no bounds'}]`);
          });
        } else {
          console.log(`   Single target: ${updateResult.updated_targets.name} [${updateResult.updated_targets.bounds?.join(', ') || 'no bounds'}]`);
        }
      }
    } else {
      const errorText = await updateResponse.text();
      console.log(`❌ Configuration update failed: ${errorText}`);
      return;
    }
    
    console.log(`\n📋 STEP 3: Verify configuration persisted in backend`);
    console.log('='.repeat(50));
    
    // Wait a moment for the backend to process
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Get measurements again to see if configuration changed
    const updatedMeasurementsResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`);
    
    if (updatedMeasurementsResponse.ok) {
      const updatedData = await updatedMeasurementsResponse.json();
      console.log(`✅ Post-update measurements: ${updatedData.measurements?.length || 0}`);
      
      if (updatedData.target_info) {
        console.log('📊 Post-update target configuration:');
        updatedData.target_info.forEach((target, index) => {
          console.log(`   ${index + 1}. ${target.name}: ${target.mode}`);
        });
      }
    } else {
      console.log(`❌ Cannot verify updated configuration: ${updatedMeasurementsResponse.status}`);
    }
    
    console.log(`\n📋 STEP 4: Test Bayesian optimization with new configuration`);
    console.log('='.repeat(50));
    
    // Generate suggestions to test if Bayesian optimization works with new config
    const suggestResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/suggest?batch_size=2`);
    
    console.log(`📥 Suggestion response status: ${suggestResponse.status} ${suggestResponse.statusText}`);
    
    if (suggestResponse.ok) {
      const suggestResult = await suggestResponse.json();
      console.log(`✅ Bayesian optimization working: ${suggestResult.status}`);
      console.log(`📊 Generated suggestions: ${suggestResult.suggestions?.length || 0}`);
      
      if (suggestResult.suggestions && suggestResult.suggestions.length > 0) {
        console.log('📊 Sample suggestion:');
        const firstSuggestion = suggestResult.suggestions[0];
        Object.entries(firstSuggestion).forEach(([param, value]) => {
          console.log(`   ${param}: ${value}`);
        });
      }
    } else {
      const errorText = await suggestResponse.text();
      console.log(`❌ Bayesian optimization failed: ${errorText}`);
    }
    
    console.log(`\n📋 STEP 5: Add a measurement with new configuration`);
    console.log('='.repeat(50));
    
    // Add a measurement to test if the new configuration accepts it
    const measurementResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        parameters: { x1: 50, x2: 60 },
        target_values: [
          {"name": "Target 1", "value": 180},  // Within new bounds [0, 200]
          {"name": "Target 2", "value": 90}    // Within new bounds [0, 100]
        ]
      })
    });
    
    console.log(`📥 Measurement response status: ${measurementResponse.status} ${measurementResponse.statusText}`);
    
    if (measurementResponse.ok) {
      const measurementResult = await measurementResponse.json();
      console.log(`✅ Measurement added: ${measurementResult.status}`);
    } else {
      const errorText = await measurementResponse.text();
      console.log(`❌ Measurement failed: ${errorText}`);
    }
    
    console.log(`\n📋 STEP 6: Generate suggestions after new measurement`);
    console.log('='.repeat(50));
    
    // Generate suggestions again to see if the new measurement is incorporated
    const finalSuggestResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/suggest?batch_size=1`);
    
    console.log(`📥 Final suggestion response status: ${finalSuggestResponse.status} ${finalSuggestResponse.statusText}`);
    
    if (finalSuggestResponse.ok) {
      const finalSuggestResult = await finalSuggestResponse.json();
      console.log(`✅ Final Bayesian optimization: ${finalSuggestResult.status}`);
      console.log(`📊 Final suggestions: ${finalSuggestResult.suggestions?.length || 0}`);
      
      if (finalSuggestResult.suggestions && finalSuggestResult.suggestions.length > 0) {
        console.log('📊 Final suggestion:');
        const finalSuggestion = finalSuggestResult.suggestions[0];
        Object.entries(finalSuggestion).forEach(([param, value]) => {
          console.log(`   ${param}: ${value}`);
        });
      }
    } else {
      const errorText = await finalSuggestResponse.text();
      console.log(`❌ Final Bayesian optimization failed: ${errorText}`);
    }
    
    console.log(`\n🎉 CONFIGURATION PERSISTENCE TEST COMPLETED`);
    console.log('='.repeat(50));
    console.log('✅ Configuration changes should persist in backend campaign');
    console.log('✅ Bayesian optimization should work with updated configuration');
    console.log('✅ New measurements should be accepted within updated bounds');
    console.log('✅ Suggestions should incorporate new measurements');
    
  } catch (error) {
    console.error('❌ Error during configuration persistence test:', error.message);
  }
}

// Run the test
testConfigurationPersistence().catch(console.error);
