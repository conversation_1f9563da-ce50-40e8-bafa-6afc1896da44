export enum UpdateType {
  // Parameter updates
  PARAMETER_BOUNDS = "parameter_bounds",
  PARAMETER_VALUES = "parameter_values",
  PARAMETER_ENCODING = "parameter_encoding",

  // Target updates
  TARGET_BOUNDS = "target_bounds",
  TARGET_WEIGHT = "target_weight",
  TARGET_MODE = "target_mode",

  // Constraint updates
  CONSTRAINT_ADD = "constraint_add",
  CONSTRAINT_REMOVE = "constraint_remove",
  CONSTRAINT_MODIFY = "constraint_modify",

  // Acquisition function updates
  ACQUISITION_FUNCTION = "acquisition_function",
  ACQUISITION_PARAMS = "acquisition_params",

  // Recommender updates
  RECOMMENDER_CONFIG = "recommender_config",

  // Complex updates requiring recreation
  PARAMETER_TYPE_CHANGE = "parameter_type_change",
  OBJECTIVE_TYPE_CHANGE = "objective_type_change",
  SEARCHSPACE_STRUCTURE = "searchspace_structure"
}

export enum UpdateComplexity {
  SIMPLE = "simple", // Can be applied without model retraining
  MODERATE = "moderate", // Requires model retraining but not recreation
  COMPLEX = "complex" // Requires full campaign recreation
}

export interface UpdateOperation {
  id: string
  type: UpdateType
  complexity: UpdateComplexity
  field: string
  oldValue: any
  newValue: any
  requiresValidation: boolean
  requiresModelRetrain: boolean
  requiresCampaignRecreation: boolean
  estimatedDuration: number // in milliseconds
  dependencies: string[] // IDs of other operations this depends on
}

export interface UpdateBatch {
  id: string
  operations: UpdateOperation[]
  totalComplexity: UpdateComplexity
  estimatedDuration: number
  requiresModelRetrain: boolean
  requiresCampaignRecreation: boolean
  canBeAppliedIncrementally: boolean
}

export interface IncrementalUpdateStrategy {
  // Simple updates that can be applied directly
  simpleUpdates: UpdateOperation[]

  // Updates that require model retraining
  moderateUpdates: UpdateOperation[]

  // Updates that require full recreation
  complexUpdates: UpdateOperation[]

  // Execution plan
  executionPlan: {
    phase: "simple" | "moderate" | "complex"
    operations: UpdateOperation[]
    estimatedDuration: number
  }[]

  // Optimization recommendations
  recommendations: {
    canOptimize: boolean
    optimizationStrategy: string
    potentialTimeSaving: number
    risks: string[]
  }
}

export interface UpdateContext {
  optimizationId: string
  currentConfiguration: any
  targetConfiguration: any
  measurementCount: number
  modelComplexity: "low" | "medium" | "high"
  lastUpdateTimestamp: Date
  userPreferences: {
    prioritizeSpeed: boolean
    allowDataLoss: boolean
    maxAcceptableDelay: number
  }
}

export interface UpdateResult {
  success: boolean
  operationsApplied: UpdateOperation[]
  operationsFailed: UpdateOperation[]
  timeTaken: number
  measurementsAffected: number
  modelRetrained: boolean
  campaignRecreated: boolean
  warnings: string[]
  errors: string[]
  nextRecommendedActions: string[]
}

// Configuration change detection
export interface ConfigurationDiff {
  parameterChanges: {
    added: string[]
    removed: string[]
    modified: Array<{
      name: string
      field: string
      oldValue: any
      newValue: any
      changeType: UpdateType
    }>
  }

  targetChanges: {
    added: string[]
    removed: string[]
    modified: Array<{
      name: string
      field: string
      oldValue: any
      newValue: any
      changeType: UpdateType
    }>
  }

  constraintChanges: {
    added: any[]
    removed: any[]
    modified: Array<{
      id: string
      field: string
      oldValue: any
      newValue: any
      changeType: UpdateType
    }>
  }

  acquisitionChanges: {
    functionChanged: boolean
    parametersChanged: boolean
    oldConfig: any
    newConfig: any
  }

  recommenderChanges: {
    configChanged: boolean
    oldConfig: any
    newConfig: any
  }
}

// Update validation
export interface UpdateValidation {
  isValid: boolean
  errors: Array<{
    operation: UpdateOperation
    errorCode: string
    message: string
    severity: "error" | "warning"
  }>
  warnings: Array<{
    operation: UpdateOperation
    warningCode: string
    message: string
    impact: string
  }>
  recommendations: Array<{
    operation: UpdateOperation
    recommendation: string
    benefit: string
  }>
}

// Performance metrics
export interface UpdatePerformanceMetrics {
  operationId: string
  startTime: Date
  endTime: Date
  duration: number
  memoryUsage: number
  cpuUsage: number
  measurementsProcessed: number
  success: boolean
  errorMessage?: string
}

export interface UpdateOptimizationHints {
  // Batching hints
  canBatchWithOthers: boolean
  optimalBatchSize: number
  batchingStrategy: "sequential" | "parallel" | "mixed"

  // Caching hints
  canUseCachedResults: boolean
  cacheKey: string
  cacheValidityDuration: number

  // Resource hints
  estimatedMemoryUsage: number
  estimatedCpuUsage: number
  recommendedExecutionTime: "immediate" | "background" | "scheduled"

  // User experience hints
  showProgressIndicator: boolean
  allowUserCancellation: boolean
  provideLiveUpdates: boolean
}
