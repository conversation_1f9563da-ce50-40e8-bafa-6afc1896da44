/*
Configures Drizzle for the app.
*/

import { config } from "dotenv"
import { defineConfig } from "drizzle-kit"

// Load environment variables from .env.local file
config({ path: ".env.local" })



// Modify DATABASE_URL to disable SSL verification if needed
let dbUrl = process.env.DATABASE_URL!;
if (dbUrl && dbUrl.includes('sslmode=require')) {
  dbUrl = dbUrl.replace('sslmode=require', 'sslmode=prefer');
}

// Define the Drizzle configuration
const drizzleConfig = defineConfig({
  schema: "./db/schema/index.ts",
  out: "./db/migrations",
  dialect: "postgresql",
  dbCredentials: { url: dbUrl }
})



export default drizzleConfig
