/*
This server layout provides the dashboard structure with collapsible sidebar navigation.
*/

"use server"

import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar"
import { FloatingFeedbackButton } from "@/components/feedback/floating-feedback-button"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"
import { ensureUserTrialAction } from "@/actions/ensure-trial-action"
import { checkSurveyCompletionAction } from "@/actions/survey-actions"
import { checkAcademicSurveyCompletionAction } from "@/actions/academic-survey-actions"
import { checkAcademicVerificationStatusAction } from "@/actions/academic-verification-actions"

export default async function DashboardLayout({
  children
}: {
  children: React.ReactNode
}) {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  // If user is not authenticated, redirect to login
  if (!userId) {
    redirect("/login?redirect_url=/dashboard/optimizations")
  }

  // Check if the user has completed the appropriate survey and has a trial initialized
  try {
    // First, check if the user is an academic user
    const verificationStatus = await checkAcademicVerificationStatusAction()

    // Check if the user is an academic user with approved verification
    if (
      verificationStatus.isSuccess &&
      (verificationStatus.status === "approved" ||
        verificationStatus.status === "domain_verified")
    ) {
      // If verification is still in progress (domain verified but email not verified),
      // redirect to the academic signup page
      if (verificationStatus.status === "domain_verified") {
        redirect("/academic-signup")
      }

      // This is a fully verified academic user, check if they've completed the academic survey
      const academicSurveyStatus = await checkAcademicSurveyCompletionAction()

      // If the academic user hasn't completed the academic survey, redirect to the academic survey
      // For academic users, survey completion is mandatory
      if (
        !academicSurveyStatus.isSuccess ||
        !academicSurveyStatus.hasCompleted
      ) {
        redirect("/academic-survey")
      }
    } else if (
      verificationStatus.isSuccess &&
      verificationStatus.status === "pending"
    ) {
      // If verification is pending, redirect to the academic signup page
      redirect("/academic-signup")
    } else {
      // This is a regular user, check if they've completed the regular survey
      const surveyStatus = await checkSurveyCompletionAction()

      // If the regular user hasn't completed the survey, redirect to the welcome page
      if (!surveyStatus.hasCompleted) {
        // Redirect to welcome page with from=dashboard parameter
        redirect("/welcome?from=dashboard")
      }
    }

    // If we get here, the user has completed the appropriate survey
    // Now ensure they have a trial initialized
    await ensureUserTrialAction()
  } catch (error) {
    if (error instanceof Error && error.message === "NEXT_REDIRECT") {
      // This is a redirect error, just throw it to let Next.js handle it
      throw error
    }

    console.error("Error in dashboard layout:", error)
    // Continue without survey check or trial initialization
    // The user will still be able to access the dashboard
  }

  return (
    <div className="flex h-screen w-full overflow-hidden">
      {/* Use a key to ensure the sidebar is completely re-rendered on the client */}
      <DashboardSidebar key="sidebar" />
      <main className="flex-1 overflow-auto p-6">
        {children}
        {/* Floating feedback button for mobile users */}
        <FloatingFeedbackButton className="md:hidden" />
      </main>
    </div>
  )
}
