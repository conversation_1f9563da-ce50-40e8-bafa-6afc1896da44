"use client"

import { useState } from "react"
import { ChevronDown, ChevronRight, Info } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface CollapsibleInfoProps {
  title: string
  children: React.ReactNode
  defaultExpanded?: boolean
  className?: string
  variant?: "default" | "compact"
  icon?: React.ReactNode
}

export function CollapsibleInfo({
  title,
  children,
  defaultExpanded = false,
  className,
  variant = "default",
  icon
}: CollapsibleInfoProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  if (variant === "compact") {
    return (
      <div className={cn("border-l-2 border-blue-200 dark:border-blue-800", className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleExpanded}
          className="w-full justify-start px-3 py-2 text-left font-normal hover:bg-blue-100 dark:hover:bg-blue-900/30"
        >
          {isExpanded ? (
            <ChevronDown className="mr-2 size-4 text-blue-600 dark:text-blue-400" />
          ) : (
            <ChevronRight className="mr-2 size-4 text-blue-600 dark:text-blue-400" />
          )}
          {icon !== undefined && (icon || <Info className="mr-2 size-4 text-blue-600 dark:text-blue-400" />)}
          <span className="text-sm font-medium text-black dark:text-white">
            {title}
          </span>
        </Button>
        {isExpanded && (
          <div className="px-3 pb-3">
            {children}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={cn("rounded-lg border border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20", className)}>
      <Button
        variant="ghost"
        onClick={toggleExpanded}
        className="w-full justify-start rounded-lg px-4 py-3 text-left font-normal hover:bg-blue-100 dark:hover:bg-blue-900/30"
      >
        {isExpanded ? (
          <ChevronDown className="mr-2 size-4 text-blue-600 dark:text-blue-400" />
        ) : (
          <ChevronRight className="mr-2 size-4 text-blue-600 dark:text-blue-400" />
        )}
        {icon !== undefined && (icon || <Info className="mr-2 size-4 text-blue-600 dark:text-blue-400" />)}
        <span className="font-medium text-black dark:text-white">
          {title}
        </span>
      </Button>
      {isExpanded && (
        <div className="px-4 pb-4">
          {children}
        </div>
      )}
    </div>
  )
}
