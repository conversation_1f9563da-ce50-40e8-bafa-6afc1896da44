"use server"

import { db } from "@/db/db"
import {
  measurementAuditTable,
  InsertMeasurementAudit,
  SelectMeasurementAudit,
  MeasurementAuditData,
  AuditContext
} from "@/db/schema/measurement-audit-schema"
import { ActionState } from "@/types"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { eq, desc } from "drizzle-orm"
import { headers } from "next/headers"
import { getChangedFields } from "@/lib/measurement-audit-utils"

/**
 * Creates an audit log entry for measurement operations
 */
export async function createMeasurementAuditAction(
  measurementId: string,
  action: "create" | "update" | "delete",
  oldData: MeasurementAuditData | null,
  newData: MeasurementAuditData | null,
  context?: Partial<AuditContext>
): Promise<ActionState<SelectMeasurementAudit>> {
  try {
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 CREATE MEASUREMENT AUDIT - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "User authentication required for audit logging"
      }
    }

    // Get request headers for additional context
    const headersList = await headers()
    const userAgent = headersList.get("user-agent") || undefined
    const forwardedFor = headersList.get("x-forwarded-for")
    const realIp = headersList.get("x-real-ip")
    const ipAddress = forwardedFor?.split(",")[0] || realIp || undefined

    // Calculate changed fields for updates
    let changedFields: string[] = []
    if (action === "update" && oldData && newData) {
      changedFields = getChangedFields(oldData, newData)
    }

    // Create audit entry
    const auditEntry: InsertMeasurementAudit = {
      measurementId,
      action,
      userId,
      oldData: oldData ? JSON.parse(JSON.stringify(oldData)) : null,
      newData: newData ? JSON.parse(JSON.stringify(newData)) : null,
      changedFields: changedFields.length > 0 ? changedFields : null,
      reason: context?.reason || null,
      userAgent: context?.userAgent || userAgent,
      ipAddress: context?.ipAddress || ipAddress
    }

    const [newAuditEntry] = await db.insert(measurementAuditTable)
      .values(auditEntry)
      .returning()

    return {
      isSuccess: true,
      message: "Audit entry created successfully",
      data: newAuditEntry
    }
  } catch (error) {
    console.error("Error creating measurement audit entry:", error)
    return {
      isSuccess: false,
      message: `Failed to create audit entry: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Gets audit history for a measurement
 */
export async function getMeasurementAuditHistoryAction(
  measurementId: string
): Promise<ActionState<SelectMeasurementAudit[]>> {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to view audit history"
      }
    }

    const auditHistory = await db.select()
      .from(measurementAuditTable)
      .where(eq(measurementAuditTable.measurementId, measurementId))
      .orderBy(desc(measurementAuditTable.createdAt))

    return {
      isSuccess: true,
      message: "Audit history retrieved successfully",
      data: auditHistory
    }
  } catch (error) {
    console.error("Error getting measurement audit history:", error)
    return {
      isSuccess: false,
      message: `Failed to get audit history: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Gets audit history for all measurements in an optimization
 */
export async function getOptimizationAuditHistoryAction(
  optimizationId: string
): Promise<ActionState<SelectMeasurementAudit[]>> {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to view audit history"
      }
    }

    // This would require a join with measurements table to filter by optimization
    // For now, we'll return an empty array as this is a placeholder
    // In a real implementation, you would join with the measurements table

    return {
      isSuccess: true,
      message: "Optimization audit history retrieved successfully",
      data: []
    }
  } catch (error) {
    console.error("Error getting optimization audit history:", error)
    return {
      isSuccess: false,
      message: `Failed to get optimization audit history: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}




