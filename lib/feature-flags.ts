/**
 * Feature flags configuration
 * Controls which features are visible to users vs developers
 */

export interface FeatureFlags {
  historyTabContent: boolean
  runExperimentsDemo: boolean
  createOptimizationDemo: boolean
  platformOverviewVideo: boolean
  createFirstOptimizationVideo: boolean

  // Help Center - Getting Started section
  helpCenterPlatformOverview: boolean
  helpCenterFirstExperiment: boolean
  helpCenterDashboardGuide: boolean

  // Help Center - Documentation section
  helpCenterDataImportExport: boolean

  // Help Center - Video Tutorials section
  helpCenterOptimizationWalkthrough: boolean
  helpCenterAdvancedAnalytics: boolean
  helpCenterResultsInterpretation: boolean

  // Help Center - Search functionality
  helpCenterSearch: boolean

  // Help Center - Support section
  helpCenterContactSupport: boolean
  helpCenterScheduleDemo: boolean

  // Legacy flag for other help center placeholders
  helpCenterPlaceholders: boolean

  // Optimization Advanced Features
  optimizationConstraints: boolean
  optimizationSurrogateModel: boolean
}

// Environment-based feature flags
const getFeatureFlags = (): FeatureFlags => {
  const isDevelopment = process.env.NODE_ENV === "development"
  const enableDevFeatures =
    process.env.NEXT_PUBLIC_ENABLE_DEV_FEATURES === "true"

  return {
    // History tab content - temporarily disabled to demo the Coming Soon functionality
    // Tab is always visible, but content shows "Coming Soon" when disabled
    historyTabContent: false, // isDevelopment || enableDevFeatures,

    // Learning Resources - temporarily disabled to demo development badges
    runExperimentsDemo: false, // isDevelopment || enableDevFeatures,
    createOptimizationDemo: isDevelopment || enableDevFeatures,
    platformOverviewVideo: false, // isDevelopment || enableDevFeatures,
    createFirstOptimizationVideo: false, // isDevelopment || enableDevFeatures,

    // Help Center - Getting Started section (temporarily disabled to demo development badges)
    helpCenterPlatformOverview: false, // isDevelopment || enableDevFeatures,
    helpCenterFirstExperiment: false, // isDevelopment || enableDevFeatures,
    helpCenterDashboardGuide: false, // isDevelopment || enableDevFeatures,

    // Help Center - Documentation section (temporarily disabled to demo development badges)
    helpCenterDataImportExport: false, // isDevelopment || enableDevFeatures,

    // Help Center - Video Tutorials section (temporarily disabled to demo development badges)
    helpCenterOptimizationWalkthrough: false, // isDevelopment || enableDevFeatures,
    helpCenterAdvancedAnalytics: false, // isDevelopment || enableDevFeatures,
    helpCenterResultsInterpretation: false, // isDevelopment || enableDevFeatures,

    // Help Center - Search functionality (temporarily disabled to demo development badges)
    helpCenterSearch: false, // isDevelopment || enableDevFeatures,

    // Help Center - Support section (temporarily disabled to demo development badges)
    helpCenterContactSupport: isDevelopment || enableDevFeatures,
    helpCenterScheduleDemo: false, // isDevelopment || enableDevFeatures,

    // Legacy flag for other help center placeholders
    helpCenterPlaceholders: isDevelopment || enableDevFeatures,

    // Optimization Advanced Features - temporarily disabled to demo development badges
    optimizationConstraints: true, // isDevelopment || enableDevFeatures, //changed from false to true to see if the dropdown appears
    optimizationSurrogateModel: false // isDevelopment || enableDevFeatures
  }
}

export const featureFlags = getFeatureFlags()

// Helper function to check if a feature is enabled
export const isFeatureEnabled = (feature: keyof FeatureFlags): boolean => {
  return featureFlags[feature]
}

// Helper function to check if a feature is in development (visible but not functional)
export const isFeatureInDevelopment = (
  feature: keyof FeatureFlags
): boolean => {
  return !featureFlags[feature]
}
