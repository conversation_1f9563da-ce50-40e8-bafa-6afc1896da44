#!/usr/bin/env node

/**
 * Test script to verify the configuration retention fix
 * This script tests the "Pareto Discrete Constraints" optimization
 * to ensure constraints and parameter values are properly retained
 */

const OPTIMIZATION_ID = "user_30EFn0yPauqlMMorn5hfsn3of5X_Pareto_Discrete_Constraints_1758032953326";

async function testConfigurationFix() {
  console.log("🔧 TESTING CONFIGURATION FIX");
  console.log("=" .repeat(50));
  
  try {
    // Step 1: Get current configuration
    console.log("\n📋 STEP 1: Get current configuration");
    console.log("-".repeat(40));
    
    const currentResponse = await fetch(`http://localhost:3000/api/optimizations/${OPTIMIZATION_ID}`);
    if (!currentResponse.ok) {
      throw new Error(`Failed to get current config: ${currentResponse.statusText}`);
    }
    
    const currentData = await currentResponse.json();
    console.log("✅ Current configuration retrieved");
    console.log("📊 Current parameters:", currentData.config?.parameters?.map(p => ({ name: p.name, type: p.type, values: p.values })));
    console.log("🔒 Current constraints:", currentData.config?.constraints?.length || 0, "constraints");
    console.log("🎯 Current acquisition function:", currentData.config?.acquisition_config?.type);
    
    // Step 2: Test parameter update with constraint preservation
    console.log("\n📋 STEP 2: Test parameter update with constraint preservation");
    console.log("-".repeat(40));
    
    const updateResponse = await fetch(`http://localhost:3000/api/optimizations/${OPTIMIZATION_ID}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        updated_parameters: [
          { name: 'x1', type: 'NumericalDiscrete', values: [1, 5, 10, 15, 20, 25, 100] },
          { name: 'x2', type: 'NumericalDiscrete', values: [10, 20, 30] }, // Keep original values
          { name: 'x3', type: 'NumericalDiscrete', values: [10, 20, 30] }, // Keep original values  
          { name: 'x4', type: 'NumericalDiscrete', values: [200, 400] }    // Keep original values
        ],
        target_config: [
          { name: 'Target 1', mode: 'MAX', type: 'Numerical' },
          { name: 'Target 2', mode: 'MIN', type: 'Numerical' }
        ],
        constraints: [
          {
            id: "constraint_test_fix",
            name: "Test Constraint Fix",
            type: "DiscreteExcludeConstraint",
            combiner: "AND",
            conditions: [
              { type: "subselection", selection: ["30"] },
              { type: "subselection", selection: ["400"] }
            ],
            parameters: ["x3", "x4"],
            description: "Test constraint to verify fix"
          }
        ],
        acquisition_config: {
          type: 'qLogNoisyExpectedHypervolumeImprovement',
          ref_point: [-10.0, 110.0]
        },
        parameter_order: ['x1', 'x2', 'x3', 'x4'],
        preview_only: false
      })
    });
    
    console.log(`📥 Update response: ${updateResponse.status} ${updateResponse.statusText}`);
    
    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      throw new Error(`Update failed: ${errorText}`);
    }
    
    const updateData = await updateResponse.json();
    console.log("✅ Update successful");
    console.log("📊 Update result keys:", Object.keys(updateData));
    
    // Step 3: Verify configuration was properly saved
    console.log("\n📋 STEP 3: Verify configuration retention");
    console.log("-".repeat(40));
    
    // Wait a moment for database update
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const verifyResponse = await fetch(`http://localhost:3000/api/optimizations/${OPTIMIZATION_ID}`);
    if (!verifyResponse.ok) {
      throw new Error(`Failed to verify config: ${verifyResponse.statusText}`);
    }
    
    const verifyData = await verifyResponse.json();
    console.log("✅ Verification configuration retrieved");
    
    // Check parameter values
    const params = verifyData.config?.parameters || [];
    console.log("\n🔍 PARAMETER VALUES VERIFICATION:");
    params.forEach(param => {
      console.log(`  - ${param.name}: ${JSON.stringify(param.values)} (type: ${param.type})`);
    });
    
    // Check constraints
    const constraints = verifyData.config?.constraints || [];
    console.log(`\n🔍 CONSTRAINTS VERIFICATION: ${constraints.length} constraints found`);
    constraints.forEach((constraint, index) => {
      console.log(`  - Constraint ${index + 1}: ${constraint.name} (${constraint.type})`);
      console.log(`    Parameters: ${constraint.parameters?.join(', ')}`);
      console.log(`    Conditions: ${JSON.stringify(constraint.conditions)}`);
    });
    
    // Check acquisition function
    const acqFunc = verifyData.config?.acquisition_config?.type;
    console.log(`\n🔍 ACQUISITION FUNCTION VERIFICATION: ${acqFunc}`);
    
    // Step 4: Verify backend logs show correct values
    console.log("\n📋 STEP 4: Check backend consistency");
    console.log("-".repeat(40));
    console.log("🔍 Please check the Docker logs to verify:");
    console.log("  1. Parameter values are correct (x4 should be [200, 400])");
    console.log("  2. Constraints are included in the request");
    console.log("  3. Acquisition function is consistent");
    console.log("  4. No measurements are incorrectly filtered");
    
    console.log("\n🎉 CONFIGURATION FIX TEST COMPLETED");
    console.log("=" .repeat(50));
    
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    process.exit(1);
  }
}

// Run the test
testConfigurationFix().catch(console.error);
