/*
API Reference page with BayBE attribution
*/

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { ExternalLink, Code, Zap, Shield, Heart, ArrowLeft } from "lucide-react"

export default function APIReferencePage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/help">
            <ArrowLeft className="mr-2 size-4" />
            Back to Help Center
          </Link>
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold">API Reference</h1>
        <p className="text-muted-foreground">
          Complete reference for the INNOptimizer™ Bayesian Optimization API
        </p>
      </div>

      {/* License Attribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="size-5" />
            License & Attribution
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div>
              <h4 className="font-medium">BayBE License</h4>
              <p className="text-muted-foreground text-sm">
                BayBE is licensed under the Apache License 2.0
              </p>
              <p className="text-muted-foreground text-sm">
                Copyright © 2022-2025 Merck KGaA, Darmstadt, Germany and/or its
                affiliates
              </p>
            </div>

            <div>
              <h4 className="font-medium">Third-Party Components</h4>
              <p className="text-muted-foreground text-sm">
                This application uses various open-source libraries. See our
                complete third-party notices for full attribution.
              </p>
            </div>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <a
                href="https://emdgroup.github.io/baybe/stable/misc/license_link.html"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1"
              >
                <ExternalLink className="size-3" />
                Apache License 2.0
              </a>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/help/third-party-notices">
                Third-Party Notices
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Acknowledgments */}
      <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="size-5 text-green-600" />
            Acknowledgments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm">
            We extend our gratitude to the BayBE development team at Merck KGaA
            for creating and open-sourcing this powerful Bayesian optimization
            library. Their contribution enables advanced optimization
            capabilities in our platform.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
