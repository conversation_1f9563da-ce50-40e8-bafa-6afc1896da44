/*
Enhanced Dashboard Home Page with Modern UI/UX Design Patterns
- Progressive disclosure with expandable sections
- Micro-interactions and subtle animations
- Smart empty states with actionable guidance
- Enhanced visual hierarchy with better typography
- Modern card designs with proper spacing
- Contextual actions and quick access patterns
- Better data visualization approach
- Full-width QuickStartWizard
*/

"use server"

import { Zap, Target } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"
import {
  getOptimizationsAction,
  getOptimizationImprovementsAction,
  getDashboardStatsAction
} from "@/actions/db/optimizations-actions"
import { DashboardThemeWrapper } from "@/components/dashboard/dashboard-theme-wrapper"
import { statusLabels, statusColors } from "@/lib/status-utils"
import { formatDistanceToNow } from "date-fns"
import Link from "next/link"
import {
  FlaskConical,
  Activity,
  Plus,
  ArrowRight,
  BarChart3,
  Clock,
  Lightbulb,
  BookOpen,
  ChevronRight,
  Sparkles
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default async function DashboardHomePage() {
  await auth()
  const userId = await getActualUserId()

  if (!userId) {
    redirect("/dashboard")
  }

  // Get comprehensive dashboard statistics
  const dashboardStatsResult = await getDashboardStatsAction(userId)
  const dashboardStats = dashboardStatsResult.isSuccess
    ? dashboardStatsResult.data
    : {
        totalExperiments: 0,
        monthlyGrowth: 0,
        activeOptimizations: 0,
        completedOptimizations: 0,
        successRate: 0,
        recentActivity: 0
      }

  const optimizationsResult = await getOptimizationsAction(userId)
  const optimizations = optimizationsResult.isSuccess
    ? optimizationsResult.data
    : []
  const recentOptimizations = optimizations.slice(0, 5)

  // Get improvement data for optimizations
  const improvementsResult = await getOptimizationImprovementsAction(userId)
  const improvements = improvementsResult.isSuccess
    ? improvementsResult.data
    : {}

  return (
    <DashboardThemeWrapper>
      {/* Enhanced Header with Welcome Message and Better Typography */}
      <div className="flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="space-y-8">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="flex size-14 items-center justify-center rounded-2xl bg-white shadow-lg">
                  <FlaskConical className="size-7" style={{ color: '#2596be' }} />
                </div>
                <div className="border-3 absolute -bottom-1 -right-1 flex size-6 items-center justify-center rounded-full border-white bg-green-500">
                  <div className="size-2 animate-pulse rounded-full bg-white" />
                </div>
              </div>
              <div className="flex-1">
                <div className="flex flex-wrap items-center gap-4">
                  <h1 className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-4xl font-bold text-transparent">
                    Welcome back!
                  </h1>
                  <div className="flex items-center gap-2 rounded-full border border-gray-200/50 bg-white/60 px-3 py-2">
                    <Clock className="size-3 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">
                      Updated 2m ago
                    </span>
                  </div>
                </div>
                <p className="mt-1 text-lg font-medium text-gray-600">
                  Ready to optimize your next experiment?
                </p>
              </div>
            </div>
          </div>

          {/* Enhanced CTA with More Prominence */}
          <div className="flex flex-col gap-3 sm:flex-row">
            <Link href="/dashboard/optimizations/create">
              <Button
                size="lg"
                className="gap-2 px-8"
              >
                <Plus className="size-5" />
                New Optimization
                <ArrowRight className="size-4" />
              </Button>
            </Link>
          </div>
        </div>

        {/* Compact Stats Cards with Professional Design */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {/* Total Experiments Card */}
          <Card className="group relative overflow-hidden border border-white/60 bg-white/90 shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:bg-white/95">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/8 via-blue-500/12 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            <CardContent className="relative p-4">
              <div className="mb-3">
                <div className="flex size-10 items-center justify-center rounded-lg">
                  <FlaskConical className="size-5" style={{ color: '#2596be' }} />
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardStats.totalExperiments}
                </p>
                <p className="text-sm font-medium text-gray-600">
                  Total Experiments
                </p>
                <p className="text-xs text-gray-500">Across all projects</p>
              </div>
            </CardContent>
          </Card>

          {/* Active Optimizations Card */}
          <Card className="group relative overflow-hidden border border-white/60 bg-white/90 shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:bg-white/95">
            <div className="absolute inset-0 bg-gradient-to-br from-violet-500/8 via-violet-500/12 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            <CardContent className="relative p-4">
              <div className="mb-3">
                <div className="flex size-10 items-center justify-center rounded-lg">
                  <Activity className="size-5" style={{ color: '#2596be' }} />
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardStats.activeOptimizations}
                </p>
                <p className="text-sm font-medium text-gray-600">
                  Active Optimizations
                </p>
                <p className="text-xs text-gray-500">Currently running</p>
              </div>
            </CardContent>
          </Card>

          {/* Success Rate Card */}
          <Card className="group relative overflow-hidden border border-white/60 bg-white/90 shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:bg-white/95">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/8 via-green-500/12 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            <CardContent className="relative p-4">
              <div className="mb-3">
                <div className="flex size-10 items-center justify-center rounded-lg">
                  <Target className="size-5" style={{ color: '#2596be' }} />
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardStats.successRate}%
                </p>
                <p className="text-sm font-medium text-gray-600">
                  Success Rate
                </p>
                <p className="text-xs text-gray-500">Completed optimizations</p>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity Card */}
          <Card className="group relative overflow-hidden border border-white/60 bg-white/90 shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:bg-white/95">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-500/8 via-amber-500/12 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            <CardContent className="relative p-4">
              <div className="mb-3">
                <div className="flex size-10 items-center justify-center rounded-lg">
                  <Clock className="size-5" style={{ color: '#2596be' }} />
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardStats.recentActivity}
                </p>
                <p className="text-sm font-medium text-gray-600">
                  Recent Activity
                </p>
                <p className="text-xs text-gray-500">Last 7 days</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Optimizations - Compact Professional Design */}
        <Card className="border border-white/60 bg-white/90 shadow-lg backdrop-blur-sm">
          <CardHeader className="border-b border-gray-100/60 bg-gradient-to-r from-gray-50/60 to-blue-50/20 pb-3 pt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <div className="flex size-7 items-center justify-center rounded-lg bg-blue-100 text-blue-600">
                    <BarChart3 className="size-4" />
                  </div>
                  Recent Optimizations
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Latest experiments and progress
                </p>
              </div>
              <div className="flex items-center gap-3">
                {recentOptimizations.length > 0 && (
                  <>
                    <Link
                      href="/dashboard/optimizations"
                      className="flex items-center gap-1 rounded-lg px-3 py-1.5 text-sm font-medium text-blue-600 transition-colors hover:bg-blue-50 hover:text-blue-700"
                    >
                      View all
                      <ChevronRight className="size-4" />
                    </Link>
                  </>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            {recentOptimizations.length > 0 ? (
              <div className="divide-y divide-gray-100">
                {recentOptimizations.map((optimization) => {
                  const status =
                    optimization.status as keyof typeof statusLabels
                  const createdAt = new Date(optimization.createdAt)
                  const timeAgo = formatDistanceToNow(createdAt, {
                    addSuffix: true
                  })
                  const improvement = improvements[optimization.id] ?? 0

                  return (
                    <div
                      key={optimization.id}
                      className="group flex items-center gap-4 p-4 transition-all duration-200 hover:bg-gradient-to-r hover:from-gray-50/40 hover:to-blue-50/20"
                    >
                      <div className="flex size-10 items-center justify-center rounded-lg transition-shadow group-hover:shadow-sm">
                        <span className="text-sm font-bold" style={{ color: '#2596be' }}>
                          {optimization.name.charAt(0).toUpperCase()}
                        </span>
                      </div>

                      <div className="min-w-0 flex-1">
                        <Link
                          href={`/dashboard/optimizations/${optimization.id}/run`}
                          className="block transition-colors group-hover:text-blue-600"
                        >
                          <h4 className="mb-0.5 truncate text-base font-semibold text-gray-900">
                            {optimization.name}
                          </h4>
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <Clock className="size-3" />
                            <span>Created {timeAgo}</span>
                          </div>
                        </Link>
                      </div>

                      <div className="flex items-center gap-3">
                        <div className="text-right">
                          <div className={`text-sm font-bold ${
                            improvement > 0
                              ? 'text-green-600'
                              : improvement < 0
                                ? 'text-red-600'
                                : 'text-gray-500'
                          }`}>
                            {improvement > 0 ? '+' : ''}{(improvement || 0).toFixed(1)}%
                          </div>
                          <div className="text-xs text-gray-500">
                            {improvement === 0 ? 'no data' : improvement > 0 ? 'improvement' : 'decline'}
                          </div>
                        </div>
                        <Badge
                          variant="secondary"
                          className={`${statusColors[status]} border-0 px-2 py-0.5 text-xs font-medium text-white`}
                        >
                          {statusLabels[status]}
                        </Badge>
                        <ChevronRight className="size-4 text-gray-400 transition-colors group-hover:text-blue-500" />
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              /* Compact Empty State */
              <div className="px-6 py-12 text-center">
                <div className="relative mx-auto mb-6">
                  <div className="mx-auto flex size-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-100 via-blue-50 to-indigo-100">
                    <Lightbulb className="size-8 text-blue-600" />
                  </div>
                  <div className="absolute -right-1 -top-1 flex size-6 items-center justify-center rounded-full bg-white shadow-sm">
                    <Sparkles className="size-3" style={{ color: '#2596be' }} />
                  </div>
                </div>
                <h3 className="mb-2 text-xl font-bold text-gray-900">
                  Ready to start optimizing?
                </h3>
                <p className="mx-auto mb-6 max-w-md text-gray-600">
                  Create your first optimization experiment and start
                  discovering insights.
                </p>
                <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
                  <Link href="/dashboard/optimizations/create">
                    <Button className="gap-2 px-6">
                      <Plus className="size-4" />
                      Create Optimization
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    className="gap-2 border-gray-300 hover:border-gray-400"
                  >
                    <BookOpen className="size-4" />
                    View Tutorial
                  </Button>
                </div>

                {/* Compact Help Links */}
                <div className="mt-6 border-t border-gray-200 pt-4">
                  <p className="mb-3 text-sm text-gray-500">
                    Need help getting started?
                  </p>
                  <div className="flex flex-wrap justify-center gap-4">
                    <a
                      href="#"
                      className="text-sm font-medium text-blue-600 hover:text-blue-700"
                    >
                      Documentation
                    </a>
                    <span className="text-gray-300">•</span>
                    <a
                      href="#"
                      className="text-sm font-medium text-blue-600 hover:text-blue-700"
                    >
                      Tutorials
                    </a>
                    <span className="text-gray-300">•</span>
                    <a
                      href="#"
                      className="text-sm font-medium text-blue-600 hover:text-blue-700"
                    >
                      Support
                    </a>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Compact Quick Actions Section */}
        <Card className="border border-white/60 bg-white/90 shadow-lg backdrop-blur-sm">
          <CardHeader className="border-b border-gray-100/60 bg-gradient-to-r from-gray-50/60 to-green-50/20 pb-3 pt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <div className="flex size-7 items-center justify-center rounded-lg bg-green-100 text-green-600">
                    <Zap className="size-4" />
                  </div>
                  Quick Actions
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Common tasks and shortcuts
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
              {/* Create Optimization */}
              <Link href="/dashboard/optimizations/create">
                <div className="group flex cursor-pointer items-center gap-3 rounded-lg border border-gray-200 bg-white p-3 transition-all duration-200 hover:border-blue-300 hover:shadow-sm">
                  <div className="flex size-10 items-center justify-center rounded-lg">
                    <Plus className="size-5" style={{ color: '#2596be' }} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 group-hover:text-blue-600">
                      New Optimization
                    </h4>
                    <p className="text-sm text-gray-500">
                      Start a new experiment
                    </p>
                  </div>
                  <ChevronRight className="size-4 text-gray-400 transition-colors group-hover:text-blue-500" />
                </div>
              </Link>

              {/* View All Optimizations */}
              <Link href="/dashboard/optimizations">
                <div className="group flex cursor-pointer items-center gap-3 rounded-lg border border-gray-200 bg-white p-3 transition-all duration-200 hover:border-violet-300 hover:shadow-sm">
                  <div className="flex size-10 items-center justify-center rounded-lg">
                    <BarChart3 className="size-5" style={{ color: '#2596be' }} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 group-hover:text-violet-600">
                      All Optimizations
                    </h4>
                    <p className="text-sm text-gray-500">
                      Manage your projects
                    </p>
                  </div>
                  <ChevronRight className="size-4 text-gray-400 transition-colors group-hover:text-violet-500" />
                </div>
              </Link>

              {/* Learning Resources */}
              <Link href="/dashboard/learning">
                <div className="group flex cursor-pointer items-center gap-3 rounded-lg border border-gray-200 bg-white p-3 transition-all duration-200 hover:border-purple-300 hover:shadow-sm">
                  <div className="flex size-10 items-center justify-center rounded-lg">
                    <BookOpen className="size-5" style={{ color: '#2596be' }} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 group-hover:text-purple-600">
                      Learning Hub
                    </h4>
                    <p className="text-sm text-gray-500">
                      Tutorials & guides
                    </p>
                  </div>
                  <ChevronRight className="size-4 text-gray-400 transition-colors group-hover:text-purple-500" />
                </div>
              </Link>
            </div>
          </CardContent>
        </Card>
    </DashboardThemeWrapper>
  )
}
