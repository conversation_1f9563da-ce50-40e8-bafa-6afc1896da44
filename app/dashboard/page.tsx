/*
This server page serves as the main dashboard entry point with sign-in protection.
*/

"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"

export default async function DashboardPage() {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🏠 DASHBOARD PAGE - Full auth result:", {
    clerkUserId: authResult.userId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId,
    sessionId: authResult.sessionId,
    hasSessionClaims: !!authResult.sessionClaims,
    sessionClaimsKeys: authResult.sessionClaims
      ? Object.keys(authResult.sessionClaims)
      : null
  })

  if (authResult.sessionClaims) {
    console.log(
      "🏠 DASHBOARD PAGE - Session claims:",
      JSON.stringify(authResult.sessionClaims, null, 2)
    )
  }

  // If user is not authenticated, redirect directly to login with custom background
  if (!userId) {
    console.log("❌ DASHBOARD PAGE - No user ID, redirecting to login")
    console.log("❌ DASHBOARD PAGE - Session ID:", authResult.sessionId)
    redirect("/login?redirect_url=/dashboard/home")
  }

  console.log(
    "✅ DASHBOARD PAGE - User authenticated, redirecting to /dashboard/home"
  )
  // If user is authenticated, redirect to the actual dashboard content
  redirect("/dashboard/home")
}
