/*
This client component provides comprehensive profile management integrated with <PERSON>.
*/

"use client"

import { useState } from "react"
import { UserProfile } from "@clerk/nextjs"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { User, Shield, Settings, CreditCard } from "lucide-react"

export function ProfileManagement() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Profile Management</CardTitle>
          <CardDescription>
            Manage your account information, security settings, and preferences.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="w-full overflow-hidden">
            <UserProfile
              appearance={{
                elements: {
                  rootBox: "w-full max-w-none",
                  card: "shadow-none border-0 bg-transparent",
                  headerTitle: "hidden",
                  headerSubtitle: "hidden",
                  pageScrollBox: "px-6 py-4",
                  page: "bg-transparent",
                  formButtonPrimary: "bg-primary hover:bg-primary/90",
                  navbarButton: "text-foreground hover:bg-accent",
                  navbarButtonIcon: "text-foreground"
                },
                layout: {
                  showOptionalFields: true
                }
              }}
              routing="hash"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
