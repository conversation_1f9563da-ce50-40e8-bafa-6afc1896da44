import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockOptimizationData, mockFetch, mockApiResponses } from '../test-utils'

// Mock the complete parameter editing workflow component
const MockParameterCRUDWorkflow = () => {
  const [isEditMode, setIsEditMode] = React.useState(false)
  const [parameters, setParameters] = React.useState(mockOptimizationData.parameters)
  const [pendingChanges, setPendingChanges] = React.useState<any>({})
  const [isPreviewMode, setIsPreviewMode] = React.useState(false)

  const handleParameterUpdate = async (parameterName: string, updates: any) => {
    setPendingChanges(prev => ({
      ...prev,
      [parameterName]: updates
    }))
  }

  const handlePreviewChanges = async () => {
    setIsPreviewMode(true)
    
    const response = await fetch('/api/optimizations/test-optimizer/bounds', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        updated_parameters: Object.values(pendingChanges),
        preview_only: true
      })
    })
    
    const result = await response.json()
    return result
  }

  const handleApplyChanges = async () => {
    const response = await fetch('/api/optimizations/test-optimizer/bounds', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        updated_parameters: Object.values(pendingChanges),
        preview_only: false
      })
    })
    
    const result = await response.json()
    
    if (result.status === 'success') {
      setParameters(prev => prev.map(param => 
        pendingChanges[param.name] ? { ...param, ...pendingChanges[param.name] } : param
      ))
      setPendingChanges({})
      setIsEditMode(false)
      setIsPreviewMode(false)
    }
    
    return result
  }

  return (
    <div data-testid="parameter-crud-workflow">
      <div data-testid="edit-mode-section">
        <label>
          <input
            type="checkbox"
            checked={isEditMode}
            onChange={(e) => setIsEditMode(e.target.checked)}
            data-testid="edit-mode-toggle"
          />
          Edit Mode
        </label>
      </div>

      <div data-testid="parameters-section">
        {parameters.map((param, index) => (
          <div key={index} data-testid={`parameter-${param.name}`}>
            <span data-testid={`param-name-${param.name}`}>{param.name}</span>
            <span data-testid={`param-type-${param.name}`}>{param.type}</span>
            
            {param.type === 'NumericalContinuous' && param.bounds && (
              <span data-testid={`param-bounds-${param.name}`}>
                [{param.bounds[0]}, {param.bounds[1]}]
              </span>
            )}
            
            {param.type === 'NumericalDiscrete' && param.values && (
              <span data-testid={`param-values-${param.name}`}>
                {param.values.join(', ')}
              </span>
            )}
            
            {param.type === 'CategoricalParameter' && param.values && (
              <span data-testid={`param-categories-${param.name}`}>
                {param.values.join(', ')}
              </span>
            )}

            {isEditMode && (
              <div data-testid={`edit-controls-${param.name}`}>
                <button
                  data-testid={`edit-btn-${param.name}`}
                  onClick={() => {
                    // Simulate parameter editing
                    const updates = { ...param }
                    if (param.type === 'NumericalContinuous' && param.bounds) {
                      updates.bounds = [param.bounds[0] + 1, param.bounds[1] + 1]
                    }
                    handleParameterUpdate(param.name, updates)
                  }}
                >
                  Edit {param.name}
                </button>
              </div>
            )}
          </div>
        ))}
      </div>

      {isEditMode && Object.keys(pendingChanges).length > 0 && (
        <div data-testid="pending-changes-section">
          <p data-testid="pending-changes-count">
            {Object.keys(pendingChanges).length} pending changes
          </p>
          
          <button
            data-testid="preview-btn"
            onClick={handlePreviewChanges}
          >
            Preview Changes
          </button>
          
          <button
            data-testid="apply-btn"
            onClick={handleApplyChanges}
          >
            Apply Changes
          </button>
        </div>
      )}

      {isPreviewMode && (
        <div data-testid="preview-section">
          <p>Preview: Changes will affect measurements</p>
          <button
            data-testid="confirm-apply-btn"
            onClick={handleApplyChanges}
          >
            Confirm & Apply
          </button>
        </div>
      )}
    </div>
  )
}

describe('Parameter CRUD Workflow Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('completes full parameter update workflow', async () => {
    const user = userEvent.setup()
    mockFetch(mockApiResponses.updateBounds.success)

    render(<MockParameterCRUDWorkflow />)

    // Step 1: Enable edit mode
    const editToggle = screen.getByTestId('edit-mode-toggle')
    await user.click(editToggle)

    expect(editToggle).toBeChecked()

    // Step 2: Edit a parameter
    const editButton = screen.getByTestId('edit-btn-temperature')
    await user.click(editButton)

    // Step 3: Verify pending changes
    await waitFor(() => {
      expect(screen.getByTestId('pending-changes-count')).toHaveTextContent('1 pending changes')
    })

    // Step 4: Apply changes
    const applyButton = screen.getByTestId('apply-btn')
    await user.click(applyButton)

    // Step 5: Verify API call was made
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/optimizations/test-optimizer/bounds',
        expect.objectContaining({
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('preview_only":false')
        })
      )
    })

    // Step 6: Verify edit mode is disabled after successful update
    await waitFor(() => {
      expect(editToggle).not.toBeChecked()
    })
  })

  it('handles preview workflow correctly', async () => {
    const user = userEvent.setup()
    mockFetch(mockApiResponses.updateBounds.preview)

    render(<MockParameterCRUDWorkflow />)

    // Enable edit mode and make changes
    await user.click(screen.getByTestId('edit-mode-toggle'))
    await user.click(screen.getByTestId('edit-btn-pressure'))

    // Preview changes
    const previewButton = screen.getByTestId('preview-btn')
    await user.click(previewButton)

    // Verify preview API call
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/optimizations/test-optimizer/bounds',
        expect.objectContaining({
          body: expect.stringContaining('preview_only":true')
        })
      )
    })

    // Verify preview section appears
    await waitFor(() => {
      expect(screen.getByTestId('preview-section')).toBeInTheDocument()
    })
  })

  it('handles multiple parameter updates in batch', async () => {
    const user = userEvent.setup()
    mockFetch(mockApiResponses.updateBounds.success)

    render(<MockParameterCRUDWorkflow />)

    // Enable edit mode
    await user.click(screen.getByTestId('edit-mode-toggle'))

    // Edit multiple parameters
    await user.click(screen.getByTestId('edit-btn-temperature'))
    await user.click(screen.getByTestId('edit-btn-pressure'))

    // Verify multiple pending changes
    await waitFor(() => {
      expect(screen.getByTestId('pending-changes-count')).toHaveTextContent('2 pending changes')
    })

    // Apply all changes
    await user.click(screen.getByTestId('apply-btn'))

    // Verify batch update API call
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/optimizations/test-optimizer/bounds',
        expect.objectContaining({
          method: 'PUT'
        })
      )
    })
  })

  it('handles API errors gracefully', async () => {
    const user = userEvent.setup()
    mockFetch(mockApiResponses.updateBounds.error, false)

    render(<MockParameterCRUDWorkflow />)

    // Enable edit mode and make changes
    await user.click(screen.getByTestId('edit-mode-toggle'))
    await user.click(screen.getByTestId('edit-btn-temperature'))

    // Try to apply changes
    await user.click(screen.getByTestId('apply-btn'))

    // Verify error handling (edit mode should remain enabled)
    await waitFor(() => {
      expect(screen.getByTestId('edit-mode-toggle')).toBeChecked()
    })
  })

  it('preserves parameter data integrity during updates', async () => {
    const user = userEvent.setup()
    mockFetch(mockApiResponses.updateBounds.success)

    render(<MockParameterCRUDWorkflow />)

    // Verify initial parameter data
    expect(screen.getByTestId('param-name-temperature')).toHaveTextContent('temperature')
    expect(screen.getByTestId('param-type-temperature')).toHaveTextContent('NumericalDiscrete')

    // Enable edit mode and update parameter
    await user.click(screen.getByTestId('edit-mode-toggle'))
    await user.click(screen.getByTestId('edit-btn-temperature'))
    await user.click(screen.getByTestId('apply-btn'))

    // Verify parameter data is preserved
    await waitFor(() => {
      expect(screen.getByTestId('param-name-temperature')).toHaveTextContent('temperature')
      expect(screen.getByTestId('param-type-temperature')).toHaveTextContent('NumericalDiscrete')
    })
  })
})
