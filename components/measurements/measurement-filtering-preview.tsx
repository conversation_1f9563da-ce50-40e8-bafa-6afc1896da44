"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  ChevronDown,
  ChevronRight,
  Eye,
  EyeOff,
  Lightbulb,
  Settings
} from "lucide-react"
import {
  FilteredMeasurement,
  ViolationReason
} from "./measurement-impact-analysis"

interface MeasurementFilteringPreviewProps {
  filteredMeasurements: FilteredMeasurement[]
  retainedCount: number
  onSuggestedFix?: (measurementId: string, fixType: string) => void
  maxHeight?: string
}

export function MeasurementFilteringPreview({
  filteredMeasurements,
  retainedCount,
  onSuggestedFix,
  maxHeight = "400px"
}: MeasurementFilteringPreviewProps) {
  const [showRetained, setShowRetained] = React.useState(false)
  const [expandedMeasurements, setExpandedMeasurements] = React.useState<
    Set<string>
  >(new Set())

  const toggleMeasurementExpansion = (measurementId: string) => {
    const newExpanded = new Set(expandedMeasurements)
    if (newExpanded.has(measurementId)) {
      newExpanded.delete(measurementId)
    } else {
      newExpanded.add(measurementId)
    }
    setExpandedMeasurements(newExpanded)
  }

  const getViolationIcon = (type: ViolationReason["type"]) => {
    switch (type) {
      case "bounds":
        return <AlertTriangle className="size-4 text-yellow-600" />
      case "constraint":
        return <XCircle className="size-4 text-red-600" />
      case "parameter_type":
        return <Settings className="size-4 text-blue-600" />
      case "nan_infinite":
        return <XCircle className="size-4 text-red-600" />
      default:
        return <AlertTriangle className="size-4 text-gray-600" />
    }
  }

  const getViolationColor = (type: ViolationReason["type"]) => {
    switch (type) {
      case "bounds":
        return "border-yellow-200 bg-yellow-50"
      case "constraint":
        return "border-red-200 bg-red-50"
      case "parameter_type":
        return "border-blue-200 bg-blue-50"
      case "nan_infinite":
        return "border-red-200 bg-red-50"
      default:
        return "border-gray-200 bg-gray-50"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Measurement Filtering Details</span>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-green-600">
              {retainedCount} Retained
            </Badge>
            <Badge variant="outline" className="text-red-600">
              {filteredMeasurements.length} Filtered
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Toggle for showing retained measurements */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowRetained(!showRetained)}
              className="text-green-600"
            >
              {showRetained ? (
                <EyeOff className="mr-2 size-4" />
              ) : (
                <Eye className="mr-2 size-4" />
              )}
              {showRetained ? "Hide" : "Show"} Retained Measurements (
              {retainedCount})
            </Button>
          </div>

          {/* Retained measurements summary */}
          {showRetained && (
            <Card className="border-green-200 bg-green-50">
              <CardContent className="pt-4">
                <div className="mb-2 flex items-center gap-2">
                  <CheckCircle className="size-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    {retainedCount} measurements will be retained
                  </span>
                </div>
                <p className="text-sm text-green-700">
                  These measurements meet all the new configuration requirements
                  and will be used for optimization.
                </p>
              </CardContent>
            </Card>
          )}

          {/* Filtered measurements */}
          {filteredMeasurements.length > 0 ? (
            <div className="space-y-3">
              <h4 className="flex items-center gap-2 text-sm font-medium">
                <XCircle className="size-4 text-red-600" />
                Filtered Measurements ({filteredMeasurements.length})
              </h4>

              <ScrollArea style={{ maxHeight }}>
                <div className="space-y-2 pr-4">
                  {filteredMeasurements.map(measurement => (
                    <Card
                      key={measurement.id}
                      className="border-red-200 bg-red-50"
                    >
                      <CardContent className="pt-4">
                        <Collapsible
                          open={expandedMeasurements.has(measurement.id)}
                          onOpenChange={() =>
                            toggleMeasurementExpansion(measurement.id)
                          }
                        >
                          <CollapsibleTrigger asChild>
                            <Button
                              variant="ghost"
                              className="h-auto w-full justify-between p-0"
                            >
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">
                                  Measurement {measurement.id.slice(-8)}
                                </span>
                                <Badge
                                  variant="destructive"
                                  className="text-xs"
                                >
                                  {measurement.violationReasons.length}{" "}
                                  violation
                                  {measurement.violationReasons.length !== 1
                                    ? "s"
                                    : ""}
                                </Badge>
                              </div>
                              {expandedMeasurements.has(measurement.id) ? (
                                <ChevronDown className="size-4" />
                              ) : (
                                <ChevronRight className="size-4" />
                              )}
                            </Button>
                          </CollapsibleTrigger>

                          <CollapsibleContent className="mt-3 space-y-3">
                            {/* Parameter values */}
                            <div>
                              <h5 className="mb-1 text-xs font-medium text-gray-700">
                                Parameters:
                              </h5>
                              <div className="grid grid-cols-2 gap-1 text-xs">
                                {Object.entries(measurement.parameters).map(
                                  ([param, value]) => (
                                    <div
                                      key={param}
                                      className="flex justify-between"
                                    >
                                      <span className="text-gray-600">
                                        {param}:
                                      </span>
                                      <span className="font-mono">
                                        {String(value)}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>

                            {/* Target values */}
                            <div>
                              <h5 className="mb-1 text-xs font-medium text-gray-700">
                                Targets:
                              </h5>
                              <div className="grid grid-cols-2 gap-1 text-xs">
                                {Object.entries(measurement.targetValues).map(
                                  ([target, value]) => (
                                    <div
                                      key={target}
                                      className="flex justify-between"
                                    >
                                      <span className="text-gray-600">
                                        {target}:
                                      </span>
                                      <span className="font-mono">{value}</span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>

                            <Separator />

                            {/* Violation reasons */}
                            <div>
                              <h5 className="mb-2 text-xs font-medium text-red-700">
                                Violations:
                              </h5>
                              <div className="space-y-2">
                                {measurement.violationReasons.map(
                                  (violation, index) => (
                                    <div
                                      key={index}
                                      className={`rounded border p-2 ${getViolationColor(violation.type)}`}
                                    >
                                      <div className="flex items-start gap-2">
                                        {getViolationIcon(violation.type)}
                                        <div className="min-w-0 flex-1">
                                          <div className="text-xs font-medium">
                                            {violation.field}
                                          </div>
                                          <div className="mt-1 text-xs text-gray-600">
                                            {violation.message}
                                          </div>
                                          {violation.expectedRange && (
                                            <div className="mt-1 text-xs text-gray-500">
                                              Expected:{" "}
                                              {violation.expectedRange[0]} -{" "}
                                              {violation.expectedRange[1]}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>

                            {/* Suggested fixes */}
                            {measurement.suggestedFixes.length > 0 && (
                              <div>
                                <h5 className="mb-2 flex items-center gap-1 text-xs font-medium text-blue-700">
                                  <Lightbulb className="size-3" />
                                  Suggested Fixes:
                                </h5>
                                <div className="space-y-1">
                                  {measurement.suggestedFixes.map(
                                    (fix, index) => (
                                      <div
                                        key={index}
                                        className="rounded bg-blue-50 p-2 text-xs text-blue-600"
                                      >
                                        {fix}
                                      </div>
                                    )
                                  )}
                                </div>
                                {onSuggestedFix && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                      onSuggestedFix(measurement.id, "auto_fix")
                                    }
                                    className="mt-2 h-6 text-xs"
                                  >
                                    Apply Suggested Fix
                                  </Button>
                                )}
                              </div>
                            )}
                          </CollapsibleContent>
                        </Collapsible>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </div>
          ) : (
            <Card className="border-green-200 bg-green-50">
              <CardContent className="pt-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="size-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    No measurements will be filtered
                  </span>
                </div>
                <p className="mt-1 text-sm text-green-700">
                  All existing measurements are compatible with the new
                  configuration.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
