const postgres = require('postgres');

async function testGPModel() {
  try {
    const connectionString = 'postgresql://postgres:postgres@localhost:5432/boapp';
    const client = postgres(connectionString, { max: 1, ssl: false });
    
    const optimizationId = '4420ca24-bc24-440e-af07-b55f78fda209';
    
    // Get all measurements
    const measurements = await client`
      SELECT id, parameters, target_value, target_values, created_at 
      FROM measurements 
      WHERE optimization_id = ${optimizationId}
      ORDER BY created_at
    `;
    
    console.log('=== GP MODEL DATA PREPROCESSING TEST ===\n');
    
    // Extract TCCA and Solvent data (same as the Sobol analysis)
    const X = [];
    const y = [];
    
    measurements.forEach(m => {
      X.push([m.parameters.TCCA, m.parameters.Solvent]);
      y.push(parseFloat(m.target_value));
    });
    
    console.log('Raw parameter data (first 5 samples):');
    for (let i = 0; i < Math.min(5, X.length); i++) {
      console.log(`  Sample ${i + 1}: TCCA=${X[i][0]}, Solvent=${X[i][1]}, Yield=${y[i]}`);
    }
    console.log('');
    
    // Calculate parameter statistics
    const tcca_values = X.map(x => x[0]);
    const solvent_values = X.map(x => x[1]);
    
    const tcca_min = Math.min(...tcca_values);
    const tcca_max = Math.max(...tcca_values);
    const tcca_range = tcca_max - tcca_min;
    
    const solvent_min = Math.min(...solvent_values);
    const solvent_max = Math.max(...solvent_values);
    const solvent_range = solvent_max - solvent_min;
    
    console.log('Parameter ranges:');
    console.log(`  TCCA: [${tcca_min}, ${tcca_max}] (range: ${tcca_range.toFixed(3)})`);
    console.log(`  Solvent: [${solvent_min}, ${solvent_max}] (range: ${solvent_range.toFixed(3)})`);
    console.log(`  Range ratio (Solvent/TCCA): ${(solvent_range/tcca_range).toFixed(1)}x`);
    console.log('');
    
    // This shows the scale mismatch problem!
    console.log('=== SCALE MISMATCH ANALYSIS ===');
    console.log('The GP kernel uses length_scale=[1.0, 1.0] for both parameters,');
    console.log('but the parameters have very different scales:');
    console.log(`- TCCA varies by ${tcca_range.toFixed(3)} units`);
    console.log(`- Solvent varies by ${solvent_range.toFixed(3)} units`);
    console.log('');
    console.log('This means the GP kernel treats a change of 1.0 in TCCA');
    console.log('the same as a change of 1.0 in Solvent, but Solvent has');
    console.log(`${(solvent_range/tcca_range).toFixed(1)}x larger natural variation!`);
    console.log('');
    console.log('SOLUTION: The parameters should be normalized to [0,1] or standardized');
    console.log('before training the GP model.');
    
    await client.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testGPModel().catch(console.error);
