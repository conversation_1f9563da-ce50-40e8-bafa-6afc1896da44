"use client"

import dynamic from "next/dynamic"

// Dynamically import the 3D visualization component with no SSR
const OptimizationStrategy3D = dynamic(
  () => import("@/components/tutorials/optimization-strategy-3d"),
  { ssr: false }
)

export default function OptimizationStrategy3DWrapper() {
  return (
    <div className="bg-card my-8 rounded-lg border p-6 shadow-sm">
      <h3 className="mb-4 text-xl font-semibold">
        3D Visualization of Optimization Strategies
      </h3>
      <p className="text-muted-foreground mb-4">
        Explore how sequential and parallel Bayesian optimization strategies
        navigate the objective function landscape. The visualization below shows
        sampling points on Him<PERSON><PERSON>u's function, a common test function with
        multiple local minima.
      </p>
      <OptimizationStrategy3D />
    </div>
  )
}
