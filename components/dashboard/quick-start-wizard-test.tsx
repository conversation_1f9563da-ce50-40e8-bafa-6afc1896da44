"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  Rocket,
  Play,
  BookOpen,
  Zap,
  Clock,
  Lightbulb,
  Users,
  TrendingUp,
  Video,
  ExternalLink,
  Beaker,
  GraduationCap,
  MousePointer,
  ChevronRight
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { createOptimizationWorkflowAction } from "@/actions/optimization-workflow-actions"
import { getSurveyResponsesAction } from "@/actions/survey-actions"
import { InteractiveVideoTour } from "./interactive-video-tour"
import { isFeatureEnabled, isFeatureInDevelopment } from "@/lib/feature-flags"
import { DevelopmentBadge } from "@/components/ui/development-badge"

interface QuickStartWizardProps {
  userId: string
}

interface SurveyData {
  industry?: string
  role?: string
  useCase?: string
}

interface VideoTour {
  id: string
  title: string
  description: string
  duration: string
  thumbnail: string
  videoUrl: string
  category: "overview" | "tutorial" | "advanced"
}

// Interactive video tours with UI automation
const interactiveTours = [
  {
    id: "create-optimization",
    title: "Creating Your First Optimization",
    description:
      "Watch as we create a complete optimization from start to finish",
    totalDuration: 45000,
    steps: [
      {
        id: "step-1",
        type: "click" as const,
        selector: "[data-tour='create-button']",
        description: "Click the 'Create Optimization' button",
        duration: 2000,
        x: 200,
        y: 100
      },
      {
        id: "step-2",
        type: "type" as const,
        selector: "[data-tour='name-input']",
        text: "My First Optimization",
        description: "Enter a name for your optimization",
        duration: 3000
      },
      {
        id: "step-3",
        type: "click" as const,
        selector: "[data-tour='target-dropdown']",
        description: "Select your optimization target",
        duration: 2000,
        x: 300,
        y: 200
      }
    ]
  }
]

// Traditional video tours for reference
const videoTours: VideoTour[] = [
  {
    id: "platform-overview",
    title: "Platform Overview (3 min)",
    description: "See how Bayesian Optimization works in action",
    duration: "3:24",
    thumbnail: "/videos/thumbnails/overview.jpg",
    videoUrl: "/videos/platform-overview.mp4",
    category: "overview"
  },
  {
    id: "first-optimization",
    title: "Create Your First Optimization",
    description: "Step-by-step walkthrough of the creation process",
    duration: "5:12",
    thumbnail: "/videos/thumbnails/first-optimization.jpg",
    videoUrl: "/videos/first-optimization.mp4",
    category: "tutorial"
  }
]

// Industry-specific templates
const industryTemplates = {
  "Pharmaceutical/Biotech": {
    examples: ["Drug formulation", "Reaction conditions", "Protein expression"],
    defaultTarget: "Yield",
    defaultParams: ["Temperature", "pH", "Concentration"]
  },
  Manufacturing: {
    examples: ["Process efficiency", "Quality control", "Cost reduction"],
    defaultTarget: "Efficiency",
    defaultParams: ["Speed", "Temperature", "Pressure"]
  },
  "Technology/Software": {
    examples: ["Algorithm tuning", "System performance", "User experience"],
    defaultTarget: "Performance",
    defaultParams: ["Learning rate", "Batch size", "Regularization"]
  },
  "Academic/Research": {
    examples: ["Experimental design", "Model optimization", "Data analysis"],
    defaultTarget: "Accuracy",
    defaultParams: ["Parameter A", "Parameter B", "Parameter C"]
  },
  Finance: {
    examples: [
      "Portfolio optimization",
      "Risk management",
      "Trading strategies"
    ],
    defaultTarget: "Return",
    defaultParams: ["Allocation", "Risk factor", "Time horizon"]
  }
}

export function QuickStartWizardTest({ userId }: QuickStartWizardProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [surveyData, setSurveyData] = useState<SurveyData>({})
  const [selectedVideo, setSelectedVideo] = useState<VideoTour | null>(null)
  const [isVideoDialogOpen, setIsVideoDialogOpen] = useState(false)
  const [showInteractiveTour, setShowInteractiveTour] = useState(false)

  // Load user survey data for personalization
  useEffect(() => {
    const loadSurveyData = async () => {
      try {
        const result = await getSurveyResponsesAction()
        if (result.isSuccess && result.responses) {
          setSurveyData(result.responses)
        }
      } catch (error) {
        console.error("Error loading survey data:", error)
      }
    }
    loadSurveyData()
  }, [])

  // Get personalized template based on survey data
  const getPersonalizedTemplate = () => {
    const industry = surveyData.industry
    if (industry && industry in industryTemplates) {
      return industryTemplates[industry as keyof typeof industryTemplates]
    }
    return industryTemplates["Academic/Research"] // Default fallback
  }

  const template = getPersonalizedTemplate()

  // Handle video selection and playback
  const handleVideoSelect = (video: VideoTour) => {
    setSelectedVideo(video)
    setIsVideoDialogOpen(true)
  }

  // Handle video completion tracking
  const handleVideoComplete = () => {
    toast({
      title: "Great job!",
      description: "Ready to create your first optimization?"
    })
  }

  // Handle realistic demo launch
  const handleRealisticDemo = () => {
    if (!isFeatureEnabled("createOptimizationDemo")) {
      toast({
        title: "Feature in Development",
        description:
          "The Create Optimization demo is currently being enhanced. Coming soon!",
        variant: "default"
      })
      return
    }
    // Navigate to optimization creation with demo mode
    router.push("/dashboard/optimizations/create?demo=true&guided=true")
  }

  // Handle run experiments demo launch
  const handleRunExperimentsDemo = () => {
    if (!isFeatureEnabled("runExperimentsDemo")) {
      toast({
        title: "Feature in Development",
        description:
          "The Run Experiments demo is currently being enhanced. Coming soon!",
        variant: "default"
      })
      return
    }
    // Navigate to run experiments with demo mode
    router.push("/dashboard/optimizations/demo-run/run?demo=true&guided=true")
  }

  // Handle video selection with feature flag check
  const handleVideoSelectWithCheck = (video: VideoTour) => {
    const featureKey =
      video.id === "platform-overview"
        ? "platformOverviewVideo"
        : "createFirstOptimizationVideo"

    if (!isFeatureEnabled(featureKey)) {
      toast({
        title: "Feature in Development",
        description: `The ${video.title} video is currently being produced. Coming soon!`,
        variant: "default"
      })
      return
    }

    handleVideoSelect(video)
  }

  // Handle quick start with template
  const handleQuickStart = async () => {
    setIsLoading(true)
    try {
      // Use template data to create a quick optimization
      const config = {
        parameters: template.defaultParams.map((name, index) => ({
          name,
          type: "NumericalContinuous" as const,
          bounds: [0, 100] // Default bounds
        })),
        target_config: {
          name: template.defaultTarget,
          mode: "MAX" as const
        },
        objective_type: "SingleTarget" as const,
        recommender_config: {
          type: "BotorchRecommender" as const,
          n_restarts: 10,
          n_raw_samples: 64
        },
        acquisition_config: {
          type: "qExpectedImprovement" as const
        }
      }

      const result = await createOptimizationWorkflowAction(
        `My First ${surveyData.industry || "Research"} Optimization`,
        `Quick start optimization for ${template.examples[0]}`,
        config
      )

      if (result.isSuccess && result.data) {
        toast({
          title: "Optimization Created!",
          description:
            "Your first optimization is ready. Let's start experimenting!"
        })
        router.push(`/dashboard/optimizations/${result.data.id}/run`)
      } else {
        throw new Error(result.message || "Failed to create optimization")
      }
    } catch (error) {
      console.error("Error creating quick optimization:", error)
      toast({
        title: "Error",
        description: "Failed to create optimization. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle custom optimization creation
  const handleCustomOptimization = () => {
    router.push("/dashboard/optimizations/create")
  }

  // Handle tutorial navigation
  const handleViewTutorials = () => {
    router.push("/tutorials")
  }

  // Show interactive tour if selected
  if (showInteractiveTour) {
    return (
      <div className="flex h-[400px] flex-col">
        <InteractiveVideoTour
          tour={interactiveTours[0]}
          onComplete={handleVideoComplete}
          className="flex-1"
        />
        <div className="flex justify-center pt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowInteractiveTour(false)}
          >
            Back to Options
          </Button>
        </div>
      </div>
    )
  }

  // Create a unified list of learning resources
  const learningResources = [
    // Interactive Demos
    {
      id: "create-optimization-demo",
      type: "interactive" as const,
      title: "Create Optimization",
      description: "Learn to set up your first optimization",
      duration: "5 min",
      icon: Play,
      badge: "Live",
      badgeColor: "from-blue-600 to-blue-600",
      onClick: handleRealisticDemo,
      hoverColor:
        "hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/30",
      iconBg: "from-blue-100 to-blue-50",
      iconColor: "text-blue-600"
    },
    {
      id: "run-experiments-demo",
      type: "interactive" as const,
      title: "Run Experiments",
      description: "Experience the experiment workflow",
      duration: "4 min",
      icon: Beaker,
      badge: "New",
      badgeColor: "from-purple-500 to-pink-600",
      onClick: handleRunExperimentsDemo,
      hoverColor:
        "hover:bg-gradient-to-r hover:from-purple-50/50 hover:to-pink-50/30",
      iconBg: "from-purple-100 to-purple-50",
      iconColor: "text-purple-600"
    },
    // Video Guides
    ...videoTours.map(video => ({
      id: video.id,
      type: "video" as const,
      title: video.title.replace(/\s*\(\d+\s*min\)/, ""),
      description: video.description,
      duration: video.duration,
      icon: Play,
      badge: "Video",
      badgeColor: "from-green-600 to-emerald-600",
      onClick: () => handleVideoSelectWithCheck(video),
      hoverColor:
        "hover:bg-gradient-to-r hover:from-green-50/50 hover:to-emerald-50/30",
      iconBg: "from-green-100 to-emerald-50",
      iconColor: "text-green-600"
    }))
  ]

  return (
    <>
      {/* Learning Resources Card */}
      <Card className="border-0 bg-white/90 shadow-lg backdrop-blur-sm">
        <CardHeader className="border-b border-gray-100/80 bg-gradient-to-r from-purple-50/80 to-indigo-50/30">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="flex size-8 items-center justify-center rounded-lg bg-gradient-to-br from-purple-100 to-purple-50 text-purple-600">
                  <GraduationCap className="size-5" />
                </div>
                Learning Resources
              </CardTitle>
              <CardDescription className="text-sm text-gray-600">
                {surveyData.industry && surveyData.industry !== "skipped"
                  ? `Interactive demos and video guides tailored for ${surveyData.industry}`
                  : "Interactive demos and comprehensive guides to get you started quickly"}
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* Learning Resources Grid */}
          <div className="space-y-4">
            {learningResources.map((resource, index) => (
              <div
                key={resource.id}
                className={`group flex cursor-pointer items-center gap-4 rounded-lg border border-gray-200 bg-white p-4 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md ${resource.hoverColor}`}
                onClick={resource.onClick}
              >
                <div className="relative shrink-0">
                  <div
                    className={`flex size-14 items-center justify-center rounded-xl bg-gradient-to-br ${resource.iconBg} shadow-sm transition-shadow group-hover:shadow-md`}
                  >
                    <resource.icon className={`size-7 ${resource.iconColor}`} />
                  </div>
                  <div className="absolute -right-2 -top-2">
                    <Badge
                      variant="secondary"
                      className={`border-0 bg-gradient-to-r ${resource.badgeColor} px-2 py-0.5 text-xs font-semibold text-white`}
                    >
                      {resource.duration}
                    </Badge>
                  </div>
                </div>

                <div className="min-w-0 flex-1">
                  <div className="mb-1 flex items-center gap-2">
                    <h4 className="text-lg font-semibold text-gray-900">
                      {resource.title}
                    </h4>
                    {/* Show development badge for features in development */}
                    {((resource.id === "create-optimization-demo" &&
                      isFeatureInDevelopment("createOptimizationDemo")) ||
                      (resource.id === "run-experiments-demo" &&
                        isFeatureInDevelopment("runExperimentsDemo")) ||
                      (resource.id === "platform-overview" &&
                        isFeatureInDevelopment("platformOverviewVideo")) ||
                      (resource.id === "first-optimization" &&
                        isFeatureInDevelopment(
                          "createFirstOptimizationVideo"
                        ))) && <DevelopmentBadge variant="icon" size="sm" />}
                  </div>
                  <p className="mb-2 text-sm text-gray-600">
                    {resource.description}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <Clock className="size-3" />
                    <span>Estimated time: {resource.duration}</span>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Badge
                    className={`border-0 bg-gradient-to-r ${resource.badgeColor} px-3 py-1 text-xs font-medium text-white`}
                  >
                    {resource.badge}
                  </Badge>
                  {resource.type === "interactive" ? (
                    <MousePointer className="size-5 text-blue-500 transition-colors group-hover:text-blue-600" />
                  ) : (
                    <ExternalLink className="size-5 text-gray-400 transition-colors group-hover:text-green-600" />
                  )}
                  <ChevronRight className="size-5 text-gray-400 transition-colors group-hover:text-gray-600" />
                </div>
              </div>
            ))}
          </div>

          {/* View All Tutorials Button */}
          <div className="mt-6 border-t border-gray-200 pt-6">
            <Button
              onClick={handleViewTutorials}
              variant="outline"
              size="lg"
              className="w-full border-purple-200 text-purple-700 transition-all duration-200 hover:border-purple-300 hover:bg-purple-50"
            >
              <Video className="mr-2 size-5" />
              View All Learning Resources
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Video Dialog */}
      <Dialog open={isVideoDialogOpen} onOpenChange={setIsVideoDialogOpen}>
        <DialogContent className="max-w-4xl border-0 bg-white/95 shadow-2xl backdrop-blur-sm">
          <DialogHeader className="text-center">
            <DialogTitle className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-2xl font-bold text-transparent">
              {selectedVideo?.title}
            </DialogTitle>
            <DialogDescription className="text-lg text-gray-600">
              {selectedVideo?.description}
            </DialogDescription>
          </DialogHeader>

          <div className="relative aspect-video w-full overflow-hidden rounded-2xl border border-gray-200 bg-gradient-to-br from-slate-100 to-green-100 shadow-xl">
            {selectedVideo ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="space-y-8 p-8 text-center">
                  <div className="relative">
                    <div className="mx-auto flex size-28 items-center justify-center rounded-full bg-gradient-to-br from-green-600 to-emerald-600 shadow-2xl">
                      <Play className="ml-1 size-14 text-white" />
                    </div>
                    <div className="absolute -right-3 -top-3 animate-pulse">
                      <Badge className="border-0 bg-gradient-to-r from-green-500 to-emerald-500 px-3 py-1 text-white">
                        {selectedVideo.duration}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h3 className="text-2xl font-bold text-gray-900">
                      Ready to Watch?
                    </h3>
                    <p className="mx-auto max-w-lg text-lg text-gray-600">
                      This comprehensive tutorial will guide you through every
                      step with real examples and best practices.
                    </p>
                  </div>

                  <div className="flex justify-center gap-4">
                    <Button
                      onClick={handleVideoComplete}
                      size="lg"
                      className="border-0 bg-gradient-to-r from-green-600 to-emerald-600 px-8 text-white shadow-lg transition-all duration-300 hover:from-green-700 hover:to-emerald-700 hover:shadow-xl"
                    >
                      <Play className="mr-2 size-5" />
                      Start Learning
                    </Button>
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => setIsVideoDialogOpen(false)}
                      className="border-gray-300 px-8 hover:bg-gray-50"
                    >
                      Maybe Later
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <p className="text-gray-500">No video selected</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
