#!/usr/bin/env node

/**
 * This script directly adds the subscription_type enum and column to the profiles table
 * Run with: node scripts/fix-subscription-type.js
 */

const { Client } = require('pg');

// The correct database URL with port 15435 and no SSL
const DATABASE_URL = 'postgresql://postgres:RTNjGBnorvVpzAVeJJXAWbNfVOqKhCwJ@localhost:15435/railway?sslmode=disable';

async function fixSubscriptionType() {
  const client = new Client({
    connectionString: DATABASE_URL,
    // Disable SSL completely
    ssl: false
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Check if the subscription_type enum exists
    const checkEnumQuery = `
      SELECT typname FROM pg_type WHERE typname = 'subscription_type';
    `;

    const enumResult = await client.query(checkEnumQuery);

    if (enumResult.rows.length === 0) {
      console.log('Creating subscription_type enum...');
      await client.query(`
        CREATE TYPE subscription_type AS ENUM ('monthly', 'yearly');
      `);
      console.log('✅ subscription_type enum created');
    } else {
      console.log('✅ subscription_type enum already exists');
    }

    // Check if the subscription_type column exists in the profiles table
    const checkColumnQuery = `
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'profiles' AND column_name = 'subscription_type';
    `;

    const columnResult = await client.query(checkColumnQuery);

    if (columnResult.rows.length === 0) {
      console.log('Adding subscription_type column to profiles table...');
      await client.query(`
        ALTER TABLE profiles ADD COLUMN subscription_type subscription_type;
      `);
      console.log('✅ subscription_type column added to profiles table');
    } else {
      console.log('✅ subscription_type column already exists in profiles table');
    }

    console.log('Database update completed successfully');
  } catch (error) {
    console.error('Error fixing subscription_type:', error);
  } finally {
    await client.end();
  }
}

fixSubscriptionType().catch(console.error);
