#!/usr/bin/env node

/**
 * Investigation script for missing measurements in database
 * Expected: 15 total (5 initial + 10 Bayesian optimization)
 * Found: Only 5 initial samples
 * Issue: Bayesian optimization measurements not being written to database
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, and, desc } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema definitions
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

const samplesTable = pgTable("samples", {
  id: uuid("id").primary<PERSON>ey(),
  optimizationId: uuid("optimization_id").notNull(),
  parameters: jsonb("parameters").notNull(),
  batchId: text("batch_id").notNull(),
  sampleIndex: text("sample_index").notNull(),
  samplingMethod: text("sampling_method").notNull(),
  sampleClass: text("sample_class").notNull(),
  targetValues: jsonb("target_values"),
  status: text("status").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function investigateMissingMeasurements() {
  console.log('🔍 INVESTIGATING MISSING MEASUREMENTS IN DATABASE');
  console.log('================================================');
  console.log('Expected: 15 total (5 initial + 10 Bayesian optimization)');
  console.log('Found: Only 5 initial samples');
  console.log('Issue: Bayesian optimization measurements not being written to database');
  console.log('');

  let client;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    // Find the optimization
    const optimizationName = "test CRUD filtering32";
    const optimizationsList = await db.select().from(optimizationsTable);
    const opt = optimizationsList.find(o => o.name === optimizationName);
    
    if (!opt) {
      console.error(`❌ Optimization "${optimizationName}" not found`);
      return;
    }
    
    console.log(`✅ Found optimization: ${opt.optimizerId}`);
    console.log(`   Created: ${opt.createdAt}`);
    console.log(`   Updated: ${opt.updatedAt}`);
    
    // Get ALL database samples (ordered by creation time)
    const allSamples = await db.select().from(samplesTable)
      .where(eq(samplesTable.optimizationId, opt.id))
      .orderBy(desc(samplesTable.createdAt));
    
    console.log(`\n📊 COMPLETE DATABASE ANALYSIS (${allSamples.length} total samples):`);
    
    if (allSamples.length === 0) {
      console.log('  ❌ No samples found in database!');
      return;
    }
    
    // Group samples by batch ID and sampling method
    const samplesByBatch = {};
    const samplesByMethod = {};
    const samplesByStatus = {};
    const samplesByClass = {};
    
    allSamples.forEach((sample, index) => {
      // Group by batch ID
      if (!samplesByBatch[sample.batchId]) {
        samplesByBatch[sample.batchId] = [];
      }
      samplesByBatch[sample.batchId].push(sample);
      
      // Group by sampling method
      if (!samplesByMethod[sample.samplingMethod]) {
        samplesByMethod[sample.samplingMethod] = [];
      }
      samplesByMethod[sample.samplingMethod].push(sample);
      
      // Group by status
      if (!samplesByStatus[sample.status]) {
        samplesByStatus[sample.status] = [];
      }
      samplesByStatus[sample.status].push(sample);
      
      // Group by sample class
      if (!samplesByClass[sample.sampleClass]) {
        samplesByClass[sample.sampleClass] = [];
      }
      samplesByClass[sample.sampleClass].push(sample);
      
      console.log(`\n  ${index + 1}. Sample ${sample.id.slice(-12)}:`);
      console.log(`     - Batch ID: ${sample.batchId}`);
      console.log(`     - Sampling Method: ${sample.samplingMethod}`);
      console.log(`     - Sample Class: ${sample.sampleClass}`);
      console.log(`     - Status: ${sample.status}`);
      console.log(`     - Parameters: ${JSON.stringify(sample.parameters)}`);
      console.log(`     - Target Values: ${JSON.stringify(sample.targetValues)}`);
      console.log(`     - Created: ${sample.createdAt}`);
      console.log(`     - Updated: ${sample.updatedAt}`);
    });
    
    // Analyze by batch
    console.log(`\n📊 ANALYSIS BY BATCH:`);
    Object.entries(samplesByBatch).forEach(([batchId, samples]) => {
      console.log(`\n  Batch: ${batchId}`);
      console.log(`  - Count: ${samples.length} samples`);
      console.log(`  - Sampling Method: ${samples[0].samplingMethod}`);
      console.log(`  - Sample Class: ${samples[0].sampleClass}`);
      console.log(`  - Created: ${samples[0].createdAt}`);
      
      // Show Target 1 values for this batch
      const target1Values = samples.map(s => s.targetValues?.['Target 1']).filter(v => v !== undefined);
      if (target1Values.length > 0) {
        console.log(`  - Target 1 values: [${target1Values.join(', ')}]`);
      }
    });
    
    // Analyze by sampling method
    console.log(`\n📊 ANALYSIS BY SAMPLING METHOD:`);
    Object.entries(samplesByMethod).forEach(([method, samples]) => {
      console.log(`\n  Method: ${method}`);
      console.log(`  - Count: ${samples.length} samples`);
      console.log(`  - Batches: ${[...new Set(samples.map(s => s.batchId))].length}`);
      
      if (method === 'LHS') {
        console.log(`  - ✅ Initial sampling (expected)`);
      } else if (method.includes('Bayesian') || method.includes('BO') || method.includes('optimization')) {
        console.log(`  - 🎯 Bayesian optimization samples`);
      } else {
        console.log(`  - ❓ Unknown method type`);
      }
    });
    
    // Analyze by status
    console.log(`\n📊 ANALYSIS BY STATUS:`);
    Object.entries(samplesByStatus).forEach(([status, samples]) => {
      console.log(`  ${status}: ${samples.length} samples`);
    });
    
    // Analyze by sample class
    console.log(`\n📊 ANALYSIS BY SAMPLE CLASS:`);
    Object.entries(samplesByClass).forEach(([sampleClass, samples]) => {
      console.log(`  ${sampleClass}: ${samples.length} samples`);
    });
    
    // Check for missing Bayesian optimization samples
    console.log(`\n🔍 MISSING BAYESIAN OPTIMIZATION ANALYSIS:`);
    
    const bayesianSamples = allSamples.filter(s => 
      s.samplingMethod.toLowerCase().includes('bayesian') || 
      s.samplingMethod.toLowerCase().includes('bo') ||
      s.sampleClass.toLowerCase().includes('optimization') ||
      s.sampleClass.toLowerCase().includes('bayesian')
    );
    
    const initialSamples = allSamples.filter(s => 
      s.samplingMethod === 'LHS' || 
      s.sampleClass === 'exploratory'
    );
    
    console.log(`  Initial samples (LHS/exploratory): ${initialSamples.length}`);
    console.log(`  Bayesian optimization samples: ${bayesianSamples.length}`);
    console.log(`  Total found: ${allSamples.length}`);
    console.log(`  Expected total: 15`);
    console.log(`  Missing: ${15 - allSamples.length}`);
    
    if (bayesianSamples.length === 0) {
      console.log(`  ❌ NO BAYESIAN OPTIMIZATION SAMPLES FOUND!`);
      console.log(`  This suggests the Bayesian optimization measurements are not being written to the database.`);
    } else {
      console.log(`  ✅ Found ${bayesianSamples.length} Bayesian optimization samples`);
    }
    
    // Try to get backend measurements to compare
    console.log(`\n🔍 COMPARING WITH BACKEND MEASUREMENTS:`);
    
    try {
      // First try to load the campaign
      const loadResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/load`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (loadResponse.ok) {
        console.log(`  ✅ Campaign loaded successfully`);
        
        // Now get measurements
        const measurementsResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/measurements`);
        
        if (measurementsResponse.ok) {
          const measurementsData = await measurementsResponse.json();
          const backendMeasurements = measurementsData.measurements || [];
          
          console.log(`  Backend measurements: ${backendMeasurements.length}`);
          console.log(`  Database samples: ${allSamples.length}`);
          
          if (backendMeasurements.length > allSamples.length) {
            console.log(`  🚨 BACKEND HAS MORE MEASUREMENTS THAN DATABASE!`);
            console.log(`  This confirms that Bayesian optimization measurements exist in backend but are not saved to database.`);
            
            // Show backend measurements
            console.log(`\n  📊 BACKEND MEASUREMENTS ANALYSIS:`);
            backendMeasurements.forEach((measurement, index) => {
              console.log(`    ${index + 1}. Target 1: ${measurement['Target 1']}, Target 2: ${measurement['Target 2']}, Batch: ${measurement['BatchNr']}, Fit: ${measurement['FitNr']}`);
            });
            
            // Try to identify which measurements are missing from database
            console.log(`\n  🔍 IDENTIFYING MISSING MEASUREMENTS:`);
            
            backendMeasurements.forEach((measurement, index) => {
              const target1 = measurement['Target 1'];
              const target2 = measurement['Target 2'];
              
              // Try to find this measurement in database
              const matchingSample = allSamples.find(s => 
                s.targetValues?.['Target 1'] === target1 && 
                s.targetValues?.['Target 2'] === target2
              );
              
              if (!matchingSample) {
                console.log(`    ❌ Missing from DB: Target 1=${target1}, Target 2=${target2}, Batch=${measurement['BatchNr']}, Fit=${measurement['FitNr']}`);
              } else {
                console.log(`    ✅ Found in DB: Target 1=${target1}, Target 2=${target2}`);
              }
            });
            
          } else if (backendMeasurements.length === allSamples.length) {
            console.log(`  ✅ Backend and database have same count`);
          } else {
            console.log(`  ❌ Database has more samples than backend`);
          }
          
        } else {
          console.log(`  ❌ Failed to get backend measurements: ${measurementsResponse.statusText}`);
        }
      } else {
        console.log(`  ❌ Failed to load campaign: ${loadResponse.statusText}`);
      }
    } catch (error) {
      console.log(`  ❌ Error accessing backend: ${error.message}`);
    }
    
    console.log(`\n🎯 INVESTIGATION SUMMARY:`);
    console.log(`=========================`);
    console.log(`Database samples found: ${allSamples.length}`);
    console.log(`Expected total: 15`);
    console.log(`Missing: ${15 - allSamples.length}`);
    
    if (allSamples.length < 15) {
      console.log(`\n❌ CONFIRMED: Bayesian optimization measurements are not being written to database!`);
      console.log(`This explains why the target filtering preview shows incorrect counts.`);
      console.log(`\nRoot causes to investigate:`);
      console.log(`1. Database writing logic for Bayesian optimization results`);
      console.log(`2. API endpoints that should save measurements to database`);
      console.log(`3. Frontend submission flow for optimization results`);
    }
    
  } catch (error) {
    console.error('❌ Error during investigation:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

// Run the investigation
investigateMissingMeasurements().catch(console.error);
