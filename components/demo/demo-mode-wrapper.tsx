"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  X,
  Play,
  Pause,
  RotateCcw,
  ArrowRight,
  MousePointer,
  Lightbulb,
  CheckCircle,
  Eye
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"

interface DemoStep {
  id: string
  title: string
  description: string
  target: string // CSS selector
  position: "top" | "bottom" | "left" | "right"
  action: "click" | "type" | "highlight" | "wait"
  content?: string
  duration?: number
  wizardStep?: number // Which wizard step this demo step belongs to
}

interface DemoModeWrapperProps {
  children: React.ReactNode
  isDemo: boolean
  onExitDemo: () => void
  steps: DemoStep[]
  className?: string
  currentWizardStep?: number
  onStepChange?: (step: number) => void
  onDemoComplete?: () => void
}

export function DemoModeWrapper({
  children,
  isDemo,
  onExitDemo,
  steps,
  className,
  currentWizardStep = 0,
  onStepChange,
  onDemoComplete
}: DemoModeWrapperProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isCompleted, setIsCompleted] = useState(false)
  const [highlightedElement, setHighlightedElement] = useState<Element | null>(
    null
  )
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 })
  const containerRef = useRef<HTMLDivElement>(null)

  // Calculate progress
  const progress = ((currentStep + 1) / steps.length) * 100

  // Find and highlight target element
  useEffect(() => {
    if (!isDemo || !steps[currentStep]) return

    const step = steps[currentStep]
    const element = document.querySelector(step.target)

    if (element) {
      setHighlightedElement(element)

      // Auto-fill form inputs if this is a type action
      if (step.action === "type" && step.content) {
        if (
          element instanceof HTMLInputElement ||
          element instanceof HTMLTextAreaElement
        ) {
          // Clear existing value and type new content
          element.value = ""
          element.focus()

          // Simulate typing with a delay
          setTimeout(() => {
            element.value = step.content!
            // Trigger input event to update React form state
            const event = new Event("input", { bubbles: true })
            element.dispatchEvent(event)
            // Also trigger change event
            const changeEvent = new Event("change", { bubbles: true })
            element.dispatchEvent(changeEvent)
          }, 500)
        }
      }

      // Auto-click for click actions (but only when playing)
      // Note: Removed auto-click for create-optimization to let user manually click
      if (
        step.action === "click" &&
        isPlaying &&
        step.id !== "create-optimization"
      ) {
        setTimeout(() => {
          if (element instanceof HTMLElement) {
            element.click()
          }
        }, 2000) // Give time to read the instruction
      }

      // Calculate tooltip position
      const rect = element.getBoundingClientRect()
      const containerRect = containerRef.current?.getBoundingClientRect()

      if (containerRect) {
        let x = rect.left - containerRect.left + rect.width / 2
        let y = rect.top - containerRect.top

        // Adjust position based on step position preference
        switch (step.position) {
          case "top":
            y = rect.top - containerRect.top - 10
            break
          case "bottom":
            y = rect.bottom - containerRect.top + 10
            break
          case "left":
            x = rect.left - containerRect.left - 10
            y = rect.top - containerRect.top + rect.height / 2
            break
          case "right":
            x = rect.right - containerRect.left + 10
            y = rect.top - containerRect.top + rect.height / 2
            break
        }

        setTooltipPosition({ x, y })
      }

      // Scroll element into view
      element.scrollIntoView({ behavior: "smooth", block: "center" })
    }
  }, [isDemo, currentStep, steps])

  // Sync demo step with wizard step
  useEffect(() => {
    if (!isDemo) return

    // Find the first step that matches the current wizard step
    const wizardStepSteps = steps.filter(
      step =>
        step.wizardStep === undefined || step.wizardStep === currentWizardStep
    )

    if (wizardStepSteps.length > 0) {
      const firstStepIndex = steps.findIndex(
        step =>
          step.wizardStep === undefined || step.wizardStep === currentWizardStep
      )
      if (firstStepIndex !== -1 && firstStepIndex !== currentStep) {
        setCurrentStep(firstStepIndex)
        setIsCompleted(false)
      }
    }
  }, [currentWizardStep, isDemo, steps])

  // Auto-play functionality - but only for steps in current wizard step
  useEffect(() => {
    if (!isPlaying || !steps[currentStep]) return

    const step = steps[currentStep]

    // Don't auto-advance if this step is for a different wizard step
    if (
      step.wizardStep !== undefined &&
      step.wizardStep !== currentWizardStep
    ) {
      return
    }

    const duration = step.duration || 3000

    const timer = setTimeout(() => {
      nextStep()
    }, duration)

    return () => clearTimeout(timer)
  }, [isPlaying, currentStep, steps, currentWizardStep])

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      const nextStepIndex = currentStep + 1
      const nextStep = steps[nextStepIndex]

      // If next step is for a different wizard step, pause and wait
      if (
        nextStep.wizardStep !== undefined &&
        nextStep.wizardStep !== currentWizardStep
      ) {
        setIsPlaying(false)
        return
      }

      setCurrentStep(nextStepIndex)
    } else {
      setIsCompleted(true)
      setIsPlaying(false)

      // Note: Completion is now handled by the wizard when "Complete Demo" is clicked
      // No automatic callback here since the final step just highlights the button
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
      setIsCompleted(false)
    }
  }

  const togglePlayback = () => {
    setIsPlaying(!isPlaying)
  }

  const resetDemo = () => {
    setCurrentStep(0)
    setIsPlaying(false)
    setIsCompleted(false)
  }

  const handleStepClick = (stepIndex: number) => {
    setCurrentStep(stepIndex)
    setIsCompleted(false)
  }

  if (!isDemo) {
    return <>{children}</>
  }

  const currentStepData = steps[currentStep]

  return (
    <div ref={containerRef} className={cn("relative", className)}>
      {/* Demo Overlay */}
      <div className="pointer-events-none absolute inset-0 z-40">
        {/* Subtle Backdrop - no blur */}
        <div className="absolute inset-0 bg-blue-500/5" />

        {/* Highlighted Element Spotlight */}
        {highlightedElement && (
          <div
            className="pointer-events-none absolute rounded-lg border-2 border-blue-500 bg-blue-500/10 shadow-lg"
            style={{
              left:
                highlightedElement.getBoundingClientRect().left -
                containerRef.current!.getBoundingClientRect().left -
                4,
              top:
                highlightedElement.getBoundingClientRect().top -
                containerRef.current!.getBoundingClientRect().top -
                4,
              width: highlightedElement.getBoundingClientRect().width + 8,
              height: highlightedElement.getBoundingClientRect().height + 8
            }}
          >
            {/* Pulsing animation */}
            <div className="absolute inset-0 animate-pulse rounded-lg border-2 border-blue-400" />
          </div>
        )}

        {/* Step Tooltip */}
        {currentStepData && (
          <AnimatePresence>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="pointer-events-auto absolute z-50"
              style={{
                left: tooltipPosition.x,
                top: tooltipPosition.y,
                transform: "translate(-50%, -100%)"
              }}
            >
              <Card className="max-w-sm border-blue-200 bg-white shadow-xl">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex size-8 items-center justify-center rounded-full bg-blue-100">
                      {currentStepData.action === "click" && (
                        <MousePointer className="size-4 text-blue-600" />
                      )}
                      {currentStepData.action === "type" && (
                        <span className="text-xs font-bold text-blue-600">
                          Aa
                        </span>
                      )}
                      {currentStepData.action === "highlight" && (
                        <Eye className="size-4 text-blue-600" />
                      )}
                      {currentStepData.action === "wait" && (
                        <Lightbulb className="size-4 text-blue-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-gray-900">
                        {currentStepData.title}
                      </h4>
                      <p className="mt-1 text-xs text-gray-600">
                        {currentStepData.description}
                      </p>
                      {currentStepData.content && (
                        <div className="mt-2 rounded bg-gray-50 p-2 font-mono text-xs">
                          {currentStepData.content}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
              {/* Arrow pointing to element */}
              <div className="absolute left-1/2 top-full -translate-x-1/2">
                <div className="size-0 border-x-4 border-t-4 border-transparent border-t-white" />
              </div>
            </motion.div>
          </AnimatePresence>
        )}
      </div>

      {/* Demo Controls */}
      <div className="fixed right-4 top-4 z-50">
        <Card className="border-blue-200 bg-white/95 shadow-lg backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Badge className="bg-blue-600 text-white">🎬 Demo Mode</Badge>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={togglePlayback}
                  className="size-8 p-0"
                >
                  {isPlaying ? (
                    <Pause className="size-4" />
                  ) : (
                    <Play className="size-4" />
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetDemo}
                  className="size-8 p-0"
                >
                  <RotateCcw className="size-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log("Test redirect button clicked")
                    onDemoComplete?.()
                  }}
                  className="size-8 p-0"
                  title="Test Redirect"
                >
                  🏠
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onExitDemo}
                  className="size-8 p-0"
                >
                  <X className="size-4" />
                </Button>
              </div>
            </div>

            <div className="mt-3 space-y-2">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span>
                  Demo Step {currentStep + 1} of {steps.length}
                </span>
                <span>Wizard Step {currentWizardStep + 1}</span>
              </div>
              <Progress value={progress} className="h-1" />
            </div>

            {isCompleted && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-3 rounded-lg border border-blue-200 bg-blue-50 p-2"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-blue-800">
                    <CheckCircle className="size-4" />
                    <span className="text-xs font-medium">
                      Ready to complete! Click the button below.
                    </span>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      console.log("Manual home button clicked")
                      window.location.href = "/dashboard/home"
                    }}
                    className="h-6 px-2 text-xs"
                  >
                    Go Home
                  </Button>
                </div>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Step Navigation */}
      <div className="fixed bottom-4 left-1/2 z-50 -translate-x-1/2">
        <Card className="border-blue-200 bg-white/95 shadow-lg backdrop-blur-sm">
          <CardContent className="p-3">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={prevStep}
                disabled={currentStep === 0}
                className="h-8 px-3"
              >
                Previous
              </Button>

              <div className="flex gap-1">
                {steps.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => handleStepClick(index)}
                    className={cn(
                      "size-2 rounded-full transition-colors",
                      index === currentStep
                        ? "bg-blue-600"
                        : index < currentStep
                          ? "bg-green-500"
                          : "bg-gray-300"
                    )}
                  />
                ))}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={nextStep}
                disabled={currentStep === steps.length - 1}
                className="h-8 px-3"
              >
                Next
                <ArrowRight className="ml-1 size-3" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Original Content - Keep interactive */}
      <div className="relative">{children}</div>
    </div>
  )
}
