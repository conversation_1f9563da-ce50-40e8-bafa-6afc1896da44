// actions/db/delete-saved-suggestions-action.ts
"use server"

import { db } from "@/db/db"
import { suggestionsTable } from "@/db/schema"
import { eq, and } from "drizzle-orm"
import { auth } from "@clerk/nextjs/server"
import { getOptimizationByIdAction } from "./optimizations-actions"

export type ActionState<T = undefined> = {
  isSuccess: boolean
  message: string
  data?: T
}

/**
 * Delete all saved suggestions for an optimization
 */
export async function deleteSavedSuggestionsAction(
  optimizationId: string
): Promise<ActionState<{ count: number }>> {
  try {
    // Verify user has access to the optimization
    const { userId } = await auth()
    if (!userId) {
      return {
        isSuccess: false,
        message: "You must be signed in to delete suggestions"
      }
    }

    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Delete all saved suggestions for this optimization
    const result = await db
      .delete(suggestionsTable)
      .where(
        and(
          eq(suggestionsTable.optimizationId, optimizationId),
          eq(suggestionsTable.status, "saved")
        )
      )
      .returning({ id: suggestionsTable.id })

    const count = result.length

    return {
      isSuccess: true,
      message: `Successfully deleted ${count} saved suggestions`,
      data: { count }
    }
  } catch (error) {
    console.error("Error deleting saved suggestions:", error)
    return {
      isSuccess: false,
      message: `Failed to delete saved suggestions: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
