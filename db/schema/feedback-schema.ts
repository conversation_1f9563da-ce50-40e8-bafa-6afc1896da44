// db/schema/feedback-schema.ts
import {
  pgTable,
  uuid,
  text,
  timestamp,
  integer,
  jsonb
} from "drizzle-orm/pg-core"

// Feedback categories enum
export const feedbackCategories = [
  "bug",
  "feature_request",
  "usability",
  "performance",
  "general"
] as const

// Feedback table for storing user feedback
export const feedbackTable = pgTable("feedback", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: text("user_id").notNull(),
  category: text("category").notNull(), // One of the feedbackCategories
  title: text("title").notNull(),
  description: text("description").notNull(),
  rating: integer("rating"), // Optional rating (1-5)
  metadata: jsonb("metadata"), // Additional metadata (page URL, browser info, etc.)
  status: text("status").notNull().default("new"), // new, in_progress, resolved, closed

  // Metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
  resolvedAt: timestamp("resolved_at")
})

// Type definitions
export type InsertFeedback = typeof feedbackTable.$inferInsert
export type SelectFeedback = typeof feedbackTable.$inferSelect
