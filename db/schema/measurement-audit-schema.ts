// db/schema/measurement-audit-schema.ts
import { pgTable, uuid, text, timestamp, jsonb } from "drizzle-orm/pg-core"
import { measurementsTable } from "./optimizations-schema"

// Audit trail table for tracking measurement changes
export const measurementAuditTable = pgTable("measurement_audit", {
  id: uuid("id").defaultRandom().primaryKey(),
  measurementId: uuid("measurement_id")
    .references(() => measurementsTable.id, { onDelete: "cascade" })
    .notNull(),

  // Action details
  action: text("action").notNull(), // 'create', 'update', 'delete'
  userId: text("user_id").notNull(),

  // Data snapshots
  oldData: jsonb("old_data"), // Previous state (null for create)
  newData: jsonb("new_data"), // New state (null for delete)

  // Change details
  changedFields: jsonb("changed_fields"), // Array of field names that changed
  reason: text("reason"), // Optional reason for the change

  // Metadata
  userAgent: text("user_agent"),
  ipAddress: text("ip_address"),

  // Timestamps
  createdAt: timestamp("created_at").defaultNow().notNull()
})

// Type definitions
export type InsertMeasurementAudit = typeof measurementAuditTable.$inferInsert
export type SelectMeasurementAudit = typeof measurementAuditTable.$inferSelect

// Helper types for audit data
export interface MeasurementAuditData {
  id?: string
  parameters: Record<string, any>
  targetValue: string
  targetValues?: Record<string, number>
  isRecommended: boolean
  batchId?: string | null
  createdAt: string
  updatedAt: string
}

export interface AuditContext {
  userId: string
  userAgent?: string
  ipAddress?: string
  reason?: string
}
