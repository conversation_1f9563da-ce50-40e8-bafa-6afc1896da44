// Demo steps for the Run Experiments interactive demo
export const runExperimentsDemoSteps = [
  {
    id: "welcome",
    title: "Welcome to the Experiments Demo! 🧪",
    description:
      "You're about to learn how to run experiments on an optimization. This demo uses a drug formulation optimization as an example.",
    target: "[data-demo='experiments-container']",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 4000
  },
  {
    id: "optimization-overview",
    title: "Your Optimization Setup",
    description:
      "This optimization is configured to maximize Bioavailability by adjusting API Concentration, pH Level, and Excipient Ratio. Let's run some experiments!",
    target: "[data-demo='experiments-container']",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 4000
  },
  {
    id: "tabs-overview",
    title: "Three Ways to Run Experiments",
    description:
      "You can run experiments in three ways: AI-suggested experiments (recommended), sample generation for exploration, or manual entry of your own data.",
    target: ".tabs-list",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 4000
  },
  {
    id: "suggested-tab",
    title: "AI-Suggested Experiments (Recommended)",
    description:
      "The AI suggests the most promising experiments based on Bayesian optimization. This is the most efficient way to find optimal parameters.",
    target: "[value='suggested']",
    position: "bottom" as const,
    action: "click" as const,
    duration: 3000
  },
  {
    id: "get-suggestions",
    title: "Get AI Recommendations",
    description:
      "Click 'Get New Suggestions' to let the AI recommend the most promising experiments to run next.",
    target: "[data-demo='get-suggestions-button']",
    position: "top" as const,
    action: "click" as const,
    duration: 3000
  },
  {
    id: "suggestions-loading",
    title: "AI is Thinking... 🤖",
    description:
      "The AI is analyzing your optimization space and finding the most promising parameter combinations to test next.",
    target: "[data-demo='suggestions-container']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 3000
  },
  {
    id: "review-suggestions",
    title: "Review AI Suggestions",
    description:
      "Here are the AI's recommendations! Each suggestion shows the parameter values to test. Notice how they explore different regions of your parameter space.",
    target: "[data-demo='suggestions-list']",
    position: "left" as const,
    action: "highlight" as const,
    duration: 4000
  },
  {
    id: "add-results",
    title: "Add Experimental Results",
    description:
      "After running these experiments in your lab, you'd enter the results here. For the demo, we'll add some sample results.",
    target: "[data-demo='target-input-0']",
    position: "top" as const,
    action: "highlight" as const,
    duration: 3000
  },
  {
    id: "enter-result-value",
    title: "Enter a Result Value",
    description:
      "Let's enter a bioavailability result of 85.2%. In real use, this would be your actual experimental measurement.",
    target: "[data-demo='target-input-0']",
    position: "top" as const,
    action: "type" as const,
    text: "85.2",
    duration: 2000
  },
  {
    id: "submit-experiment",
    title: "Submit the Experiment",
    description:
      "Click 'Submit' to add this experiment to your optimization. The AI will learn from this data to make better suggestions.",
    target: "[data-demo='submit-suggestion-0']",
    position: "top" as const,
    action: "click" as const,
    duration: 3000
  },
  {
    id: "experiment-submitted",
    title: "Great! Experiment Added 📊",
    description:
      "Your experiment has been added to the optimization. The AI now has data to learn from and will make better suggestions.",
    target: "[data-demo='suggestions-container']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 3000
  },
  {
    id: "sample-generation-tab",
    title: "Alternative: Sample Generation",
    description:
      "You can also generate samples for broader exploration of your parameter space. This is useful for initial experiments.",
    target: "[value='samples']",
    position: "bottom" as const,
    action: "click" as const,
    duration: 3000
  },
  {
    id: "sample-generation-overview",
    title: "Generate Experimental Samples",
    description:
      "Sample generation creates a set of parameter combinations to test. This helps you explore your parameter space systematically.",
    target: "[data-demo='sample-generation-section']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 4000
  },
  {
    id: "generate-samples-button",
    title: "Generate Sample Experiments",
    description:
      "Click 'Generate Samples' to create a set of experiments that systematically explore your parameter space.",
    target: "[data-demo='generate-samples-button']",
    position: "top" as const,
    action: "click" as const,
    duration: 3000
  },
  {
    id: "samples-generated",
    title: "Samples Generated! 🎯",
    description:
      "Perfect! These samples provide good coverage of your parameter space. You can run these experiments and enter the results.",
    target: "[data-demo='samples-table']",
    position: "left" as const,
    action: "highlight" as const,
    duration: 4000
  },
  {
    id: "manual-entry-tab",
    title: "Manual Entry Option",
    description:
      "You can also manually enter experiments you've already conducted. This is useful for adding historical data.",
    target: "[value='manual']",
    position: "bottom" as const,
    action: "click" as const,
    duration: 3000
  },
  {
    id: "manual-entry-overview",
    title: "Manual Experiment Entry",
    description:
      "Use this tab to enter experiments you've conducted outside the system. This helps incorporate all your experimental data.",
    target: "[data-demo='manual-entry-section']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 4000
  },
  {
    id: "workflow-summary",
    title: "Experiment Workflow Summary",
    description:
      "You've learned the complete workflow: 1) Get AI suggestions, 2) Run experiments, 3) Enter results, 4) Repeat for optimization!",
    target: "[data-demo='experiments-container']",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 4000
  },
  {
    id: "next-steps",
    title: "Ready to Optimize! 🚀",
    description:
      "You now know how to run experiments effectively. In real use, you'd continue this cycle to find optimal parameters for your process.",
    target: "[data-demo='experiments-container']",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 4000
  },
  {
    id: "completion",
    title: "🎉 Experiments Demo Complete!",
    description:
      "Congratulations! You've mastered the experiment workflow. You're ready to optimize your own processes!",
    target: "[data-demo='experiments-container']",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 3000
  }
]
