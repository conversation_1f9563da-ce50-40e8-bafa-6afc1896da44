#!/usr/bin/env node

/**
 * Check the status of optimizations that are failing
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function checkOptimizationStatus() {
  console.log('🔍 CHECKING OPTIMIZATION STATUS');
  console.log('===============================');
  
  let client;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    const failingOptimizations = [
      "user_30EFn0yPauqlMMorn5hfsn3of5X_test_CRUD_filtering32_1757677111499",
      "user_30EFn0yPauqlMMorn5hfsn3of5X_Test_CRUD_Filtering2_1757675394674"
    ];
    
    for (const optimizerId of failingOptimizations) {
      console.log(`\n🔍 CHECKING: ${optimizerId.slice(-20)}`);
      console.log('='.repeat(50));
      
      // Check database
      const dbOpt = await db.select().from(optimizationsTable)
        .where(eq(optimizationsTable.optimizerId, optimizerId))
        .limit(1);
      
      if (dbOpt.length === 0) {
        console.log('   ❌ Not found in database');
        continue;
      }
      
      const opt = dbOpt[0];
      console.log(`   📋 Database Status: ${opt.status}`);
      console.log(`   📋 Name: ${opt.name}`);
      console.log(`   📋 Created: ${opt.createdAt}`);
      console.log(`   📋 Updated: ${opt.updatedAt}`);
      
      if (opt.config) {
        console.log(`   📋 Objective Type: ${opt.config.objective_type}`);
        console.log(`   📋 Acquisition Function: ${opt.config.acquisition_config?.type}`);
        
        if (opt.config.parameters) {
          console.log(`   📋 Parameters: ${opt.config.parameters.length}`);
          opt.config.parameters.forEach((param, index) => {
            console.log(`     ${index + 1}. ${param.name}: ${param.type} [${param.bounds?.join(', ') || 'no bounds'}]`);
          });
        }
        
        if (opt.config.targets) {
          console.log(`   📋 Targets: ${opt.config.targets.length}`);
          opt.config.targets.forEach((target, index) => {
            console.log(`     ${index + 1}. ${target.name}: ${target.mode} [${target.bounds?.join(', ') || 'no bounds'}]`);
          });
        } else if (opt.config.target_config) {
          console.log(`   📋 Target Config: ${opt.config.target_config.name} (${opt.config.target_config.mode})`);
        }
      } else {
        console.log('   ❌ No configuration in database');
      }
      
      // Test backend access
      console.log('\n   🔍 Testing backend access...');
      
      try {
        // Test basic optimization info
        const infoResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/info`);
        console.log(`     Info endpoint: ${infoResponse.status} ${infoResponse.statusText}`);
        
        if (infoResponse.ok) {
          const infoData = await infoResponse.json();
          console.log(`     Backend objective type: ${infoData.objective_type}`);
        }
        
        // Test measurements endpoint
        const measurementsResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`);
        console.log(`     Measurements endpoint: ${measurementsResponse.status} ${measurementsResponse.statusText}`);
        
        if (!measurementsResponse.ok) {
          const errorText = await measurementsResponse.text();
          console.log(`     Error details: ${errorText.substring(0, 200)}`);
        }
        
        // Test load endpoint
        const loadResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/load`, {
          method: 'POST'
        });
        console.log(`     Load endpoint: ${loadResponse.status} ${loadResponse.statusText}`);
        
        if (!loadResponse.ok) {
          const errorText = await loadResponse.text();
          console.log(`     Load error: ${errorText.substring(0, 200)}`);
        }
        
      } catch (error) {
        console.log(`     ❌ Backend access error: ${error.message}`);
      }
    }
    
    // Compare with a working optimization
    console.log(`\n🔍 COMPARISON: Working optimization`);
    console.log('='.repeat(40));
    
    const workingOptId = "user_30EFn0yPauqlMMorn5hfsn3of5X_Test_campaign_working_1757682871157";
    
    const workingOpt = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.optimizerId, workingOptId))
      .limit(1);
    
    if (workingOpt.length > 0) {
      const opt = workingOpt[0];
      console.log(`   📋 Status: ${opt.status}`);
      console.log(`   📋 Objective Type: ${opt.config?.objective_type}`);
      console.log(`   📋 Acquisition Function: ${opt.config?.acquisition_config?.type}`);
      
      // Test backend access
      const measurementsResponse = await fetch(`http://localhost:8000/optimizations/${workingOptId}/measurements`);
      console.log(`   📋 Backend measurements: ${measurementsResponse.status} ${measurementsResponse.statusText}`);
    }
    
  } catch (error) {
    console.error('❌ Error during status check:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

// Run the check
checkOptimizationStatus().catch(console.error);
