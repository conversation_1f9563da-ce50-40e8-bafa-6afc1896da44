#!/usr/bin/env node

/**
 * Debug what's happening with Pareto target_config during updates
 */

async function debugParetoTargetConfig() {
  console.log('🔍 DEBUGGING PARETO TARGET_CONFIG ISSUE');
  console.log('=======================================');
  
  // Create a simple Pareto optimization
  const optimizerId = `debug_pareto_${Date.now()}`;
  
  console.log(`\n📋 STEP 1: Create Pareto optimization`);
  console.log('='.repeat(40));
  
  const paretoConfig = {
    optimizer_id: optimizerId,
    parameters: [
      { name: "x1", type: "NumericalContinuous", bounds: [0, 100] },
      { name: "x2", type: "NumericalContinuous", bounds: [0, 100] }
    ],
    target_config: [
      { name: "Target 1", mode: "MAX", type: "Numerical" },
      { name: "Target 2", mode: "MIN", type: "Numerical" }
    ],
    objective_type: "Pareto",
    acquisition_config: { type: "qNoisyExpectedHypervolumeImprovement" },
    initial_sampling_strategy: "LHS",
    num_initial_samples: 0
  };
  
  try {
    const createResponse = await fetch('http://localhost:8000/optimizations', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(paretoConfig)
    });
    
    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      console.log(`❌ Creation failed: ${createResponse.status} - ${errorText}`);
      return;
    }
    
    console.log(`✅ Pareto optimization created: ${optimizerId}`);
    
    console.log(`\n📋 STEP 2: Get campaign info to see target_config format`);
    console.log('='.repeat(50));
    
    const infoResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/info`);
    
    if (infoResponse.ok) {
      const infoData = await infoResponse.json();
      console.log(`✅ Campaign info retrieved`);
      console.log(`📊 Objective type: ${infoData.objective_type}`);
      console.log(`📊 Target config type: ${typeof infoData.target_config}`);
      console.log(`📊 Target config: ${JSON.stringify(infoData.target_config, null, 2)}`);
      
      if (infoData.targets) {
        console.log(`📊 Targets: ${JSON.stringify(infoData.targets, null, 2)}`);
      }
    } else {
      const errorText = await infoResponse.text();
      console.log(`❌ Cannot get campaign info: ${infoResponse.status} - ${errorText}`);
      return;
    }
    
    console.log(`\n📋 STEP 3: Add a measurement`);
    console.log('='.repeat(30));
    
    const measurementResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameters: { x1: 50, x2: 60 },
        target_values: [
          {"name": "Target 1", "value": 75},
          {"name": "Target 2", "value": 25}
        ]
      })
    });
    
    if (measurementResponse.ok) {
      console.log(`✅ Measurement added`);
    } else {
      const errorText = await measurementResponse.text();
      console.log(`❌ Measurement failed: ${measurementResponse.status} - ${errorText}`);
    }
    
    console.log(`\n📋 STEP 4: Try parameter bounds update with detailed logging`);
    console.log('='.repeat(60));
    
    // First, let's try a preview to see what happens
    const previewResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameter_bounds: { "x1": [0, 80] },
        preview_only: true
      })
    });
    
    console.log(`📥 Preview response: ${previewResponse.status} ${previewResponse.statusText}`);
    
    if (previewResponse.ok) {
      const previewData = await previewResponse.json();
      console.log(`✅ Preview successful`);
      console.log(`📊 Preview data keys: ${Object.keys(previewData)}`);
    } else {
      const errorText = await previewResponse.text();
      console.log(`❌ Preview failed: ${errorText}`);
      
      // Let's check if the optimization still exists
      console.log(`\n🔍 Checking if optimization still exists...`);
      
      const checkResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/info`);
      console.log(`   Info check: ${checkResponse.status} ${checkResponse.statusText}`);
      
      if (!checkResponse.ok) {
        const checkError = await checkResponse.text();
        console.log(`   Error: ${checkError}`);
      }
    }
    
    console.log(`\n📋 STEP 5: Try actual update (non-preview)`);
    console.log('='.repeat(45));
    
    const updateResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameter_bounds: { "x1": [0, 80] },
        preview_only: false
      })
    });
    
    console.log(`📥 Update response: ${updateResponse.status} ${updateResponse.statusText}`);
    
    if (updateResponse.ok) {
      const updateData = await updateResponse.json();
      console.log(`✅ Update successful`);
      console.log(`📊 Update data keys: ${Object.keys(updateData)}`);
    } else {
      const errorText = await updateResponse.text();
      console.log(`❌ Update failed: ${errorText}`);
    }
    
    console.log(`\n🎉 PARETO DEBUG COMPLETED`);
    console.log(`   Optimization ID: ${optimizerId}`);
    
  } catch (error) {
    console.error('❌ Error during Pareto debug:', error.message);
  }
}

// Run the debug
debugParetoTargetConfig().catch(console.error);
