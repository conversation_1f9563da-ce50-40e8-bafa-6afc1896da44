#!/usr/bin/env node

/**
 * This script checks the current database schema
 * Run with: node scripts/check-db-schema.js
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const { Client } = require('pg');

// Get the database URL from environment variables
let databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Modify the connection string to disable SSL verification
databaseUrl = databaseUrl.replace('sslmode=require', 'sslmode=no-verify');
console.log(`🔍 Using database: ${databaseUrl.replace(/:[^:]*@/, ':****@')}`);

// Create a client with SSL disabled
const client = new Client({
  connectionString: databaseUrl,
  ssl: {
    rejectUnauthorized: false
  }
});

async function checkSchema() {
  try {
    // Connect to the database
    await client.connect();
    console.log('✅ Connected to database');
    
    // Get all tables in the public schema
    const tablesResult = await client.query(`
      SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    `);
    
    const tables = tablesResult.rows.map(row => row.tablename);
    console.log(`\nFound ${tables.length} tables: ${tables.join(', ')}`);
    
    // Check each table's structure
    for (const table of tables) {
      console.log(`\n📋 Table: ${table}`);
      
      // Get columns for this table
      const columnsResult = await client.query(`
        SELECT 
          column_name, 
          data_type, 
          character_maximum_length,
          column_default,
          is_nullable
        FROM 
          information_schema.columns
        WHERE 
          table_schema = 'public' AND 
          table_name = $1
        ORDER BY 
          ordinal_position
      `, [table]);
      
      console.log('Columns:');
      columnsResult.rows.forEach(column => {
        const nullable = column.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
        const defaultValue = column.column_default ? `DEFAULT ${column.column_default}` : '';
        const length = column.character_maximum_length ? `(${column.character_maximum_length})` : '';
        console.log(`  - ${column.column_name}: ${column.data_type}${length} ${nullable} ${defaultValue}`);
      });
      
      // Get primary key
      const pkResult = await client.query(`
        SELECT 
          kcu.column_name
        FROM 
          information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
        WHERE 
          tc.constraint_type = 'PRIMARY KEY' AND
          tc.table_schema = 'public' AND
          tc.table_name = $1
      `, [table]);
      
      if (pkResult.rows.length > 0) {
        const pkColumns = pkResult.rows.map(row => row.column_name);
        console.log(`Primary Key: ${pkColumns.join(', ')}`);
      } else {
        console.log('No Primary Key defined');
      }
      
      // Check for trial-related columns in profiles table
      if (table === 'profiles') {
        const trialColumns = columnsResult.rows.filter(col => 
          ['trial_started_at', 'trial_ends_at', 'has_trial_expired'].includes(col.column_name)
        );
        
        if (trialColumns.length === 3) {
          console.log('\n✅ All trial-related columns are present in the profiles table');
        } else {
          console.log('\n❌ Missing trial-related columns in the profiles table:');
          const missingColumns = ['trial_started_at', 'trial_ends_at', 'has_trial_expired']
            .filter(col => !trialColumns.map(c => c.column_name).includes(col));
          
          missingColumns.forEach(col => console.log(`  - ${col}`));
        }
      }
    }
    
    // Close the connection
    await client.end();
    console.log('\n✅ Database connection closed');
    
  } catch (error) {
    console.error('❌ Error checking schema:', error);
    
    // Try to close the connection
    try {
      await client.end();
    } catch (closeError) {
      console.error('Error closing database connection:', closeError);
    }
    
    process.exit(1);
  }
}

// Start the process
checkSchema();
