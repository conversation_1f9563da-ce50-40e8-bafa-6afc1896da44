/*
Token service for generating and validating verification tokens.
*/

import { randomBytes } from "crypto"

/**
 * Generate a random token for email verification
 */
export function generateVerificationToken(): string {
  // Generate a random 32-byte hex string
  return randomBytes(32).toString("hex")
}

/**
 * Generate an expiry date for a token (24 hours from now)
 */
export function generateTokenExpiry(): Date {
  const expiry = new Date()
  expiry.setHours(expiry.getHours() + 24) // Token expires in 24 hours
  return expiry
}

/**
 * Check if a token has expired
 */
export function isTokenExpired(expiryDate: Date): boolean {
  return new Date() > expiryDate
}
