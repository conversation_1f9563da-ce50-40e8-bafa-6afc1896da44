# BOapp-frontend Comprehensive Codebase Index

## Project Overview
**INNOptimizer™** is a sophisticated Bayesian Optimization platform that provides intelligent experimental design and parameter optimization capabilities. The frontend is built with Next.js and provides a comprehensive web interface for managing optimization experiments, visualizing results, and analyzing data.

## Technology Stack

### Core Framework
- **Framework**: Next.js 15.x with App Router
- **Language**: TypeScript
- **Runtime**: Node.js
- **Package Manager**: npm

### UI & Styling
- **UI Components**: Radix UI primitives
- **Styling**: Tailwind CSS with custom configuration
- **Component Library**: Shadcn/ui components
- **Icons**: Lucide React
- **Animations**:
  - Framer Motion for page transitions
  - GSAP for complex animations
  - Tailwind CSS animations

### State Management & Forms
- **State Management**: React Hooks (useState, useEffect, useContext)
- **Form Handling**: React Hook Form
- **Validation**: Zod schema validation
- **Context**: React Context API for global state

### Authentication & Security
- **Authentication**: Clerk (email/password, social login)
- **Session Management**: Clerk session handling
- **User Management**: Clerk user profiles
- **Route Protection**: Middleware-based authentication

### Database & ORM
- **Database**: PostgreSQL
- **ORM**: Drizzle ORM
- **Migrations**: Drizzle Kit
- **Connection**: postgres.js driver

### Data Visualization
- **2D Plotting**: Plotly.js and React-Plotly.js
- **Charts**: Recharts for dashboard metrics
- **3D Visualization**: Three.js with React Three Fiber
- **Molecular Visualization**: 3dmol.js
- **Scientific Plotting**: Custom Plotly configurations

### File Handling & Export
- **Excel Files**: XLSX library
- **File Downloads**: File-saver
- **Data Export**: Custom export utilities

### Development Tools
- **Linting**: ESLint with Next.js config
- **Formatting**: Prettier
- **Type Checking**: TypeScript compiler
- **Git Hooks**: Husky for pre-commit hooks

## Directory Structure

### Root Level Files
```
BOapp-frontend/
├── package.json              # Dependencies and scripts
├── tsconfig.json            # TypeScript configuration
├── tailwind.config.ts       # Tailwind CSS configuration
├── next.config.mjs          # Next.js configuration
├── drizzle.config.ts        # Database ORM configuration
├── middleware.ts            # Next.js middleware for auth
├── prettier.config.cjs      # Code formatting rules
├── postcss.config.mjs       # PostCSS configuration
└── environment.yml          # Conda environment (if used)
```

### Core Directories

#### `/actions/` - Server Actions
Server-side functions for data operations:
- `optimization-actions.ts` - Core optimization API calls
- `optimization-workflow-actions.ts` - Workflow orchestration
- `sample-workflow-actions.ts` - Sample management
- `suggestion-workflow-actions.ts` - Suggestion handling
- `db/` - Database operations
  - `optimizations-actions.ts` - Optimization CRUD
  - `samples-actions.ts` - Sample data management
  - `suggestions-actions.ts` - Suggestion storage
  - `profiles-actions.ts` - User profile management

#### `/app/` - Next.js App Router
Main application structure:
- `layout.tsx` - Root layout with providers
- `globals.css` - Global styles
- `(auth)/` - Authentication pages
  - `login/` - Login page
  - `signup/` - Registration page
- `(marketing)/` - Public pages
  - `page.tsx` - Landing page
  - `features/` - Feature showcase
  - `pricing/` - Pricing information
- `dashboard/` - Protected application
  - `layout.tsx` - Dashboard layout with sidebar
  - `home/` - Dashboard overview
  - `optimizations/` - Optimization management
  - `experiments/` - Experiment tracking
  - `analytics/` - Data analysis
  - `history/` - Experiment history
- `api/` - API routes
  - `health/` - Health checks
  - `optimizations/` - Optimization endpoints
  - `proxy/` - Backend proxy routes
  - `stripe/` - Payment processing
  - `webhooks/` - External webhooks

#### `/components/` - Reusable Components
Organized by feature and type:
- `ui/` - Base UI components (Shadcn/ui)
  - `button.tsx`, `card.tsx`, `dialog.tsx`, etc.
- `dashboard/` - Dashboard-specific components
  - `dashboard-sidebar.tsx` - Navigation sidebar
  - `optimization-card.tsx` - Optimization display
  - `subscription-info.tsx` - User subscription status
- `optimization/` - Optimization features
  - `create-optimization-form.tsx` - Optimization creation
  - `optimization-results.tsx` - Results display
  - `plotly-surface.tsx` - 3D surface plots
  - `parameter-impact-chart.tsx` - Parameter analysis
- `landing/` - Marketing page components
  - `hero.tsx` - Hero section
  - `feature-showcase.tsx` - Feature highlights
  - `bayesian-background.tsx` - Animated background
- `feedback/` - User feedback system
- `tutorials/` - Educational components

#### `/db/` - Database Schema
Database structure and utilities:
- `db.ts` - Database connection setup
- `schema/` - Table definitions
  - `optimizations-schema.ts` - Optimization tables
  - `profiles-schema.ts` - User profiles
  - `samples-schema.ts` - Experiment samples
  - `suggestions-schema.ts` - AI suggestions
  - `feedback-schema.ts` - User feedback
- `migrations/` - Database migrations

#### `/lib/` - Utility Libraries
Shared utilities and configurations:
- `utils.ts` - Common utility functions
- `constants.ts` - Application constants
- `api/` - API client utilities
  - `baybe-client.ts` - Backend API client
  - `loading-fetch.ts` - Loading state management
- `hooks/` - Custom React hooks
  - `use-api-loading.ts` - API loading states
  - `use-copy-to-clipboard.tsx` - Clipboard utilities

#### `/types/` - TypeScript Definitions
Type definitions for the application:
- `index.ts` - Common types
- `optimization-types.ts` - Optimization-specific types
- `sample-types.ts` - Sample data types
- `server-action-types.ts` - Server action types

## Key Features & Functionality

### 1. Authentication System
**Technology**: Clerk Authentication
- **Email/Password Login**: Standard authentication
- **Social Login**: Google, GitHub integration
- **User Profiles**: Automatic profile creation
- **Session Management**: Secure session handling
- **Route Protection**: Middleware-based protection
- **Trial Management**: Academic and regular user trials

### 2. Dashboard Interface
**Main Hub**: `/dashboard/home`
- **Overview Cards**: Total experiments, active optimizations, efficiency scores
- **Recent Experiments**: List of recent optimization runs
- **Performance Metrics**: Visual performance indicators
- **Quick Actions**: Navigation to key features

### 3. Optimization Management
**Core Feature**: Bayesian Optimization
- **Creation Wizard**: `/dashboard/optimizations/create`
  - Parameter definition (numerical, categorical)
  - Target configuration (single/multi-objective)
  - Constraint specification
- **Experiment Tracking**: Real-time optimization progress
- **Results Visualization**:
  - 3D surface plots
  - Parameter impact analysis
  - Convergence tracking
- **Data Export**: Excel and CSV export capabilities

### 4. Visualization Engine
**Multiple Visualization Types**:
- **Plotly.js Integration**: Interactive 2D/3D plots
- **Surface Analysis**: 3D parameter space visualization
- **Parameter Impact**: SHAP-based feature importance
- **Convergence Plots**: Optimization progress tracking
- **Multi-objective Plots**: Pareto frontier visualization

### 5. Data Management
**Sample & Suggestion Handling**:
- **Sample Generation**: Latin Hypercube Sampling (LHS)
- **Measurement Recording**: Experiment results storage
- **Batch Processing**: Multiple measurement handling
- **Data Validation**: Input validation and sanitization
- **Export Capabilities**: Multiple format support

### 6. API Integration
**Backend Communication**:
- **BayBE API Client**: Direct integration with optimization backend
- **Health Monitoring**: API status checking
- **Error Handling**: Comprehensive error management
- **Loading States**: User feedback during operations
- **Retry Logic**: Automatic retry for failed requests

### 7. User Experience Features
**Enhanced UX**:
- **Loading Overlays**: Visual feedback during operations
- **Toast Notifications**: Success/error messaging
- **Responsive Design**: Mobile and desktop optimization
- **Dark/Light Mode**: Theme switching capability
- **Feedback System**: User feedback collection

## Database Schema

### Core Tables
1. **optimizations** - Optimization metadata
   - `id`, `userId`, `name`, `description`
   - `optimizerId`, `config`, `targetName`, `targetMode`
   - `status`, `createdAt`, `updatedAt`

2. **measurements** - Experiment results
   - `id`, `optimizationId`, `parameters`
   - `targetValue`, `targetValues`, `isRecommended`
   - `batchId`, `createdAt`, `updatedAt`

3. **profiles** - User profiles
   - User information and preferences

4. **samples** - Generated samples
   - Parameter combinations for experiments

5. **suggestions** - AI-generated suggestions
   - Optimization recommendations

## API Architecture

### Internal API Routes (`/api/`)
- **Health Checks**: `/api/health/database`
- **Optimization Management**: `/api/optimizations/[optimizer_id]`
- **Backend Proxy**: `/api/proxy/health`, `/api/proxy/feature-importance`
- **Payment Processing**: `/api/stripe/checkout`
- **Webhooks**: `/api/webhooks/clerk`

### External API Integration
- **BayBE Backend**: FastAPI-based optimization engine
- **Clerk API**: Authentication and user management
- **Stripe API**: Payment processing
- **Resend API**: Email notifications

## Configuration Files

### Next.js Configuration (`next.config.mjs`)
- Webpack optimizations
- Image optimization settings
- Environment variable handling
- Build optimizations

### Tailwind Configuration (`tailwind.config.ts`)
- Custom color schemes
- Component styling
- Animation configurations
- Responsive breakpoints

### Database Configuration (`drizzle.config.ts`)
- PostgreSQL connection settings
- Migration configurations
- Schema generation settings

### TypeScript Configuration (`tsconfig.json`)
- Strict type checking
- Path aliases
- Module resolution
- Build targets

## Development Workflow

### Scripts (`package.json`)
```bash
npm run dev          # Development server
npm run build        # Production build
npm run start        # Production server
npm run lint         # Code linting
npm run type-check   # TypeScript checking
npm run db:generate  # Generate database schema
npm run db:migrate   # Run database migrations
npm run db:push      # Push schema changes
```

### Testing Strategy
- **Unit Tests**: Component testing
- **Integration Tests**: API endpoint testing
- **E2E Tests**: User workflow testing
- **Performance Tests**: Load and stress testing

## Deployment & Production

### Environment Variables
Required environment variables:
- `DATABASE_URL` - PostgreSQL connection
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` - Clerk public key
- `CLERK_SECRET_KEY` - Clerk private key
- `STRIPE_SECRET_KEY` - Stripe integration
- `BAYBE_API_URL` - Backend API URL

### Build Process
1. TypeScript compilation
2. Next.js optimization
3. Asset bundling
4. Database migration
5. Environment validation

### Performance Optimizations
- **Code Splitting**: Automatic route-based splitting
- **Image Optimization**: Next.js image optimization
- **Caching**: API response caching
- **Lazy Loading**: Component lazy loading
- **Bundle Analysis**: Webpack bundle analyzer

## Security Considerations

### Authentication Security
- **JWT Tokens**: Secure token handling
- **Session Management**: Automatic session refresh
- **Route Protection**: Middleware-based protection
- **CSRF Protection**: Built-in CSRF protection

### Data Security
- **Input Validation**: Zod schema validation
- **SQL Injection Prevention**: ORM-based queries
- **XSS Protection**: React built-in protection
- **Environment Variables**: Secure secret management

## Integration with Backend

### API Communication
- **RESTful APIs**: Standard HTTP methods
- **JSON Payloads**: Structured data exchange
- **Error Handling**: Standardized error responses
- **Authentication**: Token-based authentication

### Data Flow
1. **User Input** → Frontend validation
2. **API Request** → Backend processing
3. **Database Storage** → Data persistence
4. **Response** → Frontend display
5. **Visualization** → User interface update

## Maintenance & Monitoring

### Health Checks
- **Database Health**: Connection monitoring
- **API Health**: Backend availability
- **Performance Metrics**: Response time tracking
- **Error Monitoring**: Error rate tracking

### Logging & Debugging
- **Console Logging**: Development debugging
- **Error Boundaries**: React error handling
- **Performance Profiling**: React DevTools
- **Network Monitoring**: API request tracking

This comprehensive index provides a complete overview of the BOapp-frontend codebase, its architecture, features, and operational aspects.
