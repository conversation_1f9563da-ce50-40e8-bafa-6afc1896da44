"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import { useState } from "react"

interface ValidationResult {
  valid: boolean
  error?: string
  message?: string
}

interface ConstraintValidationResult extends ValidationResult {
  constraintId: string
}

interface GlobalValidationResult {
  valid: boolean
  errors: ConstraintValidationResult[]
  warnings: string[]
  summary: string
}

interface ConstraintValidationFeedbackProps {
  globalValidation?: GlobalValidationResult
  isValidating: boolean
  constraintCount: number
  onRevalidate?: () => void
  className?: string
}

export function ConstraintValidationFeedback({
  globalValidation,
  isValidating,
  constraintCount,
  onRevalidate,
  className
}: ConstraintValidationFeedbackProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (constraintCount === 0) {
    return null
  }

  if (isValidating) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-muted-foreground flex items-center gap-2">
            <Loader2 className="size-4 animate-spin" />
            <span className="text-sm">Validating constraints...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!globalValidation) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="text-muted-foreground flex items-center gap-2">
              <AlertCircle className="size-4" />
              <span className="text-sm">Constraint validation pending</span>
            </div>
            {onRevalidate && (
              <Button variant="outline" size="sm" onClick={onRevalidate}>
                Validate
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  const { valid, errors, warnings, summary } = globalValidation
  const hasIssues = errors.length > 0 || warnings.length > 0

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-base">
          <div className="flex items-center gap-2">
            {valid ? (
              <CheckCircle className="size-4 text-green-600" />
            ) : (
              <AlertCircle className="size-4 text-red-600" />
            )}
            <span>Constraint Validation</span>
            <Badge
              variant={valid ? "default" : "destructive"}
              className="text-xs"
            >
              {valid ? "Valid" : "Issues Found"}
            </Badge>
          </div>
          {onRevalidate && (
            <Button variant="ghost" size="sm" onClick={onRevalidate}>
              Revalidate
            </Button>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Summary */}
        <Alert variant={valid ? "default" : "destructive"}>
          <AlertDescription className="text-sm">{summary}</AlertDescription>
        </Alert>

        {/* Detailed Issues */}
        {hasIssues && (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-between"
              >
                <span>
                  View Details ({errors.length} error
                  {errors.length !== 1 ? "s" : ""}, {warnings.length} warning
                  {warnings.length !== 1 ? "s" : ""})
                </span>
                <AlertTriangle className="size-4" />
              </Button>
            </CollapsibleTrigger>

            <CollapsibleContent className="mt-3 space-y-3">
              {/* Errors */}
              {errors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="flex items-center gap-1 text-sm font-medium text-red-700">
                    <AlertCircle className="size-3" />
                    Validation Errors
                  </h4>
                  {errors.map((error, index) => (
                    <Alert key={index} variant="destructive" className="py-2">
                      <AlertDescription className="text-xs">
                        <strong>Constraint {error.constraintId}:</strong>{" "}
                        {error.error}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              )}

              {/* Warnings */}
              {warnings.length > 0 && (
                <div className="space-y-2">
                  <h4 className="flex items-center gap-1 text-sm font-medium text-amber-700">
                    <AlertTriangle className="size-3" />
                    Compatibility Warnings
                  </h4>
                  {warnings.map((warning, index) => (
                    <Alert
                      key={index}
                      className="border-amber-200 bg-amber-50 py-2"
                    >
                      <AlertDescription className="text-xs text-amber-800">
                        {warning}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* Success State */}
        {valid && !hasIssues && (
          <div className="py-2 text-center">
            <div className="flex items-center justify-center gap-2 text-green-700">
              <CheckCircle className="size-4" />
              <span className="text-sm font-medium">
                All constraints are valid and compatible
              </span>
            </div>
            <p className="text-muted-foreground mt-1 text-xs">
              Your optimization is ready to run with these constraints.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface SingleConstraintValidationProps {
  constraintId: string
  validation?: ValidationResult
  isValidating: boolean
  constraintName?: string
}

export function SingleConstraintValidation({
  constraintId,
  validation,
  isValidating,
  constraintName
}: SingleConstraintValidationProps) {
  if (isValidating) {
    return (
      <div className="text-muted-foreground flex items-center gap-1">
        <Loader2 className="size-3 animate-spin" />
        <span className="text-xs">Validating...</span>
      </div>
    )
  }

  if (!validation) {
    return (
      <div className="text-muted-foreground flex items-center gap-1">
        <AlertCircle className="size-3" />
        <span className="text-xs">Not validated</span>
      </div>
    )
  }

  if (validation.valid) {
    return (
      <div className="flex items-center gap-1 text-green-600">
        <CheckCircle className="size-3" />
        <span className="text-xs">Valid</span>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-1 text-red-600">
      <AlertCircle className="size-3" />
      <span className="text-xs" title={validation.error}>
        Invalid
      </span>
    </div>
  )
}
