#!/usr/bin/env node

/**
 * Real-time backend log monitoring for Bayesian optimization lifecycle
 * This will monitor the backend logs and show detailed lifecycle information
 */

const { spawn } = require('child_process');
const path = require('path');

function monitorBackendLogs() {
  console.log('🔍 MONITORING BACKEND LOGS FOR BAYESIAN OPTIMIZATION');
  console.log('====================================================');
  console.log('Watching for lifecycle logging from the backend...');
  console.log('');
  console.log('📋 What to look for:');
  console.log('  🚀 OPTIMIZATION CREATION STARTED');
  console.log('  📊 MEASUREMENT ADDITION STARTED');
  console.log('  💾 LOADING CAMPAIGN FROM DISK');
  console.log('  ✅ BAYESIAN SUGGESTIONS GENERATED SUCCESSFULLY');
  console.log('');
  console.log('Now go to the UI and click "Get Suggestions" to see the detailed logging...');
  console.log('');
  console.log('=== BACKEND LOG OUTPUT ===');
  
  // Try to find the backend log file or process
  const backendPath = path.join(__dirname, '../../BOapp-backend-user');
  
  // Try to tail the log file if it exists
  const logPaths = [
    path.join(backendPath, 'logs/app.log'),
    path.join(backendPath, 'app.log'),
    path.join(backendPath, 'baybe_api.log'),
    '/tmp/baybe_api.log',
    '/var/log/baybe_api.log'
  ];
  
  let logFound = false;
  
  for (const logPath of logPaths) {
    try {
      const fs = require('fs');
      if (fs.existsSync(logPath)) {
        console.log(`📄 Found log file: ${logPath}`);
        console.log('');
        
        // Tail the log file
        const tail = spawn('tail', ['-f', logPath]);
        
        tail.stdout.on('data', (data) => {
          const lines = data.toString().split('\n');
          lines.forEach(line => {
            if (line.trim()) {
              // Highlight important lifecycle events
              if (line.includes('🚀 OPTIMIZATION CREATION STARTED') ||
                  line.includes('📊 MEASUREMENT ADDITION STARTED') ||
                  line.includes('💾 LOADING CAMPAIGN FROM DISK') ||
                  line.includes('✅ BAYESIAN SUGGESTIONS GENERATED SUCCESSFULLY') ||
                  line.includes('OPTIMIZATION LIFECYCLE LOGGING')) {
                console.log(`\n🔥 ${line}`);
              } else if (line.includes('INFO') && 
                        (line.includes('Optimizer ID') || 
                         line.includes('Parameters') ||
                         line.includes('Target') ||
                         line.includes('measurements') ||
                         line.includes('suggestions'))) {
                console.log(`📋 ${line}`);
              } else if (line.includes('ERROR') || line.includes('WARNING')) {
                console.log(`⚠️  ${line}`);
              } else {
                console.log(line);
              }
            }
          });
        });
        
        tail.stderr.on('data', (data) => {
          console.error(`❌ Log error: ${data}`);
        });
        
        tail.on('close', (code) => {
          console.log(`📄 Log monitoring stopped with code ${code}`);
        });
        
        logFound = true;
        break;
      }
    } catch (error) {
      // Continue to next log path
    }
  }
  
  if (!logFound) {
    console.log('📄 No log file found, trying to monitor backend process output...');
    console.log('');
    
    // Try to find running backend process
    const ps = spawn('ps', ['aux']);
    let psOutput = '';
    
    ps.stdout.on('data', (data) => {
      psOutput += data.toString();
    });
    
    ps.on('close', () => {
      const lines = psOutput.split('\n');
      const backendProcesses = lines.filter(line => 
        line.includes('python') && 
        (line.includes('main.py') || line.includes('uvicorn') || line.includes('baybe'))
      );
      
      if (backendProcesses.length > 0) {
        console.log('🔍 Found backend processes:');
        backendProcesses.forEach((process, index) => {
          console.log(`  ${index + 1}. ${process.trim()}`);
        });
        console.log('');
      }
      
      console.log('💡 To see backend logs, you can:');
      console.log('   1. Check if the backend is running with logs to console');
      console.log('   2. Start the backend with: cd BOapp-backend-user && python -m uvicorn baybe_api.main:app --reload --host 0.0.0.0 --port 8000');
      console.log('   3. Or check Docker logs if running in container');
      console.log('');
      console.log('🎯 For now, trigger "Get Suggestions" in the UI and watch for any console output');
      console.log('');
      
      // Monitor for any new log files being created
      console.log('📋 Monitoring for new log files...');
      const fs = require('fs');
      const chokidar = require('chokidar');
      
      try {
        const watcher = chokidar.watch([backendPath, '/tmp', '/var/log'], {
          ignored: /[\/\\]\./,
          persistent: true
        });
        
        watcher.on('add', (filePath) => {
          if (filePath.includes('log') && 
              (filePath.includes('baybe') || filePath.includes('app'))) {
            console.log(`📄 New log file detected: ${filePath}`);
            console.log('   Rerun this script to monitor it');
          }
        });
        
        // Keep the process alive
        setInterval(() => {
          // Just keep alive
        }, 1000);
        
      } catch (error) {
        console.log('📋 File watching not available, monitoring manually...');
        
        // Fallback: just keep the process alive and show instructions
        setInterval(() => {
          // Show periodic reminders
        }, 30000);
      }
    });
  }
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n\n🛑 Stopping log monitoring...');
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n\n🛑 Stopping log monitoring...');
    process.exit(0);
  });
}

// Start monitoring
monitorBackendLogs();
