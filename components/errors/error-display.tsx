"use client"

import React from "react"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import {
  AlertTriangle,
  XCircle,
  Info,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Zap
} from "lucide-react"
import { StructuredError, ErrorSeverity } from "@/lib/errors/types"

interface ErrorDisplayProps {
  error: StructuredError
  onQuickFix?: (quickFixId: string, error: StructuredError) => void
  onDismiss?: (errorId: string) => void
  showTechnicalDetails?: boolean
  compact?: boolean
}

export function ErrorDisplay({
  error,
  onQuickFix,
  onDismiss,
  showTechnicalDetails = false,
  compact = false
}: ErrorDisplayProps) {
  const [isExpanded, setIsExpanded] = React.useState(!compact)

  const getSeverityIcon = (severity: ErrorSeverity) => {
    switch (severity) {
      case "error":
        return <XCircle className="size-4" />
      case "warning":
        return <AlertTriangle className="size-4" />
      case "info":
        return <Info className="size-4" />
    }
  }

  const getSeverityVariant = (severity: ErrorSeverity) => {
    switch (severity) {
      case "error":
        return "destructive"
      case "warning":
        return "default"
      case "info":
        return "default"
    }
  }

  const handleQuickFix = (quickFixId: string) => {
    if (onQuickFix) {
      onQuickFix(quickFixId, error)
    }
  }

  return (
    <Alert variant={getSeverityVariant(error.severity)} className="mb-2">
      <div className="flex items-start gap-2">
        {getSeverityIcon(error.severity)}
        <div className="min-w-0 flex-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertTitle className="text-sm font-medium">
                {error.message}
              </AlertTitle>
              <Badge variant="outline" className="text-xs">
                {error.code}
              </Badge>
              {error.field && (
                <Badge variant="secondary" className="text-xs">
                  {error.field}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-1">
              {!compact && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="size-6 p-0"
                >
                  {isExpanded ? (
                    <ChevronDown className="size-3" />
                  ) : (
                    <ChevronRight className="size-3" />
                  )}
                </Button>
              )}
              {onDismiss && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDismiss(error.id)}
                  className="size-6 p-0"
                >
                  <XCircle className="size-3" />
                </Button>
              )}
            </div>
          </div>

          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleContent className="mt-2 space-y-3">
              {/* Resolution steps */}
              {error.resolution && (
                <div>
                  <h4 className="mb-1 text-sm font-medium">How to fix this:</h4>
                  <p className="text-muted-foreground mb-2 text-sm">
                    {error.resolution.description}
                  </p>
                  {error.resolution.steps &&
                    error.resolution.steps.length > 0 && (
                      <ol className="text-muted-foreground list-inside list-decimal space-y-1 text-sm">
                        {error.resolution.steps.map((step, index) => (
                          <li key={index}>{step}</li>
                        ))}
                      </ol>
                    )}
                </div>
              )}

              {/* Quick fixes */}
              {error.resolution?.quickFixes &&
                error.resolution.quickFixes.length > 0 && (
                  <div>
                    <h4 className="mb-2 text-sm font-medium">Quick fixes:</h4>
                    <div className="flex flex-wrap gap-2">
                      {error.resolution.quickFixes.map(quickFix => (
                        <Button
                          key={quickFix.id}
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuickFix(quickFix.id)}
                          className="h-7 text-xs"
                        >
                          <Zap className="mr-1 size-3" />
                          {quickFix.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

              {/* Documentation link */}
              {error.resolution?.documentationUrl && (
                <div>
                  <Button
                    variant="link"
                    size="sm"
                    asChild
                    className="h-auto p-0 text-xs"
                  >
                    <a
                      href={error.resolution.documentationUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="mr-1 size-3" />
                      View documentation
                    </a>
                  </Button>
                </div>
              )}

              {/* Technical details */}
              {showTechnicalDetails && error.technicalMessage && (
                <div>
                  <h4 className="mb-1 text-sm font-medium">
                    Technical details:
                  </h4>
                  <p className="text-muted-foreground bg-muted rounded p-2 font-mono text-xs">
                    {error.technicalMessage}
                  </p>
                </div>
              )}

              {/* Error context */}
              {showTechnicalDetails && error.context && (
                <div>
                  <h4 className="mb-1 text-sm font-medium">Context:</h4>
                  <pre className="text-muted-foreground bg-muted overflow-x-auto rounded p-2 font-mono text-xs">
                    {JSON.stringify(error.context, null, 2)}
                  </pre>
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        </div>
      </div>
    </Alert>
  )
}

interface ErrorListProps {
  errors: StructuredError[]
  onQuickFix?: (quickFixId: string, error: StructuredError) => void
  onDismiss?: (errorId: string) => void
  onDismissAll?: () => void
  showTechnicalDetails?: boolean
  compact?: boolean
  maxHeight?: string
}

export function ErrorList({
  errors,
  onQuickFix,
  onDismiss,
  onDismissAll,
  showTechnicalDetails = false,
  compact = false,
  maxHeight = "400px"
}: ErrorListProps) {
  if (errors.length === 0) {
    return null
  }

  const errorCount = errors.filter(e => e.severity === "error").length
  const warningCount = errors.filter(e => e.severity === "warning").length

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-medium">
            Issues Found ({errors.length})
          </h3>
          {errorCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {errorCount} error{errorCount !== 1 ? "s" : ""}
            </Badge>
          )}
          {warningCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {warningCount} warning{warningCount !== 1 ? "s" : ""}
            </Badge>
          )}
        </div>
        {onDismissAll && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismissAll}
            className="text-xs"
          >
            Dismiss All
          </Button>
        )}
      </div>

      <div className="space-y-2 overflow-y-auto" style={{ maxHeight }}>
        {errors.map(error => (
          <ErrorDisplay
            key={error.id}
            error={error}
            onQuickFix={onQuickFix}
            onDismiss={onDismiss}
            showTechnicalDetails={showTechnicalDetails}
            compact={compact}
          />
        ))}
      </div>
    </div>
  )
}
