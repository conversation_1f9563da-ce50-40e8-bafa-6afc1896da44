/*
This client component provides a simplified footer for the app with a brand watermark.
*/

"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { BRAND } from "@/lib/constants"

export function Footer() {
  const [windowWidth, setWindowWidth] = useState(0)

  // Update window width on client side
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    // Set initial width
    handleResize()

    // Add event listener
    window.addEventListener("resize", handleResize)

    // Clean up
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Calculate appropriate dimensions for the brand name
  const getMinFontSize = () => {
    // Use smaller minimum font size on mobile devices
    return windowWidth < 768 ? 40 : 70
  }
  const fontSize = Math.max(windowWidth * 0.1, getMinFontSize())
  const footerHeight = Math.max(fontSize * 1.5, 140) // Taller footer to accommodate the text

  return (
    <footer
      className="relative flex flex-col overflow-hidden border-t"
      style={{ minHeight: `${footerHeight}px` }}
    >
      {/* Brand name watermark */}
      <div className="pointer-events-none absolute inset-0 flex items-center justify-center overflow-hidden">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.03 }}
          transition={{ duration: 1 }}
          className="text-primary whitespace-nowrap px-4 font-bold"
          style={{
            fontSize: `${fontSize}px`,
            width: "100%",
            textAlign: "center",
            letterSpacing: "0.05em" // Add slight letter spacing for better readability
          }}
        >
          {BRAND.NAME}
        </motion.div>
      </div>

      {/* Empty flex spacer to push copyright to bottom */}
      <div className="grow"></div>

      {/* Footer links and copyright */}
      <div className="relative z-10 py-4">
        <div className="mb-4 flex flex-wrap justify-center gap-6 text-sm">
          <Link
            href="/privacy-policy"
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            Privacy Policy
          </Link>
          <Link
            href="/security"
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            Security
          </Link>
          <Link
            href="/data-processing"
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            Data Processing
          </Link>
          <Link
            href="/contact"
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            Contact
          </Link>
        </div>
        <div className="text-muted-foreground text-center">
          <p>Copyright &copy; {new Date().getFullYear()} SynSilico</p>
        </div>
      </div>
    </footer>
  )
}
