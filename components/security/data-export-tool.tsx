"use client"

import { motion } from "framer-motion"
import {
  Download,
  FileText,
  Database,
  Settings,
  CheckCircle,
  Clock,
  Shield
} from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useState } from "react"

const exportOptions = [
  {
    icon: Database,
    title: "Complete Data Export",
    description:
      "Export all your data including experiments, results, and account information",
    includes: [
      "All optimization experiments and parameters",
      "Measurement results and optimization history",
      "Account information and preferences",
      "Usage analytics and metadata"
    ],
    format: "ZIP archive with CSV and JSON files",
    size: "Varies (typically 1-50 MB)",
    time: "5-30 minutes depending on data volume"
  },
  {
    icon: FileText,
    title: "Experiment Data Only",
    description: "Export specific experiments or optimization campaigns",
    includes: [
      "Selected experiment parameters",
      "Measurement results and suggestions",
      "Optimization objectives and constraints",
      "Model performance metrics"
    ],
    format: "CSV or JSON format",
    size: "Small (typically < 5 MB)",
    time: "Immediate download"
  },
  {
    icon: Settings,
    title: "Account Information",
    description: "Export account data and preferences only",
    includes: [
      "Profile information and settings",
      "Subscription and billing data",
      "Privacy preferences",
      "Account activity summary"
    ],
    format: "JSON format",
    size: "Very small (< 1 MB)",
    time: "Immediate download"
  }
]

const exportFormats = [
  {
    format: "CSV",
    description: "Comma-separated values for spreadsheet applications",
    bestFor: "Data analysis, Excel, Google Sheets",
    compatibility: "Universal compatibility"
  },
  {
    format: "JSON",
    description: "JavaScript Object Notation for programmatic access",
    bestFor: "API integration, custom applications",
    compatibility: "Developer-friendly format"
  },
  {
    format: "ZIP",
    description: "Compressed archive containing multiple file formats",
    bestFor: "Complete data backup and transfer",
    compatibility: "All operating systems"
  }
]

const exportSteps = [
  "Select the type of data you want to export",
  "Choose your preferred file format",
  "Verify your identity for security",
  "Initiate the export process",
  "Receive download link via email",
  "Download your data securely"
]

export function DataExportTool() {
  const [selectedExport, setSelectedExport] = useState<string | null>(null)

  return (
    <section className="bg-gray-50 py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Data Export Tool
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            Export your data in standard formats for backup, analysis, or
            transfer to other platforms. Your data portability is guaranteed
            under GDPR and other privacy regulations.
          </p>
        </motion.div>

        <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-3">
          {exportOptions.map((option, index) => (
            <motion.div
              key={option.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card
                className={`h-full cursor-pointer transition-all hover:shadow-lg ${
                  selectedExport === option.title
                    ? "bg-blue-50 ring-2 ring-blue-500"
                    : ""
                }`}
                onClick={() => setSelectedExport(option.title)}
              >
                <CardHeader>
                  <div className="mb-4 flex size-12 items-center justify-center rounded-lg bg-blue-100">
                    <option.icon className="size-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl">{option.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {option.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="mb-2 text-sm font-semibold text-gray-900">
                        Includes:
                      </h4>
                      <ul className="space-y-1">
                        {option.includes.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start">
                            <CheckCircle className="mr-2 mt-0.5 size-3 shrink-0 text-green-600" />
                            <span className="text-sm text-gray-700">
                              {item}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-700">
                          Format:
                        </span>
                        <Badge variant="outline">{option.format}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-700">
                          Size:
                        </span>
                        <span className="text-sm text-gray-600">
                          {option.size}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-700">
                          Time:
                        </span>
                        <span className="text-sm text-gray-600">
                          {option.time}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mb-12"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Export Formats</CardTitle>
              <CardDescription>
                Choose the format that best suits your needs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                {exportFormats.map((format, index) => (
                  <div key={index} className="text-center">
                    <div className="mx-auto mb-3 flex size-12 items-center justify-center rounded-lg bg-green-100">
                      <FileText className="size-6 text-green-600" />
                    </div>
                    <h4 className="mb-2 font-semibold text-gray-900">
                      {format.format}
                    </h4>
                    <p className="mb-3 text-sm text-gray-600">
                      {format.description}
                    </p>
                    <div className="space-y-1">
                      <div className="text-xs font-medium text-gray-700">
                        Best for:
                      </div>
                      <div className="text-xs text-gray-600">
                        {format.bestFor}
                      </div>
                      <div className="text-xs text-green-600">
                        {format.compatibility}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="mb-8"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Export Process</CardTitle>
              <CardDescription>
                Simple steps to export your data securely
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {exportSteps.map((step, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="flex size-8 shrink-0 items-center justify-center rounded-full bg-blue-100">
                      <span className="text-sm font-bold text-blue-600">
                        {index + 1}
                      </span>
                    </div>
                    <span className="text-sm text-gray-700">{step}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <Card className="bg-gradient-to-r from-blue-50 to-green-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <Shield className="mx-auto mb-4 size-12 text-blue-600" />
                <h3 className="mb-4 text-xl font-semibold text-gray-900">
                  Secure Data Export
                </h3>
                <p className="mb-6 text-gray-600">
                  All data exports are encrypted, authenticated, and logged for
                  security. Download links expire after 7 days for your
                  protection.
                </p>
                <div className="mb-6 flex flex-wrap justify-center gap-4">
                  <p className="max-w-md text-center text-gray-600">
                    For data export requests, please contact our support team at{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="font-medium text-blue-600 hover:text-blue-700"
                    >
                      <EMAIL>
                    </a>{" "}
                    with your specific requirements.
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-blue-600">
                      Encrypted
                    </div>
                    <div className="text-sm text-gray-600">Secure Transfer</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-green-600">
                      Verified
                    </div>
                    <div className="text-sm text-gray-600">Identity Check</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-purple-600">
                      Logged
                    </div>
                    <div className="text-sm text-gray-600">Audit Trail</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-orange-600">
                      Free
                    </div>
                    <div className="text-sm text-gray-600">No Charges</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
