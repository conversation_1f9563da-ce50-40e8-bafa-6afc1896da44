#!/usr/bin/env node

/**
 * This script drops all tables in the database and recreates them from the schema
 * WARNING: This will delete ALL data in the database
 * Run with: node scripts/reset-db.js
 */

require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const { Client } = require('pg');
const { execSync } = require('child_process');

// Get the database URL from environment variables
let databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Remove SSL requirement for local development
if (databaseUrl.includes('sslmode=require')) {
  databaseUrl = databaseUrl.replace('sslmode=require', 'sslmode=prefer');
}

console.log(`🔍 Connecting to database: ${databaseUrl.replace(/:[^:]*@/, ':****@')}`);

// Create a client
const client = new Client({
  connectionString: databaseUrl,
  ssl: false
});

async function resetDatabase() {
  try {
    // Connect to the database
    await client.connect();
    console.log('✅ Connected to database');
    
    // Get all tables in the public schema
    const tablesResult = await client.query(`
      SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    `);
    
    const tables = tablesResult.rows.map(row => row.tablename);
    console.log(`Found ${tables.length} tables: ${tables.join(', ')}`);
    
    if (tables.length === 0) {
      console.log('No tables to drop.');
    } else {
      // Drop all tables
      console.log('Dropping all tables...');
      
      // Disable foreign key checks
      await client.query('SET session_replication_role = replica;');
      
      // Generate DROP TABLE statements for all tables
      const dropQuery = `DROP TABLE IF EXISTS ${tables.map(t => `"${t}"`).join(', ')} CASCADE;`;
      await client.query(dropQuery);
      
      // Re-enable foreign key checks
      await client.query('SET session_replication_role = DEFAULT;');
      
      console.log('✅ All tables dropped successfully');
    }
    
    // Close the connection
    await client.end();
    console.log('✅ Database connection closed');
    
    // Push the schema using Drizzle
    console.log('Pushing schema using Drizzle...');
    execSync('npx drizzle-kit push', { stdio: 'inherit' });
    
    console.log('✅ Database reset and schema pushed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error resetting database:', error);
    
    // Try to close the connection
    try {
      await client.end();
    } catch (closeError) {
      console.error('Error closing database connection:', closeError);
    }
    
    process.exit(1);
  }
}

// Confirm before proceeding
console.log('\n⚠️  WARNING: This will delete ALL data in the database ⚠️');
console.log('Press Ctrl+C to cancel or wait 5 seconds to continue...');

setTimeout(() => {
  console.log('\nProceeding with database reset...');
  resetDatabase();
}, 5000);
