import { getSubscriptionAction } from "@/actions/subscription-actions"
import { checkTrialStatusAction } from "@/actions/trial-actions"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"

export async function SubscriptionInfo() {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🔍 SUBSCRIPTION INFO - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  })

  if (!userId) {
    return {
      tier: "free",
      trialDaysLeft: 0,
      status: "inactive"
    }
  }

  const subscription = await getSubscriptionAction()

  // If subscription data is available, use it
  if (subscription) {
    // For trial tier, get the days left
    if (subscription.tier === "trial") {
      // If trialDaysLeft is already calculated, use it
      if (subscription.trialDaysLeft !== undefined) {
        return {
          tier: subscription.tier,
          trialDaysLeft: subscription.trialDaysLeft,
          status: subscription.status
        }
      }

      // Otherwise calculate from trialEndsAt
      const now = new Date()
      const trialEndDate = new Date(subscription.trialEndsAt || "")
      const trialDaysLeft = subscription.trialEndsAt
        ? Math.max(
            0,
            Math.round(
              (trialEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
            )
          )
        : 0

      // Removed console.log to prevent hydration errors

      return {
        tier: subscription.tier,
        trialDaysLeft,
        status: subscription.status
      }
    }

    // For trial-expired tier
    if (subscription.tier === "trial-expired") {
      return {
        tier: subscription.tier,
        trialDaysLeft: 0,
        status: subscription.status
      }
    }

    // For non-trial tiers
    return {
      tier: subscription.tier,
      trialDaysLeft: 0,
      status: subscription.status
    }
  }

  // Fallback to checking trial status directly
  const trialStatus = await checkTrialStatusAction(userId)

  if (trialStatus.isSuccess && trialStatus.data?.isActive) {
    return {
      tier: "trial",
      trialDaysLeft: trialStatus.data.daysLeft || 0,
      status: "active"
    }
  }

  // Default to free tier
  return {
    tier: "free",
    trialDaysLeft: 0,
    status: "active"
  }
}
