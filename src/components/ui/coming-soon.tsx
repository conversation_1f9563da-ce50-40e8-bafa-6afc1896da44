"use client"

import {
  Card,
  CardContent,
  CardDescription,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON>rk<PERSON>, <PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"
import { DevelopmentBadge } from "@/components/ui/development-badge"

interface ComingSoonProps {
  title?: string
  description?: string
  className?: string
}

export function ComingSoon({
  title = "Coming Soon",
  description = "We're working hard to bring you this feature. Stay tuned for updates!",
  className
}: ComingSoonProps) {
  return (
    <div className={cn("space-y-6", className)}>
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">{title}</h1>
      </div>

      <Card className="border-2 border-dashed border-gray-300 dark:border-gray-600">
        <CardContent className="flex flex-col items-center justify-center py-16 text-center">
          <div className="relative mb-6">
            <div className="rounded-full bg-gradient-to-br from-blue-100 to-purple-100 p-6 dark:from-blue-900/30 dark:to-purple-900/30">
              <Clock className="size-12 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="absolute -right-1 -top-1 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 p-1">
              <Sparkles className="size-4 text-white" />
            </div>
          </div>

          <CardTitle className="mb-3 flex items-center justify-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-2xl font-bold text-transparent">
            {title}
            <DevelopmentBadge variant="badge" size="sm" />
          </CardTitle>

          <CardDescription className="mb-6 max-w-md text-lg">
            {description}
          </CardDescription>

          <div className="flex justify-center">
            <Button
              variant="ghost"
              className="text-gray-600 dark:text-gray-400"
              onClick={() => window.history.back()}
            >
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
