#!/usr/bin/env node

/**
 * Test script to trigger optimization lifecycle and observe logging
 * This will create an optimization, add initial measurements, and generate suggestions
 */

async function testOptimizationLifecycle() {
  console.log('🔍 TESTING OPTIMIZATION LIFECYCLE WITH LOGGING');
  console.log('==============================================');

  const optimizerId = `test_lifecycle_logging_${Date.now()}`;
  
  try {
    // Step 1: Create optimization
    console.log('\n📝 STEP 1: Creating optimization...');
    
    const createConfig = {
      optimizer_id: optimizerId,
      parameters: [
        {
          name: "x1",
          type: "continuous",
          bounds: [0, 100]
        },
        {
          name: "x2", 
          type: "continuous",
          bounds: [0, 100]
        }
      ],
      target_config: {
        name: "Target 1",
        mode: "MAX"
      },
      objective_type: "SingleTarget",
      initial_sampling_strategy: "LHS",
      num_initial_samples: 0
    };
    
    const createResponse = await fetch('http://localhost:8000/optimizations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(createConfig)
    });
    
    if (!createResponse.ok) {
      throw new Error(`Create failed: ${createResponse.statusText}`);
    }
    
    const createResult = await createResponse.json();
    console.log('✅ Optimization created:', createResult.message);
    
    // Step 2: Add initial measurements
    console.log('\n📊 STEP 2: Adding initial measurements...');
    
    const measurements = [
      { parameters: { x1: 25, x2: 30 }, target_value: 45 },
      { parameters: { x1: 75, x2: 20 }, target_value: 65 },
      { parameters: { x1: 50, x2: 80 }, target_value: 55 }
    ];
    
    for (let i = 0; i < measurements.length; i++) {
      const measurement = measurements[i];
      console.log(`   Adding measurement ${i + 1}: x1=${measurement.parameters.x1}, x2=${measurement.parameters.x2}, target=${measurement.target_value}`);
      
      const measurementResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          parameters: measurement.parameters,
          target_values: measurement.target_value
        })
      });
      
      if (!measurementResponse.ok) {
        throw new Error(`Measurement ${i + 1} failed: ${measurementResponse.statusText}`);
      }
      
      const measurementResult = await measurementResponse.json();
      console.log(`   ✅ Measurement ${i + 1} added:`, measurementResult.message);
    }
    
    // Step 3: Generate Bayesian suggestions
    console.log('\n🎯 STEP 3: Generating Bayesian suggestions...');
    
    const suggestionResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/suggest`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        batch_size: 2
      })
    });
    
    if (!suggestionResponse.ok) {
      throw new Error(`Suggestion failed: ${suggestionResponse.statusText}`);
    }
    
    const suggestionResult = await suggestionResponse.json();
    console.log('✅ Suggestions generated:', suggestionResult.suggestions?.length || 0, 'suggestions');
    
    if (suggestionResult.suggestions && suggestionResult.suggestions.length > 0) {
      suggestionResult.suggestions.forEach((suggestion, index) => {
        console.log(`   Suggestion ${index + 1}: x1=${suggestion.x1?.toFixed(2)}, x2=${suggestion.x2?.toFixed(2)}`);
      });
    }
    
    // Step 4: Add measurement for one suggestion
    console.log('\n📊 STEP 4: Adding measurement for suggestion...');
    
    if (suggestionResult.suggestions && suggestionResult.suggestions.length > 0) {
      const firstSuggestion = suggestionResult.suggestions[0];
      const suggestionMeasurement = {
        parameters: {
          x1: firstSuggestion.x1,
          x2: firstSuggestion.x2
        },
        target_value: 70 // Simulated measurement result
      };
      
      console.log(`   Adding measurement for suggestion: x1=${suggestionMeasurement.parameters.x1?.toFixed(2)}, x2=${suggestionMeasurement.parameters.x2?.toFixed(2)}, target=${suggestionMeasurement.target_value}`);
      
      const suggestionMeasurementResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          parameters: suggestionMeasurement.parameters,
          target_values: suggestionMeasurement.target_value
        })
      });
      
      if (!suggestionMeasurementResponse.ok) {
        throw new Error(`Suggestion measurement failed: ${suggestionMeasurementResponse.statusText}`);
      }
      
      const suggestionMeasurementResult = await suggestionMeasurementResponse.json();
      console.log('   ✅ Suggestion measurement added:', suggestionMeasurementResult.message);
    }
    
    // Step 5: Generate next round of suggestions
    console.log('\n🎯 STEP 5: Generating next round of suggestions...');
    
    const nextSuggestionResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/suggest`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        batch_size: 1
      })
    });
    
    if (!nextSuggestionResponse.ok) {
      throw new Error(`Next suggestion failed: ${nextSuggestionResponse.statusText}`);
    }
    
    const nextSuggestionResult = await nextSuggestionResponse.json();
    console.log('✅ Next suggestions generated:', nextSuggestionResult.suggestions?.length || 0, 'suggestions');
    
    if (nextSuggestionResult.suggestions && nextSuggestionResult.suggestions.length > 0) {
      nextSuggestionResult.suggestions.forEach((suggestion, index) => {
        console.log(`   Next suggestion ${index + 1}: x1=${suggestion.x1?.toFixed(2)}, x2=${suggestion.x2?.toFixed(2)}`);
      });
    }
    
    // Step 6: Check final campaign state
    console.log('\n📊 STEP 6: Checking final campaign state...');
    
    const measurementsResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`);
    
    if (measurementsResponse.ok) {
      const measurementsResult = await measurementsResponse.json();
      console.log('✅ Final campaign measurements:', measurementsResult.count || 0);
      
      if (measurementsResult.measurements) {
        measurementsResult.measurements.forEach((measurement, index) => {
          const target1 = measurement['Target 1'];
          console.log(`   Measurement ${index + 1}: x1=${measurement.x1?.toFixed(2)}, x2=${measurement.x2?.toFixed(2)}, Target 1=${target1}`);
        });
      }
    }
    
    console.log('\n🎉 OPTIMIZATION LIFECYCLE TEST COMPLETED SUCCESSFULLY!');
    console.log('Check the backend logs to see the detailed lifecycle logging.');
    
  } catch (error) {
    console.error('❌ Error during lifecycle test:', error.message);
  }
}

// Run the test
testOptimizationLifecycle().catch(console.error);
