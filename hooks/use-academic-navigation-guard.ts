/*
This hook provides navigation guards for academic users to prevent bypassing
the verification and survey completion flow.
*/

"use client"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useUser } from "@clerk/nextjs"

interface NavigationGuardOptions {
  allowedPaths: string[]
  redirectTo: string
  checkCondition?: () => Promise<boolean>
}

export function useAcademicNavigationGuard(options: NavigationGuardOptions) {
  const router = useRouter()
  const pathname = usePathname()
  const { isLoaded, user } = useUser()

  useEffect(() => {
    if (!isLoaded || !user) return

    const checkNavigation = async () => {
      // Check if current path is allowed
      const isAllowedPath = options.allowedPaths.some(path => 
        pathname === path || pathname.startsWith(path)
      )

      if (isAllowedPath) return

      // If there's a custom condition, check it
      if (options.checkCondition) {
        const conditionMet = await options.checkCondition()
        if (conditionMet) return
      }

      // Redirect to the specified path
      console.log(`Navigation guard: Redirecting from ${pathname} to ${options.redirectTo}`)
      router.replace(options.redirectTo)
    }

    checkNavigation()
  }, [pathname, isLoaded, user, router, options])

  // Return a function to check if navigation should be blocked
  const shouldBlockNavigation = (targetPath: string): boolean => {
    if (!isLoaded || !user) return false
    
    return !options.allowedPaths.some(path => 
      targetPath === path || targetPath.startsWith(path)
    )
  }

  return { shouldBlockNavigation }
}

// Specific hook for academic signup flow
export function useAcademicSignupGuard() {
  return useAcademicNavigationGuard({
    allowedPaths: [
      "/academic-signup",
      "/academic-survey", 
      "/verify-email",
      "/api/",
      "/login",
      "/signup"
    ],
    redirectTo: "/academic-signup"
  })
}

// Specific hook for academic survey flow
export function useAcademicSurveyGuard() {
  return useAcademicNavigationGuard({
    allowedPaths: [
      "/academic-survey",
      "/api/",
      "/login",
      "/signup"
    ],
    redirectTo: "/academic-survey"
  })
}
