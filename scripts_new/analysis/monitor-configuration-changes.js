#!/usr/bin/env node

/**
 * Monitor configuration changes for CRUD operations
 * This will track before/after states when configuration is updated
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, desc } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema definitions
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function monitorConfigurationChanges() {
  console.log('🔍 MONITORING CONFIGURATION CHANGES');
  console.log('===================================');
  console.log('Looking for: "Test campaign working" optimization');
  console.log('');

  let client;
  let previousConfig = null;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    // Find the optimization
    const optimizationName = "Test campaign working";
    
    async function checkConfiguration() {
      try {
        const optimizationsList = await db.select().from(optimizationsTable);
        const opt = optimizationsList.find(o => o.name.toLowerCase().includes(optimizationName.toLowerCase()));
        
        if (!opt) {
          console.log(`❌ Optimization "${optimizationName}" not found`);
          return;
        }
        
        const currentConfig = opt.config;
        const currentTime = new Date().toISOString();
        
        // Check if configuration changed
        if (previousConfig === null) {
          console.log(`📋 INITIAL CONFIGURATION (${currentTime}):`);
          console.log('===========================================');
          console.log(JSON.stringify(currentConfig, null, 2));
          console.log('');
          console.log('🔍 Monitoring for changes... (Press Ctrl+C to stop)');
          console.log('');
          
          // Show key values for easy comparison
          console.log('📊 KEY VALUES TO WATCH:');
          console.log('=======================');
          if (currentConfig?.parameters) {
            currentConfig.parameters.forEach((param, index) => {
              console.log(`  Parameter ${index + 1} (${param.name}): bounds [${param.bounds?.join(', ') || 'none'}]`);
            });
          }
          if (currentConfig?.target_config) {
            console.log(`  Target (${currentConfig.target_config.name}): bounds [${currentConfig.target_config.bounds?.join(', ') || 'none'}]`);
            console.log(`  Target mode: ${currentConfig.target_config.mode}`);
          }
          if (currentConfig?.acquisition_config) {
            console.log(`  Acquisition function: ${currentConfig.acquisition_config.type}`);
          }
          console.log('');
          
        } else if (JSON.stringify(currentConfig) !== JSON.stringify(previousConfig)) {
          console.log(`🔄 CONFIGURATION CHANGED (${currentTime}):`);
          console.log('=========================================');
          
          // Compare parameter bounds
          if (currentConfig?.parameters && previousConfig?.parameters) {
            console.log('📊 PARAMETER CHANGES:');
            currentConfig.parameters.forEach((param, index) => {
              const prevParam = previousConfig.parameters[index];
              if (prevParam && JSON.stringify(param.bounds) !== JSON.stringify(prevParam.bounds)) {
                console.log(`  ✅ ${param.name}: [${prevParam.bounds?.join(', ') || 'none'}] → [${param.bounds?.join(', ') || 'none'}]`);
              }
            });
          }
          
          // Compare target bounds
          if (currentConfig?.target_config && previousConfig?.target_config) {
            const currentTargetBounds = currentConfig.target_config.bounds;
            const prevTargetBounds = previousConfig.target_config.bounds;
            
            if (JSON.stringify(currentTargetBounds) !== JSON.stringify(prevTargetBounds)) {
              console.log('🎯 TARGET CHANGES:');
              console.log(`  ✅ Target bounds: [${prevTargetBounds?.join(', ') || 'none'}] → [${currentTargetBounds?.join(', ') || 'none'}]`);
            } else {
              console.log('🎯 TARGET BOUNDS: No changes detected');
            }
            
            // Check target mode
            if (currentConfig.target_config.mode !== previousConfig.target_config.mode) {
              console.log(`  ✅ Target mode: ${previousConfig.target_config.mode} → ${currentConfig.target_config.mode}`);
            }
          }
          
          // Compare acquisition function
          if (currentConfig?.acquisition_config && previousConfig?.acquisition_config) {
            if (currentConfig.acquisition_config.type !== previousConfig.acquisition_config.type) {
              console.log('🔧 ACQUISITION FUNCTION CHANGES:');
              console.log(`  ✅ Type: ${previousConfig.acquisition_config.type} → ${currentConfig.acquisition_config.type}`);
            }
          }
          
          console.log('');
          console.log('📋 FULL NEW CONFIGURATION:');
          console.log('==========================');
          console.log(JSON.stringify(currentConfig, null, 2));
          console.log('');
          
          // Test backend to see if it reflects the changes
          console.log('🔍 TESTING BACKEND CONFIGURATION:');
          console.log('=================================');
          
          try {
            const measurementsResponse = await fetch(`http://localhost:8000/optimizations/${opt.optimizerId}/measurements`);
            
            if (measurementsResponse.ok) {
              const measurementsData = await measurementsResponse.json();
              console.log(`✅ Backend accessible, measurements: ${measurementsData.measurements?.length || 0}`);
              
              if (measurementsData.target_info) {
                console.log('🎯 Backend target info:');
                measurementsData.target_info.forEach((target, index) => {
                  console.log(`  ${index + 1}. ${target.name}: ${target.mode}`);
                });
              }
            } else {
              console.log(`❌ Backend not accessible: ${measurementsResponse.statusText}`);
            }
          } catch (error) {
            console.log(`❌ Backend error: ${error.message}`);
          }
          
          console.log('');
        }
        
        previousConfig = JSON.parse(JSON.stringify(currentConfig)); // Deep copy
        
      } catch (error) {
        console.error('❌ Error checking configuration:', error.message);
      }
    }
    
    // Initial check
    await checkConfiguration();
    
    // Poll for changes every 2 seconds
    const interval = setInterval(checkConfiguration, 2000);
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n\n🛑 Stopping configuration monitoring...');
      clearInterval(interval);
      if (client) {
        client.end();
      }
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      console.log('\n\n🛑 Stopping configuration monitoring...');
      clearInterval(interval);
      if (client) {
        client.end();
      }
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Error during monitoring:', error);
    if (client) {
      client.end();
    }
  }
}

// Run the monitoring
monitorConfigurationChanges().catch(console.error);
