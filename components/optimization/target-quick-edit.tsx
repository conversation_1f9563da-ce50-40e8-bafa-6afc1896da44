"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Edit, Save, X, ArrowUp, ArrowDown } from "lucide-react"

interface TargetConfig {
  name: string
  mode: "MAX" | "MIN"
  bounds?: [number, number]
  weight?: number
  transformation?: "LINEAR" | "LOG" | "SQRT"
  type?: "Numerical" | "Categorical"
}

interface TargetQuickEditProps {
  target: TargetConfig
  onSave: (updatedTarget: TargetConfig) => void
  trigger?: React.ReactNode
  disabled?: boolean
  isMultiTarget?: boolean
}

export function TargetQuickEdit({
  target,
  onSave,
  trigger,
  disabled = false,
  isMultiTarget = false
}: TargetQuickEditProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [editedTarget, setEditedTarget] = useState<TargetConfig>(target)
  const [hasChanges, setHasChanges] = useState(false)

  // Reset form when target changes or popover opens
  useEffect(() => {
    if (isOpen) {
      setEditedTarget(target)
      setHasChanges(false)
    }
  }, [target, isOpen])

  // Check for changes
  useEffect(() => {
    const changed = JSON.stringify(editedTarget) !== JSON.stringify(target)
    setHasChanges(changed)
  }, [editedTarget, target])

  const handleModeChange = (mode: "MAX" | "MIN") => {
    setEditedTarget(prev => ({
      ...prev,
      mode
    }))
  }

  const handleBoundChange = (index: 0 | 1, value: string) => {
    const numValue = parseFloat(value)
    if (!isNaN(numValue)) {
      setEditedTarget(prev => ({
        ...prev,
        bounds:
          index === 0
            ? [numValue, prev.bounds?.[1] || 0]
            : [prev.bounds?.[0] || 0, numValue]
      }))
    }
  }

  const handleWeightChange = (value: string) => {
    const numValue = parseFloat(value)
    // Accept percentage values (1-100) and convert to decimal (0.01-1.0) for storage
    if (!isNaN(numValue) && numValue >= 1 && numValue <= 100) {
      setEditedTarget(prev => ({
        ...prev,
        weight: numValue / 100 // Convert percentage to decimal for backend
      }))
    }
  }

  const handleSave = () => {
    onSave(editedTarget)
    setIsOpen(false)
  }

  const handleCancel = () => {
    setEditedTarget(target)
    setHasChanges(false)
    setIsOpen(false)
  }

  const defaultTrigger = (
    <Button
      variant="ghost"
      size="sm"
      disabled={disabled}
      className="size-6 p-0 opacity-0 transition-opacity group-hover:opacity-100"
    >
      <Edit className="size-3" />
    </Button>
  )

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>{trigger || defaultTrigger}</PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <h4 className="text-sm font-medium">{target.name}</h4>
            <Badge variant="outline" className="text-xs">
              {target.type || "Numerical"}
            </Badge>
          </div>

          {/* Optimization Mode */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">Mode</Label>
            <Select value={editedTarget.mode} onValueChange={handleModeChange}>
              <SelectTrigger className="h-7">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MAX">
                  <div className="flex items-center gap-2">
                    <ArrowUp className="size-3 text-green-500" />
                    <span className="text-xs">Maximize</span>
                  </div>
                </SelectItem>
                <SelectItem value="MIN">
                  <div className="flex items-center gap-2">
                    <ArrowDown className="size-3 text-green-500" />
                    <span className="text-xs">Minimize</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Target Bounds */}
          <div className="space-y-2">
            <Label className="text-xs font-medium">Bounds</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-1">
                <Label className="text-muted-foreground text-xs">Min</Label>
                <Input
                  type="number"
                  value={editedTarget.bounds?.[0] || ""}
                  onChange={e => handleBoundChange(0, e.target.value)}
                  step="any"
                  placeholder="Min"
                  className="h-7 text-xs"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-muted-foreground text-xs">Max</Label>
                <Input
                  type="number"
                  value={editedTarget.bounds?.[1] || ""}
                  onChange={e => handleBoundChange(1, e.target.value)}
                  step="any"
                  placeholder="Max"
                  className="h-7 text-xs"
                />
              </div>
            </div>
          </div>

          {/* Weight (for multi-target) */}
          {isMultiTarget && (
            <div className="space-y-2">
              <Label className="text-xs font-medium">Weight (%)</Label>
              <div className="relative">
                <Input
                  type="number"
                  value={
                    editedTarget.weight
                      ? Math.round(editedTarget.weight * 100)
                      : 50
                  }
                  onChange={e => handleWeightChange(e.target.value)}
                  step="1"
                  min="1"
                  max="100"
                  placeholder="50"
                  className="h-7 text-xs"
                />
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                  <span className="text-xs text-gray-500">%</span>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="h-7 text-xs"
            >
              <X className="mr-1 size-3" />
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
              className="h-7 text-xs"
            >
              <Save className="mr-1 size-3" />
              Save
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
