/*
This client page is the marketing homepage.
*/

"use client"

import { HeroSection } from "@/components/landing/hero"
import { PublicationsShowcase } from "@/components/landing/publications-showcase"
import { TestimonialsShowcase } from "@/components/landing/testimonials-showcase"
import { useRef } from "react"
import { motion } from "framer-motion"
import EnhancedBayesianBackground from "@/components/landing/enhanced-bayesian-background"
import EnhancedOptimizationShowcase from "@/components/landing/enhanced-optimization-showcase"

export default function HomePage() {
  const heroSectionRef = useRef<HTMLDivElement>(null)

  return (
    <div>
      {/* Hero section with Enhanced Bayesian background - positioned high */}
      <motion.div
        ref={heroSectionRef}
        className="relative -mt-4 flex min-h-[70vh] flex-col overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <EnhancedBayesianBackground />
        <div className="flex flex-1 flex-col">
          <HeroSection />
        </div>
      </motion.div>

      {/* Publications showcase section */}
      <PublicationsShowcase />

      {/* Feature showcase with default background */}
      <div className="bg-background relative">
        {/* Subtle gradient separator at the top */}
        <motion.div
          className="from-primary/5 absolute left-0 top-0 h-16 w-full bg-gradient-to-b to-transparent"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
        />
        <EnhancedOptimizationShowcase />

        {/* Subtle gradient separator between sections */}
        <motion.div
          className="from-primary/5 to-background mx-auto my-8 h-16 w-full bg-gradient-to-b"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
        />

        <TestimonialsShowcase />
      </div>
    </div>
  )
}
