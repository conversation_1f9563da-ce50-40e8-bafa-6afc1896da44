"use server"

import { db } from "@/db/db"
import { userSurveysTable } from "@/db/schema"
import { eq } from "drizzle-orm"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { revalidatePath } from "next/cache"

// Define the survey response type
export type SurveyResponses = {
  industry: string
  role: string
  useCase: string
}

// Action to save survey responses
export async function saveSurveyAction(
  responses: SurveyResponses,
  completed: boolean = true
) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return {
        isSuccess: false,
        message: "User not authenticated"
      }
    }

    // Insert or update survey responses
    await db
      .insert(userSurveysTable)
      .values({
        userId,
        responses,
        completed: completed ? "true" : "false",
        surveyType: "regular" // Explicitly set survey type to regular
      })
      .onConflictDoUpdate({
        target: userSurveysTable.userId,
        set: {
          responses,
          completed: completed ? "true" : "false",
          surveyType: "regular", // Explicitly set survey type to regular
          updatedAt: new Date()
        }
      })


    revalidatePath("/welcome")
    revalidatePath("/dashboard")

    return {
      isSuccess: true,
      message: "Survey responses saved successfully"
    }
  } catch (error) {
    console.error("Error saving survey responses:", error)
    return {
      isSuccess: false,
      message: "Failed to save survey responses"
    }
  }
}

// Action to check if a user has completed the survey
export async function checkSurveyCompletionAction() {
  try {
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    // Use the actual user ID if available, otherwise fall back to Clerk ID
    const finalUserId = userId || clerkUserId

    if (!finalUserId) {
      return {
        isSuccess: false,
        message: "User not authenticated",
        hasCompleted: false
      }
    }

    const survey = await db
      .select()
      .from(userSurveysTable)
      .where(eq(userSurveysTable.userId, finalUserId))
      .limit(1)

    const hasCompleted =
      survey.length > 0 &&
      survey[0].completed === "true" &&
      survey[0].surveyType === "regular"

    return {
      isSuccess: true,
      message: "Survey completion status retrieved",
      hasCompleted
    }
  } catch (error) {
    console.error("Error checking survey completion:", error)
    return {
      isSuccess: false,
      message: "Failed to check survey completion",
      hasCompleted: false
    }
  }
}

// Action to get survey responses for a user
export async function getSurveyResponsesAction() {
  try {
    const { userId } = await auth()

    if (!userId) {
      return {
        isSuccess: false,
        message: "User not authenticated",
        responses: null
      }
    }

    const survey = await db
      .select()
      .from(userSurveysTable)
      .where(
        eq(userSurveysTable.userId, userId) &&
        eq(userSurveysTable.surveyType, "regular")
      )
      .limit(1)

    if (survey.length === 0) {
      return {
        isSuccess: true,
        message: "No survey responses found",
        responses: null
      }
    }

    return {
      isSuccess: true,
      message: "Survey responses retrieved",
      responses: survey[0].responses as SurveyResponses
    }
  } catch (error) {
    console.error("Error getting survey responses:", error)
    return {
      isSuccess: false,
      message: "Failed to get survey responses",
      responses: null
    }
  }
}
