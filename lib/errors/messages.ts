import { ErrorCode, ErrorResolution, QuickFix } from "./types"

// Error message templates
const ERROR_MESSAGES: Record<
  ErrorCode,
  (params: Record<string, any>) => string
> = {
  // Validation errors
  [ErrorCode.VALIDATION_REQUIRED_FIELD]: params =>
    `The field "${params.field}" is required and cannot be empty.`,

  [ErrorCode.VALIDATION_INVALID_FORMAT]: params =>
    `The value "${params.currentValue}" in field "${params.field}" has an invalid format. Expected: ${params.expectedValue}`,

  [ErrorCode.VALIDATION_OUT_OF_RANGE]: params =>
    `The value ${params.currentValue} in field "${params.field}" is out of range. Expected: ${params.expectedRange?.[0]} to ${params.expectedRange?.[1]}`,

  [ErrorCode.VALIDATION_TYPE_MISMATCH]: params =>
    `The field "${params.field}" expects a ${params.expectedValue} but received ${typeof params.currentValue}`,

  // Parameter errors
  [ErrorCode.PARAMETER_BOUNDS_INVALID]: params =>
    `Parameter "${params.parameterName}" has invalid bounds. Lower bound (${params.lowerBound}) must be less than upper bound (${params.upperBound}).`,

  [ErrorCode.PARAMETER_VALUES_EMPTY]: params =>
    `Parameter "${params.parameterName}" must have at least one value specified.`,

  [ErrorCode.PARAMETER_TYPE_CHANGE_INVALID]: params =>
    `Cannot change parameter "${params.parameterName}" from ${params.oldType} to ${params.newType} because it would invalidate ${params.affectedMeasurements} existing measurements.`,

  [ErrorCode.PARAMETER_NAME_DUPLICATE]: params =>
    `Parameter name "${params.parameterName}" is already in use. Please choose a unique name.`,

  [ErrorCode.PARAMETER_BOUNDS_TOO_NARROW]: params =>
    `Parameter "${params.parameterName}" bounds are too narrow and would exclude ${params.excludedMeasurements} measurements.`,

  [ErrorCode.PARAMETER_VALUES_DUPLICATE]: params =>
    `Parameter "${params.parameterName}" contains duplicate values. Each value must be unique.`,

  // Target errors
  [ErrorCode.TARGET_BOUNDS_INVALID]: params =>
    `Target "${params.targetName}" has invalid bounds. Lower bound (${params.lowerBound}) must be less than upper bound (${params.upperBound}).`,

  [ErrorCode.TARGET_MODE_INCOMPATIBLE]: params =>
    `Target "${params.targetName}" mode ${params.mode} is incompatible with the current objective configuration.`,

  [ErrorCode.TARGET_WEIGHT_INVALID]: params =>
    `Target "${params.targetName}" weight ${params.weight} must be between 0 and 1.`,

  [ErrorCode.TARGET_TRANSFORMATION_INVALID]: params =>
    `Target "${params.targetName}" transformation ${params.transformation} is not valid for the current data range.`,

  // Constraint errors
  [ErrorCode.CONSTRAINT_SYNTAX_ERROR]: params =>
    `Constraint "${params.constraintName}" has a syntax error: ${params.error}`,

  [ErrorCode.CONSTRAINT_PARAMETER_NOT_FOUND]: params =>
    `Constraint "${params.constraintName}" references parameter "${params.parameterName}" which does not exist.`,

  [ErrorCode.CONSTRAINT_INFEASIBLE]: params =>
    `Constraint "${params.constraintName}" makes the optimization problem infeasible. No valid parameter combinations exist.`,

  [ErrorCode.CONSTRAINT_CONFLICTING]: params =>
    `Constraint "${params.constraintName}" conflicts with existing constraint "${params.conflictingConstraint}".`,

  [ErrorCode.CONSTRAINT_REDUNDANT]: params =>
    `Constraint "${params.constraintName}" is redundant and can be removed without affecting the optimization.`,

  // Measurement errors
  [ErrorCode.MEASUREMENT_ALL_FILTERED]: params =>
    `All ${params.totalMeasurements} measurements would be filtered out by the new configuration. At least one measurement must remain.`,

  [ErrorCode.MEASUREMENT_INSUFFICIENT_DATA]: params =>
    `Only ${params.remainingMeasurements} measurements would remain after filtering. At least ${params.minimumRequired} measurements are recommended.`,

  [ErrorCode.MEASUREMENT_BOUNDS_VIOLATION]: params =>
    `${params.violatingMeasurements} measurements violate the new parameter bounds and will be excluded.`,

  [ErrorCode.MEASUREMENT_CONSTRAINT_VIOLATION]: params =>
    `${params.violatingMeasurements} measurements violate the new constraints and will be excluded.`,

  // System errors
  [ErrorCode.SYSTEM_CAMPAIGN_NOT_FOUND]: params =>
    `Campaign "${params.campaignId}" was not found. It may have been deleted or moved.`,

  [ErrorCode.SYSTEM_SAVE_FAILED]: params =>
    `Failed to save the configuration changes. ${params.reason || "Please try again."}`,

  [ErrorCode.SYSTEM_LOAD_FAILED]: params =>
    `Failed to load the campaign configuration. ${params.reason || "Please refresh and try again."}`,

  [ErrorCode.SYSTEM_MEMORY_ERROR]: params =>
    `Insufficient memory to process the request. Try reducing the dataset size or simplifying the configuration.`,

  // Network errors
  [ErrorCode.NETWORK_CONNECTION_FAILED]: params =>
    `Connection to the server failed. Please check your internet connection and try again.`,

  [ErrorCode.NETWORK_TIMEOUT]: params =>
    `The request timed out after ${params.timeout || 30} seconds. The server may be busy.`,

  [ErrorCode.NETWORK_SERVER_ERROR]: params =>
    `Server error (${params.statusCode}): ${params.message || "Internal server error"}`,

  // Concurrency errors
  [ErrorCode.CONCURRENCY_CONFLICT]: params =>
    `Another user has modified this configuration. Please refresh and apply your changes again.`,

  [ErrorCode.CONCURRENCY_LOCK_TIMEOUT]: params =>
    `The configuration is currently being modified by another process. Please wait and try again.`,

  [ErrorCode.CONCURRENCY_VERSION_MISMATCH]: params =>
    `The configuration version has changed. Your changes are based on version ${params.expectedVersion} but the current version is ${params.currentVersion}.`
}

// Error resolution templates
const ERROR_RESOLUTIONS: Record<
  ErrorCode,
  (params: Record<string, any>) => ErrorResolution
> = {
  // Validation errors
  [ErrorCode.VALIDATION_REQUIRED_FIELD]: params => ({
    description: `Provide a value for the required field "${params.field}".`,
    steps: [
      `Click on the "${params.field}" field`,
      "Enter a valid value",
      "Ensure the value meets the format requirements"
    ],
    quickFixes: [
      {
        id: "focus_field",
        label: "Focus Field",
        description: `Jump to the "${params.field}" field`,
        action: { type: "reset_to_default", target: params.field }
      }
    ]
  }),

  [ErrorCode.PARAMETER_BOUNDS_INVALID]: params => ({
    description:
      "Adjust the parameter bounds so that the lower bound is less than the upper bound.",
    steps: [
      "Check the lower bound value",
      "Check the upper bound value",
      "Ensure lower bound < upper bound",
      "Consider the range of your existing measurements"
    ],
    quickFixes: [
      {
        id: "swap_bounds",
        label: "Swap Bounds",
        description: "Automatically swap the lower and upper bounds",
        action: {
          type: "adjust_bounds",
          target: params.parameterName,
          value: "swap"
        }
      },
      {
        id: "expand_bounds",
        label: "Expand to Include Data",
        description: "Set bounds to include all existing measurements",
        action: { type: "expand_range", target: params.parameterName }
      }
    ],
    documentationUrl: "/docs/parameters#bounds"
  }),

  [ErrorCode.PARAMETER_VALUES_DUPLICATE]: params => ({
    description: "Remove duplicate values from the parameter values list.",
    steps: [
      "Review the values list for duplicates",
      "Remove any duplicate entries",
      "Ensure each value appears only once",
      "Save the updated configuration"
    ],
    quickFixes: [
      {
        id: "remove_duplicates",
        label: "Remove Duplicates",
        description: "Automatically remove duplicate values",
        action: { type: "remove_duplicates", target: params.parameterName }
      }
    ],
    documentationUrl: "/docs/parameters#values"
  }),

  [ErrorCode.CONSTRAINT_INFEASIBLE]: params => ({
    description:
      "The constraint makes the optimization problem impossible to solve.",
    steps: [
      "Review the constraint expression",
      "Check if the constraint is too restrictive",
      "Consider relaxing the constraint bounds",
      "Verify parameter ranges are compatible"
    ],
    quickFixes: [
      {
        id: "remove_constraint",
        label: "Remove Constraint",
        description: "Remove this constraint to make the problem feasible",
        action: { type: "remove_constraint", target: params.constraintName }
      }
    ],
    documentationUrl: "/docs/constraints#feasibility"
  }),

  [ErrorCode.MEASUREMENT_ALL_FILTERED]: params => ({
    description:
      "Adjust the configuration to retain some existing measurements.",
    steps: [
      "Review the parameter bounds",
      "Check constraint restrictions",
      "Consider expanding bounds to include existing data",
      "Remove overly restrictive constraints"
    ],
    quickFixes: [
      {
        id: "expand_to_include_data",
        label: "Expand Bounds",
        description:
          "Automatically expand bounds to include existing measurements",
        action: { type: "expand_range", target: "all_parameters" }
      }
    ],
    documentationUrl: "/docs/measurements#filtering"
  }),

  // Default resolutions for remaining error codes
  [ErrorCode.VALIDATION_INVALID_FORMAT]: params => ({
    description: "Correct the format of the field value.",
    steps: ["Check the expected format", "Update the value accordingly"],
    documentationUrl: "/docs/validation"
  }),

  [ErrorCode.VALIDATION_OUT_OF_RANGE]: params => ({
    description: "Adjust the value to be within the valid range.",
    steps: ["Check the valid range", "Update the value accordingly"],
    documentationUrl: "/docs/validation"
  }),

  [ErrorCode.VALIDATION_TYPE_MISMATCH]: params => ({
    description: "Provide a value of the correct type.",
    steps: ["Check the expected type", "Update the value accordingly"],
    documentationUrl: "/docs/validation"
  }),

  [ErrorCode.PARAMETER_VALUES_EMPTY]: params => ({
    description: "Add at least one value to the parameter.",
    steps: ["Add values to the parameter", "Ensure values are valid"],
    documentationUrl: "/docs/parameters"
  }),

  [ErrorCode.PARAMETER_TYPE_CHANGE_INVALID]: params => ({
    description: "Parameter type change would affect existing measurements.",
    steps: ["Review impact on measurements", "Consider alternative approaches"],
    documentationUrl: "/docs/parameters"
  }),

  [ErrorCode.PARAMETER_NAME_DUPLICATE]: params => ({
    description: "Choose a unique parameter name.",
    steps: ["Select a different name", "Ensure uniqueness"],
    documentationUrl: "/docs/parameters"
  }),

  [ErrorCode.PARAMETER_BOUNDS_TOO_NARROW]: params => ({
    description: "Expand parameter bounds to include more measurements.",
    steps: ["Review current bounds", "Expand to include data"],
    documentationUrl: "/docs/parameters"
  }),

  [ErrorCode.TARGET_BOUNDS_INVALID]: params => ({
    description: "Fix target bounds so lower bound is less than upper bound.",
    steps: ["Check bounds values", "Correct the bounds"],
    documentationUrl: "/docs/targets"
  }),

  [ErrorCode.TARGET_MODE_INCOMPATIBLE]: params => ({
    description: "Select a compatible target mode.",
    steps: ["Review objective configuration", "Choose appropriate mode"],
    documentationUrl: "/docs/targets"
  }),

  [ErrorCode.TARGET_WEIGHT_INVALID]: params => ({
    description: "Set target weight between 0 and 1.",
    steps: ["Adjust weight value", "Ensure it is between 0 and 1"],
    documentationUrl: "/docs/targets"
  }),

  [ErrorCode.TARGET_TRANSFORMATION_INVALID]: params => ({
    description: "Choose a valid transformation for the target.",
    steps: ["Review available transformations", "Select appropriate one"],
    documentationUrl: "/docs/targets"
  }),

  [ErrorCode.CONSTRAINT_SYNTAX_ERROR]: params => ({
    description: "Fix the constraint syntax error.",
    steps: ["Review constraint expression", "Correct syntax"],
    documentationUrl: "/docs/constraints"
  }),

  [ErrorCode.CONSTRAINT_PARAMETER_NOT_FOUND]: params => ({
    description: "Reference an existing parameter in the constraint.",
    steps: ["Check parameter names", "Update constraint"],
    documentationUrl: "/docs/constraints"
  }),

  [ErrorCode.CONSTRAINT_CONFLICTING]: params => ({
    description: "Resolve conflicting constraints.",
    steps: ["Review constraints", "Remove or modify conflicts"],
    documentationUrl: "/docs/constraints"
  }),

  [ErrorCode.CONSTRAINT_REDUNDANT]: params => ({
    description: "Remove redundant constraint.",
    steps: ["Identify redundancy", "Remove constraint"],
    documentationUrl: "/docs/constraints"
  }),

  [ErrorCode.MEASUREMENT_INSUFFICIENT_DATA]: params => ({
    description: "Ensure sufficient measurements remain.",
    steps: ["Review filtering criteria", "Adjust to retain more data"],
    documentationUrl: "/docs/measurements"
  }),

  [ErrorCode.MEASUREMENT_BOUNDS_VIOLATION]: params => ({
    description: "Adjust bounds to include more measurements.",
    steps: ["Review parameter bounds", "Expand as needed"],
    documentationUrl: "/docs/measurements"
  }),

  [ErrorCode.MEASUREMENT_CONSTRAINT_VIOLATION]: params => ({
    description: "Adjust constraints to include more measurements.",
    steps: ["Review constraints", "Relax as needed"],
    documentationUrl: "/docs/measurements"
  }),

  [ErrorCode.SYSTEM_CAMPAIGN_NOT_FOUND]: params => ({
    description: "Campaign may have been deleted or moved.",
    steps: ["Check campaign exists", "Refresh and try again"],
    documentationUrl: "/docs/troubleshooting"
  }),

  [ErrorCode.SYSTEM_SAVE_FAILED]: params => ({
    description: "Failed to save configuration.",
    steps: ["Check connection", "Try again"],
    documentationUrl: "/docs/troubleshooting"
  }),

  [ErrorCode.SYSTEM_LOAD_FAILED]: params => ({
    description: "Failed to load configuration.",
    steps: ["Refresh page", "Check connection"],
    documentationUrl: "/docs/troubleshooting"
  }),

  [ErrorCode.SYSTEM_MEMORY_ERROR]: params => ({
    description: "Insufficient memory for operation.",
    steps: ["Reduce dataset size", "Simplify configuration"],
    documentationUrl: "/docs/troubleshooting"
  }),

  [ErrorCode.NETWORK_CONNECTION_FAILED]: params => ({
    description: "Check network connection.",
    steps: ["Verify internet connection", "Try again"],
    documentationUrl: "/docs/troubleshooting"
  }),

  [ErrorCode.NETWORK_TIMEOUT]: params => ({
    description: "Request timed out.",
    steps: ["Wait and try again", "Check server status"],
    documentationUrl: "/docs/troubleshooting"
  }),

  [ErrorCode.NETWORK_SERVER_ERROR]: params => ({
    description: "Server error occurred.",
    steps: ["Try again later", "Contact support if persistent"],
    documentationUrl: "/docs/troubleshooting"
  }),

  [ErrorCode.CONCURRENCY_CONFLICT]: params => ({
    description: "Configuration was modified by another user.",
    steps: ["Refresh page", "Reapply changes"],
    documentationUrl: "/docs/troubleshooting"
  }),

  [ErrorCode.CONCURRENCY_LOCK_TIMEOUT]: params => ({
    description: "Configuration is being modified.",
    steps: ["Wait and try again"],
    documentationUrl: "/docs/troubleshooting"
  }),

  [ErrorCode.CONCURRENCY_VERSION_MISMATCH]: params => ({
    description: "Configuration version has changed.",
    steps: ["Refresh page", "Reapply changes"],
    documentationUrl: "/docs/troubleshooting"
  })
}

export function getErrorMessage(
  code: ErrorCode,
  params: Record<string, any>
): string {
  const messageGenerator = ERROR_MESSAGES[code]
  if (messageGenerator) {
    try {
      return messageGenerator(params)
    } catch (error) {
      console.error(`Error generating message for code ${code}:`, error)
    }
  }
  return `Error ${code}: ${JSON.stringify(params)}`
}

export function getErrorResolution(
  code: ErrorCode,
  params: Record<string, any>
): ErrorResolution {
  const resolutionGenerator = ERROR_RESOLUTIONS[code]
  if (resolutionGenerator) {
    try {
      return resolutionGenerator(params)
    } catch (error) {
      console.error(`Error generating resolution for code ${code}:`, error)
    }
  }

  // Default resolution
  return {
    description: `Please review the configuration for error ${code}.`,
    steps: [
      "Check the field values",
      "Verify the configuration is valid",
      "Contact support if the issue persists"
    ],
    documentationUrl: "/docs/troubleshooting"
  }
}
