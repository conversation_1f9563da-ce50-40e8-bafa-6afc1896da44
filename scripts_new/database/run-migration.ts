import { addSampleClassToSamples } from "../../db/migrations/add_sample_class_to_samples";

async function runMigrations() {
  console.log("Running migrations...");
  
  try {
    // Run the migration to add sample_class to samples table
    const result = await addSampleClassToSamples();
    
    if (result.success) {
      console.log("Migration completed successfully");
    } else {
      console.error("Migration failed:", result.error);
    }
  } catch (error) {
    console.error("Error running migrations:", error);
  }
}

runMigrations()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Unhandled error in migrations:", error);
    process.exit(1);
  });
