"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>ch, Plus, Trash2, <PERSON>R<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { MultiSelect } from "@/components/ui/multi-select"
import { DiscreteDependenciesConstraint } from "@/lib/constraints"

interface DependenciesConstraintEditorProps {
  constraint: DiscreteDependenciesConstraint
  availableParameters: string[]
  onChange: (updates: Partial<DiscreteDependenciesConstraint>) => void
}

export function DependenciesConstraintEditor({
  constraint,
  availableParameters,
  onChange
}: DependenciesConstraintEditorProps) {
  const handleParametersChange = (parameters: string[]) => {
    onChange({ parameters })
  }

  const handleAffectedParametersChange = (
    index: number,
    affectedParams: string[]
  ) => {
    const newAffectedParameters = [...(constraint.affected_parameters || [])]
    newAffectedParameters[index] = affectedParams
    onChange({ affected_parameters: newAffectedParameters })
  }

  const addDependencyGroup = () => {
    const newAffectedParameters = [
      ...(constraint.affected_parameters || []),
      []
    ]
    onChange({ affected_parameters: newAffectedParameters })
  }

  const removeDependencyGroup = (index: number) => {
    const newAffectedParameters =
      constraint.affected_parameters?.filter((_, i) => i !== index) || []
    onChange({ affected_parameters: newAffectedParameters })
  }

  const getConstraintPreview = () => {
    if (!constraint.parameters.length) {
      return "No controlling parameters selected"
    }

    if (!constraint.affected_parameters?.length) {
      return `${constraint.parameters.join(", ")} control no parameters`
    }

    const dependencies = constraint.affected_parameters.map((group, index) => {
      if (group.length === 0) return `Group ${index + 1}: no parameters`
      return `Group ${index + 1}: ${group.join(", ")}`
    })

    return `${constraint.parameters.join(", ")} → ${dependencies.join("; ")}`
  }

  const parameterOptions = availableParameters.map(param => ({
    value: param,
    label: param
  }))

  // Get parameters not already used as controllers
  const getAvailableAffectedParameters = () => {
    return availableParameters.filter(
      param => !constraint.parameters.includes(param)
    )
  }

  const affectedParameterOptions = getAvailableAffectedParameters().map(
    param => ({
      value: param,
      label: param
    })
  )

  return (
    <div className="space-y-6">
      {/* Constraint Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <GitBranch className="size-4" />
            Dependencies Constraint Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted rounded-md p-3 font-mono text-sm">
            {getConstraintPreview()}
          </div>
          <p className="text-muted-foreground mt-2 text-sm">
            Define which parameters control the availability of other parameters
          </p>
        </CardContent>
      </Card>

      {/* Controlling Parameters */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-base font-medium">
            Controlling Parameters
          </Label>
          <Badge variant="outline">
            {constraint.parameters.length} selected
          </Badge>
        </div>

        <MultiSelect
          options={parameterOptions}
          value={constraint.parameters}
          onValueChange={handleParametersChange}
          placeholder="Select parameters that control others..."
          maxCount={5}
        />
        <p className="text-muted-foreground text-sm">
          These parameters determine when other parameters become available
        </p>
      </div>

      {/* Affected Parameter Groups */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-base font-medium">
            Dependent Parameter Groups
          </Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addDependencyGroup}
            disabled={getAvailableAffectedParameters().length === 0}
          >
            <Plus className="mr-2 size-4" />
            Add Group
          </Button>
        </div>

        {constraint.affected_parameters &&
        constraint.affected_parameters.length > 0 ? (
          <div className="space-y-3">
            {constraint.affected_parameters.map((group, index) => (
              <Card key={index} className="border-dashed">
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">Group {index + 1}</Badge>
                        <ArrowRight className="text-muted-foreground size-4" />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDependencyGroup(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="size-4" />
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm">Dependent Parameters</Label>
                      <MultiSelect
                        options={affectedParameterOptions}
                        value={group}
                        onValueChange={params =>
                          handleAffectedParametersChange(index, params)
                        }
                        placeholder="Select parameters affected by this group..."
                        maxCount={10}
                      />
                      <p className="text-muted-foreground text-xs">
                        These parameters are only available when the controlling
                        parameters meet certain conditions
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="border-dashed">
            <CardContent className="py-6">
              <div className="text-muted-foreground text-center">
                <GitBranch className="mx-auto mb-2 size-8 opacity-50" />
                <p className="text-sm">No dependency groups defined</p>
                <p className="text-xs">
                  Add groups to specify which parameters depend on others
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Information */}
      <Alert>
        <GitBranch className="size-4" />
        <AlertDescription>
          <strong>How it works:</strong> Dependencies constraints define
          conditional parameter availability. When controlling parameters meet
          certain conditions, the dependent parameters become available for
          selection. This is useful for hierarchical parameter structures where
          some parameters only make sense when others are active.
        </AlertDescription>
      </Alert>

      {/* Validation Warnings */}
      {constraint.parameters.length > 0 &&
        getAvailableAffectedParameters().length === 0 && (
          <Alert variant="destructive">
            <AlertDescription>
              All available parameters are already used as controlling
              parameters. You need some parameters to be dependent on others.
            </AlertDescription>
          </Alert>
        )}

      {/* Common Examples */}
      <Card className="bg-muted/30">
        <CardHeader>
          <CardTitle className="text-sm">Common Use Cases</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div>
            <strong>Catalyst dependency:</strong> Catalyst type only relevant
            when catalyst is enabled
          </div>
          <div>
            <strong>Solvent system:</strong> Co-solvent only available when main
            solvent is organic
          </div>
          <div>
            <strong>Process conditions:</strong> Pressure only matters when
            reaction type is high-pressure
          </div>
          <div>
            <strong>Equipment settings:</strong> Stirring speed only relevant
            when mixing is enabled
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
