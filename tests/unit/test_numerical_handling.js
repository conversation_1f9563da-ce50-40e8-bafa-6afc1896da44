// Test script to verify handling of extremely low/high numerical values
// This test directly tests the numerical handling functions without requiring a web interface

// Mock the functions we modified in run-experiment.tsx
function originalParseFloat(value) {
  return typeof value === "string" ? parseFloat(value) : value;
}

function improvedNumberHandling(value) {
  // This is our improved implementation
  if (typeof value === "string") {
    try {
      // First try using Number() which handles scientific notation better
      const numValue = Number(value);
      if (!isNaN(numValue) && isFinite(numValue)) {
        return numValue;
      } else {
        // If Number() fails, try parseFloat as a fallback
        const floatValue = parseFloat(value);
        if (!isNaN(floatValue) && isFinite(floatValue)) {
          return floatValue;
        }
      }
      return NaN;
    } catch (e) {
      console.error(`Error converting value "${value}" to number:`, e);
      return NaN;
    }
  } else if (typeof value === "number") {
    // Ensure the number is finite
    if (isFinite(value)) {
      return value;
    } else {
      console.warn(`Non-finite number detected: ${value}, keeping as is`);
      return NaN;
    }
  }
  return NaN;
}

// Test cases
const testCases = [
  { value: "1.23", description: "Simple decimal" },
  { value: "0", description: "Zero" },
  { value: "-1.23", description: "Negative decimal" },
  { value: "1e5", description: "Scientific notation (positive exponent)" },
  { value: "1e-5", description: "Scientific notation (negative exponent)" },
  { value: "1e-20", description: "Extremely small scientific notation" },
  { value: "1e20", description: "Extremely large scientific notation" },
  { value: "1.23e-15", description: "Decimal with scientific notation" },
  { value: "NaN", description: "Not a Number string" },
  { value: "Infinity", description: "Infinity string" },
  { value: "-Infinity", description: "Negative Infinity string" },
  { value: 1.23, description: "Number type (decimal)" },
  { value: 0, description: "Number type (zero)" },
  { value: -1.23, description: "Number type (negative)" },
  { value: 1e5, description: "Number type (scientific notation)" },
  { value: 1e-5, description: "Number type (small scientific notation)" },
  { value: 1e-20, description: "Number type (extremely small)" },
  { value: 1e20, description: "Number type (extremely large)" },
  { value: NaN, description: "NaN value" },
  { value: Infinity, description: "Infinity value" },
  { value: -Infinity, description: "Negative Infinity value" },
  { value: "", description: "Empty string" },
  { value: "abc", description: "Non-numeric string" },
  { value: null, description: "Null value" },
  { value: undefined, description: "Undefined value" }
];

// Run tests
console.log("TESTING NUMERICAL VALUE HANDLING");
console.log("===============================");
console.log();

let originalPassCount = 0;
let improvedPassCount = 0;

console.log("| Test Case | Input | Original parseFloat | Improved Handling | Original Result | Improved Result |");
console.log("|-----------|-------|---------------------|-------------------|----------------|----------------|");

for (const test of testCases) {
  const originalResult = originalParseFloat(test.value);
  const improvedResult = improvedNumberHandling(test.value);
  
  // Check if the result is a valid number (not NaN and finite)
  const originalValid = typeof originalResult === "number" && !isNaN(originalResult) && isFinite(originalResult);
  const improvedValid = typeof improvedResult === "number" && !isNaN(improvedResult) && isFinite(improvedResult);
  
  if (originalValid) originalPassCount++;
  if (improvedValid) improvedPassCount++;
  
  // Format the results for display
  const originalDisplay = originalValid ? originalResult.toString() : "❌ " + originalResult;
  const improvedDisplay = improvedValid ? improvedResult.toString() : "❌ " + improvedResult;
  
  console.log(`| ${test.description.padEnd(10)} | ${String(test.value).padEnd(5)} | ${originalValid ? "✅" : "❌"} | ${improvedValid ? "✅" : "❌"} | ${originalDisplay.padEnd(14)} | ${improvedDisplay.padEnd(14)} |`);
}

console.log();
console.log("SUMMARY");
console.log("=======");
console.log(`Original parseFloat: ${originalPassCount}/${testCases.length} test cases passed (${Math.round(originalPassCount/testCases.length*100)}%)`);
console.log(`Improved handling: ${improvedPassCount}/${testCases.length} test cases passed (${Math.round(improvedPassCount/testCases.length*100)}%)`);
console.log();

if (improvedPassCount > originalPassCount) {
  console.log(`✅ IMPROVEMENT CONFIRMED: The improved handling passes ${improvedPassCount - originalPassCount} more test cases than the original implementation.`);
} else if (improvedPassCount === originalPassCount) {
  console.log(`⚠️ NO DIFFERENCE: Both implementations pass the same number of test cases.`);
} else {
  console.log(`❌ REGRESSION: The improved handling passes ${originalPassCount - improvedPassCount} fewer test cases than the original implementation.`);
}

// Specifically test the extreme values that were causing issues
console.log();
console.log("EXTREME VALUES TEST");
console.log("==================");

const extremeTestCases = [
  { value: "1e-20", description: "Extremely small scientific notation (string)" },
  { value: "1e20", description: "Extremely large scientific notation (string)" },
  { value: 1e-20, description: "Extremely small scientific notation (number)" },
  { value: 1e20, description: "Extremely large scientific notation (number)" }
];

for (const test of extremeTestCases) {
  const originalResult = originalParseFloat(test.value);
  const improvedResult = improvedNumberHandling(test.value);
  
  const originalValid = typeof originalResult === "number" && !isNaN(originalResult) && isFinite(originalResult);
  const improvedValid = typeof improvedResult === "number" && !isNaN(improvedResult) && isFinite(improvedResult);
  
  console.log(`Test: ${test.description}`);
  console.log(`Input: ${test.value} (${typeof test.value})`);
  console.log(`Original parseFloat: ${originalResult} (Valid: ${originalValid ? "✅ Yes" : "❌ No"})`);
  console.log(`Improved handling: ${improvedResult} (Valid: ${improvedValid ? "✅ Yes" : "❌ No"})`);
  console.log();
}
