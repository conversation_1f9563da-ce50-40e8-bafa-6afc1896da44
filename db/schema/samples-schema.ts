// db/schema/samples-schema.ts
import {
  pgTable,
  uuid,
  text,
  jsonb,
  timestamp,
  integer
} from "drizzle-orm/pg-core"
import { optimizationsTable } from "./optimizations-schema"

// Samples table for storing generated samples
export const samplesTable = pgTable("samples", {
  id: uuid("id").defaultRandom().primaryKey(),
  optimizationId: uuid("optimization_id")
    .references(() => optimizationsTable.id, { onDelete: "cascade" })
    .notNull(),
  parameters: jsonb("parameters").notNull(), // Complete parameter configuration
  batchId: text("batch_id").notNull(), // Unique ID for batch samples
  sampleIndex: integer("sample_index").notNull(), // Index within the batch
  samplingMethod: text("sampling_method").notNull().default("LHS"), // LHS or random
  seed: integer("seed"), // Random seed used for generation (optional)
  sampleClass: text("sample_class").notNull().default("exploratory"), // exploratory, batch, sequential

  // Status tracking: pending, submitted, discarded
  status: text("status").notNull().default("pending"),

  // Target values entered by the user but not yet submitted
  targetValues: jsonb("target_values"),

  // Metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),

  // Additional fields for tracking user interaction
  lastAccessedAt: timestamp("last_accessed_at"),
  submittedAt: timestamp("submitted_at")
})

// Type definitions
export type InsertSample = typeof samplesTable.$inferInsert
export type SelectSample = typeof samplesTable.$inferSelect
