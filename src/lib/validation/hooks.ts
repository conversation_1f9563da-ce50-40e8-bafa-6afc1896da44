// lib/constraints/hooks.ts
// React hooks for constraint management

import { useState, useCallback, useEffect, useMemo, useRef } from "react"
import {
  Constraint,
  Parameter,
  ValidationResult,
  ConstraintValidationResult,
  GlobalValidationResult,
  SamplingOptions,
  SamplingResult,
  EnhancedValidationResult,
  RealTimeValidationConfig,
  ConstraintAutoComplete,
  ConstraintSyntaxRule
} from "./types"
import { validateSingleConstraint, validateAllConstraints } from "./validation"
import {
  generateConstraintAwareSamples,
  estimateConstraintFeasibility
} from "./sampling"
import { generateConstraintId } from "./utils"

/**
 * Hook for managing constraint validation
 */
export function useConstraintValidation() {
  const [isValidating, setIsValidating] = useState(false)
  const [validationResults, setValidationResults] = useState<
    Record<string, ValidationResult>
  >({})
  const [globalValidation, setGlobalValidation] =
    useState<GlobalValidationResult>({
      valid: true,
      errors: [],
      warnings: []
    })

  const validateSingle = useCallback(
    async (
      constraint: Constraint,
      parameters: Parameter[]
    ): Promise<ValidationResult> => {
      setIsValidating(true)
      try {
        const result = await validateSingleConstraint(constraint, parameters)

        if (constraint.id) {
          setValidationResults(prev => ({
            ...prev,
            [constraint.id!]: result
          }))
        }

        return result
      } finally {
        setIsValidating(false)
      }
    },
    []
  )

  const validateAll = useCallback(
    async (
      constraints: Constraint[],
      parameters: Parameter[]
    ): Promise<GlobalValidationResult> => {
      setIsValidating(true)
      try {
        const result = await validateAllConstraints(constraints, parameters)
        setGlobalValidation(result)

        // Update individual validation results
        const newValidationResults: Record<string, ValidationResult> = {}
        for (const constraint of constraints) {
          if (constraint.id) {
            const constraintError = result.errors.find(
              e => e.constraintId === constraint.id
            )
            newValidationResults[constraint.id] = constraintError || {
              valid: true
            }
          }
        }
        setValidationResults(newValidationResults)

        return result
      } finally {
        setIsValidating(false)
      }
    },
    []
  )

  const clearValidation = useCallback(() => {
    setValidationResults({})
    setGlobalValidation({ valid: true, errors: [], warnings: [] })
  }, [])

  return {
    isValidating,
    validationResults,
    globalValidation,
    validateSingle,
    validateAll,
    clearValidation
  }
}

/**
 * Hook for managing constraints collection
 */
export function useConstraints(initialConstraints: Constraint[] = []) {
  const [constraints, setConstraints] =
    useState<Constraint[]>(initialConstraints)
  const [selectedConstraintId, setSelectedConstraintId] = useState<
    string | null
  >(null)

  const addConstraint = useCallback((constraint: Constraint) => {
    const constraintWithId = {
      ...constraint,
      id: constraint.id || generateConstraintId()
    }
    setConstraints(prev => [...prev, constraintWithId])
    return constraintWithId
  }, [])

  const updateConstraint = useCallback(
    (id: string, updates: Partial<Constraint>) => {
      setConstraints(prev =>
        prev.map(c => (c.id === id ? ({ ...c, ...updates } as Constraint) : c))
      )
    },
    []
  )

  const removeConstraint = useCallback(
    (id: string) => {
      setConstraints(prev => prev.filter(c => c.id !== id))
      if (selectedConstraintId === id) {
        setSelectedConstraintId(null)
      }
    },
    [selectedConstraintId]
  )

  const duplicateConstraint = useCallback(
    (id: string) => {
      const constraint = constraints.find(c => c.id === id)
      if (constraint) {
        const duplicate = {
          ...constraint,
          id: generateConstraintId(),
          name: `${constraint.name} (Copy)`
        }
        setConstraints(prev => [...prev, duplicate])
        return duplicate
      }
      return null
    },
    [constraints]
  )

  const reorderConstraints = useCallback(
    (startIndex: number, endIndex: number) => {
      setConstraints(prev => {
        const result = Array.from(prev)
        const [removed] = result.splice(startIndex, 1)
        result.splice(endIndex, 0, removed)
        return result
      })
    },
    []
  )

  const clearConstraints = useCallback(() => {
    setConstraints([])
    setSelectedConstraintId(null)
  }, [])

  const selectedConstraint = useMemo(
    () => constraints.find(c => c.id === selectedConstraintId) || null,
    [constraints, selectedConstraintId]
  )

  return {
    constraints,
    selectedConstraint,
    selectedConstraintId,
    setConstraints,
    setSelectedConstraintId,
    addConstraint,
    updateConstraint,
    removeConstraint,
    duplicateConstraint,
    reorderConstraints,
    clearConstraints
  }
}

/**
 * Hook for constraint-aware sampling
 */
export function useConstraintAwareSampling() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [samplingResult, setSamplingResult] = useState<SamplingResult | null>(
    null
  )
  const [feasibilityEstimate, setFeasibilityEstimate] = useState<{
    feasibilityRatio: number
    estimatedEfficiency: number
  } | null>(null)

  const generateSamples = useCallback(
    async (
      parameters: Parameter[],
      constraints: Constraint[],
      options: SamplingOptions
    ): Promise<SamplingResult> => {
      setIsGenerating(true)
      try {
        const result = await generateConstraintAwareSamples(
          parameters,
          constraints,
          options
        )
        setSamplingResult(result)
        return result
      } finally {
        setIsGenerating(false)
      }
    },
    []
  )

  const estimateFeasibility = useCallback(
    async (
      parameters: Parameter[],
      constraints: Constraint[],
      sampleSize: number = 100
    ) => {
      try {
        const estimate = await estimateConstraintFeasibility(
          parameters,
          constraints,
          sampleSize
        )
        setFeasibilityEstimate(estimate)
        return estimate
      } catch (error) {
        console.error("Error estimating feasibility:", error)
        return null
      }
    },
    []
  )

  const clearResults = useCallback(() => {
    setSamplingResult(null)
    setFeasibilityEstimate(null)
  }, [])

  return {
    isGenerating,
    samplingResult,
    feasibilityEstimate,
    generateSamples,
    estimateFeasibility,
    clearResults
  }
}

/**
 * Hook for constraint templates
 */
export function useConstraintTemplates() {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [parameterMapping, setParameterMapping] = useState<
    Record<string, string>
  >({})

  const applyTemplate = useCallback(
    (
      templateConstraint: Partial<Constraint>,
      mapping: Record<string, string>
    ): Constraint => {
      const constraint = { ...templateConstraint } as Constraint

      // Apply parameter mapping
      if (constraint.parameters) {
        constraint.parameters = constraint.parameters.map(
          placeholder => mapping[placeholder] || placeholder
        )
      }

      // Update other parameter references based on constraint type
      if (constraint.type === "DiscreteDependenciesConstraint") {
        const depConstraint = constraint as Constraint & {
          type: "DiscreteDependenciesConstraint"
        }
        if (depConstraint.affected_parameters) {
          depConstraint.affected_parameters =
            depConstraint.affected_parameters.map(group =>
              group.map(placeholder => mapping[placeholder] || placeholder)
            )
        }
      }

      // Ensure constraint has an ID
      if (!constraint.id) {
        constraint.id = generateConstraintId()
      }

      return constraint
    },
    []
  )

  const clearTemplate = useCallback(() => {
    setSelectedTemplate(null)
    setParameterMapping({})
  }, [])

  return {
    selectedTemplate,
    parameterMapping,
    setSelectedTemplate,
    setParameterMapping,
    applyTemplate,
    clearTemplate
  }
}

/**
 * Hook for constraint context management
 */
export function useConstraintContext(
  parameters: Parameter[],
  constraints: Constraint[],
  options: { validationEnabled?: boolean; samplingEnabled?: boolean } = {}
) {
  const { validationEnabled = true, samplingEnabled = false } = options

  const validation = useConstraintValidation()
  const sampling = useConstraintAwareSampling()

  // Auto-validate when constraints or parameters change (with debounce)
  useEffect(() => {
    if (validationEnabled && constraints.length > 0 && parameters.length > 0) {
      const timeoutId = setTimeout(() => {
        validation.validateAll(constraints, parameters)
      }, 300) // 300ms debounce

      return () => clearTimeout(timeoutId)
    }
  }, [constraints, parameters, validationEnabled]) // Removed validation.validateAll from deps

  // Estimate feasibility when sampling is enabled (with debounce)
  useEffect(() => {
    if (samplingEnabled && constraints.length > 0 && parameters.length > 0) {
      const timeoutId = setTimeout(() => {
        sampling.estimateFeasibility(parameters, constraints)
      }, 500) // 500ms debounce

      return () => clearTimeout(timeoutId)
    }
  }, [constraints, parameters, samplingEnabled]) // Removed sampling.estimateFeasibility from deps

  const isValid = useMemo(
    () => validation.globalValidation.valid,
    [validation.globalValidation.valid]
  )

  const hasWarnings = useMemo(
    () =>
      validation.globalValidation.warnings &&
      validation.globalValidation.warnings.length > 0,
    [validation.globalValidation.warnings]
  )

  const constraintCount = useMemo(
    () => constraints.length,
    [constraints.length]
  )

  const feasibilityRatio = useMemo(
    () => sampling.feasibilityEstimate?.feasibilityRatio || 1.0,
    [sampling.feasibilityEstimate?.feasibilityRatio]
  )

  return {
    // Validation
    isValidating: validation.isValidating,
    isValid,
    hasWarnings,
    validationResults: validation.validationResults,
    globalValidation: validation.globalValidation,
    validateAll: validation.validateAll,
    validateSingle: validation.validateSingle,
    clearValidation: validation.clearValidation,

    // Sampling
    isGeneratingSamples: sampling.isGenerating,
    samplingResult: sampling.samplingResult,
    feasibilityEstimate: sampling.feasibilityEstimate,
    feasibilityRatio,
    generateSamples: sampling.generateSamples,
    estimateFeasibility: sampling.estimateFeasibility,
    clearSamplingResults: sampling.clearResults,

    // Context info
    constraintCount,
    parameterCount: parameters.length,
    hasConstraints: constraints.length > 0,
    hasParameters: parameters.length > 0
  }
}

/**
 * Hook for real-time constraint validation with enhanced features
 */
export function useRealTimeConstraintValidation(
  config: RealTimeValidationConfig = {
    enabled: true,
    debounceMs: 300,
    validateOnType: true,
    validateOnBlur: true,
    showInlineErrors: true,
    showSuggestions: true,
    enableFeasibilityCheck: true,
    feasibilitySampleSize: 100,
    enableInteractionDetection: true,
    maxValidationTime: 5000
  }
) {
  const [validationResults, setValidationResults] = useState<
    Record<string, EnhancedValidationResult>
  >({})
  const [isValidating, setIsValidating] = useState(false)
  const [autoCompleteResults, setAutoCompleteResults] =
    useState<ConstraintAutoComplete | null>(null)
  const [syntaxErrors, setSyntaxErrors] = useState<Record<string, any[]>>({})

  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
  const validationAbortControllerRef = useRef<AbortController | null>(null)

  /**
   * Enhanced validation with real-time features
   */
  const validateConstraintRealTime = useCallback(
    async (
      constraint: Constraint,
      parameters: Parameter[],
      constraintText?: string
    ): Promise<EnhancedValidationResult> => {
      const startTime = Date.now()

      try {
        // Abort previous validation if still running
        if (validationAbortControllerRef.current) {
          validationAbortControllerRef.current.abort()
        }

        validationAbortControllerRef.current = new AbortController()
        const signal = validationAbortControllerRef.current.signal

        setIsValidating(true)

        // Basic validation first
        const basicResult = await validateSingleConstraint(
          constraint,
          parameters
        )

        if (signal.aborted) return basicResult as EnhancedValidationResult

        // Enhanced validation features
        const warnings = await detectConstraintWarnings(constraint, parameters)
        const suggestions = await generateConstraintSuggestions(
          constraint,
          constraintText || ""
        )
        const interactions = config.enableInteractionDetection
          ? await detectConstraintInteractions(constraint, parameters)
          : []

        let feasibilityScore = 100
        if (config.enableFeasibilityCheck && basicResult.valid) {
          feasibilityScore = await calculateFeasibilityScore(
            constraint,
            parameters,
            config.feasibilitySampleSize
          )
        }

        const enhancedResult: EnhancedValidationResult = {
          ...basicResult,
          warnings,
          suggestions,
          feasibilityScore,
          interactionIssues: interactions,
          performanceMetrics: {
            validationTime: Date.now() - startTime
          }
        }

        if (constraint.id) {
          setValidationResults(prev => ({
            ...prev,
            [constraint.id!]: enhancedResult
          }))
        }

        return enhancedResult
      } catch (error) {
        console.error("Real-time validation error:", error)
        return {
          valid: false,
          error: error instanceof Error ? error.message : "Validation failed",
          warnings: [],
          suggestions: [],
          feasibilityScore: 0,
          interactionIssues: [],
          performanceMetrics: {
            validationTime: Date.now() - startTime
          }
        }
      } finally {
        setIsValidating(false)
      }
    },
    [config]
  )

  /**
   * Debounced validation for real-time typing
   */
  const validateWithDebounce = useCallback(
    (
      constraint: Constraint,
      parameters: Parameter[],
      constraintText?: string
    ) => {
      if (!config.enabled || !config.validateOnType) return

      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }

      debounceTimerRef.current = setTimeout(() => {
        validateConstraintRealTime(constraint, parameters, constraintText)
      }, config.debounceMs)
    },
    [config, validateConstraintRealTime]
  )

  /**
   * Immediate validation for blur events
   */
  const validateOnBlur = useCallback(
    (
      constraint: Constraint,
      parameters: Parameter[],
      constraintText?: string
    ) => {
      if (!config.enabled || !config.validateOnBlur) return

      // Clear debounce timer and validate immediately
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }

      validateConstraintRealTime(constraint, parameters, constraintText)
    },
    [config, validateConstraintRealTime]
  )

  /**
   * Generate auto-complete suggestions
   */
  const generateAutoComplete = useCallback(
    (
      currentText: string,
      cursorPosition: number,
      parameters: Parameter[]
    ): ConstraintAutoComplete => {
      const words = currentText.split(/\s+/)
      const currentWord = getCurrentWord(currentText, cursorPosition)

      const suggestions = []

      // Parameter suggestions
      for (const param of parameters) {
        if (param.name.toLowerCase().includes(currentWord.toLowerCase())) {
          suggestions.push({
            text: param.name,
            displayText: `${param.name} (${param.type})`,
            type: "parameter" as const,
            description: `Parameter: ${param.name}`,
            insertText: param.name,
            priority: 10
          })
        }
      }

      // Operator suggestions
      const operators = [
        ">=",
        "<=",
        "==",
        ">",
        "<",
        "+",
        "-",
        "*",
        "/",
        "AND",
        "OR"
      ]
      for (const op of operators) {
        if (op.toLowerCase().includes(currentWord.toLowerCase())) {
          suggestions.push({
            text: op,
            displayText: op,
            type: "operator" as const,
            description: `Operator: ${op}`,
            insertText: op,
            priority: 8
          })
        }
      }

      // Function suggestions
      const functions = ["sum", "max", "min", "abs", "sqrt", "log"]
      for (const func of functions) {
        if (func.toLowerCase().includes(currentWord.toLowerCase())) {
          suggestions.push({
            text: func,
            displayText: `${func}()`,
            type: "function" as const,
            description: `Function: ${func}`,
            insertText: `${func}()`,
            priority: 6
          })
        }
      }

      return {
        suggestions: suggestions.sort((a, b) => b.priority - a.priority),
        context: {
          currentPosition: cursorPosition,
          currentWord,
          availableParameters: parameters.map(p => p.name),
          availableFunctions: functions
        }
      }
    },
    []
  )

  /**
   * Validate syntax in real-time
   */
  const validateSyntax = useCallback(
    (
      constraintId: string,
      constraintText: string,
      syntaxRules: ConstraintSyntaxRule[]
    ) => {
      const errors: Array<{
        ruleId: string
        message: string
        severity: "error" | "warning"
        category: "syntax" | "semantic" | "style"
        autoFix?: {
          enabled: boolean
          replacement: string
          description: string
        }
      }> = []

      for (const rule of syntaxRules) {
        if (!rule.pattern.test(constraintText)) {
          errors.push({
            ruleId: rule.id,
            message: rule.errorMessage,
            severity: rule.severity,
            category: rule.category,
            autoFix: rule.autoFix
          })
        }
      }

      setSyntaxErrors(prev => ({
        ...prev,
        [constraintId]: errors
      }))

      return errors
    },
    []
  )

  /**
   * Clear validation results
   */
  const clearValidationResults = useCallback(() => {
    setValidationResults({})
    setSyntaxErrors({})
    setAutoCompleteResults(null)
  }, [])

  /**
   * Get validation result for a specific constraint
   */
  const getValidationResult = useCallback(
    (constraintId: string): EnhancedValidationResult | null => {
      return validationResults[constraintId] || null
    },
    [validationResults]
  )

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
      if (validationAbortControllerRef.current) {
        validationAbortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    // State
    validationResults,
    isValidating,
    autoCompleteResults,
    syntaxErrors,

    // Actions
    validateConstraintRealTime,
    validateWithDebounce,
    validateOnBlur,
    generateAutoComplete,
    validateSyntax,
    clearValidationResults,
    getValidationResult
  }
}

// Helper functions
function getCurrentWord(text: string, position: number): string {
  const beforeCursor = text.substring(0, position)
  const afterCursor = text.substring(position)

  const wordStart = beforeCursor.search(/\S+$/)
  const wordEnd = afterCursor.search(/\s/)

  const start = wordStart === -1 ? position : wordStart
  const end = wordEnd === -1 ? text.length : position + wordEnd

  return text.substring(start, end)
}

async function detectConstraintWarnings(
  constraint: Constraint,
  parameters: Parameter[]
): Promise<any[]> {
  // Placeholder implementation - would contain actual warning detection logic
  return []
}

async function generateConstraintSuggestions(
  constraint: Constraint,
  constraintText: string
): Promise<any[]> {
  // Placeholder implementation - would contain actual suggestion generation logic
  return []
}

async function detectConstraintInteractions(
  constraint: Constraint,
  parameters: Parameter[]
): Promise<any[]> {
  // Placeholder implementation - would contain actual interaction detection logic
  return []
}

async function calculateFeasibilityScore(
  constraint: Constraint,
  parameters: Parameter[],
  sampleSize: number
): Promise<number> {
  // Placeholder implementation - would contain actual feasibility calculation
  return 85 // Mock score
}
