"use client"

import { useEffect, useRef, useState } from "react"
import dynamic from "next/dynamic"
import { motion, AnimatePresence } from "framer-motion"
import { Play, Pause, RefreshCw, ChevronDown, ChevronUp } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ackley,
  rosenbrock,
  himmelblau,
  rastrigin,
  eggholder,
  generateFunctionGrid,
  findMinimum
} from "@/lib/test-functions"

import { cn } from "@/lib/utils"

// Dynamically import Plotly to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

// Define test function options
const testFunctions = [
  {
    id: "ackley",
    name: "<PERSON><PERSON><PERSON>",
    func: ackley,
    range: [-5, 5] as [number, number]
  },
  {
    id: "rosenbrock",
    name: "<PERSON><PERSON><PERSON>",
    func: rosenbrock,
    range: [-2, 2] as [number, number]
  },
  {
    id: "himmelblau",
    name: "Himmelblau",
    func: himmelblau,
    range: [-5, 5] as [number, number]
  },
  {
    id: "rastrigin",
    name: "Rastrigin",
    func: rastrigin,
    range: [-5.12, 5.12] as [number, number]
  },
  {
    id: "eggholder",
    name: "Eggholder",
    func: eggholder,
    range: [-512, 512] as [number, number]
  }
]

// Define color schemes
const colorSchemes = [
  { id: "viridis", name: "Viridis" },
  { id: "plasma", name: "Plasma" },
  { id: "inferno", name: "Inferno" },
  { id: "magma", name: "Magma" },
  { id: "cividis", name: "Cividis" },
  { id: "rainbow", name: "Rainbow" }
]

// Simulated Bayesian optimization points
interface OptimizationPoint {
  x: number
  y: number
  z: number
  iteration: number
  isNextPoint?: boolean
  isOptimum?: boolean
}

// Function to simulate Bayesian optimization steps
function simulateBayesianOptimization(
  func: (x: number, y: number) => number,
  range: [number, number],
  iterations: number = 10
): OptimizationPoint[] {
  const points: OptimizationPoint[] = []

  // Start with 5 random points
  for (let i = 0; i < 5; i++) {
    const x = range[0] + Math.random() * (range[1] - range[0])
    const y = range[0] + Math.random() * (range[1] - range[0])
    const z = func(x, y)
    points.push({ x, y, z, iteration: 0 })
  }

  // Simulate optimization steps
  // In a real implementation, this would use actual Bayesian optimization
  // For now, we'll just add points that get progressively closer to the optimum
  const { x: xMin, y: yMin } = findMinimum(
    generateFunctionGrid(func, range, range, 100).z,
    generateFunctionGrid(func, range, range, 100).x,
    generateFunctionGrid(func, range, range, 100).y
  )

  for (let i = 1; i <= iterations; i++) {
    // Simulate exploration-exploitation tradeoff
    // Early iterations explore more, later iterations exploit more
    const explorationFactor = Math.max(0.1, 1 - i / iterations)

    // Add some randomness but gradually move toward the optimum
    const x =
      xMin + (Math.random() - 0.5) * (range[1] - range[0]) * explorationFactor
    const y =
      yMin + (Math.random() - 0.5) * (range[1] - range[0]) * explorationFactor
    const z = func(x, y)

    points.push({ x, y, z, iteration: i })
  }

  // Mark the best point found
  let bestPoint = points[0]
  for (const point of points) {
    if (point.z < bestPoint.z) {
      bestPoint = point
    }
  }
  bestPoint.isOptimum = true

  // Mark the last point as the next suggested point
  points[points.length - 1].isNextPoint = true

  return points
}

interface OptimizationVisualizationProps {
  className?: string
}

export function OptimizationVisualization({
  className
}: OptimizationVisualizationProps) {
  // State for the visualization
  const selectedFunction = testFunctions[0] // Always use Ackley function
  const colorScheme = "Blues" // Fixed color scheme
  const [viewMode, setViewMode] = useState<"3d" | "contour">("3d")
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentIteration, setCurrentIteration] = useState(0)

  // Refs for animation
  const containerRef = useRef<HTMLDivElement>(null)
  const plotRef = useRef<HTMLDivElement>(null)

  // Generate the function grid for visualization
  const { x, y, z } = generateFunctionGrid(
    selectedFunction.func,
    selectedFunction.range,
    selectedFunction.range,
    50
  )

  // Simulate Bayesian optimization
  const [optimizationPoints, setOptimizationPoints] = useState<
    OptimizationPoint[]
  >([])

  // Update optimization points when function changes
  useEffect(() => {
    setOptimizationPoints(
      simulateBayesianOptimization(
        selectedFunction.func,
        selectedFunction.range
      )
    )
    setCurrentIteration(0)
    setIsPlaying(false)
  }, [selectedFunction])

  // Handle play/pause
  const togglePlayPause = () => {
    if (isPlaying) {
      setIsPlaying(false)
    } else {
      setIsPlaying(true)

      // If we're at the end, restart
      if (currentIteration >= 10) {
        setCurrentIteration(0)
      }
    }
  }

  // Animation effect
  useEffect(() => {
    if (!isPlaying) return

    const interval = setInterval(() => {
      setCurrentIteration(prev => {
        if (prev >= 10) {
          setIsPlaying(false)
          return prev
        }
        return prev + 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isPlaying])

  // Filter points based on current iteration
  const visiblePoints = optimizationPoints.filter(
    point => point.iteration <= currentIteration
  )

  // Reset animation
  const resetAnimation = () => {
    setCurrentIteration(0)
    setIsPlaying(false)
  }

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative w-full overflow-hidden rounded-lg border shadow-xl",
        className
      )}
    >
      {/* Main visualization area */}
      <div
        className="relative w-full bg-gradient-to-br from-gray-50 to-gray-100"
        style={{ height: "400px" }}
      >
        <div ref={plotRef} className="size-full" data-animation="surface">
          {viewMode === "3d" ? (
            <Plot
              data={[
                // Surface plot
                {
                  type: "surface",
                  x,
                  y,
                  z,
                  colorscale: colorScheme,
                  opacity: 0.9,
                  contours: {
                    z: {
                      show: true,
                      usecolormap: true,
                      highlightcolor: "#fff",
                      project: { z: true }
                    }
                  },
                  hovertemplate:
                    "x: %{x}<br>" + "y: %{y}<br>" + "z: %{z}<extra></extra>"
                },
                // Optimization points
                {
                  type: "scatter3d",
                  mode: "markers",
                  x: visiblePoints.map(p => p.x),
                  y: visiblePoints.map(p => p.y),
                  z: visiblePoints.map(p => p.z),
                  marker: {
                    size: visiblePoints.map(
                      p =>
                        p.isOptimum
                          ? 12 // Larger size for optimal point
                          : p.isNextPoint
                            ? 10 // Medium-large for next point
                            : 8 // Standard size for other points
                    ),
                    color: visiblePoints.map(p =>
                      p.isOptimum
                        ? "#10b981"
                        : p.isNextPoint
                          ? "#f59e0b"
                          : "#3b82f6"
                    ),
                    opacity: 0.9,
                    line: {
                      width: 1,
                      color: "white"
                    },
                    symbol: visiblePoints.map(p =>
                      p.isOptimum
                        ? "diamond"
                        : p.isNextPoint
                          ? "star"
                          : "circle"
                    )
                  },
                  hovertemplate:
                    "x: %{x}<br>" +
                    "y: %{y}<br>" +
                    "z: %{z}<br>" +
                    "Iteration: %{text}<extra></extra>",
                  text: visiblePoints.map(p => p.iteration.toString())
                }
              ]}
              layout={{
                title: "",
                autosize: true,
                margin: { l: 10, r: 10, b: 10, t: 10 },
                paper_bgcolor: "rgba(0,0,0,0)",
                plot_bgcolor: "rgba(0,0,0,0)",
                scene: {
                  xaxis: {
                    title: "x",
                    titlefont: { size: 12 },
                    tickfont: { size: 10 }
                  },
                  yaxis: {
                    title: "y",
                    titlefont: { size: 12 },
                    tickfont: { size: 10 }
                  },
                  zaxis: {
                    title: "f(x,y)",
                    titlefont: { size: 12 },
                    tickfont: { size: 10 }
                  },
                  camera: {
                    eye: { x: 1.5, y: 1.5, z: 1 }
                  }
                },
                font: {
                  family: "Inter, system-ui, sans-serif",
                  size: 12
                }
              }}
              config={{
                displayModeBar: false,
                responsive: true
              }}
              style={{ width: "100%", height: "100%" }}
            />
          ) : (
            <Plot
              data={[
                // Contour plot
                {
                  type: "contour",
                  x,
                  y,
                  z,
                  colorscale: colorScheme,
                  contours: {
                    coloring: "heatmap",
                    showlabels: true
                  },
                  hovertemplate:
                    "x: %{x}<br>" + "y: %{y}<br>" + "z: %{z}<extra></extra>"
                },
                // Optimization points
                {
                  type: "scatter",
                  mode: "markers",
                  x: visiblePoints.map(p => p.x),
                  y: visiblePoints.map(p => p.y),
                  marker: {
                    size: visiblePoints.map(
                      p =>
                        p.isOptimum
                          ? 16 // Larger size for optimal point
                          : p.isNextPoint
                            ? 14 // Medium-large for next point
                            : 12 // Standard size for other points
                    ),
                    color: visiblePoints.map(p =>
                      p.isOptimum
                        ? "#10b981"
                        : p.isNextPoint
                          ? "#f59e0b"
                          : "#3b82f6"
                    ),
                    opacity: 0.9,
                    line: {
                      width: 1.5,
                      color: "white"
                    },
                    symbol: visiblePoints.map(p =>
                      p.isOptimum
                        ? "diamond"
                        : p.isNextPoint
                          ? "star"
                          : "circle"
                    )
                  },
                  hovertemplate:
                    "x: %{x}<br>" +
                    "y: %{y}<br>" +
                    "z: %{z}<br>" +
                    "Iteration: %{text}<extra></extra>",
                  text: visiblePoints.map(p => p.iteration.toString())
                }
              ]}
              layout={{
                title: "",
                autosize: true,
                margin: { l: 40, r: 10, b: 40, t: 10 },
                paper_bgcolor: "rgba(0,0,0,0)",
                plot_bgcolor: "rgba(0,0,0,0)",
                xaxis: {
                  title: "x",
                  titlefont: { size: 12 },
                  tickfont: { size: 10 }
                },
                yaxis: {
                  title: "y",
                  titlefont: { size: 12 },
                  tickfont: { size: 10 }
                },
                font: {
                  family: "Inter, system-ui, sans-serif",
                  size: 12
                }
              }}
              config={{
                displayModeBar: false,
                responsive: true
              }}
              style={{ width: "100%", height: "100%" }}
            />
          )}
        </div>
      </div>

      {/* Controls and info section below the plot */}
      <div className="border-t bg-white p-3 sm:p-4">
        {/* Explanation text - always visible */}
        <div className="mb-3 rounded-md bg-gray-50 p-2 text-xs sm:mb-4 sm:p-3 sm:text-sm">
          <p>
            This visualization shows how Bayesian optimization efficiently
            navigates the complex Ackley function to find the global minimum in
            minimal experiments. The algorithm balances exploration and
            exploitation to converge on the optimal solution.
          </p>
        </div>

        {/* Simplified Controls */}
        <div className="flex flex-wrap items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="size-8 sm:size-9"
              onClick={togglePlayPause}
            >
              {isPlaying ? (
                <Pause className="size-3 sm:size-4" />
              ) : (
                <Play className="size-3 sm:size-4" />
              )}
            </Button>

            <Button
              variant="outline"
              size="icon"
              className="size-8 sm:size-9"
              onClick={resetAnimation}
            >
              <RefreshCw className="size-3 sm:size-4" />
            </Button>

            <div className="ml-2 text-xs font-medium sm:text-sm">
              Iteration: {currentIteration} / 10
            </div>
          </div>

          <div className="flex items-center">
            <span className="mr-2 text-xs font-medium sm:text-sm">
              View Mode:
            </span>
            <div className="flex rounded-md border">
              <Button
                variant={viewMode === "3d" ? "default" : "ghost"}
                size="sm"
                className="rounded-r-none text-xs sm:text-sm"
                onClick={() => setViewMode("3d")}
              >
                3D Surface
              </Button>
              <Button
                variant={viewMode === "contour" ? "default" : "ghost"}
                size="sm"
                className="rounded-l-none text-xs sm:text-sm"
                onClick={() => setViewMode("contour")}
              >
                Contour
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
