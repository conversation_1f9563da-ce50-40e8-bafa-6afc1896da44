#!/usr/bin/env node

// <PERSON>ript to analyze the backend measurements and identify the source of the issue
// Usage: node scripts/analyze-backend-measurements.js

require('dotenv').config({ path: '.env.local' });

const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  API_KEY: process.env.API_KEY || 'your-api-key-here'
};

async function analyzeBackendMeasurements() {
  console.log('🔬 ANALYZING BACKEND MEASUREMENTS');
  console.log('=================================');
  
  const optimizerId = 'user_30EFn0yPauqlMMorn5hfsn3of5X_Test_CRUD_Filtering2_1757675394674';
  
  try {
    // Get measurements directly from backend
    console.log('📤 Getting measurements from backend...');
    const response = await fetch(`${API_CONFIG.BASE_URL}/optimizations/${optimizerId}/measurements`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_CONFIG.API_KEY
      }
    });
    
    if (!response.ok) {
      console.log(`❌ Failed to get measurements: ${response.status}`);
      return;
    }
    
    const data = await response.json();
    const measurements = data.measurements || [];
    
    console.log(`📊 Backend has ${measurements.length} measurements`);
    
    // Analyze each measurement
    console.log('\n🔍 DETAILED MEASUREMENT ANALYSIS:');
    
    const databaseMeasurements = [];
    const extraMeasurements = [];
    
    measurements.forEach((measurement, index) => {
      console.log(`\n${index + 1}. Measurement Analysis:`);
      
      // Check if this looks like a database measurement (has FitNr)
      const hasParameters = measurement.parameters || measurement;
      const batchNr = measurement.BatchNr || measurement.batch_nr;
      const fitNr = measurement.FitNr || measurement.fit_nr;
      
      console.log(`   BatchNr: ${batchNr}, FitNr: ${fitNr}`);
      
      if (fitNr !== null && fitNr !== undefined) {
        databaseMeasurements.push(measurement);
        console.log(`   ✅ DATABASE MEASUREMENT (has FitNr)`);
      } else {
        extraMeasurements.push(measurement);
        console.log(`   ❓ EXTRA MEASUREMENT (no FitNr)`);
      }
      
      // Analyze parameter values for NaN/infinite issues
      const params = hasParameters;
      console.log(`   Parameters:`, params);
      
      // Check each parameter for potential issues
      Object.entries(params).forEach(([key, value]) => {
        if (typeof value === 'number') {
          const isNaN = Number.isNaN(value);
          const isFinite = Number.isFinite(value);
          const isVerySmall = Math.abs(value) < 1e-15;
          
          console.log(`     ${key}: ${value}`);
          console.log(`       - Type: ${typeof value}`);
          console.log(`       - Is NaN: ${isNaN}`);
          console.log(`       - Is Finite: ${isFinite}`);
          console.log(`       - Very small: ${isVerySmall}`);
          
          if (isVerySmall && isFinite && !isNaN) {
            console.log(`       - 🔬 VERY SMALL BUT VALID: ${value.toExponential()}`);
          }
          
          if (!isFinite || isNaN) {
            console.log(`       - ❌ ACTUAL PROBLEM: ${isNaN ? 'NaN' : 'Infinite'}`);
          }
        }
      });
    });
    
    console.log(`\n📈 SUMMARY:`);
    console.log(`  - Total measurements: ${measurements.length}`);
    console.log(`  - Database measurements (with FitNr): ${databaseMeasurements.length}`);
    console.log(`  - Extra measurements (no FitNr): ${extraMeasurements.length}`);
    
    if (extraMeasurements.length > 0) {
      console.log(`\n🚨 EXTRA MEASUREMENTS ANALYSIS:`);
      console.log(`These ${extraMeasurements.length} measurements are NOT in the database:`);
      
      extraMeasurements.forEach((measurement, index) => {
        console.log(`\n  ${index + 1}. Extra Measurement:`);
        console.log(`     BatchNr: ${measurement.BatchNr}`);
        
        const params = measurement.parameters || measurement;
        
        // Look for very small values that might be causing the issue
        const verySmallValues = [];
        Object.entries(params).forEach(([key, value]) => {
          if (typeof value === 'number' && Math.abs(value) < 1e-10 && Number.isFinite(value)) {
            verySmallValues.push({ param: key, value: value });
          }
        });
        
        if (verySmallValues.length > 0) {
          console.log(`     🔬 Very small values found:`);
          verySmallValues.forEach(({ param, value }) => {
            console.log(`       ${param}: ${value} (${value.toExponential()})`);
          });
          console.log(`     ❌ These might be incorrectly flagged as NaN/infinite!`);
        }
      });
    }
    
    // Test the filtering with a simple bounds change to see the exact issue
    console.log(`\n🧪 TESTING SIMPLE BOUNDS CHANGE:`);
    console.log(`Testing x1 bounds change from [0, 100] to [50, 100]...`);
    
    const testResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        parameter_bounds: {
          x1: [50, 100]
        },
        preview_only: true
      })
    });
    
    if (testResponse.ok) {
      const testData = await testResponse.json();
      console.log(`📥 Test result: ${testData.valid_count} retained, ${testData.dropped_count} filtered`);
      
      if (testData.dropped_measurements) {
        const nanInfiniteCount = testData.dropped_measurements.filter(m => 
          m.exclusion_reason && m.exclusion_reason.includes('NaN or infinite')
        ).length;
        
        const boundsViolationCount = testData.dropped_measurements.filter(m => 
          m.exclusion_reason && m.exclusion_reason.includes('outside bounds')
        ).length;
        
        console.log(`  - Filtered for NaN/infinite: ${nanInfiniteCount}`);
        console.log(`  - Filtered for bounds violation: ${boundsViolationCount}`);
        
        if (nanInfiniteCount > 0) {
          console.log(`\n❌ CONFIRMED BUG: ${nanInfiniteCount} measurements incorrectly flagged as NaN/infinite`);
          
          // Show examples of the incorrectly flagged measurements
          const incorrectlyFlagged = testData.dropped_measurements.filter(m => 
            m.exclusion_reason && m.exclusion_reason.includes('NaN or infinite')
          ).slice(0, 3);
          
          console.log(`\n🔍 EXAMPLES OF INCORRECTLY FLAGGED MEASUREMENTS:`);
          incorrectlyFlagged.forEach((measurement, index) => {
            console.log(`\n  ${index + 1}. Measurement:`);
            Object.entries(measurement).forEach(([key, value]) => {
              if (key !== 'exclusion_reason' && typeof value === 'number') {
                console.log(`     ${key}: ${value} (${typeof value})`);
                if (Math.abs(value) < 1e-10 && Number.isFinite(value)) {
                  console.log(`       🔬 Very small but valid: ${value.toExponential()}`);
                }
              }
            });
            console.log(`     Exclusion reason: ${measurement.exclusion_reason}`);
          });
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error analyzing measurements:', error);
  }
}

// Run the analysis
analyzeBackendMeasurements().catch(console.error);
