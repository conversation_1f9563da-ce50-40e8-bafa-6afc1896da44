"use server"

import { auth } from "@clerk/nextjs/server"
import { db } from "@/db/db"
import { profilesTable } from "@/db/schema/profiles-schema"
import { eq } from "drizzle-orm"
import {
  createProfileAction,
  getProfileByUserIdAction,
  updateProfileAction
} from "@/actions/db/profiles-actions"
import { ActionState } from "@/types"

/**
 * Initializes a trial for a new user
 * @param userId The user ID to initialize the trial for
 * @param trialDays The number of days for the trial (default: 30)
 * @param isAcademic Whether this is an academic trial (default: false)
 */
export async function initializeTrialAction(
  userId: string,
  trialDays: number = 30,
  isAcademic: boolean = false
): Promise<ActionState<{ trialEndsAt: Date }>> {
  try {
    console.log(`Initializing ${trialDays}-day trial for user: ${userId}${isAcademic ? ' (Academic)' : ''}`)

    // Check if user already exists
    let existingProfile;
    try {
      existingProfile = await getProfileByUserIdAction(userId)
    } catch (dbError) {
      console.error(`Database error when checking profile in initializeTrialAction: ${dbError}`)
      return {
        isSuccess: false,
        message: "Database error when checking profile"
      }
    }

    // If user already has a profile with trial data, don't reinitialize
    // unless we're upgrading from a standard trial to an academic trial
    if (existingProfile.isSuccess &&
        existingProfile.data &&
        existingProfile.data.trialStartedAt) {

      // If this is an academic upgrade and the user has a standard trial, allow the upgrade
      if (isAcademic && trialDays > 30) {
        console.log(`Upgrading standard trial to ${trialDays}-day academic trial for user ${userId}`)
        // Continue with the initialization (don't return early)
      } else {
        console.log(`User ${userId} already has trial data`)
        return {
          isSuccess: false,
          message: "Trial has already been initialized for this user"
        }
      }
    }

    // If user has a Stripe subscription, don't initialize trial
    if (existingProfile.isSuccess &&
        existingProfile.data &&
        existingProfile.data.stripeSubscriptionId) {
      console.log(`User ${userId} has a Stripe subscription, skipping trial`)
      return {
        isSuccess: false,
        message: "User has an active subscription, trial not needed"
      }
    }

    const trialStartDate = new Date()
    const trialEndDate = new Date(trialStartDate)
    trialEndDate.setDate(trialEndDate.getDate() + trialDays) // Custom trial duration

    console.log(`Setting trial period: ${trialStartDate.toISOString()} to ${trialEndDate.toISOString()}`)

    // If user exists but doesn't have trial data, update their profile
    if (existingProfile.isSuccess && existingProfile.data) {
      console.log(`Updating existing profile for user ${userId} with trial data`)
      const result = await updateProfileAction(userId, {
        membership: "pro", // Give pro access during trial
        trialStartedAt: trialStartDate,
        trialEndsAt: trialEndDate,
        hasTrialExpired: "false"
      })

      if (!result.isSuccess) {
        console.error(`Failed to update profile: ${result.message}`)
        throw new Error(`Failed to update profile: ${result.message}`)
      }

      console.log(`Trial initialized successfully for existing user ${userId}`)
      return {
        isSuccess: true,
        message: "Trial initialized successfully",
        data: { trialEndsAt: trialEndDate }
      }
    }

    // If user doesn't exist, create a new profile with trial data
    console.log(`Creating new profile for user ${userId} with trial data`)
    const result = await createProfileAction({
      userId,
      membership: "pro", // Give pro access during trial
      trialStartedAt: trialStartDate,
      trialEndsAt: trialEndDate,
      hasTrialExpired: "false"
    })

    if (!result.isSuccess) {
      console.error(`Failed to create profile: ${result.message}`)
      throw new Error(`Failed to create profile: ${result.message}`)
    }

    console.log(`Trial initialized successfully for new user ${userId}`)
    return {
      isSuccess: true,
      message: "Trial initialized successfully",
      data: { trialEndsAt: trialEndDate }
    }
  } catch (error) {
    console.error("Error initializing trial:", error)
    return {
      isSuccess: false,
      message: `Failed to initialize trial: ${error instanceof Error ? error.message : "Unknown error"}`
    }
  }
}

/**
 * Checks if a user's trial has expired and updates their status if needed
 */
export async function checkTrialStatusAction(userId: string): Promise<ActionState<{
  isActive: boolean,
  daysLeft?: number,
  trialEndsAt?: Date
}>> {
  try {
    let profileResult;
    try {
      profileResult = await getProfileByUserIdAction(userId)

      if (!profileResult.isSuccess || !profileResult.data) {
        // Only return an error if the profile truly doesn't exist
        if (profileResult.message === "Profile not found") {
          return {
            isSuccess: false,
            message: "User profile not found"
          }
        } else {
          // For database errors, return a temporary active trial
          console.log(`Database error in checkTrialStatusAction: ${profileResult.message}`)
          return {
            isSuccess: true,
            message: "Temporary trial status due to database error",
            data: {
              isActive: true,
              daysLeft: 30,
              trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
            }
          }
        }
      }
    } catch (dbError) {
      console.error(`Database error in checkTrialStatusAction: ${dbError}`)
      // For exceptions, return a temporary active trial
      return {
        isSuccess: true,
        message: "Temporary trial status due to database exception",
        data: {
          isActive: true,
          daysLeft: 30,
          trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        }
      }
    }

    const profile = profileResult.data

    // If user has a Stripe subscription, they're not on trial
    if (profile.stripeSubscriptionId) {
      return {
        isSuccess: true,
        message: "User has an active subscription",
        data: { isActive: false }
      }
    }

    // If user doesn't have trial data, they're not on trial
    if (!profile.trialStartedAt || !profile.trialEndsAt) {
      return {
        isSuccess: true,
        message: "User has no trial data",
        data: { isActive: false }
      }
    }

    const now = new Date()
    const trialEndDate = new Date(profile.trialEndsAt)

    // Check if trial has expired
    if (now > trialEndDate && profile.hasTrialExpired !== "true") {
      // Update profile to mark trial as expired and downgrade to free
      await updateProfileAction(userId, {
        membership: "free",
        hasTrialExpired: "true"
      })

      return {
        isSuccess: true,
        message: "Trial has expired",
        data: { isActive: false }
      }
    }

    // If trial is still active, calculate days left
    if (now <= trialEndDate) {
      // Calculate days left - use Math.max to ensure we don't get negative values
      // and Math.round to get the most accurate day count
      const daysLeft = Math.max(0, Math.round(
        (trialEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      ))

      // Removed console.log to prevent hydration errors

      return {
        isSuccess: true,
        message: "Trial is active",
        data: {
          isActive: true,
          daysLeft,
          trialEndsAt: trialEndDate
        }
      }
    }

    // Trial has already been marked as expired
    return {
      isSuccess: true,
      message: "Trial has expired",
      data: { isActive: false }
    }
  } catch (error) {
    console.error("Error checking trial status:", error)
    return {
      isSuccess: false,
      message: `Failed to check trial status: ${error instanceof Error ? error.message : "Unknown error"}`
    }
  }
}

/**
 * Batch job to check and update all expired trials
 * This should be run daily via a scheduled job
 */
export async function updateExpiredTrialsAction(): Promise<ActionState<{ updatedCount: number }>> {
  try {
    const now = new Date()

    // Find all profiles with active trials that have expired
    const expiredTrials = await db.query.profiles.findMany({
      where: (profiles) => {
        return eq(profiles.hasTrialExpired, "false")
      }
    })

    let updatedCount = 0

    // Update each expired trial
    for (const profile of expiredTrials) {
      if (profile.trialEndsAt && new Date(profile.trialEndsAt) < now) {
        await updateProfileAction(profile.userId, {
          membership: "free",
          hasTrialExpired: "true"
        })
        updatedCount++
      }
    }

    return {
      isSuccess: true,
      message: `Updated ${updatedCount} expired trials`,
      data: { updatedCount }
    }
  } catch (error) {
    console.error("Error updating expired trials:", error)
    return {
      isSuccess: false,
      message: `Failed to update expired trials: ${error instanceof Error ? error.message : "Unknown error"}`
    }
  }
}

/**
 * Gets the current trial status for the authenticated user
 */
export async function getCurrentUserTrialStatusAction(): Promise<ActionState<{
  isActive: boolean,
  daysLeft?: number,
  trialEndsAt?: Date
}>> {
  try {
    const { userId } = await auth()

    if (!userId) {
      return {
        isSuccess: false,
        message: "User not authenticated"
      }
    }

    return checkTrialStatusAction(userId)
  } catch (error) {
    console.error("Error getting current user trial status:", error)
    return {
      isSuccess: false,
      message: `Failed to get trial status: ${error instanceof Error ? error.message : "Unknown error"}`
    }
  }
}
