// db/schema/suggestions-schema.ts
import {
  pgTable,
  uuid,
  text,
  jsonb,
  timestamp,
  integer
} from "drizzle-orm/pg-core"
import { optimizationsTable } from "./optimizations-schema"

// Suggestions table for storing suggested experiments
export const suggestionsTable = pgTable("suggestions", {
  id: uuid("id").defaultRandom().primaryKey(),
  optimizationId: uuid("optimization_id")
    .references(() => optimizationsTable.id, { onDelete: "cascade" })
    .notNull(),
  parameters: jsonb("parameters").notNull(), // Complete parameter configuration
  batchId: text("batch_id").notNull(), // Unique ID for batch suggestions
  suggestionIndex: integer("suggestion_index").notNull(), // Index within the batch

  // Status tracking: pending, in_progress, saved, submitted, discarded
  status: text("status").notNull().default("pending"),

  // Target values entered by the user but not yet submitted
  targetValues: jsonb("target_values"),

  // Metadata
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),

  // Additional fields for tracking user interaction
  lastAccessedAt: timestamp("last_accessed_at"),
  submittedAt: timestamp("submitted_at")
})

// Type definitions
export type InsertSuggestion = typeof suggestionsTable.$inferInsert
export type SelectSuggestion = typeof suggestionsTable.$inferSelect
