import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { vi } from 'vitest'

// Mock optimization data for testing
export const mockOptimizationData = {
  optimizer_id: 'test-optimizer-123',
  parameters: [
    {
      name: 'temperature',
      type: 'NumericalDiscrete',
      values: [50, 60, 70, 80, 90],
      tolerance: 1.0,
      description: 'Reaction temperature'
    },
    {
      name: 'pressure',
      type: 'NumericalContinuous',
      bounds: [1.0, 5.0],
      description: 'System pressure'
    },
    {
      name: 'catalyst',
      type: 'CategoricalParameter',
      values: ['A', 'B', 'C', 'D'],
      encoding: 'OHE',
      description: 'Catalyst type'
    }
  ],
  targets: [
    {
      name: 'yield',
      mode: 'MAX',
      weight: 1.0
    }
  ],
  measurements: [
    { temperature: 60, pressure: 2.5, catalyst: 'A', yield: 0.75 },
    { temperature: 70, pressure: 3.0, catalyst: 'B', yield: 0.82 },
    { temperature: 80, pressure: 2.0, catalyst: 'C', yield: 0.68 }
  ]
}

// Mock API responses
export const mockApiResponses = {
  updateBounds: {
    success: {
      status: 'success',
      message: 'Parameter bounds updated successfully',
      filtered_measurements: 2,
      total_measurements: 3,
      suggestions: [{ temperature: 75, pressure: 2.8, catalyst: 'B' }]
    },
    preview: {
      status: 'success',
      message: 'Preview generated successfully',
      preview: {
        filtered_measurements: 1,
        total_measurements: 3,
        impact_summary: '2 measurements will be filtered out'
      },
      suggestions: [{ temperature: 65, pressure: 3.2, catalyst: 'A' }]
    },
    error: {
      status: 'error',
      message: 'Invalid parameter bounds: lower bound must be less than upper bound'
    }
  }
}

// Mock fetch function for API calls
export const mockFetch = (response: any, ok = true) => {
  global.fetch = vi.fn().mockResolvedValue({
    ok,
    status: ok ? 200 : 400,
    json: async () => response,
  })
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  // Add any custom options here
}

export const renderWithProviders = (
  ui: React.ReactElement,
  options?: CustomRenderOptions
) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    return (
      <div data-testid="test-wrapper">
        {children}
      </div>
    )
  }

  return render(ui, { wrapper: Wrapper, ...options })
}

// Helper to create mock parameter configurations
export const createMockParameter = (overrides: Partial<any> = {}) => ({
  name: 'test_parameter',
  type: 'NumericalContinuous',
  bounds: [0, 1],
  description: 'Test parameter',
  ...overrides
})

// Helper to create mock target configurations
export const createMockTarget = (overrides: Partial<any> = {}) => ({
  name: 'test_target',
  mode: 'MAX',
  weight: 1.0,
  ...overrides
})

// Helper to simulate user interactions
export const simulateParameterEdit = async (userEvent: any, parameterName: string, newValue: string) => {
  const editButton = document.querySelector(`[data-testid="edit-${parameterName}"]`)
  if (editButton) {
    await userEvent.click(editButton)
  }
  
  const input = document.querySelector(`[data-testid="input-${parameterName}"]`)
  if (input) {
    await userEvent.clear(input)
    await userEvent.type(input, newValue)
  }
}

// Helper to wait for API calls to complete
export const waitForApiCall = async (timeout = 1000) => {
  return new Promise(resolve => setTimeout(resolve, timeout))
}

// Mock toast function
export const mockToast = vi.fn()

// Re-export everything from testing-library
export * from '@testing-library/react'
export { renderWithProviders as render }
