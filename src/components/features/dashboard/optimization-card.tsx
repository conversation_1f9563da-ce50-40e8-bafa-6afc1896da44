"use client"

import { motion } from "framer-motion"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { SelectOptimization } from "@/db/schema/optimizations-schema"
import { formatDistanceToNow } from "date-fns"
import { useEffect, useState } from "react"
import { statusColors, statusDescriptions } from "@/lib/status-utils"
import {
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Beaker,
  Activity,
  Target,
  Lightbulb,
  Compass,
  BarChart,
  Layers,
  Clock,
  Zap,
  Sliders,
  Gauge,
  Microscope
} from "lucide-react"
import Link from "next/link"
import { Progress } from "@/components/ui/progress"

interface OptimizationCardProps {
  optimization: SelectOptimization
  index: number
  measurements?: number
  bestValue?: number | null
  lastActivity?: Date | null
}

export function OptimizationCard({
  optimization,
  index,
  measurements = 0,
  bestValue = null,
  lastActivity = null
}: OptimizationCardProps) {
  // Client-side only state for time formatting
  const [formattedTime, setFormattedTime] = useState<string>("")
  const [lastActivityTime, setLastActivityTime] = useState<string>("")

  // Animation delay based on index (staggered effect)
  const delay = index * 0.1

  // Status colors and descriptions are imported from lib/status-utils.ts

  // Parameter count and types
  const config = optimization.config as any
  const parameterCount = config.parameters?.length || 0

  // Get acquisition function info
  const acquisitionFunction =
    config.acquisition_config?.type || "qExpectedImprovement"
  const beta = config.acquisition_config?.beta // Only show beta if it exists

  // Format acquisition function for display
  const getAcquisitionFunctionLabel = (type: string) => {
    switch (type) {
      case "qExpectedImprovement":
        return "Expected Improvement"
      case "qProbabilityOfImprovement":
        return "Probability of Improvement"
      case "qUpperConfidenceBound":
        return `Upper Confidence Bound${beta !== undefined ? ` (β=${beta})` : ""}`
      default:
        return type
    }
  }

  // Get parameter type breakdown
  const getParameterTypeCounts = () => {
    const counts = {
      numerical: 0,
      categorical: 0,
      other: 0
    }

    if (config.parameters && Array.isArray(config.parameters)) {
      config.parameters.forEach((param: any) => {
        if (
          param.type === "NumericalContinuous" ||
          param.type === "NumericalDiscrete"
        ) {
          counts.numerical++
        } else if (param.type === "CategoricalParameter") {
          counts.categorical++
        } else {
          counts.other++
        }
      })
    }

    return counts
  }

  const parameterTypes = getParameterTypeCounts()

  // Get multi-target info
  const isMultiTarget = optimization.targetMode === "MULTI"
  const targetCount =
    isMultiTarget && Array.isArray(config.target_config)
      ? config.target_config.length
      : 1

  // Get scalarization method
  const scalarizationMethod = config.scalarizer || "GEOM_MEAN"

  // Format scalarization method for display
  const getScalarizationLabel = (method: string) => {
    switch (method) {
      case "GEOM_MEAN":
        return "Geometric Mean"
      case "WEIGHTED_SUM":
        return "Weighted Sum"
      default:
        return method
    }
  }

  // Update the formatted time on the client side only
  useEffect(() => {
    // Format creation time
    setFormattedTime(
      formatDistanceToNow(new Date(optimization.createdAt), {
        addSuffix: true
      })
    )

    // Format last activity time if available
    if (lastActivity) {
      setLastActivityTime(
        formatDistanceToNow(new Date(lastActivity), {
          addSuffix: true
        })
      )
    } else if (optimization.updatedAt) {
      setLastActivityTime(
        formatDistanceToNow(new Date(optimization.updatedAt), {
          addSuffix: true
        })
      )
    }

    // Optional: Set up an interval to refresh the time every minute
    const interval = setInterval(() => {
      setFormattedTime(
        formatDistanceToNow(new Date(optimization.createdAt), {
          addSuffix: true
        })
      )

      if (lastActivity) {
        setLastActivityTime(
          formatDistanceToNow(new Date(lastActivity), {
            addSuffix: true
          })
        )
      } else if (optimization.updatedAt) {
        setLastActivityTime(
          formatDistanceToNow(new Date(optimization.updatedAt), {
            addSuffix: true
          })
        )
      }
    }, 60000)

    return () => clearInterval(interval)
  }, [optimization.createdAt, optimization.updatedAt, lastActivity])

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3, delay }}
    >
      <Link
        href={`/dashboard/optimizations/${optimization.id}`}
        className="block transition-transform hover:scale-[1.02]"
      >
        <Card className="flex h-full min-h-[24rem] flex-col justify-between">
          <CardHeader className="pb-2">
            <div className="flex justify-between">
              <CardTitle className="truncate">{optimization.name}</CardTitle>
              <div
                className={`size-2 rounded-full ${
                  statusColors[optimization.status as keyof typeof statusColors]
                }`}
              />
            </div>
            <p className="text-muted-foreground truncate text-sm">
              {optimization.description || "No description provided"}
            </p>
          </CardHeader>

          <CardContent className="flex flex-col py-2 pb-0">
            <div className="space-y-1.5">
              {/* Target information */}
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm">
                  <Target className="mr-1 size-3.5" />
                  Target{isMultiTarget ? "s" : ""}:
                </div>
                <div className="flex items-center">
                  {isMultiTarget ? (
                    <Badge variant="outline" className="font-normal">
                      {targetCount} Targets
                    </Badge>
                  ) : (
                    <div className="flex items-center font-medium">
                      {optimization.targetName}
                      {optimization.targetMode === "MAX" ? (
                        <ArrowUp className="ml-1 size-4 text-green-500" />
                      ) : (
                        <ArrowDown className="ml-1 size-4 text-red-500" />
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Show multi-target details if applicable */}
              {isMultiTarget && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm">
                    <Sliders className="mr-1 size-3.5" />
                    Scalarization:
                  </div>
                  <Badge variant="secondary" className="font-normal">
                    {getScalarizationLabel(scalarizationMethod)}
                  </Badge>
                </div>
              )}

              {/* Acquisition function */}
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm">
                  <Compass className="mr-1 size-3.5" />
                  Strategy:
                </div>
                <Badge variant="outline" className="font-normal">
                  {getAcquisitionFunctionLabel(acquisitionFunction)}
                </Badge>
              </div>

              {/* Status with description tooltip */}
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm">
                  <Gauge className="mr-1 size-3.5" />
                  Status:
                </div>
                <div className="flex items-center">
                  <div
                    className={`size-2 rounded-full ${statusColors[optimization.status as keyof typeof statusColors]} mr-2`}
                    title={
                      statusDescriptions[
                        optimization.status as keyof typeof statusDescriptions
                      ]
                    }
                  />
                  <div className="font-medium capitalize">
                    {optimization.status}
                  </div>
                </div>
              </div>

              {/* Measurements count if available */}
              <div className="flex flex-col space-y-1">
                {measurements > 0 ? (
                  <>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm">
                        <Microscope className="mr-1 size-3.5" />
                        Experiments:
                      </div>
                      <div className="text-sm font-medium">{measurements}</div>
                    </div>
                    {bestValue !== null && (
                      <Progress
                        value={75}
                        className="h-1.5"
                        title="Optimization progress"
                      />
                    )}
                  </>
                ) : (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm">
                      <Microscope className="mr-1 size-3.5" />
                      Experiments:
                    </div>
                    <div className="text-sm font-medium">0</div>
                  </div>
                )}
              </div>

              {/* Parameter breakdown */}
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm">
                  <Layers className="mr-1 size-3.5" />
                  Parameters:
                </div>
                <div className="flex items-center space-x-1">
                  {parameterTypes.numerical > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {parameterTypes.numerical} Num
                    </Badge>
                  )}
                  {parameterTypes.categorical > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {parameterTypes.categorical} Cat
                    </Badge>
                  )}
                  {parameterTypes.other > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {parameterTypes.other} Other
                    </Badge>
                  )}
                </div>
              </div>

              {/* Timestamps */}
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm">
                  <Clock className="mr-1 size-3.5" />
                  Created:
                </div>
                <div className="text-sm">{formattedTime || "recently"}</div>
              </div>

              {lastActivityTime && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm">
                    <Zap className="mr-1 size-3.5" />
                    Last activity:
                  </div>
                  <div className="text-sm">{lastActivityTime}</div>
                </div>
              )}
            </div>
          </CardContent>

          <CardFooter className="bg-muted/40 mt-auto flex items-center justify-between border-t p-2">
            <div className="text-muted-foreground flex items-center text-sm">
              <BarChart className="mr-1 size-4" />
              <span>
                {measurements > 0
                  ? `${measurements} experiments`
                  : "No experiments yet"}
              </span>
            </div>
            <Button variant="ghost" size="sm" asChild>
              <div>
                <Activity className="mr-1 size-4" />
                View Details
              </div>
            </Button>
          </CardFooter>
        </Card>
      </Link>
    </motion.div>
  )
}
