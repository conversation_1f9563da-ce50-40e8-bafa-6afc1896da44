"use client"

import { useState, useRef, useEffect } from "react"
import dynamic from "next/dynamic"
import { motion, AnimatePresence } from "framer-motion"
import {
  Play,
  Pause,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  Sliders
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/slider"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import {
  generateParetoFront,
  generateDominatedPoints,
  generateOptimizationProgress,
  generateWeightCombinations,
  findOptimalPoint
} from "@/lib/multi-objective-utils"

// Dynamically import Plotly to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

interface MultiObjectiveVisualizationProps {
  className?: string
}

export function MultiObjectiveVisualization({
  className
}: MultiObjectiveVisualizationProps) {
  // State for the visualization
  const [viewMode, setViewMode] = useState<"pareto" | "weights" | "progress">(
    "pareto"
  )
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentIteration, setCurrentIteration] = useState(0)
  const [showControls, setShowControls] = useState(true)
  const [weights, setWeights] = useState<[number, number]>([0.5, 0.5])

  // Refs for animation
  const containerRef = useRef<HTMLDivElement>(null)

  // Generate data for visualization
  const paretoFront = generateParetoFront(20, 0.05)
  const dominatedPoints = generateDominatedPoints(30, paretoFront)
  const progressPoints = generateOptimizationProgress(10, paretoFront)
  const weightCombinations = generateWeightCombinations(5)

  // Find optimal points for each weight combination
  const optimalPoints = weightCombinations.map(w =>
    findOptimalPoint(paretoFront, w)
  )

  // Find the optimal point for the current weight
  const currentOptimalPoint = findOptimalPoint(paretoFront, weights)

  // Handle play/pause
  const togglePlayPause = () => {
    if (isPlaying) {
      setIsPlaying(false)
    } else {
      setIsPlaying(true)

      // If we're at the end, restart
      if (currentIteration >= 10) {
        setCurrentIteration(0)
      }
    }
  }

  // Animation effect
  useEffect(() => {
    if (!isPlaying || viewMode !== "progress") return

    const interval = setInterval(() => {
      setCurrentIteration(prev => {
        if (prev >= 10) {
          setIsPlaying(false)
          return prev
        }
        return prev + 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isPlaying, viewMode])

  // Reset animation
  const resetAnimation = () => {
    setCurrentIteration(0)
    setIsPlaying(false)
  }

  // Handle weight change
  const handleWeightChange = (value: number[]) => {
    setWeights([value[0] / 100, 1 - value[0] / 100])
  }

  // Filter points based on current iteration for progress view
  const visiblePoints = progressPoints.filter(
    point => point.iteration <= currentIteration
  )

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative w-full overflow-hidden rounded-lg border shadow-xl",
        className
      )}
    >
      {/* Main visualization area */}
      <div
        className="relative w-full bg-gradient-to-br from-gray-50 to-gray-100"
        style={{ height: "400px" }}
      >
        <Tabs
          value={viewMode}
          onValueChange={value =>
            setViewMode(value as "pareto" | "weights" | "progress")
          }
          className="absolute inset-x-4 top-4 z-10"
        >
          <TabsList className="mx-auto grid w-full max-w-md grid-cols-3">
            <TabsTrigger value="pareto" className="text-xs sm:text-sm">
              Pareto Front
            </TabsTrigger>
            <TabsTrigger value="weights" className="text-xs sm:text-sm">
              Weight Preferences
            </TabsTrigger>
            <TabsTrigger value="progress" className="text-xs sm:text-sm">
              Optimization Progress
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="size-full pt-16">
          {viewMode === "pareto" && (
            <Plot
              data={[
                // Pareto front
                {
                  x: paretoFront.map(p => p[0]),
                  y: paretoFront.map(p => p[1]),
                  mode: "markers+lines",
                  type: "scatter",
                  name: "Pareto Front",
                  marker: {
                    size: 8,
                    color: "#3b82f6",
                    symbol: "circle"
                  },
                  line: {
                    color: "#3b82f6",
                    width: 2,
                    dash: "solid"
                  }
                },
                // Dominated points
                {
                  x: dominatedPoints.map(p => p[0]),
                  y: dominatedPoints.map(p => p[1]),
                  mode: "markers",
                  type: "scatter",
                  name: "Dominated Solutions",
                  marker: {
                    size: 6,
                    color: "#9ca3af",
                    opacity: 0.7,
                    symbol: "circle"
                  }
                }
              ]}
              layout={{
                title: {
                  text: "Multi-Objective Optimization: Pareto Front",
                  font: {
                    family: "Inter, system-ui, sans-serif",
                    size: 14,
                    color: "#374151"
                  }
                },
                xaxis: {
                  title: {
                    text: "Objective 1 (minimize)",
                    font: { size: 12 }
                  },
                  tickfont: { size: 10 }
                },
                yaxis: {
                  title: {
                    text: "Objective 2 (minimize)",
                    font: { size: 12 }
                  },
                  tickfont: { size: 10 }
                },
                legend: {
                  x: 0,
                  y: 1,
                  font: { size: 10 }
                },
                margin: { l: 50, r: 20, t: 30, b: 40 },
                autosize: true,
                font: {
                  family: "Inter, system-ui, sans-serif",
                  size: 12
                },
                annotations: [
                  {
                    x: 0.5,
                    y: 0.05,
                    xref: "paper",
                    yref: "paper",
                    text: "Lower values are better for both objectives",
                    showarrow: false,
                    font: {
                      family: "Inter, system-ui, sans-serif",
                      size: 10,
                      color: "#6b7280"
                    }
                  }
                ]
              }}
              config={{
                displayModeBar: false,
                responsive: true
              }}
              style={{ width: "100%", height: "100%" }}
            />
          )}

          {viewMode === "weights" && (
            <Plot
              data={[
                // Pareto front
                {
                  x: paretoFront.map(p => p[0]),
                  y: paretoFront.map(p => p[1]),
                  mode: "markers+lines",
                  type: "scatter",
                  name: "Pareto Front",
                  marker: {
                    size: 6,
                    color: "#3b82f6",
                    opacity: 0.7,
                    symbol: "circle"
                  },
                  line: {
                    color: "#3b82f6",
                    width: 2,
                    dash: "solid"
                  }
                },
                // Optimal points for different weights
                {
                  x: optimalPoints.map(p => p.point[0]),
                  y: optimalPoints.map(p => p.point[1]),
                  mode: "markers",
                  type: "scatter",
                  name: "Weight Solutions",
                  marker: {
                    size: 10,
                    color: "#8b5cf6",
                    symbol: "diamond"
                  }
                },
                // Current weight optimal point
                {
                  x: [currentOptimalPoint.point[0]],
                  y: [currentOptimalPoint.point[1]],
                  mode: "markers",
                  type: "scatter",
                  name: "Current Preference",
                  marker: {
                    size: 12,
                    color: "#ef4444",
                    symbol: "star"
                  }
                }
              ]}
              layout={{
                title: {
                  text: "Weight Preferences",
                  font: {
                    family: "Inter, system-ui, sans-serif",
                    size: 14,
                    color: "#374151"
                  }
                },
                xaxis: {
                  title: {
                    text: "Objective 1 (minimize)",
                    font: { size: 12 }
                  },
                  tickfont: { size: 10 }
                },
                yaxis: {
                  title: {
                    text: "Objective 2 (minimize)",
                    font: { size: 12 }
                  },
                  tickfont: { size: 10 }
                },
                legend: {
                  x: 0,
                  y: 1,
                  font: { size: 10 }
                },
                margin: { l: 50, r: 20, t: 30, b: 40 },
                autosize: true,
                font: {
                  family: "Inter, system-ui, sans-serif",
                  size: 12
                }
              }}
              config={{
                displayModeBar: false,
                responsive: true
              }}
              style={{ width: "100%", height: "100%" }}
            />
          )}

          {viewMode === "progress" && (
            <Plot
              data={[
                // Pareto front
                {
                  x: paretoFront.map(p => p[0]),
                  y: paretoFront.map(p => p[1]),
                  mode: "lines",
                  type: "scatter",
                  name: "Pareto Front",
                  line: {
                    color: "#3b82f6",
                    width: 2,
                    dash: "dash"
                  }
                },
                // Progress points
                {
                  x: visiblePoints.map(p => p.x),
                  y: visiblePoints.map(p => p.y),
                  mode: "markers",
                  type: "scatter",
                  name: "Optimization Progress",
                  marker: {
                    size: 8,
                    color: visiblePoints.map(p => {
                      // Color gradient based on iteration
                      const r = Math.round(255 - (p.iteration / 10) * 200)
                      const g = Math.round(100 + (p.iteration / 10) * 155)
                      const b = Math.round(100 + (p.iteration / 10) * 155)
                      return `rgb(${r},${g},${b})`
                    }),
                    symbol: "circle"
                  },
                  text: visiblePoints.map(p => `Iteration: ${p.iteration}`),
                  hovertemplate: "%{text}<br>Obj1: %{x:.2f}<br>Obj2: %{y:.2f}"
                }
              ]}
              layout={{
                title: {
                  text: "Optimization Progress",
                  font: {
                    family: "Inter, system-ui, sans-serif",
                    size: 14,
                    color: "#374151"
                  }
                },
                xaxis: {
                  title: {
                    text: "Objective 1 (minimize)",
                    font: { size: 12 }
                  },
                  tickfont: { size: 10 }
                },
                yaxis: {
                  title: {
                    text: "Objective 2 (minimize)",
                    font: { size: 12 }
                  },
                  tickfont: { size: 10 }
                },
                legend: {
                  x: 0,
                  y: 1,
                  font: { size: 10 }
                },
                margin: { l: 50, r: 20, t: 30, b: 40 },
                autosize: true,
                font: {
                  family: "Inter, system-ui, sans-serif",
                  size: 12
                }
              }}
              config={{
                displayModeBar: false,
                responsive: true
              }}
              style={{ width: "100%", height: "100%" }}
            />
          )}
        </div>
      </div>

      {/* Controls and info section below the plot */}
      <div className="border-t bg-white p-3 sm:p-4">
        {/* Explanation text - always visible */}
        <div className="mb-3 rounded-md bg-gray-50 p-2 text-xs sm:mb-4 sm:p-3 sm:text-sm">
          {viewMode === "pareto" && (
            <p>
              The{" "}
              <span className="font-medium text-blue-600">Pareto front</span>{" "}
              shows the set of optimal trade-offs between competing objectives.
              Points on this front cannot be improved in one objective without
              sacrificing performance in another.
            </p>
          )}

          {viewMode === "weights" && (
            <p>
              Adjust the slider to change your preference between objectives.
              The <span className="font-medium text-red-600">star</span> shows
              the optimal solution based on your current preference weights.
            </p>
          )}

          {viewMode === "progress" && (
            <p>
              Watch how the optimization algorithm converges toward the Pareto
              front over iterations, efficiently exploring the trade-off space
              to find optimal solutions.
            </p>
          )}
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-2">
            {viewMode === "progress" && (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  className="size-8 sm:size-9"
                  onClick={togglePlayPause}
                >
                  {isPlaying ? (
                    <Pause className="size-3 sm:size-4" />
                  ) : (
                    <Play className="size-3 sm:size-4" />
                  )}
                </Button>

                <Button
                  variant="outline"
                  size="icon"
                  className="size-8 sm:size-9"
                  onClick={resetAnimation}
                >
                  <RefreshCw className="size-3 sm:size-4" />
                </Button>

                <div className="ml-2 text-xs font-medium sm:text-sm">
                  Iteration: {currentIteration} / 10
                </div>
              </>
            )}

            {viewMode === "weights" && (
              <div className="flex w-full max-w-xs items-center gap-2">
                <Sliders className="size-4" />
                <div className="flex-1">
                  <Slider
                    value={[weights[0] * 100]}
                    onValueChange={handleWeightChange}
                    max={100}
                    step={1}
                    className="w-full"
                  />
                </div>
                <div className="text-xs sm:text-sm">
                  {weights[0].toFixed(2)} : {weights[1].toFixed(2)}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
