"use client"

import { useState } from "react"
import { Plus, Trash2, <PERSON><PERSON>tor } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MultiSelect } from "@/components/ui/multi-select"
import { ContinuousLinearConstraint } from "@/lib/constraints"

interface LinearConstraintEditorProps {
  constraint: ContinuousLinearConstraint
  availableParameters: string[]
  onChange: (updates: Partial<ContinuousLinearConstraint>) => void
}

export function LinearConstraintEditor({
  constraint,
  availableParameters,
  onChange
}: LinearConstraintEditorProps) {
  const [selectedParameters, setSelectedParameters] = useState<string[]>(
    constraint.parameters || []
  )
  const [constraintMode, setConstraintMode] = useState<
    "comparison" | "sum" | "custom"
  >(
    // Determine initial mode based on constraint structure
    selectedParameters.length === 2 &&
      constraint.coefficients?.[0] === 1.0 &&
      constraint.coefficients?.[1] === -1.0 &&
      constraint.rhs === 0.0
      ? "comparison"
      : selectedParameters.length > 0 &&
          constraint.coefficients?.every(c => c === 1.0)
        ? "sum"
        : "custom"
  )

  const handleParametersChange = (parameters: string[]) => {
    setSelectedParameters(parameters)

    // Adjust coefficients array to match parameters length
    const newCoefficients = parameters.map(
      (_, index) => constraint.coefficients?.[index] ?? 1.0
    )

    onChange({
      parameters,
      coefficients: newCoefficients
    })
  }

  const handleCoefficientChange = (index: number, value: string) => {
    const numValue = parseFloat(value) || 0
    const newCoefficients = [...(constraint.coefficients || [])]
    newCoefficients[index] = numValue

    onChange({ coefficients: newCoefficients })
  }

  const handleOperatorChange = (operator: string) => {
    onChange({ operator: operator as any })
  }

  const handleRhsChange = (value: string) => {
    const numValue = parseFloat(value) || 0
    onChange({ rhs: numValue })
  }

  const addParameter = () => {
    if (selectedParameters.length < availableParameters.length) {
      const availableParam = availableParameters.find(
        p => !selectedParameters.includes(p)
      )
      if (availableParam) {
        handleParametersChange([...selectedParameters, availableParam])
      }
    }
  }

  const removeParameter = (index: number) => {
    const newParameters = selectedParameters.filter((_, i) => i !== index)
    handleParametersChange(newParameters)
  }

  const getConstraintPreview = () => {
    if (selectedParameters.length === 0) return "No parameters selected"

    // Special case for parameter comparison (2 params, coefficients [1, -1], rhs 0)
    if (
      selectedParameters.length === 2 &&
      constraint.coefficients?.[0] === 1 &&
      constraint.coefficients?.[1] === -1 &&
      constraint.rhs === 0
    ) {
      const operatorSymbol =
        {
          "<=": "≤",
          "<": "<",
          ">=": "≥",
          ">": ">",
          "=": "="
        }[constraint.operator || "<="] || constraint.operator

      return `${selectedParameters[0]} ${operatorSymbol} ${selectedParameters[1]}`
    }

    // General case
    const terms = selectedParameters.map((param, index) => {
      const coeff = constraint.coefficients?.[index] ?? 1
      if (coeff === 1) return param
      if (coeff === -1) return `-${param}`
      return `${coeff} × ${param}`
    })

    const operator = constraint.operator || "<="
    // Convert operator to display symbol
    const operatorSymbol =
      {
        "<=": "≤",
        "<": "<",
        ">=": "≥",
        ">": ">",
        "=": "="
      }[operator] || operator

    const rhs = constraint.rhs ?? 0

    return `${terms.join(" + ")} ${operatorSymbol} ${rhs}`
  }

  const parameterOptions = availableParameters.map(param => ({
    value: param,
    label: param
  }))

  return (
    <div className="space-y-6">
      {/* Live Preview */}
      <div className="rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
        <div className="mb-2 flex items-center gap-2">
          <Calculator className="size-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-800">
            Constraint Preview
          </span>
        </div>
        <div className="rounded-md border bg-white p-3 font-mono text-lg text-gray-800">
          {getConstraintPreview()}
        </div>
      </div>

      {/* Constraint Mode Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Constraint Type</CardTitle>
          <p className="text-sm text-gray-600">
            Choose the type of linear constraint to create
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-3">
            <Button
              type="button"
              variant={constraintMode === "comparison" ? "default" : "outline"}
              onClick={() => {
                setConstraintMode("comparison")
                handleParametersChange([])
                onChange({ coefficients: [], operator: "<", rhs: 0.0 })
              }}
              className="flex h-auto flex-col items-center gap-2 p-3"
            >
              <span className="font-medium">Parameter Comparison</span>
              <span className="text-xs text-gray-600">x19 &lt; x20</span>
            </Button>

            <Button
              type="button"
              variant={constraintMode === "sum" ? "default" : "outline"}
              onClick={() => {
                setConstraintMode("sum")
                handleParametersChange([])
                onChange({ coefficients: [], operator: "<=", rhs: 1.0 })
              }}
              className="flex h-auto flex-col items-center gap-2 p-3"
            >
              <span className="font-medium">Sum Constraint</span>
              <span className="text-xs text-gray-600">x6 + x15 ≤ 1.0</span>
            </Button>

            <Button
              type="button"
              variant={constraintMode === "custom" ? "default" : "outline"}
              onClick={() => {
                setConstraintMode("custom")
                // Keep existing parameters and coefficients for custom mode
              }}
              className="flex h-auto flex-col items-center gap-2 p-3"
            >
              <span className="font-medium">Custom</span>
              <span className="text-xs text-gray-600">Advanced</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Parameter Comparison Configuration */}
      {constraintMode === "comparison" && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              Configure Parameter Comparison
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 items-center gap-3">
              <div>
                <Label className="text-sm">First Parameter</Label>
                <Select
                  value={selectedParameters[0] || ""}
                  onValueChange={value => {
                    const secondParam = selectedParameters[1] || ""
                    if (secondParam) {
                      handleParametersChange([value, secondParam])
                      onChange({ coefficients: [1.0, -1.0], rhs: 0.0 })
                    } else {
                      handleParametersChange([value])
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select..." />
                  </SelectTrigger>
                  <SelectContent>
                    {availableParameters.map(param => (
                      <SelectItem key={param} value={param}>
                        {param}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-sm">Comparison</Label>
                <Select
                  value={constraint.operator || ""}
                  onValueChange={value => {
                    onChange({ operator: value as any })
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="<">{"<"} Less than</SelectItem>
                    <SelectItem value="<=">≤ Less than or equal</SelectItem>
                    <SelectItem value=">">{">"} Greater than</SelectItem>
                    <SelectItem value=">=">≥ Greater than or equal</SelectItem>
                    <SelectItem value="=">= Equal to</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-sm">Second Parameter</Label>
                <Select
                  value={selectedParameters[1] || ""}
                  onValueChange={value => {
                    const firstParam = selectedParameters[0] || ""
                    if (firstParam) {
                      handleParametersChange([firstParam, value])
                      onChange({ coefficients: [1.0, -1.0], rhs: 0.0 })
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select..." />
                  </SelectTrigger>
                  <SelectContent>
                    {availableParameters
                      .filter(p => p !== selectedParameters[0])
                      .map(param => (
                        <SelectItem key={param} value={param}>
                          {param}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sum Constraint Configuration */}
      {constraintMode === "sum" && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              Configure Sum Constraint
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm">Select Parameters to Sum</Label>
              <MultiSelect
                options={parameterOptions}
                value={selectedParameters}
                onValueChange={params => {
                  handleParametersChange(params)
                  if (params.length > 0) {
                    onChange({
                      coefficients: params.map(() => 1.0)
                    })
                  }
                }}
                placeholder="Choose parameters to add together..."
                maxCount={10}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm">Operator</Label>
                <Select
                  value={constraint.operator || "<="}
                  onValueChange={value => onChange({ operator: value as any })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="<=">≤ Less than or equal</SelectItem>
                    <SelectItem value=">=">≥ Greater than or equal</SelectItem>
                    <SelectItem value="=">= Equal to</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-sm">Value</Label>
                <Input
                  type="number"
                  step="0.1"
                  value={constraint.rhs ?? 1.0}
                  onChange={e => handleRhsChange(e.target.value)}
                  placeholder="1.0"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Custom Configuration */}
      {constraintMode === "custom" && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              Custom Linear Constraint
            </CardTitle>
            <p className="text-sm text-gray-600">
              Full control over coefficients and parameters
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm">Select Parameters</Label>
              <MultiSelect
                options={parameterOptions}
                value={selectedParameters}
                onValueChange={handleParametersChange}
                placeholder="Choose parameters for this constraint..."
                maxCount={10}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Custom Mode - Coefficients and Settings */}
      {constraintMode === "custom" && selectedParameters.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              Custom Coefficients & Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Coefficients */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Coefficients</Label>
              <div className="space-y-2">
                {selectedParameters.map((param, index) => (
                  <div key={param} className="flex items-center gap-3">
                    <div className="flex flex-1 items-center gap-2">
                      <Input
                        type="number"
                        step="0.1"
                        value={constraint.coefficients?.[index] ?? 1}
                        onChange={e =>
                          handleCoefficientChange(index, e.target.value)
                        }
                        className="w-20"
                      />
                      <span className="text-sm text-gray-500">×</span>
                      <Badge variant="secondary" className="font-mono">
                        {param}
                      </Badge>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeParameter(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="size-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* Operator and RHS */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm">Operator</Label>
                <Select
                  value={constraint.operator || "<="}
                  onValueChange={handleOperatorChange}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="<=">≤ Less than or equal</SelectItem>
                    <SelectItem value="<">{"<"} Less than</SelectItem>
                    <SelectItem value=">=">≥ Greater than or equal</SelectItem>
                    <SelectItem value=">">{">"} Greater than</SelectItem>
                    <SelectItem value="=">= Equal to</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm">Value</Label>
                <Input
                  type="number"
                  step="0.1"
                  value={constraint.rhs ?? 0}
                  onChange={e => handleRhsChange(e.target.value)}
                  placeholder="0"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions for Non-Custom Modes */}
      {constraintMode !== "custom" && selectedParameters.length > 0 && (
        <Card className="border-dashed bg-gray-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Need more control?</span>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setConstraintMode("custom")}
                className="text-sm"
              >
                Switch to Custom Mode
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Examples */}
      {selectedParameters.length === 0 && (
        <Card className="border-gray-200 bg-gray-50">
          <CardHeader>
            <CardTitle className="text-sm text-gray-700">
              Quick Examples
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 gap-2 text-sm">
              <Button
                variant="ghost"
                className="h-auto justify-start p-3 text-left"
                onClick={() => {
                  const param1 =
                    availableParameters.find(p => p.includes("19")) ||
                    availableParameters[0]
                  const param2 =
                    availableParameters.find(p => p.includes("20")) ||
                    availableParameters[1]
                  if (param1 && param2) {
                    handleParametersChange([param1, param2])
                    onChange({
                      coefficients: [1.0, -1.0],
                      operator: "<",
                      rhs: 0.0
                    })
                  }
                }}
              >
                <div>
                  <div className="font-medium">Parameter Comparison</div>
                  <div className="font-mono text-xs text-gray-600">
                    x19 &lt; x20
                  </div>
                </div>
              </Button>

              <Button
                variant="ghost"
                className="h-auto justify-start p-3 text-left"
                onClick={() => {
                  const param1 =
                    availableParameters.find(p => p.includes("6")) ||
                    availableParameters[0]
                  const param2 =
                    availableParameters.find(p => p.includes("15")) ||
                    availableParameters[1]
                  if (param1 && param2) {
                    handleParametersChange([param1, param2])
                    onChange({
                      coefficients: [1.0, 1.0],
                      operator: "<=",
                      rhs: 1.0
                    })
                  }
                }}
              >
                <div>
                  <div className="font-medium">Sum Constraint</div>
                  <div className="font-mono text-xs text-gray-600">
                    x6 + x15 ≤ 1.0
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
