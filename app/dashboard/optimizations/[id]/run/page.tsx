// app/dashboard/optimizations/[id]/run/page.tsx
"use server"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { RunExperiment } from "@/components/optimization/run-experiment"
import { auth } from "@clerk/nextjs/server"
import { redirect, notFound } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"
import {
  getOptimizationByIdAction,
  getMeasurementsAction
} from "@/actions/db/optimizations-actions"
import Link from "next/link"
import { ArrowLeft, BarChart } from "lucide-react"
import { DashboardThemeWrapper } from "@/components/dashboard/dashboard-theme-wrapper"

interface RunExperimentPageProps {
  params: Promise<{ id: string }>
}

export default async function RunExperimentPage({
  params
}: RunExperimentPageProps) {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🔍 RUN EXPERIMENT PAGE - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  })

  if (!userId) {
    console.log("❌ RUN EXPERIMENT PAGE - No user ID, redirecting to login")
    redirect("/login")
  }

  const { id } = await params

  console.log(
    "🔍 RUN EXPERIMENT PAGE - Getting optimization for ID:",
    id,
    "userId:",
    userId
  )
  // Get the optimization from the database
  const optimizationResult = await getOptimizationByIdAction(id)

  if (!optimizationResult.isSuccess || !optimizationResult.data) {
    console.log("❌ RUN EXPERIMENT PAGE - Optimization not found for ID:", id)
    notFound()
  }

  const optimization = optimizationResult.data

  console.log("🔍 RUN EXPERIMENT PAGE - Found optimization:", {
    id: optimization.id,
    name: optimization.name,
    optimizationUserId: optimization.userId,
    currentUserId: userId,
    userMatch: optimization.userId === userId
  })

  // Make sure the optimization belongs to the current user
  if (optimization.userId !== userId) {
    console.log(
      "❌ RUN EXPERIMENT PAGE - User ID mismatch, redirecting to optimizations"
    )
    console.log("Expected:", userId, "Got:", optimization.userId)
    redirect("/dashboard/optimizations")
  }

  // Get measurements for this optimization to calculate the measurement count
  const measurementsResult = await getMeasurementsAction(optimization.id)
  const measurements = measurementsResult.isSuccess
    ? measurementsResult.data
    : []

  // Add the measurement count to the optimization object
  const optimizationWithCount = {
    ...optimization,
    measurementCount: measurements.length
  }

  return (
    <DashboardThemeWrapper>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href={`/dashboard/optimizations/${id}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-1 size-4" />
              Back to Details
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">{optimization.name}</h1>
        </div>
        <Link href={`/dashboard/optimizations/${id}`}>
          <Button variant="outline">
            <BarChart className="mr-2 size-4" />
            View Results
          </Button>
        </Link>
      </div>

      <RunExperiment optimization={optimizationWithCount} />
    </DashboardThemeWrapper>
  )
}
