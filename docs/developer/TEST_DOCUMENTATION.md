# Test Documentation for Optimization Parameter CRUD Operations

## Overview

This document provides comprehensive information about the automated test suite for the Optimization Parameters tab Edit mode functionality. The tests cover both backend API endpoints and frontend components to ensure reliable CRUD operations.

## Test Structure

### Backend Tests (`BOapp-backend/tests/`)

#### 1. Parameter CRUD Operations (`test_parameter_crud_operations.py`)
- **Purpose**: Tests core parameter update functionality
- **Coverage**:
  - Numerical continuous parameter bounds updates
  - Numerical discrete parameter values updates
  - Categorical parameter encoding updates
  - Preview functionality
  - Multiple parameter batch updates
  - Target configuration updates
  - Combined parameter and target updates

#### 2. Edge Cases and Error Handling (`test_parameter_edge_cases.py`)
- **Purpose**: Tests error scenarios and validation
- **Coverage**:
  - Invalid parameter bounds (lower > upper)
  - Empty parameter values/categories
  - Invalid parameter types
  - Missing required fields
  - Extreme parameter values
  - Non-numeric bounds
  - Negative tolerance values
  - Duplicate parameter names
  - Optimizer not found errors
  - Malformed requests
  - Concurrent update handling

#### 3. Test Fixtures (`fixtures/parameter_fixtures.py`)
- **Purpose**: Provides reusable test data
- **Includes**:
  - Parameter type fixtures
  - Target configuration fixtures
  - Complete optimization scenarios
  - Update scenarios
  - API response fixtures
  - Edge case fixtures

### Frontend Tests (`BOapp-frontend/src/test/`)

#### 1. Component Tests
- **ParameterEditForm** (`components/parameter-edit-form.test.tsx`)
  - Form rendering for different parameter types
  - User input handling
  - Save/cancel/reset functionality
  - Parameter validation

- **ParameterQuickEdit** (`components/parameter-quick-edit.test.tsx`)
  - Popover functionality
  - Quick editing capabilities
  - Save/cancel operations
  - Disabled state handling

- **Edit Mode Functionality** (`components/edit-mode-functionality.test.tsx`)
  - Edit mode toggle behavior
  - UI state changes
  - Control visibility
  - State persistence

#### 2. Integration Tests (`integration/parameter-crud-workflow.test.tsx`)
- **Purpose**: End-to-end workflow testing
- **Coverage**:
  - Complete parameter update workflow
  - Preview and apply functionality
  - Batch parameter updates
  - Error handling in workflows
  - Data integrity preservation

#### 3. Error Handling Tests (`components/error-handling.test.tsx`)
- **Purpose**: Frontend error scenarios
- **Coverage**:
  - Validation errors
  - API error responses
  - Network errors
  - Loading states
  - Error recovery

## Test Execution

### Backend Tests

#### Prerequisites
```bash
cd BOapp-backend
pip install -r requirements.txt
pip install pytest pytest-cov
```

#### Running Tests
```bash
# Run all parameter CRUD tests
pytest tests/test_parameter_crud_operations.py -v

# Run edge case tests
pytest tests/test_parameter_edge_cases.py -v

# Run all tests with coverage
pytest tests/ --cov=baybe_api --cov-report=html

# Run specific test class
pytest tests/test_parameter_crud_operations.py::TestParameterCRUDOperations -v

# Run specific test method
pytest tests/test_parameter_crud_operations.py::TestParameterCRUDOperations::test_update_numerical_continuous_parameter_bounds -v
```

### Frontend Tests

#### Prerequisites
```bash
cd BOapp-frontend
npm install
```

#### Running Tests
```bash
# Run all tests
npm test

# Run tests with UI
npm run test:ui

# Run tests once (CI mode)
npm run test:run

# Run tests with coverage
npm run test:coverage

# Run specific test file
npx vitest src/test/components/parameter-edit-form.test.tsx

# Run tests in watch mode
npx vitest --watch
```

## Test Coverage Goals

### Backend Coverage
- **API Endpoints**: 100% of parameter CRUD endpoints
- **Parameter Types**: All supported parameter types (Continuous, Discrete, Categorical)
- **Error Scenarios**: All validation and error cases
- **Edge Cases**: Extreme values, empty data, malformed requests

### Frontend Coverage
- **Components**: All parameter editing components
- **User Interactions**: All user input scenarios
- **State Management**: Edit mode state transitions
- **Error Handling**: All error display and recovery scenarios

## Continuous Integration

### GitHub Actions Workflow (Recommended)

```yaml
name: Test Parameter CRUD Operations

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: |
          cd BOapp-backend
          pip install -r requirements.txt
          pip install pytest pytest-cov
      - name: Run backend tests
        run: |
          cd BOapp-backend
          pytest tests/test_parameter_crud_operations.py tests/test_parameter_edge_cases.py --cov=baybe_api

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd BOapp-frontend
          npm install
      - name: Run frontend tests
        run: |
          cd BOapp-frontend
          npm run test:run
```

## Maintenance Guidelines

### Adding New Tests

1. **Backend Tests**:
   - Add new test methods to existing test classes
   - Use fixtures from `parameter_fixtures.py`
   - Follow naming convention: `test_<functionality>_<scenario>`
   - Include both success and error cases

2. **Frontend Tests**:
   - Create test files alongside components
   - Use test utilities from `test-utils.tsx`
   - Mock API calls appropriately
   - Test user interactions with `@testing-library/user-event`

### Updating Tests

1. **When API Changes**:
   - Update backend test expectations
   - Update frontend API mocks
   - Verify integration tests still pass

2. **When UI Changes**:
   - Update component test selectors
   - Verify accessibility attributes
   - Update interaction tests

### Test Data Management

1. **Fixtures**:
   - Keep fixtures realistic and representative
   - Update fixtures when data models change
   - Maintain both simple and complex scenarios

2. **Mocks**:
   - Keep mocks synchronized with actual API responses
   - Update mock data when backend changes
   - Maintain error response mocks

## Troubleshooting

### Common Issues

1. **Backend Test Failures**:
   - Check if backend dependencies are installed
   - Verify mock configurations
   - Ensure test database is clean

2. **Frontend Test Failures**:
   - Check if all npm dependencies are installed
   - Verify mock implementations
   - Check for async/await issues

3. **Integration Test Failures**:
   - Verify API endpoint URLs
   - Check request/response formats
   - Ensure proper error handling

### Debug Tips

1. **Use verbose output**: Add `-v` flag to pytest or `--reporter=verbose` to vitest
2. **Run single tests**: Isolate failing tests to debug specific issues
3. **Check coverage reports**: Identify untested code paths
4. **Use debugger**: Add `debugger` statements in frontend tests or `pdb` in backend tests

## Performance Considerations

1. **Test Execution Time**:
   - Keep individual tests under 1 second
   - Use mocks to avoid real API calls
   - Parallelize test execution where possible

2. **Resource Usage**:
   - Clean up test data after each test
   - Avoid memory leaks in component tests
   - Use appropriate test timeouts

## Security Testing

1. **Input Validation**:
   - Test with malicious input data
   - Verify parameter sanitization
   - Check for injection vulnerabilities

2. **Authentication**:
   - Test with invalid API keys
   - Verify user permission checks
   - Test session handling
