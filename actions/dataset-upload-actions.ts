// actions/dataset-upload-actions.ts
"use server"

import { auth } from "@clerk/nextjs/server"
import { v4 as uuidv4 } from "uuid"
import { parseCSVContent, validateDataset, generateCSVTemplate } from "@/lib/csv-utils"
import { generateExcelTemplate } from "@/lib/excel-template-generator"
import { getOptimizationByOptimizerIdAction } from "./db/optimizations-actions"
import { saveSamplesWorkflowAction } from "./sample-workflow-actions"
import { DatasetUploadResult, DataValidationError } from "@/types/dataset-types"
import { OptimizationConfig } from "@/types/optimization-types"
import { ClientSample } from "@/types/sample-types"
import { ActionState } from "@/types"

/**
 * Process uploaded CSV dataset and convert to samples
 */
export async function uploadDatasetWorkflowAction(
  optimizationId: string,
  csvContent: string
): Promise<ActionState<DatasetUploadResult>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to upload datasets"
    }
  }

  try {
    // Get optimization configuration
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found: ${optResult.message}`
      }
    }

    const optimization = optResult.data
    const config = optimization.config as OptimizationConfig

    // Parse CSV content
    const { headers, rows, errors: parseErrors } = parseCSVContent(csvContent)

    console.log('[UPLOAD_ACTION] Parsed headers:', headers)
    console.log('[UPLOAD_ACTION] Parsed rows:', rows)
    console.log('[UPLOAD_ACTION] Parse errors:', parseErrors)

    if (parseErrors.length > 0) {
      return {
        isSuccess: false,
        message: `CSV parsing failed: ${parseErrors[0].message}`
      }
    }

    if (rows.length === 0) {
      return {
        isSuccess: false,
        message: "No data rows found in CSV file"
      }
    }

    // Validate data against optimization configuration
    const validationErrors = validateDataset(headers, rows, config)
    
    // Filter out rows with critical errors (missing required fields)
    const criticalErrors = validationErrors.filter(e => 
      e.type === 'missing' && e.row > 0 // Row 0 errors are header issues
    )
    
    const validRowIndices = new Set<number>()
    for (let i = 0; i < rows.length; i++) {
      const rowNumber = i + 2 // +2 because row 1 is headers
      const hasError = criticalErrors.some(e => e.row === rowNumber)
      if (!hasError) {
        validRowIndices.add(i)
      }
    }

    const validRows = rows.filter((_, index) => validRowIndices.has(index))
    
    if (validRows.length === 0) {
      return {
        isSuccess: false,
        message: "No valid data rows found after validation"
      }
    }

    // Convert valid rows to ClientSample format
    const batchId = `upload-${Date.now()}`
    const targetConfigs = Array.isArray(config.target_config)
      ? config.target_config
      : [config.target_config]

    console.log('[UPLOAD_ACTION] Target configs:', targetConfigs)

    // Helper function to find column with flexible matching
    const findColumn = (targetName: string, headers: string[]): string | null => {
      const cleanTargetName = targetName.trim()

      // First try exact match
      if (headers.includes(cleanTargetName)) {
        return cleanTargetName
      }

      // Then try case-insensitive match with trimmed spaces
      const match = headers.find(h => h.trim().toLowerCase() === cleanTargetName.toLowerCase())
      return match || null
    }

    const uploadedSamples: ClientSample[] = validRows.map((row, index) => {
      console.log(`[UPLOAD_ACTION] Processing row ${index}:`, row)

      // Extract parameters (exclude target columns)
      const parameters: Record<string, any> = {}
      config.parameters.forEach(param => {
        parameters[param.name] = row[param.name]
      })

      // Extract target values using flexible column matching
      const targetValues: Record<string, any> = {}
      targetConfigs.forEach(target => {
        console.log(`[UPLOAD_ACTION] Looking for target '${target.name}' in row:`, row)

        // Find the actual column name in the CSV
        const actualColumnName = findColumn(target.name, headers)
        console.log(`[UPLOAD_ACTION] Mapped target '${target.name}' to column '${actualColumnName}'`)

        const value = actualColumnName ? row[actualColumnName] : undefined
        console.log(`[UPLOAD_ACTION] Found value for '${target.name}':`, value)

        if (value !== undefined && value !== null && value !== '') {
          targetValues[target.name] = typeof value === 'number' ? value : parseFloat(String(value))
          console.log(`[UPLOAD_ACTION] Set target '${target.name}' =`, targetValues[target.name])
        } else {
          console.log(`[UPLOAD_ACTION] Skipping empty/null target '${target.name}'`)
        }
      })

      console.log(`[UPLOAD_ACTION] Final target values for row ${index}:`, targetValues)

      return {
        ...parameters,
        _sampleId: uuidv4(),
        _targetValues: targetValues,
        _batchId: batchId,
        _sampleIndex: index,
        _samplingMethod: "csv_upload",
        _sampleClass: "uploaded",
        _savedToDatabase: false,
        _seed: null
      }
    })

    // Save samples to database using existing workflow
    const saveResult = await saveSamplesWorkflowAction(
      optimizationId,
      uploadedSamples,
      batchId
    )

    if (!saveResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Failed to save uploaded samples: ${saveResult.message}`
      }
    }

    return {
      isSuccess: true,
      message: `Successfully uploaded ${validRows.length} experiments`,
      data: {
        uploadedSamples: saveResult.data?.savedSamples || uploadedSamples,
        batchId: batchId,
        validRows: validRows.length,
        totalRows: rows.length,
        errors: validationErrors
      }
    }

  } catch (error) {
    console.error("Error uploading dataset:", error)
    return {
      isSuccess: false,
      message: `Upload failed: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Generate CSV template for download
 */
export async function generateTemplateAction(
  optimizationId: string
): Promise<ActionState<{ csvContent: string; filename: string }>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to generate templates"
    }
  }

  try {
    // Get optimization configuration
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found: ${optResult.message}`
      }
    }

    const optimization = optResult.data
    const config = optimization.config as OptimizationConfig

    console.log('[TEMPLATE] Optimization config:', config)
    console.log('[TEMPLATE] Target config:', config.target_config)

    // Generate CSV template
    const csvContent = generateCSVTemplate(config)
    
    // Generate filename
    const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
    const filename = `${sanitizedName}-template.csv`

    return {
      isSuccess: true,
      message: "Template generated successfully",
      data: {
        csvContent,
        filename
      }
    }

  } catch (error) {
    console.error("Error generating template:", error)
    return {
      isSuccess: false,
      message: `Template generation failed: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Validate CSV content without uploading
 */
export async function validateDatasetAction(
  optimizationId: string,
  csvContent: string
): Promise<ActionState<{
  isValid: boolean
  totalRows: number
  validRows: number
  errors: any[]
  preview: any[]
}>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to validate datasets"
    }
  }

  try {
    // Get optimization configuration
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found: ${optResult.message}`
      }
    }

    const optimization = optResult.data
    const config = optimization.config as OptimizationConfig

    // Parse and validate CSV
    const { headers, rows, errors: parseErrors } = parseCSVContent(csvContent)
    const validationErrors = validateDataset(headers, rows, config)
    
    const allErrors = [...parseErrors, ...validationErrors]
    
    // Count valid rows (rows without critical errors)
    const criticalErrors = allErrors.filter(e => 
      e.type === 'missing' && e.row > 0
    )
    
    const validRowCount = rows.filter((_, index) => {
      const rowNumber = index + 2
      return !criticalErrors.some(e => e.row === rowNumber)
    }).length

    // Create preview (first 5 rows)
    const preview = rows.slice(0, 5).map((row, index) => ({
      rowNumber: index + 2,
      data: row,
      hasErrors: allErrors.some(e => e.row === index + 2)
    }))

    return {
      isSuccess: true,
      message: "Validation completed",
      data: {
        isValid: allErrors.length === 0,
        totalRows: rows.length,
        validRows: validRowCount,
        errors: allErrors,
        preview
      }
    }

  } catch (error) {
    console.error("Error validating dataset:", error)
    return {
      isSuccess: false,
      message: `Validation failed: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Generate Excel template with advanced features
 */
export async function generateExcelTemplateAction(
  optimizationId: string
): Promise<ActionState<{ buffer: number[]; filename: string }>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to generate templates"
    }
  }

  try {
    // Get optimization configuration
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found: ${optResult.message}`
      }
    }

    const optimization = optResult.data
    const config = optimization.config as OptimizationConfig

    console.log('[EXCEL_TEMPLATE] Generating Excel template for optimization:', optimization.name)
    console.log('[EXCEL_TEMPLATE] Config:', JSON.stringify(config, null, 2))

    // Generate Excel template with advanced features
    const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
    const filename = `${sanitizedName}-template.xlsx`

    console.log('[EXCEL_TEMPLATE] About to call generateExcelTemplate with filename:', filename)
    const result = await generateExcelTemplate(config, filename)
    console.log('[EXCEL_TEMPLATE] Generated result:', {
      hasBuffer: !!result.buffer,
      bufferSize: result.buffer.byteLength,
      filename: result.filename
    })

    // Convert ArrayBuffer to regular array for serialization
    const uint8Array = new Uint8Array(result.buffer)
    const bufferArray = Array.from(uint8Array)

    console.log('[EXCEL_TEMPLATE] Converted to array, length:', bufferArray.length)

    return {
      isSuccess: true,
      message: "Excel template generated successfully",
      data: {
        buffer: bufferArray,
        filename: result.filename
      }
    }

  } catch (error) {
    console.error("Error generating Excel template:", error)
    return {
      isSuccess: false,
      message: `Excel template generation failed: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
