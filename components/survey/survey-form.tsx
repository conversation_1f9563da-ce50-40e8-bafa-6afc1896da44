"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { motion, AnimatePresence } from "framer-motion"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { saveSurveyAction, SurveyResponses } from "@/actions/survey-actions"
import { toast } from "@/components/ui/use-toast"
import {
  Building2,
  User,
  Target,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  Sparkles,
  Beaker,
  Factory,
  Zap
} from "lucide-react"

// Define the form schema with validation
const formSchema = z.object({
  industry: z.string().min(1, "Please select your industry"),
  role: z.string().min(1, "Please select your role"),
  useCase: z.string().min(1, "Please select your primary use case")
})

// Animation variants
const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
}

const stepVariants = {
  hidden: { opacity: 0, x: 20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.4 }
  },
  exit: {
    opacity: 0,
    x: -20,
    transition: { duration: 0.3 }
  }
}

// Step icons and content
const stepConfig = [
  {
    icon: Building2,
    title: "Industry",
    description: "Tell us about your field",
    color: "from-blue-500 to-indigo-600"
  },
  {
    icon: User,
    title: "Role",
    description: "What's your position?",
    color: "from-indigo-500 to-purple-600"
  },
  {
    icon: Target,
    title: "Use Case",
    description: "What's your main goal?",
    color: "from-purple-500 to-pink-600"
  }
]

export default function SurveyForm() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      industry: "",
      role: "",
      useCase: ""
    }
  })

  // Calculate progress based on filled fields
  const calculateProgress = () => {
    const values = form.getValues()
    const filledFields = Object.values(values).filter(Boolean).length
    return (filledFields / 3) * 100
  }

  // Handle next step with validation
  const handleNext = () => {
    const values = form.getValues()
    if (currentStep === 0 && values.industry) {
      setCurrentStep(1)
    } else if (currentStep === 1 && values.role) {
      setCurrentStep(2)
    }
  }

  // Handle previous step
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)
    try {
      const result = await saveSurveyAction(values as SurveyResponses, true)

      if (result.isSuccess) {
        toast({
          title: "Survey completed",
          description: "Thank you for completing the survey!",
          variant: "default"
        })
        router.push("/dashboard/home")
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to submit survey",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error submitting survey:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle skipping the survey
  const handleSkip = async () => {
    setIsSubmitting(true)
    try {
      // Save an empty survey response but mark it as completed
      const emptyResponses: SurveyResponses = {
        industry: "skipped",
        role: "skipped",
        useCase: "skipped"
      }

      const result = await saveSurveyAction(emptyResponses, true)

      if (result.isSuccess) {
        router.push("/dashboard/home")
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to skip survey",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error skipping survey:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Render the appropriate step
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <FormField
            control={form.control}
            name="industry"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>What industry are you in?</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your industry" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="pharmaceuticals">
                      Pharmaceuticals
                    </SelectItem>
                    <SelectItem value="chemicals">Chemicals</SelectItem>
                    <SelectItem value="materials">Materials Science</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                    <SelectItem value="energy">Energy</SelectItem>
                    <SelectItem value="biotech">Biotech</SelectItem>
                    <SelectItem value="academia">Academia/Research</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )
      case 1:
        return (
          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>What is your role?</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="researcher">Researcher</SelectItem>
                    <SelectItem value="scientist">Scientist</SelectItem>
                    <SelectItem value="engineer">Engineer</SelectItem>
                    <SelectItem value="manager">Manager/Team Lead</SelectItem>
                    <SelectItem value="executive">Executive</SelectItem>
                    <SelectItem value="student">Student</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )
      case 2:
        return (
          <FormField
            control={form.control}
            name="useCase"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>What is your primary use case?</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your primary use case" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="formulation">
                      Formulation Optimization
                    </SelectItem>
                    <SelectItem value="process">
                      Process Optimization
                    </SelectItem>
                    <SelectItem value="materials">
                      Materials Discovery
                    </SelectItem>
                    <SelectItem value="catalyst">
                      Catalyst Development
                    </SelectItem>
                    <SelectItem value="drug">Drug Discovery</SelectItem>
                    <SelectItem value="energy">Energy Systems</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )
      default:
        return null
    }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="grid grid-cols-1 gap-8 lg:grid-cols-2 lg:gap-12"
    >
      {/* Left Side - Form */}
      <div className="space-y-6">
        {/* Enhanced Progress Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <motion.h3
              className="from-primary bg-gradient-to-r to-blue-600 bg-clip-text text-xl font-semibold text-transparent"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              3 Quick Questions
            </motion.h3>
            <motion.span
              className="text-muted-foreground text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              Step {currentStep + 1} of 3
            </motion.span>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="space-y-2">
            <Progress
              value={((currentStep + 1) / 3) * 100}
              className="h-3 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20"
            />
            <div className="text-muted-foreground flex justify-between text-xs">
              {stepConfig.map((step, index) => (
                <span
                  key={index}
                  className={`flex items-center gap-1 ${
                    index <= currentStep ? "text-primary font-medium" : ""
                  }`}
                >
                  {index <= currentStep && <CheckCircle className="size-3" />}
                  {step.title}
                </span>
              ))}
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                variants={stepVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="min-h-[200px]"
              >
                {renderStep()}
              </motion.div>
            </AnimatePresence>

            {/* Enhanced Navigation Buttons */}
            <div className="flex justify-between pt-4">
              <div>
                {currentStep > 0 ? (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handlePrevious}
                      disabled={isSubmitting}
                      className="flex items-center gap-2"
                    >
                      <ArrowLeft className="size-4" />
                      Previous
                    </Button>
                  </motion.div>
                ) : (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleSkip}
                      disabled={isSubmitting}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      Skip for now
                    </Button>
                  </motion.div>
                )}
              </div>
              <div>
                {currentStep < 2 ? (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="button"
                      onClick={handleNext}
                      disabled={
                        isSubmitting ||
                        (currentStep === 0 && !form.getValues().industry) ||
                        (currentStep === 1 && !form.getValues().role)
                      }
                      className="from-primary hover:from-primary/90 flex items-center gap-2 bg-gradient-to-r to-blue-600 hover:to-blue-700"
                    >
                      Next
                      <ArrowRight className="size-4" />
                    </Button>
                  </motion.div>
                ) : (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="submit"
                      disabled={isSubmitting || !form.formState.isValid}
                      className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="size-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          Complete
                          <CheckCircle className="size-4" />
                        </>
                      )}
                    </Button>
                  </motion.div>
                )}
              </div>
            </div>
          </form>
        </Form>
      </div>

      {/* Right Side - Visual Elements */}
      <div className="hidden flex-col items-center justify-center space-y-6 lg:flex">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.5 }}
            className="relative"
          >
            {/* Dynamic Visual Content Based on Step */}
            {currentStep === 0 && (
              <div className="space-y-4 text-center">
                <div className="relative">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 20,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                    className="mx-auto flex size-32 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-indigo-600"
                  >
                    <Building2 className="size-16 text-white" />
                  </motion.div>
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute -right-2 -top-2 flex size-8 items-center justify-center rounded-full bg-yellow-400"
                  >
                    <Sparkles className="size-4 text-yellow-800" />
                  </motion.div>
                </div>
                <h4 className="text-foreground text-xl font-semibold">
                  Your Industry
                </h4>
                <p className="text-muted-foreground max-w-sm">
                  Help us understand your field so we can show you relevant
                  examples and workflows.
                </p>
              </div>
            )}

            {currentStep === 1 && (
              <div className="space-y-4 text-center">
                <div className="relative">
                  <motion.div
                    animate={{ y: [-5, 5, -5] }}
                    transition={{ duration: 3, repeat: Infinity }}
                    className="mx-auto flex size-32 items-center justify-center rounded-full bg-gradient-to-r from-indigo-500 to-purple-600"
                  >
                    <User className="size-16 text-white" />
                  </motion.div>
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute -bottom-2 -left-2 flex size-8 items-center justify-center rounded-full bg-green-400"
                  >
                    <Zap className="size-4 text-green-800" />
                  </motion.div>
                </div>
                <h4 className="text-foreground text-xl font-semibold">
                  Your Role
                </h4>
                <p className="text-muted-foreground max-w-sm">
                  Tell us about your position to get personalized
                  recommendations and tools.
                </p>
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-4 text-center">
                <div className="relative">
                  <motion.div
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="mx-auto flex size-32 items-center justify-center rounded-full bg-gradient-to-r from-purple-500 to-pink-600"
                  >
                    <Target className="size-16 text-white" />
                  </motion.div>
                  <motion.div
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="absolute -right-3 -top-3 size-6 rounded-full bg-orange-400"
                  />
                  <motion.div
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
                    className="absolute -bottom-3 -left-3 size-6 rounded-full bg-cyan-400"
                  />
                </div>
                <h4 className="text-foreground text-xl font-semibold">
                  Your Goals
                </h4>
                <p className="text-muted-foreground max-w-sm">
                  Share your primary use case to unlock the most relevant
                  platform features.
                </p>
              </div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Benefits Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="from-primary/5 max-w-sm rounded-lg bg-gradient-to-r to-blue-500/5 p-6"
        >
          <h5 className="text-foreground mb-2 flex items-center gap-2 font-medium">
            <Beaker className="text-primary size-4" />
            What you'll get:
          </h5>
          <ul className="text-muted-foreground space-y-1 text-sm">
            <li>• Tailored examples for your industry</li>
            <li>• Relevant optimization templates</li>
            <li>• Personalized feature recommendations</li>
            <li>• Industry-specific workflows</li>
          </ul>
        </motion.div>
      </div>
    </motion.div>
  )
}
