"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { checkAcademicSurveyCompletionAction } from "@/actions/academic-survey-actions"
import { Card, CardContent } from "@/components/ui/card"
import AcademicSurveyForm from "@/components/survey/academic-survey-form"

export default async function AcademicSurveyPage() {
  const { userId } = await auth()

  // If user is not authenticated, redirect to login
  if (!userId) {
    redirect("/login")
  }

  // Check if the user has already completed the survey
  const surveyStatus = await checkAcademicSurveyCompletionAction()

  // If the user has already completed the survey, redirect to dashboard
  if (surveyStatus.isSuccess && surveyStatus.hasCompleted) {
    redirect("/dashboard/home")
  }

  return (
    <div className="from-background via-background min-h-screen bg-gradient-to-br to-blue-50/30 dark:to-blue-950/30">
      <div className="container mx-auto px-4 py-8 lg:py-12">
        {/* Header Section */}
        <div className="mb-8 text-center lg:mb-12">
          <h1 className="mb-4 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-3xl font-bold text-transparent lg:text-4xl">
            Academic User Survey
          </h1>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
            Complete your academic registration for 90-day full access! Tell us
            about your optimization tool background and research area to get the
            most relevant platform experience.
          </p>
        </div>

        {/* Enhanced Academic Survey Form Container */}
        <div className="mx-auto max-w-6xl">
          <Card className="border-0 bg-gradient-to-r from-white/80 to-white/60 shadow-xl backdrop-blur-sm dark:from-gray-900/80 dark:to-gray-900/60">
            <CardContent className="p-6 lg:p-8">
              <AcademicSurveyForm />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
