// app/api/proxy/constraints/validate/route.ts
import { NextRequest, NextResponse } from "next/server"
import { API_CONFIG } from "@/lib/config"

/**
 * Proxy endpoint for constraint validation to avoid CORS issues
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json()
    const { constraint_config, parameter_names, parameter_types } = body

    if (!constraint_config) {
      return NextResponse.json(
        { error: "constraint_config is required" },
        { status: 400 }
      )
    }

    if (!parameter_names || !Array.isArray(parameter_names)) {
      return NextResponse.json(
        { error: "parameter_names array is required" },
        { status: 400 }
      )
    }

    // Get the API URL from the centralized config
    const apiUrl = `${API_CONFIG.BASE_URL}/constraints/validate`

    console.log("Constraint Validation Proxy: Fetching from", apiUrl)
    console.log("Constraint Validation Proxy: Request body:", {
      constraint_config,
      parameter_names,
      parameter_types
    })

    // Prepare the request body for the backend API
    const requestBody = {
      ...constraint_config
      // The backend expects parameter_names as a query parameter, but we'll include it in the body for simplicity
    }

    // Construct the URL with query parameters
    const url = new URL(apiUrl)
    parameter_names.forEach(param => {
      url.searchParams.append("parameter_names", param)
    })

    console.log("Constraint Validation Proxy: Final URL:", url.toString())

    // Make the request to the API
    const response = await fetch(url.toString(), {
      method: "POST",
      headers: {
        "X-API-Key": API_CONFIG.API_KEY,
        Accept: "application/json",
        "Content-Type": "application/json"
      },
      body: JSON.stringify(requestBody),
      // Add a reasonable timeout
      signal: AbortSignal.timeout(15000)
    })

    // Get the response as text first for debugging
    const responseText = await response.text()
    console.log("Constraint Validation Proxy: Raw response:", responseText)
    console.log(
      "Constraint Validation Proxy: Response status:",
      response.status
    )

    // Try to parse the response as JSON
    let data
    try {
      data = JSON.parse(responseText)
      console.log("Constraint Validation Proxy: Parsed JSON:", data)
    } catch (error) {
      console.error("Constraint Validation Proxy: Failed to parse JSON:", error)
      return NextResponse.json(
        {
          error: "Failed to parse API response as JSON",
          raw: responseText,
          status: response.status
        },
        { status: 500 }
      )
    }

    // If the backend returned an error status, forward it
    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    // Return the data
    return NextResponse.json(data)
  } catch (error) {
    console.error("Constraint Validation Proxy error:", error)
    return NextResponse.json(
      {
        error: "Failed to validate constraint",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}
