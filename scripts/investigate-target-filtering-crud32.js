#!/usr/bin/env node

/**
 * Investigation script for Target filtering discrepancy - Test CRUD Filtering32
 * Specific case: Target 1 bounds change from [0, 100] to [25, 100]
 * Expected: 3 experiments should be dropped
 * Actual: Preview shows only 1 excluded
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, and } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema definitions
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

const samplesTable = pgTable("samples", {
  id: uuid("id").primaryKey(),
  optimizationId: uuid("optimization_id").notNull(),
  parameters: jsonb("parameters").notNull(),
  batchId: text("batch_id").notNull(),
  sampleIndex: text("sample_index").notNull(),
  samplingMethod: text("sampling_method").notNull(),
  sampleClass: text("sample_class").notNull(),
  targetValues: jsonb("target_values"),
  status: text("status").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function getBackendMeasurements(optimizerId) {
  try {
    const response = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/measurements`);
    if (!response.ok) {
      throw new Error(`Failed to get measurements: ${response.statusText}`);
    }
    const data = await response.json();
    return data.measurements || [];
  } catch (error) {
    console.error('Error fetching backend measurements:', error);
    return [];
  }
}

async function investigateTargetFilteringCRUD32() {
  console.log('🎯 INVESTIGATING TARGET FILTERING DISCREPANCY - Test CRUD Filtering32');
  console.log('====================================================================');
  console.log('Case: Target 1 bounds change from [0, 100] to [25, 100]');
  console.log('Expected: 3 experiments should be dropped');
  console.log('Actual: Preview shows only 1 excluded');
  console.log('');

  let client;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    // Find the optimization
    const optimizationName = "test CRUD filtering32";
    const optimizationsList = await db.select().from(optimizationsTable);
    const opt = optimizationsList.find(o => o.name === optimizationName);
    
    if (!opt) {
      console.error(`❌ Optimization "${optimizationName}" not found`);
      console.log('Available optimizations:');
      optimizationsList.forEach(o => console.log(`  - ${o.name}`));
      return;
    }
    
    console.log(`✅ Found optimization:`);
    console.log(`  - ID: ${opt.id}`);
    console.log(`  - Optimizer ID: ${opt.optimizerId}`);
    console.log(`  - Name: ${opt.name}`);
    console.log(`  - Status: ${opt.status}`);
    console.log(`  - Created: ${opt.createdAt}`);
    console.log(`  - Updated: ${opt.updatedAt}`);
    
    // Show current configuration
    console.log('\n📊 CURRENT CONFIGURATION:');
    console.log(`  - Config: ${JSON.stringify(opt.config, null, 2)}`);
    
    // Get database samples
    const samples = await db.select().from(samplesTable).where(eq(samplesTable.optimizationId, opt.id));
    console.log(`\n📈 DATABASE SAMPLES (${samples.length} total):`);
    
    if (samples.length === 0) {
      console.log('  No samples found in database');
    } else {
      samples.forEach((sample, index) => {
        console.log(`\n  ${index + 1}. Sample ${sample.id.slice(-12)}:`);
        console.log(`     - Parameters: ${JSON.stringify(sample.parameters)}`);
        console.log(`     - Target Values: ${JSON.stringify(sample.targetValues)}`);
        console.log(`     - Status: ${sample.status}`);
        console.log(`     - Sample Class: ${sample.sampleClass}`);
        console.log(`     - Batch ID: ${sample.batchId}`);
        console.log(`     - Created: ${sample.createdAt}`);
      });
    }
    
    // STEP 1: Check backend campaign measurements BEFORE filtering
    console.log('\n🔍 STEP 1: CHECKING BACKEND CAMPAIGN MEASUREMENTS (BEFORE)');
    console.log('===========================================================');
    
    const backendMeasurementsBefore = await getBackendMeasurements(opt.optimizerId);
    console.log(`Backend campaign has ${backendMeasurementsBefore.length} measurements`);

    // Only count submitted samples for comparison
    const submittedSamples = samples.filter(s => s.status === 'submitted' || s.targetValues);
    console.log(`Database has ${submittedSamples.length} submitted samples (with target values)`);
    
    if (backendMeasurementsBefore.length !== submittedSamples.length) {
      console.log(`❌ DISCREPANCY: Database has ${submittedSamples.length} submitted samples, backend has ${backendMeasurementsBefore.length}`);
    } else {
      console.log(`✅ MATCH: Database and backend have same number of measurements`);
    }
    
    // Analyze current Target 1 values
    console.log('\n📊 STEP 2: ANALYZING CURRENT TARGET 1 VALUES');
    console.log('=============================================');
    
    if (backendMeasurementsBefore.length > 0) {
      console.log('\nBackend measurements Target 1 analysis:');
      backendMeasurementsBefore.forEach((measurement, index) => {
        const target1 = measurement['Target 1'];
        const target2 = measurement['Target 2'];
        const fitNr = measurement['FitNr'];
        const batchNr = measurement['BatchNr'];
        
        console.log(`  ${index + 1}. Target 1: ${target1}, Target 2: ${target2} (Batch: ${batchNr}, Fit: ${fitNr})`);
        
        // Check if this would be filtered by new bounds [25, 100]
        if (typeof target1 === 'number' && Number.isFinite(target1)) {
          if (target1 < 25) {
            console.log(`     ❌ SHOULD BE FILTERED: Target 1 = ${target1} < 25`);
          } else if (target1 > 100) {
            console.log(`     ❌ SHOULD BE FILTERED: Target 1 = ${target1} > 100`);
          } else {
            console.log(`     ✅ SHOULD BE RETAINED: Target 1 = ${target1} in [25, 100]`);
          }
        } else {
          console.log(`     ❌ SHOULD BE FILTERED: Target 1 = ${target1} (invalid value)`);
        }
      });
    }
    
    if (submittedSamples.length > 0) {
      console.log('\nDatabase samples Target 1 analysis:');
      submittedSamples.forEach((sample, index) => {
        const target1 = sample.targetValues?.['Target 1'];
        const target2 = sample.targetValues?.['Target 2'];
        
        console.log(`  ${index + 1}. Target 1: ${target1}, Target 2: ${target2}`);
        
        // Check if this would be filtered by new bounds [25, 100]
        if (typeof target1 === 'number' && Number.isFinite(target1)) {
          if (target1 < 25) {
            console.log(`     ❌ SHOULD BE FILTERED: Target 1 = ${target1} < 25`);
          } else if (target1 > 100) {
            console.log(`     ❌ SHOULD BE FILTERED: Target 1 = ${target1} > 100`);
          } else {
            console.log(`     ✅ SHOULD BE RETAINED: Target 1 = ${target1} in [25, 100]`);
          }
        } else {
          console.log(`     ❌ SHOULD BE FILTERED: Target 1 = ${target1} (invalid value)`);
        }
      });
    }
    
    // STEP 3: Test the actual Target 1 bounds change
    console.log('\n🧪 STEP 3: TESTING TARGET 1 BOUNDS CHANGE [0, 100] → [25, 100]');
    console.log('================================================================');
    
    console.log(`📤 Testing Target 1 bounds change to [25, 100]...`);
    
    const targetBoundsResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target_bounds: {
          "Target 1": [25, 100]
        },
        preview_only: true
      })
    });
    
    if (!targetBoundsResponse.ok) {
      const errorText = await targetBoundsResponse.text();
      console.error(`❌ Target bounds test failed: ${targetBoundsResponse.statusText} - ${errorText}`);
      return;
    }
    
    const targetBoundsResult = await targetBoundsResponse.json();
    console.log(`📥 Backend response:`, JSON.stringify(targetBoundsResult, null, 2));
    
    // STEP 4: Analyze the discrepancy
    console.log('\n📊 STEP 4: ANALYZING THE DISCREPANCY');
    console.log('====================================');
    
    // Count expected filtering from backend measurements
    let expectedRetained = 0;
    let expectedFiltered = 0;
    
    if (backendMeasurementsBefore.length > 0) {
      backendMeasurementsBefore.forEach(measurement => {
        const target1 = measurement['Target 1'];
        
        if (typeof target1 === 'number' && Number.isFinite(target1)) {
          if (target1 >= 25 && target1 <= 100) {
            expectedRetained++;
          } else {
            expectedFiltered++;
          }
        } else {
          expectedFiltered++; // Invalid values should be filtered
        }
      });
    }
    
    console.log(`\n📊 EXPECTED FILTERING (based on backend measurements):`);
    console.log(`  - Should be retained: ${expectedRetained}`);
    console.log(`  - Should be filtered: ${expectedFiltered}`);
    console.log(`  - Total: ${expectedRetained + expectedFiltered}`);
    
    console.log(`\n📊 ACTUAL FILTERING (from backend response):`);
    console.log(`  - Actually retained: ${targetBoundsResult.valid_count || 0}`);
    console.log(`  - Actually filtered: ${targetBoundsResult.dropped_count || 0}`);
    console.log(`  - Total: ${(targetBoundsResult.valid_count || 0) + (targetBoundsResult.dropped_count || 0)}`);
    
    const retainedMatch = expectedRetained === (targetBoundsResult.valid_count || 0);
    const filteredMatch = expectedFiltered === (targetBoundsResult.dropped_count || 0);
    
    if (retainedMatch && filteredMatch) {
      console.log(`✅ FILTERING RESULTS MATCH EXPECTATIONS`);
    } else {
      console.log(`❌ FILTERING RESULTS DO NOT MATCH EXPECTATIONS`);
      if (!retainedMatch) {
        console.log(`  - Retained mismatch: expected ${expectedRetained}, got ${targetBoundsResult.valid_count || 0}`);
      }
      if (!filteredMatch) {
        console.log(`  - Filtered mismatch: expected ${expectedFiltered}, got ${targetBoundsResult.dropped_count || 0}`);
      }
    }
    
    // Analyze dropped measurements
    if (targetBoundsResult.dropped_measurements && targetBoundsResult.dropped_measurements.length > 0) {
      console.log(`\n🔍 DROPPED MEASUREMENTS ANALYSIS:`);
      console.log(`Total dropped: ${targetBoundsResult.dropped_measurements.length}`);
      
      targetBoundsResult.dropped_measurements.forEach((measurement, index) => {
        const target1 = measurement['Target 1'];
        const target2 = measurement['Target 2'];
        const reason = measurement.exclusion_reason;
        
        console.log(`\n  ${index + 1}. Dropped measurement:`);
        console.log(`     - Target 1: ${target1}`);
        console.log(`     - Target 2: ${target2}`);
        console.log(`     - Exclusion reason: "${reason}"`);
        console.log(`     - Batch: ${measurement.BatchNr}, Fit: ${measurement.FitNr}`);
        
        // Validate the exclusion
        if (typeof target1 === 'number' && Number.isFinite(target1)) {
          if (target1 < 25) {
            console.log(`     ✅ CORRECTLY FILTERED: Target 1 = ${target1} < 25`);
          } else if (target1 > 100) {
            console.log(`     ✅ CORRECTLY FILTERED: Target 1 = ${target1} > 100`);
          } else {
            console.log(`     ❌ INCORRECTLY FILTERED: Target 1 = ${target1} is within [25, 100]!`);
          }
        } else {
          console.log(`     ✅ CORRECTLY FILTERED: Invalid Target 1 value`);
        }
      });
    }
    
    // Check if there are measurements that should be filtered but weren't
    console.log(`\n🔍 CHECKING FOR MISSED FILTERING:`);
    if (backendMeasurementsBefore.length > 0) {
      const shouldBeFiltered = backendMeasurementsBefore.filter(measurement => {
        const target1 = measurement['Target 1'];
        return typeof target1 === 'number' && Number.isFinite(target1) && (target1 < 25 || target1 > 100);
      });
      
      const actuallyDropped = targetBoundsResult.dropped_measurements || [];
      
      console.log(`Measurements that should be filtered: ${shouldBeFiltered.length}`);
      console.log(`Measurements actually dropped: ${actuallyDropped.length}`);
      
      if (shouldBeFiltered.length > actuallyDropped.length) {
        console.log(`❌ MISSING FILTERING: ${shouldBeFiltered.length - actuallyDropped.length} measurements should be filtered but weren't`);
        
        shouldBeFiltered.forEach((measurement, index) => {
          const target1 = measurement['Target 1'];
          const batchNr = measurement['BatchNr'];
          const fitNr = measurement['FitNr'];
          
          // Check if this measurement is in the dropped list
          const wasDropped = actuallyDropped.some(dropped => 
            dropped['Target 1'] === target1 && 
            dropped['BatchNr'] === batchNr && 
            dropped['FitNr'] === fitNr
          );
          
          if (!wasDropped) {
            console.log(`  ${index + 1}. MISSED: Target 1 = ${target1}, Batch = ${batchNr}, Fit = ${fitNr}`);
          }
        });
      }
    }
    
    console.log('\n🎯 TARGET FILTERING INVESTIGATION COMPLETE');
    console.log('==========================================');
    
  } catch (error) {
    console.error('❌ Error during investigation:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

// Run the investigation
investigateTargetFilteringCRUD32().catch(console.error);
