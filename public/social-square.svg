<svg width="800" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Brand gradient matching from-primary to-blue-600 exactly -->
    <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:hsl(0, 0%, 9%);stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>

    <!-- Clean radial background for square format -->
    <radialGradient id="backgroundGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5f5f5;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="800" fill="url(#backgroundGradient)" />
  
  <!-- Decorative elements positioned for square format -->
  <circle cx="120" cy="120" r="50" fill="#3b82f6" opacity="0.04" />
  <circle cx="680" cy="680" r="70" fill="#171717" opacity="0.02" />
  <circle cx="650" cy="150" r="30" fill="#3b82f6" opacity="0.06" />
  <circle cx="150" cy="650" r="40" fill="#171717" opacity="0.03" />

  <!-- Main brand name - clean, no filters -->
  <text x="400" y="350"
        font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
        font-weight="700"
        font-size="56"
        text-anchor="middle"
        fill="url(#brandGradient)">
    INNOptimizer™
  </text>

  <!-- Tagline - split into two lines for better square format -->
  <text x="400" y="420"
        font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
        font-weight="400"
        font-size="18"
        text-anchor="middle"
        fill="#737373">
    Complex optimization made simple
  </text>

  <text x="400" y="450"
        font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
        font-weight="400"
        font-size="18"
        text-anchor="middle"
        fill="#737373">
    through intelligent experimental design.
  </text>
  
  <!-- Decorative accent circle -->
  <circle cx="400" cy="520" r="2" fill="url(#brandGradient)" opacity="0.6" />
  <circle cx="380" cy="520" r="1.5" fill="url(#brandGradient)" opacity="0.4" />
  <circle cx="420" cy="520" r="1.5" fill="url(#brandGradient)" opacity="0.4" />
</svg>
