"use client"

import { useEffect, useState, useRef } from "react"
import {
  motion,
  useAnimation,
  useMotionValue,
  useTransform,
  AnimatePresence
} from "framer-motion"
import { useIsMobile } from "@/lib/hooks/use-mobile"

// Types for our animated elements
interface AnimatedDot {
  id: number
  x: number
  y: number
  size: number
  color: string
  delay: number
  speed: number
}

interface SamplingPoint {
  id: number
  x: number
  y: number
  createdAt: number
  value: number
}

interface FloatingText {
  id: number
  x: number
  y: number
  text: string
  opacity: number
  scale: number
}

// Marketing keywords that will float in the background
const marketingKeywords = [
  "Optimization",
  "AI-Powered",
  "Efficiency",
  "Innovation",
  "Data-Driven",
  "Intelligent",
  "Precision",
  "Accelerate",
  "Research",
  "Discovery",
  "Breakthrough",
  "Advanced",
  "Streamlined",
  "Automated",
  "Cutting-Edge"
]

export const EnhancedBayesianBackground = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const [dots, setDots] = useState<AnimatedDot[]>([])
  const [samplingPoints, setSamplingPoints] = useState<SamplingPoint[]>([])
  const [floatingTexts, setFloatingTexts] = useState<FloatingText[]>([])
  const curveControls = useAnimation()
  const secondaryCurveControls = useAnimation()
  const isMobile = useIsMobile()

  // Motion values for the animated curves
  const curveProgress = useMotionValue(0)
  const curveY = useTransform(
    curveProgress,
    [0, 0.25, 0.5, 0.75, 1],
    [0, -30, 0, 30, 0]
  )

  // Update dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        // Get the parent container (hero section) dimensions
        const parent = containerRef.current.parentElement
        if (parent) {
          const { width, height } = parent.getBoundingClientRect()
          if (width > 0 && height > 0) {
            setDimensions({ width, height })
            return
          }
        }

        // Fallback to container's own dimensions
        const { width, height } = containerRef.current.getBoundingClientRect()
        if (width > 0 && height > 0) {
          setDimensions({ width, height })
          return
        }

        // Final fallback to viewport dimensions
        const viewportWidth = window.innerWidth
        const viewportHeight = window.innerHeight
        setDimensions({
          width: Math.max(viewportWidth, 320),
          height: Math.max(viewportHeight, 568)
        })
      }
    }

    // Initial update with delay to ensure DOM is ready
    const timer = setTimeout(updateDimensions, 100)

    // Add resize listener
    window.addEventListener("resize", updateDimensions)

    // Also listen for orientation changes on mobile
    window.addEventListener("orientationchange", () => {
      setTimeout(updateDimensions, 200)
    })

    // Cleanup
    return () => {
      clearTimeout(timer)
      window.removeEventListener("resize", updateDimensions)
      window.removeEventListener("orientationchange", updateDimensions)
    }
  }, [])

  // Generate initial dots with improved distribution and variety
  useEffect(() => {
    if (dimensions.width === 0 || dimensions.height === 0) {
      // If dimensions are still not available, try to update them again
      const updateDimensions = () => {
        if (containerRef.current) {
          const parent = containerRef.current.parentElement
          if (parent) {
            const { width, height } = parent.getBoundingClientRect()
            if (width > 0 && height > 0) {
              setDimensions({ width, height })
            }
          }
        }
      }
      updateDimensions()
      return
    }

    const newDots: AnimatedDot[] = []
    const dotCount = isMobile ? 20 : 40 // Reduced dot count for mobile performance

    // Create a grid to ensure better distribution
    const gridCells = 8
    const cellWidth = dimensions.width / gridCells
    const cellHeight = dimensions.height / gridCells

    for (let i = 0; i < dotCount; i++) {
      // Determine which cell this dot belongs to
      const cellX = i % gridCells
      const cellY = Math.floor(i / gridCells) % gridCells

      // Position within the cell with some randomness
      const x = (cellX + 0.2 + Math.random() * 0.6) * cellWidth
      const y = (cellY + 0.2 + Math.random() * 0.6) * cellHeight

      // More variety in sizes and colors
      const size = Math.random() * 4 + 2

      // Create a more interesting color palette
      let color
      const colorType = Math.floor(Math.random() * 5)
      switch (colorType) {
        case 0:
          color = "rgba(59, 130, 246, 0.5)" // Blue
          break
        case 1:
          color = "rgba(99, 102, 241, 0.4)" // Indigo
          break
        case 2:
          color = "rgba(79, 70, 229, 0.4)" // Violet
          break
        case 3:
          color = "rgba(147, 51, 234, 0.3)" // Purple
          break
        default:
          color = "rgba(236, 72, 153, 0.3)" // Pink
      }

      newDots.push({
        id: i,
        x,
        y,
        size,
        color,
        delay: i * 0.05, // Faster appearance
        speed: 0.5 + Math.random() * 1.5 // Variable speeds
      })
    }

    setDots(newDots)

    // Start the curve animations
    animateCurves()

    // Initialize floating marketing keywords
    initFloatingTexts()
  }, [dimensions])

  // Initialize floating marketing texts
  const initFloatingTexts = () => {
    if (dimensions.width === 0 || dimensions.height === 0) return

    const texts: FloatingText[] = []
    const textCount = 8 // Number of floating texts to show

    for (let i = 0; i < textCount; i++) {
      texts.push({
        id: i,
        x: Math.random() * dimensions.width * 0.8 + dimensions.width * 0.1,
        y: Math.random() * dimensions.height * 0.8 + dimensions.height * 0.1,
        text: marketingKeywords[
          Math.floor(Math.random() * marketingKeywords.length)
        ],
        opacity: 0.1 + Math.random() * 0.2,
        scale: 0.8 + Math.random() * 0.4
      })
    }

    setFloatingTexts(texts)

    // Periodically update floating texts
    const interval = setInterval(() => {
      setFloatingTexts(prev => {
        // Replace one random text
        if (prev.length > 0) {
          const newTexts = [...prev]
          const indexToReplace = Math.floor(Math.random() * newTexts.length)

          newTexts[indexToReplace] = {
            ...newTexts[indexToReplace],
            text: marketingKeywords[
              Math.floor(Math.random() * marketingKeywords.length)
            ],
            opacity: 0.1 + Math.random() * 0.2,
            scale: 0.8 + Math.random() * 0.4
          }

          return newTexts
        }
        return prev
      })
    }, 3000)

    return () => clearInterval(interval)
  }

  // Animate multiple curves continuously with different patterns
  const animateCurves = async () => {
    // Primary curve animation
    curveControls.start({
      pathLength: [0, 1],
      pathOffset: [0, 1],
      transition: {
        duration: 8,
        ease: "easeInOut",
        repeat: Infinity,
        repeatType: "loop"
      }
    })

    // Secondary curve animation (offset timing)
    secondaryCurveControls.start({
      pathLength: [0, 1],
      pathOffset: [0, 1],
      transition: {
        duration: 12,
        ease: "easeInOut",
        repeat: Infinity,
        repeatType: "loop",
        delay: 2
      }
    })
  }

  // Define a target function that we're trying to optimize
  // This simulates the unknown function in Bayesian optimization
  const targetFunction = (x: number, y: number): number => {
    // Create an interesting 2D function with multiple peaks and valleys
    // Normalized to the canvas dimensions
    const normalizedX = x / (dimensions.width || 1)

    // For visualization purposes, we'll use a 1D function that depends only on x
    // This makes it easier to visualize as a curve across the screen
    return (
      Math.sin(normalizedX * 10) * 1.5 +
      Math.sin(normalizedX * 20) * 0.5 +
      Math.sin(normalizedX * 5) * 0.8 -
      Math.pow(normalizedX - 0.5, 2) * 2
    )
  }

  // State to track animation time for continuous movement
  const [animationTime, setAnimationTime] = useState(0)

  // Update animation time for continuous movement
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationTime(prev => (prev + 0.01) % 100) // Slowly increment time
    }, 50)

    return () => clearInterval(interval)
  }, [])

  // Function to get the true function value at any x position with animation
  const getTrueFunction = (x: number): number => {
    const normalizedX = x / (dimensions.width || 1)

    // Add time-based animation to make the function move continuously
    const timeOffset = Math.sin(animationTime * 0.2) * 0.2
    const phaseShift = animationTime * 0.1

    return (
      Math.sin((normalizedX + timeOffset) * 10 + phaseShift) * 1.5 +
      Math.sin((normalizedX - timeOffset) * 20 + phaseShift * 0.5) * 0.5 +
      Math.sin((normalizedX + timeOffset * 0.5) * 5 - phaseShift * 0.3) * 0.8 -
      Math.pow(normalizedX - 0.5 + timeOffset * 0.1, 2) * 2
    )
  }

  // Add a new sampling point using a strategy similar to Bayesian optimization
  const addSamplingPoint = () => {
    if (dimensions.width === 0 || dimensions.height === 0) return

    // In real Bayesian optimization, we would select points with high expected improvement
    // Here we'll simulate this by:
    // 1. Sometimes exploring random areas (exploration)
    // 2. Sometimes focusing near existing good points (exploitation)
    // 3. Sometimes focusing on areas with high uncertainty (areas with few samples)

    let x: number, y: number

    // Choose strategy: explore (random) or exploit (near good points)
    const strategy = Math.random()

    if (strategy < 0.3 || samplingPoints.length < 3) {
      // Exploration: Choose a random point, but avoid areas that are already well-sampled
      // Divide the space into a grid
      const gridCells = 6
      const cellWidth = dimensions.width / gridCells
      const cellHeight = dimensions.height / gridCells

      // Find cells with fewer samples
      const cellCounts: number[][] = Array(gridCells)
        .fill(0)
        .map(() => Array(gridCells).fill(0))

      samplingPoints.forEach(point => {
        const cellX = Math.floor(point.x / cellWidth)
        const cellY = Math.floor(point.y / cellHeight)
        if (
          cellX >= 0 &&
          cellX < gridCells &&
          cellY >= 0 &&
          cellY < gridCells
        ) {
          cellCounts[cellX][cellY]++
        }
      })

      // Find cells with minimum counts
      let minCount = Infinity
      const candidateCells: [number, number][] = []

      for (let i = 0; i < gridCells; i++) {
        for (let j = 0; j < gridCells; j++) {
          if (cellCounts[i][j] < minCount) {
            minCount = cellCounts[i][j]
            candidateCells.length = 0
            candidateCells.push([i, j])
          } else if (cellCounts[i][j] === minCount) {
            candidateCells.push([i, j])
          }
        }
      }

      // Choose a random cell from candidates
      const [cellX, cellY] =
        candidateCells[Math.floor(Math.random() * candidateCells.length)]

      // Position within the cell with some randomness
      x = (cellX + 0.3 + Math.random() * 0.4) * cellWidth
      y = (cellY + 0.3 + Math.random() * 0.4) * cellHeight
    } else {
      // Exploitation: Sample near the best points we've found so far
      // Sort points by value (assuming higher is better)
      const sortedPoints = [...samplingPoints].sort((a, b) => b.value - a.value)

      // Take one of the top points
      const topIndex = Math.floor(
        Math.random() * Math.min(3, sortedPoints.length)
      )
      const bestPoint = sortedPoints[topIndex]

      // Sample near this point with some noise
      const explorationRadius = dimensions.width * 0.1
      x = bestPoint.x + (Math.random() * 2 - 1) * explorationRadius
      y = bestPoint.y + (Math.random() * 2 - 1) * explorationRadius

      // Ensure within bounds
      x = Math.max(0, Math.min(dimensions.width, x))
      y = Math.max(0, Math.min(dimensions.height, y))
    }

    // Calculate the value using our target function
    const value = targetFunction(x, y)

    setSamplingPoints(prev => [
      ...prev,
      {
        id: Date.now(),
        x,
        y,
        value,
        createdAt: Date.now()
      }
    ])
  }

  // Periodically add new sampling points to simulate Bayesian optimization process
  useEffect(() => {
    if (dimensions.width === 0 || dimensions.height === 0) return

    // Add initial sampling points (initial design of experiments)
    const initialPointCount = isMobile ? 3 : 5
    for (let i = 0; i < initialPointCount; i++) {
      // For initial points, use a Latin Hypercube-like sampling
      // This ensures good coverage of the space
      const x =
        ((i + 0.5) * dimensions.width) / initialPointCount +
        ((Math.random() * 0.8 - 0.4) * dimensions.width) / initialPointCount
      const y =
        ((((i * 2) % initialPointCount) + 0.5) * dimensions.height) /
          initialPointCount +
        ((Math.random() * 0.8 - 0.4) * dimensions.height) / initialPointCount

      const value = targetFunction(x, y)

      setSamplingPoints(prev => [
        ...prev,
        {
          id: Date.now() + i,
          x,
          y,
          value,
          createdAt: Date.now()
        }
      ])
    }

    // Add new points periodically to simulate the iterative optimization process
    const interval = setInterval(() => {
      // Simulate the iterative nature of Bayesian optimization
      if (Math.random() < 0.3) {
        addSamplingPoint()
      }

      // Remove old sampling points but keep a minimum number
      setSamplingPoints(prev => {
        const minPoints = isMobile ? 5 : 8
        if (prev.length <= minPoints) return prev
        return prev.filter(point => Date.now() - point.createdAt < 12000)
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [dimensions])

  // Handle click to add new sampling point with enhanced feedback
  const handleClick = (e: React.MouseEvent) => {
    if (!containerRef.current) return

    const rect = containerRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    // Calculate value using our target function
    const value = targetFunction(x, y)

    // Add the new point with a visual pulse effect
    setSamplingPoints(prev => [
      ...prev,
      {
        id: Date.now(),
        x,
        y,
        value,
        createdAt: Date.now()
      }
    ])
  }

  // Generate the true function curve path
  const generateTrueFunctionPath = () => {
    if (dimensions.width === 0) return ""

    const height = dimensions.height / 2
    const width = dimensions.width

    // Number of points to use for the curve
    const numPoints = 200
    const points: { x: number; y: number }[] = []

    // Generate points for the true function
    for (let i = 0; i < numPoints; i++) {
      const x = (i / (numPoints - 1)) * width

      // Get true function value and scale it for visualization
      const trueValue = getTrueFunction(x)
      const y = height - trueValue * 40 // Scale and invert (negative is up)

      points.push({ x, y })
    }

    // Convert points to SVG path
    let path = `M ${points[0].x},${points[0].y}`

    // Use curve interpolation for smoother path
    for (let i = 1; i < points.length; i++) {
      path += ` L ${points[i].x},${points[i].y}`
    }

    return path
  }

  // Generate a Gaussian process curve that adapts to sampling points
  const generateGaussianCurvePath = (confidenceInterval = 0) => {
    if (dimensions.width === 0) return ""

    const height = dimensions.height / 2
    const width = dimensions.width

    // Number of points to use for the curve
    const numPoints = 150
    const points: { x: number; y: number; uncertainty: number }[] = []

    // Generate base points for the Gaussian Process curve
    for (let i = 0; i < numPoints; i++) {
      const x = (i / (numPoints - 1)) * width

      // Start with a flat line at the center
      let y = height
      let uncertainty = 60 // Default uncertainty when no points

      if (samplingPoints.length > 0) {
        // Calculate GP mean and variance at this point
        let meanSum = 0
        let weightSum = 0
        let varianceSum = 0

        // For each observed point, calculate its influence using RBF kernel
        samplingPoints.forEach(point => {
          // Calculate distance from this curve point to the sampling point
          const dx = x - point.x

          // Use a Gaussian/RBF kernel to determine influence
          // The closer the sampling point, the stronger the influence
          const lengthScale = width * 0.1 // Controls how far the influence reaches
          const kernelValue = Math.exp(
            -(dx * dx) / (2 * lengthScale * lengthScale)
          )

          // Add weighted contribution to mean
          meanSum += kernelValue * point.value
          weightSum += kernelValue

          // Reduce uncertainty near observed points
          // The closer to an observation, the lower the uncertainty
          varianceSum += kernelValue
        })

        // Calculate weighted mean
        if (weightSum > 0) {
          y = height - (meanSum / weightSum) * 40 // Scale for visualization
        }

        // Calculate uncertainty (higher near edges, lower near observations)
        // This simulates how GP uncertainty behaves
        const distanceUncertainty = Math.min(
          Math.abs(x) / (width * 0.2),
          Math.abs(x - width) / (width * 0.2)
        )

        // Normalize variance between 0 and 1, with 1 being far from observations
        const normalizedVariance = Math.max(0, 1 - varianceSum * 0.8)

        // Scale uncertainty based on distance from observations and edges
        uncertainty = 20 + normalizedVariance * 60

        // Reduce uncertainty more near the best observed points
        const bestPoints = [...samplingPoints]
          .sort((a, b) => b.value - a.value)
          .slice(0, 3)
        bestPoints.forEach(point => {
          const dx = x - point.x
          const closenessFactor = Math.exp(
            -(dx * dx) / (2 * (width * 0.05) * (width * 0.05))
          )
          uncertainty *= 1 - closenessFactor * 0.5
        })
      }

      // Apply confidence interval scaling
      uncertainty = uncertainty * (1 + confidenceInterval * 0.5)

      points.push({ x, y, uncertainty })
    }

    // For confidence intervals, return upper or lower bound
    if (confidenceInterval !== 0) {
      // Convert points to SVG path for confidence bound
      let path = `M ${points[0].x},${points[0].y + confidenceInterval * points[0].uncertainty}`

      for (let i = 1; i < points.length; i++) {
        const y = points[i].y + confidenceInterval * points[i].uncertainty
        path += ` L ${points[i].x},${y}`
      }

      return path
    }

    // Convert points to SVG path for mean curve
    let path = `M ${points[0].x},${points[0].y}`

    for (let i = 1; i < points.length; i++) {
      path += ` L ${points[i].x},${points[i].y}`
    }

    return path
  }

  // Generate uncertainty region path (area between upper and lower confidence bounds)
  const generateUncertaintyRegionPath = () => {
    if (dimensions.width === 0) return ""

    const height = dimensions.height / 2
    const width = dimensions.width

    // Number of points to use for the curve
    const numPoints = 150
    const upperPoints: { x: number; y: number }[] = []
    const lowerPoints: { x: number; y: number }[] = []

    // Generate points for both upper and lower bounds
    for (let i = 0; i < numPoints; i++) {
      const x = (i / (numPoints - 1)) * width

      // Start with a flat line at the center
      let y = height
      let uncertainty = 60 // Default uncertainty when no points

      if (samplingPoints.length > 0) {
        // Calculate GP mean and variance at this point
        let meanSum = 0
        let weightSum = 0
        let varianceSum = 0

        // For each observed point, calculate its influence using RBF kernel
        samplingPoints.forEach(point => {
          // Calculate distance from this curve point to the sampling point
          const dx = x - point.x

          // Use a Gaussian/RBF kernel to determine influence
          const lengthScale = width * 0.1 // Controls how far the influence reaches
          const kernelValue = Math.exp(
            -(dx * dx) / (2 * lengthScale * lengthScale)
          )

          // Add weighted contribution to mean
          meanSum += kernelValue * point.value
          weightSum += kernelValue

          // Reduce uncertainty near observed points
          varianceSum += kernelValue
        })

        // Calculate weighted mean
        if (weightSum > 0) {
          y = height - (meanSum / weightSum) * 40 // Scale for visualization
        }

        // Calculate uncertainty (higher near edges, lower near observations)
        const distanceUncertainty = Math.min(
          Math.abs(x) / (width * 0.2),
          Math.abs(x - width) / (width * 0.2)
        )

        // Normalize variance between 0 and 1, with 1 being far from observations
        const normalizedVariance = Math.max(0, 1 - varianceSum * 0.8)

        // Scale uncertainty based on distance from observations and edges
        uncertainty = 20 + normalizedVariance * 60

        // Reduce uncertainty more near the best observed points
        const bestPoints = [...samplingPoints]
          .sort((a, b) => b.value - a.value)
          .slice(0, 3)
        bestPoints.forEach(point => {
          const dx = x - point.x
          const closenessFactor = Math.exp(
            -(dx * dx) / (2 * (width * 0.05) * (width * 0.05))
          )
          uncertainty *= 1 - closenessFactor * 0.5
        })
      }

      // Add points for upper and lower bounds
      upperPoints.push({ x, y: y - uncertainty })
      lowerPoints.push({ x, y: y + uncertainty })
    }

    // Create path for upper bound
    let path = `M ${upperPoints[0].x},${upperPoints[0].y}`
    for (let i = 1; i < upperPoints.length; i++) {
      path += ` L ${upperPoints[i].x},${upperPoints[i].y}`
    }

    // Add lower bound in reverse order to create a closed shape
    for (let i = lowerPoints.length - 1; i >= 0; i--) {
      path += ` L ${lowerPoints[i].x},${lowerPoints[i].y}`
    }

    // Close the path
    path += " Z"

    return path
  }

  return (
    <div
      ref={containerRef}
      className="bg-background absolute inset-0 -z-10 size-full overflow-hidden"
      onClick={handleClick}
    >
      {/* Floating marketing keywords */}
      <AnimatePresence>
        {floatingTexts.map(text => (
          <motion.div
            key={text.id}
            className="text-primary/20 pointer-events-none absolute font-light"
            style={{
              x: text.x,
              y: text.y,
              fontSize: `${Math.max(16, Math.min(28, dimensions.width / 40))}px`
            }}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{
              opacity: text.opacity,
              scale: text.scale,
              x: [text.x - 20, text.x + 20, text.x - 10],
              y: [text.y + 10, text.y - 15, text.y + 5]
            }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{
              duration: 20,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
          >
            {text.text}
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Enhanced animated dots with trails */}
      {dots.map(dot => (
        <motion.div
          key={dot.id}
          className="absolute rounded-full"
          style={{
            x: dot.x,
            y: dot.y,
            backgroundColor: dot.color,
            width: dot.size,
            height: dot.size,
            boxShadow: `0 0 ${dot.size * 2}px ${dot.color}`
          }}
          initial={{ opacity: 0 }}
          animate={{
            opacity: [0.2, 0.8, 0.2],
            scale: [1, 1.2, 1],
            x: [
              dot.x,
              dot.x + Math.sin(dot.id) * 60 * dot.speed,
              dot.x + Math.cos(dot.id) * 40 * dot.speed,
              dot.x
            ],
            y: [
              dot.y,
              dot.y + Math.cos(dot.id) * 40 * dot.speed,
              dot.y + Math.sin(dot.id) * 60 * dot.speed,
              dot.y
            ]
          }}
          transition={{
            duration: 10 + dot.speed * 5,
            delay: dot.delay,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
        />
      ))}

      {/* Bayesian optimization visualization */}
      <svg
        width={dimensions.width}
        height={dimensions.height}
        className="absolute left-0 top-0"
      >
        <defs>
          {/* Gradient for GP confidence interval - more subtle */}
          <linearGradient id="gpGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="rgba(59, 130, 246, 0.08)" />
            <stop offset="100%" stopColor="rgba(59, 130, 246, 0.02)" />
          </linearGradient>

          {/* Gradient for true function - more subtle */}
          <linearGradient
            id="trueFunctionGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="0%"
          >
            <stop offset="0%" stopColor="rgba(239, 68, 68, 0.3)" /> {/* Red */}
            <stop offset="50%" stopColor="rgba(236, 72, 153, 0.3)" />{" "}
            {/* Pink */}
            <stop offset="100%" stopColor="rgba(239, 68, 68, 0.3)" />{" "}
            {/* Red */}
          </linearGradient>

          {/* Gradient for GP mean - more subtle */}
          <linearGradient id="gpMeanGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(59, 130, 246, 0.3)" />{" "}
            {/* Blue */}
            <stop offset="50%" stopColor="rgba(79, 70, 229, 0.3)" />{" "}
            {/* Indigo */}
            <stop offset="100%" stopColor="rgba(59, 130, 246, 0.3)" />{" "}
            {/* Blue */}
          </linearGradient>

          {/* Radial gradients for sampling points */}
          {samplingPoints.map((point, i) => (
            <radialGradient
              key={`gradient-${i}`}
              id={`point-gradient-${i}`}
              cx="50%"
              cy="50%"
              r="50%"
              fx="50%"
              fy="50%"
            >
              <stop offset="0%" stopColor="rgba(59, 130, 246, 0.4)" />
              <stop offset="100%" stopColor="rgba(59, 130, 246, 0)" />
            </radialGradient>
          ))}

          {/* Clip path for confidence interval */}
          <clipPath id="confidenceClip">
            <rect
              x="0"
              y="0"
              width={dimensions.width}
              height={dimensions.height}
            />
          </clipPath>
        </defs>

        {/* GP confidence interval (uncertainty region) - using the same calculation as the dotted lines */}
        {(() => {
          // Get the same points used to generate the confidence bounds
          const height = dimensions.height / 2
          const width = dimensions.width
          const numPoints = 150
          const upperPoints: { x: number; y: number }[] = []
          const lowerPoints: { x: number; y: number }[] = []

          // Generate points for both upper and lower bounds - identical to the dotted lines
          for (let i = 0; i < numPoints; i++) {
            const x = (i / (numPoints - 1)) * width

            let y = height
            let uncertainty = 60

            if (samplingPoints.length > 0) {
              let meanSum = 0
              let weightSum = 0
              let varianceSum = 0

              samplingPoints.forEach(point => {
                const dx = x - point.x
                const lengthScale = width * 0.1
                const kernelValue = Math.exp(
                  -(dx * dx) / (2 * lengthScale * lengthScale)
                )
                meanSum += kernelValue * point.value
                weightSum += kernelValue
                varianceSum += kernelValue
              })

              if (weightSum > 0) {
                y = height - (meanSum / weightSum) * 40
              }

              const normalizedVariance = Math.max(0, 1 - varianceSum * 0.8)
              uncertainty = 20 + normalizedVariance * 60

              const bestPoints = [...samplingPoints]
                .sort((a, b) => b.value - a.value)
                .slice(0, 3)
              bestPoints.forEach(point => {
                const dx = x - point.x
                const closenessFactor = Math.exp(
                  -(dx * dx) / (2 * (width * 0.05) * (width * 0.05))
                )
                uncertainty *= 1 - closenessFactor * 0.5
              })
            }

            upperPoints.push({ x, y: y - uncertainty })
            lowerPoints.push({ x, y: y + uncertainty })
          }

          // Create path for the shaded region
          let path = `M ${upperPoints[0].x},${upperPoints[0].y}`

          // Add upper bound
          for (let i = 1; i < upperPoints.length; i++) {
            path += ` L ${upperPoints[i].x},${upperPoints[i].y}`
          }

          // Add lower bound in reverse
          for (let i = lowerPoints.length - 1; i >= 0; i--) {
            path += ` L ${lowerPoints[i].x},${lowerPoints[i].y}`
          }

          // Close the path
          path += " Z"

          return (
            <motion.path
              d={path}
              fill="url(#gpGradient)"
              clipPath="url(#confidenceClip)"
              animate={{
                opacity: [0.3, 0.5, 0.3]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
          )
        })()}

        {/* True underlying function (red) - normally unknown in real BO */}
        <motion.path
          d={generateTrueFunctionPath()}
          fill="none"
          stroke="url(#trueFunctionGradient)"
          strokeWidth={2}
          strokeLinecap="round"
          animate={{
            opacity: 0.5,
            strokeWidth: [1, 1.5, 1]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />

        {/* GP mean function (blue) - this is what BO uses to make decisions */}
        <motion.path
          d={generateGaussianCurvePath()}
          fill="none"
          stroke="url(#gpMeanGradient)"
          strokeWidth={2}
          strokeLinecap="round"
          animate={{
            opacity: 0.6,
            strokeWidth: [1.5, 2, 1.5]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />

        {/* Generate the exact same confidence bounds that match the shaded region */}
        {(() => {
          // Get the same points used to generate the uncertainty region
          const height = dimensions.height / 2
          const width = dimensions.width
          const numPoints = 150
          const upperPoints: { x: number; y: number }[] = []
          const lowerPoints: { x: number; y: number }[] = []

          // Generate points for both upper and lower bounds - identical to generateUncertaintyRegionPath
          for (let i = 0; i < numPoints; i++) {
            const x = (i / (numPoints - 1)) * width

            let y = height
            let uncertainty = 60

            if (samplingPoints.length > 0) {
              let meanSum = 0
              let weightSum = 0
              let varianceSum = 0

              samplingPoints.forEach(point => {
                const dx = x - point.x
                const lengthScale = width * 0.1
                const kernelValue = Math.exp(
                  -(dx * dx) / (2 * lengthScale * lengthScale)
                )
                meanSum += kernelValue * point.value
                weightSum += kernelValue
                varianceSum += kernelValue
              })

              if (weightSum > 0) {
                y = height - (meanSum / weightSum) * 40
              }

              const normalizedVariance = Math.max(0, 1 - varianceSum * 0.8)
              uncertainty = 20 + normalizedVariance * 60

              const bestPoints = [...samplingPoints]
                .sort((a, b) => b.value - a.value)
                .slice(0, 3)
              bestPoints.forEach(point => {
                const dx = x - point.x
                const closenessFactor = Math.exp(
                  -(dx * dx) / (2 * (width * 0.05) * (width * 0.05))
                )
                uncertainty *= 1 - closenessFactor * 0.5
              })
            }

            upperPoints.push({ x, y: y - uncertainty })
            lowerPoints.push({ x, y: y + uncertainty })
          }

          // Create upper bound path
          let upperPath = `M ${upperPoints[0].x},${upperPoints[0].y}`
          for (let i = 1; i < upperPoints.length; i++) {
            upperPath += ` L ${upperPoints[i].x},${upperPoints[i].y}`
          }

          // Create lower bound path
          let lowerPath = `M ${lowerPoints[0].x},${lowerPoints[0].y}`
          for (let i = 1; i < lowerPoints.length; i++) {
            lowerPath += ` L ${lowerPoints[i].x},${lowerPoints[i].y}`
          }

          return (
            <>
              {/* Upper confidence bound */}
              <motion.path
                d={upperPath}
                fill="none"
                stroke="rgba(59, 130, 246, 0.25)"
                strokeWidth={1}
                strokeDasharray="2,3"
                animate={{ opacity: [0.2, 0.4, 0.2] }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />

              {/* Lower confidence bound */}
              <motion.path
                d={lowerPath}
                fill="none"
                stroke="rgba(59, 130, 246, 0.25)"
                strokeWidth={1}
                strokeDasharray="2,3"
                animate={{ opacity: [0.2, 0.4, 0.2] }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  repeatType: "reverse",
                  delay: 1
                }}
              />
            </>
          )
        })()}

        {/* Influence areas for each sampling point */}
        <g>
          {samplingPoints.map((point, i) => (
            <motion.circle
              key={`influence-${i}`}
              cx={point.x}
              cy={point.y}
              r={dimensions.width * 0.08}
              fill={`url(#point-gradient-${i})`}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 0.15, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ duration: 0.8 }}
            />
          ))}
        </g>

        {/* Highlight points along the GP mean */}
        {[0.15, 0.35, 0.55, 0.75, 0.9].map((pos, i) => {
          // Calculate position on the GP mean curve
          const x = dimensions.width * pos

          // Calculate GP mean at this point
          let y = dimensions.height / 2
          let meanSum = 0
          let weightSum = 0

          samplingPoints.forEach(point => {
            const dx = x - point.x
            const lengthScale = dimensions.width * 0.1
            const kernelValue = Math.exp(
              -(dx * dx) / (2 * lengthScale * lengthScale)
            )
            meanSum += kernelValue * point.value
            weightSum += kernelValue
          })

          if (weightSum > 0) {
            y = dimensions.height / 2 - (meanSum / weightSum) * 40
          }

          return (
            <motion.circle
              key={i}
              cx={x}
              cy={y}
              r={3}
              fill="url(#gpMeanGradient)"
              initial={{ opacity: 0 }}
              animate={{
                opacity: [0.2, 0.5, 0.2],
                r: [1.5, 3, 1.5],
                boxShadow: [
                  "0 0 0px rgba(59, 130, 246, 0.4)",
                  "0 0 5px rgba(59, 130, 246, 0.3)",
                  "0 0 0px rgba(59, 130, 246, 0.4)"
                ]
              }}
              transition={{
                duration: 4,
                delay: i * 0.3,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
          )
        })}

        {/* Acquisition function visualization (simplified) */}
        {samplingPoints.length > 3 && (
          <motion.g
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 2 }}
          >
            {/* Show where the next point might be sampled */}
            {[...Array(5)].map((_, i) => {
              const x = dimensions.width * (0.2 + i * 0.15)

              // Calculate expected improvement (simplified)
              let meanSum = 0
              let weightSum = 0
              let uncertainty = 0

              samplingPoints.forEach(point => {
                const dx = x - point.x
                const lengthScale = dimensions.width * 0.1
                const kernelValue = Math.exp(
                  -(dx * dx) / (2 * lengthScale * lengthScale)
                )
                meanSum += kernelValue * point.value
                weightSum += kernelValue
                uncertainty += (1 - kernelValue) * 0.2
              })

              const mean = weightSum > 0 ? meanSum / weightSum : 0
              const bestValue = Math.max(...samplingPoints.map(p => p.value))
              const improvement = Math.max(0, mean - bestValue)
              const ei = improvement * uncertainty

              // Only show points with high acquisition value
              if (ei > 0.1) {
                return (
                  <motion.circle
                    key={i}
                    cx={x}
                    cy={dimensions.height / 2 - mean * 40}
                    r={3}
                    fill="rgba(16, 185, 129, 0.8)" // Green for acquisition function
                    animate={{
                      opacity: [0.4, 0.8, 0.4],
                      r: [2, 4, 2]
                    }}
                    transition={{
                      duration: 2,
                      delay: i * 0.2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  />
                )
              }
              return null
            })}
          </motion.g>
        )}
      </svg>

      {/* Sampling points visualization for Bayesian optimization */}
      <AnimatePresence>
        {samplingPoints.map(point => {
          // Get the best observed value so far
          const bestValue = Math.max(...samplingPoints.map(p => p.value))

          // Determine if this is the current best point
          const isBestPoint = point.value === bestValue

          // Determine color based on value relative to best observed
          // Blue for observed points, red for true function values
          const normalizedValue = (point.value + 3) / 6 // Adjust based on function range
          const clampedValue = Math.max(0, Math.min(1, normalizedValue))

          // Size based on how recent the point is (newer points are larger)
          const age = Date.now() - point.createdAt
          const ageRatio = Math.max(0, Math.min(1, age / 10000))
          const dotSize = 6 - ageRatio * 2

          return (
            <motion.div
              key={point.id}
              className="pointer-events-none absolute"
              style={{
                x: point.x - 60, // Center the ripple
                y: point.y - 60, // Center the ripple
                width: 120,
                height: 120,
                zIndex: isBestPoint ? 10 : 5
              }}
              initial={{ opacity: 1 }}
              animate={{ opacity: 0.9 - ageRatio * 0.5 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              {/* Inner dot - blue for observed points */}
              <motion.div
                className="absolute rounded-full"
                style={{
                  x: 58,
                  y: 58,
                  width: dotSize + (isBestPoint ? 2 : 0),
                  height: dotSize + (isBestPoint ? 2 : 0),
                  backgroundColor: isBestPoint
                    ? "rgba(16, 185, 129, 0.9)" // Green for best point
                    : "rgba(59, 130, 246, 0.9)", // Blue for other points
                  boxShadow: isBestPoint
                    ? "0 0 10px 3px rgba(16, 185, 129, 0.6)"
                    : "0 0 8px 2px rgba(59, 130, 246, 0.6)"
                }}
                initial={{ opacity: 0.9, scale: 0 }}
                animate={{
                  opacity: [0.9, 0.7, 0.9],
                  scale: [0, 1, 1],
                  boxShadow: isBestPoint
                    ? [
                        "0 0 0px rgba(16, 185, 129, 0.6)",
                        "0 0 15px rgba(16, 185, 129, 0.8)",
                        "0 0 10px rgba(16, 185, 129, 0.6)"
                      ]
                    : [
                        "0 0 0px rgba(59, 130, 246, 0.6)",
                        "0 0 12px rgba(59, 130, 246, 0.7)",
                        "0 0 8px rgba(59, 130, 246, 0.6)"
                      ]
                }}
                transition={{
                  duration: 2,
                  repeat: isBestPoint ? Infinity : 4,
                  repeatType: "reverse"
                }}
              />

              {/* Vertical line connecting to true function */}
              <motion.div
                className="absolute bg-red-500/30"
                style={{
                  x: 60,
                  y: 0,
                  width: 1,
                  height: 0,
                  translateX: "-50%"
                }}
                initial={{ height: 0, opacity: 0 }}
                animate={{
                  height: 120,
                  opacity: 0.5 - ageRatio * 0.3
                }}
                transition={{ duration: 0.8 }}
              />

              {/* True function value indicator (red dot) */}
              <motion.div
                className="absolute rounded-full bg-red-500"
                style={{
                  x: 60,
                  y:
                    dimensions.height / 2 -
                    getTrueFunction(point.x) * 40 -
                    point.y +
                    60,
                  width: 4,
                  height: 4,
                  translateX: "-50%",
                  translateY: "-50%"
                }}
                initial={{ opacity: 0, scale: 0 }}
                animate={{
                  opacity: 0.8 - ageRatio * 0.5,
                  scale: 1
                }}
                transition={{ duration: 0.5, delay: 0.3 }}
              />

              {/* Ripple effects - different for best point */}
              {[0, 1].map(i => (
                <motion.div
                  key={i}
                  className="absolute rounded-full"
                  style={{
                    x: 60,
                    y: 60,
                    width: 0,
                    height: 0,
                    translateX: "-50%",
                    translateY: "-50%",
                    border: isBestPoint
                      ? "1.5px solid rgba(16, 185, 129, 0.6)"
                      : "1.5px solid rgba(59, 130, 246, 0.5)"
                  }}
                  initial={{
                    width: 0,
                    height: 0,
                    opacity: 0.7
                  }}
                  animate={{
                    width: isBestPoint ? 120 : 80 + clampedValue * 40,
                    height: isBestPoint ? 120 : 80 + clampedValue * 40,
                    opacity: 0
                  }}
                  transition={{
                    duration: isBestPoint ? 4 : 3 + clampedValue,
                    delay: i * 1.2,
                    ease: "easeOut",
                    repeat: isBestPoint ? Infinity : 0,
                    repeatDelay: isBestPoint ? 1 : 0
                  }}
                />
              ))}

              {/* Value indicator */}
              <motion.div
                className="absolute font-mono text-xs"
                style={{
                  x: 70,
                  y: 45,
                  color: isBestPoint
                    ? "rgba(16, 185, 129, 0.9)"
                    : "rgba(59, 130, 246, 0.9)"
                }}
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 0.9 - ageRatio * 0.7, y: 0 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                {point.value.toFixed(2)}
              </motion.div>

              {/* Status label */}
              {(isBestPoint || point.createdAt > Date.now() - 3000) && (
                <motion.div
                  className="absolute font-mono text-[10px]"
                  style={{
                    x: 70,
                    y: 60,
                    color: isBestPoint
                      ? "rgba(16, 185, 129, 0.9)"
                      : "rgba(59, 130, 246, 0.7)"
                  }}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 0.9 - ageRatio * 0.7 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  {isBestPoint ? "Best Point" : "Sampled"}
                </motion.div>
              )}
            </motion.div>
          )
        })}
      </AnimatePresence>

      {/* Gradient overlay at the bottom for better text contrast */}
      <div className="from-background/80 absolute bottom-0 left-0 h-1/3 w-full bg-gradient-to-t to-transparent" />
    </div>
  )
}

export default EnhancedBayesianBackground
