"use client"

import { useState } from "react"
import { X, Plus, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MultiSelect } from "@/components/ui/multi-select"
import {
  DiscreteExcludeConstraint,
  ConstraintCondition
} from "@/lib/constraints"

interface ExcludeConstraintEditorProps {
  constraint: DiscreteExcludeConstraint
  availableParameters: string[]
  onChange: (updates: Partial<DiscreteExcludeConstraint>) => void
}

export function ExcludeConstraintEditor({
  constraint,
  availableParameters,
  onChange
}: ExcludeConstraintEditorProps) {
  const handleParametersChange = (parameters: string[]) => {
    // Adjust conditions array to match parameters length
    const newConditions = parameters.map(
      (_, index) =>
        constraint.conditions?.[index] || {
          type: "subselection" as const,
          selection: []
        }
    )

    onChange({
      parameters,
      conditions: newConditions
    })
  }

  const handleCombinerChange = (combiner: string) => {
    onChange({ combiner: combiner as "AND" | "OR" })
  }

  const handleConditionChange = (
    index: number,
    condition: ConstraintCondition
  ) => {
    const newConditions = [...(constraint.conditions || [])]
    newConditions[index] = condition
    onChange({ conditions: newConditions })
  }

  const addCondition = () => {
    const newCondition: ConstraintCondition = {
      type: "subselection",
      selection: []
    }
    onChange({
      conditions: [...(constraint.conditions || []), newCondition]
    })
  }

  const removeCondition = (index: number) => {
    const newConditions =
      constraint.conditions?.filter((_, i) => i !== index) || []
    onChange({ conditions: newConditions })
  }

  const getConstraintPreview = () => {
    if (!constraint.parameters.length || !constraint.conditions?.length) {
      return "No exclusion rules defined"
    }

    const combiner = constraint.combiner || "AND"
    const rules = constraint.parameters.map((param, index) => {
      const condition = constraint.conditions?.[index]
      if (!condition) return `${param}: any`

      if (condition.type === "threshold") {
        return `${param} ${condition.operator || ">="} ${condition.threshold}`
      } else if (condition.type === "subselection") {
        const selection = condition.selection || []
        return `${param} ∈ {${selection.join(", ")}}`
      }
      return `${param}: undefined`
    })

    return `Exclude when: ${rules.join(` ${combiner} `)}`
  }

  const parameterOptions = availableParameters.map(param => ({
    value: param,
    label: param
  }))

  return (
    <div className="space-y-6">
      {/* Constraint Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <X className="size-4" />
            Exclusion Rule Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted rounded-md p-3 font-mono text-sm">
            {getConstraintPreview()}
          </div>
          <p className="text-muted-foreground mt-2 text-sm">
            Exclude parameter combinations that match the specified conditions
          </p>
        </CardContent>
      </Card>

      {/* Parameter Selection */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-base font-medium">Parameters</Label>
          <Badge variant="outline">
            {constraint.parameters.length} selected
          </Badge>
        </div>

        <MultiSelect
          options={parameterOptions}
          value={constraint.parameters}
          onValueChange={handleParametersChange}
          placeholder="Select parameters to constrain..."
          maxCount={10}
        />
      </div>

      {/* Combiner */}
      <div className="space-y-2">
        <Label htmlFor="combiner">Condition Combiner</Label>
        <Select
          value={constraint.combiner || "AND"}
          onValueChange={handleCombinerChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select combiner" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="AND">
              AND (all conditions must be true)
            </SelectItem>
            <SelectItem value="OR">OR (any condition can be true)</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-muted-foreground text-xs">
          How to combine multiple conditions for exclusion
        </p>
      </div>

      {/* Conditions */}
      {constraint.parameters.length > 0 && (
        <div className="space-y-4">
          <Label className="text-base font-medium">Exclusion Conditions</Label>

          <div className="space-y-3">
            {constraint.parameters.map((param, index) => {
              const condition = constraint.conditions?.[index] || {
                type: "subselection" as const,
                selection: []
              }

              return (
                <Card key={`${param}-${index}`} className="border-dashed">
                  <CardContent className="pt-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary" className="font-mono">
                          {param}
                        </Badge>
                        <Select
                          value={condition.type}
                          onValueChange={type =>
                            handleConditionChange(index, {
                              ...condition,
                              type: type as "threshold" | "subselection"
                            })
                          }
                        >
                          <SelectTrigger className="w-40">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="subselection">
                              Value Selection
                            </SelectItem>
                            <SelectItem value="threshold">Threshold</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {condition.type === "threshold" ? (
                        <div className="grid grid-cols-2 gap-2">
                          <Select
                            value={condition.operator || ">="}
                            onValueChange={operator =>
                              handleConditionChange(index, {
                                ...condition,
                                operator: operator as any
                              })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value=">=">
                                &gt;= (greater or equal)
                              </SelectItem>
                              <SelectItem value="<=">
                                &lt;= (less or equal)
                              </SelectItem>
                              <SelectItem value=">">
                                &gt; (greater than)
                              </SelectItem>
                              <SelectItem value="<">
                                &lt; (less than)
                              </SelectItem>
                              <SelectItem value="=">=== (equal to)</SelectItem>
                            </SelectContent>
                          </Select>
                          <Input
                            type="number"
                            step="0.1"
                            value={condition.threshold ?? ""}
                            onChange={e =>
                              handleConditionChange(index, {
                                ...condition,
                                threshold: parseFloat(e.target.value) || 0
                              })
                            }
                            placeholder="Threshold value"
                          />
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Label className="text-sm">Excluded Values</Label>
                          <Input
                            value={(condition.selection || []).join(", ")}
                            onChange={e => {
                              const values = e.target.value
                                .split(",")
                                .map(v => v.trim())
                                .filter(Boolean)
                              handleConditionChange(index, {
                                ...condition,
                                selection: values
                              })
                            }}
                            placeholder="Enter values separated by commas (e.g., ethanol, methanol, dmf)"
                          />
                          <p className="text-muted-foreground text-xs">
                            Exclude when {param} has any of these values
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}

      {/* Warning */}
      {constraint.parameters.length > 0 &&
        constraint.conditions?.length !== constraint.parameters.length && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertTriangle className="size-4" />
                <span className="text-sm">
                  Number of conditions ({constraint.conditions?.length || 0})
                  doesn't match number of parameters (
                  {constraint.parameters.length})
                </span>
              </div>
            </CardContent>
          </Card>
        )}

      {/* Common Examples */}
      <Card className="bg-muted/30">
        <CardHeader>
          <CardTitle className="text-sm">Common Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div>
            <strong>Safety rule:</strong> Exclude high temperature AND flammable
            solvent
          </div>
          <div>
            <strong>Incompatibility:</strong> Exclude ethanol OR methanol with
            DMF
          </div>
          <div>
            <strong>Process limit:</strong> Exclude pressure &gt; 10 bar AND
            temperature &gt; 200°C
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
