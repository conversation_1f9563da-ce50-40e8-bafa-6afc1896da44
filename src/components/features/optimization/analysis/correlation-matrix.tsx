"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Grid3X3, RefreshCw, AlertCircle, X, Maximize2 } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import dynamic from "next/dynamic"

// Import Plotly dynamically to avoid SSR issues
const Plot = dynamic(() => import("react-plotly.js").then(mod => mod.default), {
  ssr: false
}) as any

interface CorrelationMatrixProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  parameterNames: string[]
  onParameterSelect?: (parameter: string) => void
  selectedParameter?: string
}

interface CorrelationData {
  parameters: string[]
  targets: string[]
  correlationMatrix: number[][]
  parameterRanges: Record<string, { min: number; max: number }>
}

export function CorrelationMatrix({
  optimization,
  measurements,
  parameterNames,
  onParameterSelect,
  selectedParameter
}: CorrelationMatrixProps) {
  const [correlationData, setCorrelationData] =
    useState<CorrelationData | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedCell, setSelectedCell] = useState<{
    row: number
    col: number
  } | null>(null)
  const [activeTab, setActiveTab] = useState<string>("pearson")

  // Calculate correlation data
  const calculateCorrelationData = () => {
    setIsLoading(true)
    setError(null)

    try {
      // Extract parameter and target values
      const paramValues: Record<string, number[]> = {}
      const targetValues: Record<string, number[]> = {}
      const paramRanges: Record<string, { min: number; max: number }> = {}

      // Initialize parameter arrays
      parameterNames.forEach(param => {
        paramValues[param] = []
        paramRanges[param] = { min: Infinity, max: -Infinity }
      })

      // Get target names
      const targetNames: string[] = []
      if (
        optimization.targetMode === "MULTI" &&
        measurements.length > 0 &&
        measurements[0].targetValues
      ) {
        const firstMeasurement = measurements[0]
        const targetValuesObj = firstMeasurement.targetValues as Record<
          string,
          number
        >
        targetNames.push(...Object.keys(targetValuesObj))
      } else {
        targetNames.push(optimization.targetName)
      }

      // Initialize target arrays
      targetNames.forEach(target => {
        targetValues[target] = []
      })

      // Collect values
      measurements.forEach(measurement => {
        // Extract parameter values
        parameterNames.forEach(param => {
          const params = measurement.parameters as Record<string, any>
          const value = parseFloat(params[param])
          if (!isNaN(value)) {
            paramValues[param].push(value)

            // Update ranges
            paramRanges[param].min = Math.min(paramRanges[param].min, value)
            paramRanges[param].max = Math.max(paramRanges[param].max, value)
          }
        })

        // Extract target values
        if (optimization.targetMode === "MULTI" && measurement.targetValues) {
          const targetValuesObj = measurement.targetValues as Record<
            string,
            string | number
          >
          targetNames.forEach(target => {
            const value =
              typeof targetValuesObj[target] === "string"
                ? parseFloat(targetValuesObj[target] as string)
                : (targetValuesObj[target] as number)
            if (!isNaN(value)) {
              targetValues[target].push(value)
            }
          })
        } else {
          const value = parseFloat(measurement.targetValue)
          if (!isNaN(value)) {
            targetValues[optimization.targetName].push(value)
          }
        }
      })

      // Calculate correlation matrix
      const allNames = [...parameterNames, ...targetNames]
      const matrix: number[][] = []

      for (let i = 0; i < allNames.length; i++) {
        const row: number[] = []
        const name1 = allNames[i]
        const values1 =
          i < parameterNames.length ? paramValues[name1] : targetValues[name1]

        for (let j = 0; j < allNames.length; j++) {
          const name2 = allNames[j]
          const values2 =
            j < parameterNames.length ? paramValues[name2] : targetValues[name2]

          // Calculate correlation
          const correlation = calculateCorrelation(values1, values2, activeTab)
          row.push(correlation)
        }

        matrix.push(row)
      }

      setCorrelationData({
        parameters: parameterNames,
        targets: targetNames,
        correlationMatrix: matrix,
        parameterRanges: paramRanges
      })

      console.log("Correlation data calculated:", {
        parameters: parameterNames,
        targets: targetNames,
        matrixSize: matrix.length,
        parameterRanges: paramRanges
      })
    } catch (err) {
      console.error("Error calculating correlation data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // Calculate correlation coefficient
  const calculateCorrelation = (
    values1: number[],
    values2: number[],
    method: string
  ): number => {
    if (values1.length !== values2.length || values1.length === 0) {
      return 0
    }

    if (method === "pearson") {
      return calculatePearsonCorrelation(values1, values2)
    } else if (method === "spearman") {
      return calculateSpearmanCorrelation(values1, values2)
    }

    return 0
  }

  // Calculate Pearson correlation
  const calculatePearsonCorrelation = (
    values1: number[],
    values2: number[]
  ): number => {
    const n = values1.length

    // Calculate means
    const mean1 = values1.reduce((sum, val) => sum + val, 0) / n
    const mean2 = values2.reduce((sum, val) => sum + val, 0) / n

    // Calculate covariance and variances
    let covariance = 0
    let variance1 = 0
    let variance2 = 0

    for (let i = 0; i < n; i++) {
      const diff1 = values1[i] - mean1
      const diff2 = values2[i] - mean2

      covariance += diff1 * diff2
      variance1 += diff1 * diff1
      variance2 += diff2 * diff2
    }

    // Avoid division by zero
    if (variance1 === 0 || variance2 === 0) {
      return 0
    }

    return covariance / Math.sqrt(variance1 * variance2)
  }

  // Calculate Spearman correlation
  const calculateSpearmanCorrelation = (
    values1: number[],
    values2: number[]
  ): number => {
    const n = values1.length

    // Create arrays of [value, index] pairs
    const pairs1 = values1.map((value, index) => ({ value, index }))
    const pairs2 = values2.map((value, index) => ({ value, index }))

    // Sort by value
    pairs1.sort((a, b) => a.value - b.value)
    pairs2.sort((a, b) => a.value - b.value)

    // Assign ranks
    const ranks1: number[] = new Array(n).fill(0)
    const ranks2: number[] = new Array(n).fill(0)

    for (let i = 0; i < n; i++) {
      ranks1[pairs1[i].index] = i + 1
      ranks2[pairs2[i].index] = i + 1
    }

    // Calculate Pearson correlation of ranks
    return calculatePearsonCorrelation(ranks1, ranks2)
  }

  // Generate heatmap data
  const heatmapData = useMemo(() => {
    if (!correlationData) return null

    const { parameters, targets, correlationMatrix } = correlationData
    const allNames = [...parameters, ...targets]

    return {
      z: correlationMatrix,
      x: allNames,
      y: allNames,
      type: "heatmap" as const,
      colorscale: "RdBu" as const,
      zmin: -1,
      zmax: 1,
      hoverongaps: false,
      hoverinfo: "text" as const,
      text: correlationMatrix.map((row, i) =>
        row.map(
          (value, j) =>
            `${allNames[i]} vs ${allNames[j]}<br>Correlation: ${value.toFixed(3)}`
        )
      )
    }
  }, [correlationData])

  // Generate scatterplot data for selected cell
  const scatterplotData = useMemo(() => {
    if (!correlationData || !selectedCell) return null

    const { parameters, targets, correlationMatrix } = correlationData
    const allNames = [...parameters, ...targets]

    const rowName = allNames[selectedCell.row]
    const colName = allNames[selectedCell.col]

    // Skip if same parameter
    if (rowName === colName) return null

    // Get values for the selected parameters/targets
    const xValues: number[] = []
    const yValues: number[] = []

    measurements.forEach(measurement => {
      let xValue: number | undefined
      let yValue: number | undefined

      // Get x value
      if (parameters.includes(colName)) {
        const params = measurement.parameters as Record<string, any>
        xValue = parseFloat(params[colName])
      } else if (
        optimization.targetMode === "MULTI" &&
        measurement.targetValues
      ) {
        const targetValuesObj = measurement.targetValues as Record<
          string,
          string | number
        >
        xValue =
          typeof targetValuesObj[colName] === "string"
            ? parseFloat(targetValuesObj[colName] as string)
            : (targetValuesObj[colName] as number)
      } else if (colName === optimization.targetName) {
        xValue = parseFloat(measurement.targetValue)
      }

      // Get y value
      if (parameters.includes(rowName)) {
        const params = measurement.parameters as Record<string, any>
        yValue = parseFloat(params[rowName])
      } else if (
        optimization.targetMode === "MULTI" &&
        measurement.targetValues
      ) {
        const targetValuesObj = measurement.targetValues as Record<
          string,
          string | number
        >
        yValue =
          typeof targetValuesObj[rowName] === "string"
            ? parseFloat(targetValuesObj[rowName] as string)
            : (targetValuesObj[rowName] as number)
      } else if (rowName === optimization.targetName) {
        yValue = parseFloat(measurement.targetValue)
      }

      // Add values if both are valid
      if (
        xValue !== undefined &&
        yValue !== undefined &&
        !isNaN(xValue) &&
        !isNaN(yValue)
      ) {
        xValues.push(xValue)
        yValues.push(yValue)
      }
    })

    const correlation = correlationMatrix[selectedCell.row][selectedCell.col]

    return {
      x: xValues,
      y: yValues,
      mode: "markers" as const,
      type: "scatter" as const,
      marker: {
        color: "rgba(31, 119, 180, 0.7)",
        size: 10
      },
      name: `${rowName} vs ${colName}`,
      hoverinfo: "text" as const,
      text: xValues.map(
        (x, i) => `${colName}: ${x}<br>${rowName}: ${yValues[i]}`
      )
    }
  }, [
    correlationData,
    selectedCell,
    measurements,
    optimization.targetMode,
    optimization.targetName
  ])

  // Handle cell click
  const handleCellClick = (event: any) => {
    if (event && event.points && event.points.length > 0) {
      const point = event.points[0]
      setSelectedCell({ row: point.y, col: point.x })

      // If parameter selection callback is provided and the clicked cell is a parameter
      if (onParameterSelect && correlationData) {
        const { parameters } = correlationData
        const allNames = [...parameters, ...correlationData.targets]
        const paramName = allNames[point.x]

        if (parameters.includes(paramName)) {
          onParameterSelect(paramName)
        }
      }
    }
  }

  // Effect to calculate correlation data when measurements change
  useEffect(() => {
    if (measurements.length > 0 && parameterNames.length > 0) {
      calculateCorrelationData()
    }
  }, [measurements.length, parameterNames.length, activeTab])

  // Effect to highlight selected parameter
  useEffect(() => {
    if (selectedParameter && correlationData) {
      const paramIndex = correlationData.parameters.indexOf(selectedParameter)
      if (paramIndex >= 0) {
        // Highlight the parameter by selecting a cell in its column
        setSelectedCell({ row: 0, col: paramIndex })
      }
    }
  }, [selectedParameter, correlationData])

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="flex items-center text-lg font-medium">
            <Grid3X3 className="text-primary mr-2 size-5" />
            Correlation Matrix
          </h3>
          <div className="flex gap-2">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-auto"
            >
              <TabsList className="grid w-auto grid-cols-2">
                <TabsTrigger value="pearson">Pearson</TabsTrigger>
                <TabsTrigger value="spearman">Spearman</TabsTrigger>
              </TabsList>
            </Tabs>
            <Button
              variant="outline"
              size="sm"
              onClick={calculateCorrelationData}
              disabled={isLoading}
            >
              {isLoading ? (
                <RefreshCw className="size-4 animate-spin" />
              ) : (
                <RefreshCw className="size-4" />
              )}
              <span className="ml-2 hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="size-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex h-[400px] items-center justify-center">
            <Skeleton className="size-full rounded-md" />
          </div>
        ) : correlationData ? (
          <div className="space-y-4">
            <div className="h-[400px] w-full overflow-hidden rounded-md border">
              <Plot
                data={[heatmapData]}
                layout={{
                  title: `${activeTab === "pearson" ? "Pearson" : "Spearman"} Correlation Matrix`,
                  autosize: true,
                  margin: { l: 80, r: 20, t: 50, b: 80 },
                  xaxis: {
                    tickangle: 45,
                    automargin: true
                  },
                  yaxis: {
                    automargin: true
                  },
                  coloraxis: {
                    colorbar: {
                      title: "Correlation",
                      titleside: "right"
                    }
                  }
                }}
                config={{ responsive: true, displayModeBar: false }}
                style={{ width: "100%", height: "100%" }}
                onClick={handleCellClick}
              />
            </div>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full">
                  <Maximize2 className="mr-2 size-4" />
                  Expand View
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>
                    {activeTab === "pearson" ? "Pearson" : "Spearman"}{" "}
                    Correlation Matrix
                  </DialogTitle>
                </DialogHeader>
                <div className="h-[600px] w-full">
                  <Plot
                    data={[heatmapData]}
                    layout={{
                      title: `${activeTab === "pearson" ? "Pearson" : "Spearman"} Correlation Matrix`,
                      autosize: true,
                      margin: { l: 80, r: 20, t: 50, b: 80 },
                      xaxis: {
                        tickangle: 45,
                        automargin: true
                      },
                      yaxis: {
                        automargin: true
                      },
                      coloraxis: {
                        colorbar: {
                          title: "Correlation",
                          titleside: "right"
                        }
                      },
                      height: 600
                    }}
                    config={{ responsive: true }}
                    style={{ width: "100%", height: "100%" }}
                    onClick={handleCellClick}
                  />
                </div>
              </DialogContent>
            </Dialog>

            {selectedCell && scatterplotData && (
              <div className="mt-4">
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="text-md font-medium">
                    Scatterplot:{" "}
                    {
                      correlationData.parameters.concat(
                        correlationData.targets
                      )[selectedCell.row]
                    }{" "}
                    vs{" "}
                    {
                      correlationData.parameters.concat(
                        correlationData.targets
                      )[selectedCell.col]
                    }
                  </h4>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <Maximize2 className="size-4" />
                        <span className="ml-2 hidden sm:inline">Expand</span>
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle>
                          Scatterplot:{" "}
                          {
                            correlationData.parameters.concat(
                              correlationData.targets
                            )[selectedCell.row]
                          }{" "}
                          vs{" "}
                          {
                            correlationData.parameters.concat(
                              correlationData.targets
                            )[selectedCell.col]
                          }
                        </DialogTitle>
                      </DialogHeader>
                      <div className="h-[500px] w-full">
                        <Plot
                          data={[scatterplotData]}
                          layout={{
                            autosize: true,
                            margin: { l: 60, r: 20, t: 30, b: 60 },
                            xaxis: {
                              title: correlationData.parameters.concat(
                                correlationData.targets
                              )[selectedCell.col]
                            },
                            yaxis: {
                              title: correlationData.parameters.concat(
                                correlationData.targets
                              )[selectedCell.row]
                            },
                            showlegend: false
                          }}
                          config={{ responsive: true }}
                          style={{ width: "100%", height: "100%" }}
                        />
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
                <div className="h-[250px] w-full overflow-hidden rounded-md border">
                  <Plot
                    data={[scatterplotData]}
                    layout={{
                      autosize: true,
                      margin: { l: 60, r: 20, t: 30, b: 60 },
                      xaxis: {
                        title: correlationData.parameters.concat(
                          correlationData.targets
                        )[selectedCell.col]
                      },
                      yaxis: {
                        title: correlationData.parameters.concat(
                          correlationData.targets
                        )[selectedCell.row]
                      },
                      showlegend: false
                    }}
                    config={{ responsive: true, displayModeBar: false }}
                    style={{ width: "100%", height: "100%" }}
                  />
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex h-[400px] flex-col items-center justify-center p-4 text-center">
            <Grid3X3 className="text-muted-foreground mb-4 size-12" />
            <h3 className="text-lg font-medium">No correlation data</h3>
            <p className="text-muted-foreground mt-2 max-w-md text-sm">
              There might not be enough measurements to calculate correlations,
              or the parameters might not have numeric values.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
