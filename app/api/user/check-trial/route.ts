import { auth } from "@clerk/nextjs/server"
import { ensureUserTrialAction } from "@/actions/ensure-trial-action"
import { getActualUserId } from "@/lib/auth-utils"
import { NextResponse } from "next/server"

export async function GET() {
  try {
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 CHECK TRIAL API - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return new Response("Unauthorized", { status: 401 })
    }

    // Check and initialize trial if needed
    const result = await ensureUserTrialAction()

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error checking trial:", error)
    return NextResponse.json(
      {
        success: false,
        message: `Error checking trial: ${error instanceof Error ? error.message : "Unknown error"}`
      },
      { status: 500 }
    )
  }
}
