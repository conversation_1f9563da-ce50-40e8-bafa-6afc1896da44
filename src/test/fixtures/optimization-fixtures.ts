// Comprehensive test fixtures for optimization CRUD operations

export const parameterFixtures = {
  numericalContinuous: {
    basic: {
      name: 'temperature',
      type: 'NumericalContinuous',
      bounds: [20, 100]
    },
    withNegativeBounds: {
      name: 'delta_pressure',
      type: 'NumericalContinuous',
      bounds: [-10, 10]
    },
    fractionalBounds: {
      name: 'concentration',
      type: 'NumericalContinuous',
      bounds: [0.001, 0.999]
    }
  },
  
  numericalDiscrete: {
    basic: {
      name: 'rpm',
      type: 'NumericalDiscrete',
      values: [100, 200, 300, 400, 500],
      tolerance: 10
    },
    withFloatValues: {
      name: 'ph_levels',
      type: 'NumericalDiscrete',
      values: [6.5, 7.0, 7.5, 8.0, 8.5],
      tolerance: 0.1
    },
    largeSet: {
      name: 'time_points',
      type: 'NumericalDiscrete',
      values: Array.from({ length: 20 }, (_, i) => (i + 1) * 5),
      tolerance: 2
    }
  },
  
  categorical: {
    basic: {
      name: 'catalyst',
      type: 'CategoricalParameter',
      values: ['Pt', 'Pd', 'Ru', 'Rh'],
      encoding: 'OHE'
    },
    withStringValues: {
      name: 'solvent',
      type: 'CategoricalParameter',
      values: ['water', 'ethanol', 'methanol', 'acetone'],
      encoding: 'OHE'
    },
    binaryChoice: {
      name: 'stirring',
      type: 'CategoricalParameter',
      values: ['on', 'off'],
      encoding: 'OHE'
    }
  }
}

export const targetFixtures = {
  singleTarget: {
    maximize: {
      name: 'yield',
      mode: 'MAX',
      weight: 1.0,
      description: 'Product yield percentage'
    },
    minimize: {
      name: 'cost',
      mode: 'MIN',
      weight: 1.0,
      description: 'Production cost'
    }
  },
  
  multiTarget: [
    {
      name: 'efficiency',
      mode: 'MAX',
      weight: 1.0,
      description: 'Process efficiency'
    },
    {
      name: 'environmental_impact',
      mode: 'MIN',
      weight: 0.8,
      description: 'Environmental impact score'
    },
    {
      name: 'safety_score',
      mode: 'MAX',
      weight: 0.9,
      description: 'Safety rating'
    }
  ]
}

export const optimizationFixtures = {
  singleObjective: {
    optimizer_id: 'single_obj_test',
    parameters: [
      parameterFixtures.numericalContinuous.basic,
      parameterFixtures.numericalDiscrete.basic,
      parameterFixtures.categorical.basic
    ],
    targets: [targetFixtures.singleTarget.maximize],
    measurements: [
      { temperature: 25, rpm: 200, catalyst: 'Pt', yield: 0.75 },
      { temperature: 50, rpm: 300, catalyst: 'Pd', yield: 0.82 },
      { temperature: 75, rpm: 400, catalyst: 'Ru', yield: 0.68 }
    ]
  },
  
  multiObjective: {
    optimizer_id: 'multi_obj_test',
    parameters: [
      parameterFixtures.numericalContinuous.basic,
      parameterFixtures.numericalDiscrete.withFloatValues,
      parameterFixtures.categorical.withStringValues
    ],
    targets: targetFixtures.multiTarget,
    measurements: [
      { temperature: 30, ph_levels: 7.0, solvent: 'water', efficiency: 0.85, environmental_impact: 0.3, safety_score: 0.9 },
      { temperature: 60, ph_levels: 7.5, solvent: 'ethanol', efficiency: 0.78, environmental_impact: 0.4, safety_score: 0.85 }
    ]
  },
  
  complexScenario: {
    optimizer_id: 'complex_test',
    parameters: [
      parameterFixtures.numericalContinuous.basic,
      parameterFixtures.numericalContinuous.withNegativeBounds,
      parameterFixtures.numericalContinuous.fractionalBounds,
      parameterFixtures.numericalDiscrete.basic,
      parameterFixtures.numericalDiscrete.withFloatValues,
      parameterFixtures.categorical.basic,
      parameterFixtures.categorical.binaryChoice
    ],
    targets: [targetFixtures.singleTarget.maximize],
    measurements: Array.from({ length: 10 }, (_, i) => ({
      temperature: 20 + i * 8,
      delta_pressure: -5 + i,
      concentration: 0.1 + i * 0.08,
      rpm: 100 + i * 50,
      ph_levels: 6.5 + i * 0.2,
      catalyst: ['Pt', 'Pd', 'Ru', 'Rh'][i % 4],
      stirring: i % 2 === 0 ? 'on' : 'off',
      yield: 0.5 + Math.random() * 0.4
    }))
  }
}

export const updateScenarios = {
  parameterBoundsUpdate: {
    original: parameterFixtures.numericalContinuous.basic,
    updated: {
      ...parameterFixtures.numericalContinuous.basic,
      bounds: [25, 95]
    }
  },
  
  discreteValuesUpdate: {
    original: parameterFixtures.numericalDiscrete.basic,
    updated: {
      ...parameterFixtures.numericalDiscrete.basic,
      values: [150, 250, 350, 450, 550],
      tolerance: 15
    }
  },
  
  categoricalUpdate: {
    original: parameterFixtures.categorical.basic,
    updated: {
      ...parameterFixtures.categorical.basic,
      values: ['Pt', 'Pd', 'Ru', 'Rh', 'Ir'],
      encoding: 'OHE'
    }
  },
  
  multipleParameterUpdate: {
    original: [
      parameterFixtures.numericalContinuous.basic,
      parameterFixtures.numericalDiscrete.basic
    ],
    updated: [
      {
        ...parameterFixtures.numericalContinuous.basic,
        bounds: [30, 90]
      },
      {
        ...parameterFixtures.numericalDiscrete.basic,
        values: [120, 220, 320, 420, 520]
      }
    ]
  }
}

export const apiResponseFixtures = {
  successful: {
    parameterUpdate: {
      status: 'success',
      message: 'Parameter bounds updated successfully',
      filtered_measurements: 8,
      total_measurements: 10,
      suggestions: [
        { temperature: 65, rpm: 350, catalyst: 'Pd' }
      ]
    },
    
    preview: {
      status: 'success',
      message: 'Preview generated successfully',
      preview: {
        filtered_measurements: 6,
        total_measurements: 10,
        impact_summary: '4 measurements will be filtered out due to parameter bound changes'
      },
      suggestions: [
        { temperature: 55, rpm: 280, catalyst: 'Pt' }
      ]
    },
    
    multipleUpdates: {
      status: 'success',
      message: 'Multiple parameters updated successfully',
      filtered_measurements: 7,
      total_measurements: 10,
      parameter_changes: 3,
      target_changes: 1
    }
  },
  
  errors: {
    invalidBounds: {
      status: 'error',
      message: 'Invalid parameter bounds: lower bound must be less than upper bound',
      parameter: 'temperature',
      provided_bounds: [100, 20]
    },
    
    optimizerNotFound: {
      status: 'error',
      message: 'Optimizer test_optimizer not found',
      optimizer_id: 'test_optimizer'
    },
    
    invalidParameterType: {
      status: 'error',
      message: 'Invalid parameter type: InvalidType',
      parameter: 'test_param',
      provided_type: 'InvalidType'
    },
    
    validationError: {
      status: 'error',
      message: 'Parameter validation failed',
      errors: [
        'Parameter name cannot be empty',
        'Bounds must be numeric values',
        'Values array cannot be empty'
      ]
    }
  }
}

export const edgeCases = {
  emptyOptimization: {
    optimizer_id: 'empty_test',
    parameters: [],
    targets: [],
    measurements: []
  },
  
  singleParameter: {
    optimizer_id: 'single_param_test',
    parameters: [parameterFixtures.numericalContinuous.basic],
    targets: [targetFixtures.singleTarget.maximize],
    measurements: [
      { temperature: 50, yield: 0.8 }
    ]
  },
  
  extremeValues: {
    optimizer_id: 'extreme_test',
    parameters: [
      {
        name: 'large_range',
        type: 'NumericalContinuous',
        bounds: [0, 1000000],
        description: 'Parameter with large range'
      },
      {
        name: 'tiny_range',
        type: 'NumericalContinuous',
        bounds: [0.0001, 0.0002],
        description: 'Parameter with tiny range'
      }
    ],
    targets: [targetFixtures.singleTarget.maximize],
    measurements: []
  }
}
