// db/db.ts
import { drizzle } from "drizzle-orm/postgres-js"
import postgres from "postgres"
import {
  profilesTable,
  optimizationsTable,
  measurementsTable,
  suggestionsTable,
  samplesTable,
  optimizationStatusHistoryTable,
  feedbackTable,
  userSurveysTable
} from "@/db/schema"
import { strategyMetricsTable } from "./schema/strategy-metrics-schema"

// Check for required environment variable
if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is not set")
}

// Create database connection with retry logic
const connectionString = process.env.DATABASE_URL

// Configure connection options with better error handling
const client = postgres(connectionString, {
  max: 5, // Increase connection pool size
  idle_timeout: 20, // Shorter idle timeout (seconds)
  connect_timeout: 5, // Shorter connection timeout (seconds)
  max_lifetime: 60 * 30, // Max connection lifetime (seconds)
  debug: process.env.NODE_ENV === "development", // Enable debug logging in development
  onnotice: () => {}, // Suppress notice messages
  onparameter: () => {}, // Suppress parameter messages
  types: {}, // No custom types
  connection: {
    application_name: "BOapp-frontend" // Identify app in database logs
  },
  ssl:
    process.env.DATABASE_SSL === "true" ? { rejectUnauthorized: false } : false,
  // Add retry logic
  transform: {
    undefined: null
  }
  // onretry is not supported in the current version of postgres.js
})

// Define schema for tables
const schema = {
  profiles: profilesTable,
  optimizations: optimizationsTable,
  measurements: measurementsTable,
  suggestions: suggestionsTable,
  samples: samplesTable,
  optimizationStatusHistory: optimizationStatusHistoryTable,
  feedback: feedbackTable,
  userSurveys: userSurveysTable
}

// Create drizzle instance with schema
export const db = drizzle(client, { schema })
