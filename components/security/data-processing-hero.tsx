"use client"

import { motion } from "framer-motion"
import { Database, Eye, Shield, Download } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function DataProcessingHero() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <div className="mx-auto mb-4 flex size-20 items-center justify-center rounded-full bg-green-100">
              <Database className="size-10 text-green-600" />
            </div>
            <h1 className="mb-4 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
              Data Processing
              <span className="block text-green-600">Transparency</span>
            </h1>
            <p className="mx-auto max-w-3xl text-xl text-gray-600">
              Complete transparency about how we collect, process, store, and
              protect your data. Understand exactly what happens to your
              information in our optimization platform.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-12 flex flex-wrap justify-center gap-4"
          >
            <Button
              asChild
              size="lg"
              className="bg-green-600 hover:bg-green-700"
            >
              <Link href="#data-collection">
                <Eye className="mr-2 size-4" />
                What We Collect
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="#data-flow">
                <Database className="mr-2 size-4" />
                Data Flow
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="#user-rights">
                <Download className="mr-2 size-4" />
                Your Rights
              </Link>
            </Button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-1 gap-6 md:grid-cols-4"
          >
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <Eye className="mx-auto mb-3 size-8 text-blue-600" />
              <h3 className="mb-2 font-semibold text-gray-900">
                Data Collection
              </h3>
              <p className="text-sm text-gray-600">
                Only essential data for optimization services
              </p>
            </div>
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <Database className="mx-auto mb-3 size-8 text-green-600" />
              <h3 className="mb-2 font-semibold text-gray-900">
                Data Processing
              </h3>
              <p className="text-sm text-gray-600">
                Secure processing with complete isolation
              </p>
            </div>
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <Shield className="mx-auto mb-3 size-8 text-purple-600" />
              <h3 className="mb-2 font-semibold text-gray-900">
                Data Protection
              </h3>
              <p className="text-sm text-gray-600">
                Encrypted storage and transmission
              </p>
            </div>
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <Download className="mx-auto mb-3 size-8 text-orange-600" />
              <h3 className="mb-2 font-semibold text-gray-900">Data Control</h3>
              <p className="text-sm text-gray-600">
                Full control and export capabilities
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
