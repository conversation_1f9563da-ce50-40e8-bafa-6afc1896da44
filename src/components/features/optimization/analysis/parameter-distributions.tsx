"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>hart, RefreshCw, AlertCircle, Maximize2 } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import dynamic from "next/dynamic"

// Import Plotly dynamically to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

interface ParameterDistributionsProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  parameterNames: string[]
  onParameterSelect?: (parameter: string) => void
  selectedParameter?: string
}

interface DistributionData {
  parameters: Record<string, number[]>
  parameterRanges: Record<string, { min: number; max: number }>
  experimentIndices: number[]
}

export function ParameterDistributions({
  optimization,
  measurements,
  parameterNames,
  onParameterSelect,
  selectedParameter
}: ParameterDistributionsProps) {
  const [distributionData, setDistributionData] =
    useState<DistributionData | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [displayMode, setDisplayMode] = useState<string>("violin")
  const [selectedParameters, setSelectedParameters] = useState<string[]>([])
  const [displayCount, setDisplayCount] = useState<number>(4)

  // Calculate distribution data
  const calculateDistributionData = () => {
    setIsLoading(true)
    setError(null)

    try {
      // Extract parameter values
      const paramValues: Record<string, number[]> = {}
      const paramRanges: Record<string, { min: number; max: number }> = {}
      const experimentIndices: number[] = []

      // Initialize parameter arrays
      parameterNames.forEach(param => {
        paramValues[param] = []
        paramRanges[param] = { min: Infinity, max: -Infinity }
      })

      // Collect values
      measurements.forEach((measurement, index) => {
        experimentIndices.push(index + 1) // 1-based indices for display

        // Extract parameter values
        parameterNames.forEach(param => {
          // Add type assertion to handle unknown type
          const params = measurement.parameters as Record<
            string,
            string | number
          >
          const value = parseFloat(String(params[param]))
          if (!isNaN(value)) {
            paramValues[param].push(value)

            // Update ranges
            paramRanges[param].min = Math.min(paramRanges[param].min, value)
            paramRanges[param].max = Math.max(paramRanges[param].max, value)
          }
        })
      })

      setDistributionData({
        parameters: paramValues,
        parameterRanges: paramRanges,
        experimentIndices: experimentIndices
      })

      // Initialize selected parameters if empty
      if (selectedParameters.length === 0 && parameterNames.length > 0) {
        setSelectedParameters(parameterNames.slice(0, displayCount))
      }

      console.log("Distribution data calculated:", {
        parameterCount: Object.keys(paramValues).length,
        measurementCount: measurements.length,
        parameterRanges: paramRanges
      })
    } catch (err) {
      console.error("Error calculating distribution data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  // Generate violin plot data
  const violinPlotData = useMemo(() => {
    if (!distributionData || selectedParameters.length === 0) return []

    return selectedParameters.map(param => {
      const values = distributionData.parameters[param] || []

      return {
        type: "violin" as const,
        x: Array(values.length).fill(param),
        y: values,
        name: param,
        box: {
          visible: true
        },
        meanline: {
          visible: true
        },
        points: "all" as const,
        pointpos: 0,
        jitter: 0.3,
        marker: {
          size: 8,
          opacity: 0.7
        },
        hoverinfo: "y" as const,
        hoverlabel: {
          namelength: -1
        }
      }
    })
  }, [distributionData, selectedParameters])

  // Generate histogram data
  const histogramPlotData = useMemo(() => {
    if (!distributionData || selectedParameters.length === 0) return []

    return selectedParameters.map(param => {
      const values = distributionData.parameters[param] || []

      return {
        type: "histogram" as const,
        x: values,
        name: param,
        opacity: 0.7,
        marker: {
          line: {
            width: 1
          }
        },
        hoverinfo: "x+y" as const,
        hoverlabel: {
          namelength: -1
        }
      }
    })
  }, [distributionData, selectedParameters])

  // Generate box plot data
  const boxPlotData = useMemo(() => {
    if (!distributionData || selectedParameters.length === 0) return []

    return selectedParameters.map(param => {
      const values = distributionData.parameters[param] || []

      return {
        type: "box" as const,
        y: values,
        name: param,
        boxpoints: "all" as const,
        jitter: 0.3,
        pointpos: 0,
        marker: {
          size: 8,
          opacity: 0.7
        },
        hoverinfo: "y" as const,
        hoverlabel: {
          namelength: -1
        }
      }
    })
  }, [distributionData, selectedParameters])

  // Handle parameter selection
  const handleParameterSelect = (param: string) => {
    if (onParameterSelect) {
      onParameterSelect(param)
    }
  }

  // Effect to calculate distribution data when measurements change
  useEffect(() => {
    if (measurements.length > 0 && parameterNames.length > 0) {
      calculateDistributionData()
    }
  }, [measurements.length, parameterNames.length])

  // Effect to update selected parameters when selectedParameter changes
  useEffect(() => {
    if (selectedParameter && !selectedParameters.includes(selectedParameter)) {
      // Replace the first parameter with the selected one
      const newSelected = [...selectedParameters]
      if (newSelected.length > 0) {
        newSelected[0] = selectedParameter
      } else {
        newSelected.push(selectedParameter)
      }
      setSelectedParameters(newSelected)
    }
  }, [selectedParameter])

  // Handle parameter selection change
  const handleParameterChange = (index: number, value: string) => {
    const newSelected = [...selectedParameters]
    newSelected[index] = value
    setSelectedParameters(newSelected)
  }

  // Get plot data based on display mode
  const getPlotData = () => {
    switch (displayMode) {
      case "violin":
        return violinPlotData
      case "histogram":
        return histogramPlotData
      case "box":
        return boxPlotData
      default:
        return violinPlotData
    }
  }

  // Get plot layout based on display mode
  const getPlotLayout = () => {
    const baseLayout = {
      autosize: true,
      margin: { l: 60, r: 20, t: 50, b: 60 },
      showlegend: true,
      legend: {
        orientation: "h" as const,
        y: -0.2
      }
    }

    switch (displayMode) {
      case "violin":
        return {
          ...baseLayout,
          title: "Parameter Distributions (Violin Plot)",
          violinmode: "group" as const,
          xaxis: {
            title: "Parameter"
          },
          yaxis: {
            title: "Value"
          }
        }
      case "histogram":
        return {
          ...baseLayout,
          title: "Parameter Distributions (Histogram)",
          barmode: "overlay" as const,
          xaxis: {
            title: "Value"
          },
          yaxis: {
            title: "Count"
          }
        }
      case "box":
        return {
          ...baseLayout,
          title: "Parameter Distributions (Box Plot)",
          boxmode: "group" as const,
          xaxis: {
            title: "Parameter"
          },
          yaxis: {
            title: "Value"
          }
        }
      default:
        return baseLayout
    }
  }

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="flex items-center text-lg font-medium">
            <LineChart className="text-primary mr-2 size-5" />
            Parameter Distributions
          </h3>
          <div className="flex gap-2">
            <Tabs
              value={displayMode}
              onValueChange={setDisplayMode}
              className="w-auto"
            >
              <TabsList className="grid w-auto grid-cols-3">
                <TabsTrigger value="violin">Violin</TabsTrigger>
                <TabsTrigger value="histogram">Histogram</TabsTrigger>
                <TabsTrigger value="box">Box</TabsTrigger>
              </TabsList>
            </Tabs>
            <Button
              variant="outline"
              size="sm"
              onClick={calculateDistributionData}
              disabled={isLoading}
            >
              {isLoading ? (
                <RefreshCw className="size-4 animate-spin" />
              ) : (
                <RefreshCw className="size-4" />
              )}
              <span className="ml-2 hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="size-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex h-[400px] items-center justify-center">
            <Skeleton className="size-full rounded-md" />
          </div>
        ) : distributionData ? (
          <div className="space-y-4">
            <div className="mb-4 grid grid-cols-2 gap-2 md:grid-cols-4">
              {Array.from({ length: displayCount }).map((_, index) => (
                <Select
                  key={index}
                  value={selectedParameters[index] || ""}
                  onValueChange={value => handleParameterChange(index, value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select parameter" />
                  </SelectTrigger>
                  <SelectContent>
                    {parameterNames.map(param => (
                      <SelectItem key={param} value={param}>
                        {param}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ))}
            </div>

            <div className="h-[400px] w-full overflow-hidden rounded-md border">
              <Plot
                data={getPlotData()}
                layout={getPlotLayout()}
                config={{ responsive: true, displayModeBar: false }}
                style={{ width: "100%", height: "100%" }}
                onClick={(event: any) => {
                  if (event && event.points && event.points.length > 0) {
                    const point = event.points[0]
                    const paramName = point.data.name || (point.x && point.x[0])
                    if (paramName && parameterNames.includes(paramName)) {
                      handleParameterSelect(paramName)
                    }
                  }
                }}
              />
            </div>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="w-full">
                  <Maximize2 className="mr-2 size-4" />
                  Expand View
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Parameter Distributions</DialogTitle>
                </DialogHeader>
                <div className="h-[600px] w-full">
                  <Plot
                    data={getPlotData()}
                    layout={{
                      ...getPlotLayout(),
                      height: 600
                    }}
                    config={{ responsive: true }}
                    style={{ width: "100%", height: "100%" }}
                  />
                </div>
              </DialogContent>
            </Dialog>
          </div>
        ) : (
          <div className="flex h-[400px] flex-col items-center justify-center p-4 text-center">
            <LineChart className="text-muted-foreground mb-4 size-12" />
            <h3 className="text-lg font-medium">No distribution data</h3>
            <p className="text-muted-foreground mt-2 max-w-md text-sm">
              There might not be enough measurements to calculate distributions,
              or the parameters might not have numeric values.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
