"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import { toast } from "@/components/ui/use-toast"
import { Play, Pause, CheckCircle, AlertTriangle, RotateCw } from "lucide-react"
import { OptimizationStatus } from "@/types"
import {
  statusColors,
  statusLabels,
  statusDescriptions
} from "@/lib/status-utils"
import { updateOptimizationStatusAction } from "@/actions/optimization-status-actions"
import { SelectOptimization } from "@/db/schema"

interface OptimizationStatusControlProps {
  optimization: SelectOptimization
  onStatusChange?: (newStatus: OptimizationStatus) => void
}

export function OptimizationStatusControl({
  optimization,
  onStatusChange
}: OptimizationStatusControlProps) {
  const [isUpdating, setIsUpdating] = useState(false)
  const [completeDialogOpen, setCompleteDialogOpen] = useState(false)
  const [pauseDialogOpen, setPauseDialogOpen] = useState(false)
  const [resumeDialogOpen, setResumeDialogOpen] = useState(false)

  // Status colors, labels, and descriptions are imported from lib/status-utils.ts

  // Update the optimization status
  const updateStatus = async (newStatus: OptimizationStatus) => {
    setIsUpdating(true)
    try {
      const result = await updateOptimizationStatusAction(
        optimization.id,
        newStatus
      )

      if (result.isSuccess) {
        toast({
          title: "Status Updated",
          description: `Optimization status changed to ${newStatus}`
        })

        // Call the callback if provided
        if (onStatusChange) {
          onStatusChange(newStatus)
        }
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error updating status:", error)
      toast({
        title: "Error",
        description: "Failed to update optimization status",
        variant: "destructive"
      })
    } finally {
      setIsUpdating(false)
      setCompleteDialogOpen(false)
      setPauseDialogOpen(false)
      setResumeDialogOpen(false)
    }
  }

  return (
    <>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Optimization Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            {/* Current status display */}
            <div className="flex items-center">
              <div
                className={`size-3 rounded-full ${statusColors[optimization.status as keyof typeof statusColors]} mr-2`}
                title={
                  statusDescriptions[
                    optimization.status as keyof typeof statusDescriptions
                  ]
                }
              />
              <div className="mr-2 text-2xl font-bold capitalize">
                {optimization.status}
              </div>
              <Badge
                variant={
                  optimization.status === "active" ? "default" : "outline"
                }
                className="ml-2"
              >
                {statusLabels[optimization.status as keyof typeof statusLabels]}
              </Badge>
            </div>

            <p className="text-muted-foreground text-sm">
              {
                statusDescriptions[
                  optimization.status as keyof typeof statusDescriptions
                ]
              }
            </p>

            {/* Status control buttons */}
            <div className="flex flex-wrap gap-2 pt-2">
              {optimization.status !== "active" && (
                <Button
                  onClick={() => setResumeDialogOpen(true)}
                  size="sm"
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <RotateCw className="mr-2 size-4 animate-spin" />
                  ) : (
                    <Play className="mr-2 size-4" />
                  )}
                  Resume Optimization
                </Button>
              )}

              {optimization.status === "active" && (
                <Button
                  onClick={() => setPauseDialogOpen(true)}
                  size="sm"
                  variant="outline"
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <RotateCw className="mr-2 size-4 animate-spin" />
                  ) : (
                    <Pause className="mr-2 size-4" />
                  )}
                  Pause Optimization
                </Button>
              )}

              {optimization.status !== "completed" && (
                <Button
                  onClick={() => setCompleteDialogOpen(true)}
                  size="sm"
                  variant="outline"
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <RotateCw className="mr-2 size-4 animate-spin" />
                  ) : (
                    <CheckCircle className="mr-2 size-4" />
                  )}
                  Mark as Completed
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Complete Dialog */}
      <AlertDialog
        open={completeDialogOpen}
        onOpenChange={setCompleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Complete Optimization</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to mark this optimization as completed? This
              will indicate that no further experiments will be run. You can
              resume it later if needed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUpdating}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => updateStatus("completed")}
              disabled={isUpdating}
            >
              {isUpdating ? "Updating..." : "Complete Optimization"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Pause Dialog */}
      <AlertDialog open={pauseDialogOpen} onOpenChange={setPauseDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Pause Optimization</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to pause this optimization? No new
              experiments will be suggested until you resume it.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUpdating}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => updateStatus("paused")}
              disabled={isUpdating}
            >
              {isUpdating ? "Updating..." : "Pause Optimization"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Resume Dialog */}
      <AlertDialog open={resumeDialogOpen} onOpenChange={setResumeDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Resume Optimization</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to resume this optimization? This will allow
              new experiments to be suggested and measurements to be added.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUpdating}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => updateStatus("active")}
              disabled={isUpdating}
            >
              {isUpdating ? "Updating..." : "Resume Optimization"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
