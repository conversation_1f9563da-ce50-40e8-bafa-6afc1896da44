// scripts/run-user-surveys-migration.js
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });
// Also load from .env if .env.local doesn't exist
dotenv.config();

async function runMigration() {
  try {
    // Create database connection
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL environment variable is not set');
    }
    
    console.log('Connecting to database...');
    
    // Create a connection with a longer timeout
    const client = postgres(connectionString, { 
      max: 1,
      connect_timeout: 30, // 30 seconds timeout
      idle_timeout: 30, // 30 seconds idle timeout
      ssl: process.env.DATABASE_SSL === "true" ? { rejectUnauthorized: false } : false
    });
    
    const db = drizzle(client);
    console.log('Connected to database');
    
    // Check if the table already exists
    console.log('Checking if user_surveys table exists...');
    const checkResult = await db.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'user_surveys'
    `);
    
    if (checkResult.length === 0) {
      console.log('Creating user_surveys table...');
      
      // Read the migration file
      const migrationPath = path.join(__dirname, '../db/migrations/add_user_surveys_table.sql');
      const migrationSql = fs.readFileSync(migrationPath, 'utf8');
      
      // Execute the migration
      await db.execute(migrationSql);
      
      console.log('Migration completed successfully!');
    } else {
      console.log('user_surveys table already exists, no migration needed');
    }
    
    // Close the connection
    await client.end();
    console.log('Database connection closed');
    
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

runMigration();
