/*
This server page provides the user profile management interface integrated with <PERSON>.
*/

"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { ProfileManagement } from "@/components/dashboard/profile-management"
import { DashboardThemeWrapper } from "@/components/dashboard/dashboard-theme-wrapper"

export default async function ProfilePage() {
  const { userId } = await auth()

  // If user is not authenticated, redirect to login
  if (!userId) {
    redirect("/login?redirect_url=/dashboard/profile")
  }

  return (
    <DashboardThemeWrapper>
      <div className="mb-6">
        <h1 className="text-2xl font-bold md:text-3xl">Profile Settings</h1>
        <p className="text-muted-foreground mt-2">
          Manage your account information, security settings, and preferences.
        </p>
      </div>

      <ProfileManagement />
    </DashboardThemeWrapper>
  )
}
