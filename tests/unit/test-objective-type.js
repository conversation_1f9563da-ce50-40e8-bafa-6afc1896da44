// Test script to verify objective type detection
const { getObjectiveType } = require('./lib/optimization-utils.ts');

// Test cases for different optimization configurations
const testCases = [
  {
    name: "Single Target Optimization",
    optimization: {
      targetMode: "MAX",
      config: {
        objective_type: "SingleTarget",
        target_config: {
          name: "Yield",
          mode: "MAX"
        }
      }
    },
    expected: "SINGLE"
  },
  {
    name: "Multi Target Desirability",
    optimization: {
      targetMode: "MULTI",
      config: {
        objective_type: "Desirability",
        target_config: [
          { name: "Yield", mode: "MAX" },
          { name: "Cost", mode: "MIN" }
        ],
        scalarizer: "GEOM_MEAN"
      }
    },
    expected: "MULTI_DESIRABILITY"
  },
  {
    name: "Multi Target Pareto",
    optimization: {
      targetMode: "MULTI",
      config: {
        objective_type: "Pareto",
        target_config: [
          { name: "Yield", mode: "MAX" },
          { name: "Cost", mode: "MIN" }
        ],
        acquisition_config: {
          type: "qNoisyExpectedHypervolumeImprovement"
        }
      }
    },
    expected: "MULTI_PARETO"
  },
  {
    name: "Legacy Multi Target with qEI (should be Desirability)",
    optimization: {
      targetMode: "MULTI",
      config: {
        target_config: [
          { name: "Yield", mode: "MAX" },
          { name: "Cost", mode: "MIN" }
        ],
        acquisition_config: {
          type: "qExpectedImprovement"
        }
      }
    },
    expected: "MULTI_DESIRABILITY"
  },
  {
    name: "Legacy Multi Target with qNEHVI (should be Pareto)",
    optimization: {
      targetMode: "MULTI",
      config: {
        target_config: [
          { name: "Yield", mode: "MAX" },
          { name: "Cost", mode: "MIN" }
        ],
        acquisition_config: {
          type: "qNoisyExpectedHypervolumeImprovement"
        }
      }
    },
    expected: "MULTI_PARETO"
  }
];

console.log("Testing Objective Type Detection...\n");

testCases.forEach((testCase, index) => {
  try {
    const result = getObjectiveType(testCase.optimization);
    const passed = result === testCase.expected;
    
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log(`  Expected: ${testCase.expected}`);
    console.log(`  Got: ${result}`);
    console.log(`  Status: ${passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log('');
  } catch (error) {
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log(`  Status: ❌ ERROR - ${error.message}`);
    console.log('');
  }
});
