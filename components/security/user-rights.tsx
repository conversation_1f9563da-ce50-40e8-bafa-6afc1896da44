"use client"

import { motion } from "framer-motion"
import {
  Eye,
  Download,
  Edit,
  Trash2,
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

const userRights = [
  {
    icon: Eye,
    title: "Right to Access",
    description: "View all personal data we hold about you",
    details: [
      "Complete data inventory and usage",
      "Data processing purposes and legal basis",
      "Data sharing and third-party processors",
      "Data retention periods and deletion schedules"
    ],
    howTo:
      "Access your data through your account dashboard or contact our privacy team",
    timeframe: "Immediate (dashboard) or within 30 days (comprehensive report)"
  },
  {
    icon: Edit,
    title: "Right to Rectification",
    description: "Correct inaccurate or incomplete personal data",
    details: [
      "Update account information and preferences",
      "Correct experimental data and parameters",
      "Modify optimization settings and objectives",
      "Update contact and billing information"
    ],
    howTo: "Edit information directly in your account or contact support",
    timeframe: "Immediate (self-service) or within 72 hours (support)"
  },
  {
    icon: Trash2,
    title: "Right to Erasure",
    description: "Request deletion of your personal data",
    details: [
      "Delete specific experiments or datasets",
      "Remove account and all associated data",
      "Erase data no longer needed for original purpose",
      "Withdraw consent for optional data processing"
    ],
    howTo: "Use account deletion tools or submit a deletion request",
    timeframe: "Within 30 days of request verification"
  },
  {
    icon: Download,
    title: "Right to Data Portability",
    description: "Export your data in a machine-readable format",
    details: [
      "Download all optimization experiments and results",
      "Export account data and preferences",
      "Receive data in standard formats (CSV, JSON)",
      "Transfer data to other optimization platforms"
    ],
    howTo: "Use the data export tool in your account dashboard",
    timeframe: "Immediate download or within 48 hours for large datasets"
  },
  {
    icon: Shield,
    title: "Right to Restrict Processing",
    description: "Limit how we process your personal data",
    details: [
      "Pause data processing while disputing accuracy",
      "Restrict processing for specific purposes",
      "Limit automated decision-making",
      "Maintain data without active processing"
    ],
    howTo: "Contact our privacy team with specific restrictions",
    timeframe: "Within 72 hours of request"
  },
  {
    icon: AlertTriangle,
    title: "Right to Object",
    description: "Object to certain types of data processing",
    details: [
      "Object to direct marketing communications",
      "Opt out of analytics and performance tracking",
      "Refuse automated decision-making",
      "Object to processing based on legitimate interests"
    ],
    howTo: "Use privacy settings or contact our privacy team",
    timeframe: "Immediate (settings) or within 72 hours (request)"
  }
]

const exerciseRights = [
  {
    method: "Self-Service Dashboard",
    description: "Immediate access to most data rights through your account",
    rights: ["Access", "Rectification", "Data Portability", "Some Objections"],
    availability: "24/7"
  },
  {
    method: "Privacy Request Form",
    description: "Formal request process for complex data rights",
    rights: ["Erasure", "Restrict Processing", "Complex Objections"],
    availability: "Business hours response"
  },
  {
    method: "Email Contact",
    description: "Direct contact with our privacy team",
    rights: ["All Rights", "Complex Situations", "Legal Questions"],
    availability: "<EMAIL>"
  }
]

export function UserRights() {
  return (
    <section id="user-rights" className="py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Your Data Rights
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            You have comprehensive rights over your personal data. Learn about
            your rights and how to exercise them easily and effectively.
          </p>
        </motion.div>

        <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {userRights.map((right, index) => (
            <motion.div
              key={right.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full transition-shadow hover:shadow-lg">
                <CardHeader>
                  <div className="mb-4 flex size-12 items-center justify-center rounded-lg bg-indigo-100">
                    <right.icon className="size-6 text-indigo-600" />
                  </div>
                  <CardTitle className="text-xl">{right.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {right.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="mb-2 text-sm font-semibold text-gray-900">
                        What you can do:
                      </h4>
                      <ul className="space-y-1">
                        {right.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-start">
                            <CheckCircle className="mr-2 mt-0.5 size-3 shrink-0 text-green-600" />
                            <span className="text-sm text-gray-700">
                              {detail}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="mb-1 text-sm font-semibold text-gray-900">
                        How to exercise:
                      </h4>
                      <p className="text-sm text-gray-600">{right.howTo}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="size-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        {right.timeframe}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="mb-12"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                How to Exercise Your Rights
              </CardTitle>
              <CardDescription>
                Multiple ways to exercise your data rights based on your needs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                {exerciseRights.map((method, index) => (
                  <div key={index} className="text-center">
                    <h4 className="mb-3 font-semibold text-gray-900">
                      {method.method}
                    </h4>
                    <p className="mb-4 text-sm text-gray-600">
                      {method.description}
                    </p>
                    <div className="mb-4 space-y-1">
                      {method.rights.map((right, rightIndex) => (
                        <Badge
                          key={rightIndex}
                          variant="outline"
                          className="mr-1 text-xs"
                        >
                          {right}
                        </Badge>
                      ))}
                    </div>
                    <div className="text-sm font-medium text-indigo-600">
                      {method.availability}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <Card className="bg-gradient-to-r from-indigo-50 to-purple-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="mb-4 text-xl font-semibold text-gray-900">
                  Exercise Your Rights Today
                </h3>
                <p className="mb-6 text-gray-600">
                  Your privacy rights are important to us. We've made it easy to
                  exercise your rights with self-service tools and responsive
                  support.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <p className="max-w-md text-center text-gray-600">
                    To exercise your data rights, please contact our privacy
                    team at{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="font-medium text-indigo-600 hover:text-indigo-700"
                    >
                      <EMAIL>
                    </a>
                    .
                  </p>
                </div>
                <div className="mt-6 grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-indigo-600">
                      6
                    </div>
                    <div className="text-sm text-gray-600">Data Rights</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-green-600">
                      24/7
                    </div>
                    <div className="text-sm text-gray-600">Self-Service</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-purple-600">
                      72hrs
                    </div>
                    <div className="text-sm text-gray-600">Max Response</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-orange-600">
                      Free
                    </div>
                    <div className="text-sm text-gray-600">No Charges</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
