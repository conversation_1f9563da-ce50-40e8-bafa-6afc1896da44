#!/usr/bin/env node

/**
 * Debug script to investigate campaign-database synchronization issue
 * for "test CRUD filtering32" optimization
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp } = require("drizzle-orm/pg-core")

// Database schema definitions
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

const samplesTable = pgTable("samples", {
  id: uuid("id").primary<PERSON>ey(),
  optimizationId: uuid("optimization_id").notNull(),
  parameters: jsonb("parameters").notNull(),
  batchId: text("batch_id").notNull(),
  sampleIndex: text("sample_index").notNull(),
  samplingMethod: text("sampling_method").notNull(),
  sampleClass: text("sample_class").notNull(),
  targetValues: jsonb("target_values"),
  status: text("status").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function debugCampaignSync() {
  console.log('🔍 DEBUGGING CAMPAIGN-DATABASE SYNCHRONIZATION ISSUE');
  console.log('====================================================');
  console.log('Optimization: test CRUD filtering32');
  console.log('Issue: Backend campaign has 3 measurements, database has 5 samples');
  console.log('');

  let client;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    // Find the optimization
    const optimizationName = "test CRUD filtering32";
    const optimizationsList = await db.select().from(optimizationsTable);
    const opt = optimizationsList.find(o => o.name === optimizationName);
    
    if (!opt) {
      console.error(`❌ Optimization "${optimizationName}" not found`);
      return;
    }
    
    console.log(`✅ Found optimization: ${opt.optimizerId}`);
    
    // Get all database samples
    const samples = await db.select().from(samplesTable).where(eq(samplesTable.optimizationId, opt.id));
    console.log(`\n📊 DATABASE SAMPLES ANALYSIS (${samples.length} total):`);
    
    samples.forEach((sample, index) => {
      console.log(`\n  ${index + 1}. Sample ${sample.id.slice(-12)}:`);
      console.log(`     - Status: ${sample.status}`);
      console.log(`     - Sample Class: ${sample.sampleClass}`);
      console.log(`     - Batch ID: ${sample.batchId}`);
      console.log(`     - Parameters: ${JSON.stringify(sample.parameters)}`);
      console.log(`     - Target Values: ${JSON.stringify(sample.targetValues)}`);
      console.log(`     - Created: ${sample.createdAt}`);
      console.log(`     - Updated: ${sample.updatedAt}`);
    });
    
    // Try to get backend measurements through different endpoints
    console.log(`\n🔍 TESTING DIFFERENT BACKEND ENDPOINTS:`);
    
    // Test 1: Direct measurements endpoint
    console.log(`\n1. Testing /api/optimizations/{id}/measurements:`);
    try {
      const measurementsResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/measurements`);
      console.log(`   Status: ${measurementsResponse.status} ${measurementsResponse.statusText}`);
      
      if (measurementsResponse.ok) {
        const measurementsData = await measurementsResponse.json();
        console.log(`   Response: ${JSON.stringify(measurementsData, null, 2)}`);
      } else {
        const errorText = await measurementsResponse.text();
        console.log(`   Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   Exception: ${error.message}`);
    }
    
    // Test 2: Campaign status endpoint
    console.log(`\n2. Testing campaign status:`);
    try {
      const statusResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/status`);
      console.log(`   Status: ${statusResponse.status} ${statusResponse.statusText}`);
      
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        console.log(`   Response: ${JSON.stringify(statusData, null, 2)}`);
      } else {
        const errorText = await statusResponse.text();
        console.log(`   Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   Exception: ${error.message}`);
    }
    
    // Test 3: Try to load the campaign first
    console.log(`\n3. Testing campaign loading:`);
    try {
      const loadResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/load`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      console.log(`   Load Status: ${loadResponse.status} ${loadResponse.statusText}`);
      
      if (loadResponse.ok) {
        const loadData = await loadResponse.json();
        console.log(`   Load Response: ${JSON.stringify(loadData, null, 2)}`);
        
        // Now try measurements again
        console.log(`\n   Retrying measurements after loading:`);
        const measurementsResponse2 = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/measurements`);
        console.log(`   Measurements Status: ${measurementsResponse2.status} ${measurementsResponse2.statusText}`);
        
        if (measurementsResponse2.ok) {
          const measurementsData2 = await measurementsResponse2.json();
          console.log(`   Measurements Response: ${JSON.stringify(measurementsData2, null, 2)}`);
          
          const measurements = measurementsData2.measurements || [];
          console.log(`\n📊 BACKEND MEASUREMENTS ANALYSIS (${measurements.length} total):`);
          
          measurements.forEach((measurement, index) => {
            console.log(`\n  ${index + 1}. Backend measurement:`);
            console.log(`     - Target 1: ${measurement['Target 1']}`);
            console.log(`     - Target 2: ${measurement['Target 2']}`);
            console.log(`     - Batch Nr: ${measurement['BatchNr']}`);
            console.log(`     - Fit Nr: ${measurement['FitNr']}`);
            console.log(`     - Parameters: x1=${measurement.x1}, x2=${measurement.x2}, x3=${measurement.x3}, x4=${measurement.x4}`);
          });
          
          // Compare with database samples
          console.log(`\n🔍 COMPARING DATABASE VS BACKEND:`);
          console.log(`Database samples: ${samples.length}`);
          console.log(`Backend measurements: ${measurements.length}`);
          
          if (samples.length !== measurements.length) {
            console.log(`❌ COUNT MISMATCH: Database has ${samples.length}, backend has ${measurements.length}`);
            
            // Try to match samples to measurements
            console.log(`\n🔍 MATCHING ANALYSIS:`);
            
            samples.forEach((sample, index) => {
              const sampleTarget1 = sample.targetValues?.['Target 1'];
              const sampleTarget2 = sample.targetValues?.['Target 2'];
              
              // Find matching measurement
              const matchingMeasurement = measurements.find(m => 
                m['Target 1'] === sampleTarget1 && m['Target 2'] === sampleTarget2
              );
              
              if (matchingMeasurement) {
                console.log(`  ✅ Sample ${index + 1} MATCHED to backend measurement`);
              } else {
                console.log(`  ❌ Sample ${index + 1} NOT FOUND in backend (Target 1: ${sampleTarget1}, Target 2: ${sampleTarget2})`);
              }
            });
            
            // Check for backend measurements not in database
            measurements.forEach((measurement, index) => {
              const measurementTarget1 = measurement['Target 1'];
              const measurementTarget2 = measurement['Target 2'];
              
              const matchingSample = samples.find(s => 
                s.targetValues?.['Target 1'] === measurementTarget1 && 
                s.targetValues?.['Target 2'] === measurementTarget2
              );
              
              if (!matchingSample) {
                console.log(`  ❌ Backend measurement ${index + 1} NOT FOUND in database (Target 1: ${measurementTarget1}, Target 2: ${measurementTarget2})`);
              }
            });
          } else {
            console.log(`✅ COUNT MATCH: Both have ${samples.length} entries`);
          }
          
        } else {
          const errorText2 = await measurementsResponse2.text();
          console.log(`   Measurements Error: ${errorText2}`);
        }
      } else {
        const errorText = await loadResponse.text();
        console.log(`   Load Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   Exception: ${error.message}`);
    }
    
    // Test 4: Now test the target filtering with the loaded campaign
    console.log(`\n4. Testing target filtering with loaded campaign:`);
    try {
      const targetFilterResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/bounds`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          target_bounds: {
            "Target 1": [25, 100]
          },
          preview_only: true
        })
      });
      
      console.log(`   Filter Status: ${targetFilterResponse.status} ${targetFilterResponse.statusText}`);
      
      if (targetFilterResponse.ok) {
        const filterData = await targetFilterResponse.json();
        console.log(`   Filter Response: ${JSON.stringify(filterData, null, 2)}`);
        
        console.log(`\n📊 FINAL FILTERING ANALYSIS:`);
        console.log(`   Total measurements in filter: ${filterData.impact_summary?.total_measurements || 0}`);
        console.log(`   Valid measurements: ${filterData.valid_count || 0}`);
        console.log(`   Dropped measurements: ${filterData.dropped_count || 0}`);
        
        // Compare with database expectation
        const dbSamplesWithTarget1Below25 = samples.filter(s => {
          const target1 = s.targetValues?.['Target 1'];
          return typeof target1 === 'number' && target1 < 25;
        });
        
        console.log(`\n📊 EXPECTED VS ACTUAL:`);
        console.log(`   Database samples: ${samples.length}`);
        console.log(`   Database samples with Target 1 < 25: ${dbSamplesWithTarget1Below25.length}`);
        console.log(`   Expected to be filtered: ${dbSamplesWithTarget1Below25.length}`);
        console.log(`   Expected to be retained: ${samples.length - dbSamplesWithTarget1Below25.length}`);
        console.log(`   Actually filtered: ${filterData.dropped_count || 0}`);
        console.log(`   Actually retained: ${filterData.valid_count || 0}`);
        
        if (samples.length === (filterData.impact_summary?.total_measurements || 0)) {
          console.log(`   ✅ TOTAL COUNT MATCH: Campaign now has all database samples`);
        } else {
          console.log(`   ❌ TOTAL COUNT MISMATCH: Campaign still missing samples`);
        }
      } else {
        const errorText = await targetFilterResponse.text();
        console.log(`   Filter Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   Exception: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Error during debugging:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

// Run the debug
debugCampaignSync().catch(console.error);
