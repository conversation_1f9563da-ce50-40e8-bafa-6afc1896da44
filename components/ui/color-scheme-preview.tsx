// components/ui/color-scheme-preview.tsx
// Color scheme preview component for surface plots

import { SurfaceColorScheme } from "@/types/surface-color-types"

interface ColorSchemePreviewProps {
  scheme: SurfaceColorScheme
  size?: "sm" | "md" | "lg"
  className?: string
}

// Color gradients for each scheme (representative colors)
const COLOR_GRADIENTS: Record<SurfaceColorScheme, string[]> = {
  viridis: ["#440154", "#31688e", "#35b779", "#fde725"],
  plasma: ["#0d0887", "#7e03a8", "#cc4778", "#f89441", "#f0f921"],
  blues: ["#f7fbff", "#c6dbef", "#6baed6", "#2171b5", "#08306b"],
  reds: ["#fff5f0", "#fcbba1", "#fb6a4a", "#cb181d", "#67000d"],
  rdylbu: ["#d73027", "#fc8d59", "#fee090", "#e0f3f8", "#91bfdb", "#4575b4"],
  rdbu: ["#0571b0", "#92c5de", "#f7f7f7", "#f4a582", "#ca0020"]
}

export function ColorSchemePreview({
  scheme,
  size = "md",
  className = ""
}: ColorSchemePreviewProps) {
  const colors = COLOR_GRADIENTS[scheme] || COLOR_GRADIENTS.viridis

  // Size configurations
  const sizeConfig = {
    sm: { height: "h-3", width: "w-12" },
    md: { height: "h-4", width: "w-16" },
    lg: { height: "h-6", width: "w-24" }
  }

  const { height, width } = sizeConfig[size]

  // Create gradient CSS
  const gradientStyle = {
    background: `linear-gradient(to right, ${colors.join(", ")})`
  }

  return (
    <div
      className={`${height} ${width} rounded border border-gray-200 ${className}`}
      style={gradientStyle}
      title={`${scheme} color scheme`}
    />
  )
}

interface ColorSchemeSelectItemProps {
  scheme: SurfaceColorScheme
  name: string
  isColorblindFriendly?: boolean
}

export function ColorSchemeSelectItem({
  scheme,
  name,
  isColorblindFriendly = false
}: ColorSchemeSelectItemProps) {
  return (
    <div className="flex items-center space-x-2">
      <ColorSchemePreview scheme={scheme} size="sm" />
      <span className="flex-1">{name}</span>
      {isColorblindFriendly && (
        <span className="text-xs font-medium text-green-600">♿</span>
      )}
    </div>
  )
}
