# Academic Verification System

This document describes the academic verification system implemented in the BOapp-frontend application.

## Overview

The academic verification system allows users to verify their academic status through their institutional email address. The system includes:

1. **Domain-based Pre-verification**: Checks if the email domain is from a known academic institution
2. **Email Verification**: Sends a verification email to the institutional email address
3. **Token Verification**: Verifies the token when the user clicks the link in the email

## Database Schema

The academic verification data is stored in the `academic_verifications` table with the following fields:

- `userId`: The user's ID (primary key)
- `fullName`: The user's full name
- `email`: The user's personal email
- `institutionalEmail`: The user's institutional email
- `institution`: The user's academic institution
- `role`: The user's role (student, researcher, professor, other)
- `verificationStatus`: The verification status (pending, domain_verified, approved, rejected)
- `verificationMethod`: The verification method (domain, email)
- `verificationTimestamp`: The timestamp of the verification
- `rejectionReason`: The reason for rejection (if applicable)
- `verificationToken`: The verification token
- `verificationTokenExpiry`: The expiry date of the verification token
- `createdAt`: The timestamp of the record creation
- `updatedAt`: The timestamp of the last update

## Verification Flow

1. User submits the academic verification form with their institutional email
2. System checks if the domain is from a known academic institution
3. System generates a verification token and sends a verification email
4. User clicks the verification link in the email
5. System verifies the token and updates the verification status
6. User is redirected to the academic survey

## Academic Domains

The system recognizes the following academic domains:

- Common academic domains: `.edu`, `.ac.uk`, `.edu.au`, `.ac.jp`, etc.
- University-related keywords: `university`, `college`, `institute`, `school`, etc.
- Company-specific domains: `synsilico.com`, `innosyn.com`

## Environment Variables

The following environment variables are required for the email verification system:

- `RESEND_API_KEY`: API key for the Resend email service
- `EMAIL_FROM`: The email address to send verification emails from
- `NEXT_PUBLIC_APP_URL`: The base URL of the application

## Implementation Details

### Email Service

The email service uses Resend to send verification emails. The email template includes:

- A personalized greeting
- A verification link
- Information about the expiry of the link
- A note about ignoring the email if the user didn't request verification

### Token Service

The token service generates and validates verification tokens:

- Tokens are 32-byte random hex strings
- Tokens expire after 24 hours
- The system checks if a token has expired before validating it

### Verification Actions

The system includes the following server actions:

- `submitAcademicVerificationAction`: Submits a verification request
- `verifyEmailTokenAction`: Verifies a token
- `resendVerificationEmailAction`: Resends a verification email
- `checkAcademicVerificationStatusAction`: Checks the verification status

## User Interface

The system includes the following UI components:

- Academic signup form
- Email verification page
- Verification status alerts
- Resend verification email button

## Security Considerations

- Tokens are randomly generated and have a limited lifespan
- The system validates the token against the database
- The system checks if the token has expired
- The system uses HTTPS for all communications

## Future Improvements

- Add support for document upload verification
- Implement admin interface for manual verification
- Add support for API integration with academic institutions
- Implement rate limiting for verification requests
