import {
  ErrorCode,
  ErrorCategory,
  ErrorSeverity,
  StructuredError,
  ErrorContext,
  ErrorResolution,
  QuickFix,
  generateErrorId
} from "./types"
import { getErrorMessage, getErrorResolution } from "./messages"

export class ErrorFactory {
  static createValidationError(
    code: ErrorCode,
    field: string,
    currentValue: any,
    expectedValue?: any,
    context?: ErrorContext
  ): StructuredError {
    return {
      id: generateErrorId(),
      code,
      category: ErrorCategory.VALIDATION,
      severity: ErrorSeverity.ERROR,
      field,
      fieldPath: field.split("."),
      currentValue,
      expectedValue,
      message: getErrorMessage(code, { field, currentValue, expectedValue }),
      resolution: getErrorResolution(code, {
        field,
        currentValue,
        expectedValue
      }),
      context,
      timestamp: new Date(),
      source: "frontend"
    }
  }

  static createParameterError(
    code: ErrorCode,
    parameterName: string,
    details: Record<string, any>,
    context?: ErrorContext
  ): StructuredError {
    return {
      id: generateErrorId(),
      code,
      category: ErrorCategory.PARAMETER,
      severity: ErrorSeverity.ERROR,
      field: parameterName,
      message: getErrorMessage(code, { parameterName, ...details }),
      resolution: getErrorResolution(code, { parameterName, ...details }),
      context,
      timestamp: new Date(),
      source: "frontend"
    }
  }

  static createConstraintError(
    code: ErrorCode,
    constraintName: string,
    details: Record<string, any>,
    context?: ErrorContext
  ): StructuredError {
    return {
      id: generateErrorId(),
      code,
      category: ErrorCategory.CONSTRAINT,
      severity: ErrorSeverity.ERROR,
      field: constraintName,
      message: getErrorMessage(code, { constraintName, ...details }),
      resolution: getErrorResolution(code, { constraintName, ...details }),
      context,
      timestamp: new Date(),
      source: "frontend"
    }
  }

  static createMeasurementError(
    code: ErrorCode,
    details: Record<string, any>,
    context?: ErrorContext
  ): StructuredError {
    return {
      id: generateErrorId(),
      code,
      category: ErrorCategory.MEASUREMENT,
      severity:
        code === ErrorCode.MEASUREMENT_ALL_FILTERED
          ? ErrorSeverity.ERROR
          : ErrorSeverity.WARNING,
      message: getErrorMessage(code, details),
      resolution: getErrorResolution(code, details),
      context,
      timestamp: new Date(),
      source: "backend"
    }
  }

  static createTargetError(
    code: ErrorCode,
    targetName: string,
    details: Record<string, any>,
    context?: ErrorContext
  ): StructuredError {
    return {
      id: generateErrorId(),
      code,
      category: ErrorCategory.TARGET,
      severity: ErrorSeverity.ERROR,
      field: targetName,
      message: getErrorMessage(code, { targetName, ...details }),
      resolution: getErrorResolution(code, { targetName, ...details }),
      context,
      timestamp: new Date(),
      source: "frontend"
    }
  }

  static createSystemError(
    code: ErrorCode,
    details: Record<string, any>,
    context?: ErrorContext
  ): StructuredError {
    return {
      id: generateErrorId(),
      code,
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.ERROR,
      message: getErrorMessage(code, details),
      resolution: getErrorResolution(code, details),
      context,
      timestamp: new Date(),
      source: "backend"
    }
  }

  static createNetworkError(
    code: ErrorCode,
    details: Record<string, any>,
    context?: ErrorContext
  ): StructuredError {
    return {
      id: generateErrorId(),
      code,
      category: ErrorCategory.NETWORK,
      severity: ErrorSeverity.ERROR,
      message: getErrorMessage(code, details),
      resolution: getErrorResolution(code, details),
      context,
      timestamp: new Date(),
      source: "frontend"
    }
  }

  static createConcurrencyError(
    code: ErrorCode,
    details: Record<string, any>,
    context?: ErrorContext
  ): StructuredError {
    return {
      id: generateErrorId(),
      code,
      category: ErrorCategory.CONCURRENCY,
      severity: ErrorSeverity.WARNING,
      message: getErrorMessage(code, details),
      resolution: getErrorResolution(code, details),
      context,
      timestamp: new Date(),
      source: "backend"
    }
  }

  // Helper method to create errors from backend responses
  static fromBackendError(
    backendError: any,
    context?: ErrorContext
  ): StructuredError {
    // Try to map backend error to structured error
    const errorCode = mapBackendErrorToCode(backendError)
    const category = getErrorCategoryFromCode(errorCode)

    return {
      id: generateErrorId(),
      code: errorCode,
      category,
      severity: ErrorSeverity.ERROR,
      message: backendError.message || "Unknown backend error",
      technicalMessage: backendError.detail || backendError.error,
      resolution: getErrorResolution(errorCode, backendError),
      context,
      timestamp: new Date(),
      source: "backend"
    }
  }
}

function mapBackendErrorToCode(backendError: any): ErrorCode {
  // Map common backend error patterns to error codes
  const message = (
    backendError.message ||
    backendError.error ||
    ""
  ).toLowerCase()

  if (message.includes("campaign") && message.includes("not found")) {
    return ErrorCode.SYSTEM_CAMPAIGN_NOT_FOUND
  }
  if (message.includes("constraint") && message.includes("validation")) {
    return ErrorCode.CONSTRAINT_SYNTAX_ERROR
  }
  if (message.includes("parameter") && message.includes("bounds")) {
    return ErrorCode.PARAMETER_BOUNDS_INVALID
  }
  if (message.includes("measurement") && message.includes("filtered")) {
    return ErrorCode.MEASUREMENT_ALL_FILTERED
  }
  if (message.includes("connection") || message.includes("network")) {
    return ErrorCode.NETWORK_CONNECTION_FAILED
  }

  // Default to system error
  return ErrorCode.SYSTEM_SAVE_FAILED
}

function getErrorCategoryFromCode(code: ErrorCode): ErrorCategory {
  const codeStr = code.toString()
  if (codeStr.startsWith("VALIDATION_")) return ErrorCategory.VALIDATION
  if (codeStr.startsWith("PARAMETER_")) return ErrorCategory.PARAMETER
  if (codeStr.startsWith("TARGET_")) return ErrorCategory.TARGET
  if (codeStr.startsWith("CONSTRAINT_")) return ErrorCategory.CONSTRAINT
  if (codeStr.startsWith("MEASUREMENT_")) return ErrorCategory.MEASUREMENT
  if (codeStr.startsWith("SYSTEM_")) return ErrorCategory.SYSTEM
  if (codeStr.startsWith("NETWORK_")) return ErrorCategory.NETWORK
  if (codeStr.startsWith("CONCURRENCY_")) return ErrorCategory.CONCURRENCY
  return ErrorCategory.SYSTEM
}
