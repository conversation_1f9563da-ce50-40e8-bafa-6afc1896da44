"use client"

import { useState } from "react"
import { MessageSquarePlus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { FeedbackDialog } from "@/components/feedback/feedback-dialog"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

interface FloatingFeedbackButtonProps {
  className?: string
}

export function FloatingFeedbackButton({
  className
}: FloatingFeedbackButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()

  return (
    <>
      <Button
        variant="default"
        size="sm"
        className={cn(
          "fixed bottom-4 right-4 z-50 flex items-center rounded-full px-4 py-2 shadow-md",
          className
        )}
        onClick={() => setIsOpen(true)}
      >
        <MessageSquarePlus className="mr-2 size-4" />
        Feedback
      </Button>

      <FeedbackDialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        initialMetadata={{ currentPath: pathname }}
      />
    </>
  )
}
