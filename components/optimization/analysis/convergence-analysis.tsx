"use client"

import { useState, useEffect } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import {
  AreaChart,
  TrendingUp,
  Target,
  AlertCircle,
  Info,
  HelpCircle,
  ExternalLink,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from "lucide-react"
import dynamic from "next/dynamic"
import Link from "next/link"
import { getTargetMode } from "@/lib/optimization-utils"

// Dynamically import Plotly to avoid SSR issues
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

interface ConvergenceAnalysisProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  targetName: string
  targetMode: "MAX" | "MIN" | "MULTI"
  isMultiTarget: boolean
  otherTargets: string[]
}

interface ConvergenceData {
  iterations: number[]
  bestValues: number[]
  currentValues: number[]
  improvements: number[]
  regret: number[]
  targetName: string
}

export function ConvergenceAnalysis({
  optimization,
  measurements,
  targetName,
  targetMode,
  isMultiTarget,
  otherTargets
}: ConvergenceAnalysisProps) {
  const [activeTab, setActiveTab] = useState("convergence")
  const [convergenceData, setConvergenceData] = useState<ConvergenceData[]>([])
  const [loading, setLoading] = useState(true)

  // Process measurements to create convergence data
  useEffect(() => {
    if (!measurements || measurements.length === 0) {
      setLoading(false)
      return
    }

    const processConvergenceData = () => {
      const targets = isMultiTarget
        ? [targetName, ...otherTargets]
        : [targetName]
      const processedData: ConvergenceData[] = []

      targets.forEach(target => {
        // Get the correct target mode for this specific target
        const currentTargetMode = getTargetMode(optimization, target)

        // Filter measurements that have this target
        const targetMeasurements = measurements.filter(
          m =>
            m.targetValues &&
            typeof m.targetValues === "object" &&
            target in m.targetValues
        )

        if (targetMeasurements.length === 0) return

        // Sort by creation date
        const sortedMeasurements = targetMeasurements.sort(
          (a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        )

        const iterations: number[] = []
        const currentValues: number[] = []
        const bestValues: number[] = []
        const improvements: number[] = []
        const regret: number[] = []

        let bestSoFar = currentTargetMode === "MAX" ? -Infinity : Infinity
        let theoreticalOptimum =
          currentTargetMode === "MAX"
            ? Math.max(
                ...sortedMeasurements.map(m =>
                  typeof m.targetValues === "object" &&
                  m.targetValues &&
                  target in m.targetValues
                    ? (m.targetValues as Record<string, number>)[target]
                    : 0
                )
              )
            : Math.min(
                ...sortedMeasurements.map(m =>
                  typeof m.targetValues === "object" &&
                  m.targetValues &&
                  target in m.targetValues
                    ? (m.targetValues as Record<string, number>)[target]
                    : 0
                )
              )

        sortedMeasurements.forEach((measurement, index) => {
          const value =
            typeof measurement.targetValues === "object" &&
            measurement.targetValues &&
            target in measurement.targetValues
              ? (measurement.targetValues as Record<string, number>)[target]
              : 0

          iterations.push(index + 1)
          currentValues.push(value)

          // Update best so far
          if (currentTargetMode === "MAX") {
            bestSoFar = Math.max(bestSoFar, value)
          } else {
            bestSoFar = Math.min(bestSoFar, value)
          }
          bestValues.push(bestSoFar)

          // Calculate improvement (difference between current best and previous best)
          const improvement =
            index === 0 ? 0 : bestSoFar - bestValues[index - 1]
          // For MIN mode, improvement is when the value decreases, so we need to flip the sign
          const actualImprovement =
            currentTargetMode === "MIN" ? -improvement : improvement
          improvements.push(actualImprovement)

          // Calculate regret (distance from theoretical optimum)
          const currentRegret =
            currentTargetMode === "MAX"
              ? theoreticalOptimum - bestSoFar
              : bestSoFar - theoreticalOptimum
          regret.push(Math.max(0, currentRegret))
        })

        processedData.push({
          iterations,
          bestValues,
          currentValues,
          improvements,
          regret,
          targetName: target
        })
      })

      setConvergenceData(processedData)
      setLoading(false)
    }

    processConvergenceData()
  }, [measurements, targetName, targetMode, isMultiTarget, otherTargets])

  const renderConvergencePlot = () => {
    if (convergenceData.length === 0) return null

    const traces = convergenceData.flatMap(data => [
      {
        x: data.iterations,
        y: data.bestValues,
        type: "scatter",
        mode: "lines+markers",
        name: `Best ${data.targetName}`,
        line: { color: "#3b82f6", width: 3 },
        marker: { size: 6 }
      },
      {
        x: data.iterations,
        y: data.currentValues,
        type: "scatter",
        mode: "markers",
        name: `Current ${data.targetName}`,
        marker: {
          color: "#94a3b8",
          size: 4,
          opacity: 0.6
        }
      }
    ])

    return (
      <Plot
        data={traces}
        layout={{
          title: "Convergence Progress",
          xaxis: {
            title: "Iteration",
            showgrid: true,
            gridcolor: "#f1f5f9"
          },
          yaxis: {
            title: "Target Value",
            showgrid: true,
            gridcolor: "#f1f5f9"
          },
          showlegend: true,
          legend: { x: 0, y: 1 },
          margin: { l: 60, r: 40, t: 60, b: 60 },
          plot_bgcolor: "white",
          paper_bgcolor: "white"
        }}
        config={{ responsive: true }}
        style={{ width: "100%", height: "400px" }}
      />
    )
  }

  const renderRegretPlot = () => {
    if (convergenceData.length === 0) return null

    const traces = convergenceData.map(data => ({
      x: data.iterations,
      y: data.regret,
      type: "scatter",
      mode: "lines+markers",
      name: `Regret ${data.targetName}`,
      line: { width: 2 },
      marker: { size: 5 }
    }))

    return (
      <Plot
        data={traces}
        layout={{
          title: "Regret Analysis",
          xaxis: {
            title: "Iteration",
            showgrid: true,
            gridcolor: "#f1f5f9"
          },
          yaxis: {
            title: "Simple Regret",
            showgrid: true,
            gridcolor: "#f1f5f9"
          },
          showlegend: true,
          legend: { x: 0, y: 1 },
          margin: { l: 60, r: 40, t: 60, b: 60 },
          plot_bgcolor: "white",
          paper_bgcolor: "white"
        }}
        config={{ responsive: true }}
        style={{ width: "100%", height: "400px" }}
      />
    )
  }

  const renderImprovementPlot = () => {
    if (convergenceData.length === 0) return null

    const traces = convergenceData.map(data => ({
      x: data.iterations,
      y: data.improvements,
      type: "bar",
      name: `Improvement ${data.targetName}`,
      marker: {
        color: data.improvements.map(imp => {
          if (imp > 0.001) return "#10b981" // Green for positive improvement
          if (imp < -0.001) return "#ef4444" // Red for negative (worse performance)
          return "#94a3b8" // Gray for no improvement (zero)
        }),
        opacity: 0.8
      }
    }))

    return (
      <Plot
        data={traces}
        layout={{
          title: "Iteration-wise Improvements",
          xaxis: {
            title: "Iteration",
            showgrid: true,
            gridcolor: "#f1f5f9"
          },
          yaxis: {
            title: "Improvement",
            showgrid: true,
            gridcolor: "#f1f5f9",
            zeroline: true,
            zerolinecolor: "#64748b",
            zerolinewidth: 2
          },
          showlegend: true,
          legend: { x: 0, y: 1 },
          margin: { l: 60, r: 40, t: 60, b: 60 },
          plot_bgcolor: "white",
          paper_bgcolor: "white"
        }}
        config={{ responsive: true }}
        style={{ width: "100%", height: "400px" }}
      />
    )
  }

  const getConvergenceStats = () => {
    if (convergenceData.length === 0) return null

    const stats = convergenceData.map(data => {
      // Get the correct target mode for this specific target
      const currentTargetMode = getTargetMode(optimization, data.targetName)

      const totalIterations = data.iterations.length

      // Calculate total improvement correctly based on target mode
      const rawImprovement =
        data.bestValues[totalIterations - 1] - data.bestValues[0]
      const totalImprovement =
        currentTargetMode === "MIN" ? -rawImprovement : rawImprovement

      const improvementRate = totalImprovement / totalIterations
      const lastImprovement = data.iterations.findLastIndex(
        i => data.improvements[i - 1] > 0
      )
      const plateauLength = totalIterations - lastImprovement - 1

      return {
        targetName: data.targetName,
        totalIterations,
        totalImprovement,
        improvementRate,
        plateauLength,
        currentBest: data.bestValues[totalIterations - 1],
        currentRegret: data.regret[totalIterations - 1]
      }
    })

    return stats
  }

  const getOptimizationRecommendation = () => {
    if (convergenceData.length === 0) return null

    const stats = getConvergenceStats()
    if (!stats) return null

    // Analyze across all targets
    const recommendations = stats.map(stat => {
      let recommendation = "continue"
      let confidence = "medium"
      let reasons = []

      // Decision logic for small datasets (≤25 samples)
      if (stat.plateauLength >= 7) {
        recommendation = "stop"
        confidence = "high"
        reasons.push(
          `No improvement for ${stat.plateauLength} consecutive experiments`
        )
      } else if (stat.plateauLength >= 5) {
        recommendation = "consider_stopping"
        confidence = "medium"
        reasons.push(`Plateau of ${stat.plateauLength} iterations detected`)
      }

      if (stat.totalIterations >= 20) {
        if (recommendation === "continue") {
          recommendation = "consider_stopping"
          confidence = "medium"
        }
        reasons.push(
          `Approaching small dataset limit (${stat.totalIterations}/25 experiments)`
        )
      }

      if (stat.currentRegret < 0.01 && stat.plateauLength >= 3) {
        recommendation = "stop"
        confidence = "high"
        reasons.push("Very low regret suggests optimum found")
      }

      if (stat.improvementRate > 0 && stat.plateauLength < 3) {
        recommendation = "continue"
        confidence = "high"
        reasons.push("Positive improvement rate with recent progress")
      }

      if (stat.totalImprovement <= 0 && stat.totalIterations >= 10) {
        recommendation = "stop"
        confidence = "high"
        reasons.push("No overall improvement after significant experimentation")
      }

      return {
        targetName: stat.targetName,
        recommendation,
        confidence,
        reasons,
        stats: stat
      }
    })

    // Overall recommendation (most conservative)
    const overallRec = recommendations.reduce((overall, current) => {
      if (current.recommendation === "stop") return "stop"
      if (current.recommendation === "consider_stopping" && overall !== "stop")
        return "consider_stopping"
      return overall
    }, "continue")

    return {
      overall: overallRec,
      targets: recommendations,
      primaryTarget: recommendations[0] // Main target recommendation
    }
  }

  const RecommendationInsight = () => {
    const recommendation = getOptimizationRecommendation()
    if (!recommendation) return null

    const { overall, primaryTarget } = recommendation

    const getInsightConfig = (rec: string) => {
      switch (rec) {
        case "stop":
          return {
            icon: Info,
            color: "text-blue-600",
            title: "Optimization Insight",
            message: "Your optimization appears to have converged",
            suggestion: "Current results may be optimal for your needs"
          }
        case "consider_stopping":
          return {
            icon: Clock,
            color: "text-amber-600",
            title: "Progress Update",
            message: "Limited recent improvements detected",
            suggestion: "Consider if current results meet your requirements"
          }
        case "continue":
          return {
            icon: TrendingUp,
            color: "text-green-600",
            title: "Progress Update",
            message: "Optimization is showing positive progress",
            suggestion: "Additional experiments may yield better results"
          }
        default:
          return {
            icon: Info,
            color: "text-gray-600",
            title: "Analyzing Progress",
            message: "Gathering optimization insights",
            suggestion: "Continue monitoring convergence patterns"
          }
      }
    }

    const config = getInsightConfig(overall)
    const Icon = config.icon

    return (
      <div className="bg-muted/30 border-muted rounded-lg border p-4">
        <div className="flex items-start gap-3">
          <Icon className={`mt-0.5 size-5 ${config.color}`} />
          <div className="flex-1 space-y-2">
            <div>
              <h4 className="text-sm font-medium">{config.title}</h4>
              <p className="text-muted-foreground text-sm">{config.message}</p>
            </div>

            <div className="text-muted-foreground text-xs">
              <span className="font-medium">Insight: </span>
              {config.suggestion}
              {primaryTarget.reasons.length > 0 && (
                <span className="ml-1">
                  ({primaryTarget.reasons[0].toLowerCase()})
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  const ConvergenceInfoDialog = () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="size-8 p-0">
          <HelpCircle className="size-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AreaChart className="size-5" />
            Convergence Analysis Guide
          </DialogTitle>
          <DialogDescription>
            Understanding how to interpret your optimization progress
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Overview */}
          <div>
            <h3 className="mb-3 text-lg font-semibold">
              What is Convergence Analysis?
            </h3>
            <p className="text-muted-foreground mb-3 text-sm">
              Convergence analysis shows how your optimization is progressing
              toward finding the best solution. It helps you understand if your
              experiments are improving your results and when you might want to
              stop running more experiments. This is especially important when
              you have a limited budget for experiments.
            </p>
          </div>

          {/* Three main plots */}
          <div className="grid gap-6">
            <div className="rounded-lg border p-4">
              <h4 className="mb-2 flex items-center gap-2 text-base font-semibold">
                <TrendingUp className="size-4 text-blue-500" />
                Convergence Plot
              </h4>
              <div className="space-y-2 text-sm">
                <p>
                  <strong>What it shows:</strong> The best value found so far at
                  each iteration (blue line) and individual measurements (gray
                  dots).
                </p>
                <p>
                  <strong>How to interpret:</strong>
                </p>
                <ul className="ml-4 list-inside list-disc space-y-1">
                  <li>
                    Upward trend (MAX mode) or downward trend (MIN mode)
                    indicates good progress
                  </li>
                  <li>
                    Flattening curve suggests convergence or need for more
                    exploration
                  </li>
                  <li>
                    Gray dots scattered around the line show measurement noise -
                    normal for real experiments
                  </li>
                </ul>
                <p>
                  <strong>For limited experiments:</strong> Expect some
                  variation, but look for overall improvement trend.
                </p>
              </div>
            </div>

            <div className="rounded-lg border p-4">
              <h4 className="mb-2 flex items-center gap-2 text-base font-semibold">
                <Target className="size-4 text-green-500" />
                Regret Analysis
              </h4>
              <div className="space-y-2 text-sm">
                <p>
                  <strong>What it shows:</strong> The gap between your current
                  best result and the true optimum.
                </p>
                <p>
                  <strong>How to interpret:</strong>
                </p>
                <ul className="ml-4 list-inside list-disc space-y-1">
                  <li>
                    Decreasing regret = getting closer to the optimal solution
                  </li>
                  <li>Flat regret = optimization has plateaued</li>
                  <li>Lower regret values are better</li>
                </ul>
                <p>
                  <strong>For limited experiments:</strong> Since we don't know
                  the perfect answer, we compare against your best result so
                  far.
                </p>
              </div>
            </div>

            <div className="rounded-lg border p-4">
              <h4 className="mb-2 flex items-center gap-2 text-base font-semibold">
                <AlertCircle className="size-4 text-orange-500" />
                Improvement Analysis
              </h4>
              <div className="space-y-2 text-sm">
                <p>
                  <strong>What it shows:</strong> How much improvement was
                  gained at each iteration.
                </p>
                <p>
                  <strong>How to interpret:</strong>
                </p>
                <ul className="ml-4 list-inside list-disc space-y-1">
                  <li>Green bars = iterations that found better solutions</li>
                  <li>Gray bars = no improvement found (same performance)</li>
                  <li>Red bars = performance got worse</li>
                  <li>
                    Many consecutive gray/red bars = potential convergence
                  </li>
                </ul>
                <p>
                  <strong>For limited experiments:</strong> Don't expect
                  improvement every time - the system balances trying new areas
                  vs refining known good areas.
                </p>
              </div>
            </div>
          </div>

          {/* Statistics explanation */}
          <div>
            <h3 className="mb-3 text-lg font-semibold">
              Understanding the Statistics
            </h3>
            <div className="grid gap-4">
              <div className="rounded-lg border p-3">
                <h5 className="mb-2 font-medium">Key Metrics:</h5>
                <ul className="space-y-1 text-sm">
                  <li>
                    <strong>Iterations:</strong> Total number of experiments
                    conducted
                  </li>
                  <li>
                    <strong>Current Best:</strong> Best value found so far
                  </li>
                  <li>
                    <strong>Total Improvement:</strong> Difference between first
                    and best measurement
                  </li>
                  <li>
                    <strong>Plateau Length:</strong> Number of iterations since
                    last improvement
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Limited experiments guidance */}
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <h3 className="mb-3 text-lg font-semibold text-blue-800">
              Working with Limited Experiments (≤25 runs)
            </h3>
            <div className="space-y-2 text-sm text-blue-700">
              <p>
                <strong>What to expect:</strong>
              </p>
              <ul className="ml-4 list-inside list-disc space-y-1">
                <li>Results may vary more between experiments</li>
                <li>Progress might appear slower - this is normal</li>
                <li>Each experiment provides valuable information</li>
                <li>Patterns become clearer after 10+ experiments</li>
              </ul>
              <p>
                <strong>When to consider stopping:</strong>
              </p>
              <ul className="ml-4 list-inside list-disc space-y-1">
                <li>No improvement for 5-7 consecutive experiments</li>
                <li>Very small improvements that don't justify the cost</li>
                <li>You've reached your experimental budget</li>
              </ul>
              <p>
                <strong>Warning signs:</strong>
              </p>
              <ul className="ml-4 list-inside list-disc space-y-1">
                <li>No improvement after 10+ experiments</li>
                <li>Highly inconsistent results (check experimental setup)</li>
                <li>Performance getting worse over time</li>
              </ul>
            </div>
          </div>

          {/* Practical tips */}
          <div>
            <h3 className="mb-3 text-lg font-semibold">
              Practical Tips for Your Optimization
            </h3>
            <div className="space-y-3 text-sm">
              <div className="border-l-4 border-blue-500 pl-4">
                <p>
                  <strong>Getting Started:</strong>
                </p>
                <ul className="ml-4 list-inside list-disc space-y-1">
                  <li>
                    Run at least 5-10 experiments before drawing conclusions
                  </li>
                  <li>Look for overall trends rather than individual points</li>
                  <li>
                    Don't worry if early results seem random - this is normal
                  </li>
                </ul>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <p>
                  <strong>Making Decisions:</strong>
                </p>
                <ul className="ml-4 list-inside list-disc space-y-1">
                  <li>If you see steady improvement, continue optimizing</li>
                  <li>
                    If no improvement for several iterations, consider stopping
                  </li>
                  <li>
                    Use these plots to justify your experimental decisions
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Link to detailed documentation */}
          <div className="border-t pt-4">
            <Link
              href="/dashboard/help/convergence-analysis"
              className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              <ExternalLink className="size-4" />
              View Complete Convergence Analysis Guide
            </Link>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center text-lg">
                <AreaChart className="text-primary mr-2 size-5" />
                Convergence Analysis
              </CardTitle>
              <CardDescription>Loading convergence data...</CardDescription>
            </div>
            <ConvergenceInfoDialog />
          </div>
        </CardHeader>
      </Card>
    )
  }

  if (measurements.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center text-lg">
                <AreaChart className="text-primary mr-2 size-5" />
                Convergence Analysis
              </CardTitle>
              <CardDescription>
                Analyze optimization progress and convergence behavior
              </CardDescription>
            </div>
            <ConvergenceInfoDialog />
          </div>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="size-4" />
            <AlertTitle>No Data Available</AlertTitle>
            <AlertDescription>
              No measurements found. Add some measurements to see convergence
              analysis.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  const stats = getConvergenceStats()

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center text-lg">
              <AreaChart className="text-primary mr-2 size-5" />
              Convergence Analysis
            </CardTitle>
            <CardDescription>
              Analyze optimization progress and convergence behavior for small
              datasets
            </CardDescription>
          </div>
          <ConvergenceInfoDialog />
        </div>
      </CardHeader>
      <CardContent>
        {/* Convergence Statistics */}
        {stats && (
          <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {stats.map(stat => (
              <div key={stat.targetName} className="rounded-lg border p-4">
                <h4 className="text-muted-foreground mb-2 text-sm font-medium">
                  {stat.targetName}
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Iterations:</span>
                    <Badge variant="outline">{stat.totalIterations}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Current Best:</span>
                    <Badge variant="secondary">
                      {stat.currentBest.toFixed(3)}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Total Improvement:</span>
                    <Badge
                      variant={
                        stat.totalImprovement > 0 ? "default" : "destructive"
                      }
                    >
                      {stat.totalImprovement.toFixed(3)}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Plateau Length:</span>
                    <div className="flex items-center gap-1">
                      <Badge
                        variant={
                          stat.plateauLength > 5 ? "destructive" : "outline"
                        }
                      >
                        {stat.plateauLength} iterations
                      </Badge>
                      {stat.plateauLength >= 7 && (
                        <div
                          className="size-2 rounded-full bg-blue-500"
                          title="Consider reviewing progress"
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Subtle Recommendation Insight */}
        {stats && (
          <div className="mb-6">
            <RecommendationInsight />
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 grid grid-cols-3">
            <TabsTrigger value="convergence">Convergence</TabsTrigger>
            <TabsTrigger value="regret">Regret Analysis</TabsTrigger>
            <TabsTrigger value="improvements">Improvements</TabsTrigger>
          </TabsList>

          <TabsContent value="convergence" className="space-y-4">
            <div className="space-y-4">
              {renderConvergencePlot()}
              <Alert>
                <TrendingUp className="size-4" />
                <AlertTitle>Convergence Insights</AlertTitle>
                <AlertDescription>
                  The blue line shows the best value found so far at each
                  iteration. Gray dots show individual measurements. For small
                  datasets (≤25 samples), expect some noise but look for overall
                  upward (MAX) or downward (MIN) trends.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>

          <TabsContent value="regret" className="space-y-4">
            <div className="space-y-4">
              {renderRegretPlot()}
              <Alert>
                <Target className="size-4" />
                <AlertTitle>Regret Analysis</AlertTitle>
                <AlertDescription>
                  Simple regret measures the gap between the current best and
                  the true optimum. Decreasing regret indicates good
                  convergence. With small datasets, some fluctuation is normal.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>

          <TabsContent value="improvements" className="space-y-4">
            <div className="space-y-4">
              {renderImprovementPlot()}
              <Alert>
                <AlertCircle className="size-4" />
                <AlertTitle>Improvement Analysis</AlertTitle>
                <AlertDescription>
                  Green bars show iterations with improvements, red bars show no
                  improvement. Long periods without improvement may indicate
                  convergence or need for exploration.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
