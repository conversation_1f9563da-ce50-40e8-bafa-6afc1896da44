"use client"

import { useState, useCallback, useRef } from "react"
import { incrementalUpdateService } from "./service"
import { IncrementalUpdateAnalyzer } from "./analyzer"
import {
  UpdateResult,
  UpdateContext,
  IncrementalUpdateStrategy,
  UpdateValidation,
  UpdatePerformanceMetrics
} from "./types"

export interface UseIncrementalUpdatesOptions {
  optimizationId: string
  onUpdateStart?: () => void
  onUpdateProgress?: (progress: number) => void
  onUpdateComplete?: (result: UpdateResult) => void
  onUpdateError?: (error: Error) => void
}

export function useIncrementalUpdates(options: UseIncrementalUpdatesOptions) {
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateProgress, setUpdateProgress] = useState(0)
  const [lastUpdateResult, setLastUpdateResult] = useState<UpdateResult | null>(
    null
  )
  const [updateStrategy, setUpdateStrategy] =
    useState<IncrementalUpdateStrategy | null>(null)
  const [validationResult, setValidationResult] =
    useState<UpdateValidation | null>(null)
  const [performanceMetrics, setPerformanceMetrics] = useState<
    UpdatePerformanceMetrics[]
  >([])

  const abortControllerRef = useRef<AbortController | null>(null)

  /**
   * Analyze configuration changes and determine update strategy
   */
  const analyzeChanges = useCallback(
    async (
      currentConfig: any,
      targetConfig: any,
      context: UpdateContext
    ): Promise<IncrementalUpdateStrategy> => {
      try {
        const strategy = IncrementalUpdateAnalyzer.analyzeConfigurationChanges(
          currentConfig,
          targetConfig,
          context
        )

        setUpdateStrategy(strategy)

        // Validate the operations
        const allOperations = [
          ...strategy.simpleUpdates,
          ...strategy.moderateUpdates,
          ...strategy.complexUpdates
        ]

        const validation = IncrementalUpdateAnalyzer.validateUpdateOperations(
          allOperations,
          context
        )

        setValidationResult(validation)

        return strategy
      } catch (error) {
        console.error("Failed to analyze configuration changes:", error)
        throw error
      }
    },
    []
  )

  /**
   * Execute incremental update
   */
  const executeUpdate = useCallback(
    async (
      currentConfig: any,
      targetConfig: any,
      context: UpdateContext
    ): Promise<UpdateResult> => {
      if (isUpdating) {
        throw new Error("Update already in progress")
      }

      setIsUpdating(true)
      setUpdateProgress(0)
      setLastUpdateResult(null)

      // Create abort controller for cancellation
      abortControllerRef.current = new AbortController()

      try {
        options.onUpdateStart?.()

        // Simulate progress updates
        const progressInterval = setInterval(() => {
          setUpdateProgress(prev => Math.min(prev + 10, 90))
        }, 200)

        const result = await incrementalUpdateService.executeIncrementalUpdate(
          options.optimizationId,
          currentConfig,
          targetConfig,
          context
        )

        clearInterval(progressInterval)
        setUpdateProgress(100)
        setLastUpdateResult(result)

        // Get performance metrics
        const metrics = incrementalUpdateService.getPerformanceMetrics()
        setPerformanceMetrics(metrics)

        options.onUpdateComplete?.(result)

        return result
      } catch (error) {
        const errorObj =
          error instanceof Error ? error : new Error("Unknown error")
        options.onUpdateError?.(errorObj)
        throw errorObj
      } finally {
        setIsUpdating(false)
        setUpdateProgress(0)
        abortControllerRef.current = null
      }
    },
    [isUpdating, options]
  )

  /**
   * Preview update strategy without executing
   */
  const previewUpdate = useCallback(
    async (
      currentConfig: any,
      targetConfig: any,
      context: UpdateContext
    ): Promise<{
      strategy: IncrementalUpdateStrategy
      validation: UpdateValidation
      estimatedDuration: number
      canOptimize: boolean
    }> => {
      const strategy = await analyzeChanges(
        currentConfig,
        targetConfig,
        context
      )

      const allOperations = [
        ...strategy.simpleUpdates,
        ...strategy.moderateUpdates,
        ...strategy.complexUpdates
      ]

      const validation = IncrementalUpdateAnalyzer.validateUpdateOperations(
        allOperations,
        context
      )

      const estimatedDuration = strategy.executionPlan.reduce(
        (total, phase) => total + phase.estimatedDuration,
        0
      )

      return {
        strategy,
        validation,
        estimatedDuration,
        canOptimize: strategy.recommendations.canOptimize
      }
    },
    [analyzeChanges]
  )

  /**
   * Cancel ongoing update
   */
  const cancelUpdate = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      setIsUpdating(false)
      setUpdateProgress(0)
    }
  }, [])

  /**
   * Get update recommendations
   */
  const getUpdateRecommendations = useCallback(
    (
      strategy: IncrementalUpdateStrategy
    ): {
      canOptimize: boolean
      timeSavingPotential: number
      riskLevel: "low" | "medium" | "high"
      recommendations: string[]
    } => {
      const hasComplexUpdates = strategy.complexUpdates.length > 0
      const hasModerateUpdates = strategy.moderateUpdates.length > 0

      let riskLevel: "low" | "medium" | "high" = "low"
      if (hasComplexUpdates) {
        riskLevel = "high"
      } else if (hasModerateUpdates) {
        riskLevel = "medium"
      }

      const recommendations: string[] = []

      if (strategy.recommendations.canOptimize) {
        recommendations.push("Configuration can be updated incrementally")
        recommendations.push(
          `Estimated time saving: ${strategy.recommendations.potentialTimeSaving}ms`
        )
      }

      if (hasComplexUpdates) {
        recommendations.push("Some changes require full campaign recreation")
        recommendations.push("Consider batching complex changes together")
      }

      if (hasModerateUpdates && !hasComplexUpdates) {
        recommendations.push(
          "Changes require model retraining but not recreation"
        )
        recommendations.push("This is more efficient than full recreation")
      }

      if (strategy.simpleUpdates.length > 0) {
        recommendations.push(
          `${strategy.simpleUpdates.length} changes can be applied instantly`
        )
      }

      return {
        canOptimize: strategy.recommendations.canOptimize,
        timeSavingPotential: strategy.recommendations.potentialTimeSaving,
        riskLevel,
        recommendations
      }
    },
    []
  )

  /**
   * Get update statistics
   */
  const getUpdateStatistics = useCallback(() => {
    if (!updateStrategy) return null

    const totalOperations =
      updateStrategy.simpleUpdates.length +
      updateStrategy.moderateUpdates.length +
      updateStrategy.complexUpdates.length

    const estimatedDuration = updateStrategy.executionPlan.reduce(
      (total, phase) => total + phase.estimatedDuration,
      0
    )

    return {
      totalOperations,
      simpleOperations: updateStrategy.simpleUpdates.length,
      moderateOperations: updateStrategy.moderateUpdates.length,
      complexOperations: updateStrategy.complexUpdates.length,
      estimatedDuration,
      canOptimize: updateStrategy.recommendations.canOptimize,
      optimizationStrategy: updateStrategy.recommendations.optimizationStrategy
    }
  }, [updateStrategy])

  /**
   * Check if configuration changes are valid
   */
  const validateChanges = useCallback(
    (
      currentConfig: any,
      targetConfig: any,
      context: UpdateContext
    ): boolean => {
      if (!validationResult) return true
      return validationResult.isValid
    },
    [validationResult]
  )

  /**
   * Get validation errors and warnings
   */
  const getValidationIssues = useCallback(() => {
    if (!validationResult) return { errors: [], warnings: [] }

    return {
      errors: validationResult.errors.map(e => ({
        field: e.operation.field,
        message: e.message,
        severity: e.severity
      })),
      warnings: validationResult.warnings.map(w => ({
        field: w.operation.field,
        message: w.message,
        impact: w.impact
      }))
    }
  }, [validationResult])

  return {
    // State
    isUpdating,
    updateProgress,
    lastUpdateResult,
    updateStrategy,
    validationResult,
    performanceMetrics,

    // Actions
    analyzeChanges,
    executeUpdate,
    previewUpdate,
    cancelUpdate,

    // Utilities
    getUpdateRecommendations,
    getUpdateStatistics,
    validateChanges,
    getValidationIssues
  }
}
