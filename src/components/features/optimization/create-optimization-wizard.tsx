"use client"

import { useState, useMemo } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm, Controller } from "react-hook-form"
import * as z from "zod"
import {
  Trash,
  Plus,
  Beaker,
  Target,
  ClipboardCheck,
  ArrowLeft,
  ArrowRight,
  Scale,
  Settings,
  CheckCircle,
  Brain
} from "lucide-react"
import { createOptimizationWorkflowAction } from "@/actions/optimization-workflow-actions"
import { toast } from "@/components/ui/use-toast"
import { OptimizationConfig, Parameter, TargetConfig } from "@/types"
import { ProgressStepper } from "@/components/ui/progress-stepper"
import { AdvancedOptionsSection } from "./advanced-options-section"
import { CentralizedConstraintBuilder } from "@/components/constraints/centralized-constraint-builder"
import { AcquisitionFunctionControls } from "./acquisition-function-controls"
import { Constraint, Parameter as ConstraintParameter } from "@/lib/constraints"

// Helper function to get default acquisition function based on objective type
const getDefaultAcquisitionFunction = (objectiveType: string) => {
  if (objectiveType === "MULTI_PARETO") {
    return "qNoisyExpectedHypervolumeImprovement"
  }
  return "qExpectedImprovement"
}

// Helper function to generate smart reference points based on target bounds and modes
const generateSmartReferencePoints = (multiTargets: any[]) => {
  if (!multiTargets || multiTargets.length === 0) {
    return [0.0, 0.0] // Fallback for 2 objectives
  }

  return multiTargets.map((target: any) => {
    const { lowerBound, upperBound, mode } = target

    // Handle cases where bounds are not available
    if (lowerBound === undefined || upperBound === undefined) {
      // Use conservative defaults based on mode
      if (mode === "MAX") {
        return 0.0 // Conservative reference point for maximization
      } else {
        return 100.0 // Conservative reference point for minimization
      }
    }

    const range = upperBound - lowerBound

    if (mode === "MAX") {
      // For maximization, reference point should be below lower bound
      // This represents "worst acceptable performance"
      if (lowerBound <= 0) {
        // If lower bound is 0 or negative, go further below
        return lowerBound - Math.max(range * 0.1, 1)
      } else {
        // If lower bound is positive, use a percentage below
        return lowerBound * 0.9
      }
    } else {
      // For minimization, reference point should be above upper bound
      // This represents "worst acceptable performance"
      if (upperBound >= 0) {
        // If upper bound is 0 or positive, go further above
        return upperBound + Math.max(range * 0.1, 1)
      } else {
        // If upper bound is negative, use a percentage above (less negative)
        return upperBound * 0.9
      }
    }
  })
}

// Schema for the form validation
const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().optional(),
  objectiveType: z.enum(["SINGLE", "MULTI_DESIRABILITY", "MULTI_PARETO"]),
  singleTarget: z
    .object({
      name: z.string().min(1, "Target name is required"),
      mode: z.enum(["MAX", "MIN"]),
      lowerBound: z.number().optional(),
      upperBound: z.number().optional()
    })
    .refine(
      data => {
        // If both bounds are provided, lower must be less than upper
        if (data.lowerBound !== undefined && data.upperBound !== undefined) {
          return data.lowerBound < data.upperBound
        }
        return true
      },
      {
        message: "Upper bound must be greater than lower bound",
        path: ["upperBound"]
      }
    ),
  multiTargets: z
    .array(
      z
        .object({
          name: z.string().min(1, "Target name is required"),
          mode: z.enum(["MAX", "MIN"]),
          weight: z
            .number()
            .min(1, "Weight percentage must be at least 1")
            .max(100, "Weight percentage cannot exceed 100"),
          lowerBound: z.number(),
          upperBound: z.number()
        })
        .refine(data => data.lowerBound < data.upperBound, {
          message: "Upper bound must be greater than lower bound",
          path: ["upperBound"]
        })
    )
    .min(
      2,
      "At least two targets are required for multi-objective optimization"
    ),
  acquisitionFunction: z
    .object({
      type: z.enum([
        "qExpectedImprovement",
        "qProbabilityOfImprovement",
        "qUpperConfidenceBound",
        "qNoisyExpectedHypervolumeImprovement",
        "qLogNoisyExpectedHypervolumeImprovement",
        "qLogNParEGO"
      ]),
      beta: z.number().min(0.1).optional(),
      ref_point: z.array(z.number()).optional(),
      weights: z.array(z.number()).optional(),
      rho: z.number().optional(),
      prune_baseline: z.boolean().optional()
    })
    .refine(
      data => {
        // Beta is required for UCB, not allowed for others
        if (data.type === "qUpperConfidenceBound") {
          return data.beta !== undefined && data.beta >= 0.1
        } else if (
          data.type === "qExpectedImprovement" ||
          data.type === "qProbabilityOfImprovement"
        ) {
          return data.beta === undefined
        }
        // For multi-objective functions, beta should not be present
        return data.beta === undefined
      },
      {
        message:
          "Beta parameter is only required for Upper Confidence Bound acquisition function",
        path: ["beta"]
      }
    ),
  parameters: z
    .array(
      z
        .object({
          name: z.string().min(1, "Parameter name is required"),
          type: z.enum([
            "NumericalDiscrete",
            "NumericalContinuous",
            "CategoricalParameter"
          ]),
          values: z.string().optional(),
          encoding: z.enum(["OHE", "LE"]).optional(),
          tolerance: z.string().optional(),
          bounds: z.string().optional()
        })
        .refine(
          data => {
            // For NumericalDiscrete and CategoricalParameter, values is required
            if (
              (data.type === "NumericalDiscrete" ||
                data.type === "CategoricalParameter") &&
              !data.values
            ) {
              return false
            }
            // For NumericalContinuous, bounds is required
            if (data.type === "NumericalContinuous" && !data.bounds) {
              return false
            }
            return true
          },
          {
            message: "Required field missing for this parameter type",
            path: ["type"] // This will highlight the type field when validation fails
          }
        )
    )
    .min(1, "At least one parameter is required"),
  constraints: z.array(z.any()).optional() // Constraints are optional
})

type FormValues = z.infer<typeof formSchema>

interface CreateOptimizationWizardProps {
  demoMode?: boolean
  demoData?: any
  onStepChange?: (step: number) => void
  onDemoComplete?: () => void
}

export function CreateOptimizationWizard({
  demoMode = false,
  demoData,
  onStepChange,
  onDemoComplete
}: CreateOptimizationWizardProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeStep, setActiveStep] = useState(0)
  const [selectedAcquisitionFunction, setSelectedAcquisitionFunction] =
    useState(() => {
      // Initialize with smart default based on demo data or default objective type
      const initialObjectiveType = demoData?.objectiveType || "SINGLE"
      return getDefaultAcquisitionFunction(initialObjectiveType)
    })

  // Define the steps for the wizard
  const steps = ["Basic Info", "Target", "Parameters", "Advanced", "Review"]

  // Use demo data if provided, otherwise use defaults
  const defaultValues = useMemo(() => {
    const objType = demoData?.objectiveType || "SINGLE"
    const defaultAcqFunction = getDefaultAcquisitionFunction(objType)

    return demoData
      ? {
          ...demoData,
          parameters:
            demoData.parameters && demoData.parameters.length > 0
              ? demoData.parameters
              : [
                  {
                    name: "",
                    type: "NumericalDiscrete",
                    values: "",
                    encoding: "OHE",
                    tolerance: "",
                    bounds: ""
                  }
                ],
          acquisitionFunction: {
            type: demoData.acquisitionFunction?.type || defaultAcqFunction
            // Note: beta parameter is only added for UCB acquisition functions
          },
          name: demoData.name || "",
          description: demoData.description || "",
          objectiveType: objType,
          singleTarget: demoData.singleTarget || {
            name: "Target",
            mode: "MAX",
            lowerBound: undefined,
            upperBound: undefined
          },
          multiTargets: demoData.multiTargets || [
            {
              name: "Target 1",
              mode: "MAX",
              weight: 50,
              lowerBound: 0,
              upperBound: 100
            },
            {
              name: "Target 2",
              mode: "MAX",
              weight: 50,
              lowerBound: 0,
              upperBound: 100
            }
          ],
          constraints: demoData.constraints || []
        }
      : {
          name: "",
          description: "",
          objectiveType: "SINGLE",
          singleTarget: {
            name: "Target",
            mode: "MAX",
            lowerBound: undefined,
            upperBound: undefined
          },
          multiTargets: [
            {
              name: "Target 1",
              mode: "MAX",
              weight: 50,
              lowerBound: 0,
              upperBound: 100
            },
            {
              name: "Target 2",
              mode: "MAX",
              weight: 50,
              lowerBound: 0,
              upperBound: 100
            }
          ],
          acquisitionFunction: {
            type: "qExpectedImprovement"
            // Note: beta parameter is only added for UCB acquisition functions
          },
          parameters: [
            {
              name: "",
              type: "NumericalDiscrete",
              values: "",
              encoding: "OHE",
              tolerance: "",
              bounds: ""
            }
          ],
          constraints: []
        }
  }, [demoData])

  // Initialize the form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues
  })

  // Function to add a new target to the multi-target list
  const addTarget = () => {
    const multiTargets = form.getValues("multiTargets") || []
    // Calculate an equal distribution of weights
    const newWeight = Math.floor(100 / (multiTargets.length + 1))

    // Update existing targets to maintain total of 100%
    const updatedTargets = multiTargets.map(target => ({
      ...target,
      weight: newWeight
    }))

    form.setValue("multiTargets", [
      ...updatedTargets,
      {
        name: `Target ${multiTargets.length + 1}`,
        mode: "MAX",
        weight: newWeight,
        lowerBound: 0,
        upperBound: 100
      }
    ])
  }

  // Function to remove a target from the multi-target list
  const removeTarget = (index: number) => {
    const multiTargets = form.getValues("multiTargets") || []
    if (multiTargets.length > 2) {
      multiTargets.splice(index, 1)

      // Redistribute weights evenly among remaining targets
      const newWeight = Math.floor(100 / multiTargets.length)
      const updatedTargets = multiTargets.map(target => ({
        ...target,
        weight: newWeight
      }))

      form.setValue("multiTargets", [...updatedTargets])
    } else {
      toast({
        title: "Cannot remove target",
        description:
          "At least two targets are required for multi-objective optimization",
        variant: "destructive"
      })
    }
  }

  // Function to handle weight changes and ensure they sum to 100%
  const handleWeightChange = (index: number, newValue: number) => {
    const multiTargets = form.getValues("multiTargets") || []

    // Update the weight for the current target
    multiTargets[index].weight = Math.max(1, Math.min(100, newValue))

    form.setValue(`multiTargets.${index}.weight`, multiTargets[index].weight)
  }

  // Function to handle moving to the next step
  const handleNext = async () => {
    // Validate the current step before proceeding
    let isValid = true

    if (activeStep === 0) {
      // Validate basic info
      const nameValid = await form.trigger("name")
      if (!nameValid) {
        isValid = false
      }
    } else if (activeStep === 1) {
      // Validate target based on objective type
      const objectiveType = form.getValues("objectiveType")

      if (objectiveType === "SINGLE") {
        const singleTargetValid = await form.trigger("singleTarget")
        if (!singleTargetValid) {
          isValid = false
        }
      } else if (
        objectiveType === "MULTI_DESIRABILITY" ||
        objectiveType === "MULTI_PARETO"
      ) {
        const multiTargetsValid = await form.trigger("multiTargets")
        if (!multiTargetsValid) {
          isValid = false
        }
      }
    } else if (activeStep === 2) {
      // Validate parameters
      const parametersValid = await form.trigger("parameters")
      if (!parametersValid) {
        isValid = false
      }
    } else if (activeStep === 3) {
      // Validate constraints (optional, so always valid)
      // Constraints are optional, so this step is always valid
      isValid = true
    }

    if (isValid && activeStep < steps.length - 1) {
      const newStep = activeStep + 1
      setActiveStep(newStep)
      onStepChange?.(newStep)
    }
  }

  // Function to handle moving to the previous step
  const handlePrevious = () => {
    if (activeStep > 0) {
      const newStep = activeStep - 1
      setActiveStep(newStep)
      onStepChange?.(newStep)
    }
  }

  // Function to add a new parameter to the form
  const addParameter = () => {
    const parameters = form.getValues("parameters")
    form.setValue("parameters", [
      ...parameters,
      {
        name: "",
        type: "NumericalDiscrete",
        values: "",
        encoding: "OHE",
        tolerance: "",
        bounds: ""
      }
    ])
  }

  // Function to remove a parameter from the form
  const removeParameter = (index: number) => {
    const parameters = form.getValues("parameters")
    if (parameters.length > 1) {
      parameters.splice(index, 1)
      form.setValue("parameters", [...parameters])
    } else {
      toast({
        title: "Cannot remove parameter",
        description: "At least one parameter is required",
        variant: "destructive"
      })
    }
  }

  // Parse values from the form data
  const parseValues = (
    valuesStr: string | undefined,
    type: string
  ): number[] | string[] => {
    if (!valuesStr || valuesStr.trim() === "") {
      // Return default values if none provided
      if (type === "NumericalDiscrete") {
        return [1, 2, 3] // Default numerical values
      }
      return ["A", "B", "C"] // Default categorical values
    }

    if (type === "NumericalDiscrete" || type === "NumericalContinuous") {
      return valuesStr.split(",").map(v => {
        const num = Number(v.trim())
        return isNaN(num) ? 0 : num // Replace NaN with 0
      })
    }
    return valuesStr
      .split(",")
      .map(v => v.trim())
      .filter(v => v.length > 0) // Remove empty strings
  }

  // Parse bounds from the form data
  const parseBounds = (boundsStr: string): [number, number] => {
    const parts = boundsStr.split(",").map(v => Number(v.trim()))
    return [parts[0] || 0, parts[1] || 100]
  }

  // Function to convert form values to the API format
  const prepareOptimizationConfig = (data: FormValues): OptimizationConfig => {
    const parameters: Parameter[] = data.parameters.map(param => {
      const base = {
        name: param.name,
        type: param.type
      } as Parameter

      if (param.type === "NumericalDiscrete") {
        return {
          ...base,
          type: "NumericalDiscrete" as const,
          values: parseValues(param.values, param.type) as number[],
          tolerance: param.tolerance ? parseFloat(param.tolerance) : undefined
        }
      } else if (param.type === "NumericalContinuous") {
        return {
          ...base,
          type: "NumericalContinuous" as const,
          bounds: parseBounds(param.bounds || "0,100")
        }
      } else if (param.type === "CategoricalParameter") {
        return {
          ...base,
          type: "CategoricalParameter" as const,
          values: parseValues(param.values, param.type) as string[],
          encoding: param.encoding || "OHE"
        }
      }

      return base
    })

    // Create the appropriate objective configuration based on the selected type
    let objective_config: any

    // Prepare acquisition function configuration
    const acquisitionConfig: any = {
      type: data.acquisitionFunction.type
    }

    // Handle parameters based on acquisition function type
    if (data.acquisitionFunction.type === "qUpperConfidenceBound") {
      acquisitionConfig.beta = data.acquisitionFunction.beta || 0.2 // Use BayBE's default
    } else if (
      data.acquisitionFunction.type ===
        "qNoisyExpectedHypervolumeImprovement" ||
      data.acquisitionFunction.type ===
        "qLogNoisyExpectedHypervolumeImprovement"
    ) {
      // Add reference point for hypervolume-based acquisition functions
      if (data.acquisitionFunction.ref_point) {
        acquisitionConfig.ref_point = data.acquisitionFunction.ref_point
      } else if (data.objectiveType === "MULTI_PARETO") {
        // Auto-generate reference points when not provided for Pareto objectives
        const smartRefPoints = generateSmartReferencePoints(data.multiTargets)
        acquisitionConfig.ref_point = smartRefPoints
        console.log(
          "Auto-generated reference points for Pareto optimization:",
          smartRefPoints
        )
      }
      // Add prune_baseline parameter for qNEHVI/qLogNEHVI
      if (data.acquisitionFunction.prune_baseline !== undefined) {
        acquisitionConfig.prune_baseline =
          data.acquisitionFunction.prune_baseline
      }
    } else if (data.acquisitionFunction.type === "qLogNParEGO") {
      // Add weights for qNParEGO
      if (data.acquisitionFunction.weights) {
        acquisitionConfig.weights = data.acquisitionFunction.weights
      } else if (data.objectiveType === "MULTI_PARETO") {
        // Auto-generate equal weights when not provided for Pareto objectives
        const numObjectives = data.multiTargets.length
        const equalWeights = Array(numObjectives).fill(1.0 / numObjectives)
        acquisitionConfig.weights = equalWeights
        console.log(
          "Auto-generated equal weights for qLogNParEGO:",
          equalWeights
        )
      }
      // Add rho parameter for augmented Chebyshev scalarization
      if (data.acquisitionFunction.rho !== undefined) {
        acquisitionConfig.rho = data.acquisitionFunction.rho
      } else {
        // Use BayBE's default rho value (0.05) when not specified
        acquisitionConfig.rho = 0.05
        console.log("Using default rho value for qLogNParEGO:", 0.05)
      }
    }

    if (data.objectiveType === "SINGLE") {
      // For SingleTarget, we need to pass the target configuration with optional bounds
      const targetConfig: any = {
        name: data.singleTarget.name,
        mode: data.singleTarget.mode,
        type: "Numerical"
      }

      // Add bounds if both are provided
      if (
        data.singleTarget.lowerBound !== undefined &&
        data.singleTarget.upperBound !== undefined
      ) {
        targetConfig.bounds = [
          data.singleTarget.lowerBound,
          data.singleTarget.upperBound
        ]
        targetConfig.transformation = "LINEAR"
      }

      return {
        parameters,
        target_config: targetConfig,
        objective_type: "SingleTarget",
        recommender_config: {
          type: "BotorchRecommender",
          n_restarts: 10,
          n_raw_samples: 64
        },
        acquisition_config: acquisitionConfig,
        constraints: data.constraints || []
      }
    } else if (data.objectiveType === "MULTI_DESIRABILITY") {
      // Multi-Target Objective (Desirability)
      // For multi-target, we need to provide a list of targets with the correct bounds format
      const targetsList = data.multiTargets.map(target => {
        // Ensure bounds are valid numbers
        const lowerBound = Number(target.lowerBound)
        const upperBound = Number(target.upperBound)

        // Ensure the bounds are valid and properly formatted
        if (
          isNaN(lowerBound) ||
          isNaN(upperBound) ||
          lowerBound >= upperBound
        ) {
          console.error(
            `Invalid bounds for target ${target.name}: [${lowerBound}, ${upperBound}]`
          )
          // Provide default bounds if invalid
          return {
            name: target.name,
            mode: target.mode,
            weight: target.weight,
            type: "Numerical",
            bounds: [0, 100]
          }
        }

        // Convert percentage weight (1-100) to decimal weight (0.1-1.0)
        // We normalize by the sum of all weights to ensure they add up to 1.0
        const percentageWeight = target.weight

        // Convert to a value between 0.1 and 1.0, ensuring minimum weight is 0.1
        const normalizedWeight = Math.max(0.1, percentageWeight / 100)

        return {
          name: target.name,
          mode: target.mode,
          weight: normalizedWeight,
          // Explicitly set the type to Numerical
          type: "Numerical",
          // The backend expects bounds directly in the target config as a list of two numbers
          bounds: [lowerBound, upperBound],
          // Add transformation to ensure proper normalization
          transformation: "LINEAR"
        }
      })

      // For Desirability objective, we need to specify the objective_type
      return {
        parameters,
        target_config: targetsList, // Pass as array for multi-target
        objective_type: "Desirability",
        scalarizer: "GEOM_MEAN", // Default scalarizer from the BayBE API
        recommender_config: {
          type: "BotorchRecommender",
          n_restarts: 10,
          n_raw_samples: 64
        },
        acquisition_config: acquisitionConfig,
        constraints: data.constraints || []
      }
    } else if (data.objectiveType === "MULTI_PARETO") {
      // Multi-Target Objective (Pareto) - True Pareto optimization
      // For Pareto optimization, we need to provide a list of targets without weights
      const targetsList = data.multiTargets.map(target => {
        // Ensure bounds are valid numbers
        const lowerBound = Number(target.lowerBound)
        const upperBound = Number(target.upperBound)

        // Ensure the bounds are valid and properly formatted
        if (
          isNaN(lowerBound) ||
          isNaN(upperBound) ||
          lowerBound >= upperBound
        ) {
          console.error(
            `Invalid bounds for target ${target.name}: [${lowerBound}, ${upperBound}]`
          )
          throw new Error(
            `Invalid bounds for target ${target.name}. Upper bound must be greater than lower bound.`
          )
        }

        return {
          name: target.name,
          mode: target.mode,
          type: "Numerical",
          bounds: [lowerBound, upperBound],
          // Add transformation to ensure proper normalization
          transformation: "LINEAR"
        }
      })

      // For ParetoObjective, we specify the objective_type as "Pareto"
      return {
        parameters,
        target_config: targetsList, // Pass as array for multi-target
        objective_type: "Pareto", // Use Pareto instead of Desirability
        recommender_config: {
          type: "BotorchRecommender",
          n_restarts: 10,
          n_raw_samples: 64
        },
        acquisition_config: acquisitionConfig,
        constraints: data.constraints || []
      }
    } else {
      throw new Error("Invalid objective type")
    }
  }

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    console.log("=== FORM SUBMISSION STARTED ===")
    console.log("Form submitted with data:", data)
    console.log("Demo mode:", demoMode)
    console.log("onDemoComplete callback available:", !!onDemoComplete)

    // In demo mode, don't actually submit the form
    if (demoMode) {
      console.log("=== DEMO MODE SUBMISSION ===")
      console.log("Demo mode: Form submitted, calling completion callback")

      // Call the demo completion callback immediately (no duplicate toast)
      console.log("=== CALLING DEMO COMPLETION CALLBACK ===")
      console.log("Demo mode: Calling onDemoComplete callback")
      console.log("Callback function:", onDemoComplete)

      if (onDemoComplete) {
        onDemoComplete()
      } else {
        console.error("onDemoComplete callback is not available!")
      }
      return
    }

    setIsSubmitting(true)
    try {
      // Validate form data
      if (!data.name || data.name.length < 2) {
        throw new Error("Name must be at least 2 characters")
      }

      // Validate target configuration based on objective type
      if (data.objectiveType === "SINGLE") {
        if (!data.singleTarget.name) {
          throw new Error("Target name is required")
        }
      } else if (
        data.objectiveType === "MULTI_DESIRABILITY" ||
        data.objectiveType === "MULTI_PARETO"
      ) {
        if (data.multiTargets.length < 2) {
          throw new Error(
            "At least two targets are required for multi-objective optimization"
          )
        }

        // Check for duplicate target names
        const targetNames = new Set()
        for (const target of data.multiTargets) {
          if (!target.name) {
            throw new Error("All target names are required")
          }
          if (target.weight < 1 || target.weight > 100) {
            throw new Error("All target weights must be between 1% and 100%")
          }
          if (targetNames.has(target.name)) {
            throw new Error(
              `Duplicate target name: ${target.name}. Each target must have a unique name.`
            )
          }
          targetNames.add(target.name)

          // Validate bounds
          if (
            target.lowerBound === undefined ||
            target.upperBound === undefined
          ) {
            throw new Error(
              `Bounds must be specified for target: ${target.name}`
            )
          }
          if (
            isNaN(Number(target.lowerBound)) ||
            isNaN(Number(target.upperBound))
          ) {
            throw new Error(
              `Bounds must be valid numbers for target: ${target.name}`
            )
          }
          if (Number(target.lowerBound) >= Number(target.upperBound)) {
            throw new Error(
              `Upper bound must be greater than lower bound for target: ${target.name}`
            )
          }
        }
      }

      // Validate acquisition function
      if (!data.acquisitionFunction.type) {
        throw new Error("Acquisition function type is required")
      }

      // For UCB, validate that beta is provided and valid
      if (data.acquisitionFunction.type === "qUpperConfidenceBound") {
        if (
          !data.acquisitionFunction.beta ||
          data.acquisitionFunction.beta < 0.1
        ) {
          throw new Error(
            "Beta parameter must be at least 0.1 for Upper Confidence Bound"
          )
        }
      }

      // For EI and PI, ensure beta is not provided (should be handled by schema validation)
      if (
        (data.acquisitionFunction.type === "qExpectedImprovement" ||
          data.acquisitionFunction.type === "qProbabilityOfImprovement") &&
        data.acquisitionFunction.beta !== undefined
      ) {
        throw new Error(
          `Beta parameter is not supported for ${data.acquisitionFunction.type}`
        )
      }

      // Validate multi-objective acquisition function parameters
      if (
        data.acquisitionFunction.type ===
          "qNoisyExpectedHypervolumeImprovement" ||
        data.acquisitionFunction.type ===
          "qLogNoisyExpectedHypervolumeImprovement"
      ) {
        // Reference point validation will be handled by the backend if not provided
        if (
          data.acquisitionFunction.ref_point &&
          data.acquisitionFunction.ref_point.length === 0
        ) {
          throw new Error("Reference point must contain at least one value")
        }
      }

      if (data.acquisitionFunction.type === "qLogNParEGO") {
        // Weights validation will be handled by the backend if not provided
        if (
          data.acquisitionFunction.weights &&
          data.acquisitionFunction.weights.length === 0
        ) {
          throw new Error("Weights must contain at least one value")
        }
      }

      if (data.parameters.length === 0) {
        throw new Error("At least one parameter is required")
      }

      // Prepare configuration
      const config = prepareOptimizationConfig(data)
      console.log("Prepared config:", config)

      // Log the configuration in a more readable format for debugging
      console.log("Config:", JSON.stringify(config, null, 2))

      // Log specific details about the targets for debugging
      if (
        data.objectiveType === "MULTI_DESIRABILITY" ||
        data.objectiveType === "MULTI_PARETO"
      ) {
        console.log("Target details for debugging:")
        data.multiTargets.forEach((target, index) => {
          console.log(
            `Target ${index + 1}: ${target.name}, Mode: ${target.mode}, Bounds: [${target.lowerBound}, ${target.upperBound}]`
          )
        })
      }

      // Ensure recommender_config has the required fields
      if (
        config.recommender_config &&
        config.recommender_config.type === "BotorchRecommender"
      ) {
        // Type assertion to help TypeScript understand the structure
        const botorchRecommender = config.recommender_config as {
          type: "BotorchRecommender"
          n_restarts?: number
          n_raw_samples?: number
        }

        // Ensure n_restarts is an integer
        if (
          botorchRecommender.n_restarts === undefined ||
          botorchRecommender.n_restarts === null
        ) {
          botorchRecommender.n_restarts = 10
        }

        // Ensure n_raw_samples is an integer
        if (
          botorchRecommender.n_raw_samples === undefined ||
          botorchRecommender.n_raw_samples === null
        ) {
          botorchRecommender.n_raw_samples = 64
        }
      }

      // Submit to server action
      console.log(
        "Calling createOptimizationWorkflowAction with config:",
        config
      )
      console.log("Form data:", data)

      const result = await createOptimizationWorkflowAction(
        data.name,
        data.description || "",
        config
      )

      console.log("Server action result:", result)
      console.log("Result type:", typeof result)
      console.log("Result isSuccess:", result?.isSuccess)
      console.log("Result structure:", Object.keys(result || {}))

      // Check if result is valid and has the expected structure
      if (!result || typeof result !== "object") {
        console.error(
          "Invalid result from createOptimizationWorkflowAction:",
          result
        )
        toast({
          title: "Error creating optimization",
          description: "Invalid response from server. Please try again.",
          variant: "destructive"
        })
        return
      }

      // Additional check for isSuccess property
      if (typeof result.isSuccess !== "boolean") {
        console.error("Result missing isSuccess property:", result)
        toast({
          title: "Error creating optimization",
          description:
            "Invalid response structure from server. Please try again.",
          variant: "destructive"
        })
        return
      }

      if (result.isSuccess && result.data) {
        toast({
          title: "Optimization created",
          description: "Your optimization has been created successfully"
        })
        router.push(`/dashboard/optimizations/${result.data.id}`)
      } else {
        toast({
          title: "Error creating optimization",
          description: result.message || "Failed to create optimization",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error creating optimization:", error)
      toast({
        title: "Error creating optimization",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Render the appropriate step content based on the active step
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <div className="mt-6 space-y-4" data-demo="basic-info-section">
            <h3 className="text-lg font-medium">Basic Information</h3>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Optimization Name</Label>
                <Controller
                  name="name"
                  control={form.control}
                  render={({ field }) => (
                    <div>
                      <Input
                        id="name"
                        placeholder="My Optimization"
                        {...field}
                      />
                      {form.formState.errors.name && (
                        <p className="text-destructive mt-1 text-sm">
                          {form.formState.errors.name.message}
                        </p>
                      )}
                    </div>
                  )}
                />
                <p className="text-muted-foreground text-sm">
                  A unique name for your optimization
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Controller
                  name="description"
                  control={form.control}
                  render={({ field }) => (
                    <Textarea
                      id="description"
                      placeholder="A brief description of this optimization"
                      {...field}
                    />
                  )}
                />
              </div>
            </div>
          </div>
        )

      case 1:
        return (
          <div className="mt-6 space-y-6" data-demo="target-config-section">
            <h3 className="flex items-center text-lg font-medium">
              <Target className="mr-2 size-5" /> Target Configuration
            </h3>

            {/* Objective Type Selection */}
            <div className="space-y-2" data-demo="objective-type-section">
              <Label htmlFor="objectiveType">Objective Type</Label>
              <Controller
                name="objectiveType"
                control={form.control}
                render={({ field }) => (
                  <div>
                    <Select
                      onValueChange={value => {
                        field.onChange(value)
                        // Auto-select appropriate default acquisition function based on objective type
                        if (value === "SINGLE") {
                          form.setValue(
                            "acquisitionFunction.type",
                            "qExpectedImprovement"
                          )
                          setSelectedAcquisitionFunction("qExpectedImprovement")
                        } else if (value === "MULTI_DESIRABILITY") {
                          form.setValue(
                            "acquisitionFunction.type",
                            "qExpectedImprovement"
                          )
                          setSelectedAcquisitionFunction("qExpectedImprovement")
                        } else if (value === "MULTI_PARETO") {
                          form.setValue(
                            "acquisitionFunction.type",
                            "qNoisyExpectedHypervolumeImprovement"
                          )
                          setSelectedAcquisitionFunction(
                            "qNoisyExpectedHypervolumeImprovement"
                          )
                        }
                        // Clear acquisition function parameters when switching objective types
                        form.setValue("acquisitionFunction.beta", undefined)
                        form.setValue(
                          "acquisitionFunction.ref_point",
                          undefined
                        )
                        form.setValue("acquisitionFunction.weights", undefined)
                        form.setValue("acquisitionFunction.rho", undefined)
                        form.setValue(
                          "acquisitionFunction.prune_baseline",
                          undefined
                        )
                      }}
                      value={field.value}
                    >
                      <SelectTrigger id="objectiveType">
                        <SelectValue placeholder="Select objective type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SINGLE">
                          <div className="flex items-center">
                            <Target className="mr-2 size-4" />
                            Single Target Objective
                          </div>
                        </SelectItem>
                        <SelectItem value="MULTI_DESIRABILITY">
                          <div className="flex items-center">
                            <Scale className="mr-2 size-4" />
                            Multi-Target Objective (Desirability)
                          </div>
                        </SelectItem>
                        <SelectItem value="MULTI_PARETO">
                          <div className="flex items-center">
                            <Scale className="mr-2 size-4" />
                            Multi-Target Objective (Pareto)
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="text-muted-foreground mt-2 text-sm">
                      <div className="space-y-1">
                        <p>
                          <strong>Single Target:</strong> Optimize one objective
                          (e.g., maximize efficiency)
                        </p>
                        <p>
                          <strong>Desirability:</strong> Combine multiple
                          objectives using weighted scalarization
                        </p>
                        <p>
                          <strong>Pareto:</strong> Find trade-off solutions
                          across multiple conflicting objectives
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              />
            </div>

            {/* Single Target Configuration */}
            {form.watch("objectiveType") === "SINGLE" && (
              <div className="space-y-4 rounded-md border p-4">
                <h4 className="font-medium">Single Target Configuration</h4>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="singleTarget.name">Target Name</Label>
                    <Controller
                      name="singleTarget.name"
                      control={form.control}
                      render={({ field }) => (
                        <div>
                          <Input
                            id="singleTarget.name"
                            placeholder="e.g., Yield"
                            {...field}
                          />
                          {form.formState.errors.singleTarget?.name && (
                            <p className="text-destructive mt-1 text-sm">
                              {form.formState.errors.singleTarget.name.message}
                            </p>
                          )}
                        </div>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="singleTarget.mode">Target Mode</Label>
                    <Controller
                      name="singleTarget.mode"
                      control={form.control}
                      render={({ field }) => (
                        <div>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <SelectTrigger
                              id="singleTarget.mode"
                              data-demo="target-mode-select"
                            >
                              <SelectValue placeholder="Select a mode" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="MAX">Maximize</SelectItem>
                              <SelectItem value="MIN">Minimize</SelectItem>
                            </SelectContent>
                          </Select>
                          {form.formState.errors.singleTarget?.mode && (
                            <p className="text-destructive mt-1 text-sm">
                              {form.formState.errors.singleTarget.mode.message}
                            </p>
                          )}
                          <p className="text-muted-foreground mt-1 text-sm">
                            Whether to maximize or minimize the target value
                          </p>
                        </div>
                      )}
                    />
                  </div>
                </div>

                {/* Target Bounds (Optional) */}
                <div className="mt-4">
                  <h5 className="mb-3 font-medium">Target Bounds (Optional)</h5>
                  <p className="text-muted-foreground mb-4 text-sm">
                    Define physical or practical limits for your target. This
                    helps the optimization understand realistic ranges and
                    improves model performance.
                  </p>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="singleTarget.lowerBound">
                        Lower Bound
                      </Label>
                      <Controller
                        name="singleTarget.lowerBound"
                        control={form.control}
                        render={({ field }) => (
                          <div>
                            <Input
                              id="singleTarget.lowerBound"
                              type="number"
                              placeholder="e.g., 0"
                              {...field}
                              value={field.value ?? ""}
                              onChange={e => {
                                const value = e.target.value
                                field.onChange(
                                  value === "" ? undefined : parseFloat(value)
                                )
                              }}
                            />
                            {form.formState.errors.singleTarget?.lowerBound && (
                              <p className="text-destructive mt-1 text-sm">
                                {
                                  form.formState.errors.singleTarget.lowerBound
                                    .message
                                }
                              </p>
                            )}
                            <p className="text-muted-foreground mt-1 text-sm">
                              Minimum physically possible value
                            </p>
                          </div>
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="singleTarget.upperBound">
                        Upper Bound
                      </Label>
                      <Controller
                        name="singleTarget.upperBound"
                        control={form.control}
                        render={({ field }) => (
                          <div>
                            <Input
                              id="singleTarget.upperBound"
                              type="number"
                              placeholder="e.g., 100"
                              {...field}
                              value={field.value ?? ""}
                              onChange={e => {
                                const value = e.target.value
                                field.onChange(
                                  value === "" ? undefined : parseFloat(value)
                                )
                              }}
                            />
                            {form.formState.errors.singleTarget?.upperBound && (
                              <p className="text-destructive mt-1 text-sm">
                                {
                                  form.formState.errors.singleTarget.upperBound
                                    .message
                                }
                              </p>
                            )}
                            <p className="text-muted-foreground mt-1 text-sm">
                              Maximum physically possible value
                            </p>
                          </div>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Multi-Target Configuration */}
            {(form.watch("objectiveType") === "MULTI_DESIRABILITY" ||
              form.watch("objectiveType") === "MULTI_PARETO") && (
              <div className="space-y-4 rounded-md border p-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Multi-Target Configuration</h4>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addTarget}
                  >
                    <Plus className="mr-2 size-4" /> Add Target
                  </Button>
                </div>

                <p className="text-muted-foreground text-sm">
                  Define multiple targets with weights for desirability-based
                  optimization.
                  <span className="font-medium">
                    {" "}
                    Each target requires bounds to enable the computation of
                    desirability values.
                  </span>
                </p>
                <div className="bg-muted mt-2 rounded-md p-3">
                  <h5 className="mb-1 font-medium">About Target Bounds:</h5>
                  <p className="text-sm">
                    Bounds define the expected range of values for each target.
                    The optimization algorithm uses these bounds to normalize
                    target values and compute desirability scores. Make sure to
                    set realistic bounds that cover the expected range of your
                    target values.
                  </p>
                </div>
                <div className="mt-2 rounded-md border border-amber-200 bg-amber-50 p-3 dark:border-amber-900 dark:bg-amber-950/50">
                  <h5 className="mb-1 flex items-center font-medium text-amber-800 dark:text-amber-400">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1"
                    >
                      <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
                      <path d="M12 9v4"></path>
                      <path d="M12 17h.01"></path>
                    </svg>
                    Important Note:
                  </h5>
                  <p className="text-sm text-amber-800 dark:text-amber-400">
                    Multi-target optimization with the Desirability objective
                    requires each target to have{" "}
                    <strong>both bounds and a LINEAR transformation</strong>.
                    The bounds must be valid numbers, and the upper bound must
                    be greater than the lower bound. For example, if your target
                    values range from 0 to 100, set the bounds to [0, 100]. If
                    you're unsure, use wider bounds like [0, 1000] to ensure all
                    possible values are covered.
                  </p>
                  <p className="mt-2 text-sm text-amber-800 dark:text-amber-400">
                    The app will automatically set missing fields to appropriate
                    defaults, but it's best to explicitly specify all required
                    fields for each target.
                  </p>
                </div>

                {form.formState.errors.multiTargets?.message && (
                  <p className="text-destructive text-sm">
                    {form.formState.errors.multiTargets.message}
                  </p>
                )}

                {(form.watch("multiTargets") || []).map((target, index) => (
                  <Card key={index} className="border border-gray-200">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">
                          Target {index + 1}
                        </CardTitle>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTarget(index)}
                        >
                          <Trash className="size-4 text-red-500" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-4 pt-0">
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div className="space-y-2 md:col-span-1">
                          <Label htmlFor={`multiTargets.${index}.name`}>
                            Target Name
                          </Label>
                          <Controller
                            name={`multiTargets.${index}.name`}
                            control={form.control}
                            render={({ field }) => (
                              <div>
                                <Input
                                  id={`multiTargets.${index}.name`}
                                  placeholder="e.g., Yield"
                                  {...field}
                                />
                                {form.formState.errors.multiTargets?.[index]
                                  ?.name && (
                                  <p className="text-destructive mt-1 text-sm">
                                    {
                                      form.formState.errors.multiTargets[index]
                                        ?.name?.message
                                    }
                                  </p>
                                )}
                              </div>
                            )}
                          />
                        </div>

                        <div className="space-y-2 md:col-span-1">
                          <Label htmlFor={`multiTargets.${index}.mode`}>
                            Target Mode
                          </Label>
                          <Controller
                            name={`multiTargets.${index}.mode`}
                            control={form.control}
                            render={({ field }) => (
                              <div>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger
                                    id={`multiTargets.${index}.mode`}
                                  >
                                    <SelectValue placeholder="Select mode" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="MAX">
                                      Maximize
                                    </SelectItem>
                                    <SelectItem value="MIN">
                                      Minimize
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                {form.formState.errors.multiTargets?.[index]
                                  ?.mode && (
                                  <p className="text-destructive mt-1 text-sm">
                                    {
                                      form.formState.errors.multiTargets[index]
                                        ?.mode?.message
                                    }
                                  </p>
                                )}
                              </div>
                            )}
                          />
                        </div>

                        {/* Weight field - only show for Desirability objective */}
                        {form.watch("objectiveType") ===
                          "MULTI_DESIRABILITY" && (
                          <div className="space-y-2 md:col-span-1">
                            <Label htmlFor={`multiTargets.${index}.weight`}>
                              Weight (%)
                            </Label>
                            <Controller
                              name={`multiTargets.${index}.weight`}
                              control={form.control}
                              render={({ field }) => (
                                <div>
                                  <div className="relative">
                                    <Input
                                      id={`multiTargets.${index}.weight`}
                                      type="number"
                                      step="1"
                                      min="1"
                                      max="100"
                                      placeholder="50"
                                      {...field}
                                      onChange={e => {
                                        const value = parseFloat(e.target.value)
                                        handleWeightChange(index, value)
                                      }}
                                    />
                                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                      <span className="text-gray-500 sm:text-sm">
                                        %
                                      </span>
                                    </div>
                                  </div>
                                  {form.formState.errors.multiTargets?.[index]
                                    ?.weight && (
                                    <p className="text-destructive mt-1 text-sm">
                                      {
                                        form.formState.errors.multiTargets[
                                          index
                                        ]?.weight?.message
                                      }
                                    </p>
                                  )}
                                  <p className="text-muted-foreground mt-1 text-sm">
                                    Relative importance of this target
                                    (percentage)
                                  </p>
                                </div>
                              )}
                            />
                          </div>
                        )}
                      </div>

                      {/* Target Bounds */}
                      <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor={`multiTargets.${index}.lowerBound`}>
                            Lower Bound
                          </Label>
                          <Controller
                            name={`multiTargets.${index}.lowerBound`}
                            control={form.control}
                            render={({ field }) => (
                              <div>
                                <Input
                                  id={`multiTargets.${index}.lowerBound`}
                                  type="number"
                                  placeholder="0"
                                  {...field}
                                  onChange={e =>
                                    field.onChange(parseFloat(e.target.value))
                                  }
                                />
                                {form.formState.errors.multiTargets?.[index]
                                  ?.lowerBound && (
                                  <p className="text-destructive mt-1 text-sm">
                                    {
                                      form.formState.errors.multiTargets[index]
                                        ?.lowerBound?.message
                                    }
                                  </p>
                                )}
                                <p className="text-muted-foreground mt-1 text-sm">
                                  Minimum expected value for this target
                                  (required for desirability calculation)
                                </p>
                              </div>
                            )}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor={`multiTargets.${index}.upperBound`}>
                            Upper Bound
                          </Label>
                          <Controller
                            name={`multiTargets.${index}.upperBound`}
                            control={form.control}
                            render={({ field }) => (
                              <div>
                                <Input
                                  id={`multiTargets.${index}.upperBound`}
                                  type="number"
                                  placeholder="100"
                                  {...field}
                                  onChange={e =>
                                    field.onChange(parseFloat(e.target.value))
                                  }
                                />
                                {form.formState.errors.multiTargets?.[index]
                                  ?.upperBound && (
                                  <p className="text-destructive mt-1 text-sm">
                                    {
                                      form.formState.errors.multiTargets[index]
                                        ?.upperBound?.message
                                    }
                                  </p>
                                )}
                                <p className="text-muted-foreground mt-1 text-sm">
                                  Maximum expected value for this target
                                  (required for desirability calculation)
                                </p>
                              </div>
                            )}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )

      case 2:
        return (
          <div className="mt-6 space-y-4" data-demo="parameters-section">
            <div className="flex items-center justify-between">
              <h3 className="flex items-center text-lg font-medium">
                <Beaker className="mr-2 size-5" /> Parameters
              </h3>
              <Button
                type="button"
                variant="outline"
                onClick={addParameter}
                data-demo="add-parameter-button"
              >
                <Plus className="mr-2 size-4" /> Add Parameter
              </Button>
            </div>

            {/* Parameter List */}
            {form.watch("parameters").map((parameter, index) => (
              <Card key={index} className="border border-gray-200">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">
                      Parameter {index + 1}
                    </CardTitle>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeParameter(index)}
                    >
                      <Trash className="size-4 text-red-500" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pb-4 pt-0">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor={`parameters.${index}.name`}>
                        Parameter Name
                      </Label>
                      <Controller
                        name={`parameters.${index}.name`}
                        control={form.control}
                        render={({ field }) => (
                          <div>
                            <Input
                              id={`parameters.${index}.name`}
                              placeholder="e.g., Temperature"
                              {...field}
                            />
                            {form.formState.errors.parameters?.[index]
                              ?.name && (
                              <p className="text-destructive mt-1 text-sm">
                                {typeof form.formState.errors.parameters[index]
                                  ?.name === "object" &&
                                "message" in
                                  form.formState.errors.parameters[index]?.name
                                  ? form.formState.errors.parameters[index]
                                      ?.name.message
                                  : "Invalid parameter name"}
                              </p>
                            )}
                          </div>
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`parameters.${index}.type`}>
                        Parameter Type
                      </Label>
                      <Controller
                        name={`parameters.${index}.type`}
                        control={form.control}
                        render={({ field }) => (
                          <div>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <SelectTrigger
                                id={`parameters.${index}.type`}
                                data-demo={`parameter-type-${index}`}
                              >
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="NumericalDiscrete">
                                  Numerical Discrete
                                </SelectItem>
                                <SelectItem value="NumericalContinuous">
                                  Numerical Continuous
                                </SelectItem>
                                <SelectItem value="CategoricalParameter">
                                  Categorical
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            {form.formState.errors.parameters?.[index]
                              ?.type && (
                              <p className="text-destructive mt-1 text-sm">
                                {typeof form.formState.errors.parameters[index]
                                  ?.type === "object" &&
                                "message" in
                                  form.formState.errors.parameters[index]?.type
                                  ? form.formState.errors.parameters[index]
                                      ?.type.message
                                  : "Invalid parameter type"}
                              </p>
                            )}
                          </div>
                        )}
                      />
                    </div>
                  </div>

                  {/* Type-specific fields */}
                  {form.watch(`parameters.${index}.type`) ===
                    "NumericalDiscrete" && (
                    <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor={`parameters.${index}.values`}>
                          Values (comma-separated)
                        </Label>
                        <Controller
                          name={`parameters.${index}.values`}
                          control={form.control}
                          render={({ field }) => (
                            <div>
                              <Input
                                id={`parameters.${index}.values`}
                                placeholder="10, 20, 30, 40, 50"
                                {...field}
                              />
                              {(form.formState.errors.parameters?.[index]
                                ?.values ||
                                (form.formState.errors.parameters?.[index]
                                  ?.type &&
                                  form.watch(`parameters.${index}.type`) ===
                                    "NumericalDiscrete")) && (
                                <p className="text-destructive mt-1 text-sm">
                                  {form.formState.errors.parameters[index]
                                    ?.values?.message ||
                                    "Values are required for Numerical Discrete parameters"}
                                </p>
                              )}
                              <p className="text-muted-foreground mt-1 text-sm">
                                Discrete values to explore
                              </p>
                            </div>
                          )}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`parameters.${index}.tolerance`}>
                          Tolerance (optional)
                        </Label>
                        <Controller
                          name={`parameters.${index}.tolerance`}
                          control={form.control}
                          render={({ field }) => (
                            <div>
                              <Input
                                id={`parameters.${index}.tolerance`}
                                placeholder="0.5"
                                {...field}
                              />
                              <p className="text-muted-foreground mt-1 text-sm">
                                Allowed deviation from discrete values
                              </p>
                            </div>
                          )}
                        />
                      </div>
                    </div>
                  )}

                  {form.watch(`parameters.${index}.type`) ===
                    "NumericalContinuous" && (
                    <div className="mt-4">
                      <div className="space-y-2">
                        <Label htmlFor={`parameters.${index}.bounds`}>
                          Bounds (min, max)
                        </Label>
                        <Controller
                          name={`parameters.${index}.bounds`}
                          control={form.control}
                          render={({ field }) => (
                            <div>
                              <Input
                                id={`parameters.${index}.bounds`}
                                placeholder="0, 100"
                                {...field}
                              />
                              {(form.formState.errors.parameters?.[index]
                                ?.bounds ||
                                (form.formState.errors.parameters?.[index]
                                  ?.type &&
                                  form.watch(`parameters.${index}.type`) ===
                                    "NumericalContinuous")) && (
                                <p className="text-destructive mt-1 text-sm">
                                  {form.formState.errors.parameters[index]
                                    ?.bounds?.message ||
                                    "Bounds are required for Numerical Continuous parameters"}
                                </p>
                              )}
                              <p className="text-muted-foreground mt-1 text-sm">
                                Range of values to explore, e.g., "0, 100"
                              </p>
                            </div>
                          )}
                        />
                      </div>
                    </div>
                  )}

                  {form.watch(`parameters.${index}.type`) ===
                    "CategoricalParameter" && (
                    <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor={`parameters.${index}.values`}>
                          Values (comma-separated)
                        </Label>
                        <Controller
                          name={`parameters.${index}.values`}
                          control={form.control}
                          render={({ field }) => (
                            <div>
                              <Input
                                id={`parameters.${index}.values`}
                                placeholder="Red, Green, Blue"
                                {...field}
                              />
                              {(form.formState.errors.parameters?.[index]
                                ?.values ||
                                (form.formState.errors.parameters?.[index]
                                  ?.type &&
                                  form.watch(`parameters.${index}.type`) ===
                                    "CategoricalParameter")) && (
                                <p className="text-destructive mt-1 text-sm">
                                  {form.formState.errors.parameters[index]
                                    ?.values?.message ||
                                    "Values are required for Categorical parameters"}
                                </p>
                              )}
                              <p className="text-muted-foreground mt-1 text-sm">
                                Categorical values to explore
                              </p>
                            </div>
                          )}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`parameters.${index}.encoding`}>
                          Encoding
                        </Label>
                        <Controller
                          name={`parameters.${index}.encoding`}
                          control={form.control}
                          render={({ field }) => (
                            <div>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger
                                  id={`parameters.${index}.encoding`}
                                >
                                  <SelectValue placeholder="Select encoding" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="OHE">
                                    One-Hot Encoding (OHE)
                                  </SelectItem>
                                  <SelectItem value="LE">
                                    Label Encoding (LE)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )

      case 3:
        return (
          <div className="mt-6 space-y-6" data-demo="advanced-section">
            <h3 className="flex items-center text-lg font-medium">
              <Settings className="mr-2 size-5" /> Advanced Options
            </h3>

            {/* User Guidance Message */}
            <div className="space-y-4 rounded-md border p-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="mt-0.5 rounded-full bg-green-100 p-2">
                    <CheckCircle className="size-4 text-green-600" />
                  </div>
                  <div className="space-y-1">
                    <h4 className="font-medium text-green-900">
                      Ready to Optimize
                    </h4>
                    <p className="text-sm leading-relaxed text-green-800">
                      Your optimization is ready to run with intelligent
                      defaults. The system will automatically use proven
                      settings that work well for most scenarios.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="mt-0.5 rounded-full bg-blue-100 p-2">
                    <Brain className="size-4 text-blue-600" />
                  </div>
                  <div className="space-y-1">
                    <h4 className="font-medium text-blue-900">
                      Expert Mode Available
                    </h4>
                    <p className="text-sm leading-relaxed text-blue-800">
                      Advanced users familiar with Bayesian optimization can
                      enable expert controls below for fine-tuned customization
                      of constraints, acquisition functions, and surrogate
                      models.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <AdvancedOptionsSection
              showConstraints={true}
              constraintCount={form.watch("constraints")?.length || 0}
              onConstraintsToggle={enabled => {
                if (!enabled) {
                  // Clear constraints when disabled
                  form.setValue("constraints", [])
                }
              }}
              showAcquisitionFunction={true}
              onAcquisitionFunctionToggle={enabled => {
                if (!enabled) {
                  // Reset to appropriate default based on objective type when disabled
                  const currentObjectiveType = form.watch("objectiveType")
                  const defaultAcqFunction =
                    getDefaultAcquisitionFunction(currentObjectiveType)
                  form.setValue("acquisitionFunction", {
                    type: defaultAcqFunction
                  })
                  setSelectedAcquisitionFunction(defaultAcqFunction)
                }
              }}
              acquisitionFunctionChildren={
                <AcquisitionFunctionControls
                  control={form.control}
                  selectedAcquisitionFunction={selectedAcquisitionFunction}
                  objectiveType={form.watch("objectiveType")}
                  onAcquisitionFunctionChange={value => {
                    setSelectedAcquisitionFunction(value)

                    // Clear all acquisition function parameters first
                    form.setValue("acquisitionFunction.beta", undefined)
                    form.setValue("acquisitionFunction.ref_point", undefined)
                    form.setValue("acquisitionFunction.weights", undefined)
                    form.setValue("acquisitionFunction.rho", undefined)
                    form.setValue(
                      "acquisitionFunction.prune_baseline",
                      undefined
                    )

                    // Set appropriate defaults based on function type
                    if (value === "qUpperConfidenceBound") {
                      form.setValue("acquisitionFunction.beta", 0.2)
                    }
                    // For multi-objective functions, we don't set defaults since they're optional
                    // and the backend will handle missing values appropriately
                  }}
                  formErrors={form.formState.errors}
                />
              }
            >
              <CentralizedConstraintBuilder
                constraints={form.watch("constraints") || []}
                onConstraintsChange={constraints => {
                  form.setValue("constraints", constraints)
                }}
                parameters={form
                  .watch("parameters")
                  .filter(p => p.name && p.name.trim() !== "")
                  .map(
                    p =>
                      ({
                        name: p.name,
                        type: p.type as
                          | "NumericalDiscrete"
                          | "NumericalContinuous"
                          | "CategoricalParameter",
                        values:
                          p.type === "CategoricalParameter"
                            ? p.values?.split(",").map(v => v.trim())
                            : p.type === "NumericalDiscrete"
                              ? p.values
                                  ?.split(",")
                                  .map(v => parseFloat(v.trim()))
                                  .filter(v => !isNaN(v))
                              : undefined,
                        bounds:
                          p.type === "NumericalContinuous" && p.bounds
                            ? (p.bounds
                                .split(",")
                                .map(v => parseFloat(v.trim()))
                                .filter(v => !isNaN(v)) as [number, number])
                            : undefined,
                        tolerance: p.tolerance
                          ? parseFloat(p.tolerance)
                          : undefined,
                        encoding: p.encoding as "OHE" | "LE" | undefined
                      }) as ConstraintParameter
                  )}
                enableSampling={true}
                samplingOptions={{
                  strategy: "LHS",
                  nSamples: 10,
                  respectConstraints: true
                }}
              />
            </AdvancedOptionsSection>
          </div>
        )

      case 4:
        return (
          <div className="mt-6 space-y-4" data-demo="review-section">
            <h3 className="flex items-center text-lg font-medium">
              <ClipboardCheck className="mr-2 size-5" /> Review Your
              Optimization
            </h3>

            <div className="space-y-6">
              <Card className="border border-gray-200">
                <CardHeader>
                  <CardTitle className="text-base">Basic Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <dl className="space-y-2">
                    <div>
                      <dt className="font-medium">Name</dt>
                      <dd>{form.getValues("name")}</dd>
                    </div>
                    {form.getValues("description") && (
                      <div>
                        <dt className="font-medium">Description</dt>
                        <dd>{form.getValues("description")}</dd>
                      </div>
                    )}
                  </dl>
                </CardContent>
              </Card>

              <Card className="border border-gray-200">
                <CardHeader>
                  <CardTitle className="text-base">
                    Target Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <dl className="space-y-2">
                    <div>
                      <dt className="font-medium">Objective Type</dt>
                      <dd>
                        {(() => {
                          const objType = form.getValues("objectiveType")
                          switch (objType) {
                            case "SINGLE":
                              return "Single Target Objective"
                            case "MULTI_DESIRABILITY":
                              return "Multi-Target Objective (Desirability)"
                            case "MULTI_PARETO":
                              return "Multi-Target Objective (Pareto)"
                            default:
                              return objType
                          }
                        })()}
                      </dd>
                    </div>

                    {/* Add helpful descriptions for objective types */}
                    <div className="text-muted-foreground text-sm">
                      {form.getValues("objectiveType") ===
                        "MULTI_DESIRABILITY" && (
                        <p className="rounded-md border border-green-200 bg-green-50 p-2">
                          <strong>Desirability:</strong> Combines multiple
                          objectives using weighted scalarization. Finds
                          solutions optimized for the specific weight
                          combination.
                        </p>
                      )}
                      {form.getValues("objectiveType") === "MULTI_PARETO" && (
                        <p className="rounded-md border border-blue-200 bg-blue-50 p-2">
                          <strong>Pareto:</strong> Explores the full trade-off
                          space between objectives. Finds solutions along the
                          Pareto frontier without predetermined weights.
                        </p>
                      )}
                    </div>

                    {form.getValues("objectiveType") === "SINGLE" ? (
                      <>
                        <div>
                          <dt className="font-medium">Target Name</dt>
                          <dd>{form.getValues("singleTarget.name")}</dd>
                        </div>
                        <div>
                          <dt className="font-medium">Optimization Mode</dt>
                          <dd>
                            {form.getValues("singleTarget.mode") === "MAX"
                              ? "Maximize"
                              : "Minimize"}
                          </dd>
                        </div>
                        {(form.getValues("singleTarget.lowerBound") !==
                          undefined ||
                          form.getValues("singleTarget.upperBound") !==
                            undefined) && (
                          <div>
                            <dt className="font-medium">Target Bounds</dt>
                            <dd>
                              [
                              {form.getValues("singleTarget.lowerBound") ??
                                "−∞"}
                              ,{" "}
                              {form.getValues("singleTarget.upperBound") ??
                                "+∞"}
                              ]
                            </dd>
                          </div>
                        )}
                      </>
                    ) : (
                      <>
                        <div>
                          <dt className="font-medium">Targets</dt>
                          <dd>
                            <div className="mt-2 space-y-2">
                              {(form.getValues("multiTargets") || []).map(
                                (target, index) => (
                                  <div
                                    key={index}
                                    className="rounded-md border p-3"
                                  >
                                    <h4 className="font-medium">
                                      {target.name}
                                    </h4>
                                    <dl className="mt-2 grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                      <div>
                                        <dt className="text-muted-foreground">
                                          Mode
                                        </dt>
                                        <dd>
                                          {target.mode === "MAX"
                                            ? "Maximize"
                                            : "Minimize"}
                                        </dd>
                                      </div>
                                      {form.getValues("objectiveType") ===
                                        "MULTI_DESIRABILITY" && (
                                        <div>
                                          <dt className="text-muted-foreground">
                                            Weight
                                          </dt>
                                          <dd>{target.weight}%</dd>
                                        </div>
                                      )}
                                      <div>
                                        <dt className="text-muted-foreground">
                                          Bounds
                                        </dt>
                                        <dd>
                                          [{target.lowerBound},{" "}
                                          {target.upperBound}]
                                        </dd>
                                      </div>
                                    </dl>
                                  </div>
                                )
                              )}
                            </div>
                          </dd>
                        </div>
                      </>
                    )}
                  </dl>
                </CardContent>
              </Card>

              <Card className="border border-gray-200">
                <CardHeader>
                  <CardTitle className="text-base">
                    Acquisition Function
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <dl className="space-y-2">
                    <div>
                      <dt className="font-medium">Function Type</dt>
                      <dd>
                        {(() => {
                          const type = form.getValues(
                            "acquisitionFunction.type"
                          )
                          switch (type) {
                            case "qExpectedImprovement":
                              return "Expected Improvement (EI)"
                            case "qProbabilityOfImprovement":
                              return "Probability of Improvement (PI)"
                            case "qUpperConfidenceBound":
                              return "Upper Confidence Bound (UCB)"
                            case "qNoisyExpectedHypervolumeImprovement":
                              return "Noisy Expected Hypervolume Improvement (qNEHVI)"
                            case "qLogNoisyExpectedHypervolumeImprovement":
                              return "Log Noisy Expected Hypervolume Improvement (qLogNEHVI)"
                            case "qLogNParEGO":
                              return "Log ParEGO (qLogNParEGO)"
                            default:
                              return type
                          }
                        })()}
                      </dd>
                    </div>
                    {form.getValues("acquisitionFunction.type") ===
                      "qUpperConfidenceBound" && (
                      <div>
                        <dt className="font-medium">Beta Parameter</dt>
                        <dd>
                          {form.getValues("acquisitionFunction.beta") || 0.2}
                        </dd>
                      </div>
                    )}
                    {(form.getValues("acquisitionFunction.type") ===
                      "qNoisyExpectedHypervolumeImprovement" ||
                      form.getValues("acquisitionFunction.type") ===
                        "qLogNoisyExpectedHypervolumeImprovement") &&
                      form.getValues("acquisitionFunction.ref_point") && (
                        <div>
                          <dt className="font-medium">Reference Point</dt>
                          <dd>
                            {JSON.stringify(
                              form.getValues("acquisitionFunction.ref_point")
                            )}
                          </dd>
                        </div>
                      )}
                    {form.getValues("acquisitionFunction.type") ===
                      "qLogNParEGO" &&
                      form.getValues("acquisitionFunction.weights") && (
                        <div>
                          <dt className="font-medium">Objective Weights</dt>
                          <dd>
                            {JSON.stringify(
                              form.getValues("acquisitionFunction.weights")
                            )}
                          </dd>
                        </div>
                      )}
                    {form.getValues("acquisitionFunction.type") ===
                      "qLogNParEGO" &&
                      form.getValues("acquisitionFunction.rho") !==
                        undefined && (
                        <div>
                          <dt className="font-medium">Rho Parameter</dt>
                          <dd>{form.getValues("acquisitionFunction.rho")}</dd>
                        </div>
                      )}
                    <div className="text-muted-foreground mt-2 text-sm">
                      <p className="mb-1 font-medium">Description:</p>
                      {(() => {
                        const type = form.getValues("acquisitionFunction.type")
                        switch (type) {
                          case "qExpectedImprovement":
                            return "Balances exploration and exploitation. Good general-purpose choice."
                          case "qProbabilityOfImprovement":
                            return "More exploitative, focuses on areas likely to improve."
                          case "qUpperConfidenceBound":
                            return "Allows explicit control of exploration/exploitation trade-off via beta parameter."
                          case "qNoisyExpectedHypervolumeImprovement":
                            return "Multi-objective acquisition function for noisy optimization. Handles measurement noise in multi-objective optimization."
                          case "qLogNoisyExpectedHypervolumeImprovement":
                            return "Log-space version of qNEHVI for better numerical stability. More stable than qNEHVI."
                          case "qLogNParEGO":
                            return "Log-space scalarization-based multi-objective acquisition. More stable than regular ParEGO for extreme values."
                          default:
                            return ""
                        }
                      })()}
                    </div>
                  </dl>
                </CardContent>
              </Card>

              <Card className="border border-gray-200">
                <CardHeader>
                  <CardTitle className="text-base">
                    Parameters ({form.getValues("parameters").length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {form.getValues("parameters").map((param, index) => (
                      <div key={index} className="rounded-md border p-3">
                        <h4 className="font-medium">
                          {param.name || `Parameter ${index + 1}`}
                        </h4>
                        <dl className="mt-2 grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                          <div>
                            <dt className="text-muted-foreground">Type</dt>
                            <dd>{param.type}</dd>
                          </div>
                          {param.type === "NumericalDiscrete" && (
                            <>
                              <div>
                                <dt className="text-muted-foreground">
                                  Values
                                </dt>
                                <dd>{param.values}</dd>
                              </div>
                              {param.tolerance && (
                                <div>
                                  <dt className="text-muted-foreground">
                                    Tolerance
                                  </dt>
                                  <dd>{param.tolerance}</dd>
                                </div>
                              )}
                            </>
                          )}
                          {param.type === "NumericalContinuous" && (
                            <div>
                              <dt className="text-muted-foreground">Bounds</dt>
                              <dd>{param.bounds}</dd>
                            </div>
                          )}
                          {param.type === "CategoricalParameter" && (
                            <>
                              <div>
                                <dt className="text-muted-foreground">
                                  Values
                                </dt>
                                <dd>{param.values}</dd>
                              </div>
                              <div>
                                <dt className="text-muted-foreground">
                                  Encoding
                                </dt>
                                <dd>{param.encoding}</dd>
                              </div>
                            </>
                          )}
                        </dl>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Constraints Review */}
              {(() => {
                const constraints = form.getValues("constraints")
                return (
                  constraints &&
                  constraints.length > 0 && (
                    <Card className="border border-gray-200">
                      <CardHeader>
                        <CardTitle className="text-base">
                          Constraints ({constraints.length})
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {constraints.map((constraint: any, index: number) => (
                            <div key={index} className="rounded-md border p-3">
                              <h4 className="font-medium">
                                {constraint.name || `Constraint ${index + 1}`}
                              </h4>
                              <dl className="mt-2 grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                <div>
                                  <dt className="text-muted-foreground">
                                    Type
                                  </dt>
                                  <dd>{constraint.type}</dd>
                                </div>
                                <div>
                                  <dt className="text-muted-foreground">
                                    Parameters
                                  </dt>
                                  <dd>
                                    {constraint.parameters?.join(", ") ||
                                      "None"}
                                  </dd>
                                </div>
                                {constraint.description && (
                                  <div className="col-span-2">
                                    <dt className="text-muted-foreground">
                                      Description
                                    </dt>
                                    <dd>{constraint.description}</dd>
                                  </div>
                                )}
                              </dl>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )
                )
              })()}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Create New Optimization</CardTitle>
          <CardDescription>
            Configure your optimization parameters and target
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Progress Stepper */}
          <ProgressStepper steps={steps} currentStep={activeStep} />

          {/* Step Content */}
          <form onSubmit={form.handleSubmit(onSubmit)}>
            {renderStepContent()}
          </form>
        </CardContent>

        <CardFooter className="flex justify-between">
          <div>
            {activeStep > 0 ? (
              <Button type="button" variant="outline" onClick={handlePrevious}>
                <ArrowLeft className="mr-2 size-4" />
                Previous
              </Button>
            ) : (
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Cancel
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            {activeStep < steps.length - 1 ? (
              <Button
                type="button"
                onClick={handleNext}
                data-demo="next-button"
              >
                Next
                <ArrowRight className="ml-2 size-4" />
              </Button>
            ) : (
              <>
                <Button
                  type="button"
                  onClick={e => {
                    console.log("=== COMPLETE DEMO BUTTON CLICKED ===")
                    console.log("Demo mode:", demoMode)
                    console.log("Form errors:", form.formState.errors)
                    console.log("Form values:", form.getValues())

                    if (demoMode) {
                      // In demo mode, bypass form validation and call onSubmit directly
                      console.log("Demo mode: Calling onSubmit directly")
                      onSubmit(form.getValues())
                    } else {
                      // Normal mode: use form validation
                      form.handleSubmit(onSubmit)(e)
                    }
                  }}
                  disabled={isSubmitting}
                  data-demo="create-button"
                >
                  {isSubmitting
                    ? "Creating..."
                    : demoMode
                      ? "Complete Demo"
                      : "Create Optimization"}
                </Button>
              </>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
