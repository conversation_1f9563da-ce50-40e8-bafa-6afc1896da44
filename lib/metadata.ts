/**
 * Utility functions for generating consistent metadata across the application
 * This ensures proper social media sharing and SEO for all pages
 */

import { Metadata } from "next"
import { BRAND } from "./constants"

interface PageMetadataOptions {
  title?: string
  description?: string
  path?: string
  image?: string
  keywords?: string[]
  type?: "website" | "article"
}

/**
 * Generate comprehensive metadata for a page
 */
export function generateMetadata(options: PageMetadataOptions = {}): Metadata {
  const {
    title,
    description = BRAND.SEO_DESCRIPTION,
    path = "",
    image = "/social-preview.png",
    keywords = [],
    type = "website"
  } = options

  // Construct full title
  const fullTitle = title ? `${title} | ${BRAND.NAME}` : BRAND.SEO_TITLE

  // Construct full URL
  const url = `${BRAND.DOMAIN}${path}`

  // Default keywords
  const defaultKeywords = [
    "optimization",
    "experimental design",
    "bayesian optimization",
    "pharmaceutical research",
    "scientific research",
    "process optimization",
    "machine learning",
    "data science"
  ]

  const allKeywords = [...defaultKeywords, ...keywords]

  return {
    title: fullTitle,
    description,

    // Open Graph (Facebook, LinkedIn, WhatsApp, etc.)
    openGraph: {
      title: fullTitle,
      description,
      url,
      siteName: BRAND.NAME,
      images: [
        {
          url: image,
          width: image.includes("square") ? 800 : 1200,
          height: image.includes("square")
            ? 800
            : image.includes("twitter")
              ? 600
              : 630,
          alt: `${BRAND.NAME} - ${description}`
        }
      ],
      locale: "en_US",
      type
    },

    // Twitter Card
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description,
      images: [image]
    },

    // SEO
    keywords: allKeywords,
    authors: [{ name: BRAND.NAME }],

    // Additional meta
    other: {
      "theme-color": "#000000",
      "color-scheme": "light"
    },

    // Canonical URL
    alternates: {
      canonical: url
    }
  }
}

/**
 * Generate metadata specifically for tutorial pages
 */
export function generateTutorialMetadata(
  title: string,
  description: string,
  path: string
): Metadata {
  return generateMetadata({
    title,
    description,
    path,
    type: "article",
    keywords: ["tutorial", "guide", "how-to", "learning"]
  })
}

/**
 * Generate metadata for marketing pages
 */
export function generateMarketingMetadata(
  title: string,
  description: string,
  path: string
): Metadata {
  return generateMetadata({
    title,
    description,
    path,
    keywords: ["features", "pricing", "benefits", "platform"]
  })
}

/**
 * Social media image options
 */
export const SOCIAL_IMAGES = {
  DEFAULT: "/social-preview.png",
  TWITTER: "/twitter-card.png",
  SQUARE: "/social-square.png"
} as const
