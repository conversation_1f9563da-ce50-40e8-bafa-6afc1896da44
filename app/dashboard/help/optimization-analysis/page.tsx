import { <PERSON>ada<PERSON> } from "next"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  Workflow,
  GitBranch,
  BarChart3,
  LineChart,
  Layers,
  Target,
  AreaChart,
  ExternalLink
} from "lucide-react"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Optimization Analysis | Help Centre",
  description: "Understanding the analysis tools in the optimization system"
}

export default function OptimizationAnalysisPage() {
  return (
    <div className="container max-w-5xl py-10">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Optimization Analysis
        </h1>
        <p className="text-muted-foreground">
          Understanding the analysis tools and visualizations in the
          optimization system
        </p>
      </div>

      <div className="mt-8 space-y-8">
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Workflow className="text-primary size-5" />
                Strategy Evolution
              </CardTitle>
              <CardDescription>
                Understanding how optimization strategy changes over time
                (Coming Soon)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-muted/50 flex h-[200px] w-full items-center justify-center rounded-md border">
                <div className="p-6 text-center">
                  <Workflow className="text-muted-foreground mx-auto mb-4 size-12" />
                  <h3 className="text-lg font-medium">Strategy Evolution</h3>
                  <p className="text-muted-foreground mt-2 max-w-md text-sm">
                    This advanced visualization will show how the optimization
                    balances between exploration and exploitation over time.
                    Coming soon!
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="text-primary size-5" />
                Trade-off Explorer
              </CardTitle>
              <CardDescription>
                Visualizing trade-offs between multiple optimization targets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                The Trade-off Explorer helps you understand the relationships
                and trade-offs between different optimization targets. It's
                particularly useful for multi-target optimizations where
                improving one target might come at the expense of another.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Key Features:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    Scatter plot showing the relationship between two targets
                  </li>
                  <li>
                    Pareto front highlighting to identify optimal trade-offs
                  </li>
                  <li>
                    Experiment selection to view specific parameter values
                  </li>
                  <li>
                    Target selection to explore different trade-off
                    relationships
                  </li>
                </ul>
              </div>

              <p className="mt-4">
                By exploring these trade-offs, you can make informed decisions
                about which parameter combinations best meet your specific
                requirements, especially when you need to balance multiple
                competing objectives.
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AreaChart className="text-primary size-5" />
                Convergence Analysis
              </CardTitle>
              <CardDescription>
                Understanding optimization progress and when to stop experiments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Convergence Analysis shows how your optimization is progressing
                toward finding the best solution. It's especially valuable when
                working with limited experimental budgets (≤25 experiments) to
                make informed decisions about when to continue or stop.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Three Key Visualizations:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>Convergence Plot</strong>: Shows best value found
                    over time with individual measurements
                  </li>
                  <li>
                    <strong>Regret Analysis</strong>: Tracks distance from
                    optimal solution
                  </li>
                  <li>
                    <strong>Improvement Analysis</strong>: Identifies when
                    experiments find better solutions
                  </li>
                </ul>
              </div>

              <div className="bg-muted mt-4 space-y-2 rounded-md p-4">
                <h3 className="font-medium">Decision Support:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>Statistics on plateau length and improvement rates</li>
                  <li>Visual indicators for when to continue or stop</li>
                  <li>Guidance specific to small dataset optimization</li>
                </ul>
              </div>

              <p className="mt-4">
                This analysis is crucial for maximizing the value of each
                experiment when working with limited resources and helps avoid
                both premature stopping and wasteful over-experimentation.
              </p>

              <div className="bg-muted mt-4 space-y-2 rounded-md p-4">
                <h3 className="font-medium">Intelligent Recommendations:</h3>
                <p className="text-sm">
                  The system provides gentle, data-driven insights to help guide
                  your decisions:
                </p>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>Progress Updates</strong>: When optimization is
                    making good progress
                  </li>
                  <li>
                    <strong>Plateau Detection</strong>: When improvements have
                    slowed or stopped
                  </li>
                  <li>
                    <strong>Convergence Insights</strong>: When optimization
                    appears to have found the optimum
                  </li>
                  <li>
                    <strong>Budget Awareness</strong>: Considers your
                    experimental budget constraints
                  </li>
                </ul>
              </div>

              <div className="mt-4 border-t pt-4">
                <Link
                  href="/dashboard/help/convergence-analysis"
                  className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  <ExternalLink className="size-4" />
                  Complete Convergence Analysis Guide
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="text-primary size-5" />
                Parameter Analysis
              </CardTitle>
              <CardDescription>
                Understanding parameter relationships and importance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Parameter Analysis helps you understand how different parameters
                affect your optimization targets and how they interact with each
                other. This can provide valuable insights for experimental
                design and process understanding.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Available Visualizations:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>Correlation Explorer</strong>: Shows relationships
                    between parameters and targets
                  </li>
                  <li>
                    <strong>Parameter Distributions</strong>: Visualizes the
                    distribution of parameter values
                  </li>
                  <li>
                    <strong>Parameter Importance</strong>: Identifies which
                    parameters have the most influence
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="text-primary size-5" />
                Experiment Timeline
              </CardTitle>
              <CardDescription>
                Tracking optimization progress over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                The Experiment Timeline section provides visualizations that
                track how your optimization progresses over time, helping you
                understand the learning rate and strategy evolution.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Timeline Components:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>Progress Visualization</strong>: Shows how target
                    values improve over time
                  </li>
                </ul>
                <p className="text-muted-foreground mt-2 text-xs">
                  Additional timeline features (Strategy Evolution and Learning
                  Rate) are coming soon.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layers className="text-primary size-5" />
                Response Surface Analysis
              </CardTitle>
              <CardDescription>
                Visualizing the predicted response surface
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Response Surface Analysis provides visualizations of how the
                optimization targets are predicted to change as parameters vary.
                This helps you understand the shape of the optimization
                landscape and identify promising regions.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Surface Visualizations:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>3D Surface Plots</strong>: Show how two parameters
                    affect a target
                  </li>
                  <li>
                    <strong>Contour Plots</strong>: Provide 2D views of the
                    response surface
                  </li>
                  <li>
                    <strong>Cross-sections</strong>: Show how targets change
                    along specific parameter paths
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="text-primary size-5" />
                Multi-Target Analysis
              </CardTitle>
              <CardDescription>
                Analyzing relationships between multiple targets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Multi-Target Analysis helps you understand the relationships
                between different optimization targets and how they interact.
                This is crucial for multi-objective optimization where you need
                to balance multiple competing goals.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h3 className="font-medium">Analysis Tools:</h3>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <strong>Trade-off Explorer</strong>: Visualizes Pareto
                    fronts and trade-offs
                  </li>
                  <li>
                    <strong>Parallel Coordinates</strong>: Shows
                    multi-dimensional relationships
                  </li>
                  <li>
                    <strong>Target Correlations</strong>: Identifies how targets
                    relate to each other
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
