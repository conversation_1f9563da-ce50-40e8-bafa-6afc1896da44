#!/usr/bin/env node

/**
 * Test script to verify target bounds updates are saved to database
 */

async function testTargetBoundsUpdate() {
  console.log('🎯 TESTING TARGET BOUNDS UPDATE');
  console.log('===============================');
  
  const optimizerId = "user_30EFn0yPauqlMMorn5hfsn3of5X_Test_campaign_working_1757682871157";
  
  try {
    console.log(`📋 Testing target bounds change for: ${optimizerId.slice(-20)}`);
    console.log(`📋 Changing Target bounds from [0, 150] to [0, 120]`);
    console.log('');
    
    // Test target bounds update
    const response = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target_bounds: {
          "Target": [0, 120]
        },
        preview_only: false
      })
    });
    
    console.log(`📤 Request sent to: /api/optimizations/${optimizerId.slice(-20)}/bounds`);
    console.log(`📤 Request body: {"target_bounds": {"Target": [0, 120]}, "preview_only": false}`);
    console.log('');
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Target bounds update failed: ${response.status} ${response.statusText}`);
      console.error(`❌ Error details: ${errorText}`);
      return;
    }
    
    const result = await response.json();
    console.log(`✅ Target bounds update response:`, result);
    console.log('');
    
    if (result.status === "success") {
      console.log(`✅ Backend update successful`);
      
      if (result.database_updated === true) {
        console.log(`✅ Database configuration updated successfully`);
      } else if (result.database_updated === false) {
        console.log(`⚠️ Database update failed: ${result.database_error || 'Unknown error'}`);
      } else {
        console.log(`ℹ️ Database update status not reported`);
      }
      
      if (result.updated_config) {
        console.log(`✅ Updated configuration provided by backend`);
        console.log(`   Target bounds in updated config: ${JSON.stringify(result.updated_config.target_config?.bounds)}`);
      } else {
        console.log(`⚠️ No updated configuration provided by backend`);
      }
    } else {
      console.log(`❌ Backend update failed: ${result.message || 'Unknown error'}`);
    }
    
    console.log('');
    console.log(`🔍 The monitoring script should now show the configuration change...`);
    console.log(`   Watch for "TARGET CHANGES" in the monitoring output`);
    
  } catch (error) {
    console.error('❌ Error during target bounds test:', error.message);
  }
}

// Run the test
testTargetBoundsUpdate().catch(console.error);
