"use client"

import React, { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Section } from "@/components/ui/section"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>aker,
  FlaskConical,
  Rocket,
  ArrowRight,
  Gauge,
  MessageSquare
} from "lucide-react"
import Link from "next/link"
import { BRAND } from "@/lib/constants"

// Real user testimonials
const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON>",
    role: "Associate Scientist Organic Chemistry",
    company: "InnoSyn",
    logo: "/images/testimonials/Innosynlogo.svg",
    image: "/images/testimonials/noortje-coenen.jpg",
    testimonial:
      "I used the INNOptimizer™ tool to optimize a chemical conversion process with three key parameters. Starting with just 6 manually inputted baseline experiments, I was able to achieve remarkable results in a very short time. The process was incredibly straightforward. After entering my initial data, I ran three optimization cycles, taking 4 AI-suggested experiments each time and feeding the results back into the system. By the end of just three runs, I reached 97% conversion – a result I'm extremely pleased with as this outcome exceeded my initial expectations. What impressed me most was how user-friendly the tool is, especially for someone like me who isn't very tech-savvy. The explanatory information beneath the working menus was invaluable when I was getting started, making the whole process much more accessible. This tool has been a game-changer for my optimization work. It saved me a significant amount of time compared to my previous manual approach of making my own experimental suggestions. I'll definitely continue using it for future optimization projects – the efficiency gains are simply too valuable to ignore.",
    highlight: "This tool has been a game-changer for my optimization work",
    type: "user"
  },
  {
    id: 2,
    name: "Merck KGaA, Darmstadt, Germany",
    role: "",
    company: "Merck KGaA, Darmstadt, Germany",
    logo: null,
    image: null,
    testimonial:
      "INNOptimizer™ provides comprehensive capabilities for experimental planning and visual analysis whilst maintaining a smooth user experience.",
    highlight:
      "Comprehensive capabilities for experimental planning and visual analysis",
    type: "company"
  }
]

// User types with their benefits
const userTypes = [
  {
    title: "Research Scientists",
    description:
      "Stretch limited lab resources further with smart experiment designs that deliver maximum insights from every test.",
    icon: <Beaker className="text-primary size-8" />
  },
  {
    title: "Materials Scientists",
    description:
      "Explore complex material designs efficiently to discover novel materials with targeted properties, cutting development timelines by 40-60%.",
    icon: <FlaskConical className="text-primary size-8" />
  },
  {
    title: "Process Engineers",
    description:
      "Scale lab discoveries to production with robust optimization that tackles real-world constraints and variability.",
    icon: <Gauge className="text-primary size-8" />
  },
  {
    title: "R&D Teams",
    description:
      "Accelerate innovation cycles and reduce time-to-market with data-driven optimization strategies.",
    icon: <Rocket className="text-primary size-8" />
  }
]

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 10
    }
  }
}

const profileVariants = {
  inactive: {
    scale: 1,
    y: 0,
    opacity: 0.7
  },
  hover: {
    scale: 1.05,
    y: -2,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 15
    }
  },
  active: {
    scale: 1.02,
    y: -1,
    opacity: 1
  }
}

export function TestimonialsShowcase() {
  const [activeTestimonial, setActiveTestimonial] = useState(0)

  // Auto-rotate testimonials every 5 seconds (only if more than one testimonial)
  useEffect(() => {
    if (testimonials.length <= 1) return

    const interval = setInterval(() => {
      setActiveTestimonial(prev => (prev + 1) % testimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  return (
    <Section className="from-background bg-gradient-to-b to-blue-50/30 py-16 md:py-24">
      <div className="container mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-16 text-center"
        >
          <h2 className="from-primary mb-4 bg-gradient-to-r to-blue-600 bg-clip-text text-3xl font-bold text-transparent md:text-4xl">
            Trusted by Leading Researchers
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-base md:text-lg">
            See how scientists and engineers worldwide are accelerating their
            research and achieving breakthrough results with our optimization
            platform.
          </p>
        </motion.div>

        {/* Equal Width Layout: Testimonials + Features */}
        <div className="grid gap-8 lg:grid-cols-2 lg:gap-12">
          {/* Left Column - Testimonials */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="order-2 lg:order-1"
          >
            <h3 className="from-primary mb-8 bg-gradient-to-r to-blue-600 bg-clip-text text-2xl font-semibold text-transparent">
              What Our Users Say
            </h3>

            <div className="mb-8">
              <motion.div
                className="h-80 max-w-lg rounded-lg border border-gray-200 p-6 transition-all duration-300 hover:border-blue-800 md:h-96 md:p-8"
                whileHover={{ borderColor: "rgb(30 58 138)" }}
              >
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeTestimonial}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                  >
                    {/* Testimonial Text - Scrollable within fixed height */}
                    <motion.blockquote
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2 }}
                      className="mb-6 h-48 overflow-y-auto text-left text-xs font-medium leading-relaxed text-gray-900 md:h-56 md:text-sm"
                    >
                      <div className="pr-2">
                        "{testimonials[activeTestimonial].testimonial}"
                      </div>
                    </motion.blockquote>

                    {/* Author Info */}
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-3">
                        <div className="from-primary/20 size-12 overflow-hidden rounded-full bg-gradient-to-r to-blue-600/20 p-0.5">
                          {testimonials[activeTestimonial].type ===
                          "company" ? (
                            <div className="flex size-full items-center justify-center rounded-full bg-gradient-to-br from-blue-500 via-purple-500 to-teal-500">
                              <div className="text-lg font-bold text-white">
                                {testimonials[activeTestimonial].company
                                  .split(" ")[0]
                                  .charAt(0)}
                              </div>
                            </div>
                          ) : (
                            <img
                              src={testimonials[activeTestimonial].image || ""}
                              alt={testimonials[activeTestimonial].name}
                              className="size-full rounded-full bg-white object-cover"
                            />
                          )}
                        </div>
                        <div className="text-left">
                          <h4 className="font-semibold text-gray-900">
                            {testimonials[activeTestimonial].name}
                          </h4>
                          {testimonials[activeTestimonial].role && (
                            <p className="text-sm text-gray-600">
                              {testimonials[activeTestimonial].role}
                            </p>
                          )}
                          {testimonials[activeTestimonial].type !==
                            "company" && (
                            <p className="text-xs text-gray-500">
                              {testimonials[activeTestimonial].company}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Company Logo - Only show for non-company testimonials */}
                      {testimonials[activeTestimonial].type !== "company" && (
                        <div className="hidden md:block">
                          <div className="from-primary/10 flex h-8 w-24 items-center justify-center rounded bg-gradient-to-r to-blue-600/10 p-1">
                            {testimonials[activeTestimonial].logo ? (
                              <img
                                src={testimonials[activeTestimonial].logo}
                                alt={`${testimonials[activeTestimonial].company} logo`}
                                className="h-full w-auto object-contain"
                              />
                            ) : (
                              <span className="text-xs font-medium text-gray-600">
                                {
                                  testimonials[activeTestimonial].company.split(
                                    " "
                                  )[0]
                                }
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  </motion.div>
                </AnimatePresence>
              </motion.div>
            </div>

            {/* Profile Images Grid - Same width as testimonial box */}
            <div className="from-primary/5 max-w-lg rounded-xl bg-gradient-to-r to-blue-600/5 p-3 shadow-sm">
              <div className="grid grid-cols-5 gap-2 md:grid-cols-10">
                {testimonials.map((testimonial, index) => (
                  <motion.button
                    key={testimonial.id}
                    variants={profileVariants}
                    initial="inactive"
                    animate={
                      activeTestimonial === index ? "active" : "inactive"
                    }
                    whileHover="hover"
                    onClick={() => setActiveTestimonial(index)}
                    className="group relative focus:outline-none"
                  >
                    <div
                      className={`from-primary/20 size-8 overflow-hidden rounded-full bg-gradient-to-r to-blue-600/20 p-0.5 transition-all duration-500 md:size-10 ${
                        activeTestimonial === index
                          ? "ring-primary/60 shadow-primary/30 shadow-lg ring-2 ring-offset-1"
                          : "hover:ring-primary/20 hover:ring-2 hover:ring-offset-1"
                      }`}
                    >
                      {testimonial.type === "company" ? (
                        <div className="flex size-full items-center justify-center rounded-full bg-gradient-to-br from-blue-500 via-purple-500 to-teal-500">
                          <div className="text-xs font-bold text-white md:text-sm">
                            {testimonial.company.split(" ")[0].charAt(0)}
                          </div>
                        </div>
                      ) : (
                        <img
                          src={testimonial.image || ""}
                          alt={testimonial.name}
                          className="size-full rounded-full bg-white object-cover"
                        />
                      )}
                    </div>

                    {/* Tooltip */}
                    <motion.div
                      initial={{ opacity: 0, y: 10, scale: 0.8 }}
                      whileHover={{ opacity: 1, y: 0, scale: 1 }}
                      className="absolute -top-8 left-1/2 z-10 -translate-x-1/2 whitespace-nowrap rounded bg-gray-900 px-2 py-1 text-xs text-white shadow-lg"
                    >
                      {testimonial.type === "company"
                        ? testimonial.company.split(",")[0]
                        : testimonial.name.split(" ")[0]}
                      <div className="absolute -bottom-1 left-1/2 size-1.5 -translate-x-1/2 rotate-45 bg-gray-900"></div>
                    </motion.div>
                  </motion.button>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Right Column - Features */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="order-1 flex flex-col lg:order-2"
          >
            <h3 className="from-primary mb-8 bg-gradient-to-r to-blue-600 bg-clip-text text-2xl font-semibold text-transparent">
              Why Choose Our Platform?
            </h3>

            {/* Features Cards - Carousel Style */}
            <div className="space-y-4">
              {userTypes.map((userType, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{
                    y: -6,
                    scale: 1.02,
                    transition: { duration: 0.3, ease: "easeOut" }
                  }}
                  whileTap={{ y: 0, scale: 1 }}
                  className="group relative overflow-hidden rounded-2xl border border-white/40 bg-gradient-to-br from-white/95 via-gray-50/90 to-white/95 p-4 shadow-xl backdrop-blur-lg transition-all duration-700 hover:-translate-y-2 hover:shadow-2xl hover:shadow-blue-500/30"
                >
                  <div className="relative z-10 flex items-start gap-3">
                    <div className="bg-primary/10 flex size-10 shrink-0 items-center justify-center rounded-full transition-all duration-500 group-hover:scale-110 group-hover:bg-blue-500/20">
                      {React.cloneElement(userType.icon, {
                        className:
                          "text-primary size-5 transition-all duration-500 group-hover:text-blue-700"
                      })}
                    </div>
                    <div className="flex-1">
                      <h4 className="mb-2 text-base font-semibold transition-all duration-500 group-hover:scale-105 group-hover:text-blue-600">
                        {userType.title}
                      </h4>
                      <p className="text-muted-foreground group-hover:text-foreground/90 text-xs leading-relaxed transition-all duration-500">
                        {userType.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Call to Action - Full Width Below Columns */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mt-16 flex items-start justify-between"
        >
          <div className="text-left">
            <h3 className="mb-2 text-2xl font-semibold md:text-3xl">
              Ready to Transform Your Research?
            </h3>
            <p className="from-primary mb-3 bg-gradient-to-r to-blue-600 bg-clip-text text-lg font-medium text-transparent">
              Quick wins in every experimental test.
            </p>
            <p className="text-muted-foreground max-w-2xl text-base md:text-lg">
              Join leading labs accelerating discovery with us. Explore{" "}
              {BRAND.NAME} with complementary rights by accessing the Dashboard.
            </p>
          </div>

          <div className="ml-8 flex shrink-0 flex-col gap-4">
            {/* Get Started Button - Aligned with heading */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                asChild
                size="lg"
                className="from-primary hover:from-primary/90 group bg-gradient-to-r to-blue-600 px-8 py-4 text-lg shadow-lg transition-all hover:to-blue-700 hover:shadow-xl"
              >
                <Link href="/dashboard">
                  Get Started
                  <ArrowRight className="ml-2 size-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
            </motion.div>

            {/* Contact Us Button */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="group border border-gray-200 bg-white px-8 py-4 text-lg text-gray-700 shadow-md transition-all duration-300 hover:border-blue-800 hover:bg-gray-50 hover:shadow-lg"
              >
                <Link href="/contact">
                  Contact Us
                  <MessageSquare className="ml-2 size-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </Section>
  )
}
