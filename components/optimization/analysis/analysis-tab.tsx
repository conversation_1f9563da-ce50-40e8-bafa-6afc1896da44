"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  BarChart3,
  Grid3X3,
  <PERSON><PERSON>,
  GitB<PERSON>ch,
  Clock,
  Lightbulb,
  Area<PERSON>hart
} from "lucide-react"
import { SurfaceAnalysis } from "@/components/optimization/surface-analysis"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"

// Placeholder components for other analysis sections
import { OverviewDashboard } from "./overview-dashboard"
import { ParameterAnalysis } from "./parameter-analysis"
import { ResponseSurfaceAnalysis } from "./response-surface-analysis"
import { MultiTargetAnalysis } from "./multi-target-analysis"
import { ExperimentTimeline } from "./experiment-timeline"
import { AdvancedAnalytics } from "./advanced-analytics"
import { SobolContourPlot } from "./sobol-contour-plot"
import { ConvergenceAnalysis } from "./convergence-analysis"

interface AnalysisTabProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  parameterNames: string[]
  isMultiTarget: boolean
  otherTargets: string[]
  initialSection?: string
}

export function AnalysisTab({
  optimization,
  measurements,
  parameterNames,
  isMultiTarget,
  otherTargets,
  initialSection = "parameters"
}: AnalysisTabProps) {
  const [activeSection, setActiveSection] = useState(initialSection)

  return (
    <div className="space-y-6">
      <Tabs
        value={activeSection}
        onValueChange={setActiveSection}
        className="w-full"
      >
        <TabsList className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-6">
          <TabsTrigger value="parameters" className="flex items-center">
            <BarChart3 className="mr-2 size-4" />
            <span className="hidden sm:inline">Parameters</span>
          </TabsTrigger>
          <TabsTrigger value="surface" className="flex items-center">
            <Layers className="mr-2 size-4" />
            <span className="hidden sm:inline">Surface</span>
          </TabsTrigger>
          <TabsTrigger value="sobol" className="flex items-center">
            <Grid3X3 className="mr-2 size-4" />
            <span className="hidden sm:inline">SOBOL</span>
          </TabsTrigger>
          <TabsTrigger value="convergence" className="flex items-center">
            <AreaChart className="mr-2 size-4" />
            <span className="hidden sm:inline">Convergence</span>
          </TabsTrigger>
          <TabsTrigger value="multi-target" className="flex items-center">
            <GitBranch className="mr-2 size-4" />
            <span className="hidden sm:inline">Multi-Target</span>
          </TabsTrigger>
          <TabsTrigger value="timeline" className="flex items-center">
            <Clock className="mr-2 size-4" />
            <span className="hidden sm:inline">Timeline</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Dashboard removed - now part of main Overview tab */}

        {/* Parameter Analysis */}
        <TabsContent value="parameters" className="space-y-6">
          <ParameterAnalysis
            optimization={optimization}
            measurements={measurements}
            parameterNames={parameterNames}
          />
        </TabsContent>

        {/* Response Surface Analysis */}
        <TabsContent value="surface" className="space-y-6">
          <ResponseSurfaceAnalysis
            optimization={optimization}
            measurements={measurements}
            parameterNames={parameterNames}
            targetName={optimization.targetName}
            targetMode={optimization.targetMode as "MAX" | "MIN" | "MULTI"}
            availableTargets={
              isMultiTarget ? [optimization.targetName, ...otherTargets] : []
            }
          />
        </TabsContent>

        {/* SOBOL Contour Analysis */}
        <TabsContent value="sobol" className="space-y-6">
          <SobolContourPlot
            optimization={optimization}
            parameterNames={parameterNames}
            targetMode={optimization.targetMode as "MAX" | "MIN" | "MULTI"}
            availableTargets={
              isMultiTarget ? [optimization.targetName, ...otherTargets] : []
            }
          />
        </TabsContent>

        {/* Convergence Analysis */}
        <TabsContent value="convergence" className="space-y-6">
          <ConvergenceAnalysis
            optimization={optimization}
            measurements={measurements}
            targetName={optimization.targetName}
            targetMode={optimization.targetMode as "MAX" | "MIN" | "MULTI"}
            isMultiTarget={isMultiTarget}
            otherTargets={otherTargets}
          />
        </TabsContent>

        {/* Multi-Target Analysis */}
        <TabsContent value="multi-target" className="space-y-6">
          <MultiTargetAnalysis
            optimization={optimization}
            measurements={measurements}
            isMultiTarget={isMultiTarget}
            targetName={optimization.targetName}
            otherTargets={otherTargets}
          />
        </TabsContent>

        {/* Experiment Timeline */}
        <TabsContent value="timeline" className="space-y-6">
          <ExperimentTimeline
            optimization={optimization}
            measurements={measurements}
          />
        </TabsContent>

        {/* Advanced Analytics */}
        <TabsContent value="advanced" className="space-y-6">
          <AdvancedAnalytics
            optimization={optimization}
            measurements={measurements}
            parameterNames={parameterNames}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
