import {
  ConfigurationVersion,
  Configuration<PERSON><PERSON>e,
  VersionConflict,
  ConflictResolution,
  VersionDiff,
  VersionQuery,
  VersionLock,
  VersionEvent,
  ChangeImpact,
  VersionMetadata
} from "./types"

export class ConfigurationVersionManager {
  private static instance: ConfigurationVersionManager
  private versions: Map<string, ConfigurationVersion[]> = new Map()
  private conflicts: Map<string, VersionConflict[]> = new Map()
  private locks: Map<string, VersionLock[]> = new Map()
  private events: VersionEvent[] = []

  private constructor() {}

  static getInstance(): ConfigurationVersionManager {
    if (!ConfigurationVersionManager.instance) {
      ConfigurationVersionManager.instance = new ConfigurationVersionManager()
    }
    return ConfigurationVersionManager.instance
  }

  /**
   * Create a new configuration version
   */
  async createVersion(
    optimizationId: string,
    configuration: any,
    changes: ConfigurationChange[],
    author: { userId: string; userName: string; userEmail?: string },
    changeSummary: string,
    parentVersion?: number
  ): Promise<ConfigurationVersion> {
    const versions = this.getVersions(optimizationId)
    const newVersionNumber = Math.max(0, ...versions.map(v => v.version)) + 1

    const version: ConfigurationVersion = {
      id: this.generateVersionId(),
      optimizationId,
      version: newVersionNumber,
      parentVersion,
      timestamp: new Date(),
      author,
      changeType: parentVersion ? "update" : "create",
      changeSummary,
      changeDetails: changes,
      configuration: this.deepClone(configuration),
      metadata: this.createVersionMetadata(),
      tags: [],
      isActive: true,
      isMerged: false
    }

    // Deactivate previous active version
    versions.forEach(v => {
      if (v.isActive) v.isActive = false
    })

    versions.push(version)
    this.versions.set(optimizationId, versions)

    // Check for conflicts
    await this.detectConflicts(optimizationId, version)

    // Record event
    this.recordEvent({
      id: this.generateEventId(),
      optimizationId,
      version: newVersionNumber,
      eventType: "created",
      timestamp: new Date(),
      actor: { userId: author.userId, userName: author.userName },
      details: { changeSummary, changesCount: changes.length },
      metadata: { source: "version_manager" }
    })

    return version
  }

  /**
   * Get all versions for an optimization
   */
  getVersions(optimizationId: string): ConfigurationVersion[] {
    return this.versions.get(optimizationId) || []
  }

  /**
   * Get a specific version
   */
  getVersion(
    optimizationId: string,
    version: number
  ): ConfigurationVersion | null {
    const versions = this.getVersions(optimizationId)
    return versions.find(v => v.version === version) || null
  }

  /**
   * Get the active version
   */
  getActiveVersion(optimizationId: string): ConfigurationVersion | null {
    const versions = this.getVersions(optimizationId)
    return versions.find(v => v.isActive) || null
  }

  /**
   * Get the latest version
   */
  getLatestVersion(optimizationId: string): ConfigurationVersion | null {
    const versions = this.getVersions(optimizationId)
    if (versions.length === 0) return null

    return versions.reduce((latest, current) =>
      current.version > latest.version ? current : latest
    )
  }

  /**
   * Compare two versions and generate diff
   */
  compareVersions(
    optimizationId: string,
    fromVersion: number,
    toVersion: number
  ): VersionDiff | null {
    const from = this.getVersion(optimizationId, fromVersion)
    const to = this.getVersion(optimizationId, toVersion)

    if (!from || !to) return null

    const changes = this.calculateChanges(from.configuration, to.configuration)

    return {
      fromVersion,
      toVersion,
      changes,
      summary: {
        totalChanges: changes.length,
        addedItems: changes.filter(c => c.action === "add").length,
        removedItems: changes.filter(c => c.action === "remove").length,
        modifiedItems: changes.filter(c => c.action === "modify").length,
        impactLevel: this.calculateImpactLevel(changes)
      },
      compatibility: {
        isBackwardCompatible: this.isBackwardCompatible(changes),
        isForwardCompatible: this.isForwardCompatible(changes),
        migrationRequired: this.requiresMigration(changes),
        migrationSteps: this.generateMigrationSteps(changes)
      }
    }
  }

  /**
   * Rollback to a previous version
   */
  async rollbackToVersion(
    optimizationId: string,
    targetVersion: number,
    author: { userId: string; userName: string }
  ): Promise<ConfigurationVersion> {
    const targetVersionObj = this.getVersion(optimizationId, targetVersion)
    if (!targetVersionObj) {
      throw new Error(`Version ${targetVersion} not found`)
    }

    const currentVersion = this.getActiveVersion(optimizationId)
    const changes = currentVersion
      ? this.calculateChanges(
          currentVersion.configuration,
          targetVersionObj.configuration
        )
      : []

    const rollbackVersion = await this.createVersion(
      optimizationId,
      targetVersionObj.configuration,
      changes,
      author,
      `Rollback to version ${targetVersion}`,
      currentVersion?.version
    )

    rollbackVersion.changeType = "rollback"

    return rollbackVersion
  }

  /**
   * Detect conflicts between versions
   */
  async detectConflicts(
    optimizationId: string,
    newVersion: ConfigurationVersion
  ): Promise<VersionConflict[]> {
    const versions = this.getVersions(optimizationId)
    const conflicts: VersionConflict[] = []

    // Check for concurrent edits (versions created around the same time)
    const recentVersions = versions.filter(
      v =>
        v.version !== newVersion.version &&
        Math.abs(v.timestamp.getTime() - newVersion.timestamp.getTime()) <
          300000 // 5 minutes
    )

    for (const recentVersion of recentVersions) {
      const conflict = this.checkForConflict(newVersion, recentVersion)
      if (conflict) {
        conflicts.push(conflict)
      }
    }

    // Store conflicts
    const existingConflicts = this.conflicts.get(optimizationId) || []
    this.conflicts.set(optimizationId, [...existingConflicts, ...conflicts])

    return conflicts
  }

  /**
   * Check for conflict between two versions
   */
  private checkForConflict(
    version1: ConfigurationVersion,
    version2: ConfigurationVersion
  ): VersionConflict | null {
    const conflictFields = []

    // Compare configurations and find conflicting fields
    const changes1 = version1.changeDetails
    const changes2 = version2.changeDetails

    for (const change1 of changes1) {
      for (const change2 of changes2) {
        if (
          change1.field === change2.field &&
          JSON.stringify(change1.newValue) !== JSON.stringify(change2.newValue)
        ) {
          conflictFields.push({
            field: change1.field,
            baseValue: change1.oldValue,
            conflictingValues: [
              {
                version: version1.version,
                value: change1.newValue,
                author: version1.author.userName,
                timestamp: version1.timestamp
              },
              {
                version: version2.version,
                value: change2.newValue,
                author: version2.author.userName,
                timestamp: version2.timestamp
              }
            ],
            conflictReason: "Concurrent modification of the same field",
            suggestedResolution: "use_latest" as const
          })
        }
      }
    }

    if (conflictFields.length === 0) return null

    return {
      id: this.generateConflictId(),
      optimizationId: version1.optimizationId,
      baseVersion: Math.min(
        version1.parentVersion || 0,
        version2.parentVersion || 0
      ),
      conflictingVersions: [version1.version, version2.version],
      conflictType: "concurrent_edit",
      conflictFields,
      detectedAt: new Date(),
      status: "pending"
    }
  }

  /**
   * Resolve a conflict
   */
  async resolveConflict(
    conflictId: string,
    resolution: ConflictResolution
  ): Promise<void> {
    // Find the conflict
    for (const [optimizationId, conflicts] of this.conflicts.entries()) {
      const conflict = conflicts.find(c => c.id === conflictId)
      if (conflict) {
        conflict.status = "resolved"
        conflict.resolution = resolution

        // Record event
        this.recordEvent({
          id: this.generateEventId(),
          optimizationId,
          version: 0, // Conflict resolution doesn't have a specific version
          eventType: "conflict_resolved",
          timestamp: new Date(),
          actor: {
            userId: resolution.resolvedBy.userId,
            userName: resolution.resolvedBy.userName
          },
          details: {
            conflictId,
            resolutionStrategy: resolution.resolutionStrategy
          },
          metadata: { source: "conflict_resolver" }
        })

        break
      }
    }
  }

  /**
   * Get conflicts for an optimization
   */
  getConflicts(optimizationId: string): VersionConflict[] {
    return this.conflicts.get(optimizationId) || []
  }

  /**
   * Lock a version for editing
   */
  async lockVersion(
    optimizationId: string,
    version: number,
    lockInfo: {
      userId: string
      userName: string
      sessionId: string
      lockReason: string
      lockType: "read" | "write" | "exclusive"
      durationMinutes?: number
    }
  ): Promise<VersionLock> {
    const lock: VersionLock = {
      id: this.generateLockId(),
      optimizationId,
      version,
      lockedBy: {
        userId: lockInfo.userId,
        userName: lockInfo.userName,
        sessionId: lockInfo.sessionId
      },
      lockedAt: new Date(),
      lockType: lockInfo.lockType,
      lockReason: lockInfo.lockReason,
      expiresAt: new Date(
        Date.now() + (lockInfo.durationMinutes || 30) * 60000
      ),
      isActive: true
    }

    const locks = this.locks.get(optimizationId) || []
    locks.push(lock)
    this.locks.set(optimizationId, locks)

    return lock
  }

  /**
   * Release a version lock
   */
  async unlockVersion(lockId: string): Promise<void> {
    for (const [optimizationId, locks] of this.locks.entries()) {
      const lock = locks.find(l => l.id === lockId)
      if (lock) {
        lock.isActive = false
        break
      }
    }
  }

  /**
   * Check if a version is locked
   */
  isVersionLocked(optimizationId: string, version: number): VersionLock | null {
    const locks = this.locks.get(optimizationId) || []
    return (
      locks.find(
        l => l.version === version && l.isActive && l.expiresAt > new Date()
      ) || null
    )
  }

  /**
   * Query versions with filters
   */
  queryVersions(query: VersionQuery): ConfigurationVersion[] {
    let results: ConfigurationVersion[] = []

    if (query.optimizationId) {
      results = this.getVersions(query.optimizationId)
    } else {
      // Get all versions from all optimizations
      for (const versions of this.versions.values()) {
        results.push(...versions)
      }
    }

    // Apply filters
    if (query.authorId) {
      results = results.filter(v => v.author.userId === query.authorId)
    }

    if (query.fromDate) {
      results = results.filter(v => v.timestamp >= query.fromDate!)
    }

    if (query.toDate) {
      results = results.filter(v => v.timestamp <= query.toDate!)
    }

    if (query.changeType) {
      results = results.filter(v => query.changeType!.includes(v.changeType))
    }

    if (query.tags && query.tags.length > 0) {
      results = results.filter(v =>
        query.tags!.some(tag => v.tags.includes(tag))
      )
    }

    if (query.isActive !== undefined) {
      results = results.filter(v => v.isActive === query.isActive)
    }

    // Sort results
    if (query.sortBy) {
      results.sort((a, b) => {
        let aVal: any, bVal: any

        switch (query.sortBy) {
          case "timestamp":
            aVal = a.timestamp.getTime()
            bVal = b.timestamp.getTime()
            break
          case "version":
            aVal = a.version
            bVal = b.version
            break
          case "author":
            aVal = a.author.userName
            bVal = b.author.userName
            break
          default:
            return 0
        }

        if (query.sortOrder === "desc") {
          return bVal > aVal ? 1 : bVal < aVal ? -1 : 0
        } else {
          return aVal > bVal ? 1 : aVal < bVal ? -1 : 0
        }
      })
    }

    // Apply pagination
    if (query.offset || query.limit) {
      const start = query.offset || 0
      const end = query.limit ? start + query.limit : undefined
      results = results.slice(start, end)
    }

    return results
  }

  // Helper methods
  private generateVersionId(): string {
    return `version_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateConflictId(): string {
    return `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateLockId(): string {
    return `lock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private createVersionMetadata(): VersionMetadata {
    return {
      source: "ui",
      environment: (process.env.NODE_ENV as any) || "development",
      clientVersion: process.env.NEXT_PUBLIC_APP_VERSION || "1.0.0",
      serverVersion: "1.0.0",
      sessionId: this.getSessionId(),
      requestId: this.generateRequestId(),
      checksum: this.generateChecksum(),
      size: 0,
      compressionUsed: false
    }
  }

  private getSessionId(): string {
    if (typeof window !== "undefined") {
      let sessionId = sessionStorage.getItem("version_session_id")
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        sessionStorage.setItem("version_session_id", sessionId)
      }
      return sessionId
    }
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateChecksum(): string {
    return Math.random().toString(36).substr(2, 16)
  }

  private deepClone(obj: any): any {
    return JSON.parse(JSON.stringify(obj))
  }

  private recordEvent(event: VersionEvent): void {
    this.events.push(event)

    // Keep only the last 10000 events
    if (this.events.length > 10000) {
      this.events = this.events.slice(-10000)
    }
  }

  private calculateChanges(
    oldConfig: any,
    newConfig: any
  ): ConfigurationChange[] {
    // Simplified change calculation - would need more sophisticated diff logic
    const changes: ConfigurationChange[] = []

    // This is a placeholder - real implementation would do deep comparison
    if (JSON.stringify(oldConfig) !== JSON.stringify(newConfig)) {
      changes.push({
        id: this.generateVersionId(),
        type: "parameter",
        action: "modify",
        field: "configuration",
        oldValue: oldConfig,
        newValue: newConfig,
        impact: {
          measurementsAffected: 0,
          dataLossRisk: "low",
          performanceImpact: "minimal",
          compatibilityImpact: "minor",
          estimatedDuration: 1000
        },
        validation: {
          isValid: true,
          errors: [],
          warnings: [],
          recommendations: []
        }
      })
    }

    return changes
  }

  private calculateImpactLevel(
    changes: ConfigurationChange[]
  ): "low" | "medium" | "high" {
    const highImpactChanges = changes.filter(
      c =>
        c.impact.compatibilityImpact === "breaking" ||
        c.impact.dataLossRisk === "high"
    )

    if (highImpactChanges.length > 0) return "high"
    if (changes.length > 10) return "medium"
    return "low"
  }

  private isBackwardCompatible(changes: ConfigurationChange[]): boolean {
    return !changes.some(c => c.impact.compatibilityImpact === "breaking")
  }

  private isForwardCompatible(changes: ConfigurationChange[]): boolean {
    return !changes.some(c => c.action === "remove")
  }

  private requiresMigration(changes: ConfigurationChange[]): boolean {
    return changes.some(c => c.impact.compatibilityImpact === "major")
  }

  private generateMigrationSteps(changes: ConfigurationChange[]): string[] {
    return changes
      .filter(c => c.impact.compatibilityImpact === "major")
      .map(c => `Migrate ${c.field} from ${c.oldValue} to ${c.newValue}`)
  }
}

// Export singleton instance
export const versionManager = ConfigurationVersionManager.getInstance()
