"use client"

import { useCookieConsent } from "@/lib/hooks/use-cookie-consent"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Settings, Shield, CheckCircle, XCircle } from "lucide-react"

/**
 * Cookie Consent Manager Component
 *
 * This component demonstrates how to use the useCookieConsent hook
 * and provides a UI for managing cookie preferences.
 */
export function CookieConsentManager() {
  const {
    consentState,
    acceptAll,
    rejectAll,
    showSettings,
    hasAnalyticsConsent,
    hasMarketingConsent,
    hasFunctionalConsent,
    isLoaded
  } = useCookieConsent()

  if (!isLoaded) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="size-5" />
            Cookie Preferences
          </CardTitle>
          <CardDescription>Loading cookie consent manager...</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="size-5" />
          Cookie Preferences
        </CardTitle>
        <CardDescription>
          Manage your cookie consent preferences
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Consent Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Current Consent Status:</h4>
          <div className="grid grid-cols-2 gap-2">
            <ConsentBadge
              label="Necessary"
              granted={consentState.necessary}
              required
            />
            <ConsentBadge label="Functional" granted={hasFunctionalConsent} />
            <ConsentBadge label="Analytics" granted={hasAnalyticsConsent} />
            <ConsentBadge label="Marketing" granted={hasMarketingConsent} />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2">
          <Button onClick={acceptAll} className="w-full" variant="default">
            Accept All Cookies
          </Button>

          <Button onClick={rejectAll} className="w-full" variant="outline">
            Reject Optional Cookies
          </Button>

          <Button onClick={showSettings} className="w-full" variant="ghost">
            <Settings className="mr-2 size-4" />
            Customize Settings
          </Button>
        </div>

        {/* Information */}
        <div className="text-muted-foreground text-xs">
          <p>
            Necessary cookies are always enabled to ensure basic website
            functionality. You can change your preferences at any time.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

interface ConsentBadgeProps {
  label: string
  granted: boolean
  required?: boolean
}

function ConsentBadge({ label, granted, required = false }: ConsentBadgeProps) {
  return (
    <div className="flex items-center justify-between rounded-md border p-2">
      <span className="text-sm font-medium">{label}</span>
      <div className="flex items-center gap-1">
        {required && (
          <Badge variant="secondary" className="text-xs">
            Required
          </Badge>
        )}
        {granted ? (
          <CheckCircle className="size-4 text-green-500" />
        ) : (
          <XCircle className="size-4 text-red-500" />
        )}
      </div>
    </div>
  )
}
