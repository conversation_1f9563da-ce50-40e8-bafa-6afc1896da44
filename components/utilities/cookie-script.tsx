"use client"

import Script from "next/script"

interface CookieScriptProps {
  cookieScriptId?: string
  enableGoogleConsentMode?: boolean
  enableDebugMode?: boolean
}

export function CookieScript({
  cookieScriptId,
  enableGoogleConsentMode = true,
  enableDebugMode = false
}: CookieScriptProps) {
  // Get CookieScript ID from environment variable or props
  const scriptId = cookieScriptId || process.env.NEXT_PUBLIC_COOKIESCRIPT_ID

  // Don't render if no script ID is provided
  if (!scriptId || scriptId === "your_cookiescript_id_here") {
    if (enableDebugMode) {
      console.warn(
        "CookieScript: No valid script ID provided. Please set NEXT_PUBLIC_COOKIESCRIPT_ID in your environment variables."
      )
    }
    return null
  }

  return (
    <>
      {/* Google Consent Mode initialization (if enabled) */}
      {enableGoogleConsentMode && (
        <Script
          id="google-consent-mode-init"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}

              // Set default consent state
              gtag('consent', 'default', {
                'analytics_storage': 'denied',
                'ad_storage': 'denied',
                'ad_user_data': 'denied',
                'ad_personalization': 'denied',
                'functionality_storage': 'granted',
                'security_storage': 'granted'
              });

              // Ensure Clerk authentication cookies are always allowed
              // These are essential for the application to function
              if (typeof window !== 'undefined' && window.CookieScript) {
                window.CookieScript.instance.config.essentialCookies = [
                  '__clerk_db_jwt',
                  '__session',
                  '__clerk_*',
                  '_clerk_*'
                ];
              }
            `
          }}
        />
      )}

      {/* Pre-configure CookieScript before it loads */}
      <Script
        id="cookiescript-preconfig"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Configure CookieScript before it loads
            window.CookieScriptConfig = {
              essentialCookies: [
                '__clerk_db_jwt',
                '__clerk_db_jwt_*',
                '__session',
                '__session_*',
                'clerk_active_context',
                '__clerk_session',
                '_clerk_session'
              ],
              // Allow all cookies by default in development
              defaultConsent: ${process.env.NODE_ENV === "development" ? "true" : "false"}
            };
          `
        }}
      />

      {/* CookieScript widget */}
      <Script
        id="cookiescript"
        src={`https://cdn.cookie-script.com/s/${scriptId}.js`}
        strategy="afterInteractive"
        onLoad={() => {
          // Configure essential cookies for authentication
          if (typeof window !== "undefined" && window.CookieScript) {
            try {
              // Configure Clerk authentication cookies as essential
              const clerkCookiePatterns = [
                "__clerk_db_jwt",
                "__session",
                "clerk_active_context",
                "__clerk_session",
                "_clerk_session",
                "__clerk_refresh_token",
                "_clerk_refresh_token"
              ]

              // Try multiple approaches to ensure cookies are not blocked
              if (window.CookieScript.instance) {
                // Method 1: Add to essential cookies
                clerkCookiePatterns.forEach(cookieName => {
                  try {
                    window.CookieScript.instance.addEssentialCookie(cookieName)
                    // Also try with wildcard patterns
                    window.CookieScript.instance.addEssentialCookie(
                      cookieName + "_*"
                    )
                  } catch (e) {
                    console.warn(`Could not add ${cookieName} as essential:`, e)
                  }
                })

                // Method 2: Override the cookie blocking function
                const originalBlockCookie =
                  window.CookieScript.instance.blockCookie
                if (originalBlockCookie) {
                  window.CookieScript.instance.blockCookie = function (
                    cookieName: string
                  ) {
                    // Don't block Clerk authentication cookies
                    const isClerkCookie = clerkCookiePatterns.some(
                      pattern =>
                        cookieName.includes(pattern) ||
                        cookieName.startsWith("__clerk") ||
                        cookieName.startsWith("clerk_")
                    )

                    if (isClerkCookie) {
                      return false // Don't block
                    }

                    // Use original function for other cookies
                    return originalBlockCookie.call(this, cookieName)
                  }
                }
              }
            } catch (error) {
              console.warn(
                "CookieScript: Could not configure essential cookies:",
                error
              )
            }

            // Listen for consent changes
            window.addEventListener("CookieScriptAccept", () => {
              if (enableDebugMode) {
                console.log("CookieScript: User accepted cookies")
              }
            })

            window.addEventListener("CookieScriptReject", () => {
              if (enableDebugMode) {
                console.log("CookieScript: User rejected cookies")
              }
            })
          }
        }}
        onError={e => {
          console.error("Failed to load CookieScript:", e)
        }}
      />
    </>
  )
}
