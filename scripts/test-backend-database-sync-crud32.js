#!/usr/bin/env node

/**
 * Test script to verify the backend-database synchronization issue
 * This will test the theory that backend campaigns don't load measurements from database
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, and, desc } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema definitions
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

const measurementsTable = pgTable("measurements", {
  id: uuid("id").primaryKey(),
  optimizationId: uuid("optimization_id").notNull(),
  parameters: jsonb("parameters").notNull(),
  targetValue: text("target_value").notNull(),
  targetValues: jsonb("target_values"),
  isRecommended: boolean("is_recommended").notNull(),
  batchId: text("batch_id"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function testBackendDatabaseSync() {
  console.log('🔍 TESTING BACKEND-DATABASE SYNCHRONIZATION');
  console.log('===========================================');

  let client;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    // Find the optimization
    const optimizationName = "test CRUD filtering32";
    const optimizationsList = await db.select().from(optimizationsTable);
    const opt = optimizationsList.find(o => o.name === optimizationName);
    
    if (!opt) {
      console.error(`❌ Optimization "${optimizationName}" not found`);
      return;
    }
    
    console.log(`✅ Found optimization: ${opt.optimizerId}`);
    
    // Get database measurements
    const dbMeasurements = await db.select().from(measurementsTable)
      .where(eq(measurementsTable.optimizationId, opt.id))
      .orderBy(desc(measurementsTable.createdAt));
    
    console.log(`\n📊 DATABASE STATE:`);
    console.log(`Database measurements: ${dbMeasurements.length}`);
    
    // Show Target 1 values from database
    const dbTarget1Values = dbMeasurements.map(m => {
      if (m.targetValues && typeof m.targetValues === 'object') {
        return m.targetValues['Target 1'];
      }
      return parseFloat(m.targetValue);
    }).filter(v => v !== undefined);
    
    console.log(`Database Target 1 values: [${dbTarget1Values.join(', ')}]`);
    
    // Test backend campaign loading
    console.log(`\n🔍 TESTING BACKEND CAMPAIGN LOADING:`);
    
    try {
      // First, unload the campaign to ensure fresh load
      console.log(`1. Unloading campaign to ensure fresh load...`);
      const unloadResponse = await fetch(`http://localhost:8000/optimizations/${opt.optimizerId}/unload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (unloadResponse.ok) {
        console.log(`   ✅ Campaign unloaded successfully`);
      } else {
        console.log(`   ⚠️ Unload failed (campaign might not be loaded): ${unloadResponse.statusText}`);
      }

      // Now load the campaign
      console.log(`2. Loading campaign from disk...`);
      const loadResponse = await fetch(`http://localhost:8000/optimizations/${opt.optimizerId}/load`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!loadResponse.ok) {
        console.log(`   ❌ Failed to load campaign: ${loadResponse.statusText}`);
        return;
      }

      console.log(`   ✅ Campaign loaded successfully`);

      // Get measurements from backend
      console.log(`3. Getting measurements from backend campaign...`);
      const measurementsResponse = await fetch(`http://localhost:8000/optimizations/${opt.optimizerId}/measurements`);
      
      if (!measurementsResponse.ok) {
        console.log(`   ❌ Failed to get backend measurements: ${measurementsResponse.statusText}`);
        return;
      }
      
      const measurementsData = await measurementsResponse.json();
      const backendMeasurements = measurementsData.measurements || [];
      
      console.log(`   Backend measurements: ${backendMeasurements.length}`);
      
      // Show Target 1 values from backend
      const backendTarget1Values = backendMeasurements.map(m => m['Target 1']).filter(v => v !== undefined);
      console.log(`   Backend Target 1 values: [${backendTarget1Values.join(', ')}]`);
      
      // Compare database vs backend
      console.log(`\n📊 COMPARISON ANALYSIS:`);
      console.log(`Database measurements: ${dbMeasurements.length}`);
      console.log(`Backend measurements: ${backendMeasurements.length}`);
      console.log(`Difference: ${dbMeasurements.length - backendMeasurements.length}`);
      
      if (dbMeasurements.length > backendMeasurements.length) {
        console.log(`❌ CONFIRMED: Backend campaign is missing ${dbMeasurements.length - backendMeasurements.length} measurements from database!`);
        console.log(`\nThis proves that backend campaigns don't automatically load measurements from the database.`);
        
        // Identify which measurements are missing
        console.log(`\n🔍 MISSING MEASUREMENTS ANALYSIS:`);
        
        const missingMeasurements = [];
        dbMeasurements.forEach(dbMeasurement => {
          const dbTarget1 = dbMeasurement.targetValues?.['Target 1'] || parseFloat(dbMeasurement.targetValue);
          
          // Try to find this measurement in backend
          const matchingBackend = backendMeasurements.find(bm => bm['Target 1'] === dbTarget1);
          
          if (!matchingBackend) {
            missingMeasurements.push({
              id: dbMeasurement.id.slice(-12),
              target1: dbTarget1,
              target2: dbMeasurement.targetValues?.['Target 2'],
              batchId: dbMeasurement.batchId,
              created: dbMeasurement.createdAt
            });
          }
        });
        
        console.log(`Missing from backend: ${missingMeasurements.length} measurements`);
        missingMeasurements.forEach((missing, index) => {
          console.log(`  ${index + 1}. ID: ${missing.id}, Target 1: ${missing.target1}, Target 2: ${missing.target2}, Batch: ${missing.batchId?.slice(-12) || 'none'}`);
        });
        
      } else if (dbMeasurements.length === backendMeasurements.length) {
        console.log(`✅ MATCH: Database and backend have same number of measurements`);
        
        // Check if the values match
        const dbSorted = dbTarget1Values.sort((a, b) => a - b);
        const backendSorted = backendTarget1Values.sort((a, b) => a - b);
        
        const valuesMatch = JSON.stringify(dbSorted) === JSON.stringify(backendSorted);
        
        if (valuesMatch) {
          console.log(`✅ VALUES MATCH: All Target 1 values are identical`);
        } else {
          console.log(`❌ VALUES DIFFER: Target 1 values don't match`);
          console.log(`   Database sorted: [${dbSorted.join(', ')}]`);
          console.log(`   Backend sorted: [${backendSorted.join(', ')}]`);
        }
      } else {
        console.log(`❓ UNEXPECTED: Backend has more measurements than database`);
      }
      
      // Test the filtering with current backend state
      console.log(`\n🔍 TESTING FILTERING WITH CURRENT BACKEND STATE:`);
      
      const filteringRequest = {
        target_bounds: {
          "Target 1": [25, 100]  // Changed from [0, 100] to [25, 100]
        },
        preview_only: true
      };
      
      const filterResponse = await fetch(`http://localhost:8000/optimizations/${opt.optimizerId}/bounds`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(filteringRequest)
      });
      
      if (filterResponse.ok) {
        const filterData = await filterResponse.json();
        
        console.log(`   ✅ Filtering test successful`);
        console.log(`   Measurements retained: ${filterData.measurements_retained || 'unknown'}`);
        console.log(`   Measurements excluded: ${filterData.measurements_excluded || 'unknown'}`);
        console.log(`   Total measurements: ${filterData.total_measurements || 'unknown'}`);
        
        if (filterData.exclusion_details) {
          console.log(`   Exclusion details: ${filterData.exclusion_details.length} entries`);
        }
        
        // Compare with expected results
        const expectedFiltered = dbTarget1Values.filter(v => v < 25).length;
        const expectedRetained = dbTarget1Values.filter(v => v >= 25).length;
        
        console.log(`\n   📊 EXPECTED vs ACTUAL:`);
        console.log(`   Expected filtered (Target 1 < 25): ${expectedFiltered}`);
        console.log(`   Expected retained (Target 1 >= 25): ${expectedRetained}`);
        console.log(`   Actual filtered: ${filterData.measurements_excluded || 'unknown'}`);
        console.log(`   Actual retained: ${filterData.measurements_retained || 'unknown'}`);
        
        if (filterData.measurements_excluded !== expectedFiltered) {
          console.log(`   ❌ FILTERING DISCREPANCY: Expected ${expectedFiltered} filtered, got ${filterData.measurements_excluded}`);
          console.log(`   This confirms the backend is operating on incomplete data.`);
        } else {
          console.log(`   ✅ FILTERING MATCHES EXPECTED RESULTS`);
        }
        
      } else {
        console.log(`   ❌ Filtering test failed: ${filterResponse.statusText}`);
      }
      
    } catch (error) {
      console.log(`❌ Error testing backend: ${error.message}`);
    }
    
    console.log(`\n🎯 SYNCHRONIZATION TEST SUMMARY:`);
    console.log(`===============================`);
    console.log(`Database measurements: ${dbMeasurements.length}`);
    console.log(`Backend measurements: ${backendMeasurements?.length || 'unknown'}`);
    
    if (dbMeasurements.length > (backendMeasurements?.length || 0)) {
      console.log(`\n❌ CONFIRMED ISSUE: Backend-Database Synchronization Problem`);
      console.log(`\nRoot Cause: Backend campaigns load from JSON files but don't sync with database measurements`);
      console.log(`\nSolution Needed: Implement database-to-campaign synchronization mechanism`);
      console.log(`\nThis explains why filtering shows incorrect counts - it's operating on incomplete data.`);
    }
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

// Run the test
testBackendDatabaseSync().catch(console.error);
