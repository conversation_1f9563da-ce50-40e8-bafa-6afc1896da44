"use client"

import { useUser } from "@clerk/nextjs"
import { useRouter } from "next/navigation"

interface AcademicSignupWrapperProps {
  children: React.ReactNode
}

export function AcademicSignupWrapper({
  children
}: AcademicSignupWrapperProps) {
  const { isLoaded, user } = useUser()
  const router = useRouter()

  // Still loading Clerk - show loading spinner
  if (!isLoaded) {
    return (
      <div className="container mx-auto max-w-2xl px-4 py-12">
        <div className="space-y-8 text-center">
          {/* Enhanced loading spinner */}
          <div className="relative">
            <div className="mx-auto size-16 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="size-8 animate-pulse rounded-full bg-blue-600"></div>
            </div>
          </div>

          <div className="space-y-3">
            <h1 className="text-3xl font-bold tracking-tight">
              Loading Academic Signup
            </h1>
            <p className="text-muted-foreground text-lg">
              Please wait while we verify your account access.
            </p>
          </div>
        </div>
      </div>
    )
  }

  // User is authenticated, show main content
  if (user) {
    return <>{children}</>
  }

  // Clerk is loaded but no user - redirect to login

  // Redirect to login with return URL
  router.push("/login?redirect_url=/academic-signup")

  // Show loading state while redirecting
  return (
    <div className="container mx-auto max-w-2xl px-4 py-12">
      <div className="space-y-8 text-center">
        <div className="relative">
          <div className="mx-auto size-16 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="size-8 animate-pulse rounded-full bg-blue-600"></div>
          </div>
        </div>
        <div className="space-y-3">
          <h1 className="text-3xl font-bold tracking-tight">
            Redirecting to Login
          </h1>
          <p className="text-muted-foreground text-lg">
            Please wait while we redirect you to sign in...
          </p>
        </div>
      </div>
    </div>
  )
}
