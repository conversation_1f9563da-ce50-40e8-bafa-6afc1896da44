"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { RefreshCw, RotateCw, Download, FileSpreadsheet } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { getStatusHistoryAction } from "@/actions/db/optimization-status-history-actions"
import { statusColors } from "@/lib/status-utils"
import { SelectOptimizationStatusHistory } from "@/db/schema/optimization-status-history-schema"
import { formatDistanceToNow } from "date-fns"
import { downloadCSV, downloadExcel } from "@/lib/export-utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"

interface OptimizationStatusHistoryProps {
  optimizationId: string
  optimizationName?: string
}

export function OptimizationStatusHistory({
  optimizationId,
  optimizationName = "Optimization"
}: OptimizationStatusHistoryProps) {
  const [history, setHistory] = useState<SelectOptimizationStatusHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isExporting, setIsExporting] = useState(false)

  // Create status color mapping with text color
  const statusColorClasses = Object.entries(statusColors).reduce(
    (acc, [status, bgColor]) => ({
      ...acc,
      [status]: `${bgColor} text-white`
    }),
    {} as Record<string, string>
  )

  // Load status history
  const loadHistory = async () => {
    setIsLoading(true)
    try {
      const result = await getStatusHistoryAction(optimizationId)

      if (result.isSuccess) {
        setHistory(result.data)
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error loading status history:", error)
      toast({
        title: "Error",
        description: "Failed to load status history",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Prepare data for export
  const prepareExportData = () => {
    return history.map(entry => ({
      date: new Date(entry.createdAt).toLocaleDateString(),
      time: new Date(entry.createdAt).toLocaleTimeString(),
      previousStatus: entry.previousStatus,
      newStatus: entry.newStatus,
      reason: entry.reason || ""
    }))
  }

  // Define export headers
  const exportHeaders = [
    { key: "date" as const, label: "Date" },
    { key: "time" as const, label: "Time" },
    { key: "previousStatus" as const, label: "Previous Status" },
    { key: "newStatus" as const, label: "New Status" },
    { key: "reason" as const, label: "Reason" }
  ]

  // Handle export to CSV
  const handleExportCSV = async () => {
    if (history.length === 0) {
      toast({
        title: "No data to export",
        description: "There is no history data available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export
      const formattedData = prepareExportData()

      // Generate filename with optimization name and current date
      const sanitizedName = optimizationName.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-history-${new Date().toISOString().split("T")[0]}.csv`

      // Download the CSV
      downloadCSV(formattedData, filename, exportHeaders)

      toast({
        title: "Export successful",
        description: "The history data has been exported as CSV.",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting history to CSV:", error)
      toast({
        title: "Export failed",
        description: "Failed to export history data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Handle export to Excel (.xlsx)
  const handleExportExcel = async () => {
    if (history.length === 0) {
      toast({
        title: "No data to export",
        description: "There is no history data available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export
      const formattedData = prepareExportData()

      // Generate filename with optimization name and current date
      const sanitizedName = optimizationName.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-history-${new Date().toISOString().split("T")[0]}.xlsx`

      // Download as Excel
      downloadExcel(formattedData, filename, exportHeaders)

      toast({
        title: "Export successful",
        description:
          "The history data has been exported as Excel (.xlsx) file.",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting history to Excel:", error)
      toast({
        title: "Export failed",
        description: "Failed to export history data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Load history on mount
  useEffect(() => {
    loadHistory()
  }, [optimizationId])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Status History</CardTitle>
        <div className="flex space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={isLoading || isExporting || history.length === 0}
              >
                {isExporting ? (
                  <RotateCw className="mr-2 size-4 animate-spin" />
                ) : (
                  <Download className="mr-2 size-4" />
                )}
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleExportCSV}>
                <Download className="mr-2 size-4" />
                <span>Export as CSV</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExportExcel}>
                <FileSpreadsheet className="mr-2 size-4" />
                <span>Export as Excel (.xlsx)</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button
            variant="outline"
            size="sm"
            onClick={loadHistory}
            disabled={isLoading}
          >
            {isLoading ? (
              <RotateCw className="mr-2 size-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 size-4" />
            )}
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <RotateCw className="text-muted-foreground size-8 animate-spin" />
          </div>
        ) : history.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>From</TableHead>
                <TableHead>To</TableHead>
                <TableHead>Reason</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {history.map(entry => (
                <TableRow key={entry.id}>
                  <TableCell className="whitespace-nowrap">
                    <div className="font-medium">
                      {new Date(entry.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {formatDistanceToNow(new Date(entry.createdAt), {
                        addSuffix: true
                      })}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {entry.previousStatus}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      className={`capitalize ${statusColorClasses[entry.newStatus as keyof typeof statusColorClasses] || ""}`}
                    >
                      {entry.newStatus}
                    </Badge>
                  </TableCell>
                  <TableCell>{entry.reason}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-muted-foreground py-4 text-center">
            No status changes recorded yet
          </div>
        )}
      </CardContent>
    </Card>
  )
}
