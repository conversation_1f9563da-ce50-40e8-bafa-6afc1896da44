"use client"

import React, { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertCircle, CheckCircle, XCircle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface PreviewData {
  valid_count: number
  dropped_count: number
  dropped_measurements: any[]
  impact_summary: any
}

interface PendingChanges {
  parameters: Record<string, any>
  targets: Record<string, any>
  acquisitionFunction: any
  constraints: any
}

interface ConfigurationPreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  optimization: any
  pendingChanges: PendingChanges
  onConfirm: () => void
  onCancel: () => void
}

export function ConfigurationPreviewDialog({
  open,
  onOpenChange,
  optimization,
  pendingChanges,
  onConfirm,
  onCancel
}: ConfigurationPreviewDialogProps) {
  const [previewData, setPreviewData] = useState<PreviewData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const { toast } = useToast()

  // Debug pending changes when dialog opens
  React.useEffect(() => {
    if (open) {
      console.log("🎯 PREVIEW DIALOG - Dialog opened with pending changes:")
      console.log(
        "  - Parameters:",
        Object.keys(pendingChanges.parameters).length,
        pendingChanges.parameters
      )
      console.log(
        "  - Targets:",
        Object.keys(pendingChanges.targets).length,
        pendingChanges.targets
      )
      console.log("  - Acquisition:", pendingChanges.acquisitionFunction)
      console.log("  - Constraints:", pendingChanges.constraints)
    }
  }, [open, pendingChanges])

  const getTotalChangesCount = () => {
    return (
      Object.keys(pendingChanges.parameters).length +
      Object.keys(pendingChanges.targets).length +
      (pendingChanges.acquisitionFunction ? 1 : 0) +
      (pendingChanges.constraints ? 1 : 0)
    )
  }

  const generatePreview = async () => {
    console.log("🎯 FRONTEND TARGETS CRUD: === GENERATING PREVIEW ===")
    console.log(
      "🎯 FRONTEND TARGETS CRUD: Starting preview generation for optimization:",
      optimization.optimizerId
    )
    console.log("🎯 FRONTEND TARGETS CRUD: Pending changes summary:", {
      parameters: Object.keys(pendingChanges.parameters).length,
      targets: Object.keys(pendingChanges.targets).length,
      acquisitionFunction: pendingChanges.acquisitionFunction ? "Yes" : "No",
      constraints: pendingChanges.constraints ? "Yes" : "No"
    })

    setIsLoading(true)
    try {
      // Prepare parameter bounds and updated parameters from pending changes
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Preparing parameter bounds and updated parameters"
      )
      const parameterBounds: Record<string, [number, number]> = {}
      const updatedParameters: any[] = []
      const parameterOrder: string[] = []

      // Get original parameters and apply pending changes
      const originalParams = (optimization.config as any).parameters || []
      originalParams.forEach((param: any) => {
        const pendingParam = pendingChanges.parameters[param.name]
        const finalParam = pendingParam || param

        parameterOrder.push(param.name)

        // Add bounds for NumericalContinuous parameters
        if (finalParam.type === "NumericalContinuous" && finalParam.bounds) {
          parameterBounds[param.name] = finalParam.bounds
        }

        // IMPORTANT: Include ALL parameters in updated_parameters, not just those with pending changes
        // This ensures that previously applied changes are preserved when making subsequent updates
        updatedParameters.push({
          name: finalParam.name,
          type: finalParam.type,
          values: finalParam.values,
          bounds: finalParam.bounds,
          encoding: finalParam.encoding,
          tolerance: finalParam.tolerance
        })
      })

      // Prepare target configuration - use current optimization state, not original
      console.log(
        "🎯 FRONTEND TARGETS CRUD: === PREPARING TARGET CONFIGURATION ==="
      )
      const targetConfigForAPI: any[] = []
      const currentTargetConfig = (optimization.config as any).target_config

      console.log(
        "🎯 FRONTEND TARGETS CRUD: Current target config type:",
        typeof currentTargetConfig
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Current target config:",
        currentTargetConfig
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Pending target changes:",
        pendingChanges.targets
      )

      if (Array.isArray(currentTargetConfig)) {
        // Multi-target case
        console.log(
          "🎯 FRONTEND TARGETS CRUD: Processing multi-target configuration with",
          currentTargetConfig.length,
          "targets"
        )
        currentTargetConfig.forEach((target: any, index: number) => {
          const pendingTarget = pendingChanges.targets[target.name]
          const finalTarget = pendingTarget || target

          console.log(`🎯 FRONTEND TARGETS CRUD: Target ${index + 1}:`, {
            name: finalTarget.name,
            mode: finalTarget.mode || "MAX",
            weight: finalTarget.weight || 1.0,
            transformation: finalTarget.transformation || "LINEAR",
            type: finalTarget.type || "Numerical",
            bounds: finalTarget.bounds,
            hasPendingChanges: !!pendingTarget
          })

          targetConfigForAPI.push({
            name: finalTarget.name,
            mode: finalTarget.mode || "MAX",
            weight: finalTarget.weight || 1.0,
            transformation: finalTarget.transformation || "LINEAR",
            type: finalTarget.type || "Numerical",
            bounds: finalTarget.bounds
          })
        })
      } else {
        // Single target case
        console.log(
          "🎯 FRONTEND TARGETS CRUD: Processing single-target configuration"
        )
        const targetName = optimization.targetName
        const pendingTarget = pendingChanges.targets[targetName]

        console.log(
          "🎯 FRONTEND TARGETS CRUD: === SINGLE TARGET CONFIG DEBUG ==="
        )
        console.log("🎯 FRONTEND TARGETS CRUD: Target name:", targetName)
        console.log(
          "🎯 FRONTEND TARGETS CRUD: Current target config:",
          currentTargetConfig
        )
        console.log(
          "🎯 FRONTEND TARGETS CRUD: Pending target changes:",
          pendingTarget
        )
        console.log(
          "🎯 FRONTEND TARGETS CRUD: All pending target changes:",
          pendingChanges.targets
        )

        const finalTarget = pendingTarget || {
          name: targetName,
          mode: currentTargetConfig?.mode || "MAX",
          weight: currentTargetConfig?.weight || 1.0,
          transformation: currentTargetConfig?.transformation || "LINEAR",
          type: currentTargetConfig?.type || "Numerical",
          bounds: currentTargetConfig?.bounds
        }

        console.log("Final target for API:", finalTarget)
        console.log("=== END TARGET CONFIG DEBUG ===")

        targetConfigForAPI.push({
          name: finalTarget.name,
          mode: finalTarget.mode,
          weight: finalTarget.weight || 1.0,
          transformation: finalTarget.transformation || "LINEAR",
          type: finalTarget.type || "Numerical",
          bounds: finalTarget.bounds
        })
      }

      // 🔧 CRITICAL FIX: Normalize weights before sending to API
      console.log(
        "🔧 PREVIEW WEIGHT FIX: Checking target weights before API call..."
      )
      if (targetConfigForAPI.length > 1) {
        const currentWeights = targetConfigForAPI.map(
          target => target.weight || 0
        )
        const weightSum = currentWeights.reduce((sum, w) => sum + w, 0)
        console.log(
          `🔧 PREVIEW WEIGHT FIX: Current weights: ${currentWeights}, sum: ${weightSum}`
        )

        if (Math.abs(weightSum - 1.0) > 0.001) {
          console.log(
            `🔧 PREVIEW WEIGHT FIX: Weights need normalization (sum=${weightSum}), applying fix...`
          )

          if (weightSum > 0) {
            // Apply proportional redistribution
            targetConfigForAPI.forEach(target => {
              const currentWeight = target.weight || 0
              const normalizedWeight = currentWeight / weightSum
              target.weight = Math.max(0.01, normalizedWeight) // Ensure minimum weight
            })
            console.log(
              `🔧 PREVIEW WEIGHT FIX: Applied normalization:`,
              targetConfigForAPI.map(target => ({
                name: target.name,
                weight: target.weight,
                percentage: Math.round((target.weight || 0) * 100)
              }))
            )
          } else {
            // If all weights are 0, distribute equally
            const equalWeight = 1.0 / targetConfigForAPI.length
            targetConfigForAPI.forEach(target => {
              target.weight = equalWeight
            })
            console.log(
              `🔧 PREVIEW WEIGHT FIX: Applied equal distribution:`,
              targetConfigForAPI.map(target => ({
                name: target.name,
                weight: target.weight,
                percentage: Math.round((target.weight || 0) * 100)
              }))
            )
          }
        } else {
          console.log(
            `🔧 PREVIEW WEIGHT FIX: Weights already normalized, sum = ${weightSum}`
          )
        }
      } else {
        console.log(
          `🔧 PREVIEW WEIGHT FIX: Single target, no normalization needed`
        )
      }

      // Prepare acquisition function config
      const acquisitionConfig = pendingChanges.acquisitionFunction ||
        (optimization.config as any).acquisition_config || {
          type: "qExpectedImprovement"
        }

      const requestBody: any = {
        parameter_bounds: parameterBounds,
        target_config: targetConfigForAPI,
        acquisition_config: acquisitionConfig,
        parameter_order: parameterOrder,
        preview_only: true
      }

      // Only include updated_parameters if there are any
      if (updatedParameters.length > 0) {
        requestBody.updated_parameters = updatedParameters
      }

      console.log(
        "🎯 FRONTEND TARGETS CRUD: === PREVIEW REQUEST PREPARATION ==="
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Parameter bounds:",
        parameterBounds
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Updated parameters count:",
        updatedParameters.length
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Target config for API:",
        targetConfigForAPI
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Acquisition config:",
        acquisitionConfig
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Full preview request body:",
        requestBody
      )
      console.log("🎯 FRONTEND TARGETS CRUD: === SENDING PREVIEW REQUEST ===")

      const response = await fetch(
        `/api/optimizations/${optimization.optimizerId}/bounds`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(requestBody)
        }
      )

      console.log(
        "🎯 FRONTEND TARGETS CRUD: Preview response status:",
        response.status
      )
      console.log("🎯 FRONTEND TARGETS CRUD: Preview response ok:", response.ok)

      // Check if response has content
      console.log(
        "🎯 FRONTEND TARGETS CRUD: === PROCESSING PREVIEW RESPONSE ==="
      )
      const responseText = await response.text()
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Preview response text length:",
        responseText.length
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Preview response text (first 200 chars):",
        responseText.slice(0, 200)
      )

      if (!responseText) {
        console.error("🎯 FRONTEND TARGETS CRUD: Empty response from server")
        throw new Error("Empty response from server")
      }

      let result
      try {
        result = JSON.parse(responseText)
        console.log(
          "🎯 FRONTEND TARGETS CRUD: Successfully parsed JSON response"
        )
        console.log("🎯 FRONTEND TARGETS CRUD: Response status:", result.status)
        console.log(
          "🎯 FRONTEND TARGETS CRUD: Response message:",
          result.message
        )
      } catch (parseError) {
        console.error("🎯 FRONTEND TARGETS CRUD: JSON parse error:", parseError)
        console.error("🎯 FRONTEND TARGETS CRUD: Raw response:", responseText)
        throw new Error(
          `Invalid JSON response: ${responseText.slice(0, 100)}...`
        )
      }

      if (!response.ok) {
        throw new Error(
          result.error ||
            `Server error: ${response.status} ${response.statusText}`
        )
      }

      setPreviewData(result)
      setShowPreview(true)
    } catch (error) {
      console.error("Preview error:", error)
      toast({
        title: "Preview Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfirm = () => {
    console.log(
      "🎯 FRONTEND TARGETS CRUD: === CONFIRMING CONFIGURATION CHANGES ==="
    )
    console.log(
      "🎯 FRONTEND TARGETS CRUD: User confirmed configuration changes for optimization:",
      optimization.optimizerId
    )
    console.log("🎯 FRONTEND TARGETS CRUD: Preview data summary:", {
      validCount: previewData?.valid_count,
      droppedCount: previewData?.dropped_count,
      retentionRate: previewData?.impact_summary?.retention_rate
    })
    console.log(
      "🎯 FRONTEND TARGETS CRUD: Calling onConfirm callback to apply changes"
    )
    onConfirm()
    handleClose()
  }

  const handleCancel = () => {
    console.log(
      "🎯 FRONTEND TARGETS CRUD: === CANCELLING CONFIGURATION CHANGES ==="
    )
    console.log(
      "🎯 FRONTEND TARGETS CRUD: User cancelled configuration changes for optimization:",
      optimization.optimizerId
    )
    console.log("🎯 FRONTEND TARGETS CRUD: Calling onCancel callback")
    onCancel()
    handleClose()
  }

  const handleClose = () => {
    setShowPreview(false)
    setPreviewData(null)
    onOpenChange(false)
  }

  const handleBack = () => {
    setShowPreview(false)
    setPreviewData(null)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {showPreview
              ? "Confirm Configuration Changes"
              : "Review Configuration Changes"}
          </DialogTitle>
          <DialogDescription>
            {showPreview
              ? "Review the impact of your configuration changes before applying them."
              : "Preview the impact of your changes on existing measurements and model training."}
          </DialogDescription>
        </DialogHeader>

        {!showPreview ? (
          <div className="space-y-6">
            {/* Changes Summary */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Pending Changes Summary</h3>

              {/* Parameter Changes */}
              {Object.keys(pendingChanges.parameters).length > 0 && (
                <div className="space-y-2">
                  <h4 className="flex items-center gap-2 font-medium">
                    Parameters
                    <Badge variant="secondary">
                      {Object.keys(pendingChanges.parameters).length} modified
                    </Badge>
                  </h4>
                  <div className="grid gap-2">
                    {Object.entries(pendingChanges.parameters).map(
                      ([name, param]: [string, any]) => {
                        // Get original parameter for comparison
                        const originalParam =
                          optimization?.config?.parameters?.find?.(
                            (p: any) => p.name === name
                          )

                        // Build change description
                        const changes = []

                        // Check bounds changes
                        if (
                          param.bounds &&
                          JSON.stringify(originalParam?.bounds) !==
                            JSON.stringify(param.bounds)
                        ) {
                          changes.push(`Bounds: [${param.bounds.join(", ")}]`)
                        }

                        // Check values changes (for discrete parameters)
                        if (
                          param.values &&
                          JSON.stringify(originalParam?.values) !==
                            JSON.stringify(param.values)
                        ) {
                          const valueDisplay =
                            param.values.length > 3
                              ? `{${param.values.slice(0, 3).join(", ")}...}`
                              : `{${param.values.join(", ")}}`
                          changes.push(`Values: ${valueDisplay}`)
                        }

                        // Check type changes
                        if (param.type !== originalParam?.type) {
                          changes.push(
                            `Type: ${originalParam?.type || "Unknown"} → ${param.type}`
                          )
                        }

                        // Check encoding changes
                        if (param.encoding !== originalParam?.encoding) {
                          changes.push(
                            `Encoding: ${originalParam?.encoding || "Default"} → ${param.encoding}`
                          )
                        }

                        // Check tolerance changes
                        if (param.tolerance !== originalParam?.tolerance) {
                          changes.push(
                            `Tolerance: ${originalParam?.tolerance || "Default"} → ${param.tolerance}`
                          )
                        }

                        const changeDescription =
                          changes.length > 0
                            ? changes.join(", ")
                            : `${param.type} - Modified`

                        return (
                          <div
                            key={name}
                            className="bg-muted/30 flex items-center justify-between rounded p-2"
                          >
                            <span className="font-medium">{name}</span>
                            <span className="text-muted-foreground text-sm">
                              {changeDescription}
                            </span>
                          </div>
                        )
                      }
                    )}
                  </div>
                </div>
              )}

              {/* Target Changes */}
              {Object.keys(pendingChanges.targets).length > 0 && (
                <div className="space-y-2">
                  <h4 className="flex items-center gap-2 font-medium">
                    Targets
                    <Badge variant="secondary">
                      {Object.keys(pendingChanges.targets).length} modified
                    </Badge>
                  </h4>
                  <div className="grid gap-2">
                    {Object.entries(pendingChanges.targets).map(
                      ([name, target]: [string, any]) => {
                        // Get original target for comparison
                        const originalTarget =
                          optimization?.config?.target_config?.find?.(
                            (t: any) => t.name === name
                          ) ||
                          optimization?.config?.targets?.find?.(
                            (t: any) => t.name === name
                          ) ||
                          (optimization?.config?.target_config?.name === name
                            ? optimization.config.target_config
                            : null)

                        // Build change description
                        const changes = []

                        // Check weight changes
                        if (
                          target.weight !== undefined &&
                          originalTarget?.weight !== target.weight
                        ) {
                          const oldWeight = Math.round(
                            (originalTarget?.weight || 0) * 100
                          )
                          const newWeight = Math.round(target.weight * 100)
                          changes.push(`Weight: ${oldWeight}% → ${newWeight}%`)
                        }

                        // Check mode changes
                        if (
                          target.mode !== undefined &&
                          originalTarget?.mode !== target.mode
                        ) {
                          changes.push(
                            `Mode: ${originalTarget?.mode || "MAX"} → ${target.mode}`
                          )
                        }

                        // Check bounds changes
                        if (
                          target.bounds &&
                          JSON.stringify(originalTarget?.bounds) !==
                            JSON.stringify(target.bounds)
                        ) {
                          changes.push(`Bounds: [${target.bounds.join(", ")}]`)
                        }

                        const changeDescription =
                          changes.length > 0 ? changes.join(", ") : "Modified"

                        return (
                          <div
                            key={name}
                            className="bg-muted/30 flex items-center justify-between rounded p-2"
                          >
                            <span className="font-medium">{name}</span>
                            <span className="text-muted-foreground text-sm">
                              {changeDescription}
                            </span>
                          </div>
                        )
                      }
                    )}
                  </div>
                </div>
              )}

              {/* Acquisition Function Changes */}
              {pendingChanges.acquisitionFunction && (
                <div className="space-y-2">
                  <h4 className="flex items-center gap-2 font-medium">
                    Acquisition Function
                    <Badge variant="secondary">Modified</Badge>
                  </h4>
                  <div className="bg-muted/30 rounded p-2">
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {pendingChanges.acquisitionFunction.type}
                      </div>
                      {pendingChanges.acquisitionFunction.beta && (
                        <div className="text-muted-foreground text-xs">
                          Beta parameter:{" "}
                          {pendingChanges.acquisitionFunction.beta}
                        </div>
                      )}
                      {pendingChanges.acquisitionFunction.xi && (
                        <div className="text-muted-foreground text-xs">
                          Xi parameter: {pendingChanges.acquisitionFunction.xi}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Constraints Changes */}
              {pendingChanges.constraints && (
                <div className="space-y-2">
                  <h4 className="flex items-center gap-2 font-medium">
                    Constraints
                    <Badge variant="secondary">Modified</Badge>
                  </h4>
                  <div className="bg-muted/30 rounded p-2">
                    <div className="space-y-1">
                      {Array.isArray(pendingChanges.constraints) ? (
                        <>
                          <div className="text-sm font-medium">
                            {pendingChanges.constraints.length} constraint
                            {pendingChanges.constraints.length !== 1 ? "s" : ""}
                          </div>
                          {pendingChanges.constraints
                            .slice(0, 3)
                            .map((constraint: any, index: number) => (
                              <div
                                key={index}
                                className="text-muted-foreground text-xs"
                              >
                                {constraint.type || "Constraint"}:{" "}
                                {constraint.expression ||
                                  constraint.description ||
                                  "Modified"}
                              </div>
                            ))}
                          {pendingChanges.constraints.length > 3 && (
                            <div className="text-muted-foreground text-xs">
                              ...and {pendingChanges.constraints.length - 3}{" "}
                              more
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="text-sm">
                          Constraints configuration modified
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          // Preview Section
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="size-4" />
              <AlertTitle>Data Impact Summary</AlertTitle>
              <AlertDescription>
                • {previewData?.valid_count} measurements will be kept
                {(previewData?.dropped_count ?? 0) > 0 && (
                  <>
                    <br />• {previewData?.dropped_count} measurements will be
                    excluded
                    <br />• Measurements are excluded because their parameter
                    values are outside the new configuration ranges/values
                  </>
                )}
                <br />• Campaign will be retrained with valid data only
              </AlertDescription>
            </Alert>

            {previewData && previewData.dropped_count > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Measurements to be excluded:</h4>
                <div className="max-h-40 overflow-y-auto rounded border p-2">
                  {previewData.dropped_measurements
                    .slice(0, 10)
                    .map((measurement, idx) => {
                      // Format measurement data more readably
                      const parameterValues = Object.entries(measurement)
                        .filter(
                          ([key]) =>
                            !key.startsWith("target_") && !key.includes("_")
                        )
                        .map(([key, value]) => `${key}: ${value}`)
                        .join(", ")

                      return (
                        <div
                          key={idx}
                          className="border-b py-1 text-sm last:border-b-0"
                        >
                          <div className="font-medium">
                            Measurement {idx + 1}
                          </div>
                          <div className="text-muted-foreground">
                            {parameterValues}
                          </div>
                        </div>
                      )
                    })}
                  {previewData.dropped_count > 10 && (
                    <div className="text-muted-foreground mt-2 text-sm">
                      ...and {previewData.dropped_count - 10} more measurements
                    </div>
                  )}
                </div>
              </div>
            )}

            {previewData && previewData.dropped_count === 0 && (
              <Alert>
                <CheckCircle className="size-4" />
                <AlertTitle>No Data Loss</AlertTitle>
                <AlertDescription>
                  All existing measurements are compatible with the new
                  configuration. No data will be excluded.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        <DialogFooter className="flex flex-col gap-2 sm:flex-row sm:justify-end">
          {!showPreview ? (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button onClick={generatePreview} disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 size-4 animate-spin" />}
                Preview Changes ({getTotalChangesCount()})
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={handleBack}>
                Back to Changes
              </Button>
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                onClick={handleConfirm}
                className="bg-primary text-primary-foreground hover:bg-primary/90"
              >
                Apply Changes & Retrain
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
