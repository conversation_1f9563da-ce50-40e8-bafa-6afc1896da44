import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, createMockParameter } from '../test-utils'
import { ParameterEditForm } from '@/components/optimization/parameter-edit-form'

describe('ParameterEditForm', () => {
  const mockOnSave = vi.fn()
  const mockOnCancel = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders numerical continuous parameter form correctly', () => {
    const parameter = createMockParameter({
      name: 'temperature',
      type: 'NumericalContinuous',
      bounds: [20, 100]
    })

    render(
      <ParameterEditForm
        parameter={parameter}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    expect(screen.getByDisplayValue('temperature')).toBeInTheDocument()
    expect(screen.getByDisplayValue('20')).toBeInTheDocument()
    expect(screen.getByDisplayValue('100')).toBeInTheDocument()
  })

  it('renders numerical discrete parameter form correctly', () => {
    const parameter = createMockParameter({
      name: 'pressure',
      type: 'NumericalDiscrete',
      values: [1, 2, 3, 4, 5],
      tolerance: 0.5
    })

    render(
      <ParameterEditForm
        parameter={parameter}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    expect(screen.getByDisplayValue('pressure')).toBeInTheDocument()
    expect(screen.getByDisplayValue('1, 2, 3, 4, 5')).toBeInTheDocument()
    expect(screen.getByDisplayValue('0.5')).toBeInTheDocument()
  })

  it('renders categorical parameter form correctly', () => {
    const parameter = createMockParameter({
      name: 'catalyst',
      type: 'CategoricalParameter',
      values: ['A', 'B', 'C'],
      encoding: 'OHE'
    })

    render(
      <ParameterEditForm
        parameter={parameter}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    expect(screen.getByDisplayValue('catalyst')).toBeInTheDocument()
    expect(screen.getByDisplayValue('A, B, C')).toBeInTheDocument()
    expect(screen.getByDisplayValue('OHE')).toBeInTheDocument()
  })

  it('handles parameter name changes', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'old_name',
      type: 'NumericalContinuous',
      bounds: [0, 1]
    })

    render(
      <ParameterEditForm
        parameter={parameter}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    const nameInput = screen.getByDisplayValue('old_name')
    await user.clear(nameInput)
    await user.type(nameInput, 'new_name')

    expect(nameInput).toHaveValue('new_name')
  })

  it('handles bounds changes for continuous parameters', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'temperature',
      type: 'NumericalContinuous',
      bounds: [20, 80]
    })

    render(
      <ParameterEditForm
        parameter={parameter}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    const lowerBoundInput = screen.getByDisplayValue('20')
    const upperBoundInput = screen.getByDisplayValue('80')

    fireEvent.change(lowerBoundInput, { target: { value: '25' } })
    fireEvent.change(upperBoundInput, { target: { value: '85' } })

    expect(lowerBoundInput).toHaveValue(25)
    expect(upperBoundInput).toHaveValue(85)
  })

  it('handles values changes for discrete parameters', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'levels',
      type: 'NumericalDiscrete',
      values: [1, 2, 3],
      tolerance: 0.1
    })

    render(
      <ParameterEditForm
        parameter={parameter}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    const valuesInput = screen.getByDisplayValue('1, 2, 3')
    fireEvent.change(valuesInput, { target: { value: '1, 2, 3, 4, 5' } })

    expect(valuesInput).toHaveValue('1, 2, 3, 4, 5')
  })

  it('calls onSave with updated parameter when save button is clicked', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'test_param',
      type: 'NumericalContinuous',
      bounds: [0, 1]
    })

    render(
      <ParameterEditForm
        parameter={parameter}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    const nameInput = screen.getByDisplayValue('test_param')
    await user.clear(nameInput)
    await user.type(nameInput, 'updated_param')

    const saveButton = screen.getByRole('button', { name: /save/i })
    await user.click(saveButton)

    expect(mockOnSave).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'updated_param',
        type: 'NumericalContinuous',
        bounds: [0, 1]
      })
    )
  })

  it('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter()

    render(
      <ParameterEditForm
        parameter={parameter}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    await user.click(cancelButton)

    expect(mockOnCancel).toHaveBeenCalled()
  })

  it('resets form when reset button is clicked', async () => {
    const user = userEvent.setup()
    const parameter = createMockParameter({
      name: 'original_name',
      type: 'NumericalContinuous',
      bounds: [0, 1]
    })

    render(
      <ParameterEditForm
        parameter={parameter}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    const nameInput = screen.getByDisplayValue('original_name')
    await user.clear(nameInput)
    await user.type(nameInput, 'changed_name')

    const resetButton = screen.getByRole('button', { name: /reset/i })
    await user.click(resetButton)

    expect(nameInput).toHaveValue('original_name')
  })
})
