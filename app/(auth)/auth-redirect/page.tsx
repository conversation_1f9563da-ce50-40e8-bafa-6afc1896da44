"use client"

import { useSearchParams } from "next/navigation"
import { useUser } from "@clerk/nextjs"
import { useEffect, useState } from "react"
import { RedirectHandler } from "@/components/auth/redirect-handler"

/**
 * This page handles redirects after authentication
 * It extracts the redirect URL from the query parameters
 * and uses the RedirectHandler component to handle the redirect
 */
export default function AuthRedirectPage() {
  const searchParams = useSearchParams()
  const { isLoaded, user } = useUser()
  const [shouldRedirect, setShouldRedirect] = useState(false)

  // Get the redirect URL from the query parameters
  const redirectUrl = searchParams.get("redirect_url") || "/welcome"

  // Get the plan type from the query parameters
  const planType = searchParams.get("plan")

  // Store the plan type in localStorage if it exists
  if (typeof window !== "undefined" && planType) {
    localStorage.setItem("selectedPlanType", planType)
  }

  // If redirecting to academic signup, set the academic intent flag
  if (
    typeof window !== "undefined" &&
    redirectUrl.includes("/academic-signup")
  ) {
    localStorage.setItem("academicSignupIntent", "true")
  }

  // Wait for user to be loaded before redirecting
  useEffect(() => {
    if (isLoaded) {
      if (user) {
        setShouldRedirect(true)
      } else {
        // If no user after loading, redirect to login with the original redirect URL
        window.location.href = `/login?redirect_url=${encodeURIComponent(redirectUrl)}`
      }
    }
  }, [isLoaded, user, redirectUrl])

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <h1 className="mb-4 text-2xl font-bold">
          {!isLoaded ? "Loading..." : "Redirecting..."}
        </h1>
        <p className="text-muted-foreground">
          {!isLoaded
            ? "Please wait while we verify your authentication..."
            : "Please wait while we redirect you."}
        </p>
        {shouldRedirect && <RedirectHandler redirectUrl={redirectUrl} />}
      </div>
    </div>
  )
}
