// scripts/clear-db.js
// Simple script to clear all data from the database using <PERSON><PERSON><PERSON>

require('dotenv').config();

const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { sql } = require('drizzle-orm');

// Check for required environment variable
if (!process.env.DATABASE_URL) {
  console.error("DATABASE_URL environment variable is not set");
  process.exit(1);
}

// Create database connection
const connectionString = process.env.DATABASE_URL;
const client = postgres(connectionString, { max: 1 });
const db = drizzle(client);

async function clearDatabase() {
  console.log("Starting database cleanup...");

  try {
    // Delete data from tables in reverse order of dependencies
    console.log("Deleting samples...");
    await db.execute(sql`DELETE FROM samples`);

    console.log("Deleting measurements...");
    await db.execute(sql`DELETE FROM measurements`);

    console.log("Deleting suggestions...");
    await db.execute(sql`DELETE FROM suggestions`);

    console.log("Deleting optimization status history...");
    await db.execute(sql`DELETE FROM optimization_status_history`);

    console.log("Deleting optimizations...");
    await db.execute(sql`DELETE FROM optimizations`);

    console.log("Deleting profiles...");
    await db.execute(sql`DELETE FROM profiles`);

    console.log("Database cleanup completed successfully!");
  } catch (error) {
    console.error("Error during database cleanup:", error);
  } finally {
    await client.end();
    process.exit(0);
  }
}

// Run the cleanup
clearDatabase();
