"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  PopoverTrigger
} from "@/components/ui/popover"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { InfoIcon, ExternalLink } from "lucide-react"
import Link from "next/link"

export function BestResultsDocumentation() {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="size-6">
          <InfoIcon className="size-4" />
          <span className="sr-only">About Best Results</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="max-h-[500px] w-[350px] overflow-y-auto"
        align="end"
      >
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Understanding Best Results</h3>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">How It's Calculated</h4>
            <p className="text-muted-foreground text-sm">
              The Best Results card shows the optimal parameter values found so
              far based on your optimization objectives.
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Single-Target Optimization</h4>
            <p className="text-muted-foreground text-sm">
              For single-target optimizations, we simply display the highest
              (for MAX) or lowest (for MIN) target value achieved.
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Multi-Target Optimization</h4>
            <p className="text-muted-foreground text-sm">
              For multi-target optimizations, we calculate a composite score
              using:
            </p>
            <ol className="text-muted-foreground list-decimal space-y-1 pl-5 text-sm">
              <li>Normalization of each target value to a 0-1 scale</li>
              <li>Inversion of MIN targets so higher is always better</li>
              <li>Weighting of targets (currently equal weights)</li>
              <li>Summation into a composite score</li>
            </ol>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Target Contributions</h4>
            <p className="text-muted-foreground text-sm">
              For multi-target optimizations, we show:
            </p>
            <ul className="text-muted-foreground list-disc space-y-1 pl-5 text-sm">
              <li>Raw value for each target</li>
              <li>Weight percentage for each target</li>
              <li>Contribution to the composite score</li>
              <li>Visual progress bar showing normalized value</li>
            </ul>
          </div>

          <div className="border-t pt-2">
            <Link
              href="/dashboard/help/optimization-results"
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Learn more about optimization results
              <ExternalLink className="ml-1 size-3" />
            </Link>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
