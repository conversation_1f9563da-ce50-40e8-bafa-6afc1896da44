"use client"

import { motion } from "framer-motion"
import { User, Database, BarChart, Settings, Globe, Clock } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const dataCategories = [
  {
    icon: User,
    title: "Account Information",
    purpose: "User authentication and account management",
    retention: "Account lifetime",
    dataTypes: [
      {
        name: "Email address",
        required: true,
        purpose: "Authentication and communication"
      },
      { name: "Name", required: true, purpose: "Account identification" },
      {
        name: "Profile preferences",
        required: false,
        purpose: "Personalized experience"
      },
      {
        name: "Subscription status",
        required: true,
        purpose: "Service access control"
      }
    ]
  },
  {
    icon: Database,
    title: "Optimization Data",
    purpose: "Bayesian optimization experiments and results",
    retention: "User-configurable (default: 2 years)",
    dataTypes: [
      {
        name: "Experiment parameters",
        required: true,
        purpose: "Optimization calculations"
      },
      {
        name: "Measurement results",
        required: true,
        purpose: "Model training and suggestions"
      },
      {
        name: "Optimization history",
        required: true,
        purpose: "Progress tracking and analysis"
      },
      {
        name: "Custom objectives",
        required: true,
        purpose: "Multi-objective optimization"
      }
    ]
  },
  {
    icon: BarChart,
    title: "Usage Analytics",
    purpose: "Service improvement and performance monitoring",
    retention: "12 months",
    dataTypes: [
      {
        name: "Feature usage",
        required: false,
        purpose: "Product improvement"
      },
      {
        name: "Performance metrics",
        required: true,
        purpose: "Service optimization"
      },
      {
        name: "Error logs",
        required: true,
        purpose: "Bug fixing and stability"
      },
      {
        name: "Session data",
        required: true,
        purpose: "Security and authentication"
      }
    ]
  },
  {
    icon: Settings,
    title: "Technical Data",
    purpose: "System functionality and security",
    retention: "90 days",
    dataTypes: [
      {
        name: "IP address",
        required: true,
        purpose: "Security and fraud prevention"
      },
      {
        name: "Browser information",
        required: true,
        purpose: "Compatibility and support"
      },
      {
        name: "Device information",
        required: false,
        purpose: "Responsive design optimization"
      },
      {
        name: "API usage logs",
        required: true,
        purpose: "Rate limiting and security"
      }
    ]
  }
]

const dataMinimizationPrinciples = [
  "We collect only data necessary for providing our optimization services",
  "Optional data collection requires explicit user consent",
  "Data is automatically deleted after retention periods",
  "Users can request data deletion at any time",
  "Anonymous processing is used whenever possible",
  "Data sharing is limited to essential service providers"
]

export function DataCollection() {
  return (
    <section id="data-collection" className="py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            What Data We Collect
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            We practice data minimization, collecting only the information
            necessary to provide our optimization services effectively and
            securely.
          </p>
        </motion.div>

        <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-2">
          {dataCategories.map((category, index) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="flex size-12 items-center justify-center rounded-lg bg-blue-100">
                      <category.icon className="size-6 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">
                        {category.title}
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        {category.purpose}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="size-4 text-gray-500" />
                    <span className="text-sm text-gray-600">
                      Retention: {category.retention}
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {category.dataTypes.map((dataType, typeIndex) => (
                      <div
                        key={typeIndex}
                        className="flex items-start justify-between"
                      >
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-900">
                              {dataType.name}
                            </span>
                            <Badge
                              variant={
                                dataType.required ? "default" : "secondary"
                              }
                            >
                              {dataType.required ? "Required" : "Optional"}
                            </Badge>
                          </div>
                          <p className="mt-1 text-xs text-gray-600">
                            {dataType.purpose}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Card className="bg-gradient-to-r from-green-50 to-blue-50">
            <CardHeader>
              <CardTitle className="text-xl">
                Data Minimization Principles
              </CardTitle>
              <CardDescription>
                Our commitment to collecting only necessary data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                {dataMinimizationPrinciples.map((principle, index) => (
                  <div key={index} className="flex items-start">
                    <div className="mr-3 mt-1 size-2 shrink-0 rounded-full bg-green-600" />
                    <span className="text-sm text-gray-700">{principle}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-8 text-center"
        >
          <Card>
            <CardContent className="pt-6">
              <h3 className="mb-4 text-xl font-semibold text-gray-900">
                Data Collection Summary
              </h3>
              <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-blue-600">4</div>
                  <div className="text-sm text-gray-600">Data Categories</div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-green-600">
                    Minimal
                  </div>
                  <div className="text-sm text-gray-600">Data Collection</div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-purple-600">
                    Transparent
                  </div>
                  <div className="text-sm text-gray-600">
                    Purpose Disclosure
                  </div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-orange-600">
                    User Control
                  </div>
                  <div className="text-sm text-gray-600">Data Management</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
