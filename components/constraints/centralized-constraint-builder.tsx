"use client"

import React, { useState, useMemo } from "react"
import {
  Plus,
  Trash2,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ert<PERSON>ircle,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ting<PERSON>
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { ConstraintTemplateSelector } from "@/components/optimization/constraints/constraint-template-selector"
import { ConstraintTypeSelector } from "@/components/optimization/constraints/constraint-type-selector"
import { ConstraintEditor } from "@/components/optimization/constraints/constraint-editor"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

// Import from centralized constraints library
import {
  useConstraints,
  useConstraintValidation,
  useConstraintContext,
  useConstraintAwareSampling,
  Constraint,
  Parameter,
  SamplingOptions,
  formatConstraintDisplay,
  getConstraintCategory,
  createConstraint,
  generateConstraintId,
  CONSTRAINT_CATEGORIES,
  CONSTRAINT_TYPE_METADATA
} from "@/lib/constraints"

interface CentralizedConstraintBuilderProps {
  parameters: Parameter[]
  constraints: Constraint[]
  onConstraintsChange: (constraints: Constraint[]) => void
  enableSampling?: boolean
  samplingOptions?: Partial<SamplingOptions>
  className?: string
}

export function CentralizedConstraintBuilder({
  parameters,
  constraints: initialConstraints,
  onConstraintsChange,
  enableSampling = false,
  samplingOptions = {},
  className = ""
}: CentralizedConstraintBuilderProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingConstraint, setEditingConstraint] = useState<Constraint | null>(
    null
  )
  const [selectedConstraintType, setSelectedConstraintType] =
    useState<string>("")
  const [samplingEnabled, setSamplingEnabled] = useState(enableSampling)

  // Use the constraints from props directly (controlled component)
  const [selectedConstraintId, setSelectedConstraintId] = useState<
    string | null
  >(null)

  // Create constraint management functions that work with the parent's state
  const addConstraint = (constraint: Constraint) => {
    const constraintWithId = {
      ...constraint,
      id: constraint.id || generateConstraintId()
    }
    const updatedConstraints = [...initialConstraints, constraintWithId]
    onConstraintsChange(updatedConstraints)
    return constraintWithId
  }

  const updateConstraint = (id: string, updates: Partial<Constraint>) => {
    const updatedConstraints = initialConstraints.map(c =>
      c.id === id ? ({ ...c, ...updates } as Constraint) : c
    )
    onConstraintsChange(updatedConstraints)
  }

  const removeConstraint = (id: string) => {
    const updatedConstraints = initialConstraints.filter(c => c.id !== id)
    onConstraintsChange(updatedConstraints)
  }

  const duplicateConstraint = (id: string) => {
    const constraint = initialConstraints.find(c => c.id === id)
    if (constraint) {
      const duplicated = {
        ...constraint,
        id: generateConstraintId(),
        name: `${constraint.name} (Copy)`
      }
      const updatedConstraints = [...initialConstraints, duplicated]
      onConstraintsChange(updatedConstraints)
    }
  }

  // Use constraints from props
  const constraints = initialConstraints
  const selectedConstraint = selectedConstraintId
    ? constraints.find(c => c.id === selectedConstraintId)
    : null

  // Use centralized constraint context
  const {
    isValidating,
    isValid,
    hasWarnings,
    validationResults,
    globalValidation,
    isGeneratingSamples,
    samplingResult,
    feasibilityEstimate,
    generateSamples,
    constraintCount,
    hasConstraints
  } = useConstraintContext(
    parameters,
    constraints,
    useMemo(
      () => ({
        validationEnabled: true,
        samplingEnabled: samplingEnabled
      }),
      [samplingEnabled]
    )
  )

  // No need for constraint synchronization since we're directly updating parent state

  const handleEditConstraint = (constraint: Constraint) => {
    setEditingConstraint({ ...constraint })
    setSelectedConstraintId(constraint.id!)
    setIsDialogOpen(true)
  }

  const handleSaveConstraint = (constraint: Constraint) => {
    if (
      editingConstraint?.id &&
      constraints.find(c => c.id === editingConstraint.id)
    ) {
      // Update existing constraint
      updateConstraint(editingConstraint.id, constraint)
    } else {
      // Add new constraint
      addConstraint(constraint)
    }

    setEditingConstraint(null)
    setIsDialogOpen(false)

    toast({
      title: "Constraint saved",
      description: `${constraint.name || constraint.type} has been saved successfully.`
    })
  }

  const handleDeleteConstraint = (constraintId: string) => {
    removeConstraint(constraintId)
    toast({
      title: "Constraint deleted",
      description: "The constraint has been removed."
    })
  }

  const handleDuplicateConstraint = (constraintId: string) => {
    const constraint = constraints.find(c => c.id === constraintId)
    if (constraint) {
      duplicateConstraint(constraintId)
      toast({
        title: "Constraint duplicated",
        description: `${constraint.name} (Copy) has been created.`
      })
    }
  }

  const handleGenerateSamples = async () => {
    if (!samplingEnabled || parameters.length === 0) return

    const options: SamplingOptions = {
      strategy: "LHS",
      nSamples: 10,
      respectConstraints: hasConstraints,
      ...samplingOptions
    }

    try {
      await generateSamples(parameters, constraints, options)
      toast({
        title: "Samples generated",
        description: `Generated ${samplingResult?.samples.length || 0} constraint-aware samples.`
      })
    } catch (error) {
      toast({
        title: "Sampling failed",
        description: "Failed to generate constraint-aware samples.",
        variant: "destructive"
      })
    }
  }

  const handleTemplateSelect = (template: any) => {
    // Create a new constraint based on the template
    const newConstraint = {
      id: generateConstraintId(),
      name: template.name || `${template.constraint.type} Template`,
      type: template.constraint.type,
      parameters: template.constraint.parameters || [],
      ...template.constraint // Apply all template properties
    } as Constraint

    // Set the constraint for editing (don't add to list yet - user needs to save)
    setEditingConstraint(newConstraint)
    setIsDialogOpen(true)

    toast({
      title: "Template loaded",
      description: `${template.name} template loaded for editing.`
    })
  }

  const handleAddConstraint = () => {
    if (!selectedConstraintType) return

    const newConstraint = createConstraint(
      selectedConstraintType,
      [],
      `${selectedConstraintType.replace("Constraint", "")} ${constraints.length + 1}`
    )

    // Set the constraint for editing (don't add to list yet - user needs to save)
    setEditingConstraint(newConstraint)
    setIsDialogOpen(true)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Constraints</h3>
          <p className="text-muted-foreground text-sm">
            Define mathematical constraints to restrict the search space
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {constraintCount} constraint{constraintCount !== 1 ? "s" : ""}
          </Badge>
          {isValidating && (
            <Badge variant="outline" className="animate-pulse">
              Validating...
            </Badge>
          )}
          {!isValidating && hasConstraints && (
            <Badge variant={isValid ? "default" : "destructive"}>
              {isValid ? "Valid" : "Invalid"}
            </Badge>
          )}
        </div>
      </div>

      {/* Description and guidance */}
      <div className="text-muted-foreground space-y-1 text-sm">
        <p>
          Add mathematical constraints to restrict the search space when
          parameters have dependencies or limitations.
        </p>
        <p className="text-xs">
          <strong>Enable if:</strong> You have mixture ratios, exclusion rules,
          or parameter relationships that must be maintained.
        </p>
      </div>

      {/* Quick Templates */}
      <ConstraintTemplateSelector
        onTemplateSelect={handleTemplateSelect}
        availableParameters={parameters.map(p => p.name)}
        parameters={parameters}
      />

      {/* Add New Constraint */}
      <Card className="border-dashed">
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex gap-2">
              <div className="flex-1">
                <ConstraintTypeSelector
                  value={selectedConstraintType}
                  onValueChange={setSelectedConstraintType}
                  availableParameters={parameters.map(p => p.name)}
                  placeholder="Choose constraint type..."
                />
              </div>
              <Button
                type="button"
                onClick={handleAddConstraint}
                disabled={!selectedConstraintType}
                className="shrink-0"
              >
                <Plus className="mr-2 size-4" />
                Add Constraint
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sampling Controls */}
      {enableSampling && (
        <div className="flex items-center gap-2">
          <Label htmlFor="sampling-toggle" className="text-sm">
            Constraint-aware sampling
          </Label>
          <Switch
            id="sampling-toggle"
            checked={samplingEnabled}
            onCheckedChange={setSamplingEnabled}
          />
        </div>
      )}

      {/* Existing Constraints */}
      {constraints.length > 0 && (
        <div className="space-y-3">
          {constraints.map(constraint => {
            const category = getConstraintCategory(constraint.type)
            const categoryInfo = CONSTRAINT_CATEGORIES[category]

            return (
              <Card key={constraint.id} className="relative">
                <CardContent className="pt-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="mb-2 flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className="text-xs"
                          style={{
                            backgroundColor: `${categoryInfo.color}20`,
                            borderColor: categoryInfo.color,
                            color: categoryInfo.color
                          }}
                        >
                          {categoryInfo.name}
                        </Badge>
                        <span className="font-medium">
                          {constraint.name || constraint.type}
                        </span>
                      </div>

                      <div className="text-muted-foreground text-sm">
                        {formatConstraintDisplay(constraint)}
                      </div>

                      {constraint.parameters &&
                        constraint.parameters.length > 0 && (
                          <div className="mt-2 text-xs">
                            <span className="font-medium">Parameters: </span>
                            <span className="text-muted-foreground">
                              {constraint.parameters.join(", ")}
                            </span>
                          </div>
                        )}
                    </div>

                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setEditingConstraint(constraint)
                          setIsDialogOpen(true)
                        }}
                        className="size-8 p-0"
                      >
                        <Edit className="size-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => duplicateConstraint(constraint.id!)}
                        className="size-8 p-0"
                      >
                        <Copy className="size-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeConstraint(constraint.id!)}
                        className="text-destructive hover:text-destructive size-8 p-0"
                      >
                        <Trash2 className="size-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* No constraints message */}
      {constraints.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="py-8 text-center">
            <div className="text-muted-foreground">
              <AlertCircle className="mx-auto mb-2 size-8 opacity-50" />
              <p className="text-sm font-medium">No constraints defined</p>
              <p className="text-xs">
                Add constraints to restrict the optimization search space
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Validation feedback */}
      {!isValidating && hasWarnings && (
        <Alert>
          <AlertCircle className="size-4" />
          <AlertDescription>
            <div className="space-y-1">
              {globalValidation.warnings?.map((warning, index) => (
                <div key={index} className="text-sm">
                  {warning}
                </div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Feasibility estimate */}
      {samplingEnabled && feasibilityEstimate && hasConstraints && (
        <Alert>
          <Settings className="size-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span className="text-sm">
                Constraint feasibility:{" "}
                {(feasibilityEstimate.feasibilityRatio * 100).toFixed(1)}%
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={handleGenerateSamples}
                disabled={isGeneratingSamples}
              >
                {isGeneratingSamples ? "Generating..." : "Test Sampling"}
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Sampling results */}
      {samplingResult && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Sampling Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">
                  Samples generated:
                </span>
                <span className="ml-2 font-medium">
                  {samplingResult.samples.length}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Feasible samples:</span>
                <span className="ml-2 font-medium">
                  {samplingResult.feasibleSamples}
                </span>
              </div>
              {samplingResult.constraintViolations !== undefined && (
                <div>
                  <span className="text-muted-foreground">Violations:</span>
                  <span className="ml-2 font-medium">
                    {samplingResult.constraintViolations}
                  </span>
                </div>
              )}
              {samplingResult.totalAttempts !== undefined && (
                <div>
                  <span className="text-muted-foreground">Total attempts:</span>
                  <span className="ml-2 font-medium">
                    {samplingResult.totalAttempts}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Constraint Editor Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingConstraint?.id ? "Edit Constraint" : "Add New Constraint"}
            </DialogTitle>
            <DialogDescription>
              Configure constraint parameters and validation rules.
            </DialogDescription>
          </DialogHeader>

          {editingConstraint && (
            <ConstraintEditor
              constraint={editingConstraint}
              availableParameters={parameters.map(p => p.name)}
              onSave={updatedConstraint => {
                // Use the constraint directly (no conversion needed)

                // Check if this constraint already exists in the list
                const existingConstraint = editingConstraint?.id
                  ? constraints.find(c => c.id === editingConstraint.id)
                  : null

                if (existingConstraint) {
                  // Update existing constraint
                  updateConstraint(editingConstraint.id!, updatedConstraint)
                } else {
                  // Add new constraint
                  const newConstraint = {
                    ...updatedConstraint,
                    id: generateConstraintId()
                  }
                  addConstraint(newConstraint)
                }
                setIsDialogOpen(false)
                setEditingConstraint(null)

                toast({
                  title: "Constraint saved",
                  description: `${updatedConstraint.name || updatedConstraint.type} has been saved.`
                })
              }}
              onCancel={() => {
                setIsDialogOpen(false)
                setEditingConstraint(null)
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
