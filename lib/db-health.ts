"use server"

import { db } from "@/db/db"
import { sql } from "drizzle-orm"

/**
 * Checks if the database connection is healthy
 * @returns A promise that resolves to true if the database is healthy, false otherwise
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    // Simple query to check if the database is responding
    const result = await db.execute(sql`SELECT 1 as health`)
    return result.length > 0
  } catch (error) {
    console.error("Database health check failed:", error)
    return false
  }
}

/**
 * Waits for the database to be available
 * @param maxRetries Maximum number of retries
 * @param retryInterval Interval between retries in milliseconds
 * @returns A promise that resolves to true if the database becomes available, false otherwise
 */
export async function waitForDatabase(
  maxRetries: number = 5,
  retryInterval: number = 1000
): Promise<boolean> {
  let retries = 0

  while (retries < maxRetries) {
    const isHealthy = await checkDatabaseHealth()

    if (isHealthy) {
      return true
    }

    console.log(
      `Database not available, retrying in ${retryInterval}ms (${retries + 1}/${maxRetries})`
    )
    await new Promise(resolve => setTimeout(resolve, retryInterval))
    retries++
  }

  return false
}

/**
 * Executes a database operation with retry logic
 * @param operation The database operation to execute
 * @param maxRetries Maximum number of retries
 * @param retryInterval Interval between retries in milliseconds
 * @returns The result of the operation, or null if all retries fail
 */
export async function withDatabaseRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  retryInterval: number = 500
): Promise<T | null> {
  let retries = 0

  while (retries < maxRetries) {
    try {
      return await operation()
    } catch (error) {
      console.error(
        `Database operation failed (attempt ${retries + 1}/${maxRetries}):`,
        error
      )

      if (retries === maxRetries - 1) {
        // Last retry failed
        return null
      }

      await new Promise(resolve => setTimeout(resolve, retryInterval))
      retries++
    }
  }

  return null
}
