"use server"

import { auth } from "@clerk/nextjs/server"
import { getProfileByUserIdAction } from "./db/profiles-actions"
import { initializeTrialAction } from "./trial-actions"
import { getActualUserId } from "@/lib/auth-utils"

/**
 * Ensures that a user has a trial initialized if they don't already have one.
 * This function should be called when a user logs in or accesses protected routes.
 */
export async function ensureUserTrialAction() {
  try {
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 ENSURE TRIAL ACTION - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      console.log("ensureUserTrialAction: User not authenticated")
      return { success: false, message: "User not authenticated" }
    }

    console.log(`ensureUserTrialAction: Checking trial status for user ${userId}`)

    // Check if user already has a profile with trial data
    let profileResult;
    try {
      profileResult = await getProfileByUserIdAction(userId)
    } catch (dbError) {
      console.error(`Database error when checking profile: ${dbError}`)
      // Return a temporary success to prevent errors during login
      // The trial will be checked again on subsequent requests
      return {
        success: true,
        message: "Temporary trial access granted due to database connection issues",
        hasExistingTrial: true
      }
    }

    // If user has a profile with trial data, do nothing
    if (profileResult?.isSuccess &&
        profileResult.data &&
        (profileResult.data.trialStartedAt || profileResult.data.stripeSubscriptionId)) {

      console.log(`ensureUserTrialAction: User ${userId} already has trial data or subscription`)
      console.log(`Trial data: ${profileResult.data.trialStartedAt ? new Date(profileResult.data.trialStartedAt).toISOString() : 'none'}`)
      console.log(`Trial end: ${profileResult.data.trialEndsAt ? new Date(profileResult.data.trialEndsAt).toISOString() : 'none'}`)
      console.log(`Subscription: ${profileResult.data.stripeSubscriptionId || 'none'}`)

      return {
        success: true,
        message: "User already has trial data or subscription",
        hasExistingTrial: true
      }
    }

    console.log(`ensureUserTrialAction: Checking for academic verification before initializing trial for user ${userId}`)

    // Check if user has academic verification in progress or approved
    let hasAcademicVerification = false;
    let academicStatus: any = null;
    try {
      const { checkAcademicVerificationStatusAction } = await import("./academic-verification-actions")
      academicStatus = await checkAcademicVerificationStatusAction()

      if (academicStatus.isSuccess && academicStatus.status && academicStatus.status !== "none") {
        hasAcademicVerification = true
        console.log(`ensureUserTrialAction: User ${userId} has academic verification status: ${academicStatus.status}`)

        // If user is already approved, they should have a 90-day trial
        if (academicStatus.status === "approved") {
          console.log(`ensureUserTrialAction: User ${userId} is approved academic user, should already have 90-day trial`)
        }
      }
    } catch (academicError) {
      console.log(`ensureUserTrialAction: Could not check academic status: ${academicError}`)
      // Continue with regular trial initialization
    }

    // For users without academic verification, check if they might be going through academic signup
    // In this case, we'll give them a regular 30-day trial that can be upgraded later
    if (!hasAcademicVerification) {
      console.log(`ensureUserTrialAction: User ${userId} has no academic verification, will get standard 30-day trial`)
    }

    // If user has academic verification in progress, give them a temporary 30-day trial
    // that will be upgraded to 90 days when they complete verification
    const trialDays = hasAcademicVerification && academicStatus?.status === "approved" ? 90 : 30
    const isAcademic = hasAcademicVerification && academicStatus?.status === "approved"

    console.log(`ensureUserTrialAction: Initializing ${trialDays}-day trial for user ${userId} (academic: ${isAcademic})`)

    // Initialize trial for the user
    let trialResult;
    try {
      trialResult = await initializeTrialAction(userId, trialDays, isAcademic)

      if (!trialResult.isSuccess) {
        console.error("Failed to initialize trial:", trialResult.message)
        // If we can't initialize the trial, grant temporary access
        return {
          success: true,
          message: "Temporary trial access granted. Please try again later.",
          hasExistingTrial: true
        }
      }
    } catch (trialError) {
      console.error(`Error initializing trial: ${trialError}`)
      // If there's an error, grant temporary access
      return {
        success: true,
        message: "Temporary trial access granted due to initialization issues",
        hasExistingTrial: true
      }
    }

    console.log(`ensureUserTrialAction: Trial initialized successfully for user ${userId}`)

    return {
      success: true,
      message: "Trial initialized successfully",
      hasExistingTrial: false,
      trialEndsAt: trialResult.data?.trialEndsAt
    }
  } catch (error) {
    console.error("Error ensuring user trial:", error)
    console.error(`Stack trace: ${error instanceof Error ? error.stack : 'No stack trace'}`)
    return {
      success: false,
      message: `Error ensuring user trial: ${error instanceof Error ? error.message : "Unknown error"}`
    }
  }
}
