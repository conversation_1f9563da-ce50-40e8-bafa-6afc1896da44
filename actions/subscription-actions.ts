"use server"

import { auth } from "@clerk/nextjs/server"
import { getProfileByUserIdAction } from "./db/profiles-actions"
import { getActualUserId } from "@/lib/auth-utils"
import { stripe } from "@/lib/stripe"
import { getCurrentUserTrialStatusAction } from "./trial-actions"

type SubscriptionData = {
  tier: "free" | "trial" | "trial-expired" | "pro"
  status: string
  subscriptionType?: "monthly" | "yearly" | null
  trialEndsAt?: Date
  trialDaysLeft?: number
  currentPeriodEnd?: Date
  cancelAtPeriodEnd?: boolean
  stripeSubscriptionId?: string | null
}

export async function getSubscriptionAction(): Promise<SubscriptionData> {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🔍 SUBSCRIPTION ACTION - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  })

  if (!userId) {
    console.log(`[getSubscriptionAction] No user ID, returning free tier`)
    return {
      tier: "free",
      status: "active"
    }
  }

  // Get user profile
  const profileResult = await getProfileByUserIdAction(userId)

  if (!profileResult.isSuccess || !profileResult.data) {
    console.log(`[getSubscriptionAction] No profile found for user ${userId}, returning free tier`)
    return {
      tier: "free",
      status: "active"
    }
  }

  const profile = profileResult.data
  console.log(`[getSubscriptionAction] User ${userId} profile: membership=${profile.membership}, stripeSubscriptionId=${profile.stripeSubscriptionId ? 'present' : 'null'}`)

  // If user has a Stripe subscription, get details from Stripe
  if (profile.stripeSubscriptionId) {
    console.log(`[getSubscriptionAction] User has Stripe subscription ID, retrieving from Stripe`)
    try {
      const subscription = await stripe.subscriptions.retrieve(
        profile.stripeSubscriptionId
      )

      console.log(`[getSubscriptionAction] Stripe subscription status: ${subscription.status}, returning tier: ${profile.membership}`)
      return {
        tier: profile.membership,
        status: subscription.status,
        subscriptionType: profile.subscriptionType,
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        stripeSubscriptionId: subscription.id
      }
    } catch (error) {
      console.error("Error retrieving subscription from Stripe:", error)
      // Fall back to profile data if Stripe retrieval fails
      console.log(`[getSubscriptionAction] Stripe error, falling back to profile membership: ${profile.membership}`)
      return {
        tier: profile.membership,
        status: "active",
        subscriptionType: profile.subscriptionType,
        stripeSubscriptionId: profile.stripeSubscriptionId
      }
    }
  }

  // Check if user is on trial
  console.log(`[getSubscriptionAction] No Stripe subscription, checking trial status`)
  const trialResult = await getCurrentUserTrialStatusAction()

  if (trialResult.isSuccess && trialResult.data?.isActive) {
    console.log(`[getSubscriptionAction] User has active trial, returning trial tier`)
    return {
      tier: "trial",
      status: "trialing",
      trialEndsAt: trialResult.data.trialEndsAt,
      trialDaysLeft: trialResult.data.daysLeft,
      stripeSubscriptionId: null
    }
  }

  // Check if user had a trial that expired
  if (profile.hasTrialExpired === "true" && profile.trialStartedAt) {
    console.log(`[getSubscriptionAction] User had trial that expired, returning trial-expired tier`)
    return {
      tier: "trial-expired",
      status: "active",
      stripeSubscriptionId: null
    }
  }

  // Default to free tier
  console.log(`[getSubscriptionAction] No active trial, defaulting to profile membership: ${profile.membership}`)
  return {
    tier: profile.membership,
    status: "active",
    stripeSubscriptionId: null
  }
}
