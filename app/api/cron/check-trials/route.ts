import { updateExpiredTrialsAction } from "@/actions/trial-actions"
import { NextResponse } from "next/server"

// This route should be called by a scheduled job (e.g., Vercel Cron)
export async function GET(req: Request) {
  try {
    // Verify the request is authorized (optional, implement your own auth)
    const authHeader = req.headers.get("authorization")
    const cronSecret = process.env.CRON_SECRET

    if (cronSecret && (!authHeader || authHeader !== `Bearer ${cronSecret}`)) {
      return new Response("Unauthorized", { status: 401 })
    }

    // Update expired trials
    const result = await updateExpiredTrialsAction()

    if (!result.isSuccess) {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `Updated ${result.data?.updatedCount} expired trials`
    })
  } catch (error) {
    console.error("Error in check-trials cron job:", error)
    return NextResponse.json(
      {
        success: false,
        message: `Error in check-trials cron job: ${error instanceof Error ? error.message : "Unknown error"}`
      },
      { status: 500 }
    )
  }
}
