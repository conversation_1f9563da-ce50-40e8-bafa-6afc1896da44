#!/usr/bin/env node

/**
 * Monitor script to show complete Bayesian optimization data
 * This will find the "Test campaign build" optimization and show all data fed to suggestions
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, and, desc } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema definitions
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

const measurementsTable = pgTable("measurements", {
  id: uuid("id").primaryKey(),
  optimizationId: uuid("optimization_id").notNull(),
  parameters: jsonb("parameters").notNull(),
  targetValue: text("target_value").notNull(),
  targetValues: jsonb("target_values"),
  isRecommended: boolean("is_recommended").notNull(),
  batchId: text("batch_id"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

const samplesTable = pgTable("samples", {
  id: uuid("id").primaryKey(),
  optimizationId: uuid("optimization_id").notNull(),
  parameters: jsonb("parameters").notNull(),
  batchId: text("batch_id").notNull(),
  sampleIndex: text("sample_index").notNull(),
  samplingMethod: text("sampling_method").notNull(),
  sampleClass: text("sample_class").notNull(),
  targetValues: jsonb("target_values"),
  status: text("status").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function monitorBayesianOptimizationData() {
  console.log('🔍 MONITORING BAYESIAN OPTIMIZATION DATA');
  console.log('========================================');
  console.log('Looking for: "Test campaign working" optimization');
  console.log('');

  let client;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    // Find the optimization
    const optimizationName = "Test campaign working";
    const optimizationsList = await db.select().from(optimizationsTable);
    const opt = optimizationsList.find(o => o.name.toLowerCase().includes(optimizationName.toLowerCase()));
    
    if (!opt) {
      console.error(`❌ Optimization "${optimizationName}" not found`);
      console.log('\n📋 Available optimizations:');
      optimizationsList.slice(-10).forEach((o, index) => {
        console.log(`  ${index + 1}. ${o.name} (${o.optimizerId.slice(-12)})`);
      });
      return;
    }
    
    console.log(`✅ Found optimization: ${opt.name}`);
    console.log(`   Optimizer ID: ${opt.optimizerId}`);
    console.log(`   Status: ${opt.status}`);
    console.log(`   Created: ${opt.createdAt}`);
    console.log(`   Updated: ${opt.updatedAt}`);
    
    // Show complete configuration
    console.log(`\n📋 COMPLETE OPTIMIZATION CONFIGURATION:`);
    console.log('=====================================');
    if (opt.config) {
      console.log(JSON.stringify(opt.config, null, 2));
    } else {
      console.log('No configuration stored in database');
    }
    
    // Get database measurements
    console.log(`\n📊 DATABASE MEASUREMENTS:`);
    console.log('========================');
    
    const dbMeasurements = await db.select().from(measurementsTable)
      .where(eq(measurementsTable.optimizationId, opt.id))
      .orderBy(desc(measurementsTable.createdAt));
    
    console.log(`Found ${dbMeasurements.length} measurements in database:`);
    
    if (dbMeasurements.length > 0) {
      dbMeasurements.forEach((measurement, index) => {
        console.log(`\n  ${index + 1}. Measurement ${measurement.id.slice(-12)}:`);
        console.log(`     - Parameters: ${JSON.stringify(measurement.parameters)}`);
        console.log(`     - Target Value: ${measurement.targetValue}`);
        console.log(`     - Target Values: ${JSON.stringify(measurement.targetValues)}`);
        console.log(`     - Is Recommended: ${measurement.isRecommended}`);
        console.log(`     - Batch ID: ${measurement.batchId?.slice(-12) || 'none'}`);
        console.log(`     - Created: ${measurement.createdAt}`);
      });
    } else {
      console.log('  No measurements found in database');
    }
    
    // Get database samples
    console.log(`\n📋 DATABASE SAMPLES:`);
    console.log('==================');
    
    const dbSamples = await db.select().from(samplesTable)
      .where(eq(samplesTable.optimizationId, opt.id))
      .orderBy(desc(samplesTable.createdAt));
    
    console.log(`Found ${dbSamples.length} samples in database:`);
    
    if (dbSamples.length > 0) {
      dbSamples.forEach((sample, index) => {
        console.log(`\n  ${index + 1}. Sample ${sample.id.slice(-12)}:`);
        console.log(`     - Parameters: ${JSON.stringify(sample.parameters)}`);
        console.log(`     - Target Values: ${JSON.stringify(sample.targetValues)}`);
        console.log(`     - Sampling Method: ${sample.samplingMethod}`);
        console.log(`     - Sample Class: ${sample.sampleClass}`);
        console.log(`     - Status: ${sample.status}`);
        console.log(`     - Batch ID: ${sample.batchId?.slice(-12) || 'none'}`);
        console.log(`     - Created: ${sample.createdAt}`);
      });
    } else {
      console.log('  No samples found in database');
    }
    
    // Test backend campaign state
    console.log(`\n🔍 BACKEND CAMPAIGN STATE:`);
    console.log('=========================');

    let measurementsData = null;
    let backendMeasurements = [];

    try {
      // Get measurements from backend
      const measurementsResponse = await fetch(`http://localhost:8000/optimizations/${opt.optimizerId}/measurements`);

      if (measurementsResponse.ok) {
        measurementsData = await measurementsResponse.json();
        backendMeasurements = measurementsData.measurements || [];
        
        console.log(`Backend campaign measurements: ${backendMeasurements.length}`);
        
        if (backendMeasurements.length > 0) {
          console.log(`\nBackend measurements details:`);
          backendMeasurements.forEach((measurement, index) => {
            console.log(`\n  ${index + 1}. Backend Measurement:`);
            
            // Show all parameter values
            const params = {};
            const targets = {};
            const metadata = {};
            
            Object.entries(measurement).forEach(([key, value]) => {
              if (key.startsWith('Target')) {
                targets[key] = value;
              } else if (['BatchNr', 'FitNr', '_suggestionId', '_targetValues'].includes(key)) {
                metadata[key] = value;
              } else {
                params[key] = value;
              }
            });
            
            console.log(`     - Parameters: ${JSON.stringify(params)}`);
            console.log(`     - Targets: ${JSON.stringify(targets)}`);
            console.log(`     - Metadata: ${JSON.stringify(metadata)}`);
          });
          
          // Show target information
          if (measurementsData.target_info) {
            console.log(`\nTarget configuration:`);
            measurementsData.target_info.forEach((target, index) => {
              console.log(`  ${index + 1}. ${target.name}: ${target.mode}`);
            });
          }
        } else {
          console.log('  No measurements in backend campaign');
        }
      } else {
        console.log(`❌ Failed to get backend measurements: ${measurementsResponse.statusText}`);
      }
    } catch (error) {
      console.log(`❌ Error accessing backend: ${error.message}`);
    }
    
    // Now test suggestion generation to see what data is used
    console.log(`\n🎯 TESTING SUGGESTION GENERATION:`);
    console.log('================================');
    
    try {
      console.log('Generating suggestions to see what data is fed to Bayesian optimization...');
      
      const suggestionResponse = await fetch(`http://localhost:8000/optimizations/${opt.optimizerId}/suggest?batch_size=2`);
      
      if (suggestionResponse.ok) {
        const suggestionResult = await suggestionResponse.json();
        
        console.log(`✅ Suggestions generated successfully:`);
        console.log(`   Number of suggestions: ${suggestionResult.suggestions?.length || 0}`);
        console.log(`   Batch size: ${suggestionResult.batch_size}`);
        
        if (suggestionResult.suggestions && suggestionResult.suggestions.length > 0) {
          console.log(`\nGenerated suggestions:`);
          suggestionResult.suggestions.forEach((suggestion, index) => {
            console.log(`\n  ${index + 1}. Suggestion:`);
            
            // Show all parameter values in the suggestion
            Object.entries(suggestion).forEach(([key, value]) => {
              if (!key.startsWith('_')) {
                console.log(`     - ${key}: ${typeof value === 'number' ? value.toFixed(6) : value}`);
              }
            });
          });
        }
        
        console.log(`\n📊 BAYESIAN OPTIMIZATION INPUT SUMMARY:`);
        console.log(`======================================`);
        const backendCount = backendMeasurements.length;
        console.log(`✅ The Bayesian optimization used ${backendCount} measurements as training data`);
        console.log(`✅ Generated ${suggestionResult.suggestions?.length || 0} new suggestions`);

        if (dbMeasurements.length !== backendCount) {
          console.log(`⚠️  SYNCHRONIZATION ISSUE DETECTED:`);
          console.log(`   Database has ${dbMeasurements.length} measurements`);
          console.log(`   Backend campaign has ${backendCount} measurements`);
          console.log(`   Missing ${dbMeasurements.length - backendCount} measurements from backend`);
        } else {
          console.log(`✅ Database and backend are synchronized`);
        }
        
      } else {
        console.log(`❌ Failed to generate suggestions: ${suggestionResponse.statusText}`);
      }
    } catch (error) {
      console.log(`❌ Error generating suggestions: ${error.message}`);
    }
    
    console.log(`\n🎉 MONITORING COMPLETE`);
    console.log(`======================`);
    console.log(`Now click "Get Suggestions" in the UI and check the backend logs for detailed lifecycle logging.`);
    
  } catch (error) {
    console.error('❌ Error during monitoring:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

// Run the monitoring
monitorBayesianOptimizationData().catch(console.error);
