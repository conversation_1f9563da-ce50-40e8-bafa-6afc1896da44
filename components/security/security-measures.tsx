"use client"

import { motion } from "framer-motion"
import {
  Shield,
  Lock,
  Key,
  Eye,
  Server,
  Database,
  Globe,
  UserCheck
} from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"

const securityMeasures = [
  {
    icon: Lock,
    title: "End-to-End Encryption",
    description:
      "All data is encrypted in transit using TLS 1.3 and at rest using AES-256 encryption.",
    details: [
      "TLS 1.3 for all data transmission",
      "AES-256 encryption for data at rest",
      "Perfect Forward Secrecy (PFS)",
      "Certificate pinning for API communications"
    ]
  },
  {
    icon: UserCheck,
    title: "Authentication & Authorization",
    description:
      "Multi-layered authentication with industry-standard protocols and session management.",
    details: [
      "OAuth 2.0 and OpenID Connect",
      "Multi-factor authentication (MFA) support",
      "JWT tokens with short expiration",
      "Role-based access control (RBAC)"
    ]
  },
  {
    icon: Database,
    title: "Data Protection",
    description:
      "AWS RDS-powered data protection with enterprise-grade security and compliance.",
    details: [
      "AWS KMS encryption at rest and in transit",
      "VPC network isolation and security groups",
      "Complete data isolation between users",
      "Input validation and SQL injection prevention via ORM"
    ]
  },
  {
    icon: Server,
    title: "Infrastructure Security",
    description:
      "AWS cloud infrastructure with enterprise-grade security and compliance.",
    details: [
      "AWS RDS with Multi-AZ deployment",
      "AWS CloudWatch monitoring and alerting",
      "Automated security patching and updates",
      "Encrypted backups with point-in-time recovery"
    ]
  },
  {
    icon: Key,
    title: "API Security",
    description:
      "Secure API design with authentication, rate limiting, and comprehensive logging.",
    details: [
      "API key authentication",
      "Rate limiting and DDoS protection",
      "Request signing for critical operations",
      "Comprehensive audit logging"
    ]
  },
  {
    icon: Eye,
    title: "Privacy by Design",
    description:
      "Privacy-first approach with data minimization and user control.",
    details: [
      "Data minimization principles",
      "Granular privacy controls",
      "Automatic data purging",
      "Anonymous processing options"
    ]
  }
]

export function SecurityMeasures() {
  return (
    <section id="security-measures" className="py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Comprehensive Security Measures
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            We implement multiple layers of security to protect your data and
            ensure the integrity of your optimization experiments.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {securityMeasures.map((measure, index) => (
            <motion.div
              key={measure.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full transition-shadow hover:shadow-lg">
                <CardHeader>
                  <div className="mb-4 flex size-12 items-center justify-center rounded-lg bg-blue-100">
                    <measure.icon className="size-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl">{measure.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {measure.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {measure.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start">
                        <div className="mr-2 mt-1.5 size-1.5 shrink-0 rounded-full bg-blue-600" />
                        <span className="text-sm text-gray-700">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
