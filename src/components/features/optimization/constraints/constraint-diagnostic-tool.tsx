"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AlertTriangle, CheckCircle, Calculator, Info } from "lucide-react"

interface DiagnosticParameter {
  name: string
  min: number
  max: number
}

interface DiagnosticConstraint {
  parameters: string[]
  coefficients: number[]
  operator: string
  rhs: number
}

export function ConstraintDiagnosticTool() {
  const [parameters, setParameters] = useState<DiagnosticParameter[]>([
    { name: "P1", min: 20, max: 100 },
    { name: "P2", min: 0, max: 24 },
    { name: "P3", min: 0.1, max: 1 }
  ])

  const [constraint, setConstraint] = useState<DiagnosticConstraint>({
    parameters: ["P1", "P2", "P3"],
    coefficients: [1, 1, 1],
    operator: "<=",
    rhs: 50
  })

  const [analysis, setAnalysis] = useState<any>(null)

  const analyzeConstraint = () => {
    try {
      // Create parameter bounds lookup
      const paramBounds: Record<string, [number, number]> = {}
      parameters.forEach(p => {
        paramBounds[p.name] = [p.min, p.max]
      })

      // Calculate constraint bounds
      let minLhs = 0
      let maxLhs = 0

      for (let i = 0; i < constraint.parameters.length; i++) {
        const param = constraint.parameters[i]
        const coeff = constraint.coefficients[i]

        if (!paramBounds[param]) {
          setAnalysis({
            feasible: false,
            error: `Parameter ${param} not found in parameter list`
          })
          return
        }

        const [paramMin, paramMax] = paramBounds[param]

        if (coeff > 0) {
          minLhs += coeff * paramMin
          maxLhs += coeff * paramMax
        } else {
          minLhs += coeff * paramMax
          maxLhs += coeff * paramMin
        }
      }

      // Check feasibility
      let feasible = true
      let issues: string[] = []
      let suggestions: string[] = []

      if (constraint.operator === "<=") {
        if (minLhs > constraint.rhs) {
          feasible = false
          issues.push(
            `Constraint is always violated. Minimum possible LHS (${minLhs.toFixed(3)}) > RHS (${constraint.rhs})`
          )
          suggestions.push(
            `Increase RHS to at least ${minLhs.toFixed(3)} or adjust coefficients/parameter bounds`
          )
        }
      } else if (constraint.operator === ">=") {
        if (maxLhs < constraint.rhs) {
          feasible = false
          issues.push(
            `Constraint is always violated. Maximum possible LHS (${maxLhs.toFixed(3)}) < RHS (${constraint.rhs})`
          )
          suggestions.push(
            `Decrease RHS to at most ${maxLhs.toFixed(3)} or adjust coefficients/parameter bounds`
          )
        }
      } else if (constraint.operator === "=") {
        if (constraint.rhs < minLhs || constraint.rhs > maxLhs) {
          feasible = false
          issues.push(
            `Equality constraint cannot be satisfied. RHS (${constraint.rhs}) not in feasible range [${minLhs.toFixed(3)}, ${maxLhs.toFixed(3)}]`
          )
          suggestions.push(
            `Set RHS between ${minLhs.toFixed(3)} and ${maxLhs.toFixed(3)}`
          )
        }
      }

      setAnalysis({
        feasible,
        minLhs,
        maxLhs,
        issues,
        suggestions,
        constraintEquation: `${constraint.coefficients.map((c, i) => `${c}×${constraint.parameters[i]}`).join(" + ")} ${constraint.operator} ${constraint.rhs}`
      })
    } catch (error) {
      setAnalysis({
        feasible: false,
        error: error instanceof Error ? error.message : "Analysis failed"
      })
    }
  }

  const updateParameter = (
    index: number,
    field: keyof DiagnosticParameter,
    value: string | number
  ) => {
    const newParams = [...parameters]
    newParams[index] = { ...newParams[index], [field]: value }
    setParameters(newParams)
  }

  const updateConstraint = (field: keyof DiagnosticConstraint, value: any) => {
    setConstraint({ ...constraint, [field]: value })
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="size-5" />
            Constraint Feasibility Diagnostic Tool
          </CardTitle>
          <CardDescription>
            Analyze your constraint configuration to identify feasibility issues
            before optimization. This tool helps diagnose "No feasible point
            found" errors.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Parameters Section */}
          <div>
            <Label className="text-base font-medium">Parameters & Bounds</Label>
            <div className="mt-2 grid gap-4">
              {parameters.map((param, index) => (
                <div
                  key={index}
                  className="grid grid-cols-3 items-center gap-2"
                >
                  <Input
                    placeholder="Parameter name"
                    value={param.name}
                    onChange={e =>
                      updateParameter(index, "name", e.target.value)
                    }
                  />
                  <Input
                    type="number"
                    placeholder="Min"
                    value={param.min}
                    onChange={e =>
                      updateParameter(
                        index,
                        "min",
                        parseFloat(e.target.value) || 0
                      )
                    }
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    value={param.max}
                    onChange={e =>
                      updateParameter(
                        index,
                        "max",
                        parseFloat(e.target.value) || 0
                      )
                    }
                  />
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Constraint Section */}
          <div>
            <Label className="text-base font-medium">Linear Constraint</Label>
            <div className="mt-2 grid gap-4">
              <div>
                <Label className="text-sm">
                  Coefficients (one per parameter)
                </Label>
                <Input
                  placeholder="e.g., 1,1,1"
                  value={constraint.coefficients.join(",")}
                  onChange={e => {
                    const coeffs = e.target.value
                      .split(",")
                      .map(c => parseFloat(c.trim()) || 0)
                    updateConstraint("coefficients", coeffs)
                  }}
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-sm">Operator</Label>
                  <Select
                    value={constraint.operator}
                    onValueChange={value => updateConstraint("operator", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="<=">&lt;=</SelectItem>
                      <SelectItem value=">=">&gt;=</SelectItem>
                      <SelectItem value="=">=</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-sm">Right-hand side (RHS)</Label>
                  <Input
                    type="number"
                    value={constraint.rhs}
                    onChange={e =>
                      updateConstraint("rhs", parseFloat(e.target.value) || 0)
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          <Button onClick={analyzeConstraint} className="w-full">
            Analyze Constraint Feasibility
          </Button>

          {/* Analysis Results */}
          {analysis && (
            <Alert variant={analysis.feasible ? "default" : "destructive"}>
              {analysis.feasible ? (
                <CheckCircle className="size-4" />
              ) : (
                <AlertTriangle className="size-4" />
              )}
              <AlertTitle>
                {analysis.feasible
                  ? "Constraint Appears Feasible"
                  : "Feasibility Issues Detected"}
              </AlertTitle>
              <AlertDescription className="space-y-3">
                {analysis.error ? (
                  <p>{analysis.error}</p>
                ) : (
                  <>
                    <div>
                      <p>
                        <strong>Constraint:</strong>{" "}
                        {analysis.constraintEquation}
                      </p>
                      <p>
                        <strong>LHS Range:</strong> [
                        {analysis.minLhs?.toFixed(3)},{" "}
                        {analysis.maxLhs?.toFixed(3)}]
                      </p>
                    </div>

                    {analysis.issues && analysis.issues.length > 0 && (
                      <div>
                        <p className="font-medium">Issues:</p>
                        <ul className="list-inside list-disc space-y-1">
                          {analysis.issues.map(
                            (issue: string, index: number) => (
                              <li key={index} className="text-sm">
                                {issue}
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    {analysis.suggestions &&
                      analysis.suggestions.length > 0 && (
                        <div>
                          <p className="font-medium">Suggestions:</p>
                          <ul className="list-inside list-disc space-y-1">
                            {analysis.suggestions.map(
                              (suggestion: string, index: number) => (
                                <li
                                  key={index}
                                  className="text-sm text-blue-700 dark:text-blue-300"
                                >
                                  {suggestion}
                                </li>
                              )
                            )}
                          </ul>
                        </div>
                      )}
                  </>
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
