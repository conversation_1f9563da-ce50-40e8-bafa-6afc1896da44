"use client"

import React, { useState, useC<PERSON>back, useRef, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  Lightbulb,
  Zap,
  Clock,
  TrendingUp
} from "lucide-react"
import { useRealTimeConstraintValidation } from "@/lib/constraints/hooks"
import {
  Constraint,
  Parameter,
  EnhancedValidationResult,
  RealTimeValidationConfig
} from "@/lib/constraints/types"

interface RealTimeConstraintEditorProps {
  constraint: Constraint
  parameters: Parameter[]
  onConstraintChange: (constraint: Constraint) => void
  onValidationChange?: (result: EnhancedValidationResult) => void
  config?: Partial<RealTimeValidationConfig>
  className?: string
}

export function RealTimeConstraintEditor({
  constraint,
  parameters,
  onConstraintChange,
  onValidationChange,
  config = {},
  className = ""
}: RealTimeConstraintEditorProps) {
  const [constraintText, setConstraintText] = useState("")
  const [cursorPosition, setCursorPosition] = useState(0)
  const [showSuggestions, setShowSuggestions] = useState(false)

  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const validationConfig: RealTimeValidationConfig = {
    enabled: true,
    debounceMs: 300,
    validateOnType: true,
    validateOnBlur: true,
    showInlineErrors: true,
    showSuggestions: true,
    enableFeasibilityCheck: true,
    feasibilitySampleSize: 100,
    enableInteractionDetection: true,
    maxValidationTime: 5000,
    ...config
  }

  const {
    validationResults,
    isValidating,
    autoCompleteResults,
    syntaxErrors,
    validateWithDebounce,
    validateOnBlur,
    generateAutoComplete,
    getValidationResult
  } = useRealTimeConstraintValidation(validationConfig)

  const currentValidation = constraint.id
    ? getValidationResult(constraint.id)
    : null

  /**
   * Handle constraint text changes
   */
  const handleConstraintTextChange = useCallback(
    (value: string) => {
      setConstraintText(value)

      // Update constraint object based on text (simplified)
      const updatedConstraint = { ...constraint }
      // This would need proper parsing logic based on constraint type
      onConstraintChange(updatedConstraint)

      // Trigger real-time validation
      if (validationConfig.validateOnType) {
        validateWithDebounce(constraint, parameters, value)
      }
    },
    [
      constraint,
      parameters,
      onConstraintChange,
      validateWithDebounce,
      validationConfig.validateOnType
    ]
  )

  /**
   * Handle blur events
   */
  const handleBlur = useCallback(() => {
    validateOnBlur(constraint, parameters, constraintText)
    setShowSuggestions(false)
  }, [constraint, parameters, constraintText, validateOnBlur])

  /**
   * Handle cursor position changes for auto-complete
   */
  const handleCursorChange = useCallback(
    (event: React.ChangeEvent<HTMLTextAreaElement>) => {
      const position = event.target.selectionStart
      setCursorPosition(position)

      if (validationConfig.showSuggestions) {
        const autoComplete = generateAutoComplete(
          constraintText,
          position,
          parameters
        )
        if (autoComplete.suggestions.length > 0) {
          setShowSuggestions(true)
        }
      }
    },
    [
      constraintText,
      parameters,
      generateAutoComplete,
      validationConfig.showSuggestions
    ]
  )

  /**
   * Handle constraint name changes
   */
  const handleNameChange = useCallback(
    (name: string) => {
      onConstraintChange({ ...constraint, name })
    },
    [constraint, onConstraintChange]
  )

  /**
   * Handle constraint description changes
   */
  const handleDescriptionChange = useCallback(
    (description: string) => {
      onConstraintChange({ ...constraint, description })
    },
    [constraint, onConstraintChange]
  )

  /**
   * Apply a suggestion
   */
  const applySuggestion = useCallback(
    (suggestion: any) => {
      if (!textareaRef.current) return

      const textarea = textareaRef.current
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newText =
        constraintText.substring(0, start) +
        suggestion.insertText +
        constraintText.substring(end)

      setConstraintText(newText)
      handleConstraintTextChange(newText)
      setShowSuggestions(false)

      // Set cursor position after inserted text
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(
          start + suggestion.insertText.length,
          start + suggestion.insertText.length
        )
      }, 0)
    },
    [constraintText, handleConstraintTextChange]
  )

  // Notify parent of validation changes
  useEffect(() => {
    if (currentValidation && onValidationChange) {
      onValidationChange(currentValidation)
    }
  }, [currentValidation, onValidationChange])

  const getValidationIcon = () => {
    if (isValidating) return <Clock className="size-4 animate-spin" />
    if (!currentValidation) return null

    if (!currentValidation.valid)
      return <XCircle className="size-4 text-red-500" />
    if (currentValidation.warnings.length > 0)
      return <AlertTriangle className="size-4 text-yellow-500" />
    return <CheckCircle className="size-4 text-green-500" />
  }

  const getValidationStatus = () => {
    if (isValidating) return "Validating..."
    if (!currentValidation) return "Not validated"

    if (!currentValidation.valid) return "Invalid"
    if (currentValidation.warnings.length > 0) return "Valid with warnings"
    return "Valid"
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Constraint Editor</span>
          <div className="flex items-center gap-2">
            {getValidationIcon()}
            <span className="text-muted-foreground text-sm">
              {getValidationStatus()}
            </span>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Basic constraint info */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="constraint-name">Name</Label>
            <Input
              id="constraint-name"
              value={constraint.name || ""}
              onChange={e => handleNameChange(e.target.value)}
              placeholder="Enter constraint name"
            />
          </div>
          <div>
            <Label htmlFor="constraint-type">Type</Label>
            <Input
              id="constraint-type"
              value={constraint.type}
              disabled
              className="bg-muted"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="constraint-description">Description</Label>
          <Input
            id="constraint-description"
            value={constraint.description || ""}
            onChange={e => handleDescriptionChange(e.target.value)}
            placeholder="Enter constraint description"
          />
        </div>

        {/* Constraint expression editor */}
        <div className="relative">
          <Label htmlFor="constraint-expression">Constraint Expression</Label>
          <div className="relative">
            <Textarea
              ref={textareaRef}
              id="constraint-expression"
              value={constraintText}
              onChange={e => {
                handleConstraintTextChange(e.target.value)
                handleCursorChange(e)
              }}
              onBlur={handleBlur}
              onFocus={() => setShowSuggestions(true)}
              placeholder="Enter constraint expression..."
              className={`min-h-[100px] font-mono ${
                currentValidation && !currentValidation.valid
                  ? "border-red-500"
                  : ""
              }`}
            />

            {/* Auto-complete suggestions */}
            {showSuggestions &&
              autoCompleteResults &&
              autoCompleteResults.suggestions.length > 0 && (
                <div className="absolute inset-x-0 top-full z-10 max-h-48 overflow-y-auto rounded-md border border-gray-200 bg-white shadow-lg">
                  {autoCompleteResults.suggestions
                    .slice(0, 10)
                    .map((suggestion, index) => (
                      <button
                        key={index}
                        className="flex w-full items-center justify-between px-3 py-2 text-left hover:bg-gray-100"
                        onClick={() => applySuggestion(suggestion)}
                      >
                        <div>
                          <span className="font-mono">
                            {suggestion.displayText}
                          </span>
                          <span className="ml-2 text-sm text-gray-500">
                            {suggestion.description}
                          </span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {suggestion.type}
                        </Badge>
                      </button>
                    ))}
                </div>
              )}
          </div>
        </div>

        {/* Validation results */}
        {currentValidation && (
          <div className="space-y-3">
            {/* Errors */}
            {!currentValidation.valid && currentValidation.error && (
              <Alert variant="destructive">
                <XCircle className="size-4" />
                <AlertDescription>{currentValidation.error}</AlertDescription>
              </Alert>
            )}

            {/* Warnings */}
            {currentValidation.warnings.map((warning, index) => (
              <Alert
                key={index}
                variant="default"
                className="border-yellow-500"
              >
                <AlertTriangle className="size-4 text-yellow-500" />
                <AlertDescription>
                  <strong>{warning.type}:</strong> {warning.message}
                  {warning.recommendation && (
                    <div className="text-muted-foreground mt-1 text-sm">
                      Recommendation: {warning.recommendation}
                    </div>
                  )}
                </AlertDescription>
              </Alert>
            ))}

            {/* Suggestions */}
            {validationConfig.showSuggestions &&
              currentValidation.suggestions.map((suggestion, index) => (
                <Alert
                  key={index}
                  variant="default"
                  className="border-blue-500"
                >
                  <Lightbulb className="size-4 text-blue-500" />
                  <AlertDescription>
                    <strong>{suggestion.title}</strong>
                    <div className="mt-1">{suggestion.description}</div>
                    {suggestion.benefits.length > 0 && (
                      <div className="mt-2">
                        <strong>Benefits:</strong>
                        <ul className="list-inside list-disc text-sm">
                          {suggestion.benefits.map((benefit, i) => (
                            <li key={i}>{benefit}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    <div className="mt-2 flex items-center gap-2">
                      <Badge variant="outline">
                        Confidence: {suggestion.confidence}%
                      </Badge>
                      <Button size="sm" variant="outline">
                        Apply Suggestion
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}

            {/* Performance metrics */}
            {currentValidation.performanceMetrics && (
              <div className="text-muted-foreground flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Zap className="size-3" />
                  <span>
                    Validation:{" "}
                    {currentValidation.performanceMetrics.validationTime}ms
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <TrendingUp className="size-3" />
                  <span>
                    Feasibility: {currentValidation.feasibilityScore}%
                  </span>
                </div>
              </div>
            )}

            {/* Interaction issues */}
            {currentValidation.interactionIssues.map((issue, index) => (
              <Alert
                key={index}
                variant="default"
                className="border-orange-500"
              >
                <AlertTriangle className="size-4 text-orange-500" />
                <AlertDescription>
                  <strong>
                    {issue.type} ({issue.severity}):
                  </strong>{" "}
                  {issue.description}
                  <div className="mt-2">
                    <strong>Resolution:</strong> {issue.resolution.strategy}
                    <ul className="mt-1 list-inside list-disc text-sm">
                      {issue.resolution.steps.map((step, i) => (
                        <li key={i}>{step}</li>
                      ))}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            ))}
          </div>
        )}

        {/* Syntax errors */}
        {constraint.id &&
          syntaxErrors[constraint.id] &&
          syntaxErrors[constraint.id].length > 0 && (
            <div className="space-y-2">
              {syntaxErrors[constraint.id].map((error, index) => (
                <Alert key={index} variant="destructive">
                  <XCircle className="size-4" />
                  <AlertDescription>
                    <strong>Syntax Error:</strong> {error.message}
                    {error.autoFix && (
                      <Button size="sm" variant="outline" className="ml-2">
                        Auto-fix
                      </Button>
                    )}
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}
      </CardContent>
    </Card>
  )
}
