/*
This client component provides a blurred dashboard background that precisely mimics the actual dashboard home page layout with accurate dimensions and responsive behavior.
*/

"use client"

export function BlurredDashboardBackground() {
  return (
    <div className="absolute inset-0 blur-sm brightness-75">
      <div className="flex h-screen w-full overflow-hidden">
        {/* Sidebar mockup - matches exact dashboard sidebar dimensions */}
        <div className="bg-sidebar border-sidebar-border flex h-screen w-64 flex-col border-r">
          {/* Logo and App name - matches dashboard-sidebar.tsx */}
          <div className="flex items-center gap-2 p-4">
            <div className="bg-primary/30 size-6 shrink-0 rounded"></div>
            <div className="bg-primary/20 h-6 w-20 rounded"></div>
          </div>

          {/* Collapse/expand button placeholder */}
          <div className="my-1 mr-2 self-end">
            <div className="bg-primary/15 size-8 rounded"></div>
          </div>

          {/* Main navigation - matches dashboard links */}
          <nav className="flex-1 p-2">
            <div className="space-y-1">
              <div className="bg-primary/20 h-10 rounded-md"></div>
              <div className="bg-primary/15 h-10 rounded-md"></div>
            </div>
          </nav>

          {/* Beta feedback note area - matches BetaFeedbackNote component */}
          <div className="m-3 rounded-lg border-2 border-dashed border-purple-500/50 p-3">
            <div className="mb-2 flex justify-center">
              <div className="size-24 rounded-lg bg-purple-500/50"></div>
            </div>
            <div className="space-y-1">
              <div className="h-3 w-full rounded bg-purple-100/50"></div>
              <div className="h-3 w-3/4 rounded bg-purple-100/50"></div>
              <div className="h-3 w-1/2 rounded bg-purple-100/50"></div>
            </div>
            <div className="mt-2 h-8 w-full rounded bg-purple-200/50"></div>
          </div>

          {/* User profile area */}
          <div className="p-4">
            <div className="flex items-center gap-2">
              <div className="bg-primary/20 size-8 rounded-full"></div>
              <div className="flex-1 space-y-1">
                <div className="bg-primary/15 h-4 w-24 rounded"></div>
                <div className="bg-primary/10 h-3 w-16 rounded"></div>
              </div>
              <div className="bg-primary/15 size-4 rounded"></div>
            </div>
          </div>
        </div>

        {/* Main content area - matches dashboard layout exactly */}
        <main className="flex-1 overflow-auto p-6">
          <div className="container mx-auto">
            {/* Dashboard title - matches h1 styling */}
            <div className="bg-primary/20 mb-6 h-9 w-32 rounded"></div>

            {/* Stats cards row - matches exact grid: grid-cols-1 gap-6 md:grid-cols-3 */}
            <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-3">
              {[
                "Total Experiments",
                "Active Optimizations",
                "Efficiency Score"
              ].map((_, i) => (
                <div key={i} className="bg-muted rounded-lg shadow-sm">
                  {/* CardHeader with pb-2 */}
                  <div className="p-6 pb-2">
                    <div className="bg-primary/15 h-4 w-28 rounded"></div>
                  </div>
                  {/* CardContent */}
                  <div className="px-6 pb-6">
                    <div className="bg-primary/25 h-8 w-12 rounded font-bold"></div>
                  </div>
                </div>
              ))}
            </div>

            {/* Two-column layout - matches exact grid: grid-cols-1 gap-6 lg:grid-cols-2 */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {/* Recent Optimizations Card */}
              <div className="bg-muted rounded-lg shadow-sm">
                {/* CardHeader */}
                <div className="p-6">
                  <div className="bg-primary/20 h-6 w-36 rounded"></div>
                </div>
                {/* CardContent */}
                <div className="px-6 pb-6">
                  <div className="space-y-4">
                    {[1, 2, 3].map(i => (
                      <div
                        key={i}
                        className="flex items-center justify-between border-b pb-2"
                      >
                        <div className="space-y-1">
                          <div className="bg-primary/15 h-4 w-32 rounded"></div>
                          <div className="bg-primary/10 h-3 w-20 rounded"></div>
                        </div>
                        <div className="h-6 w-16 rounded bg-green-200/50"></div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Performance Metrics Card */}
              <div className="bg-muted rounded-lg shadow-sm">
                {/* CardHeader */}
                <div className="p-6">
                  <div className="bg-primary/20 h-6 w-32 rounded"></div>
                </div>
                {/* CardContent */}
                <div className="px-6 pb-6">
                  {/* Matches exact h-[300px] from dashboard */}
                  <div className="bg-primary/10 flex h-[300px] items-center justify-center rounded-md border">
                    <div className="bg-primary/15 h-4 w-48 rounded"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Bottom CTA card - matches DashboardHomeClient component */}
            <div className="mt-6">
              <div className="from-primary/5 to-background rounded-lg border bg-gradient-to-br shadow-md">
                {/* CardHeader */}
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="bg-primary/30 mr-2 size-6 rounded"></div>
                    <div className="bg-primary/20 h-6 w-40 rounded"></div>
                  </div>
                </div>
                {/* CardContent */}
                <div className="px-6 pb-6">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div className="mb-4 space-y-2 md:mb-0 md:max-w-[60%]">
                      <div className="bg-primary/15 h-4 w-64 rounded"></div>
                      <div className="bg-primary/15 h-4 w-56 rounded"></div>
                      <div className="bg-primary/10 h-3 w-48 rounded"></div>
                      <div className="bg-primary/10 h-3 w-40 rounded"></div>
                    </div>
                    <div className="bg-primary/30 h-10 w-32 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
