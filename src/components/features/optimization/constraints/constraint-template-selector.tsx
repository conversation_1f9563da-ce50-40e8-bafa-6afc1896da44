"use client"

import { useState } from "react"
import {
  Zap,
  ChevronDown,
  Calculator,
  Hash,
  X,
  Beaker,
  DollarSign,
  Users
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import { ConstraintTemplate } from "@/lib/constraints"

// Predefined constraint templates for common use cases
const CONSTRAINT_TEMPLATES: ConstraintTemplate[] = [
  {
    id: "mixture_ratios",
    name: "Mixture Ratios",
    description: "Components must sum to 100% (mixture constraint)",
    category: "Chemical/Materials",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["component_a", "component_b", "component_c"],
      operator: "=",
      coefficients: [1.0, 1.0, 1.0],
      rhs: 1.0,
      description: "All components must sum to 1.0 (100%)"
    },
    parameterPlaceholders: ["component_a", "component_b", "component_c"]
  },
  {
    id: "budget_constraint",
    name: "Budget Constraint",
    description: "Total cost must not exceed budget limit",
    category: "Economic",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["material_1", "material_2", "labor"],
      operator: "<=",
      coefficients: [10.0, 15.0, 25.0],
      rhs: 1000.0,
      description: "Total cost ≤ budget"
    },
    parameterPlaceholders: ["material_1", "material_2", "labor"]
  },
  {
    id: "parameter_comparison",
    name: "Parameter Comparison",
    description: "One parameter must be strictly less than another",
    category: "Comparison",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["x19", "x20"],
      operator: "<",
      coefficients: [1.0, -1.0],
      rhs: 0.0,
      description: "x19 < x20"
    },
    parameterPlaceholders: ["x19", "x20"]
  },
  {
    id: "sum_constraint",
    name: "Sum Constraint",
    description: "Sum of parameters must be less than or equal to a value",
    category: "Mathematical",
    constraint: {
      type: "ContinuousLinearConstraint",
      parameters: ["x6", "x15"],
      operator: "<=",
      coefficients: [1.0, 1.0],
      rhs: 1.0,
      description: "x6 + x15 ≤ 1.0"
    },
    parameterPlaceholders: ["x6", "x15"]
  },
  {
    id: "incompatible_solvents",
    name: "Incompatible Solvents",
    description: "Exclude dangerous solvent combinations",
    category: "Safety",
    constraint: {
      type: "DiscreteExcludeConstraint",
      parameters: ["solvent_1", "solvent_2"],
      combiner: "AND",
      conditions: [
        { type: "subselection", selection: ["ethanol", "methanol"] },
        { type: "subselection", selection: ["dmf", "dmso"] }
      ],
      description: "Avoid mixing alcohols with polar aprotic solvents"
    },
    parameterPlaceholders: ["solvent_1", "solvent_2"]
  },
  {
    id: "max_active_components",
    name: "Maximum Active Components",
    description: "Limit number of active components in formulation",
    category: "Formulation",
    constraint: {
      type: "DiscreteCardinalityConstraint",
      parameters: ["comp_1", "comp_2", "comp_3", "comp_4"],
      max_cardinality: 2,
      description: "Use at most 2 components"
    },
    parameterPlaceholders: ["comp_1", "comp_2", "comp_3", "comp_4"]
  },
  {
    id: "temperature_pressure_safety",
    name: "Temperature-Pressure Safety",
    description: "Safety limit for high temperature and pressure",
    category: "Safety",
    constraint: {
      type: "DiscreteCustomConstraint",
      parameters: ["temperature", "pressure"],
      validator: "temperature * pressure <= 1000",
      description: "Temperature × Pressure safety limit"
    },
    parameterPlaceholders: ["temperature", "pressure"]
  },
  {
    id: "minimum_diversity",
    name: "Minimum Diversity",
    description: "Ensure minimum number of different parameters are used",
    category: "Design",
    constraint: {
      type: "ContinuousCardinalityConstraint",
      parameters: ["param_1", "param_2", "param_3"],
      min_cardinality: 2,
      description: "Use at least 2 parameters"
    },
    parameterPlaceholders: ["param_1", "param_2", "param_3"]
  }
]

// Template categories with icons
const TEMPLATE_CATEGORIES = {
  "Chemical/Materials": { icon: Beaker, color: "blue" },
  Economic: { icon: DollarSign, color: "green" },
  Safety: { icon: X, color: "red" },
  Formulation: { icon: Hash, color: "purple" },
  Design: { icon: Users, color: "orange" }
}

interface ConstraintTemplateSelectorProps {
  onTemplateSelect: (template: ConstraintTemplate) => void
  availableParameters: string[]
  parameters: Array<{ name: string; type: string }> // Add parameter type information
  className?: string
}

export function ConstraintTemplateSelector({
  onTemplateSelect,
  availableParameters,
  parameters,
  className
}: ConstraintTemplateSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Group templates by category
  const groupedTemplates = CONSTRAINT_TEMPLATES.reduce(
    (acc, template) => {
      if (!acc[template.category]) {
        acc[template.category] = []
      }
      acc[template.category].push(template)
      return acc
    },
    {} as Record<string, ConstraintTemplate[]>
  )

  // Helper function to check if constraint type is compatible with parameter type
  const isConstraintParameterCompatible = (
    constraintType: string,
    parameterType: string
  ): boolean => {
    const discreteConstraints = [
      "DiscreteExcludeConstraint",
      "DiscreteCardinalityConstraint",
      "DiscreteSumConstraint",
      "DiscreteProductConstraint",
      "DiscreteDependenciesConstraint",
      "DiscreteLinkedParametersConstraint",
      "DiscreteNoLabelDuplicatesConstraint",
      "DiscretePermutationInvarianceConstraint",
      "DiscreteCustomConstraint",
      "DiscreteLinearConstraint"
    ]

    const continuousConstraints = [
      "ContinuousLinearConstraint",
      "ContinuousCardinalityConstraint"
    ]

    if (discreteConstraints.includes(constraintType)) {
      return (
        parameterType === "NumericalDiscrete" ||
        parameterType === "CategoricalParameter"
      )
    }

    if (continuousConstraints.includes(constraintType)) {
      return parameterType === "NumericalContinuous"
    }

    return true // Unknown constraint types are allowed
  }

  // Helper function to find compatible parameters for a constraint type
  const findCompatibleParameters = (
    constraintType: string,
    requiredCount: number
  ): string[] => {
    const compatibleParams = parameters.filter(param =>
      isConstraintParameterCompatible(constraintType, param.type)
    )

    return compatibleParams.slice(0, requiredCount).map(p => p.name)
  }

  // Helper function to check if a template is compatible with current parameters
  const isTemplateCompatible = (template: ConstraintTemplate): boolean => {
    const constraintType = template.constraint.type || ""
    const requiredCount = template.parameterPlaceholders.length
    const compatibleParams = findCompatibleParameters(
      constraintType,
      requiredCount
    )
    return compatibleParams.length >= requiredCount
  }

  // Helper function to get compatibility info for a template
  const getTemplateCompatibilityInfo = (
    template: ConstraintTemplate
  ): {
    compatible: boolean
    message?: string
    compatibleCount: number
    requiredCount: number
  } => {
    const constraintType = template.constraint.type || ""
    const requiredCount = template.parameterPlaceholders.length
    const compatibleParams = findCompatibleParameters(
      constraintType,
      requiredCount
    )
    const compatible = compatibleParams.length >= requiredCount

    if (!compatible) {
      const parameterTypeNeeded = constraintType.includes("Discrete")
        ? "discrete (NumericalDiscrete or Categorical)"
        : "continuous (NumericalContinuous)"

      return {
        compatible: false,
        message: `Needs ${requiredCount} ${parameterTypeNeeded} parameter(s), but only ${compatibleParams.length} available`,
        compatibleCount: compatibleParams.length,
        requiredCount
      }
    }

    return {
      compatible: true,
      compatibleCount: compatibleParams.length,
      requiredCount
    }
  }

  const handleTemplateSelect = (template: ConstraintTemplate) => {
    const mappedTemplate = { ...template }
    const constraintType = template.constraint.type || ""
    const requiredParamCount = template.parameterPlaceholders.length

    // Find compatible parameters for this constraint type
    const compatibleParameters = findCompatibleParameters(
      constraintType,
      requiredParamCount
    )

    if (compatibleParameters.length >= requiredParamCount) {
      // Map to compatible parameters
      mappedTemplate.constraint = {
        ...template.constraint,
        parameters: compatibleParameters
      }
      onTemplateSelect(mappedTemplate)
      setIsOpen(false)
    } else {
      // Show helpful error message with guidance
      const parameterTypeNeeded = constraintType.includes("Discrete")
        ? "discrete (NumericalDiscrete or Categorical)"
        : "continuous (NumericalContinuous)"

      const currentParameterTypes = parameters
        .map(p => `${p.name}: ${p.type}`)
        .join(", ")

      toast({
        title: "Incompatible Parameter Types",
        description: `The "${template.name}" template requires ${requiredParamCount} ${parameterTypeNeeded} parameter(s), but you only have ${compatibleParameters.length} compatible parameter(s).`,
        variant: "destructive"
      })

      // Additional guidance toast
      setTimeout(() => {
        toast({
          title: "How to Fix This",
          description: `Current parameters: ${currentParameterTypes}. To use this template, either: 1) Change your parameter types to match the constraint requirements, or 2) Choose a different template that's compatible with your parameter types.`,
          variant: "default"
        })
      }, 1000)
    }
  }

  const getConstraintTypeLabel = (type: string) => {
    return type
      .replace("Constraint", "")
      .replace(/([A-Z])/g, " $1")
      .trim()
  }

  return (
    <Card className={className}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="hover:bg-muted/50 cursor-pointer transition-colors">
            <CardTitle className="flex items-center justify-between text-base">
              <div className="flex items-center gap-2">
                <Zap className="size-4 text-orange-500" />
                <span>Quick Templates</span>
                <Badge variant="secondary" className="text-xs">
                  {CONSTRAINT_TEMPLATES.length} templates
                </Badge>
              </div>
              <ChevronDown
                className={`size-4 transition-transform ${isOpen ? "rotate-180" : ""}`}
              />
            </CardTitle>
            <CardDescription>
              Start with common constraint patterns
            </CardDescription>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="space-y-4">
            {Object.entries(groupedTemplates).map(([category, templates]) => {
              const categoryInfo =
                TEMPLATE_CATEGORIES[
                  category as keyof typeof TEMPLATE_CATEGORIES
                ]
              const Icon = categoryInfo?.icon || Calculator

              return (
                <div key={category} className="space-y-2">
                  <div className="text-muted-foreground flex items-center gap-2 text-sm font-medium">
                    <Icon className="size-4" />
                    <span>{category}</span>
                  </div>

                  <div className="grid gap-2 md:grid-cols-2">
                    {templates.map(template => {
                      const compatibilityInfo =
                        getTemplateCompatibilityInfo(template)
                      const isCompatible = compatibilityInfo.compatible

                      return (
                        <Card
                          key={template.id}
                          className={`border-dashed transition-colors ${
                            isCompatible
                              ? "hover:bg-muted/50 cursor-pointer"
                              : "bg-muted/20 cursor-not-allowed opacity-60"
                          }`}
                          onClick={() =>
                            isCompatible && handleTemplateSelect(template)
                          }
                        >
                          <CardContent className="p-3">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <h4
                                  className={`text-sm font-medium ${!isCompatible ? "text-muted-foreground" : ""}`}
                                >
                                  {template.name}
                                </h4>
                                <div className="flex items-center gap-1">
                                  <Badge
                                    variant={
                                      isCompatible ? "default" : "secondary"
                                    }
                                    className="text-xs"
                                  >
                                    {isCompatible ? "✓" : "✗"}
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    {getConstraintTypeLabel(
                                      template.constraint.type || "Unknown"
                                    )}
                                  </Badge>
                                </div>
                              </div>
                              <p className="text-muted-foreground text-xs">
                                {template.description}
                              </p>
                              <div className="text-muted-foreground text-xs">
                                <span className="font-medium">
                                  Parameters:{" "}
                                </span>
                                <span className="font-mono">
                                  {template.parameterPlaceholders.join(", ")}
                                </span>
                              </div>
                              {!isCompatible && compatibilityInfo.message && (
                                <div className="text-destructive text-xs">
                                  ⚠️ {compatibilityInfo.message}
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              )
            })}

            {availableParameters.length === 0 && (
              <div className="text-muted-foreground py-4 text-center text-sm">
                Add parameters first to use constraint templates
              </div>
            )}

            {availableParameters.length > 0 && (
              <div className="bg-muted/30 mt-4 rounded-lg p-3">
                <div className="text-sm">
                  <div className="mb-2 font-medium">
                    💡 Template Compatibility Guide
                  </div>
                  <div className="text-muted-foreground space-y-1 text-xs">
                    <div>
                      • <strong>Discrete constraints</strong> (marked with ✗)
                      require NumericalDiscrete or Categorical parameters
                    </div>
                    <div>
                      • <strong>Continuous constraints</strong> require
                      NumericalContinuous parameters
                    </div>
                    <div>
                      • Templates marked with ✓ are compatible with your current
                      parameter types
                    </div>
                    <div>
                      • You can change parameter types in the Parameters section
                      above
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
