/*
This client component provides the publications showcase section with carousel and enhanced styling.
*/

"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ExternalLink } from "lucide-react"
import { Section } from "@/components/ui/section"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi
} from "@/components/ui/carousel"

// Utility function to determine texture pattern based on article content
const getTexturePattern = (title: string, journal: string) => {
  const content = `${title} ${journal}`.toLowerCase()

  // DNA Helix: Biological, genetic, molecular biology content
  if (
    content.includes("dna") ||
    content.includes("genetic") ||
    content.includes("molecular biology") ||
    content.includes("biotechnology") ||
    content.includes("bioengineering") ||
    content.includes("drug discovery") ||
    content.includes("protein") ||
    content.includes("enzyme") ||
    content.includes("cell")
  ) {
    return "dna-helix"
  }

  // Molecular Structure: Chemistry, materials, nanoparticles
  if (
    content.includes("chemical") ||
    content.includes("chemistry") ||
    content.includes("nanoparticle") ||
    content.includes("catalysis") ||
    content.includes("reaction") ||
    content.includes("molecular") ||
    content.includes("polymer") ||
    content.includes("material") ||
    content.includes("synthesis")
  ) {
    return "molecular-structure"
  }

  // Circuit Board: Engineering, systems, interconnected, industrial
  if (
    content.includes("engineering") ||
    content.includes("industrial") ||
    content.includes("system") ||
    content.includes("interconnected") ||
    content.includes("process") ||
    content.includes("framework") ||
    content.includes("implementation") ||
    content.includes("control") ||
    content.includes("automation")
  ) {
    return "circuit-board"
  }

  // Academic Dots: Research papers, theoretical work, reviews
  if (
    content.includes("review") ||
    content.includes("survey") ||
    content.includes("analysis") ||
    content.includes("study") ||
    content.includes("research") ||
    content.includes("theoretical") ||
    content.includes("methods") ||
    content.includes("approach") ||
    content.includes("where do we stand")
  ) {
    return "academic-pattern"
  }

  // Scientific Grid: Mathematical, computational, Monte Carlo, Bayesian
  if (
    content.includes("monte carlo") ||
    content.includes("bayesian") ||
    content.includes("optimization") ||
    content.includes("algorithm") ||
    content.includes("computational") ||
    content.includes("mathematical") ||
    content.includes("statistical") ||
    content.includes("sampling") ||
    content.includes("parameter")
  ) {
    return "scientific-grid"
  }

  // Default to scientific grid for general scientific content
  return "scientific-grid"
}

// Peer-reviewed publications data with journal information and REAL article titles
const publications = [
  {
    url: "https://doi.org/10.3390/e27010058",
    journal: "Entropy",
    publisher: "MDPI",
    title:
      "Advanced Monte Carlo for Acquisition Sampling in Bayesian Optimization",
    year: "2025",
    type: "peer-reviewed",
    openAccess: true,
    impactFactor: "2.7",
    quartile: "Q2"
  },
  {
    url: "https://pubs.acs.org/doi/10.1021/acs.iecr.4c02915",
    journal: "Industrial & Engineering Chemistry Research",
    publisher: "ACS",
    title:
      "Implementation of a Bayesian Optimization Framework for Interconnected Systems",
    year: "2024",
    type: "peer-reviewed",
    openAccess: false,
    impactFactor: "4.2",
    quartile: "Q1"
  },
  {
    url: "https://doi.org/10.1002/bit.28960",
    journal: "Biotechnology and Bioengineering",
    publisher: "Wiley",
    title:
      "Bayesian Optimization in Bioprocess Engineering—Where Do We Stand Today?",
    year: "2024",
    type: "peer-reviewed",
    openAccess: false,
    impactFactor: "3.8",
    quartile: "Q1"
  },
  {
    url: "https://doi.org/10.26434/chemrxiv-2024-44ft2",
    journal: "ChemRxiv",
    publisher: "ChemRxiv",
    title: "Cost-Informed Bayesian Reaction Optimization",
    year: "2024",
    type: "preprint",
    openAccess: true,
    impactFactor: "N/A",
    quartile: "Preprint"
  },
  {
    url: "https://doi.org/10.1007/978-1-0716-3449-3_5",
    journal: "Methods in Molecular Biology",
    publisher: "Springer",
    title: "Bayesian Optimization in Drug Discovery",
    year: "2024",
    type: "peer-reviewed",
    openAccess: false,
    impactFactor: "2.1",
    quartile: "Q3"
  },
  {
    url: "https://doi.org/10.1021/acs.iecr.3c04665",
    journal: "Industrial & Engineering Chemistry Research",
    publisher: "ACS",
    title:
      "Kinetic Parameter Estimation of the Polyethylene Process by Bayesian Optimization",
    year: "2024",
    type: "peer-reviewed",
    openAccess: false,
    impactFactor: "4.2",
    quartile: "Q1"
  },
  {
    url: "https://doi.org/10.1021/jacs.4c03789",
    journal: "Journal of the American Chemical Society",
    publisher: "ACS",
    title:
      "Multivariate Bayesian Optimization of CoO Nanoparticles for CO2 Hydrogenation Catalysis",
    year: "2024",
    type: "peer-reviewed",
    openAccess: false,
    impactFactor: "15.0",
    quartile: "Q1"
  },
  {
    url: "https://doi.org/10.3390/microorganisms12081568",
    journal: "Microorganisms",
    publisher: "MDPI",
    title:
      "Design of Fragrance Formulations with Antiviral Activity Using Bayesian Optimization",
    year: "2024",
    type: "peer-reviewed",
    openAccess: true,
    impactFactor: "4.5",
    quartile: "Q1"
  },
  {
    url: "https://doi.org/10.3390/microorganisms12081569",
    journal: "Microorganisms",
    publisher: "MDPI",
    title:
      "Microbial Diversity of Soil in a Mediterranean Biodiversity Hotspot: Parque Nacional La Campana, Chile",
    year: "2024",
    type: "peer-reviewed",
    openAccess: true,
    impactFactor: "4.5",
    quartile: "Q1"
  },
  {
    url: "https://doi.org/10.1039/d3mh01474f",
    journal: "Mater. Horiz.",
    publisher: "RSC",
    title:
      "PAL 2.0: a physics-driven bayesian optimization framework for material discovery",
    year: "2024",
    type: "peer-reviewed",
    openAccess: false,
    impactFactor: "13.3",
    quartile: "Q1"
  },
  {
    url: "https://doi.org/10.1039/d3sc05607d",
    journal: "Chemical Science",
    publisher: "RSC",
    title:
      "Combining Bayesian optimization and automation to simultaneously optimize reaction conditions and routes",
    year: "2024",
    type: "peer-reviewed",
    openAccess: true,
    impactFactor: "8.4",
    quartile: "Q1"
  },
  {
    url: "https://doi.org/10.1002/smll.202309579",
    journal: "Small",
    publisher: "Wiley",
    title:
      "Bayesian Optimization of Environmentally Sustainable Graphene Inks Produced by Wet Jet Milling",
    year: "2024",
    type: "peer-reviewed",
    openAccess: false,
    impactFactor: "13.3",
    quartile: "Q1"
  },
  {
    url: "https://doi.org/10.1038/s41428-024-00923-8",
    journal: "Polymer Journal",
    publisher: "Nature",
    title:
      "Development of Pd-immobilized porous polymer catalysts via Bayesian optimization"
  },
  {
    url: "https://doi.org/10.1021/acssuschemeng.4c03253",
    journal: "ACS Sustainable Chemistry & Engineering",
    publisher: "ACS",
    title:
      "Self-Optimizing Flow Reactions for Sustainability: An Experimental Bayesian Optimization Study"
  },
  {
    url: "https://doi.org/10.1080/27660400.2024.2425178",
    journal: "Chemical Engineering Journal Advances",
    publisher: "Taylor & Francis",
    title:
      "Bayesian optimization of radical polymerization reactions in a flow synthesis system"
  },
  {
    url: "https://doi.org/10.1016/j.compchemeng.2024.108813",
    journal: "Computers & Chemical Engineering",
    publisher: "Elsevier",
    title:
      "Operating condition design with a Bayesian optimization approach for pharmaceutical intermediate batch concentration"
  },
  {
    url: "https://doi.org/10.1016/j.oceram.2024.100705",
    journal: "Open Ceramics",
    publisher: "Elsevier",
    title:
      "Development of aluminum oxide slurries for additive manufacturing by Bayesian optimization"
  },
  {
    url: "https://doi.org/10.3390/ma17205019",
    journal: "Materials",
    publisher: "MDPI",
    title:
      "Leveraging Bayesian Optimization Software for Atomic Layer Deposition: Single-Objective Optimization of TiO2 Layers"
  },
  {
    url: "https://doi.org/10.1039/d4nr00915k",
    journal: "Nanoscale",
    publisher: "RSC",
    title:
      "Bayesian optimization of glycopolymer structures for the interaction with cholera toxin B subunit"
  },
  {
    url: "https://doi.org/10.1039/d4cp02533d",
    journal: "Physical Chemistry Chemical Physics",
    publisher: "RSC",
    title:
      "First-principles study on the lithiation process of amorphous SiO anode for Li-ion batteries with Bayesian optimization"
  },
  {
    url: "https://doi.org/10.1039/d3ta06651g",
    journal: "Journal of Materials Chemistry A",
    publisher: "RSC",
    title:
      "Navigating the unknown with AI: multiobjective Bayesian optimization of non-noble acidic OER catalysts"
  },
  {
    url: "https://doi.org/10.1021/acsami.4c14279",
    journal: "ACS Applied Materials & Interfaces",
    publisher: "ACS",
    title:
      "Continuous Flow Chemistry and Bayesian Optimization for Polymer-Functionalized Carbon Nanotube-Based Chemiresistive Methane Sensors"
  },
  {
    url: "https://doi.org/10.26434/chemrxiv-2024-m0kf5",
    journal: "ChemRxiv",
    publisher: "ChemRxiv",
    title:
      "Optimization of heterogeneous continuous flow hydrogenation using FTIR inline analysis: a comparative study of multi-objective Bayesian optimization and kinetic modeling"
  },
  {
    url: "https://doi.org/10.1016/j.polymer.2023.126554",
    journal: "Polymer",
    publisher: "Elsevier",
    title:
      "Bayesian optimization of HDPE copolymerization process based on polymer product-process integration"
  },
  {
    url: "https://doi.org/10.1016/j.compchemeng.2024.108810",
    journal: "Computers & Chemical Engineering",
    publisher: "Elsevier",
    title:
      "Human-algorithm collaborative Bayesian optimization for engineering systems"
  },
  {
    url: "https://doi.org/10.1002/cctc.202400777",
    journal: "ChemCatChem",
    publisher: "Wiley",
    title:
      "Avoiding Replicates in Biocatalysis Experiments: Machine Learning for Enzyme Cascade Optimization"
  }
]

export const PublicationsShowcase = () => {
  const [carouselApi, setCarouselApi] = useState<CarouselApi>()
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  // Autoplay functionality
  useEffect(() => {
    if (!carouselApi) return

    const interval = setInterval(() => {
      carouselApi.scrollNext()
    }, 6000) // Auto-scroll every 6 seconds

    return () => {
      clearInterval(interval)
    }
  }, [carouselApi])

  return (
    <Section className="relative overflow-hidden bg-gradient-to-b from-gray-50/70 via-white/90 to-gray-50/50 py-16 md:py-24">
      {/* Enhanced background with subtle scientific pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="scientific-grid size-full" />
      </div>

      {/* Decorative background elements for better page integration */}
      <div className="absolute -left-20 -top-20 size-64 rounded-full bg-blue-50/40 blur-3xl"></div>
      <div className="absolute -bottom-20 -right-20 size-64 rounded-full bg-purple-50/30 blur-3xl"></div>
      <div className="absolute left-1/2 top-1/2 size-[30rem] -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-blue-50/20 to-purple-50/20 blur-3xl"></div>
      <div className="absolute right-1/4 top-1/3 size-48 rounded-full bg-blue-50/20 blur-3xl"></div>
      <div className="absolute bottom-1/3 left-1/4 size-48 rounded-full bg-purple-50/20 blur-3xl"></div>

      <div className="container relative z-10 mx-auto">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="mx-auto w-full max-w-6xl px-4"
        >
          {/* Enhanced description with scientific styling */}
          <div className="mb-16 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative mx-auto max-w-4xl"
            >
              {/* Enhanced background with research aesthetic */}
              <div className="research-highlight animate-research-glow absolute inset-0 rounded-2xl backdrop-blur-sm" />
              <div className="scientific-grid absolute inset-0 rounded-2xl opacity-30" />

              {/* Content */}
              <div className="relative p-8">
                <div className="mb-6 flex items-center justify-center gap-2">
                  <div className="h-px w-12 bg-gradient-to-r from-transparent to-blue-500/50" />
                  <div className="flex items-center gap-1">
                    <div className="animate-scientific-pulse size-1.5 rounded-full bg-blue-500/60" />
                    <div className="animate-scientific-pulse size-1 rounded-full bg-purple-500/60 delay-150" />
                    <div className="animate-scientific-pulse size-1.5 rounded-full bg-blue-500/60 delay-300" />
                  </div>
                  <div className="h-px w-12 bg-gradient-to-l from-transparent to-blue-500/50" />
                </div>

                <h2 className="text-foreground/90 mb-6 text-2xl font-bold md:text-3xl lg:text-4xl">
                  The Gold Standard in Optimization
                </h2>

                <p className="text-muted-foreground text-lg leading-relaxed md:text-xl">
                  <span className="text-foreground/80 font-semibold">
                    IINNOptimizer™
                  </span>{" "}
                  leverages
                  <span className="text-foreground/80 font-semibold">
                    {" "}
                    Bayesian Optimization
                  </span>
                  —the gold standard trusted by leading universities and
                  research institutions worldwide. This advanced approach
                  consistently outperforms traditional{" "}
                  <span className="text-foreground/80 font-semibold">
                    Design of Experiments (DOE)
                  </span>{" "}
                  methods.
                </p>

                <div className="mt-8 flex items-center justify-center gap-3">
                  <div className="h-px w-12 bg-gradient-to-r from-transparent to-blue-500/30" />
                  <span className="text-muted-foreground/80 text-sm font-medium tracking-wide">
                    VALIDATED THROUGH EXTENSIVE PEER-REVIEWED PUBLICATIONS AND
                    CUTTING-EDGE RESEARCH
                  </span>
                  <div className="h-px w-12 bg-gradient-to-l from-transparent to-blue-500/30" />
                </div>
              </div>
            </motion.div>
          </div>

          {/* Publications carousel with cards floating on page background */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="relative w-full"
          >
            {/* Subtle decorative elements scattered on page background */}
            <div className="absolute -top-12 left-1/4 size-32 rounded-full bg-blue-100/20 blur-3xl"></div>
            <div className="absolute -top-8 right-1/3 size-40 rounded-full bg-purple-100/15 blur-3xl"></div>
            <div className="left-1/6 absolute top-1/2 size-24 rounded-full bg-blue-100/25 blur-2xl"></div>
            <div className="absolute bottom-0 right-1/4 size-36 rounded-full bg-purple-100/20 blur-3xl"></div>

            {/* Direct carousel without container - cards float on page */}
            <Carousel
              setApi={setCarouselApi}
              opts={{
                align: "start",
                loop: true,
                slidesToScroll: 1
              }}
              className="relative w-full py-8 md:py-12"
            >
              <CarouselContent className="-ml-4 overflow-visible p-4 md:px-4 md:py-8">
                {publications.map((pub, index) => (
                  <CarouselItem
                    key={index}
                    className="basis-full pl-9 sm:basis-1/2 lg:basis-1/3"
                  >
                    <div className="relative">
                      <motion.a
                        href={pub.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block"
                        whileHover={{ y: -6, rotateY: 2 }}
                        whileTap={{ y: 0, rotateY: 0 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                        onMouseEnter={() => setHoveredCard(index)}
                        onMouseLeave={() => setHoveredCard(null)}
                        style={{ perspective: "1000px" }}
                      >
                        <div
                          className={`group relative h-[380px] w-full overflow-hidden rounded-2xl border border-white/40 bg-gradient-to-br from-white/95 via-gray-50/90 to-white/95 p-6 shadow-2xl backdrop-blur-lg transition-all duration-700 md:h-[400px] ${
                            hoveredCard === index
                              ? "shadow-3xl -translate-y-2 scale-105 shadow-blue-500/40 ring-2 ring-blue-500/60"
                              : "shadow-xl hover:-translate-y-1 hover:shadow-2xl hover:shadow-blue-500/30"
                          }`}
                        >
                          {/* Scientific pattern overlay with dynamic texture based on content */}
                          <div className="absolute inset-0 opacity-50">
                            {/* Dynamic texture pattern based on article content */}
                            <div
                              className={`size-full ${getTexturePattern(pub.title, pub.journal)}`}
                            />
                            <div
                              className={`absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/15 to-transparent transition-all duration-1000 ${
                                hoveredCard === index ? "animate-data-flow" : ""
                              }`}
                            />
                          </div>

                          {/* Dynamic gradient overlay */}
                          <div
                            className={`absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-blue-600/10 transition-all duration-700 ${
                              hoveredCard === index
                                ? "scale-102 opacity-80"
                                : "opacity-40"
                            }`}
                          />

                          {/* Enhanced content container with article information */}
                          <div className="relative z-10 flex h-full flex-col">
                            {/* Top section with external link indicator */}
                            <div className="mb-6 flex justify-end">
                              <div
                                className={`rounded-full p-1.5 transition-all duration-500 ${
                                  hoveredCard === index
                                    ? "scale-110 bg-blue-500/20 text-blue-700"
                                    : "bg-gray-500/10 text-gray-500"
                                }`}
                              >
                                <ExternalLink className="size-3" />
                              </div>
                            </div>

                            {/* Publisher name as main title */}
                            <div className="mb-6 text-center">
                              <h2
                                className={`text-lg font-bold text-blue-600 transition-all duration-500 md:text-xl ${
                                  hoveredCard === index ? "scale-105" : ""
                                }`}
                              >
                                {pub.publisher}
                              </h2>
                            </div>

                            {/* Article title - centrally positioned */}
                            <div className="text-center">
                              <h3
                                className={`px-2 text-sm font-semibold leading-tight tracking-tight transition-all duration-500 md:text-base ${
                                  hoveredCard === index
                                    ? "text-foreground scale-105"
                                    : "text-foreground/90"
                                }`}
                              >
                                {pub.title}
                              </h3>
                            </div>

                            {/* Bottom section with journal title only - always at bottom */}
                            <div className="mt-auto border-t border-gray-200/50 pb-1 pt-4">
                              <h4
                                className={`px-2 text-center text-sm font-semibold leading-relaxed text-blue-600 transition-all duration-500 ${
                                  hoveredCard === index ? "scale-105" : ""
                                }`}
                              >
                                {pub.journal}
                              </h4>
                            </div>
                          </div>
                        </div>
                      </motion.a>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>

              {/* Decorative elements floating on page background */}
              <div className="mt-8 flex items-center justify-center gap-3">
                <div className="h-px w-20 bg-gradient-to-r from-transparent to-blue-500/40"></div>
                <div className="flex items-center gap-1">
                  <div className="animate-scientific-pulse size-1.5 rounded-full bg-blue-500/70"></div>
                  <div className="animate-scientific-pulse size-2 rounded-full bg-purple-500/70 delay-150"></div>
                  <div className="animate-scientific-pulse size-1.5 rounded-full bg-blue-500/70 delay-300"></div>
                </div>
                <div className="h-px w-20 bg-gradient-to-l from-transparent to-blue-500/40"></div>
              </div>
            </Carousel>
          </motion.div>
        </motion.div>
      </div>
    </Section>
  )
}
