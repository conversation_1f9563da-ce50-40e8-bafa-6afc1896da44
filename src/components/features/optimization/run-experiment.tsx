// components/optimization/run-experiment.tsx
"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { getSubscriptionAction } from "@/actions/subscription-actions"

// Function to check if a string is a valid UUID
function isValidUUID(id: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(id)
}
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { DiscretizationTransparency } from "@/components/ui/discretization-transparency"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import { ConstraintViolationNotification } from "@/components/ui/constraint-violation-notification"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  getSuggestionWorkflowAction,
  addMeasurementWorkflowAction,
  loadOptimizationWorkflowAction
} from "@/actions/optimization-workflow-actions"
import {
  generateSamplesWorkflowAction,
  getSavedSamplesWorkflowAction,
  updateSampleTargetValuesWorkflowAction,
  submitSamplesWorkflowAction,
  saveSamplesWorkflowAction
} from "@/actions/sample-workflow-actions"
import {
  getPendingSuggestionsWorkflowAction,
  markSuggestionSubmittedAction,
  updateSuggestionTargetValuesWorkflowAction,
  saveSuggestionsWorkflowAction,
  getSavedSuggestionsWorkflowAction,
  checkSavedSuggestionsWorkflowAction
} from "@/actions/suggestion-workflow-actions"
import { deleteSavedSuggestionsWorkflowAction } from "@/actions/delete-saved-suggestions-workflow-action"
import { DatasetUpload } from "./dataset-upload"
import { deleteSavedSamplesWorkflowAction } from "@/actions/delete-saved-samples-workflow-action"
import { checkSavedSamplesWorkflowAction } from "@/actions/check-saved-samples-workflow-action"
import { SelectOptimization } from "@/db/schema/optimizations-schema"
// Removed SavedSuggestions import
import {
  AlertCircle,
  ArrowDown,
  ArrowUp,
  Beaker,
  Brain,
  Check,
  Database,
  Download,
  FileSpreadsheet,
  Lightbulb,
  Loader2,
  RefreshCw,
  RotateCw,
  Save,
  Sparkles,
  Trash2,
  Upload,
  X
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  downloadCSV,
  downloadExcel,
  downloadExcelCSV
} from "@/lib/export-utils"
import { UpgradeModal } from "@/components/subscription/upgrade-modal"

interface RunExperimentProps {
  optimization: SelectOptimization
}

export function RunExperiment({ optimization }: RunExperimentProps) {
  const router = useRouter()

  // Helper function to determine the appropriate tab order based on measurement count
  const getTabOrder = (measurementCount: number) => {
    // If there are no measurements, suggest Sample Generation first
    if (measurementCount === 0) {
      return ["samples", "upload", "manual", "suggested"]
    }
    // If there are measurements, keep the default order
    return ["suggested", "samples", "upload", "manual"]
  }

  // Helper function to determine the initial active tab
  const getInitialActiveTab = (measurementCount: number) => {
    // If there are no measurements, start with Sample Generation
    if (measurementCount === 0) {
      return "samples"
    }
    // If there are measurements, start with Suggested Experiments
    return "suggested"
  }

  // Removed notification state

  // Set the initial active tab based on measurement count
  const [activeTab, setActiveTab] = useState(
    getInitialActiveTab((optimization as any).measurementCount || 0)
  )

  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false)
  const [loadingMode, setLoadingMode] = useState<"saved" | "new">("saved")
  const [isAddingMeasurement, setIsAddingMeasurement] = useState(false)
  const [isSavingSuggestions, setIsSavingSuggestions] = useState(false)
  const [isGeneratingSamples, setIsGeneratingSamples] = useState(false)
  const [isLoadingSamples, setIsLoadingSamples] = useState(false)
  const [isSavingSamples, setIsSavingSamples] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [hasSavedSuggestions, setHasSavedSuggestions] = useState(false)
  const [savedSuggestionsCount, setSavedSuggestionsCount] = useState(0)
  const [hasSavedSamples, setHasSavedSamples] = useState(false)
  const [savedSamplesCount, setSavedSamplesCount] = useState(0)
  const [isCheckingSavedSuggestions, setIsCheckingSavedSuggestions] =
    useState(false)
  const [isCheckingSavedSamples, setIsCheckingSavedSamples] = useState(false)
  const [samples, setSamples] = useState<any[]>([])
  const [samplingStrategy, setSamplingStrategy] = useState<
    "LHS" | "random" | "sobol"
  >("LHS")
  const [discretizationTransparency, setDiscretizationTransparency] =
    useState<any>(null)
  const [constraintViolations, setConstraintViolations] = useState<
    number | undefined
  >(undefined)
  const [feasibleSamples, setFeasibleSamples] = useState<number | undefined>(
    undefined
  )
  const [totalAttempts, setTotalAttempts] = useState<number | undefined>(
    undefined
  )
  const [respectConstraints, setRespectConstraints] = useState<boolean>(true)
  const [showSamplingDetails, setShowSamplingDetails] = useState<boolean>(false)
  const [subscriptionTier, setSubscriptionTier] = useState<
    "free" | "trial" | "pro"
  >("free")
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false)
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [currentBatchId, setCurrentBatchId] = useState<string | null>(null)
  // Use an array of parameter objects for multiple manual experiments
  const [manualExperiments, setManualExperiments] = useState<
    Array<Record<string, any>>
  >([{}])

  // For backward compatibility, keep manualParameters as a reference to the first experiment
  const manualParameters = manualExperiments[0] || {}
  const setManualParameters = (params: Record<string, any>) => {
    setManualExperiments(prev => [params, ...prev.slice(1)])
  }
  // Use a nested object for target values to support multi-target optimizations and multiple suggestions
  // Format: { suggestionIndex: { targetName: value } }
  const [targetValues, setTargetValues] = useState<
    Record<number, Record<string, string>>
  >({})
  const [submittedSuggestions, setSubmittedSuggestions] = useState<
    Record<number, boolean>
  >({})
  // State to track batch submission progress
  const [batchProgress, setBatchProgress] = useState<{
    current: number
    total: number
  }>({ current: 0, total: 0 })

  // Keep track of which suggestion is currently being submitted
  const [activeSuggestionIndex, setActiveSuggestionIndex] = useState<number>(0)

  // Helper function to determine if this is a multi-target optimization
  const isMultiTarget = () => {
    return (
      optimization.targetMode === "MULTI" ||
      ((optimization.config as any).objective_type === "Desirability" &&
        Array.isArray((optimization.config as any).target_config))
    )
  }

  // Helper function to get target configs
  const getTargetConfigs = () => {
    if (Array.isArray((optimization.config as any).target_config)) {
      return (optimization.config as any).target_config
    } else {
      return [(optimization.config as any).target_config]
    }
  }

  // Helper function to check if all target values are filled for a suggestion or sample
  const hasAllTargetValues = (index: number) => {
    console.log(`[FRONTEND] Checking target values for index ${index}`)

    // Get the target values from the UI state
    const uiTargetValues = targetValues[index] || {}

    // Check if this is a suggestion or a sample
    const isSuggestion = index >= 0 && suggestions[index]
    const isSample = index >= 0 && samples[index]

    // Get stored target values from either suggestion or sample
    let storedTargetValues = {}

    if (isSuggestion) {
      storedTargetValues = suggestions[index]._targetValues || {}
      console.log(`[FRONTEND] Suggestion target values:`, storedTargetValues)
    } else if (isSample) {
      storedTargetValues = samples[index]._targetValues || {}
      console.log(`[FRONTEND] Sample target values:`, storedTargetValues)
    } else {
      console.log(`[FRONTEND] No suggestion or sample found at index ${index}`)
      // For UI-only values (like when entering target values for a new suggestion)
      // we should still check if all UI target values are filled
      if (Object.keys(uiTargetValues).length === 0) {
        return false
      }
    }

    console.log(`[FRONTEND] UI target values:`, uiTargetValues)

    // Combine both sources, with UI values taking precedence
    const combinedTargetValues: Record<string, any> = { ...storedTargetValues }

    // Add any UI values that might not be in the stored values yet
    for (const [key, value] of Object.entries(uiTargetValues)) {
      if (value && typeof value === "string" && value.trim() !== "") {
        const numValue = parseFloat(value)
        if (!isNaN(numValue)) {
          combinedTargetValues[key] = numValue
        }
      }
    }

    console.log(`[FRONTEND] Combined target values:`, combinedTargetValues)

    const targetConfigs = getTargetConfigs()

    // Check if all targets have values
    const result = targetConfigs.every((target: any) => {
      const value = combinedTargetValues[target.name]

      if (value === undefined || value === null) {
        console.log(
          `[FRONTEND] Missing value for target ${target.name} at index ${index}`
        )
        return false
      }

      if (typeof value === "number") {
        if (isNaN(value) || !isFinite(value)) {
          console.log(
            `[FRONTEND] Invalid numeric value for target ${target.name} at index ${index}: ${value}`
          )
          return false
        }
        return true
      }

      console.log(
        `[FRONTEND] Invalid value type for target ${target.name} at index ${index}: ${typeof value}`
      )
      return false
    })

    if (!result) {
      console.log(
        `[FRONTEND] Item at index ${index} is missing some target values:`,
        combinedTargetValues
      )
    } else {
      console.log(
        `[FRONTEND] Item at index ${index} has all required target values`
      )
    }

    return result
  }

  // Fetch user's subscription tier
  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        console.log(`[RunExperiment] Fetching subscription tier...`)
        const subscription = await getSubscriptionAction()
        console.log(
          `[RunExperiment] Fetched subscription tier: ${subscription.tier}`
        )
        setSubscriptionTier(subscription.tier as "free" | "trial" | "pro")
      } catch (error) {
        console.error("[RunExperiment] Error fetching subscription:", error)
        // Default to free tier if there's an error, but log it
        console.warn("[RunExperiment] Defaulting to free tier due to error")
        setSubscriptionTier("free")
      }
    }

    fetchSubscription()
  }, [])

  // Initialize manual parameters and load pending suggestions and samples
  useEffect(() => {
    // Create initial parameters for a single experiment
    const createInitialParams = () => {
      return (optimization.config as any).parameters.reduce(
        (acc: Record<string, any>, param: any) => {
          if (param.type === "NumericalDiscrete") {
            // Ensure we're using a valid value from the allowed values
            if (param.values && param.values.length > 0) {
              // Convert string values to numbers for numerical parameters
              acc[param.name] =
                typeof param.values[0] === "string"
                  ? parseFloat(param.values[0])
                  : param.values[0]
            } else {
              acc[param.name] = 0
            }
          } else if (param.type === "NumericalContinuous") {
            // Ensure we're within bounds
            acc[param.name] = param.bounds ? param.bounds[0] : 0
          } else if (param.type === "CategoricalParameter") {
            // Use the first allowed value
            acc[param.name] =
              param.values && param.values.length > 0 ? param.values[0] : ""
          }
          return acc
        },
        {} as Record<string, any>
      )
    }

    // Initialize with a single experiment
    setManualExperiments([createInitialParams()])

    // Initialize target values for manual entries
    const targetConfigs = getTargetConfigs()
    const initialTargetValues: Record<number, Record<string, string>> = {}

    // For the first manual experiment, use index -1 (for backward compatibility)
    initialTargetValues[-1] = targetConfigs.reduce(
      (acc: Record<string, string>, target: any) => {
        acc[target.name] = ""
        return acc
      },
      {} as Record<string, string>
    )

    // For additional experiments, use indices -2, -3, etc.
    // We'll start with just one experiment, so no need to add more yet

    setTargetValues(initialTargetValues)

    // Load pending suggestions from the database
    const loadPendingSuggestions = async () => {
      try {
        setIsLoadingSuggestions(true)
        setLoadingMode("saved")
        const result = await getPendingSuggestionsWorkflowAction(
          optimization.optimizerId
        )

        if (
          result.isSuccess &&
          result.data &&
          result.data.suggestions.length > 0
        ) {
          // Set the suggestions and batch ID
          setSuggestions(result.data.suggestions)
          setCurrentBatchId(result.data.batchId)

          // Initialize target values from saved values
          const savedTargetValues: Record<number, Record<string, string>> = {}

          result.data.suggestions.forEach((suggestion, index) => {
            if (suggestion._targetValues) {
              savedTargetValues[index] = suggestion._targetValues as Record<
                string,
                string
              >
            }
          })

          if (Object.keys(savedTargetValues).length > 0) {
            // Merge with existing target values
            setTargetValues(prev => ({
              ...prev,
              ...savedTargetValues
            }))
          }

          // Switch to the suggested tab if we found pending suggestions
          setActiveTab("suggested")

          toast({
            title: "Saved suggestions loaded",
            description: `Loaded ${result.data.suggestions.length} previously saved suggestions`
          })
        }
      } catch (error) {
        console.error("Error loading pending suggestions:", error)
        // Don't show an error to the user - the UI still works without this
      } finally {
        setIsLoadingSuggestions(false)
      }
    }

    // Don't automatically load suggestions - only load them when the user clicks the button
    // loadPendingSuggestions()
  }, [
    optimization.optimizerId,
    (optimization.config as any).parameters,
    (optimization.config as any).target_config
  ])

  // Load saved suggestions from the database when the user clicks the Load Saved Suggestions button
  const loadSavedSuggestions = async () => {
    try {
      setIsLoadingSuggestions(true)
      setLoadingMode("saved")
      const result = await getSavedSuggestionsWorkflowAction(
        optimization.optimizerId
      )

      if (
        result.isSuccess &&
        result.data &&
        result.data.suggestions.length > 0
      ) {
        // Set the suggestions and batch ID
        setSuggestions(result.data.suggestions)
        setCurrentBatchId(result.data.batchId)

        // Initialize target values from saved values
        const savedTargetValues: Record<number, Record<string, string>> = {}

        result.data.suggestions.forEach((suggestion, index) => {
          if (suggestion._targetValues) {
            savedTargetValues[index] = suggestion._targetValues as Record<
              string,
              string
            >
          }
        })

        if (Object.keys(savedTargetValues).length > 0) {
          // Merge with existing target values
          setTargetValues(prev => ({
            ...prev,
            ...savedTargetValues
          }))
        }

        // Switch to the suggested tab if we found saved suggestions
        setActiveTab("suggested")

        toast({
          title: "Saved suggestions loaded",
          description: `Loaded ${result.data.suggestions.length} previously saved suggestions`
        })
      } else {
        toast({
          title: "No saved suggestions",
          description: "No saved suggestions found for this optimization"
        })
      }
    } catch (error) {
      console.error("Error loading saved suggestions:", error)
      toast({
        title: "Error",
        description: "Failed to load saved suggestions. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoadingSuggestions(false)
    }
  }

  // Prepare sample data for export (template-compatible format)
  const prepareSamplesExportData = (includeMetadata: boolean = false) => {
    if (samples.length === 0) return []

    return samples.map((sample, index) => {
      // Extract parameter values in order
      const paramValues: Record<string, any> = {}

      // Get optimization parameters in correct order
      const parameters = (optimization.config as any)?.parameters || []
      parameters.forEach((param: any) => {
        const value = sample[param.name]
        paramValues[param.name] = value !== undefined ? value : ""
      })

      // Extract target values in order (clean names without suffixes)
      const targetConfigs = getTargetConfigs()
      const sampleTargetValues = sample._targetValues || {}
      const uiTargetValues = targetValues[index] || {}

      // Combine target values, with UI values taking precedence
      const combinedTargetValues: Record<string, any> = {
        ...sampleTargetValues
      }
      for (const [key, value] of Object.entries(uiTargetValues)) {
        if (value && typeof value === "string" && value.trim() !== "") {
          const numValue = parseFloat(value)
          if (!isNaN(numValue)) {
            combinedTargetValues[key] = numValue
          }
        }
      }

      // Add target values with clean names (template format)
      targetConfigs.forEach((target: any) => {
        const targetValue = combinedTargetValues[target.name]
        paramValues[target.name] = targetValue !== undefined ? targetValue : ""
      })

      // Add metadata only if requested (extended mode)
      if (includeMetadata) {
        paramValues["Sample #"] = index + 1
        paramValues["Sampling Method"] = sample._samplingMethod || "LHS"
        paramValues["Status"] = submittedSuggestions[index]
          ? "Submitted"
          : "Pending"
      }

      return paramValues
    })
  }

  // Handle export to CSV (template-compatible format)
  const handleSamplesExportCSV = async () => {
    if (samples.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no samples available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export (template-compatible format)
      const formattedData = prepareSamplesExportData(false)

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-samples-${new Date().toISOString().split("T")[0]}.csv`

      // Download the CSV with Excel compatibility and template format
      downloadExcelCSV(formattedData, filename, undefined, optimization.config)

      toast({
        title: "Export successful",
        description:
          "The samples data has been exported as CSV (template format).",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting samples to CSV:", error)
      toast({
        title: "Export failed",
        description: "Failed to export samples data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Handle export to Excel (template-compatible format)
  const handleSamplesExportExcel = async () => {
    if (samples.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no samples available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export (template-compatible format)
      const formattedData = prepareSamplesExportData(false)

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-samples-${new Date().toISOString().split("T")[0]}.xlsx`

      // Download as Excel with template-style formatting
      await downloadExcel(
        formattedData,
        filename,
        undefined,
        true,
        optimization.config
      )

      toast({
        title: "Export successful",
        description:
          "The samples data has been exported as Excel (.xlsx) file (template format).",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting samples to Excel:", error)
      toast({
        title: "Export failed",
        description: "Failed to export samples data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Prepare suggestions data for export (template-compatible format)
  const prepareSuggestionsExportData = (includeMetadata: boolean = false) => {
    if (suggestions.length === 0) return []

    return suggestions.map((suggestion, index) => {
      // Extract parameter values in order
      const paramValues: Record<string, any> = {}

      // Get optimization parameters in correct order
      const parameters = (optimization.config as any)?.parameters || []
      parameters.forEach((param: any) => {
        const value = suggestion[param.name]
        paramValues[param.name] = value !== undefined ? value : ""
      })

      // Extract target values in order (clean names without suffixes)
      const targetConfigs = getTargetConfigs()
      const suggestionTargetValues = suggestion._targetValues || {}
      const uiTargetValues = targetValues[index] || {}

      // Combine target values, with UI values taking precedence
      const combinedTargetValues: Record<string, any> = {
        ...suggestionTargetValues
      }
      for (const [key, value] of Object.entries(uiTargetValues)) {
        if (value && typeof value === "string" && value.trim() !== "") {
          const numValue = parseFloat(value)
          if (!isNaN(numValue)) {
            combinedTargetValues[key] = numValue
          }
        }
      }

      // Add target values with clean names (template format)
      targetConfigs.forEach((target: any) => {
        const targetValue = combinedTargetValues[target.name]
        paramValues[target.name] = targetValue !== undefined ? targetValue : ""
      })

      // Add metadata only if requested (extended mode)
      if (includeMetadata) {
        paramValues["Suggestion #"] = index + 1
        paramValues["Status"] = submittedSuggestions[index]
          ? "Submitted"
          : "Pending"
      }

      return paramValues
    })
  }

  // Handle export to CSV for suggestions (template-compatible format)
  const handleSuggestionsExportCSV = async () => {
    if (suggestions.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no suggestions available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export (template-compatible format)
      const formattedData = prepareSuggestionsExportData(false)

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-suggestions-${new Date().toISOString().split("T")[0]}.csv`

      // Download the CSV with Excel compatibility and template format
      downloadExcelCSV(formattedData, filename, undefined, optimization.config)

      toast({
        title: "Export successful",
        description:
          "The suggestions data has been exported as CSV (template format).",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting suggestions to CSV:", error)
      toast({
        title: "Export failed",
        description: "Failed to export suggestions data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Handle export to Excel for suggestions (template-compatible format)
  const handleSuggestionsExportExcel = async () => {
    if (suggestions.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no suggestions available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export (template-compatible format)
      const formattedData = prepareSuggestionsExportData(false)

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-suggestions-${new Date().toISOString().split("T")[0]}.xlsx`

      // Download as Excel with template-style formatting
      await downloadExcel(
        formattedData,
        filename,
        undefined,
        true,
        optimization.config
      )

      toast({
        title: "Export successful",
        description:
          "The suggestions data has been exported as Excel (.xlsx) file (template format).",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting suggestions to Excel:", error)
      toast({
        title: "Export failed",
        description: "Failed to export suggestions data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Prepare manual experiments data for export (template-compatible format)
  const prepareManualExperimentsExportData = (
    includeMetadata: boolean = false
  ) => {
    if (manualExperiments.length === 0) return []

    return manualExperiments.map((experiment, experimentIndex) => {
      // Extract parameter values in order
      const paramValues: Record<string, any> = {}

      // Get optimization parameters in correct order
      const parameters = (optimization.config as any)?.parameters || []
      parameters.forEach((param: any) => {
        const value = experiment[param.name]
        paramValues[param.name] = value !== undefined ? value : ""
      })

      // Extract target values in order (clean names without suffixes)
      const targetConfigs = getTargetConfigs()
      const targetIndex = experimentIndex === 0 ? -1 : -1 - experimentIndex
      const experimentTargetValues = targetValues[targetIndex] || {}

      // Add target values with clean names (template format)
      targetConfigs.forEach((target: any) => {
        const targetValue = experimentTargetValues[target.name]
        const parsedValue = targetValue ? parseFloat(targetValue) : undefined
        paramValues[target.name] = !isNaN(parsedValue as number)
          ? parsedValue
          : ""
      })

      // Add metadata only if requested (extended mode)
      if (includeMetadata) {
        paramValues["Experiment #"] = experimentIndex + 1
        paramValues["Status"] = submittedSuggestions[targetIndex]
          ? "Submitted"
          : "Pending"
      }

      return paramValues
    })
  }

  // Handle export to CSV for manual experiments (template-compatible format)
  const handleManualExperimentsExportCSV = async () => {
    if (manualExperiments.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no manual experiments available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export (template-compatible format)
      const formattedData = prepareManualExperimentsExportData(false)

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-manual-experiments-${new Date().toISOString().split("T")[0]}.csv`

      // Download the CSV with Excel compatibility and template format
      downloadExcelCSV(formattedData, filename, undefined, optimization.config)

      toast({
        title: "Export successful",
        description:
          "The manual experiments data has been exported as CSV (template format).",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting manual experiments to CSV:", error)
      toast({
        title: "Export failed",
        description:
          "Failed to export manual experiments data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Handle export to Excel for manual experiments (template-compatible format)
  const handleManualExperimentsExportExcel = async () => {
    if (manualExperiments.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no manual experiments available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export (template-compatible format)
      const formattedData = prepareManualExperimentsExportData(false)

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-manual-experiments-${new Date().toISOString().split("T")[0]}.xlsx`

      // Download as Excel with template-style formatting
      await downloadExcel(
        formattedData,
        filename,
        undefined,
        true,
        optimization.config
      )

      toast({
        title: "Export successful",
        description:
          "The manual experiments data has been exported as Excel (.xlsx) file (template format).",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting manual experiments to Excel:", error)
      toast({
        title: "Export failed",
        description:
          "Failed to export manual experiments data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Delete all saved suggestions
  const deleteSavedSuggestions = async () => {
    // Show a confirmation dialog
    if (
      !window.confirm(
        "Are you sure you want to permanently delete all saved suggestions? This action cannot be undone."
      )
    ) {
      return
    }

    try {
      // Show loading state
      setIsLoadingSuggestions(true)

      // Call the server action to delete saved suggestions
      const result = await deleteSavedSuggestionsWorkflowAction(
        optimization.optimizerId
      )

      if (result.isSuccess) {
        // Update the saved suggestions count
        setSavedSuggestionsCount(0)
        setHasSavedSuggestions(false)

        // Trigger a refresh of the saved suggestions count
        setRefreshSavedSuggestionsCount(prev => prev + 1)

        // Show success message
        toast({
          title: "Saved suggestions deleted",
          description: `Successfully deleted ${result.data?.count || 0} saved suggestions`
        })

        // Clear any loaded suggestions if they exist
        if (suggestions.length > 0 && activeTab === "suggested") {
          setSuggestions([])
          setCurrentBatchId("")
        }
      } else {
        // Show error message
        toast({
          title: "Error",
          description: `Failed to delete saved suggestions: ${result.message}`,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error deleting saved suggestions:", error)
      toast({
        title: "Error",
        description: "Failed to delete saved suggestions. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoadingSuggestions(false)
    }
  }

  // Check if there are saved suggestions
  const checkForSavedSuggestions = async () => {
    try {
      setIsCheckingSavedSuggestions(true)
      const result = await checkSavedSuggestionsWorkflowAction(
        optimization.optimizerId
      )

      if (result.isSuccess && result.data) {
        setHasSavedSuggestions(result.data.hasSavedSuggestions)
        setSavedSuggestionsCount(result.data.count)
        console.log(`Found ${result.data.count} saved suggestions`)
      }
    } catch (error) {
      console.error("Error checking for saved suggestions:", error)
    } finally {
      setIsCheckingSavedSuggestions(false)
    }
  }

  // Track when we need to refresh the saved counts
  const [refreshSavedSuggestionsCount, setRefreshSavedSuggestionsCount] =
    useState(0)
  const [refreshSavedSamplesCount, setRefreshSavedSamplesCount] = useState(0)

  // Check for saved suggestions when the component mounts or after deletion
  useEffect(() => {
    const checkForSavedSuggestions = async () => {
      try {
        setIsCheckingSavedSuggestions(true)
        const result = await checkSavedSuggestionsWorkflowAction(
          optimization.optimizerId
        )

        if (result.isSuccess && result.data) {
          setHasSavedSuggestions(result.data.hasSavedSuggestions)
          setSavedSuggestionsCount(result.data.count)
          console.log(`Found ${result.data.count} saved suggestions`)
        } else {
          // Reset the state if no suggestions found or error
          setHasSavedSuggestions(false)
          setSavedSuggestionsCount(0)
        }
      } catch (error) {
        console.error("Error checking for saved suggestions:", error)
        // Reset the state on error
        setHasSavedSuggestions(false)
        setSavedSuggestionsCount(0)
      } finally {
        setIsCheckingSavedSuggestions(false)
      }
    }

    checkForSavedSuggestions()
  }, [optimization.optimizerId, refreshSavedSuggestionsCount])

  // Update the active tab when the optimization's measurement count changes
  useEffect(() => {
    // Get the current measurement count
    const measurementCount = (optimization as any).measurementCount || 0

    // Log for debugging
    console.log(
      `[RunExperiment] Current measurement count: ${measurementCount}, Active tab: ${activeTab}`
    )

    // Get the appropriate tab order based on the measurement count
    const tabOrder = getTabOrder(measurementCount)
    console.log(`[RunExperiment] Tab order: ${tabOrder.join(", ")}`)

    // If there are no measurements and the active tab is "suggested",
    // switch to the first tab in the new order (which should be "samples")
    if (measurementCount === 0 && activeTab === "suggested") {
      console.log(
        `[RunExperiment] Switching from "suggested" to "${tabOrder[0]}" because there are no measurements`
      )
      setActiveTab(tabOrder[0])
    }

    // If there are measurements and we're not already on a valid tab,
    // switch to the first tab in the new order (which should be "suggested")
    if (measurementCount > 0 && !tabOrder.includes(activeTab)) {
      console.log(
        `[RunExperiment] Switching to "${tabOrder[0]}" because there are measurements and current tab is not valid`
      )
      setActiveTab(tabOrder[0])
    }

    // If there are measurements and we're on the samples tab, log that suggested tab is available
    if (
      measurementCount > 0 &&
      activeTab === "samples" &&
      tabOrder[0] === "suggested"
    ) {
      console.log(
        `[RunExperiment] Measurements detected, suggested tab is now available`
      )
      // We don't automatically switch here to avoid disrupting the user's workflow
    }
  }, [(optimization as any).measurementCount, activeTab])

  // Check for saved samples when the component mounts or after saving samples
  useEffect(() => {
    const checkForSavedSamples = async () => {
      try {
        setIsCheckingSavedSamples(true)
        const result = await checkSavedSamplesWorkflowAction(
          optimization.optimizerId
        )

        if (result.isSuccess && result.data) {
          setHasSavedSamples(result.data.hasSavedSamples)
          setSavedSamplesCount(result.data.count)
          console.log(`Found ${result.data.count} saved samples`)
        } else {
          // Reset the state if no samples found or error
          setHasSavedSamples(false)
          setSavedSamplesCount(0)
        }
      } catch (error) {
        console.error("Error checking for saved samples:", error)
        // Reset the state on error
        setHasSavedSamples(false)
        setSavedSamplesCount(0)
      } finally {
        setIsCheckingSavedSamples(false)
      }
    }

    checkForSavedSamples()
  }, [optimization.optimizerId, refreshSavedSamplesCount])

  // Load saved samples from the database when the user clicks the Load Saved Samples button
  const loadSavedSamples = async () => {
    try {
      setIsLoadingSamples(true)
      console.log(
        "[FRONTEND] Loading saved samples for optimization:",
        optimization.optimizerId
      )
      console.log("[FRONTEND] Optimization details:", {
        id: optimization.id,
        optimizerId: optimization.optimizerId,
        name: optimization.name
      })

      const result = await getSavedSamplesWorkflowAction(
        optimization.optimizerId,
        "pending"
      )

      if (result.isSuccess && result.data && result.data.samples.length > 0) {
        console.log(
          `Loaded ${result.data.samples.length} samples from database:`,
          result.data.samples.map(s => ({
            id: s._sampleId,
            isUUID: isValidUUID(s._sampleId),
            hasTargetValues:
              s._targetValues && Object.keys(s._targetValues).length > 0
          }))
        )

        // Set the samples
        setSamples(result.data.samples)

        // Initialize target values from saved values
        const savedTargetValues: Record<number, Record<string, string>> = {}

        result.data.samples.forEach((sample, index) => {
          if (sample._targetValues) {
            // Convert numeric target values to strings for the UI
            const stringTargetValues: Record<string, string> = {}

            for (const [key, value] of Object.entries(sample._targetValues)) {
              stringTargetValues[key] = value.toString()
            }

            savedTargetValues[index] = stringTargetValues
          }
        })

        if (Object.keys(savedTargetValues).length > 0) {
          console.log(
            "Setting target values from saved samples:",
            savedTargetValues
          )

          // Merge with existing target values
          setTargetValues(prev => ({
            ...prev,
            ...savedTargetValues
          }))
        }

        // Switch to the samples tab if we found pending samples
        setActiveTab("samples")

        toast({
          title: "Saved samples loaded",
          description: `Loaded ${result.data.samples.length} previously saved samples`
        })
      } else {
        console.log("No saved samples found or error loading samples:", result)
        toast({
          title: "No saved samples",
          description: "No saved samples found for this optimization"
        })
      }
    } catch (error) {
      console.error("Error loading saved samples:", error)
      toast({
        title: "Error",
        description: "Failed to load saved samples. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoadingSamples(false)
    }
  }

  // Save generated samples to the database
  const saveSamples = async () => {
    if (samples.length === 0) return

    setIsSavingSamples(true)
    try {
      // Log the samples being saved
      console.log(`Saving ${samples.length} samples to the database`)

      // Check if any samples already have valid UUIDs
      const alreadySavedSamples = samples.filter(sample =>
        isValidUUID(sample._sampleId)
      )
      if (alreadySavedSamples.length > 0) {
        console.log(
          `${alreadySavedSamples.length} samples already have valid UUIDs and may already be saved`
        )
      }

      // Generate a batch ID if one doesn't exist
      const batchId =
        samples[0]?._batchId || `${optimization.optimizerId}_${Date.now()}`

      // Call the workflow action to explicitly save samples
      const result = await saveSamplesWorkflowAction(
        optimization.optimizerId,
        samples,
        batchId
      )

      if (result.isSuccess) {
        // Update the samples with the database IDs
        setSamples(result.data?.savedSamples || samples)

        // Trigger a refresh of the saved samples count
        setRefreshSavedSamplesCount(prev => prev + 1)

        toast({
          title: "Samples saved",
          description: `Successfully saved ${samples.length} samples for later use`
        })
      } else {
        toast({
          title: "Error saving samples",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error saving samples:", error)
      toast({
        title: "Error",
        description: "Failed to save samples. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSavingSamples(false)
    }
  }

  const saveSuggestions = async () => {
    if (suggestions.length === 0) return

    setIsSavingSuggestions(true)
    try {
      // Call the workflow action to explicitly save suggestions
      const result = await saveSuggestionsWorkflowAction(
        optimization.optimizerId,
        suggestions,
        currentBatchId
      )

      if (result.isSuccess) {
        // Trigger a refresh of the saved suggestions count
        setRefreshSavedSuggestionsCount(prev => prev + 1)

        toast({
          title: "Suggestions saved",
          description: `Successfully saved ${suggestions.length} suggestions for later use`
        })
      } else {
        toast({
          title: "Error saving suggestions",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error saving suggestions:", error)
      toast({
        title: "Error",
        description: "Failed to save suggestions. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSavingSuggestions(false)
    }
  }

  // Function to update sample target values
  const updateSampleTargetValue = async (
    sampleIndex: number,
    targetName: string,
    value: string
  ) => {
    console.log(
      `[FRONTEND] Updating target value for sample ${sampleIndex}, target ${targetName}, value ${value}`
    )

    // Update the local state first for immediate feedback
    setTargetValues(prev => {
      const newValues = { ...prev }
      if (!newValues[sampleIndex]) {
        newValues[sampleIndex] = {}
      }
      newValues[sampleIndex][targetName] = value
      return newValues
    })

    // Get the sample ID
    const sample = samples[sampleIndex]
    if (!sample || !sample._sampleId) {
      console.error(
        `[FRONTEND] No sample found at index ${sampleIndex} or missing sample ID`
      )
      return
    }

    console.log(
      `[FRONTEND] Sample ID: ${sample._sampleId}, isUUID: ${isValidUUID(sample._sampleId)}`
    )

    // Get all current target values for this sample
    const currentTargetValues = targetValues[sampleIndex] || {}
    console.log(`[FRONTEND] Current target values:`, currentTargetValues)

    // Convert target values to numbers for the API
    // Only convert the specific target that was updated
    const numericTargetValues = sample._targetValues
      ? { ...sample._targetValues }
      : {}

    // Update only the specific target that was changed
    if (value && value.trim() !== "") {
      const numValue = parseFloat(value)
      if (!isNaN(numValue)) {
        numericTargetValues[targetName] = numValue
        console.log(
          `[FRONTEND] Updated target ${targetName} to value ${numValue}`
        )
      } else {
        console.log(
          `[FRONTEND] Invalid numeric value for target ${targetName}: ${value}`
        )
      }
    } else {
      // If the value is empty, remove this target
      delete numericTargetValues[targetName]
      console.log(`[FRONTEND] Removed empty target ${targetName}`)
    }

    console.log(`[FRONTEND] Updated target values:`, numericTargetValues)

    console.log(`[FRONTEND] Numeric target values:`, numericTargetValues)

    // Only update if we have at least one target value
    if (Object.keys(numericTargetValues).length === 0) {
      console.log(`[FRONTEND] No valid numeric target values to update`)
      return
    }

    // Update the sample in the local state to reflect the new target values
    setSamples(prev => {
      const newSamples = [...prev]
      newSamples[sampleIndex] = {
        ...newSamples[sampleIndex],
        _targetValues: numericTargetValues
      }
      return newSamples
    })

    // Check if the sample has been saved to the database (has a valid UUID)
    // Only attempt to update in the database if it's a valid UUID
    if (isValidUUID(sample._sampleId)) {
      try {
        console.log(
          `[FRONTEND] Updating target values in database for sample ${sample._sampleId}`
        )

        // Update the sample in the database
        const result = await updateSampleTargetValuesWorkflowAction(
          sample._sampleId,
          numericTargetValues
        )

        if (!result.isSuccess) {
          console.error(
            `[FRONTEND] Error updating sample target values:`,
            result.message
          )
        } else {
          console.log(
            `[FRONTEND] Successfully updated target values for sample ${sample._sampleId}`
          )
        }
      } catch (error) {
        console.error(`[FRONTEND] Error updating sample target values:`, error)
      }
    } else {
      // For samples not yet saved to the database, just log that we're skipping the update
      console.log(
        `[FRONTEND] Skipping database update for unsaved sample with ID ${sample._sampleId}`
      )
    }
  }

  // Function to submit samples with target values
  const submitSamples = async () => {
    console.log("[FRONTEND] Starting sample submission process")
    console.log("[FRONTEND] Total samples available:", samples.length)

    if (samples.length === 0) {
      console.log("[FRONTEND] No samples available to submit")
      return
    }

    // Get samples that have all target values filled
    let samplesToSubmit = samples.filter((sample, index) =>
      hasAllTargetValues(index)
    )

    console.log(
      "[FRONTEND] Samples with all target values:",
      samplesToSubmit.length
    )

    if (samplesToSubmit.length === 0) {
      console.log("[FRONTEND] No samples have complete target values")
      toast({
        title: "No samples to submit",
        description: "Please fill in target values for at least one sample",
        variant: "destructive"
      })
      return
    }

    console.log(
      "[FRONTEND] Samples to submit before saving:",
      samplesToSubmit.map(s => ({
        id: s._sampleId,
        isUUID: isValidUUID(s._sampleId),
        savedToDatabase: s._savedToDatabase,
        targetValues: s._targetValues
      }))
    )

    // Always save samples before submitting to ensure they have database IDs
    // This simplifies the workflow and ensures all samples are in the database
    try {
      setIsSavingSamples(true)
      console.log("[FRONTEND] Saving samples before submission...")
      console.log("[FRONTEND] Optimization ID:", optimization.optimizerId)
      console.log(
        "[FRONTEND] Batch ID:",
        samplesToSubmit[0]._batchId || `${Date.now()}`
      )

      // Check if all samples already have valid UUIDs
      const unsavedSamples = samplesToSubmit.filter(
        s => !isValidUUID(s._sampleId) || !s._savedToDatabase
      )
      console.log(
        `[FRONTEND] ${unsavedSamples.length} of ${samplesToSubmit.length} samples need to be saved`
      )

      const saveResult = await saveSamplesWorkflowAction(
        optimization.optimizerId,
        samplesToSubmit,
        samplesToSubmit[0]._batchId || `${Date.now()}`
      )

      console.log("[FRONTEND] Save result:", {
        isSuccess: saveResult.isSuccess,
        message: saveResult.message,
        hasSavedSamples: !!saveResult.data?.savedSamples,
        savedSamplesCount: saveResult.data?.savedSamples?.length
      })

      if (saveResult.isSuccess && saveResult.data?.savedSamples) {
        // Update samples with database IDs
        setSamples(saveResult.data.savedSamples)

        // Trigger a refresh of the saved samples count
        setRefreshSavedSamplesCount(prev => prev + 1)

        // Continue with the updated samples
        samplesToSubmit = saveResult.data.savedSamples

        console.log(
          "[FRONTEND] Samples saved successfully:",
          samplesToSubmit.map(s => ({
            id: s._sampleId,
            isUUID: isValidUUID(s._sampleId),
            savedToDatabase: s._savedToDatabase
          }))
        )
      } else {
        console.error("[FRONTEND] Failed to save samples:", saveResult.message)
        toast({
          title: "Error saving samples",
          description:
            saveResult.message || "Failed to save samples before submission.",
          variant: "destructive"
        })
        setIsSavingSamples(false)
        return
      }
      setIsSavingSamples(false)
    } catch (error) {
      console.error("[FRONTEND] Error saving samples before submission:", error)
      toast({
        title: "Error",
        description:
          "Failed to save samples before submission. Please try again.",
        variant: "destructive"
      })
      setIsSavingSamples(false)
      return
    }

    setIsAddingMeasurement(true)
    try {
      // Get the sample IDs to submit
      const sampleIds = samplesToSubmit.map(sample => sample._sampleId)

      // Log the sample IDs being submitted
      console.log(
        `[FRONTEND] Submitting ${sampleIds.length} samples with IDs:`,
        sampleIds
      )

      // Double-check that all sample IDs are valid UUIDs
      const invalidIds = sampleIds.filter(id => !isValidUUID(id))
      if (invalidIds.length > 0) {
        console.error(
          `[FRONTEND] Found ${invalidIds.length} invalid sample IDs:`,
          invalidIds
        )
        toast({
          title: "Error submitting samples",
          description:
            "Some samples have invalid IDs. Please try saving samples again before submitting.",
          variant: "destructive"
        })
        setIsAddingMeasurement(false)
        return
      }

      console.log(
        `[FRONTEND] All ${sampleIds.length} sample IDs are valid UUIDs`
      )
      console.log(
        `[FRONTEND] Calling submitSamplesWorkflowAction with optimization ID: ${optimization.optimizerId}`
      )

      // Submit the samples
      const result = await submitSamplesWorkflowAction(
        optimization.optimizerId,
        sampleIds
      )

      console.log(`[FRONTEND] Submit result:`, {
        isSuccess: result.isSuccess,
        message: result.message
      })

      if (result.isSuccess) {
        toast({
          title: "Samples submitted",
          description: `Successfully submitted ${samplesToSubmit.length} samples to the optimization`
        })

        // Refresh the page to show the updated measurements
        router.refresh()
      } else {
        toast({
          title: "Error submitting samples",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error submitting samples:", error)
      toast({
        title: "Error",
        description: "Failed to submit samples. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsAddingMeasurement(false)
    }
  }

  const generateSamples = async (
    numSamples: number = 10,
    strategy: "LHS" | "random" | "sobol" = "LHS",
    seed?: number,
    respectConstraintsOverride?: boolean
  ) => {
    setSamples([])
    setDiscretizationTransparency(null)
    setConstraintViolations(undefined)
    setFeasibleSamples(undefined)
    setTotalAttempts(undefined)
    setIsGeneratingSamples(true)
    try {
      // First, check if the optimization exists in the API
      try {
        const loadResult = await loadOptimizationWorkflowAction(
          optimization.optimizerId
        )

        if (!loadResult.isSuccess) {
          // If loading fails, we'll try to continue anyway, but log the error
          console.warn(
            "Warning: Could not load optimization from API:",
            loadResult.message
          )
          // We don't return here - we'll try to generate samples anyway
        }
      } catch (error) {
        // If there's an error, we'll try to continue anyway, but log the error
        console.warn("Warning: Error checking optimization:", error)
        // We don't return here - we'll try to generate samples anyway
      }

      // Now generate samples
      const finalRespectConstraints =
        respectConstraintsOverride !== undefined
          ? respectConstraintsOverride
          : respectConstraints

      const result = await generateSamplesWorkflowAction(
        optimization.optimizerId,
        numSamples,
        strategy,
        seed,
        finalRespectConstraints
      )

      if (result.isSuccess && result.data) {
        // Log the generated samples
        console.log(
          `Successfully generated ${result.data.samples.length} samples`
        )

        // Check the sample IDs to ensure they're valid UUIDs
        const sampleIds = result.data.samples.map(sample => sample._sampleId)
        const validUUIDs = sampleIds.filter(id => isValidUUID(id))
        console.log(
          `${validUUIDs.length} of ${sampleIds.length} samples have valid UUID IDs`
        )

        setSamples(result.data.samples)

        // Store discretization transparency information
        setDiscretizationTransparency(result.data.discretizationTransparency)

        // Store constraint violation statistics
        setConstraintViolations(result.data.constraintViolations)
        setFeasibleSamples(result.data.feasibleSamples)
        setTotalAttempts(result.data.totalAttempts)

        // Initialize target values for the samples
        const targetConfigs = getTargetConfigs()
        const initialTargetValues: Record<number, Record<string, string>> = {}

        // For each sample, create an entry in the target values object
        result.data.samples.forEach((sample, index) => {
          initialTargetValues[index] = targetConfigs.reduce(
            (acc: Record<string, string>, target: any) => {
              acc[target.name] = ""
              return acc
            },
            {} as Record<string, string>
          )
        })

        // Update the target values state
        setTargetValues(prev => ({
          ...prev,
          ...initialTargetValues
        }))

        toast({
          title: "Samples generated",
          description: `Successfully generated ${result.data.samples.length} samples using ${
            strategy === "LHS"
              ? "Latin Hypercube Sampling"
              : strategy === "sobol"
                ? "Sobol sequences"
                : "random sampling"
          }`
        })
      } else {
        toast({
          title: "Error generating samples",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error generating samples:", error)
      // Check if this is a constraint-related error
      const errorMessage =
        (error as any)?.message ||
        "Failed to generate samples. Please try again or refresh the page."
      const isConstraintError =
        errorMessage.toLowerCase().includes("constraint") ||
        errorMessage.toLowerCase().includes("feasible") ||
        errorMessage.toLowerCase().includes("infeasible")

      toast({
        title: isConstraintError ? "Constraint Error" : "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsGeneratingSamples(false)
    }
  }

  const getSuggestions = async (batchSize: number = 1) => {
    console.log(
      `[getSuggestions] Starting with requested batch size: ${batchSize}`
    )

    // Re-fetch subscription to ensure we have the latest status
    console.log(
      `[getSuggestions] Current subscription tier: ${subscriptionTier}`
    )
    try {
      const currentSubscription = await getSubscriptionAction()
      console.log(
        `[getSuggestions] Re-fetched subscription tier: ${currentSubscription.tier}`
      )

      // Update the state if it's different
      if (currentSubscription.tier !== subscriptionTier) {
        console.log(
          `[getSuggestions] Updating subscription tier from ${subscriptionTier} to ${currentSubscription.tier}`
        )
        setSubscriptionTier(
          currentSubscription.tier as "free" | "trial" | "pro"
        )
      }

      // Check if user has a free subscription using the fresh data
      if (currentSubscription.tier === "free") {
        console.log(
          `[getSuggestions] User has free tier, showing upgrade modal`
        )
        setIsUpgradeModalOpen(true)
        return
      }
    } catch (error) {
      console.error(`[getSuggestions] Error re-fetching subscription:`, error)
      // If we can't fetch subscription, fall back to current state
      if (subscriptionTier === "free") {
        console.log(
          `[getSuggestions] Fallback: User appears to have free tier, showing upgrade modal`
        )
        setIsUpgradeModalOpen(true)
        return
      }
    }

    // Validate batch size - backend supports up to 100 suggestions
    const validBatchSize = Math.min(Math.max(1, batchSize), 100)
    if (validBatchSize !== batchSize) {
      console.warn(
        `[getSuggestions] Adjusted batch size from ${batchSize} to ${validBatchSize} (valid range: 1-100)`
      )
    }

    // Show warning for large batch sizes
    if (validBatchSize > 20) {
      toast({
        title: "Large Batch Size Warning",
        description: `You requested ${validBatchSize} suggestions. For batch sizes over 20, the operation may take a long time or time out. Consider using a smaller batch size for better reliability.`,
        variant: "destructive",
        duration: 6000 // Show for 6 seconds
      })
    }

    // Clear existing suggestions first to ensure we show "Generating new suggestions..."
    setSuggestions([])
    setIsLoadingSuggestions(true)
    setLoadingMode("new")

    const startTime = Date.now()
    console.time("getSuggestions-total")

    try {
      console.log(
        `[getSuggestions] Clearing existing suggestions and setting loading state`
      )

      // First, check if the optimization exists in the API
      try {
        console.log(
          `[getSuggestions] Checking if optimization exists: ${optimization.optimizerId}`
        )
        console.time("getSuggestions-loadOptimization")

        const loadResult = await loadOptimizationWorkflowAction(
          optimization.optimizerId
        )

        console.timeEnd("getSuggestions-loadOptimization")

        if (!loadResult.isSuccess) {
          // If loading fails, we'll try to continue anyway, but log the error
          console.warn(
            `[getSuggestions] Warning: Could not load optimization from API: ${loadResult.message}`
          )
          // We don't return here - we'll try to get suggestions anyway
        } else {
          console.log(`[getSuggestions] Successfully loaded optimization`)
        }
      } catch (error) {
        // If there's an error, we'll try to continue anyway, but log the error
        console.warn(
          `[getSuggestions] Warning: Error checking optimization:`,
          error
        )
        // We don't return here - we'll try to get suggestions anyway
      }

      console.log(
        `[getSuggestions] Requesting ${validBatchSize} suggestions from the API...`
      )
      console.time("getSuggestions-apiCall")

      // Now get suggestions with the validated batch size
      const result = await getSuggestionWorkflowAction(
        optimization.optimizerId,
        validBatchSize
      )

      console.timeEnd("getSuggestions-apiCall")
      const apiCallDuration = Date.now() - startTime
      console.log(`[getSuggestions] API call completed in ${apiCallDuration}ms`)

      if (result.isSuccess && result.data) {
        const receivedCount = result.data.suggestions.length
        console.log(
          `[getSuggestions] Successfully received ${receivedCount} suggestions from the API`
        )

        if (receivedCount !== validBatchSize) {
          console.warn(
            `[getSuggestions] Requested ${validBatchSize} suggestions but received ${receivedCount}`
          )
        }

        // Initialize target values for the suggestions
        console.log(
          `[getSuggestions] Initializing target values for ${receivedCount} suggestions`
        )
        const targetConfigs = getTargetConfigs()
        console.log(`[getSuggestions] Target configs:`, targetConfigs)

        const initialTargetValues: Record<number, Record<string, string>> = {}

        // For each suggestion, create an entry in the target values object
        result.data.suggestions.forEach((suggestion, index) => {
          initialTargetValues[index] = targetConfigs.reduce(
            (acc: Record<string, string>, target: any) => {
              acc[target.name] = ""
              return acc
            },
            {} as Record<string, string>
          )
        })

        console.log(
          `[getSuggestions] Updating target values state with initial values`
        )

        // Update the target values state
        setTargetValues(prev => ({
          ...prev,
          ...initialTargetValues
        }))

        console.log(
          `[getSuggestions] Setting suggestions state with ${receivedCount} items`
        )
        setSuggestions(result.data.suggestions)

        // Reset submitted suggestions when getting new ones
        console.log(`[getSuggestions] Resetting submitted suggestions state`)
        setSubmittedSuggestions({})

        // Set batch ID if provided by the API, regardless of the number of suggestions
        // This ensures that all measurements from the same batch share the same batch ID
        const batchId = result.data.batchId
        setCurrentBatchId(batchId)
        console.log(
          `[getSuggestions] Setting batch ID: ${batchId || "null"} for ${receivedCount} suggestions`
        )

        // Log batch information
        if (batchId) {
          console.log(
            `[getSuggestions] Loaded batch suggestions (${receivedCount}) with batch ID: ${batchId}`
          )
        } else {
          console.log(
            `[getSuggestions] Loaded sequential suggestion (no batch ID or single suggestion)`
          )
        }

        // Log the first suggestion for debugging
        if (receivedCount > 0) {
          console.log(
            `[getSuggestions] First suggestion sample:`,
            JSON.stringify(result.data.suggestions[0], null, 2).substring(
              0,
              200
            ) + "..."
          )
        }

        toast({
          title: "New suggestions generated",
          description: `Successfully generated ${receivedCount} new suggestions`
        })
      } else {
        console.error(`[getSuggestions] Error from API:`, result.message)
        toast({
          title: "Error loading suggestions",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error(`[getSuggestions] Error getting suggestions:`, error)

      // Try to extract more information from the error
      let errorDetails = ""
      if (error instanceof Error) {
        errorDetails = `Error type: ${error.constructor.name}, Message: ${error.message}`
        if (error.stack) {
          console.error(`[getSuggestions] Error stack:`, error.stack)
        }
      }

      console.error(`[getSuggestions] Error details: ${errorDetails}`)

      toast({
        title: "Error",
        description:
          "Failed to get suggestions. Please try again or refresh the page.",
        variant: "destructive"
      })
    } finally {
      console.log(`[getSuggestions] Setting loading state to false`)
      setIsLoadingSuggestions(false)

      const totalDuration = Date.now() - startTime
      console.log(
        `[getSuggestions] Total operation completed in ${totalDuration}ms`
      )
      console.timeEnd("getSuggestions-total")
    }
  }

  const addMeasurement = async (
    parameters: Record<string, any>,
    value: number | Record<string, number>,
    isRecommended: boolean = true
  ) => {
    setIsAddingMeasurement(true)
    try {
      // First, check if the optimization exists in the API
      try {
        const loadResult = await loadOptimizationWorkflowAction(
          optimization.optimizerId
        )

        if (!loadResult.isSuccess) {
          // If loading fails, we'll try to continue anyway, but log the error
          console.warn(
            "Warning: Could not load optimization from API:",
            loadResult.message
          )
          // We don't return here - we'll try to add the measurement anyway
        }
      } catch (error) {
        // If there's an error, we'll try to continue anyway, but log the error
        console.warn("Warning: Error checking optimization:", error)
        // We don't return here - we'll try to add the measurement anyway
      }

      // Now add the measurement
      // Pass batch ID for all API-recommended measurements if we have one
      // This ensures that all measurements from the same batch share the same batch ID
      const result = await addMeasurementWorkflowAction(
        optimization.optimizerId,
        parameters,
        value,
        isRecommended,
        isRecommended && currentBatchId ? currentBatchId : null
      )

      // Log for debugging
      console.log(
        `Added measurement with batch ID: ${isRecommended && currentBatchId ? currentBatchId : "null"}`
      )

      if (result.isSuccess) {
        toast({
          title: "Measurement added",
          description: "Successfully added measurement to the optimization"
        })

        // Clear target values for the submitted suggestion
        setTargetValues(prev => {
          const newValues = { ...prev }

          if (isRecommended) {
            // For suggested experiments, clear the active suggestion
            delete newValues[activeSuggestionIndex]
          } else {
            // For manual experiments, clear the manual entry
            newValues[-1] = getTargetConfigs().reduce(
              (acc: Record<string, string>, target: any) => {
                acc[target.name] = ""
                return acc
              },
              {} as Record<string, string>
            )
          }

          return newValues
        })

        // If this was a suggested measurement, mark it as submitted
        if (isRecommended) {
          // Instead of clearing all suggestions, just mark this one as submitted
          // This allows users to submit multiple measurements from the same batch
          setSubmittedSuggestions(prev => ({
            ...prev,
            [activeSuggestionIndex]: true
          }))

          // Log for debugging
          console.log(
            `Submitted measurement for suggestion ${activeSuggestionIndex} with batch ID ${currentBatchId}`
          )
          console.log(
            `Submitted suggestions: ${Object.keys(submittedSuggestions).join(", ")}`
          )

          // Mark the suggestion as submitted in the database
          // This ensures it won't be loaded again on next visit
          const suggestion = suggestions[activeSuggestionIndex]
          if (suggestion && suggestion._suggestionId) {
            try {
              console.log(
                `Marking suggestion ${suggestion._suggestionId} as submitted in database`
              )
              // Use await to ensure the database update completes before continuing
              const result = await markSuggestionSubmittedAction(
                suggestion._suggestionId
              )
              if (result.isSuccess) {
                console.log(
                  `Successfully marked suggestion ${suggestion._suggestionId} as submitted`
                )
              } else {
                console.warn(
                  `Failed to mark suggestion as submitted: ${result.message}`
                )
              }
            } catch (error) {
              console.error(`Error marking suggestion as submitted:`, error)
            }
          }

          // Don't refresh the page if we're in the middle of a batch submission
          // This allows all measurements to be submitted before refreshing
          if (!isAddingMeasurement) {
            // Refresh the page to show the updated measurements
            router.refresh()
          }
        } else {
          // For manual measurements, always refresh
          router.refresh()
        }
      } else {
        toast({
          title: "Error adding measurement",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error adding measurement:", error)
      toast({
        title: "Error",
        description:
          "Failed to add measurement. Please try again or refresh the page.",
        variant: "destructive"
      })
    } finally {
      setIsAddingMeasurement(false)
    }
  }

  const handleManualParameterChange = (
    experimentIndex: number,
    name: string,
    value: any,
    paramType?: string
  ) => {
    // Convert numerical values to numbers
    let processedValue = value
    if (
      paramType &&
      (paramType === "NumericalDiscrete" || paramType === "NumericalContinuous")
    ) {
      // Convert to number if it's a numerical parameter
      const numValue = parseFloat(value)
      if (!isNaN(numValue)) {
        processedValue = numValue
      }
    }

    setManualExperiments(prev => {
      const updated = [...prev]
      if (!updated[experimentIndex]) {
        updated[experimentIndex] = {}
      }
      updated[experimentIndex] = {
        ...updated[experimentIndex],
        [name]: processedValue
      }
      return updated
    })
  }

  // Function to add a new manual experiment
  const addManualExperiment = () => {
    // Create initial parameters for a new experiment
    const createInitialParams = () => {
      return (optimization.config as any).parameters.reduce(
        (acc: Record<string, any>, param: any) => {
          if (param.type === "NumericalDiscrete") {
            // Ensure we're using a valid value from the allowed values
            if (param.values && param.values.length > 0) {
              // Convert string values to numbers for numerical parameters
              acc[param.name] =
                typeof param.values[0] === "string"
                  ? parseFloat(param.values[0])
                  : param.values[0]
            } else {
              acc[param.name] = 0
            }
          } else if (param.type === "NumericalContinuous") {
            // Ensure we're within bounds
            acc[param.name] = param.bounds ? param.bounds[0] : 0
          } else if (param.type === "CategoricalParameter") {
            // Use the first allowed value
            acc[param.name] =
              param.values && param.values.length > 0 ? param.values[0] : ""
          }
          return acc
        },
        {} as Record<string, any>
      )
    }

    // Add a new experiment to the list
    setManualExperiments(prev => [...prev, createInitialParams()])

    // Initialize target values for the new experiment
    const targetConfigs = getTargetConfigs()
    const newExperimentIndex = -1 - manualExperiments.length // Use -2, -3, etc. for additional experiments

    setTargetValues(prev => ({
      ...prev,
      [newExperimentIndex]: targetConfigs.reduce(
        (acc: Record<string, string>, target: any) => {
          acc[target.name] = ""
          return acc
        },
        {} as Record<string, string>
      )
    }))
  }

  // Function to remove a manual experiment
  const removeManualExperiment = (experimentIndex: number) => {
    // Don't allow removing the last experiment
    if (manualExperiments.length <= 1) return

    // Remove the experiment from the list
    setManualExperiments(prev => {
      const updated = [...prev]
      updated.splice(experimentIndex, 1)
      return updated
    })

    // Remove target values for this experiment
    const targetIndex = experimentIndex === 0 ? -1 : -1 - experimentIndex
    setTargetValues(prev => {
      const updated = { ...prev }
      delete updated[targetIndex]
      return updated
    })
  }

  // Function to delete a suggestion
  const handleDeleteSuggestion = (suggestionIndex: number) => {
    // Remove the suggestion from the array
    setSuggestions(prev => {
      const newSuggestions = [...prev]
      newSuggestions.splice(suggestionIndex, 1)
      return newSuggestions
    })

    // Remove the target values for this suggestion
    setTargetValues(prev => {
      const newValues = { ...prev }
      delete newValues[suggestionIndex]
      return newValues
    })

    // Remove from submitted suggestions if it was submitted
    setSubmittedSuggestions(prev => {
      const newSubmitted = { ...prev }
      delete newSubmitted[suggestionIndex]
      return newSubmitted
    })

    toast({
      title: "Suggestion deleted",
      description: "The suggestion has been removed"
    })
  }

  const handleTargetValueChange = (
    index: number,
    name: string,
    value: string
  ) => {
    try {
      // Update local state first for immediate UI feedback
      setTargetValues(prev => {
        // Create a deep copy of the previous state
        const newValues = { ...prev }

        // Initialize the index if it doesn't exist
        if (!newValues[index]) {
          newValues[index] = {}
        }

        // Update the target value
        newValues[index] = {
          ...newValues[index],
          [name]: value
        }

        return newValues
      })

      // Determine if this is a suggestion or a sample
      const isSuggestion = index >= 0 && suggestions[index]
      const isSample = index >= 0 && samples[index]

      // If this is a suggestion from the database, save the target value
      if (isSuggestion) {
        try {
          const suggestion = suggestions[index]
          const suggestionId = suggestion._suggestionId

          if (suggestionId) {
            // Get the current values for this suggestion
            const currentValues = targetValues[index] || {}

            // Create updated values with the new value
            const updatedValues = {
              ...currentValues,
              [name]: value
            }

            // Save to database in the background
            // We don't need to await this or show errors to the user
            // It's just for persistence between sessions
            try {
              // Use the workflow action to update target values
              updateSuggestionTargetValuesWorkflowAction(
                suggestionId,
                updatedValues
              )
                .then(result => {
                  if (!result.isSuccess) {
                    console.warn(
                      "Failed to save suggestion target value to database:",
                      result.message
                    )
                  }
                })
                .catch(error => {
                  console.error(
                    "Error saving suggestion target value to database:",
                    error
                  )
                })
            } catch (error) {
              console.error(
                "Error calling updateSuggestionTargetValuesWorkflowAction:",
                error
              )
              // Don't show an error to the user - the UI still works
            }
          }
        } catch (error) {
          console.error(
            "Error preparing to save suggestion target value:",
            error
          )
          // Don't show an error to the user - the UI still works
        }
      }
      // If this is a sample from the database, save the target value
      else if (isSample) {
        try {
          const sample = samples[index]
          const sampleId = sample._sampleId

          if (sampleId) {
            // Get the current values for this sample
            const currentValues = targetValues[index] || {}

            // Create updated values with the new value
            // Convert string values to numbers for the API
            const numericValues: Record<string, number> = {}

            // Process existing values
            for (const [key, val] of Object.entries(currentValues)) {
              const numVal = parseFloat(val)
              if (!isNaN(numVal)) {
                numericValues[key] = numVal
              }
            }

            // Add the new value if it's valid
            const numValue = parseFloat(value)
            if (!isNaN(numValue)) {
              numericValues[name] = numValue
            }

            // Save to database in the background
            // We don't need to await this or show errors to the user
            // It's just for persistence between sessions
            try {
              // Use the workflow action to update target values
              updateSampleTargetValuesWorkflowAction(sampleId, numericValues)
                .then(result => {
                  if (!result.isSuccess) {
                    console.warn(
                      "Failed to save sample target value to database:",
                      result.message
                    )
                  }
                })
                .catch(error => {
                  console.error(
                    "Error saving sample target value to database:",
                    error
                  )
                })
            } catch (error) {
              console.error(
                "Error calling updateSampleTargetValuesWorkflowAction:",
                error
              )
              // Don't show an error to the user - the UI still works
            }
          }
        } catch (error) {
          console.error("Error preparing to save sample target value:", error)
          // Don't show an error to the user - the UI still works
        }
      }
    } catch (error) {
      console.error("Error in handleTargetValueChange:", error)
      // Don't show an error to the user - the UI still works
    }
  }

  // Function to submit all suggestions at once
  const handleSubmitAllSuggestions = async () => {
    if (suggestions.length === 0) {
      toast({
        title: "No suggestions",
        description: "There are no suggestions to submit.",
        variant: "destructive"
      })
      return
    }

    // Check if any suggestion has target values
    const hasAnyTargetValues = Object.keys(targetValues).some(key => {
      const index = parseInt(key)
      return index >= 0 && Object.keys(targetValues[index] || {}).length > 0
    })

    if (!hasAnyTargetValues) {
      toast({
        title: "Missing target values",
        description: "Please enter target values for at least one suggestion.",
        variant: "destructive"
      })
      return
    }

    // Get target configs
    const targetConfigs = getTargetConfigs()

    // Track if we've submitted at least one measurement
    let submittedCount = 0
    let errorCount = 0

    // Create a local copy of the submitted suggestions state that we'll update during the process
    // This helps ensure we have the most up-to-date state when making decisions
    const updatedSubmittedSuggestions = { ...submittedSuggestions }

    // Set a flag to prevent multiple submissions
    setIsAddingMeasurement(true)

    // Count how many suggestions we need to process
    const suggestionsToProcess = suggestions.filter((_, index) => {
      // Only count suggestions that have target values and haven't been submitted yet
      const hasTargetValues = Object.keys(targetValues[index] || {}).length > 0
      const notSubmitted = !updatedSubmittedSuggestions[index]
      return hasTargetValues && notSubmitted
    }).length

    // Initialize progress
    setBatchProgress({ current: 0, total: suggestionsToProcess })

    // Process each suggestion that has target values
    for (let index = 0; index < suggestions.length; index++) {
      // Skip if already submitted
      if (updatedSubmittedSuggestions[index]) {
        continue
      }

      // Get the target values for this suggestion
      const suggestionTargetValues = targetValues[index] || {}

      // Skip if no target values
      if (Object.keys(suggestionTargetValues).length === 0) {
        continue
      }

      try {
        if (isMultiTarget()) {
          // For multi-target, validate all target values
          const missingTargets = targetConfigs.filter(
            (target: any) =>
              !suggestionTargetValues[target.name] ||
              suggestionTargetValues[target.name].trim() === ""
          )

          if (missingTargets.length > 0) {
            console.log(
              `Skipping suggestion ${index} due to missing targets: ${missingTargets.map((t: any) => t.name).join(", ")}`
            )
            continue
          }

          // Parse and validate all target values
          const parsedTargetValues: Record<string, number> = {}
          let hasInvalidValue = false

          for (const target of targetConfigs as any[]) {
            const parsedValue = parseFloat(suggestionTargetValues[target.name])
            if (isNaN(parsedValue)) {
              console.log(
                `Skipping suggestion ${index} due to invalid value for ${target.name}`
              )
              hasInvalidValue = true
              break
            }
            parsedTargetValues[target.name] = parsedValue
          }

          if (!hasInvalidValue) {
            // Set the active suggestion index
            setActiveSuggestionIndex(index)

            // Submit the measurement
            await addMeasurement(suggestions[index], parsedTargetValues, true)

            // Ensure the suggestion is marked as submitted in the database
            const suggestion = suggestions[index]
            if (suggestion && suggestion._suggestionId) {
              try {
                console.log(
                  `Batch submission: Marking suggestion ${suggestion._suggestionId} as submitted in database`
                )
                const result = await markSuggestionSubmittedAction(
                  suggestion._suggestionId
                )
                if (result.isSuccess) {
                  console.log(
                    `Batch submission: Successfully marked suggestion ${suggestion._suggestionId} as submitted`
                  )
                } else {
                  console.warn(
                    `Batch submission: Failed to mark suggestion as submitted: ${result.message}`
                  )
                }
              } catch (error) {
                console.error(
                  `Batch submission: Error marking suggestion as submitted:`,
                  error
                )
              }
            }

            submittedCount++

            // Update progress
            setBatchProgress(prev => ({ ...prev, current: prev.current + 1 }))

            // Explicitly update the submittedSuggestions state to ensure UI reflects the change
            setSubmittedSuggestions(prev => ({
              ...prev,
              [index]: true
            }))

            // Update our local copy as well
            updatedSubmittedSuggestions[index] = true
          }
        } else {
          // For single target
          const targetName = targetConfigs[0].name
          const value = parseFloat(suggestionTargetValues[targetName] || "")

          if (!isNaN(value)) {
            // Set the active suggestion index
            setActiveSuggestionIndex(index)

            // Submit the measurement
            await addMeasurement(suggestions[index], value, true)

            // Ensure the suggestion is marked as submitted in the database
            const suggestion = suggestions[index]
            if (suggestion && suggestion._suggestionId) {
              try {
                console.log(
                  `Batch submission: Marking suggestion ${suggestion._suggestionId} as submitted in database`
                )
                const result = await markSuggestionSubmittedAction(
                  suggestion._suggestionId
                )
                if (result.isSuccess) {
                  console.log(
                    `Batch submission: Successfully marked suggestion ${suggestion._suggestionId} as submitted`
                  )
                } else {
                  console.warn(
                    `Batch submission: Failed to mark suggestion as submitted: ${result.message}`
                  )
                }
              } catch (error) {
                console.error(
                  `Batch submission: Error marking suggestion as submitted:`,
                  error
                )
              }
            }

            submittedCount++

            // Update progress
            setBatchProgress(prev => ({ ...prev, current: prev.current + 1 }))

            // Explicitly update the submittedSuggestions state to ensure UI reflects the change
            setSubmittedSuggestions(prev => ({
              ...prev,
              [index]: true
            }))

            // Update our local copy as well
            updatedSubmittedSuggestions[index] = true
          }
        }
      } catch (error) {
        console.error(`Error submitting suggestion ${index}:`, error)
        errorCount++
      }
    }

    // Reset the flag and progress
    setIsAddingMeasurement(false)
    setBatchProgress({ current: 0, total: 0 })

    // Force a final update of the submittedSuggestions state with all the values from our local copy
    // This ensures the UI is fully up-to-date
    setSubmittedSuggestions(updatedSubmittedSuggestions)

    // Log the final state of submitted suggestions for debugging
    console.log("Final submitted suggestions state:", submittedSuggestions)
    console.log(
      "Local copy of submitted suggestions:",
      updatedSubmittedSuggestions
    )

    // Show a toast with the results
    if (submittedCount > 0) {
      toast({
        title: "Batch submission complete",
        description: `Successfully submitted ${submittedCount} measurements${errorCount > 0 ? ` (${errorCount} errors)` : ""}.`,
        variant: errorCount > 0 ? "destructive" : "default"
      })

      // Refresh the page to show the updated measurements
      router.refresh()
    } else if (errorCount > 0) {
      toast({
        title: "Batch submission failed",
        description: `Failed to submit any measurements (${errorCount} errors).`,
        variant: "destructive"
      })
    } else {
      toast({
        title: "No measurements submitted",
        description: "No valid measurements were found to submit.",
        variant: "destructive"
      })
    }
  }

  const handleManualSubmit = async () => {
    // Get target configs
    const targetConfigs = getTargetConfigs()

    // Generate a batch ID for multiple experiments
    // Always generate a batch ID when there are multiple experiments
    // This ensures they're grouped together in the History tab
    const batchId =
      manualExperiments.length > 1 ? `manual-batch-${Date.now()}` : null

    // Log batch information
    if (batchId) {
      console.log(
        `Generated batch ID for manual experiments: ${batchId} (count: ${manualExperiments.length})`
      )
    }

    // Track submission progress
    let submittedCount = 0
    let errorCount = 0

    // Set a flag to prevent multiple submissions
    setIsAddingMeasurement(true)

    // Initialize progress
    setBatchProgress({ current: 0, total: manualExperiments.length })

    // Process each manual experiment
    for (
      let experimentIndex = 0;
      experimentIndex < manualExperiments.length;
      experimentIndex++
    ) {
      // Get the experiment parameters
      const experimentParams = manualExperiments[experimentIndex]

      // Get the target values for this experiment
      const targetIndex = experimentIndex === 0 ? -1 : -1 - experimentIndex
      const experimentTargetValues = targetValues[targetIndex] || {}

      // Validate parameters
      let hasInvalidParams = false
      for (const param of (optimization.config as any).parameters) {
        const value = experimentParams[param.name]

        if (value === undefined || value === "") {
          toast({
            title: "Missing parameter",
            description: `Please provide a value for ${param.name} in experiment ${experimentIndex + 1}`,
            variant: "destructive"
          })
          hasInvalidParams = true
          break
        }

        if (param.type === "NumericalDiscrete") {
          const numValue = typeof value === "string" ? parseFloat(value) : value
          if (isNaN(numValue)) {
            toast({
              title: "Invalid parameter value",
              description: `Please enter a valid number for ${param.name} in experiment ${experimentIndex + 1}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }

          // For NumericalDiscrete, check if the value is in the allowed values
          if (Array.isArray(param.values) && !param.values.includes(numValue)) {
            toast({
              title: "Invalid parameter value",
              description: `${numValue} is not a valid value for ${param.name}. Allowed values are: ${param.values.join(", ")}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }

          // Convert to number for submission
          experimentParams[param.name] = numValue
        } else if (param.type === "NumericalContinuous") {
          const numValue = typeof value === "string" ? parseFloat(value) : value
          if (isNaN(numValue)) {
            toast({
              title: "Invalid parameter value",
              description: `Please enter a valid number for ${param.name} in experiment ${experimentIndex + 1}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }

          // For NumericalContinuous, check if the value is within bounds
          if (
            param.bounds &&
            (numValue < param.bounds[0] || numValue > param.bounds[1])
          ) {
            toast({
              title: "Invalid parameter value",
              description: `${numValue} is outside the allowed range for ${param.name}. Range is: ${param.bounds[0]} to ${param.bounds[1]}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }

          // Convert to number for submission
          experimentParams[param.name] = numValue
        } else if (param.type === "CategoricalParameter") {
          // For CategoricalParameter, check if the value is in the allowed values
          if (Array.isArray(param.values) && !param.values.includes(value)) {
            toast({
              title: "Invalid parameter value",
              description: `"${value}" is not a valid value for ${param.name}. Allowed values are: ${param.values.join(", ")}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }
        }
      }

      if (hasInvalidParams) {
        errorCount++
        continue
      }

      // For multi-target optimizations, validate all target values
      if (isMultiTarget()) {
        // Check if any target value is missing
        const missingTargets = targetConfigs.filter(
          (target: any) =>
            !experimentTargetValues[target.name] ||
            experimentTargetValues[target.name].trim() === ""
        )

        if (missingTargets.length > 0) {
          toast({
            title: "Missing target values",
            description: `Please enter values for all targets in experiment ${experimentIndex + 1}: ${missingTargets.map((t: any) => t.name).join(", ")}.`,
            variant: "destructive"
          })
          errorCount++
          continue
        }

        // Parse and validate all target values
        const parsedTargetValues: Record<string, number> = {}
        let hasInvalidValue = false

        for (const target of targetConfigs as any[]) {
          const parsedValue = parseFloat(experimentTargetValues[target.name])
          if (isNaN(parsedValue)) {
            toast({
              title: "Invalid target value",
              description: `Please enter a valid number for ${target.name} in experiment ${experimentIndex + 1}`,
              variant: "destructive"
            })
            hasInvalidValue = true
            break
          }
          parsedTargetValues[target.name] = parsedValue
        }

        if (hasInvalidValue) {
          errorCount++
          continue
        }

        try {
          // Submit the measurement with the batch ID for multiple experiments
          console.log(
            `Submitting manual experiment ${experimentIndex + 1} with batch ID: ${batchId || "null"}`
          )
          const result = await addMeasurementWorkflowAction(
            optimization.optimizerId,
            experimentParams,
            parsedTargetValues,
            false,
            batchId
          )
          console.log(
            `Manual experiment ${experimentIndex + 1} submission result:`,
            result.isSuccess ? "success" : "failure"
          )

          if (result.isSuccess) {
            // Mark this experiment as submitted
            const targetIndex =
              experimentIndex === 0 ? -1 : -1 - experimentIndex
            setSubmittedSuggestions(prev => ({
              ...prev,
              [targetIndex]: true
            }))
            submittedCount++
          }

          // Update progress
          setBatchProgress(prev => ({ ...prev, current: prev.current + 1 }))
        } catch (error) {
          console.error(
            `Error submitting manual experiment ${experimentIndex + 1}:`,
            error
          )
          errorCount++
        }
      } else {
        // Single target optimization
        const targetName = targetConfigs[0].name
        const parsedValue = parseFloat(experimentTargetValues[targetName] || "")

        if (isNaN(parsedValue)) {
          toast({
            title: "Invalid target value",
            description: `Please enter a valid number for the target value in experiment ${experimentIndex + 1}`,
            variant: "destructive"
          })
          errorCount++
          continue
        }

        try {
          // Submit the measurement with the batch ID for multiple experiments
          console.log(
            `Submitting manual experiment ${experimentIndex + 1} with batch ID: ${batchId || "null"}`
          )
          const result = await addMeasurementWorkflowAction(
            optimization.optimizerId,
            experimentParams,
            parsedValue,
            false,
            batchId
          )
          console.log(
            `Manual experiment ${experimentIndex + 1} submission result:`,
            result.isSuccess ? "success" : "failure"
          )

          if (result.isSuccess) {
            // Mark this experiment as submitted
            const targetIndex =
              experimentIndex === 0 ? -1 : -1 - experimentIndex
            setSubmittedSuggestions(prev => ({
              ...prev,
              [targetIndex]: true
            }))
            submittedCount++
          }

          // Update progress
          setBatchProgress(prev => ({ ...prev, current: prev.current + 1 }))
        } catch (error) {
          console.error(
            `Error submitting manual experiment ${experimentIndex + 1}:`,
            error
          )
          errorCount++
        }
      }
    }

    // Reset the flag and progress
    setIsAddingMeasurement(false)
    setBatchProgress({ current: 0, total: 0 })

    // Show a toast with the results
    if (submittedCount > 0) {
      toast({
        title:
          submittedCount > 1
            ? "Batch submission complete"
            : "Measurement submitted",
        description:
          submittedCount > 1
            ? `Successfully submitted ${submittedCount} measurements${errorCount > 0 ? ` (${errorCount} errors)` : ""}.`
            : "Successfully added measurement to the optimization",
        variant: errorCount > 0 ? "destructive" : "default"
      })

      // Show a success message similar to the suggested experiments
      if (errorCount === 0) {
        toast({
          title: "Optimization updated",
          description:
            submittedCount > 1
              ? "All experiments have been added to the optimization history. You can now enter new target values for the same parameters or modify parameters and submit again."
              : "The experiment has been added to the optimization history. You can now enter new target values for the same parameters or modify parameters and submit again."
        })
      }

      // Keep the parameter values but reset the submission state
      // This allows users to submit new target values for the same parameters

      // Clear the submittedSuggestions state for manual experiments
      setSubmittedSuggestions(prev => {
        const updated = { ...prev }
        // Remove all negative indices (manual experiments)
        Object.keys(updated).forEach(key => {
          const index = parseInt(key)
          if (index < 0) delete updated[index]
        })
        return updated
      })

      // Reset target values
      const initialTargetValues: Record<number, Record<string, string>> = {}
      initialTargetValues[-1] = targetConfigs.reduce(
        (acc: Record<string, string>, target: any) => {
          acc[target.name] = ""
          return acc
        },
        {} as Record<string, string>
      )
      setTargetValues(prev => {
        const updated = { ...prev }
        // Keep suggested experiment values, but clear manual ones
        Object.keys(updated).forEach(key => {
          const index = parseInt(key)
          if (index < 0) delete updated[index]
        })
        return { ...updated, ...initialTargetValues }
      })

      // Refresh the page to show the updated measurements
      router.refresh()
    } else if (errorCount > 0) {
      toast({
        title: "Submission failed",
        description: `Failed to submit any measurements (${errorCount} errors).`,
        variant: "destructive"
      })
    } else {
      toast({
        title: "No measurements submitted",
        description: "No valid measurements were found to submit.",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Run Experiments</h2>

      {/* Add a workflow explanation for new optimizations */}
      {(optimization as any).measurementCount === 0 && (
        <Alert className="mb-4 border-amber-200 bg-amber-50 dark:border-amber-900 dark:bg-amber-950/50">
          <AlertCircle className="size-5 text-amber-600 dark:text-amber-400" />
          <AlertTitle>Getting Started with Experiments</AlertTitle>
          <AlertDescription>
            <p className="mb-2">
              To use AI-powered suggestions, you need to first provide some
              initial data. Follow these steps:
            </p>
            <ol className="list-decimal space-y-1 pl-5 text-sm">
              <li>
                Start with <strong>Sample Generation</strong> to create and
                submit initial data points
              </li>
              <li>
                Or use <strong>Manual Experiments</strong> to enter your own
                experiment results
              </li>
              <li>
                Once you have submitted measurements, the{" "}
                <strong>Suggested Experiments</strong> tab will become fully
                functional
              </li>
            </ol>
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="tabs-list">
          {/* Render tabs in the order determined by the tab order function */}
          {getTabOrder((optimization as any).measurementCount || 0).map(
            tabValue => {
              // Render the appropriate tab based on the tab value
              if (tabValue === "suggested") {
                return (
                  <TabsTrigger
                    key={tabValue}
                    value="suggested"
                    className="relative"
                    data-demo="suggested-tab"
                  >
                    <Sparkles className="mr-2 size-4" />
                    <span>Suggested Experiments</span>
                    <span className="ml-2 inline-flex items-center rounded-full bg-purple-100 px-2 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                      <span className="mr-1">AI</span>
                      <Brain className="size-3" />
                    </span>
                    {(optimization as any).measurementCount === 0 && (
                      <span className="absolute -right-1 -top-1 flex size-3">
                        <span className="absolute inline-flex size-full animate-ping rounded-full bg-amber-400 opacity-75"></span>
                        <span className="relative inline-flex size-3 rounded-full bg-amber-500"></span>
                      </span>
                    )}
                  </TabsTrigger>
                )
              } else if (tabValue === "samples") {
                return (
                  <TabsTrigger
                    key={tabValue}
                    value="samples"
                    data-demo="samples-tab"
                  >
                    <svg
                      className="mr-2 size-4"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="3" y="3" width="18" height="18" rx="2" />
                      <circle cx="8.5" cy="8.5" r="1.5" />
                      <circle cx="15.5" cy="8.5" r="1.5" />
                      <circle cx="8.5" cy="15.5" r="1.5" />
                      <circle cx="15.5" cy="15.5" r="1.5" />
                    </svg>
                    Sample Generation
                    {(optimization as any).measurementCount === 0 && (
                      <span className="ml-2 inline-flex items-center rounded-full bg-amber-100 px-2 py-0.5 text-xs font-medium text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                        Start here
                      </span>
                    )}
                  </TabsTrigger>
                )
              } else if (tabValue === "upload") {
                return (
                  <TabsTrigger key={tabValue} value="upload">
                    <Upload className="mr-2 size-4" />
                    Upload Dataset
                  </TabsTrigger>
                )
              } else {
                return (
                  <TabsTrigger
                    key={tabValue}
                    value="manual"
                    data-demo="manual-tab"
                  >
                    <Beaker className="mr-2 size-4" />
                    Manual Experiments
                  </TabsTrigger>
                )
              }
            }
          )}
        </TabsList>

        <TabsContent
          value="samples"
          className="space-y-6"
          data-demo="sample-generation-section"
        >
          <Card>
            <CardHeader>
              <CardTitle>Statistical Sample Generation</CardTitle>
              <CardDescription className="mt-1 flex items-center">
                <svg
                  className="mr-2 size-4 text-blue-500"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="3" y="3" width="18" height="18" rx="2" />
                  <circle cx="8.5" cy="8.5" r="1.5" />
                  <circle cx="15.5" cy="8.5" r="1.5" />
                  <circle cx="8.5" cy="15.5" r="1.5" />
                  <circle cx="15.5" cy="15.5" r="1.5" />
                </svg>
                <span>
                  Generate sample points using Latin Hypercube Sampling or
                  Random Sampling
                </span>
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <h3 className="text-sm font-medium">
                      Generation Parameters
                    </h3>
                    <p className="text-muted-foreground text-xs">
                      Configure how samples are generated
                    </p>
                  </div>
                </div>

                <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-end">
                  <div className="w-full sm:w-64">
                    <Label htmlFor="sampleCount" className="text-sm">
                      Number of Samples
                    </Label>
                    <div className="mt-1 flex items-center gap-2">
                      <Input
                        id="sampleCount"
                        type="number"
                        min="1"
                        max="100"
                        defaultValue="10"
                        className="w-full"
                        onChange={e => {
                          // Ensure value is between 1 and 100
                          const value = parseInt(e.target.value)
                          if (value < 1) e.target.value = "1"
                          if (value > 100) e.target.value = "100"
                        }}
                      />
                      <div className="text-muted-foreground text-xs">
                        (1-100)
                      </div>
                    </div>
                  </div>

                  <div className="w-full sm:w-64">
                    <Label htmlFor="samplingStrategy" className="text-sm">
                      Sampling Strategy
                    </Label>
                    <select
                      id="samplingStrategy"
                      className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={samplingStrategy}
                      onChange={e =>
                        setSamplingStrategy(
                          e.target.value as "LHS" | "random" | "sobol"
                        )
                      }
                    >
                      <option value="LHS">Latin Hypercube Sampling</option>
                      <option value="random">Random Sampling</option>
                      <option value="sobol">Sobol Sequences</option>
                    </select>
                  </div>

                  <div className="w-full sm:w-64">
                    <Label htmlFor="seed" className="text-sm">
                      Random Seed (Optional)
                    </Label>
                    <Input
                      id="seed"
                      type="number"
                      placeholder="Leave empty for random"
                      className="w-full"
                    />
                  </div>

                  {/* Constraint-aware sampling options */}
                  {(() => {
                    const config = optimization.config as any
                    const hasConstraints =
                      config.constraints && config.constraints.length > 0

                    if (hasConstraints) {
                      return (
                        <div className="flex items-center space-x-3 rounded-md border border-blue-200 bg-blue-50 px-3 py-2">
                          <div className="flex items-center space-x-2">
                            <svg
                              className="size-4 text-blue-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                              />
                            </svg>
                            <span className="text-sm font-medium text-blue-900">
                              {config.constraints.length} constraint
                              {config.constraints.length !== 1 ? "s" : ""}
                            </span>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Switch
                              id="respectConstraints"
                              checked={respectConstraints}
                              onCheckedChange={setRespectConstraints}
                              className="scale-75"
                            />
                            <Label
                              htmlFor="respectConstraints"
                              className="text-xs text-blue-800"
                            >
                              {respectConstraints ? "Respect" : "Ignore"}
                            </Label>
                          </div>
                        </div>
                      )
                    }
                    return null
                  })()}

                  <div className="flex w-full flex-wrap items-center gap-2 sm:w-auto">
                    {/* 1. Generate Samples */}
                    <Button
                      className="w-full sm:w-auto"
                      data-demo="generate-samples-button"
                      onClick={() => {
                        const sampleCountInput = document.getElementById(
                          "sampleCount"
                        ) as HTMLInputElement
                        const seedInput = document.getElementById(
                          "seed"
                        ) as HTMLInputElement
                        const sampleCount =
                          parseInt(sampleCountInput.value) || 10
                        const seed = seedInput.value
                          ? parseInt(seedInput.value)
                          : undefined
                        generateSamples(
                          sampleCount,
                          samplingStrategy,
                          seed,
                          respectConstraints
                        )
                      }}
                      disabled={isGeneratingSamples || isLoadingSamples}
                    >
                      {isGeneratingSamples ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <svg
                          className="mr-2 size-4"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <rect x="3" y="3" width="18" height="18" rx="2" />
                          <circle cx="8.5" cy="8.5" r="1.5" />
                          <circle cx="15.5" cy="8.5" r="1.5" />
                          <circle cx="8.5" cy="15.5" r="1.5" />
                          <circle cx="15.5" cy="15.5" r="1.5" />
                        </svg>
                      )}
                      {isGeneratingSamples
                        ? "Generating Samples..."
                        : (() => {
                            const config = optimization.config as any
                            const hasConstraints =
                              config.constraints &&
                              config.constraints.length > 0

                            if (!hasConstraints) {
                              return "Generate Samples"
                            }

                            return respectConstraints
                              ? "Generate Constraint-Aware Samples"
                              : "Generate Samples (Ignoring Constraints)"
                          })()}
                    </Button>

                    {/* 2. Save Samples */}
                    {samples.length > 0 && (
                      <Button
                        onClick={saveSamples}
                        disabled={isGeneratingSamples || isSavingSamples}
                        variant="secondary"
                        className="w-full sm:w-auto"
                      >
                        {isSavingSamples ? (
                          <>
                            <Loader2 className="mr-2 size-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 size-4" />
                            Save Samples
                          </>
                        )}
                      </Button>
                    )}

                    {/* 3. Load Saved */}
                    <Button
                      onClick={loadSavedSamples}
                      disabled={isGeneratingSamples || isLoadingSamples}
                      variant="outline"
                      className="w-full sm:w-auto"
                    >
                      {isLoadingSamples ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <Database className="mr-2 size-4" />
                      )}
                      Load Saved
                      {savedSamplesCount > 0 && (
                        <Badge variant="secondary" className="ml-1">
                          {savedSamplesCount}
                        </Badge>
                      )}
                    </Button>

                    {/* 4. Export */}
                    {samples.length > 0 && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full sm:w-auto"
                            disabled={isExporting || samples.length === 0}
                          >
                            {isExporting ? (
                              <RotateCw className="mr-2 size-4 animate-spin" />
                            ) : (
                              <Download className="mr-2 size-4" />
                            )}
                            Export
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={handleSamplesExportCSV}>
                            <Download className="mr-2 size-4" />
                            <span>Export as CSV</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={handleSamplesExportExcel}>
                            <FileSpreadsheet className="mr-2 size-4" />
                            <span>Export as Excel (.xlsx)</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}

                    {/* 5. Delete Saved */}
                    {savedSamplesCount > 0 && (
                      <Button
                        onClick={async () => {
                          // Show a confirmation dialog
                          if (
                            !window.confirm(
                              "Are you sure you want to permanently delete all saved samples? This action cannot be undone."
                            )
                          ) {
                            return
                          }

                          try {
                            // Show loading state
                            setIsLoadingSamples(true)

                            // Call the server action to delete saved samples
                            const result =
                              await deleteSavedSamplesWorkflowAction(
                                optimization.optimizerId
                              )

                            if (result.isSuccess) {
                              // Update the saved samples count
                              setSavedSamplesCount(0)
                              setHasSavedSamples(false)

                              // Trigger a refresh of the saved samples count
                              setRefreshSavedSamplesCount(prev => prev + 1)

                              // Show success message
                              toast({
                                title: "Saved samples deleted",
                                description: `Successfully deleted ${result.data?.count || 0} saved samples`
                              })

                              // Clear any loaded samples if they exist
                              if (
                                samples.length > 0 &&
                                activeTab === "samples"
                              ) {
                                setSamples([])
                              }
                            } else {
                              // Show error message
                              toast({
                                title: "Error",
                                description: `Failed to delete saved samples: ${result.message}`,
                                variant: "destructive"
                              })
                            }
                          } catch (error) {
                            console.error(
                              "Error deleting saved samples:",
                              error
                            )
                            toast({
                              title: "Error",
                              description:
                                "Failed to delete saved samples. Please try again.",
                              variant: "destructive"
                            })
                          } finally {
                            setIsLoadingSamples(false)
                          }
                        }}
                        disabled={isGeneratingSamples || isLoadingSamples}
                        variant="outline"
                        className="w-full border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 sm:w-auto"
                      >
                        {isLoadingSamples ? (
                          <Loader2 className="mr-2 size-4 animate-spin" />
                        ) : (
                          <Trash2 className="mr-2 size-4" />
                        )}
                        Delete Saved
                      </Button>
                    )}

                    {/* 6. Clear All */}
                    {samples.length > 0 && (
                      <Button
                        variant="outline"
                        className="w-full border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 sm:w-auto"
                        onClick={() => {
                          // Clear all samples and related data
                          setSamples([])
                          setDiscretizationTransparency(null)
                          setConstraintViolations(undefined)
                          setFeasibleSamples(undefined)
                          setTotalAttempts(undefined)
                          toast({
                            title: "Cleared",
                            description: "All samples have been cleared."
                          })
                        }}
                      >
                        <X className="mr-2 size-4" />
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {samples.length > 0 ? (
                <div className="mt-6 space-y-6">
                  {/* Constraint Violation Notification */}
                  <ConstraintViolationNotification
                    constraintViolations={constraintViolations}
                    feasibleSamples={feasibleSamples}
                    totalAttempts={totalAttempts}
                    requestedSamples={samples.length}
                    hasConstraints={(() => {
                      const config = optimization.config as any
                      return config.constraints && config.constraints.length > 0
                    })()}
                  />

                  {/* Sampling Mode Indicator */}
                  {(() => {
                    const config = optimization.config as any
                    const hasConstraints =
                      config.constraints && config.constraints.length > 0

                    if (hasConstraints) {
                      return (
                        <Collapsible
                          open={showSamplingDetails}
                          onOpenChange={setShowSamplingDetails}
                        >
                          <div className="flex items-center space-x-2 rounded-md border border-gray-200 bg-gray-50 px-3 py-2">
                            <svg
                              className={`size-4 ${respectConstraints ? "text-green-600" : "text-orange-600"}`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d={
                                  respectConstraints
                                    ? "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                    : "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                                }
                              />
                            </svg>
                            <span
                              className={`text-sm font-medium ${respectConstraints ? "text-green-800" : "text-orange-800"}`}
                            >
                              {respectConstraints
                                ? "Constraints respected"
                                : "Constraints ignored"}
                            </span>
                            <CollapsibleTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-auto p-1"
                              >
                                <svg
                                  className="size-4 text-gray-500"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                              </Button>
                            </CollapsibleTrigger>
                          </div>
                          <CollapsibleContent className="mt-2">
                            <div
                              className={`rounded-md border p-3 text-sm ${respectConstraints ? "border-green-200 bg-green-50 text-green-700" : "border-orange-200 bg-orange-50 text-orange-700"}`}
                            >
                              {respectConstraints
                                ? `Samples were generated using ${samplingStrategy} with constraint validation. All ${config.constraints.length} constraint(s) were respected.`
                                : `Samples were generated using ${samplingStrategy} within parameter bounds only. ${config.constraints.length} constraint(s) were ignored.`}

                              {/* Show sampling efficiency if available */}
                              {(() => {
                                const lastResult =
                                  samples.length > 0
                                    ? (samples as any).lastSamplingResult
                                    : null
                                if (
                                  lastResult?.sampling_efficiency !== undefined
                                ) {
                                  const efficiency = (
                                    lastResult.sampling_efficiency * 100
                                  ).toFixed(1)
                                  return (
                                    <div className="mt-2 text-xs opacity-75">
                                      Sampling efficiency: {efficiency}% (
                                      {lastResult.feasible_samples || 0}{" "}
                                      feasible out of{" "}
                                      {lastResult.total_attempts || 0} attempts)
                                    </div>
                                  )
                                }
                                return null
                              })()}

                              {/* Show warnings if available */}
                              {(() => {
                                const lastResult =
                                  samples.length > 0
                                    ? (samples as any).lastSamplingResult
                                    : null
                                if (
                                  lastResult?.warnings &&
                                  lastResult.warnings.length > 0
                                ) {
                                  return (
                                    <div className="mt-2 space-y-1">
                                      {lastResult.warnings.map(
                                        (warning: string, index: number) => (
                                          <div
                                            key={index}
                                            className="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-700"
                                          >
                                            ⚠️ {warning}
                                          </div>
                                        )
                                      )}
                                    </div>
                                  )
                                }
                                return null
                              })()}
                            </div>
                          </CollapsibleContent>
                        </Collapsible>
                      )
                    }
                    return null
                  })()}

                  {/* Discretization Transparency */}
                  <DiscretizationTransparency
                    discretizationInfo={discretizationTransparency}
                  />

                  {/* Submit All button for batch submissions */}
                  {samples.length > 1 && (
                    <div className="mb-4 flex justify-end">
                      <Button
                        variant="default"
                        size="lg"
                        className="bg-green-600 hover:bg-green-700"
                        onClick={submitSamples}
                        disabled={
                          isAddingMeasurement ||
                          samples.filter((_, index) =>
                            hasAllTargetValues(index)
                          ).length === 0
                        }
                      >
                        {isAddingMeasurement ? (
                          <>
                            <Loader2 className="mr-2 size-5 animate-spin" />
                            {batchProgress.total > 0 &&
                              `${batchProgress.current}/${batchProgress.total}`}
                          </>
                        ) : (
                          <Check className="mr-2 size-5" />
                        )}
                        {isAddingMeasurement && batchProgress.total > 0
                          ? "Submitting..."
                          : "Submit All Experiments"}
                      </Button>
                    </div>
                  )}

                  {/* Samples table with target value inputs */}
                  <div data-demo="samples-table">
                    {samples.map((sample, index) => (
                      <Card
                        key={index}
                        className={`border-l-4 ${
                          submittedSuggestions[index]
                            ? "border-l-green-500 dark:border-l-green-400"
                            : hasAllTargetValues(index)
                              ? "border-l-blue-500 dark:border-l-blue-400"
                              : "border-l-amber-500 dark:border-l-amber-400"
                        }`}
                      >
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="flex items-center text-lg">
                              <span>Sample #{index + 1}</span>
                              <span className="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                <svg
                                  className="mr-1 size-3"
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <rect
                                    x="3"
                                    y="3"
                                    width="18"
                                    height="18"
                                    rx="2"
                                  />
                                  <circle cx="8.5" cy="8.5" r="1.5" />
                                  <circle cx="15.5" cy="8.5" r="1.5" />
                                  <circle cx="8.5" cy="15.5" r="1.5" />
                                  <circle cx="15.5" cy="15.5" r="1.5" />
                                </svg>
                                <span>{sample._samplingMethod || "LHS"}</span>
                              </span>
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {submittedSuggestions[index] ? (
                                <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                                  Submitted
                                </span>
                              ) : hasAllTargetValues(index) ? (
                                <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                                  Ready to submit
                                </span>
                              ) : (
                                <span className="rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-800">
                                  Needs target values
                                </span>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="text-muted-foreground size-8 hover:text-red-500"
                                onClick={e => {
                                  e.stopPropagation()
                                  // Remove this sample from the list
                                  setSamples(prev =>
                                    prev.filter((_, i) => i !== index)
                                  )
                                  toast({
                                    title: "Sample removed",
                                    description: `Sample #${index + 1} has been removed.`
                                  })
                                }}
                              >
                                <Trash2 className="size-4" />
                                <span className="sr-only">Delete sample</span>
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                            {Object.entries(sample)
                              .filter(([key]) => !key.startsWith("_"))
                              .map(([key, value]) => (
                                <div key={key} className="space-y-1.5">
                                  <Label htmlFor={`param-${index}-${key}`}>
                                    {key}
                                  </Label>
                                  <Input
                                    id={`param-${index}-${key}`}
                                    value={
                                      typeof value === "number"
                                        ? value.toFixed(value % 1 === 0 ? 0 : 2)
                                        : String(value)
                                    }
                                    readOnly
                                    className="bg-muted"
                                  />
                                </div>
                              ))}
                            {/* Show target inputs based on whether it's multi-target or not */}
                            {getTargetConfigs().map(
                              (target: any, targetIndex: number) => (
                                <div
                                  key={`${index}-${target.name}`}
                                  className="space-y-1.5"
                                >
                                  <Label
                                    htmlFor={`sample-result-${index}-${targetIndex}`}
                                    className="flex items-center"
                                  >
                                    {target.name}
                                    {target.mode === "MAX" ? (
                                      <ArrowUp className="ml-1 size-4 text-green-500" />
                                    ) : (
                                      <ArrowDown className="ml-1 size-4 text-green-500" />
                                    )}
                                  </Label>
                                  <div className="space-y-1">
                                    <Input
                                      id={`sample-result-${index}-${targetIndex}`}
                                      placeholder={`25.00`}
                                      type="number"
                                      step="0.01"
                                      value={
                                        targetValues[index]?.[target.name] || ""
                                      }
                                      onChange={e =>
                                        handleTargetValueChange(
                                          index,
                                          target.name,
                                          e.target.value
                                        )
                                      }
                                      onBlur={e =>
                                        updateSampleTargetValue(
                                          index,
                                          target.name,
                                          e.target.value
                                        )
                                      }
                                      className={
                                        !targetValues[index]?.[target.name] &&
                                        !submittedSuggestions[index]
                                          ? "border-amber-300 focus:ring-amber-500"
                                          : ""
                                      }
                                      autoFocus={
                                        index === 0 &&
                                        targetIndex === 0 &&
                                        !submittedSuggestions[index] &&
                                        samples.length > 0
                                      }
                                      disabled={
                                        submittedSuggestions[index] ||
                                        isAddingMeasurement
                                      }
                                    />
                                    <p className="text-muted-foreground text-xs">
                                      Use decimal point (e.g., 25.08)
                                    </p>
                                  </div>
                                </div>
                              )
                            )}
                          </div>
                        </CardContent>
                        <CardFooter className="justify-end space-x-2">
                          <Button
                            onClick={async () => {
                              // Submit this individual sample
                              const sampleTargetValues =
                                targetValues[index] || {}
                              setIsAddingMeasurement(true)

                              try {
                                if (isMultiTarget()) {
                                  // For multi-target, validate all target values
                                  const targetConfigs = getTargetConfigs()
                                  const missingTargets = targetConfigs.filter(
                                    (target: any) =>
                                      !sampleTargetValues[target.name] ||
                                      sampleTargetValues[target.name].trim() ===
                                        ""
                                  )

                                  if (missingTargets.length > 0) {
                                    toast({
                                      title: "Missing target values",
                                      description: `Please enter values for all targets: ${missingTargets.map((t: any) => t.name).join(", ")}.`,
                                      variant: "destructive"
                                    })
                                    setIsAddingMeasurement(false)
                                    return
                                  }

                                  // Parse and validate all target values
                                  const parsedTargetValues: Record<
                                    string,
                                    number
                                  > = {}
                                  let hasInvalidValue = false

                                  for (const target of targetConfigs as any[]) {
                                    const parsedValue = parseFloat(
                                      sampleTargetValues[target.name]
                                    )
                                    if (isNaN(parsedValue)) {
                                      toast({
                                        title: "Invalid target value",
                                        description: `Please enter a valid number for ${target.name}`,
                                        variant: "destructive"
                                      })
                                      hasInvalidValue = true
                                      break
                                    }
                                    parsedTargetValues[target.name] =
                                      parsedValue
                                  }

                                  if (hasInvalidValue) {
                                    setIsAddingMeasurement(false)
                                    return
                                  }

                                  // Save the sample first if it doesn't have a valid UUID
                                  if (!isValidUUID(sample._sampleId)) {
                                    console.log(
                                      "[FRONTEND] Saving sample before submission..."
                                    )
                                    const saveResult =
                                      await saveSamplesWorkflowAction(
                                        optimization.optimizerId,
                                        [sample],
                                        sample._batchId || `${Date.now()}`
                                      )

                                    if (!saveResult.isSuccess) {
                                      toast({
                                        title: "Error saving sample",
                                        description:
                                          saveResult.message ||
                                          "Failed to save sample before submission.",
                                        variant: "destructive"
                                      })
                                      setIsAddingMeasurement(false)
                                      return
                                    }

                                    // Update the sample with the saved version
                                    if (
                                      saveResult.data?.savedSamples?.length > 0
                                    ) {
                                      sample = saveResult.data.savedSamples[0]
                                      // Update the samples array
                                      setSamples(prev => {
                                        const newSamples = [...prev]
                                        newSamples[index] = sample
                                        return newSamples
                                      })
                                    }
                                  }

                                  // Use addMeasurement to submit the sample
                                  await addMeasurement(
                                    sample,
                                    parsedTargetValues,
                                    false
                                  )

                                  // Mark as submitted
                                  setSubmittedSuggestions(prev => ({
                                    ...prev,
                                    [index]: true
                                  }))
                                } else {
                                  // For single target
                                  const targetName = getTargetConfigs()[0].name
                                  const value = parseFloat(
                                    sampleTargetValues[targetName] || ""
                                  )
                                  if (isNaN(value)) {
                                    toast({
                                      title: "Invalid value",
                                      description:
                                        "Please enter a valid number",
                                      variant: "destructive"
                                    })
                                    setIsAddingMeasurement(false)
                                    return
                                  }

                                  // Save the sample first if it doesn't have a valid UUID
                                  if (!isValidUUID(sample._sampleId)) {
                                    console.log(
                                      "[FRONTEND] Saving sample before submission..."
                                    )
                                    const saveResult =
                                      await saveSamplesWorkflowAction(
                                        optimization.optimizerId,
                                        [sample],
                                        sample._batchId || `${Date.now()}`
                                      )

                                    if (!saveResult.isSuccess) {
                                      toast({
                                        title: "Error saving sample",
                                        description:
                                          saveResult.message ||
                                          "Failed to save sample before submission.",
                                        variant: "destructive"
                                      })
                                      setIsAddingMeasurement(false)
                                      return
                                    }

                                    // Update the sample with the saved version
                                    if (
                                      saveResult.data?.savedSamples?.length > 0
                                    ) {
                                      sample = saveResult.data.savedSamples[0]
                                      // Update the samples array
                                      setSamples(prev => {
                                        const newSamples = [...prev]
                                        newSamples[index] = sample
                                        return newSamples
                                      })
                                    }
                                  }

                                  // Use addMeasurement to submit the sample
                                  await addMeasurement(sample, value, false)

                                  // Mark as submitted
                                  setSubmittedSuggestions(prev => ({
                                    ...prev,
                                    [index]: true
                                  }))
                                }
                              } catch (error) {
                                console.error("Error submitting sample:", error)
                                toast({
                                  title: "Error",
                                  description:
                                    "Failed to submit sample. Please try again.",
                                  variant: "destructive"
                                })
                              } finally {
                                setIsAddingMeasurement(false)
                              }
                            }}
                            disabled={
                              isAddingMeasurement ||
                              !hasAllTargetValues(index) ||
                              submittedSuggestions[index]
                            }
                          >
                            {isAddingMeasurement ? (
                              <Loader2 className="mr-2 size-4 animate-spin" />
                            ) : submittedSuggestions[index] ? (
                              <Check className="mr-2 size-4" />
                            ) : (
                              <Check className="mr-2 size-4" />
                            )}
                            {submittedSuggestions[index]
                              ? "Submitted"
                              : "Submit Measurement"}
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="mt-8 text-center">
                  {isGeneratingSamples || isLoadingSamples ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <Loader2 className="text-muted-foreground mb-4 size-12 animate-spin" />
                      <p className="text-muted-foreground">
                        {isGeneratingSamples
                          ? "Generating samples..."
                          : "Loading saved samples..."}
                      </p>
                    </div>
                  ) : (
                    <div className="text-muted-foreground">
                      <p className="mb-4">No samples generated yet.</p>
                      <p>
                        Click "Generate Samples" to create sample points for
                        your parameter space.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Alert>
            <svg
              className="size-5 text-blue-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" />
              <circle cx="8.5" cy="8.5" r="1.5" />
              <circle cx="15.5" cy="8.5" r="1.5" />
              <circle cx="8.5" cy="15.5" r="1.5" />
              <circle cx="15.5" cy="15.5" r="1.5" />
            </svg>
            <AlertTitle>Using Sample Generation</AlertTitle>
            <AlertDescription>
              <p className="mb-2">
                Sample generation creates points within your parameter space
                that can be used for initial experiments or exploration. These
                samples provide the foundation for the AI to learn from and make
                better suggestions.
              </p>
              <p className="text-muted-foreground text-sm">
                <strong>Sample Count:</strong> You can generate between 1 and
                100 samples at once. For initial exploration, 5-10 samples is
                typically sufficient to get started.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Latin Hypercube Sampling (LHS):</strong> Provides better
                coverage of the parameter space than random sampling by ensuring
                samples are distributed across the entire range of each
                parameter. This is the recommended method for most cases.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Random Sampling:</strong> Generates completely random
                points within the parameter space. This can be useful for
                certain applications but may not provide as even coverage as LHS
                or Sobol sequences.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Sobol Sequences:</strong> Uses quasi-random
                low-discrepancy sequences that provide superior space-filling
                properties compared to both LHS and random sampling. Sobol
                sequences offer more uniform coverage and better convergence,
                making them ideal for high-dimensional optimization problems.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Target Values:</strong> Enter numerical values using a
                decimal point (e.g., 25.08).
              </p>
            </AlertDescription>
          </Alert>
        </TabsContent>

        <TabsContent value="upload" className="space-y-6">
          <DatasetUpload
            optimizationId={optimization.optimizerId}
            optimizationName={optimization.name}
            onUploadSuccess={(uploadedSamples, batchId) => {
              // Handle successful upload - treat as manual experiments
              // Convert uploaded samples to manual experiments format
              const manualExperimentsFromUpload = uploadedSamples.map(
                sample => {
                  // Extract parameters (exclude internal fields)
                  const params: Record<string, any> = {}
                  Object.entries(sample).forEach(([key, value]) => {
                    if (!key.startsWith("_")) {
                      params[key] = value
                    }
                  })
                  return params
                }
              )

              // Set as manual experiments
              setManualExperiments(manualExperimentsFromUpload)

              // Initialize target values for uploaded experiments
              const uploadTargetValues: Record<
                number,
                Record<string, string>
              > = {}
              uploadedSamples.forEach((sample, index) => {
                if (
                  sample._targetValues &&
                  Object.keys(sample._targetValues).length > 0
                ) {
                  // Use negative indices for manual experiments: -1, -2, -3, etc.
                  const manualIndex = -(index + 1)

                  console.log(
                    `[UPLOAD] Sample ${index} target values:`,
                    sample._targetValues
                  )
                  console.log(`[UPLOAD] Setting at manual index ${manualIndex}`)

                  uploadTargetValues[manualIndex] = Object.entries(
                    sample._targetValues
                  ).reduce((acc: Record<string, string>, [key, value]) => {
                    const stringValue = String(value)
                    console.log(`[UPLOAD] Setting ${key} = ${stringValue}`)
                    acc[key] = stringValue
                    return acc
                  }, {})

                  console.log(
                    `[UPLOAD] Final target values for index ${manualIndex}:`,
                    uploadTargetValues[manualIndex]
                  )
                }
              })

              if (Object.keys(uploadTargetValues).length > 0) {
                console.log(
                  `[UPLOAD] Setting target values:`,
                  uploadTargetValues
                )
                setTargetValues(prev => {
                  const newTargetValues = {
                    ...prev,
                    ...uploadTargetValues
                  }
                  console.log(
                    `[UPLOAD] New target values state:`,
                    newTargetValues
                  )
                  return newTargetValues
                })
              } else {
                console.log(`[UPLOAD] No target values to set`)
              }

              // Switch to manual tab to show uploaded data for review
              setActiveTab("manual")

              toast({
                title: "Upload successful",
                description: `Successfully uploaded ${uploadedSamples.length} experiments. You can now review and submit them in the Manual Experiments tab.`
              })
            }}
          />
        </TabsContent>

        <TabsContent value="suggested" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center">
                <CardTitle className="flex items-center">
                  <span>AI-Powered Experiment Recommendations</span>
                  <span className="ml-2 inline-flex items-center rounded-full bg-purple-100 px-2 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    <Sparkles className="mr-1 size-3" />
                    <span>Smart AI</span>
                  </span>
                </CardTitle>
              </div>
              <CardDescription className="mt-1 flex items-center">
                <Brain className="mr-2 size-4 text-purple-500" />
                <span>
                  Get intelligent experiment suggestions using Bayesian
                  optimization
                </span>
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <h3 className="text-sm font-medium">Request Parameters</h3>
                    <p className="text-muted-foreground text-xs">
                      Get suggestions for your next experiments
                    </p>
                  </div>
                </div>

                <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-end">
                  <div className="w-full sm:w-64">
                    <Label htmlFor="batchSize" className="text-sm">
                      Number of Suggestions
                    </Label>
                    <div className="mt-1 flex items-center gap-2">
                      <Input
                        id="batchSize"
                        type="number"
                        min="1"
                        max="100"
                        defaultValue="1"
                        className="w-full"
                        onChange={e => {
                          // Ensure value is between 1 and 100
                          const value = parseInt(e.target.value)
                          if (value < 1) e.target.value = "1"
                          if (value > 100) e.target.value = "100"
                        }}
                      />
                      <div className="text-muted-foreground text-xs">
                        (1-100)
                      </div>
                    </div>
                  </div>

                  <div className="flex w-full flex-wrap items-center gap-2 sm:w-auto">
                    {/* 1. Get Suggestions */}
                    <Button
                      className="w-full sm:w-auto"
                      data-demo="get-suggestions-button"
                      onClick={() => {
                        const batchSizeInput = document.getElementById(
                          "batchSize"
                        ) as HTMLInputElement
                        const batchSize = parseInt(batchSizeInput.value) || 1
                        getSuggestions(batchSize)
                      }}
                      disabled={
                        isLoadingSuggestions ||
                        (optimization as any).measurementCount === 0
                      }
                      title={
                        subscriptionTier === "free"
                          ? "This feature is only available for trial and premium users. Please upgrade your subscription to use AI-powered suggestions."
                          : (optimization as any).measurementCount === 0
                            ? "You need to submit some initial data before getting AI suggestions. Go to the Sample Generation tab first."
                            : "Get AI-powered suggestions for your next experiments"
                      }
                    >
                      {isLoadingSuggestions && loadingMode === "new" ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <>
                          <Sparkles className="mr-2 size-4" />
                          {subscriptionTier === "free" && (
                            <span className="ml-1 inline-flex size-4 items-center justify-center rounded-full bg-gray-200 text-[10px] font-bold text-gray-700 dark:bg-gray-700 dark:text-gray-200">
                              P
                            </span>
                          )}
                        </>
                      )}
                      Get Suggestions
                    </Button>

                    {/* 2. Save Suggestions */}
                    {suggestions.length > 0 && (
                      <Button
                        onClick={saveSuggestions}
                        disabled={isLoadingSuggestions || isSavingSuggestions}
                        variant="secondary"
                        className="w-full sm:w-auto"
                      >
                        {isSavingSuggestions ? (
                          <>
                            <Loader2 className="mr-2 size-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 size-4" />
                            Save Suggestions
                          </>
                        )}
                      </Button>
                    )}

                    {/* 3. Load Saved */}
                    <Button
                      onClick={loadSavedSuggestions}
                      disabled={isLoadingSuggestions}
                      variant="outline"
                      className="w-full sm:w-auto"
                    >
                      {isLoadingSuggestions && loadingMode === "saved" ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <Database className="mr-2 size-4" />
                      )}
                      Load Saved
                      {savedSuggestionsCount > 0 && (
                        <Badge variant="secondary" className="ml-1">
                          {savedSuggestionsCount}
                        </Badge>
                      )}
                    </Button>

                    {/* 4. Export */}
                    {suggestions.length > 0 && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full sm:w-auto"
                            disabled={isExporting || suggestions.length === 0}
                          >
                            {isExporting ? (
                              <RotateCw className="mr-2 size-4 animate-spin" />
                            ) : (
                              <Download className="mr-2 size-4" />
                            )}
                            Export
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={handleSuggestionsExportCSV}
                          >
                            <Download className="mr-2 size-4" />
                            <span>Export as CSV</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={handleSuggestionsExportExcel}
                          >
                            <FileSpreadsheet className="mr-2 size-4" />
                            <span>Export as Excel (.xlsx)</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}

                    {/* 5. Delete Saved */}
                    {savedSuggestionsCount > 0 && (
                      <Button
                        onClick={deleteSavedSuggestions}
                        disabled={isLoadingSuggestions}
                        variant="outline"
                        className="w-full border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 sm:w-auto"
                      >
                        <Trash2 className="mr-2 size-4" />
                        Delete Saved
                      </Button>
                    )}

                    {/* 6. Clear All */}
                    {suggestions.length > 0 && (
                      <Button
                        variant="outline"
                        className="w-full border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 sm:w-auto"
                        onClick={() => {
                          // Clear all suggestions and target values
                          setSuggestions([])
                          setTargetValues({})
                          setSubmittedSuggestions({})
                          setCurrentBatchId(null)
                          toast({
                            title: "Cleared",
                            description: "All suggestions have been cleared."
                          })
                        }}
                      >
                        <X className="mr-2 size-4" />
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {suggestions.length > 0 ? (
                <div
                  className="mt-6 space-y-6"
                  data-demo="suggestions-container"
                >
                  {/* Action buttons for suggestions */}
                  <div className="mb-4 flex justify-end gap-2">
                    {/* Submit All button for batch submissions */}
                    {suggestions.length > 1 && (
                      <Button
                        variant="default"
                        size="lg"
                        className="bg-green-600 hover:bg-green-700"
                        onClick={handleSubmitAllSuggestions}
                        disabled={
                          isAddingMeasurement ||
                          Object.keys(targetValues).length === 0
                        }
                      >
                        {isAddingMeasurement ? (
                          <>
                            <Loader2 className="mr-2 size-5 animate-spin" />
                            {batchProgress.total > 0 &&
                              `${batchProgress.current}/${batchProgress.total}`}
                          </>
                        ) : (
                          <Check className="mr-2 size-5" />
                        )}
                        {isAddingMeasurement && batchProgress.total > 0
                          ? "Submitting..."
                          : "Submit All Experiments"}
                      </Button>
                    )}
                  </div>
                  <div data-demo="suggestions-list">
                    {suggestions.map((suggestion, index) => (
                      <Card
                        key={index}
                        className={`border-l-4 ${
                          submittedSuggestions[index]
                            ? "border-l-green-500 dark:border-l-green-400"
                            : hasAllTargetValues(index)
                              ? "border-l-blue-500 dark:border-l-blue-400"
                              : "border-l-amber-500 dark:border-l-amber-400"
                        }`}
                      >
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="flex items-center text-lg">
                              <span>Suggested Experiment #{index + 1}</span>
                              <span className="ml-2 inline-flex items-center rounded-full bg-purple-100 px-2 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                <Sparkles className="mr-1 size-3" />
                                <span>AI</span>
                              </span>
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {submittedSuggestions[index] ? (
                                <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                                  Submitted
                                </span>
                              ) : hasAllTargetValues(index) ? (
                                <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                                  Ready to submit
                                </span>
                              ) : (
                                <span className="rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-800">
                                  Needs target values
                                </span>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="text-muted-foreground size-8 hover:text-red-500"
                                onClick={e => {
                                  e.stopPropagation()
                                  handleDeleteSuggestion(index)
                                }}
                              >
                                <Trash2 className="size-4" />
                                <span className="sr-only">
                                  Delete suggestion
                                </span>
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                            {Object.entries(suggestion)
                              .filter(([key]) => !key.startsWith("_"))
                              .map(([key, value]) => (
                                <div key={key} className="space-y-1.5">
                                  <Label htmlFor={`param-${index}-${key}`}>
                                    {key}
                                  </Label>
                                  <Input
                                    id={`param-${index}-${key}`}
                                    value={
                                      typeof value === "number"
                                        ? value.toFixed(value % 1 === 0 ? 0 : 2)
                                        : String(value)
                                    }
                                    readOnly
                                    className="bg-muted"
                                  />
                                </div>
                              ))}
                            {/* Show target inputs based on whether it's multi-target or not */}
                            {getTargetConfigs().map(
                              (target: any, targetIndex: number) => (
                                <div
                                  key={`${index}-${target.name}`}
                                  className="space-y-1.5"
                                >
                                  <Label
                                    htmlFor={`result-${index}-${targetIndex}`}
                                    className="flex items-center"
                                  >
                                    {target.name}
                                    {target.mode === "MAX" ? (
                                      <ArrowUp className="ml-1 size-4 text-green-500" />
                                    ) : (
                                      <ArrowDown className="ml-1 size-4 text-green-500" />
                                    )}
                                  </Label>
                                  <div className="space-y-1">
                                    <Input
                                      id={`result-${index}-${targetIndex}`}
                                      data-demo={`target-input-${index}`}
                                      placeholder={`25.00`}
                                      type="number"
                                      step="0.01"
                                      value={
                                        targetValues[index]?.[target.name] || ""
                                      }
                                      onChange={e =>
                                        handleTargetValueChange(
                                          index,
                                          target.name,
                                          e.target.value
                                        )
                                      }
                                      onFocus={() =>
                                        setActiveSuggestionIndex(index)
                                      }
                                      className={
                                        !targetValues[index]?.[target.name] &&
                                        !submittedSuggestions[index]
                                          ? "border-amber-300 focus:ring-amber-500"
                                          : ""
                                      }
                                      autoFocus={
                                        index === 0 &&
                                        targetIndex === 0 &&
                                        !submittedSuggestions[index] &&
                                        suggestions.length > 0
                                      }
                                      disabled={
                                        submittedSuggestions[index] ||
                                        isAddingMeasurement
                                      }
                                    />
                                    <p className="text-muted-foreground text-xs">
                                      Use decimal point (e.g., 25.08)
                                    </p>
                                  </div>
                                </div>
                              )
                            )}
                          </div>
                        </CardContent>
                        <CardFooter className="justify-end space-x-2">
                          <Button
                            data-demo={`submit-suggestion-${index}`}
                            onClick={() => {
                              // Set the active suggestion index to this card
                              setActiveSuggestionIndex(index)

                              // Get the target values for this suggestion
                              const suggestionTargetValues =
                                targetValues[index] || {}

                              if (isMultiTarget()) {
                                // For multi-target, validate all target values
                                const targetConfigs = getTargetConfigs()
                                const missingTargets = targetConfigs.filter(
                                  (target: any) =>
                                    !suggestionTargetValues[target.name] ||
                                    suggestionTargetValues[
                                      target.name
                                    ].trim() === ""
                                )

                                if (missingTargets.length > 0) {
                                  toast({
                                    title: "Missing target values",
                                    description: `Please enter values for all targets: ${missingTargets.map((t: any) => t.name).join(", ")}.`,
                                    variant: "destructive"
                                  })
                                  return
                                }

                                // Parse and validate all target values
                                const parsedTargetValues: Record<
                                  string,
                                  number
                                > = {}
                                let hasInvalidValue = false

                                for (const target of targetConfigs as any[]) {
                                  const parsedValue = parseFloat(
                                    suggestionTargetValues[target.name]
                                  )
                                  if (isNaN(parsedValue)) {
                                    toast({
                                      title: "Invalid target value",
                                      description: `Please enter a valid number for ${target.name}`,
                                      variant: "destructive"
                                    })
                                    hasInvalidValue = true
                                    break
                                  }
                                  parsedTargetValues[target.name] = parsedValue
                                }

                                if (!hasInvalidValue) {
                                  addMeasurement(
                                    suggestion,
                                    parsedTargetValues,
                                    true
                                  )
                                }
                              } else {
                                // For single target
                                const targetName = getTargetConfigs()[0].name
                                const value = parseFloat(
                                  suggestionTargetValues[targetName] || ""
                                )
                                if (!isNaN(value)) {
                                  addMeasurement(suggestion, value, true)
                                } else {
                                  toast({
                                    title: "Invalid value",
                                    description: "Please enter a valid number",
                                    variant: "destructive"
                                  })
                                }
                              }
                            }}
                            disabled={
                              isAddingMeasurement ||
                              !hasAllTargetValues(index) ||
                              submittedSuggestions[index]
                            }
                          >
                            {isAddingMeasurement ? (
                              <Loader2 className="mr-2 size-4 animate-spin" />
                            ) : submittedSuggestions[index] ? (
                              <Check className="mr-2 size-4" />
                            ) : (
                              <Check className="mr-2 size-4" />
                            )}
                            {submittedSuggestions[index]
                              ? "Submitted"
                              : "Submit Measurement"}
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="mt-8 text-center">
                  {isLoadingSuggestions ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <Loader2 className="text-muted-foreground mb-4 size-12 animate-spin" />
                      <p className="text-muted-foreground">
                        {loadingMode === "saved"
                          ? "Loading saved suggestions..."
                          : "Generating new suggestions..."}
                      </p>
                    </div>
                  ) : (
                    <div className="text-muted-foreground">
                      <p className="mb-4">No suggestions loaded yet.</p>
                      <p className="mb-2">
                        Click "Get Suggestions" to receive recommendations for
                        your next experiments.
                      </p>
                      {hasSavedSuggestions && (
                        <p className="mb-2">
                          Or click "Load Saved" to retrieve your{" "}
                          {savedSuggestionsCount} previously saved suggestion
                          {savedSuggestionsCount !== 1 ? "s" : ""}.
                        </p>
                      )}
                      {(optimization as any).measurementCount === 0 && (
                        <div className="mt-6 rounded-md bg-amber-50 p-4 text-left dark:bg-amber-950/50">
                          <div className="flex">
                            <AlertCircle className="mr-3 size-5 text-amber-600 dark:text-amber-400" />
                            <div>
                              <p className="font-medium text-amber-800 dark:text-amber-300">
                                No data available yet
                              </p>
                              <p className="mt-1 text-sm text-amber-700 dark:text-amber-400">
                                To get meaningful AI suggestions, you need to
                                provide some initial data first. Go to the{" "}
                                <button
                                  className="font-medium text-amber-800 underline dark:text-amber-300"
                                  onClick={() => setActiveTab("samples")}
                                >
                                  Sample Generation
                                </button>{" "}
                                tab to create and submit some initial samples.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Alert>
            <Brain className="size-5" />
            <AlertTitle>Using AI suggestions</AlertTitle>
            <AlertDescription>
              <p className="mb-2">
                The optimizer uses Bayesian optimization to suggest the most
                promising experiments based on your results so far. Each new
                measurement helps the AI learn and improve its suggestions.
              </p>
              <p className="text-muted-foreground text-sm">
                <strong>Batch Size:</strong> You can request between 1 and 100
                suggestions at once. Smaller batches (1-5) are more focused on
                immediate improvement, while larger batches provide more diverse
                exploration options. For most cases, 1-10 suggestions is
                optimal.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Target Values:</strong> Enter numerical values using a
                decimal point (e.g., 25.08).
              </p>
            </AlertDescription>
          </Alert>
        </TabsContent>

        <TabsContent
          value="manual"
          className="space-y-6"
          data-demo="manual-entry-section"
        >
          <Card>
            <CardHeader>
              <CardTitle>Manual Experiment Entry</CardTitle>
              <CardDescription>
                Submit your own experiment parameters and results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <h3 className="text-sm font-medium">Experiment Controls</h3>
                    <p className="text-muted-foreground text-xs">
                      Add and manage your manual experiments
                    </p>
                  </div>

                  {/* Export dropdown */}
                  {manualExperiments.length > 0 && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={
                            isExporting || manualExperiments.length === 0
                          }
                        >
                          {isExporting ? (
                            <RotateCw className="mr-2 size-4 animate-spin" />
                          ) : (
                            <Download className="mr-2 size-4" />
                          )}
                          Export
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={handleManualExperimentsExportCSV}
                        >
                          <Download className="mr-2 size-4" />
                          <span>Export as CSV</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={handleManualExperimentsExportExcel}
                        >
                          <FileSpreadsheet className="mr-2 size-4" />
                          <span>Export as Excel (.xlsx)</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>

                <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-end">
                  <div className="w-full sm:w-64">
                    <Label htmlFor="experimentCount" className="text-sm">
                      Number of Experiments
                    </Label>
                    <div className="mt-1 flex items-center gap-2">
                      <Input
                        id="experimentCount"
                        type="number"
                        min="1"
                        max="10"
                        defaultValue="1"
                        className="w-full"
                        onChange={e => {
                          // Ensure value is between 1 and 10
                          let value = parseInt(e.target.value)
                          if (isNaN(value)) value = 1
                          if (value < 1) value = 1
                          if (value > 10) value = 10

                          // Update the input value
                          e.target.value = value.toString()
                        }}
                      />
                      <div className="text-muted-foreground text-xs">
                        (1-10)
                      </div>
                    </div>
                  </div>

                  <div className="flex w-full items-center gap-2 sm:w-auto">
                    <Button
                      variant="default"
                      className="w-full sm:w-auto"
                      onClick={() => {
                        const countInput = document.getElementById(
                          "experimentCount"
                        ) as HTMLInputElement
                        const targetCount = parseInt(countInput.value) || 1
                        const currentCount = manualExperiments.length

                        // Calculate how many experiments to add
                        const toAdd = Math.max(
                          0,
                          Math.min(
                            targetCount - currentCount,
                            10 - currentCount
                          )
                        )

                        // Create initial parameters for a new experiment with valid values
                        const createInitialParams = () => {
                          return (optimization.config as any).parameters.reduce(
                            (acc: Record<string, any>, param: any) => {
                              if (param.type === "NumericalDiscrete") {
                                // Ensure we're using a valid value from the allowed values
                                if (param.values && param.values.length > 0) {
                                  // Convert string values to numbers for numerical parameters
                                  acc[param.name] =
                                    typeof param.values[0] === "string"
                                      ? parseFloat(param.values[0])
                                      : param.values[0]
                                } else {
                                  acc[param.name] = 0
                                }
                              } else if (param.type === "NumericalContinuous") {
                                // Ensure we're within bounds
                                acc[param.name] = param.bounds
                                  ? param.bounds[0]
                                  : 0
                              } else if (
                                param.type === "CategoricalParameter"
                              ) {
                                // Use the first allowed value
                                acc[param.name] =
                                  param.values && param.values.length > 0
                                    ? param.values[0]
                                    : ""
                              }
                              return acc
                            },
                            {} as Record<string, any>
                          )
                        }

                        // Add the required number of experiments with valid initial values
                        if (toAdd > 0) {
                          const newExperiments: Record<string, any>[] = []
                          for (let i = 0; i < toAdd; i++) {
                            newExperiments.push(createInitialParams())
                          }
                          setManualExperiments(prev => [
                            ...prev,
                            ...newExperiments
                          ])
                        }

                        // If we need to remove experiments
                        if (targetCount < currentCount) {
                          // Keep the first targetCount experiments and remove the rest
                          setManualExperiments(prev =>
                            prev.slice(0, targetCount)
                          )

                          // Clear target values for removed experiments
                          setTargetValues(prev => {
                            const updated = { ...prev }
                            Object.keys(updated).forEach(key => {
                              const index = parseInt(key)
                              // Keep indices for experiments we want to keep (-1 for first experiment, -2 for second, etc.)
                              // Remove indices for experiments beyond our target count
                              if (index < -1 && index < -targetCount) {
                                delete updated[index]
                              }
                            })
                            return updated
                          })

                          // Show notification
                          toast({
                            title: "Experiments updated",
                            description: `Reduced to ${targetCount} experiment${targetCount !== 1 ? "s" : ""}.`
                          })
                        } else if (toAdd > 0) {
                          // Show notification for added experiments
                          toast({
                            title: "Experiments updated",
                            description: `Set to ${targetCount} experiment${targetCount !== 1 ? "s" : ""}.`
                          })
                        }
                      }}
                      disabled={isAddingMeasurement}
                    >
                      {isAddingMeasurement ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <Beaker className="mr-2 size-4" />
                      )}
                      Set Experiments
                    </Button>

                    {manualExperiments.length > 1 && (
                      <Button
                        variant="outline"
                        className="w-full border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 sm:w-auto"
                        onClick={() => {
                          // Remove all but the first experiment
                          setManualExperiments(prev => [prev[0]])

                          // Clear target values for removed experiments
                          setTargetValues(prev => {
                            const updated = { ...prev }
                            Object.keys(updated).forEach(key => {
                              const index = parseInt(key)
                              if (index < -1) delete updated[index]
                            })
                            return updated
                          })

                          toast({
                            title: "Cleared",
                            description:
                              "All additional experiments have been cleared."
                          })
                        }}
                        disabled={isAddingMeasurement}
                      >
                        <X className="mr-2 size-4" />
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {/* Submit All button for batch submissions */}
              {manualExperiments.length > 1 && (
                <div className="mb-4 flex justify-end">
                  <Button
                    variant="default"
                    size="lg"
                    className="bg-green-600 hover:bg-green-700"
                    onClick={handleManualSubmit}
                    disabled={
                      isAddingMeasurement ||
                      Object.values(targetValues).every(v => !v)
                    }
                  >
                    {isAddingMeasurement ? (
                      <>
                        <Loader2 className="mr-2 size-5 animate-spin" />
                        {batchProgress.total > 0 &&
                          `${batchProgress.current}/${batchProgress.total}`}
                      </>
                    ) : (
                      <Check className="mr-2 size-5" />
                    )}
                    {isAddingMeasurement && batchProgress.total > 0
                      ? "Submitting..."
                      : "Submit All Experiments"}
                  </Button>
                </div>
              )}

              {/* Manual experiments */}
              <div className="mt-6 space-y-6">
                {manualExperiments.map((experimentParams, experimentIndex) => {
                  // Calculate the correct target value index
                  const targetValueIndex =
                    experimentIndex === 0 ? -1 : -1 - experimentIndex

                  // Check if all target values are filled for this experiment
                  const hasAllTargetsForExperiment = () => {
                    if (!targetValues[targetValueIndex]) return false

                    const targetConfigs = getTargetConfigs()
                    const experimentTargetValues =
                      targetValues[targetValueIndex] || {}

                    return targetConfigs.every(
                      (target: any) =>
                        experimentTargetValues[target.name] &&
                        experimentTargetValues[target.name].trim() !== ""
                    )
                  }

                  return (
                    <Card
                      key={experimentIndex}
                      className={`border-l-4 ${
                        submittedSuggestions[targetValueIndex]
                          ? "border-l-green-500 dark:border-l-green-400"
                          : hasAllTargetsForExperiment()
                            ? "border-l-blue-500 dark:border-l-blue-400"
                            : "border-l-amber-500 dark:border-l-amber-400"
                      }`}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">
                            Manual Experiment #{experimentIndex + 1}
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            {submittedSuggestions[targetValueIndex] ? (
                              <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                                Submitted
                              </span>
                            ) : hasAllTargetsForExperiment() ? (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                                Ready to submit
                              </span>
                            ) : (
                              <span className="rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-800">
                                Needs target values
                              </span>
                            )}
                            {experimentIndex > 0 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="size-7 p-0 text-red-500 hover:bg-red-50 hover:text-red-700"
                                onClick={() =>
                                  removeManualExperiment(experimentIndex)
                                }
                                disabled={isAddingMeasurement}
                              >
                                <X className="size-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                          {(optimization.config as any).parameters.map(
                            (param: any) => (
                              <div
                                key={`${experimentIndex}-${param.name}`}
                                className="space-y-1.5"
                              >
                                <Label
                                  htmlFor={`manual-${experimentIndex}-${param.name}`}
                                >
                                  {param.name}
                                </Label>
                                {/* Use dropdown for both Categorical and NumericalDiscrete parameters with predefined values */}
                                {param.type === "CategoricalParameter" ||
                                (param.type === "NumericalDiscrete" &&
                                  Array.isArray(param.values)) ? (
                                  <select
                                    id={`manual-${experimentIndex}-${param.name}`}
                                    value={experimentParams[param.name] || ""}
                                    onChange={e =>
                                      handleManualParameterChange(
                                        experimentIndex,
                                        param.name,
                                        param.type === "NumericalDiscrete"
                                          ? parseFloat(e.target.value)
                                          : e.target.value,
                                        param.type
                                      )
                                    }
                                    className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    disabled={isAddingMeasurement}
                                  >
                                    <option value="">Select a value</option>
                                    {param.values.map((value: any) => (
                                      <option key={value} value={value}>
                                        {value}
                                      </option>
                                    ))}
                                  </select>
                                ) : (
                                  <Input
                                    id={`manual-${experimentIndex}-${param.name}`}
                                    type={
                                      param.type.startsWith("Numerical")
                                        ? "number"
                                        : "text"
                                    }
                                    step={
                                      param.type.startsWith("Numerical")
                                        ? "0.01"
                                        : undefined
                                    }
                                    min={
                                      param.type === "NumericalContinuous" &&
                                      param.bounds
                                        ? param.bounds[0]
                                        : undefined
                                    }
                                    max={
                                      param.type === "NumericalContinuous" &&
                                      param.bounds
                                        ? param.bounds[1]
                                        : undefined
                                    }
                                    value={experimentParams[param.name] || ""}
                                    onChange={e => {
                                      handleManualParameterChange(
                                        experimentIndex,
                                        param.name,
                                        e.target.value,
                                        param.type
                                      )
                                    }}
                                    placeholder={`Enter ${param.name} value`}
                                    disabled={isAddingMeasurement}
                                  />
                                )}

                                {param.type === "NumericalDiscrete" &&
                                  Array.isArray(param.values) && (
                                    <p className="text-muted-foreground text-xs">
                                      Allowed values: {param.values.join(", ")}
                                    </p>
                                  )}
                                {param.type === "NumericalContinuous" &&
                                  param.bounds && (
                                    <p className="text-muted-foreground text-xs">
                                      Range: {param.bounds[0]} to{" "}
                                      {param.bounds[1]}
                                    </p>
                                  )}
                              </div>
                            )
                          )}

                          {/* Target value inputs */}
                          {getTargetConfigs().map(
                            (target: any, targetIndex: number) => (
                              <div
                                key={`${experimentIndex}-${target.name}`}
                                className="space-y-1.5"
                              >
                                <Label
                                  htmlFor={`manual-${experimentIndex}-target-${targetIndex}`}
                                  className="flex items-center"
                                >
                                  {target.name} Value
                                  {target.mode === "MAX" ? (
                                    <ArrowUp className="ml-1 size-4 text-green-500" />
                                  ) : (
                                    <ArrowDown className="ml-1 size-4 text-green-500" />
                                  )}
                                </Label>
                                <div className="space-y-1">
                                  <Input
                                    id={`manual-${experimentIndex}-target-${targetIndex}`}
                                    type="number"
                                    step="0.01"
                                    value={
                                      targetValues[targetValueIndex]?.[
                                        target.name
                                      ] || ""
                                    }
                                    onChange={e =>
                                      handleTargetValueChange(
                                        targetValueIndex,
                                        target.name,
                                        e.target.value
                                      )
                                    }
                                    placeholder={`25.00`}
                                    className={
                                      !targetValues[targetValueIndex]?.[
                                        target.name
                                      ]
                                        ? "border-amber-300 focus:ring-amber-500"
                                        : ""
                                    }
                                    disabled={isAddingMeasurement}
                                  />
                                  <p className="text-muted-foreground text-xs">
                                    Use decimal point (e.g., 25.08)
                                  </p>
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      </CardContent>
                      <CardFooter className="justify-end space-x-2">
                        <Button
                          onClick={handleManualSubmit}
                          disabled={
                            isAddingMeasurement ||
                            !hasAllTargetsForExperiment() ||
                            submittedSuggestions[targetValueIndex]
                          }
                        >
                          {isAddingMeasurement ? (
                            <Loader2 className="mr-2 size-4 animate-spin" />
                          ) : submittedSuggestions[targetValueIndex] ? (
                            <Check className="mr-2 size-4" />
                          ) : (
                            <Check className="mr-2 size-4" />
                          )}
                          {submittedSuggestions[targetValueIndex]
                            ? "Submitted"
                            : "Submit Measurement"}
                        </Button>
                      </CardFooter>
                    </Card>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          <Alert>
            <Beaker className="size-5" />
            <AlertTitle>Manual measurements</AlertTitle>
            <AlertDescription>
              <p className="mb-2">
                Use this form to enter results from experiments you've conducted
                outside the optimization system. These measurements will be
                incorporated into the model to improve future suggestions.
              </p>
              <p className="text-muted-foreground text-sm">
                <strong>Batch Submission:</strong> You can add multiple
                experiments and submit them as a batch. When submitted together,
                they will be classified as a batch in the History tab.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Target Values:</strong> Enter numerical values using a
                decimal point (e.g., 25.08).
              </p>
            </AlertDescription>
          </Alert>
        </TabsContent>
      </Tabs>

      {/* Upgrade Modal */}
      <UpgradeModal
        isOpen={isUpgradeModalOpen}
        onOpenChange={setIsUpgradeModalOpen}
      />
    </div>
  )
}
