/*
 * This server page displays the Bayesian Optimization tutorial.
 */

"use server"

import { Book<PERSON><PERSON> } from "lucide-react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"
import { BayesianOptimizationIntro } from "@/components/tutorials/bayesian-optimization-intro"

export default async function BayesianOptimizationIntroPage() {
  return (
    <div className="container mx-auto max-w-4xl px-4 py-12">
      <div className="mb-8">
        <Link href="/tutorials">
          <Button variant="ghost" className="flex items-center gap-2 pl-0">
            <ChevronLeft className="size-4" />
            <span>Back to Tutorials</span>
          </Button>
        </Link>
      </div>

      <div className="space-y-8">
        <div className="mb-8">
          <div className="mb-2 flex items-center gap-3">
            <div className="bg-primary text-primary-foreground rounded-full p-2">
              <BookOpen className="size-5" />
            </div>
            <span className="text-muted-foreground text-sm font-medium">
              Beginner
            </span>
          </div>
          <h1 className="mb-4 text-4xl font-bold">
            Familiarize with Bayesian Optimization
          </h1>
          <p className="text-muted-foreground text-lg">
            Learn the fundamentals of Bayesian optimization and how it can guide
            your experimental design process.
          </p>
        </div>

        {/* Tutorial content imported from component */}
        <div className="prose prose-slate dark:prose-invert max-w-none">
          <BayesianOptimizationIntro />
        </div>
      </div>
    </div>
  )
}
