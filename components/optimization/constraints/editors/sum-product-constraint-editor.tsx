"use client"

import { Sigma, Calculator } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MultiSelect } from "@/components/ui/multi-select"
import {
  DiscreteSumConstraint,
  DiscreteProductConstraint,
  ConstraintCondition
} from "@/lib/constraints"

type SumProductConstraint = DiscreteSumConstraint | DiscreteProductConstraint

interface SumProductConstraintEditorProps {
  constraint: SumProductConstraint
  availableParameters: string[]
  onChange: (updates: Partial<SumProductConstraint>) => void
}

export function SumProductConstraintEditor({
  constraint,
  availableParameters,
  onChange
}: SumProductConstraintEditorProps) {
  const isSum = constraint.type === "DiscreteSumConstraint"

  const handleParametersChange = (parameters: string[]) => {
    onChange({ parameters })
  }

  const handleConditionChange = (condition: Partial<ConstraintCondition>) => {
    const currentCondition = constraint.condition || {
      type: "threshold" as const
    }
    onChange({
      condition: { ...currentCondition, ...condition } as ConstraintCondition
    })
  }

  const getConstraintPreview = () => {
    if (constraint.parameters.length === 0) {
      return `No parameters selected for ${isSum ? "sum" : "product"}`
    }

    const operation = isSum ? "sum" : "product"
    const symbol = isSum ? "+" : "×"
    const paramStr = constraint.parameters.join(` ${symbol} `)

    const condition = constraint.condition
    if (!condition) {
      return `${operation}(${paramStr}) [no condition]`
    }

    if (condition.type === "threshold") {
      const operator = condition.operator || ">="
      const threshold = condition.threshold ?? 0
      return `${operation}(${paramStr}) ${operator} ${threshold}`
    } else if (condition.type === "subselection") {
      const selection = condition.selection || []
      return `${operation}(${paramStr}) ∈ {${selection.join(", ")}}`
    }

    return `${operation}(${paramStr}) [invalid condition]`
  }

  const parameterOptions = availableParameters.map(param => ({
    value: param,
    label: param
  }))

  const condition = constraint.condition || { type: "threshold" as const }

  return (
    <div className="space-y-6">
      {/* Constraint Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            {isSum ? (
              <Sigma className="size-4" />
            ) : (
              <Calculator className="size-4" />
            )}
            {isSum ? "Sum" : "Product"} Constraint Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted rounded-md p-3 font-mono text-lg">
            {getConstraintPreview()}
          </div>
          <p className="text-muted-foreground mt-2 text-sm">
            {isSum
              ? "Constrains the sum of parameter values"
              : "Constrains the product of parameter values"}
          </p>
        </CardContent>
      </Card>

      {/* Parameter Selection */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-base font-medium">Parameters</Label>
          <Badge variant="outline">
            {constraint.parameters.length} selected
          </Badge>
        </div>

        <MultiSelect
          options={parameterOptions}
          value={constraint.parameters}
          onValueChange={handleParametersChange}
          placeholder={`Select parameters for ${isSum ? "sum" : "product"}...`}
          maxCount={10}
        />
      </div>

      {/* Condition Configuration */}
      <div className="space-y-4">
        <Label className="text-base font-medium">Condition</Label>

        <div className="space-y-3">
          <div className="space-y-2">
            <Label htmlFor="condition-type">Condition Type</Label>
            <Select
              value={condition.type}
              onValueChange={type =>
                handleConditionChange({
                  type: type as "threshold" | "subselection"
                })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="threshold">Threshold Comparison</SelectItem>
                <SelectItem value="subselection">Value Selection</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {condition.type === "threshold" ? (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="operator">Operator</Label>
                <Select
                  value={condition.operator || ">="}
                  onValueChange={operator =>
                    handleConditionChange({ operator: operator as any })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=">=">&gt;= (greater or equal)</SelectItem>
                    <SelectItem value="<=">&lt;= (less or equal)</SelectItem>
                    <SelectItem value=">">&gt; (greater than)</SelectItem>
                    <SelectItem value="<">&lt; (less than)</SelectItem>
                    <SelectItem value="=">=== (equal to)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="threshold">Threshold Value</Label>
                <Input
                  id="threshold"
                  type="number"
                  step="0.1"
                  value={condition.threshold ?? ""}
                  onChange={e =>
                    handleConditionChange({
                      threshold: parseFloat(e.target.value) || 0
                    })
                  }
                  placeholder="Enter threshold..."
                />
              </div>

              {/* Optional tolerance for continuous values */}
              <div className="col-span-2 space-y-2">
                <Label htmlFor="tolerance">Tolerance (Optional)</Label>
                <Input
                  id="tolerance"
                  type="number"
                  step="0.001"
                  min="0"
                  value={condition.tolerance ?? ""}
                  onChange={e =>
                    handleConditionChange({
                      tolerance: e.target.value
                        ? parseFloat(e.target.value)
                        : undefined
                    })
                  }
                  placeholder="Tolerance for equality (e.g., 0.001)"
                />
                <p className="text-muted-foreground text-xs">
                  Tolerance for floating-point comparisons (useful for equality
                  checks)
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="selection">Allowed Values</Label>
              <Input
                id="selection"
                value={(condition.selection || []).join(", ")}
                onChange={e => {
                  const values = e.target.value
                    .split(",")
                    .map(v => {
                      const trimmed = v.trim()
                      // Try to parse as number, otherwise keep as string
                      const num = parseFloat(trimmed)
                      return isNaN(num) ? trimmed : num
                    })
                    .filter(v => v !== "")
                  handleConditionChange({ selection: values })
                }}
                placeholder="Enter allowed values separated by commas (e.g., 1.0, 2.5, 10)"
              />
              <p className="text-muted-foreground text-xs">
                The {isSum ? "sum" : "product"} must equal one of these values
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Common Examples */}
      <Card className="bg-muted/30">
        <CardHeader>
          <CardTitle className="text-sm">Common Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          {isSum ? (
            <>
              <div>
                <strong>Mixture constraint:</strong> component_A + component_B +
                component_C = 1.0
              </div>
              <div>
                <strong>Budget limit:</strong> cost_1 + cost_2 + cost_3 ≤ 1000
              </div>
              <div>
                <strong>Minimum total:</strong> amount_A + amount_B ≥ 50
              </div>
            </>
          ) : (
            <>
              <div>
                <strong>Safety limit:</strong> temperature × pressure ≤ 1000
              </div>
              <div>
                <strong>Area constraint:</strong> length × width = 100
              </div>
              <div>
                <strong>Volume limit:</strong> height × width × depth ≤ 500
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
