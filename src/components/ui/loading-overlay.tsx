"use client"

import React, { useEffect, useState } from "react"
import { Loader2 } from "lucide-react"
import { useLoading } from "@/contexts/loading-context"
import { cn } from "@/lib/utils"

export function LoadingOverlay() {
  const { isLoading, stopLoading } = useLoading()
  const [visible, setVisible] = useState(false)
  const [delayedLoading, setDelayedLoading] = useState(false)

  // Add a small delay before showing the loading overlay to prevent flashing
  // for very quick operations
  useEffect(() => {
    let timeout: NodeJS.Timeout

    if (isLoading && !delayedLoading) {
      timeout = setTimeout(() => {
        setDelayedLoading(true)
      }, 150) // 150ms delay before showing the overlay (reduced from 300ms)
    } else if (!isLoading && delayedLoading) {
      setDelayedLoading(false)
    }

    return () => {
      clearTimeout(timeout)
    }
  }, [isLoading, delayedLoading])

  // Add a fade-in/fade-out effect
  useEffect(() => {
    let timeout: NodeJS.Timeout

    if (delayedLoading && !visible) {
      setVisible(true)
    } else if (!delayedLoading && visible) {
      // Keep the overlay visible briefly before fading out
      timeout = setTimeout(() => {
        setVisible(false)
      }, 150) // 150ms fade-out delay (reduced from 300ms)
    }

    return () => {
      clearTimeout(timeout)
    }
  }, [delayedLoading, visible])

  // Safety mechanism: automatically hide the overlay after a maximum time
  // This prevents the overlay from being stuck if navigation or other operations fail
  useEffect(() => {
    let timeout: NodeJS.Timeout

    if (isLoading) {
      timeout = setTimeout(() => {
        stopLoading()
      }, 3000) // 3 seconds maximum loading time (reduced from 8 seconds)
    }

    return () => {
      clearTimeout(timeout)
    }
  }, [isLoading, stopLoading])

  if (!visible) return null

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm transition-opacity duration-300",
        delayedLoading ? "opacity-100" : "opacity-0"
      )}
    >
      <div className="bg-background rounded-lg p-6 shadow-lg">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="text-primary size-12 animate-spin" />
          <p className="text-foreground font-medium">Loading...</p>
        </div>
      </div>
    </div>
  )
}
