"use client"

import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import Image from "next/image"
import { OptimizationMethodsComparison } from "./optimization-methods-comparison"
import { ConvergenceComparisonChart } from "./convergence-comparison-chart"

export function BayesianOptimizationIntro() {
  return (
    <div className="space-y-8">
      <section>
        <h2 className="mb-4 text-2xl font-bold">
          How Bayesian Optimization Works: A Conceptual Introduction
        </h2>
        <p className="mb-4">
          Bayesian optimization (BO) is a powerful approach for optimizing
          complex, expensive-to-evaluate functions.
        </p>
      </section>

      <section>
        <h3 className="mb-3 text-xl font-bold">
          The Core Problem: Efficient Optimization of Expensive Functions
        </h3>
        <p className="mb-4">
          Consider a common challenge faced by researchers and engineers:
          finding the optimal settings for a process that is costly,
          time-consuming, or resource-intensive to evaluate. For example:
        </p>
        <ul className="mb-4 list-disc space-y-2 pl-6">
          <li>
            A materials scientist searching for the optimal composition of an
            alloy
          </li>
          <li>
            A process engineer tuning parameters for a manufacturing process
          </li>
          <li>
            A chemical engineer looking to scale up a biorefinery process for
            lignin production
          </li>
        </ul>
        <p className="mb-4">
          In these scenarios, each evaluation consumes significant resources.
          Traditional optimization methods often fall short in addressing these
          challenges efficiently.
        </p>
      </section>

      <section>
        <h3 className="mb-3 text-xl font-bold">
          Comparison with Traditional Optimization Methods
        </h3>

        <Tabs defaultValue="visual" className="mb-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="visual">Visual Comparison</TabsTrigger>
            <TabsTrigger value="grid-random">Grid & Random Search</TabsTrigger>
            <TabsTrigger value="gradient">Gradient-Based</TabsTrigger>
            <TabsTrigger value="doe">Design of Experiments</TabsTrigger>
            <TabsTrigger value="ofat">One-Factor-At-A-Time</TabsTrigger>
          </TabsList>

          <TabsContent value="visual" className="mt-4 space-y-4">
            <h4 className="text-lg font-semibold">
              Visual Comparison of Optimization Methods
            </h4>
            <p className="mb-4">
              The visualizations below show how different optimization methods
              explore the parameter space and converge to the optimum:
            </p>

            {/* Import the visualization components */}
            <div className="mt-6">
              <OptimizationMethodsComparison />
            </div>

            <h4 className="mt-8 text-lg font-semibold">
              Convergence Comparison
            </h4>
            <p className="mb-4">
              This chart compares how quickly each method converges to the
              optimal solution:
            </p>

            <div className="mt-6">
              <ConvergenceComparisonChart />
            </div>
          </TabsContent>

          <TabsContent value="grid-random" className="mt-4 space-y-4">
            <h4 className="text-lg font-semibold">
              Grid Search and Random Search
            </h4>
            <p>
              Grid search systematically evaluates points in a predefined grid
              across the parameter space, while random search samples points
              randomly. Both methods:
            </p>
            <ul className="list-disc space-y-2 pl-6">
              <li>Are simple to implement and parallelize</li>
              <li>Make no assumptions about the underlying function</li>
              <li>Require many function evaluations to find good solutions</li>
              <li>Do not leverage information from previous evaluations</li>
            </ul>
          </TabsContent>

          <TabsContent value="gradient" className="mt-4 space-y-4">
            <h4 className="text-lg font-semibold">Gradient-Based Methods</h4>
            <p>Methods like gradient descent and its variants:</p>
            <ul className="list-disc space-y-2 pl-6">
              <li>
                Can efficiently find local optima when gradients are available
              </li>
              <li>Require differentiable objective functions</li>
              <li>May get trapped in local optima</li>
              <li>
                Are not applicable when gradient information is unavailable
              </li>
            </ul>
          </TabsContent>

          <TabsContent value="doe" className="mt-4 space-y-4">
            <h4 className="text-lg font-semibold">
              Design of Experiments (DOE)
            </h4>
            <p>
              DOE is a systematic approach to determining cause-and-effect
              relationships, commonly used in industrial settings:
            </p>
            <ul className="list-disc space-y-2 pl-6">
              <li>Uses statistical methods to plan experiments efficiently</li>
              <li>
                Includes techniques like factorial designs, response surface
                methodology, and Taguchi methods
              </li>
              <li>Provides structured exploration of the parameter space</li>
              <li>Often requires a predefined experimental plan</li>
              <li>
                May not adapt efficiently to information gained during
                experimentation
              </li>
            </ul>
          </TabsContent>

          <TabsContent value="ofat" className="mt-4 space-y-4">
            <h4 className="text-lg font-semibold">
              One-Factor-At-A-Time (OFAT)
            </h4>
            <p>OFAT is a traditional experimental approach where:</p>
            <ul className="list-disc space-y-2 pl-6">
              <li>One parameter is varied while others are held constant</li>
              <li>Effects of each factor are studied in isolation</li>
              <li>Interactions between factors may be missed</li>
              <li>The approach is intuitive but often inefficient</li>
            </ul>
          </TabsContent>
        </Tabs>
      </section>

      <section>
        <h3 className="mb-3 text-xl font-bold">
          The Bayesian Optimization Approach: Learning from Every Evaluation
        </h3>
        <p className="mb-4">
          Bayesian optimization addresses these challenges through a
          fundamentally different approach, through systematic experimentation:
        </p>
        <ol className="mb-4 list-decimal space-y-2 pl-6">
          <li>
            <strong>Build a surrogate model:</strong> Create a probabilistic
            model of the objective function based on previous observations
          </li>
          <li>
            <strong>Quantify uncertainty:</strong> Estimate not just the
            expected value at each point, but also the uncertainty
          </li>
          <li>
            <strong>Make informed decisions:</strong> Use this model to decide
            where to sample next, balancing exploration of unknown regions with
            exploitation of promising areas
          </li>
          <li>
            <strong>Update and iterate:</strong> Incorporate new observations to
            refine the model and repeat
          </li>
        </ol>
        <p className="mb-4">
          This approach is powerful because it learns from every evaluation,
          making each subsequent decision more informed than the last.
        </p>
      </section>

      <section>
        <h3 className="mb-3 text-xl font-bold">
          The Mechanics: A Step-by-Step Explanation
        </h3>
        <p className="mb-4">
          Here's how Bayesian optimization works in practice:
        </p>

        {/* Step cards */}
        <div className="space-y-8 py-4">
          <div className="relative">
            <Card className="mb-6 border-l-4 border-l-blue-500 shadow-lg">
              <CardContent className="pt-6">
                <div className="absolute -left-6 top-6 flex size-12 items-center justify-center rounded-full bg-blue-500 text-lg font-bold text-white">
                  1
                </div>
                <h4 className="mb-2 pl-8 text-lg font-semibold">
                  Initial Sampling
                </h4>
                <p className="mb-4">
                  The process begins with a small number of initial
                  observations, which might be:
                </p>
                <ul className="mb-4 list-disc space-y-1 pl-6">
                  <li>Randomly selected points</li>
                  <li>Points chosen based on domain knowledge</li>
                  <li>
                    A designed experiment (e.g., Latin hypercube sampling)
                  </li>
                </ul>
                <p>
                  These initial observations provide the first glimpse of the
                  objective function's behavior.
                </p>

                {/* Visual illustration */}
                <div className="mt-6 rounded-lg border border-gray-100 bg-gray-50 p-4">
                  <div className="relative h-[120px]">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg width="300" height="120" viewBox="0 0 300 120">
                        <rect
                          x="20"
                          y="20"
                          width="260"
                          height="80"
                          fill="#f8fafc"
                          stroke="#e2e8f0"
                          strokeWidth="1"
                        />
                        <circle cx="80" cy="40" r="5" fill="#3b82f6" />
                        <circle cx="150" cy="80" r="5" fill="#3b82f6" />
                        <circle cx="220" cy="50" r="5" fill="#3b82f6" />
                        <circle cx="50" cy="70" r="5" fill="#3b82f6" />
                        <circle cx="250" cy="30" r="5" fill="#3b82f6" />
                      </svg>
                    </div>
                  </div>
                  <p className="text-center text-xs text-gray-500">
                    Initial sampling points distributed across the parameter
                    space
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="relative">
            <Card className="mb-6 border-l-4 border-l-indigo-500 shadow-lg">
              <CardContent className="pt-6">
                <div className="absolute -left-6 top-6 flex size-12 items-center justify-center rounded-full bg-indigo-500 text-lg font-bold text-white">
                  2
                </div>
                <h4 className="mb-2 pl-8 text-lg font-semibold">
                  Building the Surrogate Model
                </h4>
                <p className="mb-4">
                  After collecting initial data, Bayesian optimization
                  constructs a surrogate model (a Gaussian Process (GP)). The GP
                  provides:
                </p>
                <ul className="mb-4 list-disc space-y-1 pl-6">
                  <li>A mean prediction (best guess of the function value)</li>
                  <li>A variance (uncertainty in that prediction)</li>
                </ul>
                <p>
                  The GP model "fills in the gaps" between observed points,
                  creating a probabilistic map of the parameter space.
                </p>

                {/* Visual illustration */}
                <div className="mt-6 rounded-lg border border-gray-100 bg-gray-50 p-4">
                  <div className="relative h-[120px]">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg width="300" height="120" viewBox="0 0 300 120">
                        <rect
                          x="20"
                          y="20"
                          width="260"
                          height="80"
                          fill="#f8fafc"
                          stroke="#e2e8f0"
                          strokeWidth="1"
                        />
                        <circle cx="80" cy="40" r="4" fill="#3b82f6" />
                        <circle cx="150" cy="80" r="4" fill="#3b82f6" />
                        <circle cx="220" cy="50" r="4" fill="#3b82f6" />
                        <circle cx="50" cy="70" r="4" fill="#3b82f6" />
                        <circle cx="250" cy="30" r="4" fill="#3b82f6" />

                        {/* Surrogate model curve */}
                        <path
                          d="M20,70 C50,70 60,40 80,40 C100,40 120,90 150,80 C180,70 200,30 220,50 C240,70 250,30 280,30"
                          fill="none"
                          stroke="#818cf8"
                          strokeWidth="2"
                        />

                        {/* Uncertainty bands */}
                        <path
                          d="M20,80 C50,80 60,50 80,50 C100,50 120,100 150,90 C180,80 200,40 220,60 C240,80 250,40 280,40"
                          fill="none"
                          stroke="#818cf8"
                          strokeWidth="1"
                          strokeDasharray="3,2"
                        />
                        <path
                          d="M20,60 C50,60 60,30 80,30 C100,30 120,80 150,70 C180,60 200,20 220,40 C240,60 250,20 280,20"
                          fill="none"
                          stroke="#818cf8"
                          strokeWidth="1"
                          strokeDasharray="3,2"
                        />
                      </svg>
                    </div>
                  </div>
                  <p className="text-center text-xs text-gray-500">
                    Gaussian Process model with mean prediction (solid line) and
                    uncertainty (dashed lines)
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="relative">
            <Card className="mb-6 border-l-4 border-l-purple-500 shadow-lg">
              <CardContent className="pt-6">
                <div className="absolute -left-6 top-6 flex size-12 items-center justify-center rounded-full bg-purple-500 text-lg font-bold text-white">
                  3
                </div>
                <h4 className="mb-2 pl-8 text-lg font-semibold">
                  Acquisition Function Optimization
                </h4>
                <p className="mb-4">
                  The acquisition function guides where to sample next,
                  balancing:
                </p>
                <ul className="mb-4 list-disc space-y-1 pl-6">
                  <li>
                    Exploration: Sampling in regions of high uncertainty to
                    improve the model
                  </li>
                  <li>
                    Exploitation: Sampling in regions where the model predicts
                    good performance
                  </li>
                </ul>
                <p className="mb-4">Common acquisition functions include:</p>
                <ul className="mb-4 list-disc space-y-1 pl-6">
                  <li>
                    Expected Improvement (EI): Measures the expected improvement
                    over the current best observation
                  </li>
                  <li>
                    Probability of Improvement (PI): Calculates the probability
                    of exceeding the current best
                  </li>
                  <li>
                    Upper Confidence Bound (UCB): Combines the mean prediction
                    with uncertainty, weighted by a trade-off parameter
                  </li>
                </ul>
                <p>
                  The next evaluation point is selected by maximizing the
                  acquisition function.
                </p>

                {/* Visual illustration */}
                <div className="mt-6 rounded-lg border border-gray-100 bg-gray-50 p-4">
                  <div className="relative h-[120px]">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg width="300" height="120" viewBox="0 0 300 120">
                        <rect
                          x="20"
                          y="20"
                          width="260"
                          height="80"
                          fill="#f8fafc"
                          stroke="#e2e8f0"
                          strokeWidth="1"
                        />

                        {/* Surrogate model curve */}
                        <path
                          d="M20,70 C50,70 60,40 80,40 C100,40 120,90 150,80 C180,70 200,30 220,50 C240,70 250,30 280,30"
                          fill="none"
                          stroke="#818cf8"
                          strokeWidth="1.5"
                          opacity="0.5"
                        />

                        {/* Acquisition function */}
                        <path
                          d="M20,90 C40,90 50,80 60,70 C70,60 75,50 80,60 C90,80 100,90 110,80 C120,70 130,50 140,40 C150,30 160,25 170,30 C180,35 190,50 200,60 C210,70 220,75 230,70 C240,65 250,70 260,80 C270,90 280,90 280,90"
                          fill="none"
                          stroke="#a855f7"
                          strokeWidth="2"
                        />

                        {/* Next point to evaluate */}
                        <circle
                          cx="170"
                          cy="30"
                          r="5"
                          fill="#ec4899"
                          stroke="white"
                          strokeWidth="1.5"
                        />
                        <line
                          x1="170"
                          y1="30"
                          x2="170"
                          y2="100"
                          stroke="#ec4899"
                          strokeWidth="1"
                          strokeDasharray="3,2"
                        />
                        <text x="175" y="45" fontSize="10" fill="#ec4899">
                          Next point
                        </text>
                      </svg>
                    </div>
                  </div>
                  <p className="text-center text-xs text-gray-500">
                    Acquisition function (purple) identifies the next most
                    promising point to evaluate
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="relative">
            <Card className="mb-6 border-l-4 border-l-pink-500 shadow-lg">
              <CardContent className="pt-6">
                <div className="absolute -left-6 top-6 flex size-12 items-center justify-center rounded-full bg-pink-500 text-lg font-bold text-white">
                  4
                </div>
                <h4 className="mb-2 pl-8 text-lg font-semibold">
                  Function Evaluation and Model Update
                </h4>
                <p className="mb-4">
                  The objective function is evaluated at the selected point, and
                  this new observation updates the GP model. The posterior
                  distribution shifts, reducing uncertainty near the new point
                  and altering the acquisition function landscape.
                </p>

                {/* Visual illustration */}
                <div className="mt-6 rounded-lg border border-gray-100 bg-gray-50 p-4">
                  <div className="relative h-[120px]">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg width="300" height="120" viewBox="0 0 300 120">
                        <rect
                          x="20"
                          y="20"
                          width="260"
                          height="80"
                          fill="#f8fafc"
                          stroke="#e2e8f0"
                          strokeWidth="1"
                        />

                        {/* Original model curve (faded) */}
                        <path
                          d="M20,70 C50,70 60,40 80,40 C100,40 120,90 150,80 C180,70 200,30 220,50 C240,70 250,30 280,30"
                          fill="none"
                          stroke="#818cf8"
                          strokeWidth="1"
                          strokeDasharray="3,2"
                          opacity="0.4"
                        />

                        {/* Updated model curve */}
                        <path
                          d="M20,70 C50,70 60,40 80,40 C100,40 120,90 150,80 C160,75 165,60 170,45 C175,30 190,25 220,50 C240,70 250,30 280,30"
                          fill="none"
                          stroke="#818cf8"
                          strokeWidth="2"
                        />

                        {/* Original points */}
                        <circle cx="80" cy="40" r="3" fill="#3b82f6" />
                        <circle cx="150" cy="80" r="3" fill="#3b82f6" />
                        <circle cx="220" cy="50" r="3" fill="#3b82f6" />
                        <circle cx="50" cy="70" r="3" fill="#3b82f6" />
                        <circle cx="250" cy="30" r="3" fill="#3b82f6" />

                        {/* New evaluated point */}
                        <circle cx="170" cy="45" r="5" fill="#ec4899" />
                      </svg>
                    </div>
                  </div>
                  <p className="text-center text-xs text-gray-500">
                    Model is updated with the new observation, reducing
                    uncertainty in that region
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="relative">
            <Card className="mb-6 border-l-4 border-l-orange-500 shadow-lg">
              <CardContent className="pt-6">
                <div className="absolute -left-6 top-6 flex size-12 items-center justify-center rounded-full bg-orange-500 text-lg font-bold text-white">
                  5
                </div>
                <h4 className="mb-2 pl-8 text-lg font-semibold">
                  Iteration Until Convergence
                </h4>
                <p className="mb-4">
                  Steps 3 and 4 repeat until a stopping criterion is met, such
                  as:
                </p>
                <ul className="mb-4 list-disc space-y-1 pl-6">
                  <li>Reaching a maximum number of evaluations</li>
                  <li>Achieving a target objective value</li>
                  <li>Observing diminishing returns</li>
                </ul>

                {/* Visual illustration */}
                <div className="mt-6 rounded-lg border border-gray-100 bg-gray-50 p-4">
                  <div className="relative h-[120px]">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg width="300" height="120" viewBox="0 0 300 120">
                        <rect
                          x="20"
                          y="20"
                          width="260"
                          height="80"
                          fill="#f8fafc"
                          stroke="#e2e8f0"
                          strokeWidth="1"
                        />

                        {/* Final model curve */}
                        <path
                          d="M20,70 C50,70 60,40 80,40 C100,40 120,90 150,80 C160,75 165,60 170,45 C175,30 190,25 220,50 C240,70 250,30 280,30"
                          fill="none"
                          stroke="#818cf8"
                          strokeWidth="2"
                        />

                        {/* All evaluated points */}
                        <circle cx="80" cy="40" r="3" fill="#3b82f6" />
                        <circle cx="150" cy="80" r="3" fill="#3b82f6" />
                        <circle cx="220" cy="50" r="3" fill="#3b82f6" />
                        <circle cx="50" cy="70" r="3" fill="#3b82f6" />
                        <circle cx="250" cy="30" r="3" fill="#3b82f6" />
                        <circle cx="170" cy="45" r="3" fill="#ec4899" />
                        <circle cx="185" cy="35" r="3" fill="#ec4899" />
                        <circle cx="160" cy="50" r="3" fill="#ec4899" />

                        {/* Optimum */}
                        <circle
                          cx="175"
                          cy="30"
                          r="6"
                          fill="#f97316"
                          stroke="white"
                          strokeWidth="1.5"
                        />
                        <text
                          x="175"
                          y="20"
                          fontSize="10"
                          textAnchor="middle"
                          fill="#f97316"
                        >
                          Optimum
                        </text>
                      </svg>
                    </div>
                  </div>
                  <p className="text-center text-xs text-gray-500">
                    After several iterations, the algorithm converges to the
                    optimum
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <section>
        <h3 className="mb-3 text-xl font-bold">Why Gaussian Processes?</h3>
        <p className="mb-4">
          Gaussian Processes are the most common surrogate model in Bayesian
          optimization because:
        </p>
        <ol className="mb-4 list-decimal space-y-2 pl-6">
          <li>
            <strong>Flexibility:</strong> They can model a wide variety of
            function shapes
          </li>
          <li>
            <strong>Uncertainty quantification:</strong> They naturally provide
            uncertainty estimates
          </li>
          <li>
            <strong>Analytical updates:</strong> The posterior has a closed-form
            solution
          </li>
          <li>
            <strong>Prior incorporation:</strong> Domain knowledge can be
            encoded via kernels
          </li>
        </ol>
      </section>
    </div>
  )
}
