// app/api/proxy/sampling/generate/route.ts
import { NextRequest, NextResponse } from "next/server"
import { API_CONFIG } from "@/lib/config"

/**
 * Proxy endpoint for constraint-aware sample generation
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json()
    const {
      parameters,
      n_samples,
      sampling_strategy,
      seed,
      constraints,
      respect_constraints,
      max_attempts,
      tolerance
    } = body

    if (!parameters || !Array.isArray(parameters)) {
      return NextResponse.json(
        { error: "parameters array is required" },
        { status: 400 }
      )
    }

    if (!n_samples || n_samples < 1) {
      return NextResponse.json(
        { error: "n_samples must be a positive integer" },
        { status: 400 }
      )
    }

    // Get the API URL from the centralized config
    const apiUrl = `${API_CONFIG.BASE_URL}/sampling/generate`

    console.log("Constraint-Aware Sampling Proxy: Fetching from", apiUrl)
    console.log("Constraint-Aware Sampling Proxy: Request body:", {
      parameters: parameters.length,
      n_samples,
      sampling_strategy,
      respect_constraints,
      constraints: constraints?.length || 0
    })

    // Prepare the request body for the backend API
    const requestBody = {
      parameters,
      n_samples,
      sampling_strategy: sampling_strategy || "LHS",
      seed,
      constraints: constraints || [],
      respect_constraints: respect_constraints || false,
      max_attempts: max_attempts || 1000,
      tolerance: tolerance || 1e-6
    }

    console.log(
      "Constraint-Aware Sampling Proxy: Final request body:",
      requestBody
    )

    // Make the request to the API
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "X-API-Key": API_CONFIG.API_KEY,
        Accept: "application/json",
        "Content-Type": "application/json"
      },
      body: JSON.stringify(requestBody),
      // Add a reasonable timeout
      signal: AbortSignal.timeout(30000) // 30 seconds for sampling
    })

    // Get the response as text first for debugging
    const responseText = await response.text()
    console.log("Constraint-Aware Sampling Proxy: Raw response:", responseText)
    console.log(
      "Constraint-Aware Sampling Proxy: Response status:",
      response.status
    )

    if (!response.ok) {
      console.error(
        "Constraint-Aware Sampling Proxy: API request failed:",
        response.status,
        responseText
      )
      return NextResponse.json(
        {
          error: "Sampling request failed",
          details: responseText,
          status: response.status
        },
        { status: response.status }
      )
    }

    // Parse the response
    let result
    try {
      result = JSON.parse(responseText)
    } catch (parseError) {
      console.error(
        "Constraint-Aware Sampling Proxy: Failed to parse response:",
        parseError
      )
      return NextResponse.json(
        { error: "Invalid response format from sampling API" },
        { status: 500 }
      )
    }

    console.log("Constraint-Aware Sampling Proxy: Parsed result:", {
      status: result.status,
      samples: result.samples?.length || 0,
      constraint_violations: result.constraint_violations,
      feasible_samples: result.feasible_samples
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error("Constraint-Aware Sampling Proxy: Error:", error)

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        return NextResponse.json(
          { error: "Sampling request timed out" },
          { status: 408 }
        )
      }

      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(
      { error: "Unknown error occurred during sampling" },
      { status: 500 }
    )
  }
}
