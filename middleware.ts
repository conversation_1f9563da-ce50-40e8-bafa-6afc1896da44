/*
Contains middleware for protecting routes, checking user authentication, and redirecting as needed.
*/

import { clerkMiddleware } from "@clerk/nextjs/server"
import { NextResponse } from "next/server"

// Helper function to check if a path matches any of the patterns
const matchesPattern = (path: string, patterns: string[]) => {
  return patterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
      return regex.test(path);
    }
    return path === pattern;
  });
};

// Define route patterns
const protectedRoutes = ["/dashboard(.*)", "/todo(.*)"];
const welcomePages = ["/welcome"];
const academicPages = ["/academic-signup", "/academic-survey"];
const marketingPages = [
  "/", // Include root path as a marketing page
  "/features",
  "/pricing",
  "/contact",
  "/case-studies",
  "/tutorials",
  "/tutorials/(.*)" // Include all tutorial subpages
];

export default clerkMiddleware(async (auth, req) => {
  const authResult = await auth()
  const { userId, redirectToSignIn, sessionId, sessionClaims } = authResult
  const { pathname } = req.nextUrl

  // Extract the correct user ID from session claims (our custom userId)
  const actualUserId = sessionClaims?.userId || userId;

  // Define public routes that don't require authentication
  const publicRoutes = [
    "/",
    "/about",
    "/features",
    "/pricing",
    "/contact",
    "/case-studies",
    "/tutorials",
    "/tutorials/(.*)", // Include all tutorial subpages
    "/api/stripe/webhooks",
    "/api/stripe/checkout",
    "/auth-redirect", // Add our new auth-redirect page
    "/login",
    "/signup",
    "/signup/(.*)", // Allow all signup sub-routes including verification
    "/academic-signup" // Allow academic signup page for post-auth redirect
  ];

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.includes(pathname) ||
                        pathname.startsWith('/api/stripe/webhooks') ||
                        pathname.startsWith('/api/stripe/checkout') ||
                        pathname.startsWith('/tutorials/') ||
                        pathname.startsWith('/login') ||
                        pathname.startsWith('/signup') ||
                        pathname.startsWith('/auth-redirect') ||
                        pathname === '/academic-signup';

  // Skip detailed logging for health check and other noisy routes
  const isHealthCheck = pathname.includes('/api/proxy/health') ||
                       pathname.includes('/api/health') ||
                       pathname.includes('/_next/') ||
                       pathname.includes('/favicon.ico')



  // Skip ALL logging for health checks (including unauthenticated access)
  if (isHealthCheck) {
    // Handle the route logic without any logging
    if (isPublicRoute) {
      return NextResponse.next()
    }

    if (actualUserId) {
      return NextResponse.next()
    }

    return redirectToSignIn()
  }





  // CASE 1: User is not logged in
  if (!userId) {
    // If trying to access protected routes, redirect to sign-in
    if (matchesPattern(pathname, protectedRoutes)) {

      return redirectToSignIn({ returnBackUrl: pathname })
    }

    // If trying to access welcome page, redirect to sign-in
    if (matchesPattern(pathname, welcomePages)) {
      return redirectToSignIn({ returnBackUrl: "/welcome" })
    }

    // If trying to access academic survey (but not signup), redirect to sign-in
    if (pathname === '/academic-survey') {
      return redirectToSignIn({ returnBackUrl: pathname })
    }

    // For all other public routes (marketing, etc.), allow access
    if (!isHealthCheck) {
      // Allow public route access
    }
    return NextResponse.next()
  }

  // CASE 2: User is logged in - allow access to all routes
  if (!isHealthCheck) {
    // Allow authenticated user access
  }
  return NextResponse.next()
})

export const config = {
  matcher: [
    "/((?!.*\\..*|_next).*)",
    "/",
    "/(api|trpc)((?!/proxy).*)"] // Exclude /api/proxy routes from authentication
}
