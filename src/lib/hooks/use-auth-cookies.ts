"use client"

import { useEffect } from "react"

/**
 * Hook to ensure authentication cookies are always allowed
 * This helps prevent authentication issues when users reject cookies
 */
export function useAuthCookies() {
  useEffect(() => {
    // Ensure authentication cookies are not blocked
    const ensureAuthCookies = () => {
      if (typeof window === "undefined") return

      try {
        // List of critical authentication cookies
        const authCookies = [
          "__clerk_db_jwt",
          "__session",
          "clerk_active_context",
          "__clerk_session",
          "_clerk_session"
        ]

        // Check if CookieScript is loaded
        if (window.CookieScript && window.CookieScript.instance) {
          // Method 1: Add to essential cookies
          authCookies.forEach(cookieName => {
            try {
              window.CookieScript.instance.addEssentialCookie(cookieName)
              // Also add with wildcard patterns for dynamic suffixes
              window.CookieScript.instance.addEssentialCookie(cookieName + "_*")
            } catch (error) {
              console.warn(`Could not mark ${cookieName} as essential:`, error)
            }
          })

          // Method 2: Override cookie deletion for auth cookies
          const originalDeleteCookie = window.CookieScript.instance.deleteCookie
          if (originalDeleteCookie) {
            window.CookieScript.instance.deleteCookie = function (
              cookieName: string
            ) {
              // Don't delete authentication cookies
              const isAuthCookie = authCookies.some(
                pattern =>
                  cookieName.includes(pattern) ||
                  cookieName.startsWith("__clerk") ||
                  cookieName.startsWith("clerk_") ||
                  cookieName.startsWith("__session")
              )

              if (isAuthCookie) {
                return false
              }

              // Use original function for other cookies
              return originalDeleteCookie.call(this, cookieName)
            }
          }
        }

        // Method 3: Periodically check and restore auth cookies if they're missing
        const checkAuthCookies = () => {
          const currentCookies = document.cookie
          const hasClerkJWT = currentCookies.includes("__clerk_db_jwt")
          const hasSession =
            currentCookies.includes("__session") ||
            currentCookies.includes("clerk_active_context")

          if (!hasClerkJWT || !hasSession) {
            // Try to restore from localStorage or trigger re-authentication
            if ((window as any).Clerk) {
              ;(window as any).Clerk.session?.reload?.()
            }
          }
        }

        // Check every 30 seconds
        const interval = setInterval(checkAuthCookies, 30000)

        return () => clearInterval(interval)
      } catch (error) {
        // Silently handle errors in cookie protection setup
      }
    }

    // Run immediately
    ensureAuthCookies()

    // Also run when CookieScript loads
    const handleCookieScriptLoad = () => {
      setTimeout(ensureAuthCookies, 1000) // Give CookieScript time to initialize
    }

    window.addEventListener("CookieScriptLoaded", handleCookieScriptLoad)

    return () => {
      window.removeEventListener("CookieScriptLoaded", handleCookieScriptLoad)
    }
  }, [])
}
