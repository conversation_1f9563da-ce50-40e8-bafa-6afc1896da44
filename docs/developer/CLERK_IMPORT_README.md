# Clerk User Import Guide

This guide explains how to import users from your CSV export to your new Clerk instance while maintaining database compatibility.

## Overview

Your setup is configured to use external IDs, which means:
- Your existing user IDs from the CSV will become external IDs in Clerk
- Your session token is configured to return the external ID as the user ID
- Your existing database records will continue to work seamlessly

## Prerequisites

1. ✅ CSV file with user data (`ins_2tnpF5wYJCReSjzrJ7ocPvZFRCb (1).csv`)
2. ✅ Clerk instance configured with your credentials
3. ✅ Session token customized: `{"userId": "{{user.external_id || user.id}}"}`
4. ✅ Dependencies installed (`csv-parser` is now installed)

## Step-by-Step Import Process

### Step 1: Verify Clerk Connection

First, test that your Clerk credentials are working:

```bash
npm run verify:clerk
```

This will:
- Test connection to your Clerk instance
- Show current user count
- Display sample users (if any exist)
- Confirm you're ready for import

### Step 2: Run the Import

Import all users from your CSV:

```bash
npm run import:clerk-users
```

The script will:
- Read your CSV file
- Process users in batches (10 at a time)
- Create users with external IDs matching your original user IDs
- <PERSON>le duplicates gracefully
- Preserve password hashes where available
- Generate a detailed report

### Step 3: Review Results

After import, check:
1. **Console output** - Shows real-time progress and summary
2. **`clerk-import-report.json`** - Detailed report with all results
3. **Clerk Dashboard** - Verify users appear in your Clerk instance

## What the Import Does

For each user in your CSV, the script:

1. **Maps data fields:**
   - `id` → `external_id` (maintains database compatibility)
   - `primary_email_address` → `email_addresses[0]`
   - `first_name` → `first_name`
   - `last_name` → `last_name`
   - `password_digest` → `password_digest` (preserves existing passwords)

2. **Creates Clerk user** with verified email status

3. **Handles errors gracefully:**
   - Skips users that already exist
   - Logs failed imports for review
   - Continues processing remaining users

## Expected Results

From your CSV with 31 users, you should see:
- ✅ Successfully imported: ~31 users (minus any duplicates)
- ⚠️ Already existed: 0 users (first run)
- ❌ Failed: 0 users (if all data is valid)

## Verification

After import, your application should work exactly as before because:

1. **Database queries** will find existing records using the same user IDs
2. **Session tokens** will return the external ID as the user ID
3. **User authentication** will work with existing or new passwords

## Troubleshooting

### Common Issues:

**"User already exists"**
- This is normal if re-running the script
- Users are skipped automatically

**"Invalid email address"**
- Check CSV for malformed email addresses
- Script validates and skips invalid emails

**"Rate limit exceeded"**
- Script includes delays between batches
- Increase `DELAY_BETWEEN_BATCHES` if needed

**"Authentication failed"**
- Verify your `CLERK_SECRET_KEY` is correct
- Ensure the key has proper permissions

### Files Created:

- `clerk-import-report.json` - Detailed import results
- Console logs - Real-time progress and errors

## Next Steps

After successful import:

1. **Test login** with a few imported users
2. **Verify database queries** work with existing records  
3. **Update your application** to use the new Clerk instance
4. **Monitor** for any authentication issues

## Support

If you encounter issues:
1. Check the `clerk-import-report.json` for detailed error messages
2. Verify your Clerk dashboard shows the imported users
3. Test authentication with a sample user
4. Ensure your session token configuration is active

---

**Important**: This import preserves your existing user IDs as external IDs, ensuring your database relationships remain intact while migrating to the new Clerk instance.
