"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Edit, Save, X } from "lucide-react"

interface ParameterConfig {
  name: string
  type: "NumericalContinuous" | "NumericalDiscrete" | "CategoricalParameter"
  bounds?: [number, number]
  values?: (number | string)[]
  encoding?: string
  tolerance?: number
  rawBoundsInput?: { [key: number]: string } // Store raw bound inputs for editing
}

interface ParameterQuickEditProps {
  parameter: ParameterConfig
  onSave: (updatedParameter: ParameterConfig) => void
  trigger?: React.ReactNode
  disabled?: boolean
}

export function ParameterQuickEdit({
  parameter,
  onSave,
  trigger,
  disabled = false
}: ParameterQuickEditProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [editedParameter, setEditedParameter] =
    useState<ParameterConfig>(parameter)
  const [hasChanges, setHasChanges] = useState(false)

  // Reset form when parameter changes or popover opens
  useEffect(() => {
    if (isOpen) {
      setEditedParameter(parameter)
      setHasChanges(false)
    }
  }, [parameter, isOpen])

  // Check for changes
  useEffect(() => {
    const changed =
      JSON.stringify(editedParameter) !== JSON.stringify(parameter)
    setHasChanges(changed)
  }, [editedParameter, parameter])

  const handleBoundChange = (index: 0 | 1, value: string) => {
    setEditedParameter(prev => {
      const newBounds = [...(prev.bounds || [0, 0])] as [number, number]

      // Allow empty string for editing, but convert to 0 for storage
      if (value === "") {
        newBounds[index] = 0
      } else {
        const numValue = parseFloat(value)
        if (!isNaN(numValue)) {
          newBounds[index] = numValue
        } else {
          // For invalid input, keep the previous value but allow the input to show
          return {
            ...prev,
            rawBoundsInput: {
              ...prev.rawBoundsInput,
              [index]: value
            }
          }
        }
      }

      return {
        ...prev,
        bounds: newBounds,
        rawBoundsInput: {
          ...prev.rawBoundsInput,
          [index]: value
        }
      }
    })
  }

  const handleValuesChange = (valuesString: string) => {
    let values: (number | string)[] = []

    if (editedParameter.type === "NumericalDiscrete") {
      values = valuesString
        .split(",")
        .map((s: string) => s.trim())
        .map((s: string) => parseFloat(s))
        .filter((n: number) => !isNaN(n))
    } else if (editedParameter.type === "CategoricalParameter") {
      values = valuesString
        .split(",")
        .map((s: string) => s.trim())
        .filter((s: string) => s.length > 0)
    }

    setEditedParameter(prev => ({
      ...prev,
      values
    }))
  }

  const handleSave = () => {
    onSave(editedParameter)
    setIsOpen(false)
  }

  const handleCancel = () => {
    setEditedParameter(parameter)
    setHasChanges(false)
    setIsOpen(false)
  }

  const defaultTrigger = (
    <Button
      variant="ghost"
      size="sm"
      disabled={disabled}
      className="size-6 p-0 opacity-0 transition-opacity group-hover:opacity-100"
    >
      <Edit className="size-3" />
    </Button>
  )

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>{trigger || defaultTrigger}</PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <h4 className="text-sm font-medium">{parameter.name}</h4>
            <Badge variant="outline" className="text-xs">
              {parameter.type}
            </Badge>
          </div>

          {/* Numerical Continuous Parameters */}
          {editedParameter.type === "NumericalContinuous" && (
            <div className="space-y-2">
              <Label className="text-xs font-medium">Bounds</Label>
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <Label className="text-muted-foreground text-xs">Min</Label>
                  <Input
                    type="number"
                    value={
                      editedParameter.rawBoundsInput?.[0] ??
                      editedParameter.bounds?.[0] ??
                      ""
                    }
                    onChange={e => handleBoundChange(0, e.target.value)}
                    step="any"
                    placeholder="Min"
                    className="h-7 text-xs"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-muted-foreground text-xs">Max</Label>
                  <Input
                    type="number"
                    value={
                      editedParameter.rawBoundsInput?.[1] ??
                      editedParameter.bounds?.[1] ??
                      ""
                    }
                    onChange={e => handleBoundChange(1, e.target.value)}
                    step="any"
                    placeholder="Max"
                    className="h-7 text-xs"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Discrete and Categorical Parameters */}
          {(editedParameter.type === "NumericalDiscrete" ||
            editedParameter.type === "CategoricalParameter") && (
            <div className="space-y-2">
              <Label className="text-xs font-medium">
                Values{" "}
                {editedParameter.type === "NumericalDiscrete"
                  ? "(numbers)"
                  : "(text)"}
              </Label>
              <Input
                type="text"
                value={editedParameter.values?.join(", ") ?? ""}
                onChange={e => handleValuesChange(e.target.value)}
                placeholder={
                  editedParameter.type === "NumericalDiscrete"
                    ? "1, 2, 5, 10"
                    : "option1, option2"
                }
                className="h-7 text-xs"
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="h-7 text-xs"
            >
              <X className="mr-1 size-3" />
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
              className="h-7 text-xs"
            >
              <Save className="mr-1 size-3" />
              Save
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
