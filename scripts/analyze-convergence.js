#!/usr/bin/env node

/**
 * Script to analyze the latest optimization project and validate convergence analysis
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js");
const postgres = require("postgres");
const { desc, eq } = require("drizzle-orm");
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core");

// Define schema inline to avoid import issues
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: text("user_id").notNull(),
  name: text("name").notNull(),
  description: text("description"),
  optimizerId: text("optimizer_id").notNull().unique(),
  config: jsonb("config").notNull(),
  targetName: text("target_name").notNull(),
  targetMode: text("target_mode").notNull(),
  status: text("status").notNull().default("draft"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

const measurementsTable = pgTable("measurements", {
  id: uuid("id").defaultRandom().primaryKey(),
  optimizationId: uuid("optimization_id").references(() => optimizationsTable.id, { onDelete: "cascade" }).notNull(),
  parameters: jsonb("parameters").notNull(),
  targetValue: text("target_value").notNull(),
  targetValues: jsonb("target_values"),
  isRecommended: boolean("is_recommended").notNull().default(true),
  batchId: text("batch_id"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

// Database connection
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  console.error("DATABASE_URL environment variable is required");
  process.exit(1);
}

const sql = postgres(connectionString);
const db = drizzle(sql);

/**
 * Get the target mode (MAX/MIN) for a specific target in an optimization.
 * Handles both single-target and multi-target optimizations correctly.
 */
function getTargetMode(optimization, targetName) {
  if (optimization.targetMode !== "MULTI") {
    // Handle enum-style target modes like "TargetMode.MAX"
    const targetMode = String(optimization.targetMode).toUpperCase();
    if (targetMode.includes("MAX")) {
      return "MAX";
    } else if (targetMode.includes("MIN")) {
      return "MIN";
    }
    return "MAX"; // Default to MAX if we can't determine
  }

  // For multi-target, find the specific target's mode from the config
  try {
    const config = optimization.config;
    if (Array.isArray(config.target_config)) {
      const targetConfig = config.target_config.find(
        (t) => t.name === targetName
      );
      if (targetConfig && targetConfig.mode) {
        return targetConfig.mode;
      }
    }
  } catch (error) {
    console.error("Error getting target mode:", error);
  }

  // Default to MAX if we can't determine
  return "MAX";
}

/**
 * Process convergence data similar to the frontend component
 */
function processConvergenceData(measurements, optimization, targetName, targetMode, isMultiTarget = false, otherTargets = []) {
  if (!measurements || measurements.length === 0) {
    return [];
  }

  const targets = isMultiTarget ? [targetName, ...otherTargets] : [targetName];
  const processedData = [];

  targets.forEach(target => {
    // Get the correct target mode for this specific target
    const currentTargetMode = getTargetMode(optimization, target);

    // Filter measurements that have this target
    const targetMeasurements = measurements.filter(m =>
      m.targetValues && typeof m.targetValues === 'object' && target in m.targetValues
    );

    if (targetMeasurements.length === 0) return;

    // Sort by creation date
    const sortedMeasurements = targetMeasurements.sort((a, b) =>
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    const iterations = [];
    const currentValues = [];
    const bestValues = [];
    const improvements = [];
    const regret = [];

    let bestSoFar = currentTargetMode === "MAX" ? -Infinity : Infinity;
    let theoreticalOptimum = currentTargetMode === "MAX" ?
      Math.max(...sortedMeasurements.map(m => m.targetValues[target])) :
      Math.min(...sortedMeasurements.map(m => m.targetValues[target]));

    sortedMeasurements.forEach((measurement, index) => {
      const value = measurement.targetValues[target];

      iterations.push(index + 1);
      currentValues.push(value);

      // Update best so far
      if (currentTargetMode === "MAX") {
        bestSoFar = Math.max(bestSoFar, value);
      } else {
        bestSoFar = Math.min(bestSoFar, value);
      }
      bestValues.push(bestSoFar);

      // Calculate improvement (difference between current best and previous best)
      const improvement = index === 0 ? 0 : bestSoFar - bestValues[index - 1];
      // For MIN mode, improvement is when the value decreases, so we need to flip the sign
      const actualImprovement = currentTargetMode === "MIN" ? -improvement : improvement;
      improvements.push(actualImprovement);

      // Calculate regret (distance from theoretical optimum)
      const currentRegret = currentTargetMode === "MAX" ?
        theoreticalOptimum - bestSoFar :
        bestSoFar - theoreticalOptimum;
      regret.push(Math.max(0, currentRegret));
    });

    processedData.push({
      iterations,
      bestValues,
      currentValues,
      improvements,
      regret,
      targetName: target,
      theoreticalOptimum,
      finalBest: bestSoFar
    });
  });

  return processedData;
}

/**
 * Calculate convergence statistics
 */
function calculateStats(convergenceData, optimization) {
  return convergenceData.map(data => {
    // Get the correct target mode for this specific target
    const currentTargetMode = getTargetMode(optimization, data.targetName);

    const totalIterations = data.iterations.length;

    // Calculate total improvement correctly based on target mode
    const rawImprovement = data.bestValues[totalIterations - 1] - data.bestValues[0];
    const totalImprovement = currentTargetMode === "MIN" ? -rawImprovement : rawImprovement;

    const improvementRate = totalImprovement / totalIterations;
    const lastImprovement = data.iterations.findLastIndex(i =>
      data.improvements[i - 1] > 0
    );
    const plateauLength = totalIterations - lastImprovement - 1;

    return {
      targetName: data.targetName,
      totalIterations,
      totalImprovement,
      improvementRate,
      plateauLength,
      currentBest: data.bestValues[totalIterations - 1],
      currentRegret: data.regret[totalIterations - 1],
      theoreticalOptimum: data.theoreticalOptimum,
      finalBest: data.finalBest,
      improvementCount: data.improvements.filter(imp => imp > 0.001).length,
      noImprovementCount: data.improvements.filter(imp => Math.abs(imp) <= 0.001).length,
      regressionCount: data.improvements.filter(imp => imp < -0.001).length
    };
  });
}

/**
 * Validate convergence logic
 */
function validateConvergence(stats, convergenceData) {
  const validationResults = [];

  stats.forEach((stat, index) => {
    const data = convergenceData[index];
    const issues = [];

    // Check if best values are monotonic (non-decreasing for MAX, non-increasing for MIN)
    for (let i = 1; i < data.bestValues.length; i++) {
      if (data.bestValues[i] < data.bestValues[i-1]) {
        issues.push(`Best values not monotonic at iteration ${i+1}: ${data.bestValues[i]} < ${data.bestValues[i-1]}`);
      }
    }

    // Check if regret is non-negative
    const negativeRegret = data.regret.filter(r => r < 0);
    if (negativeRegret.length > 0) {
      issues.push(`Found ${negativeRegret.length} negative regret values`);
    }

    // Check if final regret is zero (should be for simple regret)
    if (data.regret[data.regret.length - 1] > 0.001) {
      issues.push(`Final regret is not zero: ${data.regret[data.regret.length - 1]}`);
    }

    // Check improvement calculation consistency
    let calculatedImprovements = 0;
    for (let i = 1; i < data.bestValues.length; i++) {
      if (data.bestValues[i] > data.bestValues[i-1]) {
        calculatedImprovements++;
      }
    }
    if (calculatedImprovements !== stat.improvementCount) {
      issues.push(`Improvement count mismatch: calculated ${calculatedImprovements}, reported ${stat.improvementCount}`);
    }

    validationResults.push({
      targetName: stat.targetName,
      isValid: issues.length === 0,
      issues
    });
  });

  return validationResults;
}

/**
 * Main analysis function
 */
async function analyzeLatestOptimization() {
  try {
    console.log("🔍 Analyzing latest optimization project...\n");

    // Get the latest optimization
    const optimizations = await db.select()
      .from(optimizationsTable)
      .orderBy(desc(optimizationsTable.createdAt))
      .limit(1);

    if (optimizations.length === 0) {
      console.log("❌ No optimizations found in database");
      return;
    }

    const optimization = optimizations[0];
    console.log(`📊 Latest Optimization: ${optimization.name}`);
    console.log(`   ID: ${optimization.id}`);
    console.log(`   Target: ${optimization.targetName} (${optimization.targetMode})`);
    console.log(`   Created: ${optimization.createdAt}`);
    console.log(`   Status: ${optimization.status}\n`);

    // Get measurements for this optimization
    const measurements = await db.select()
      .from(measurementsTable)
      .where(eq(measurementsTable.optimizationId, optimization.id))
      .orderBy(desc(measurementsTable.createdAt));

    console.log(`📈 Found ${measurements.length} measurements\n`);

    if (measurements.length === 0) {
      console.log("❌ No measurements found for this optimization");
      return;
    }

    // Check if it's multi-target
    const firstMeasurement = measurements[0];
    const isMultiTarget = firstMeasurement.targetValues && 
      typeof firstMeasurement.targetValues === 'object' &&
      Object.keys(firstMeasurement.targetValues).length > 1;

    const otherTargets = isMultiTarget ? 
      Object.keys(firstMeasurement.targetValues).filter(t => t !== optimization.targetName) : [];

    console.log(`🎯 Multi-target: ${isMultiTarget}`);
    if (isMultiTarget) {
      console.log(`   Other targets: ${otherTargets.join(', ')}\n`);
    }

    // Process convergence data
    console.log("🔄 Processing convergence data...\n");
    const convergenceData = processConvergenceData(
      measurements,
      optimization,
      optimization.targetName,
      optimization.targetMode,
      isMultiTarget,
      otherTargets
    );

    // Calculate statistics
    const stats = calculateStats(convergenceData, optimization);

    // Display results for each target
    stats.forEach((stat, index) => {
      const data = convergenceData[index];
      console.log(`📊 Target: ${stat.targetName}`);
      console.log(`   Total Iterations: ${stat.totalIterations}`);
      console.log(`   Current Best: ${stat.currentBest.toFixed(4)}`);
      console.log(`   Theoretical Optimum: ${stat.theoreticalOptimum.toFixed(4)}`);
      console.log(`   Total Improvement: ${stat.totalImprovement.toFixed(4)}`);
      console.log(`   Improvement Rate: ${stat.improvementRate.toFixed(4)}/iteration`);
      console.log(`   Plateau Length: ${stat.plateauLength} iterations`);
      console.log(`   Current Regret: ${stat.currentRegret.toFixed(4)}`);
      console.log(`   Improvements: ${stat.improvementCount}, No change: ${stat.noImprovementCount}, Regressions: ${stat.regressionCount}`);

      // Show detailed iteration data
      console.log(`   📈 Iteration Details:`);
      for (let i = 0; i < Math.min(data.iterations.length, 10); i++) {
        const improvement = data.improvements[i];
        const improvementType = improvement > 0.001 ? '🟢' : improvement < -0.001 ? '🔴' : '⚪';
        console.log(`      ${data.iterations[i]}: Value=${data.currentValues[i].toFixed(2)}, Best=${data.bestValues[i].toFixed(2)}, Improvement=${improvement.toFixed(3)} ${improvementType}`);
      }
      if (data.iterations.length > 10) {
        console.log(`      ... and ${data.iterations.length - 10} more iterations`);
      }
      console.log();
    });

    // Validate convergence logic
    console.log("✅ Validating convergence calculations...\n");
    const validationResults = validateConvergence(stats, convergenceData);

    validationResults.forEach(result => {
      console.log(`🎯 ${result.targetName}: ${result.isValid ? '✅ VALID' : '❌ INVALID'}`);
      if (!result.isValid) {
        result.issues.forEach(issue => {
          console.log(`   ⚠️  ${issue}`);
        });
      }
      console.log();
    });

    // Overall assessment
    const allValid = validationResults.every(r => r.isValid);
    console.log(`🏁 Overall Convergence Analysis: ${allValid ? '✅ ACCURATE' : '❌ HAS ISSUES'}\n`);

    // Recommendations
    console.log("💡 Recommendations:");
    stats.forEach(stat => {
      if (stat.plateauLength > 5) {
        console.log(`   📈 ${stat.targetName}: Consider stopping - plateau length is ${stat.plateauLength} iterations`);
      } else if (stat.improvementRate > 0) {
        console.log(`   📈 ${stat.targetName}: Continue optimizing - showing positive improvement rate`);
      } else {
        console.log(`   📈 ${stat.targetName}: Monitor closely - improvement rate is ${stat.improvementRate.toFixed(4)}`);
      }
    });

  } catch (error) {
    console.error("❌ Error analyzing optimization:", error);
  } finally {
    await sql.end();
  }
}

// Run the analysis
analyzeLatestOptimization();
