-- Migration: 0006_create_optimization_status_history.sql
-- Create optimization_status_history table for tracking status changes

-- Create the table
CREATE TABLE IF NOT EXISTS "optimization_status_history" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "optimization_id" uuid NOT NULL REFERENCES "optimizations"("id") ON DELETE CASCADE,
  "previous_status" text NOT NULL,
  "new_status" text NOT NULL,
  "reason" text,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "created_by" text NOT NULL
);

-- Create index for faster lookups by optimization_id
CREATE INDEX IF NOT EXISTS "idx_optimization_status_history_optimization_id" ON "optimization_status_history"("optimization_id");

-- Create index for faster lookups by new_status
CREATE INDEX IF NOT EXISTS "idx_optimization_status_history_new_status" ON "optimization_status_history"("new_status");

-- Add a comment to the table
COMMENT ON TABLE "optimization_status_history" IS 'Tracks the history of status changes for optimizations';
