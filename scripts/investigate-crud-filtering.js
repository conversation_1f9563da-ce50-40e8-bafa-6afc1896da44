#!/usr/bin/env node

// Investigation script for CRUD filtering discrepancy
// Usage: node scripts/investigate-crud-filtering.js

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, and } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Define schema inline to avoid import issues
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").defaultRandom().primaryKey(),
  userId: text("user_id").notNull(),
  name: text("name").notNull(),
  description: text("description"),
  optimizerId: text("optimizer_id").notNull().unique(),
  config: jsonb("config").notNull(),
  targetName: text("target_name").notNull(),
  targetMode: text("target_mode").notNull(),
  status: text("status").notNull().default("draft"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

const measurementsTable = pgTable("measurements", {
  id: uuid("id").defaultRandom().primaryKey(),
  optimizationId: uuid("optimization_id").references(() => optimizationsTable.id, { onDelete: "cascade" }).notNull(),
  parameters: jsonb("parameters").notNull(),
  targetValue: text("target_value").notNull(),
  targetValues: jsonb("target_values"),
  isRecommended: boolean("is_recommended").notNull().default(true),
  batchId: text("batch_id"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

// Database connection
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  console.error("DATABASE_URL environment variable is required");
  process.exit(1);
}

const client = postgres(connectionString, {
  max: 1,
  ssl: process.env.DATABASE_SSL === "true" ? { rejectUnauthorized: false } : false
});

const db = drizzle(client, {
  schema: {
    optimizations: optimizationsTable,
    measurements: measurementsTable
  }
});

async function investigateCRUDFiltering() {
  try {
    console.log("🔍 INVESTIGATING CRUD FILTERING DISCREPANCY");
    console.log("=" .repeat(60));

    // First, let's see all optimizations
    const allOptimizations = await db
      .select()
      .from(optimizationsTable)
      .orderBy(optimizationsTable.createdAt);

    console.log(`📋 Found ${allOptimizations.length} optimizations in database:`);
    allOptimizations.forEach((opt, index) => {
      console.log(`  ${index + 1}. "${opt.name}" (ID: ${opt.optimizerId})`);
    });

    // Find the "Test CRUD Filtering2" optimization
    let optimization = await db
      .select()
      .from(optimizationsTable)
      .where(eq(optimizationsTable.name, "Test CRUD Filtering2"))
      .limit(1);

    if (optimization.length === 0) {
      // Try to find the most recent one with CRUD in the name
      const allOpts = await db.select().from(optimizationsTable).orderBy(optimizationsTable.createdAt);
      const found = allOpts.reverse().find(opt =>
        opt.name.toLowerCase().includes("crud filtering")
      );

      if (found) {
        console.log(`🔍 Found CRUD filtering optimization: "${found.name}"`);
        optimization = [found];
      } else {
        console.log("❌ No optimization with 'CRUD filtering' in name found");
        return;
      }
    }

    const opt = optimization[0];
    console.log("✅ Found optimization:");
    console.log("  - ID:", opt.id);
    console.log("  - Optimizer ID:", opt.optimizerId);
    console.log("  - Name:", opt.name);
    console.log("  - Status:", opt.status);
    console.log("  - Created:", opt.createdAt);
    console.log("  - Updated:", opt.updatedAt);

    // Get the configuration
    console.log("\n📊 CONFIGURATION:");
    console.log("  - Config:", JSON.stringify(opt.config, null, 2));

    // Get all measurements for this optimization
    const measurements = await db
      .select()
      .from(measurementsTable)
      .where(eq(measurementsTable.optimizationId, opt.id))
      .orderBy(measurementsTable.createdAt);

    console.log(`\n📈 MEASUREMENTS (${measurements.length} total):`);
    
    measurements.forEach((measurement, index) => {
      console.log(`\n  ${index + 1}. Measurement ${measurement.id}:`);
      console.log(`     - Parameters:`, JSON.stringify(measurement.parameters, null, 6));
      console.log(`     - Target Value:`, measurement.targetValue);
      console.log(`     - Target Values:`, JSON.stringify(measurement.targetValues, null, 6));
      console.log(`     - Is Recommended:`, measurement.isRecommended);
      console.log(`     - Batch ID:`, measurement.batchId);
      console.log(`     - Created:`, measurement.createdAt);
    });

    // Analyze the specific case mentioned
    console.log("\n🔬 ANALYZING SPECIFIC CASE:");
    console.log("Looking for measurement with x4 = 27.097245441269315...");
    
    const problematicMeasurement = measurements.find(m => {
      const params = m.parameters;
      return params.x4 && Math.abs(params.x4 - 27.097245441269315) < 0.001;
    });

    if (problematicMeasurement) {
      console.log("🎯 Found problematic measurement:");
      console.log("  - ID:", problematicMeasurement.id);
      console.log("  - Parameters:", JSON.stringify(problematicMeasurement.parameters, null, 4));
      console.log("  - Target Values:", JSON.stringify(problematicMeasurement.targetValues, null, 4));
      
      // Check for NaN or infinite values
      const params = problematicMeasurement.parameters;
      console.log("\n🔍 CHECKING FOR NaN/INFINITE VALUES:");
      Object.entries(params).forEach(([key, value]) => {
        const isNaN = Number.isNaN(value);
        const isInfinite = !Number.isFinite(value);
        const isVerySmall = Math.abs(value) < 1e-10;
        
        console.log(`  - ${key}: ${value}`);
        console.log(`    * Is NaN: ${isNaN}`);
        console.log(`    * Is Infinite: ${isInfinite}`);
        console.log(`    * Is Very Small (< 1e-10): ${isVerySmall}`);
        
        if (isVerySmall && !isNaN && Number.isFinite(value)) {
          console.log(`    * Scientific notation: ${value.toExponential()}`);
        }
      });

      // Check bounds compliance for x4
      const x4Value = params.x4;
      console.log(`\n📏 BOUNDS ANALYSIS FOR x4:`);
      console.log(`  - Current x4 value: ${x4Value}`);
      console.log(`  - Original bounds: [0, 100]`);
      console.log(`  - New bounds: [30, 100]`);
      console.log(`  - Within original bounds: ${x4Value >= 0 && x4Value <= 100}`);
      console.log(`  - Within new bounds: ${x4Value >= 30 && x4Value <= 100}`);
      console.log(`  - Should be filtered: ${x4Value < 30}`);
    } else {
      console.log("❌ Could not find the specific problematic measurement");
    }

    // Check for measurements that should be filtered by x4 bounds change
    console.log("\n📊 FILTERING ANALYSIS:");
    console.log("Checking which measurements should be filtered by x4 bounds change (0-100 → 30-100):");
    
    let shouldBeFiltered = 0;
    let shouldBeRetained = 0;
    let hasNaNInfinite = 0;
    
    measurements.forEach((measurement, index) => {
      const params = measurement.parameters;
      const x4Value = params.x4;
      
      // Check for NaN/infinite values
      const hasNaN = Object.values(params).some(v => Number.isNaN(v) || !Number.isFinite(v));
      
      if (hasNaN) {
        hasNaNInfinite++;
        console.log(`  ${index + 1}. ❌ HAS NaN/INFINITE - Should be filtered`);
      } else if (x4Value < 30) {
        shouldBeFiltered++;
        console.log(`  ${index + 1}. ❌ x4=${x4Value} < 30 - Should be filtered`);
      } else {
        shouldBeRetained++;
        console.log(`  ${index + 1}. ✅ x4=${x4Value} >= 30 - Should be retained`);
      }
    });

    console.log("\n📈 SUMMARY:");
    console.log(`  - Total measurements: ${measurements.length}`);
    console.log(`  - Should be retained: ${shouldBeRetained}`);
    console.log(`  - Should be filtered (x4 < 30): ${shouldBeFiltered}`);
    console.log(`  - Should be filtered (NaN/Infinite): ${hasNaNInfinite}`);
    console.log(`  - Total should be filtered: ${shouldBeFiltered + hasNaNInfinite}`);
    
    console.log("\n🔍 BACKEND RESPONSE COMPARISON:");
    console.log("  - Backend reported: 2 retained, 8 filtered");
    console.log(`  - Database analysis: ${shouldBeRetained} should be retained, ${shouldBeFiltered + hasNaNInfinite} should be filtered`);
    
    if (shouldBeRetained === 2 && (shouldBeFiltered + hasNaNInfinite) === 8) {
      console.log("✅ MATCH: Database analysis matches backend response");
    } else {
      console.log("❌ MISMATCH: Database analysis does not match backend response");
    }

  } catch (error) {
    console.error("❌ Error during investigation:", error);
  } finally {
    await client.end();
  }
}

// Run the investigation
investigateCRUDFiltering().catch(console.error);
