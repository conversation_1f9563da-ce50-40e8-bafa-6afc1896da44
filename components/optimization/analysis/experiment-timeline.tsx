"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { Clock } from "lucide-react"
import { ProgressVisualization } from "./experiment-timeline/progress-visualization"

interface ExperimentTimelineProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
}

export function ExperimentTimeline({
  optimization,
  measurements
}: ExperimentTimelineProps) {
  const [activeTab, setActiveTab] = useState("progress")
  const [selectedExperiment, setSelectedExperiment] = useState<
    number | undefined
  >(undefined)

  // Get all target names
  const targetNames = getTargetNames(optimization, measurements)

  // Handle experiment selection
  const handleExperimentSelect = (experimentIndex: number) => {
    console.log("Selected experiment:", experimentIndex)
    setSelectedExperiment(experimentIndex)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-lg">
          <Clock className="text-primary mr-2 size-5" />
          Experiment Timeline
        </CardTitle>
        <CardDescription>
          Visualize the optimization journey over time
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 grid grid-cols-1">
            <TabsTrigger value="progress">Progress Visualization</TabsTrigger>
          </TabsList>

          {/* Progress Visualization */}
          <TabsContent value="progress" className="space-y-4">
            <ProgressVisualization
              optimization={optimization}
              measurements={measurements}
              targetNames={targetNames}
              onExperimentSelect={handleExperimentSelect}
              selectedExperiment={selectedExperiment}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

// Helper function to extract target names from optimization and measurements
function getTargetNames(
  optimization: SelectOptimization,
  measurements: SelectMeasurement[]
): string[] {
  const targetNames: string[] = []

  // Add primary target
  if (optimization.targetName) {
    targetNames.push(optimization.targetName)
  }

  // Add additional targets from multi-target measurements
  if (measurements.length > 0 && measurements[0].targetValues) {
    const firstMeasurement = measurements[0]
    const targetValuesObj = firstMeasurement.targetValues as Record<
      string,
      number
    >

    Object.keys(targetValuesObj).forEach(target => {
      if (!targetNames.includes(target)) {
        targetNames.push(target)
      }
    })
  }

  // Check if target configuration is available in optimization config
  if (optimization.config && typeof optimization.config === "object") {
    const config = optimization.config as any
    if (config.target_config && Array.isArray(config.target_config)) {
      config.target_config.forEach((targetConfig: any) => {
        if (targetConfig.name && !targetNames.includes(targetConfig.name)) {
          targetNames.push(targetConfig.name)
        }
      })
    }
  }

  return targetNames
}
