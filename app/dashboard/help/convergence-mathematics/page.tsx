/*
This server page provides detailed documentation about convergence analysis mathematics.
*/

"use server"

import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  ArrowLeft,
  Calculator,
  TrendingUp,
  Target,
  BarChart3,
  Info,
  AlertTriangle,
  BookOpen,
  Sigma
} from "lucide-react"
import Link from "next/link"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default async function ConvergenceMathematicsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/help">
            <ArrowLeft className="mr-2 size-4" />
            Back to Help Center
          </Link>
        </Button>
      </div>

      <h1 className="flex items-center gap-2 text-3xl font-bold">
        <Calculator className="text-primary size-8" />
        Convergence Analysis Mathematics
      </h1>

      <p className="text-muted-foreground text-lg">
        Understanding the mathematical foundations and calculations behind
        convergence analysis, regret computation, and optimization
        recommendations
      </p>

      {/* Overview */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="text-primary size-5" />
              Mathematical Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              Convergence analysis uses several mathematical concepts to track
              optimization progress. This section explains the calculations in
              detail, helping you understand exactly how the system determines
              convergence, regret, and recommendations.
            </p>

            <Alert>
              <Info className="size-4" />
              <AlertTitle>Mathematical Notation</AlertTitle>
              <AlertDescription className="text-sm">
                We use standard mathematical notation where possible. Variables
                are defined clearly, and examples use real numbers to illustrate
                concepts.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      {/* Core Calculations */}
      <div className="mt-8 space-y-8">
        <h2 className="text-2xl font-bold">Core Mathematical Calculations</h2>

        {/* Best Value Tracking */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="size-5 text-blue-500" />
              Best Value Tracking (Running Optimum)
            </CardTitle>
            <CardDescription>
              How the system tracks the best solution found so far
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium">Mathematical Definition</h4>
              <div className="bg-muted rounded-lg p-4 font-mono text-sm">
                <p>
                  <strong>For Maximization (MAX mode):</strong>
                </p>
                <p>B(t) = max(f(x1), f(x2), ..., f(xt))</p>
                <br />
                <p>
                  <strong>For Minimization (MIN mode):</strong>
                </p>
                <p>B(t) = min(f(x1), f(x2), ..., f(xt))</p>
              </div>

              <div className="space-y-2">
                <p>
                  <strong>Where:</strong>
                </p>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <code>B(t)</code> = Best value found up to iteration t
                  </li>
                  <li>
                    <code>f(xi)</code> = Objective function value at experiment
                    i
                  </li>
                  <li>
                    <code>t</code> = Current iteration number
                  </li>
                  <li>
                    <code>xi</code> = Parameter configuration for experiment i
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                <h4 className="mb-2 font-medium text-blue-800">
                  Example Calculation
                </h4>
                <div className="space-y-1 text-sm text-blue-700">
                  <p>
                    <strong>Experiment values (MAX mode):</strong> [21, 41, 52,
                    63, 87, 785, 574, 85]
                  </p>
                  <p>
                    <strong>Running best values:</strong>
                  </p>
                  <ul className="list-disc space-y-1 pl-5">
                    <li>B(1) = max(21) = 21</li>
                    <li>B(2) = max(21, 41) = 41</li>
                    <li>B(3) = max(21, 41, 52) = 52</li>
                    <li>B(6) = max(21, 41, 52, 63, 87, 785) = 785</li>
                    <li>B(7) = max(..., 574) = 785 (no improvement)</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Improvement Calculation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="size-5 text-green-500" />
              Improvement Calculation
            </CardTitle>
            <CardDescription>
              How improvements between iterations are calculated
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium">Mathematical Definition</h4>
              <div className="bg-muted rounded-lg p-4 font-mono text-sm">
                <p>
                  <strong>Raw Improvement:</strong>
                </p>
                <p>I_raw(t) = B(t) - B(t-1)</p>
                <br />
                <p>
                  <strong>Adjusted Improvement (for MIN mode):</strong>
                </p>
                <p>I(t) = I_raw(t) × mode_multiplier</p>
                <br />
                <p>
                  <strong>Where mode_multiplier:</strong>
                </p>
                <p>• MAX mode: +1 (positive improvement = better)</p>
                <p>• MIN mode: -1 (negative improvement = better)</p>
              </div>

              <div className="space-y-2">
                <p>
                  <strong>Interpretation:</strong>
                </p>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <code>I(t) &gt; 0</code> = Improvement found (green bar)
                  </li>
                  <li>
                    <code>I(t) = 0</code> = No improvement (gray bar)
                  </li>
                  <li>
                    <code>I(t) &lt; 0</code> = Regression (red bar, rare in
                    practice)
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                <h4 className="mb-2 font-medium text-green-800">
                  Example Calculation (MAX mode)
                </h4>
                <div className="space-y-1 text-sm text-green-700">
                  <p>
                    <strong>Best values:</strong> [21, 41, 52, 63, 87, 785, 785,
                    785]
                  </p>
                  <p>
                    <strong>Improvements:</strong>
                  </p>
                  <ul className="list-disc space-y-1 pl-5">
                    <li>I(1) = 0 (baseline)</li>
                    <li>I(2) = 41 - 21 = 20 (improvement)</li>
                    <li>I(3) = 52 - 41 = 11 (improvement)</li>
                    <li>I(6) = 785 - 87 = 698 (major improvement)</li>
                    <li>I(7) = 785 - 785 = 0 (no improvement)</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Regret Calculation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="size-5 text-orange-500" />
              Regret Calculation
            </CardTitle>
            <CardDescription>
              How distance from the optimal solution is measured
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium">Mathematical Definition</h4>
              <div className="bg-muted rounded-lg p-4 font-mono text-sm">
                <p>
                  <strong>Simple Regret:</strong>
                </p>
                <p>R(t) = |f* - B(t)|</p>
                <br />
                <p>
                  <strong>For Maximization:</strong>
                </p>
                <p>R(t) = max(0, f* - B(t))</p>
                <br />
                <p>
                  <strong>For Minimization:</strong>
                </p>
                <p>R(t) = max(0, B(t) - f*)</p>
              </div>

              <div className="space-y-2">
                <p>
                  <strong>Where:</strong>
                </p>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    <code>R(t)</code> = Regret at iteration t
                  </li>
                  <li>
                    <code>f*</code> = Theoretical optimum (best value seen so
                    far)
                  </li>
                  <li>
                    <code>B(t)</code> = Best value found up to iteration t
                  </li>
                </ul>
              </div>

              <Alert>
                <AlertTriangle className="size-4" />
                <AlertTitle>Important Note</AlertTitle>
                <AlertDescription className="text-sm">
                  Since we don&apos;t know the true global optimum, we use the
                  best value observed in our experiments as f*. This means the
                  final regret is always 0.
                </AlertDescription>
              </Alert>

              <div className="rounded-lg border border-orange-200 bg-orange-50 p-4">
                <h4 className="mb-2 font-medium text-orange-800">
                  Example Calculation (MAX mode)
                </h4>
                <div className="space-y-1 text-sm text-orange-700">
                  <p>
                    <strong>Theoretical optimum f* = 785</strong> (best value
                    seen)
                  </p>
                  <p>
                    <strong>Best values:</strong> [21, 41, 52, 63, 87, 785, 785,
                    785]
                  </p>
                  <p>
                    <strong>Regret values:</strong>
                  </p>
                  <ul className="list-disc space-y-1 pl-5">
                    <li>R(1) = 785 - 21 = 764</li>
                    <li>R(2) = 785 - 41 = 744</li>
                    <li>R(3) = 785 - 52 = 733</li>
                    <li>R(6) = 785 - 785 = 0</li>
                    <li>R(7) = 785 - 785 = 0</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statistical Measures */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sigma className="text-primary size-5" />
              Statistical Measures
            </CardTitle>
            <CardDescription>
              Key statistics derived from the convergence data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-medium">Total Improvement</h4>
                <div className="bg-muted rounded-lg p-3 font-mono text-sm">
                  <p>Total_Improvement = B(T) - B(1)</p>
                </div>
                <p className="text-muted-foreground text-sm">
                  Measures the overall progress from first to final iteration.
                </p>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Improvement Rate</h4>
                <div className="bg-muted rounded-lg p-3 font-mono text-sm">
                  <p>Rate = Total_Improvement / T</p>
                </div>
                <p className="text-muted-foreground text-sm">
                  Average improvement per iteration across all experiments.
                </p>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Plateau Length</h4>
                <div className="bg-muted rounded-lg p-3 font-mono text-sm">
                  <p>Plateau = T - last_improvement_iteration</p>
                </div>
                <p className="text-muted-foreground text-sm">
                  Number of consecutive iterations without improvement.
                </p>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Improvement Count</h4>
                <div className="bg-muted rounded-lg p-3 font-mono text-sm">
                  <p>Count = Σ(I(t) &gt; threshold)</p>
                </div>
                <p className="text-muted-foreground text-sm">
                  Number of iterations that found meaningful improvements
                  (threshold = 0.001).
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recommendation System Mathematics */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="text-primary size-5" />
              Recommendation System Mathematics
            </CardTitle>
            <CardDescription>
              Mathematical logic behind optimization recommendations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium">Decision Logic Framework</h4>
              <p className="text-muted-foreground text-sm">
                The recommendation system uses a multi-criteria decision
                framework that combines several mathematical indicators to
                provide robust guidance.
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-medium">Plateau Detection Algorithm</h4>
                <div className="bg-muted rounded-lg p-4 font-mono text-sm">
                  <p>
                    <strong>Plateau Length Calculation:</strong>
                  </p>
                  <p>P(t) = t - max&#123;i : I(i) &gt; ε, i ≤ t&#125;</p>
                  <br />
                  <p>
                    <strong>Where:</strong>
                  </p>
                  <p>• ε = 0.001 (improvement threshold)</p>
                  <p>• I(i) = improvement at iteration i</p>
                  <p>• P(t) = plateau length at iteration t</p>
                </div>

                <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                  <h5 className="mb-2 font-medium text-yellow-800">
                    Decision Thresholds
                  </h5>
                  <div className="space-y-1 text-sm text-yellow-700">
                    <p>
                      <strong>For small datasets (≤25 samples):</strong>
                    </p>
                    <ul className="list-disc space-y-1 pl-5">
                      <li>P(t) ≥ 7: Strong stop recommendation</li>
                      <li>5 ≤ P(t) &lt; 7: Consider stopping</li>
                      <li>P(t) &lt; 5: Continue optimization</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Regret-Based Convergence</h4>
                <div className="bg-muted rounded-lg p-4 font-mono text-sm">
                  <p>
                    <strong>Convergence Indicator:</strong>
                  </p>
                  <p>C(t) = (R(t) &lt; δ) ∧ (P(t) ≥ k)</p>
                  <br />
                  <p>
                    <strong>Where:</strong>
                  </p>
                  <p>• δ = 0.01 (regret threshold)</p>
                  <p>• k = 3 (minimum plateau length)</p>
                  <p>• ∧ = logical AND operator</p>
                </div>

                <p className="text-muted-foreground text-sm">
                  This combines low regret (close to optimum) with plateau
                  detection to identify convergence with high confidence.
                </p>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Budget Awareness Function</h4>
                <div className="bg-muted rounded-lg p-4 font-mono text-sm">
                  <p>
                    <strong>Budget Pressure:</strong>
                  </p>
                  <p>B_pressure(t) = t / T_max</p>
                  <br />
                  <p>
                    <strong>Adjusted Threshold:</strong>
                  </p>
                  <p>P_threshold(t) = P_base × (1 - α × B_pressure(t))</p>
                  <br />
                  <p>
                    <strong>Where:</strong>
                  </p>
                  <p>• T_max = 25 (small dataset limit)</p>
                  <p>• P_base = 7 (base plateau threshold)</p>
                  <p>• α = 0.3 (budget sensitivity parameter)</p>
                </div>

                <p className="text-muted-foreground text-sm">
                  As you approach the 25-sample limit, the system becomes more
                  conservative, recommending stopping with shorter plateaus.
                </p>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">
                  Multi-Target Decision Aggregation
                </h4>
                <div className="bg-muted rounded-lg p-4 font-mono text-sm">
                  <p>
                    <strong>Conservative Aggregation:</strong>
                  </p>
                  <p>Decision = max&#123;D1, D2, ..., Dn&#125;</p>
                  <br />
                  <p>
                    <strong>Where:</strong>
                  </p>
                  <p>• Di ∈ &#123;continue=0, consider=1, stop=2&#125;</p>
                  <p>• n = number of targets</p>
                  <p>• max = most conservative recommendation</p>
                </div>

                <p className="text-muted-foreground text-sm">
                  For multi-target optimizations, the system takes the most
                  conservative recommendation across all targets to avoid
                  premature stopping.
                </p>
              </div>
            </div>

            <Alert>
              <Calculator className="size-4" />
              <AlertTitle>Implementation Note</AlertTitle>
              <AlertDescription className="text-sm">
                These mathematical formulations are implemented in the
                convergence analysis component and automatically applied to your
                optimization data. The thresholds have been tuned specifically
                for small dataset optimization scenarios.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      {/* Practical Examples */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="text-primary size-5" />
              Worked Example
            </CardTitle>
            <CardDescription>
              Complete mathematical walkthrough using real optimization data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
              <h4 className="mb-3 font-medium text-blue-800">
                Example Dataset (MAX mode)
              </h4>
              <div className="space-y-2 text-sm text-blue-700">
                <p>
                  <strong>Experiment values:</strong> [21, 41, 52, 63, 87, 785,
                  574, 85, 14, 97, 123, 456, 234, 345]
                </p>
                <p>
                  <strong>Target:</strong> Maximize objective function
                </p>
                <p>
                  <strong>Total iterations:</strong> 14
                </p>
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-medium">Step 1: Calculate Running Best</h4>
                <div className="bg-muted rounded-lg p-3 font-mono text-sm">
                  <p>B(1) = 21</p>
                  <p>B(2) = max(21,41) = 41</p>
                  <p>B(3) = max(21,41,52) = 52</p>
                  <p>B(4) = max(...,63) = 63</p>
                  <p>B(5) = max(...,87) = 87</p>
                  <p>B(6) = max(...,785) = 785</p>
                  <p>B(7) = max(...,574) = 785</p>
                  <p>...</p>
                  <p>B(14) = 785</p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Step 2: Calculate Improvements</h4>
                <div className="bg-muted rounded-lg p-3 font-mono text-sm">
                  <p>I(1) = 0 (baseline)</p>
                  <p>I(2) = 41-21 = 20 ✓</p>
                  <p>I(3) = 52-41 = 11 ✓</p>
                  <p>I(4) = 63-52 = 11 ✓</p>
                  <p>I(5) = 87-63 = 24 ✓</p>
                  <p>I(6) = 785-87 = 698 ✓</p>
                  <p>I(7) = 785-785 = 0</p>
                  <p>...</p>
                  <p>I(14) = 0</p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Step 3: Calculate Regret</h4>
                <div className="bg-muted rounded-lg p-3 font-mono text-sm">
                  <p>f* = 785 (best seen)</p>
                  <p>R(1) = 785-21 = 764</p>
                  <p>R(2) = 785-41 = 744</p>
                  <p>R(3) = 785-52 = 733</p>
                  <p>...</p>
                  <p>R(6) = 785-785 = 0</p>
                  <p>R(7) = 785-785 = 0</p>
                  <p>...</p>
                  <p>R(14) = 0</p>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Step 4: Calculate Statistics</h4>
                <div className="bg-muted rounded-lg p-3 font-mono text-sm">
                  <p>Total_Improvement = 785-21 = 764</p>
                  <p>Improvement_Rate = 764/14 = 54.6</p>
                  <p>Last_Improvement = iteration 6</p>
                  <p>Plateau_Length = 14-6 = 8</p>
                  <p>Improvement_Count = 5</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">Step 5: Generate Recommendation</h4>
              <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                <div className="space-y-2 text-sm text-blue-700">
                  <p>
                    <strong>Decision Logic:</strong>
                  </p>
                  <ul className="list-disc space-y-1 pl-5">
                    <li>Plateau_Length = 8 ≥ 7 → Stop recommendation</li>
                    <li>
                      Current_Regret = 0 &lt; 0.01 → Convergence confirmed
                    </li>
                    <li>Budget_Usage = 14/25 = 56% → Moderate pressure</li>
                  </ul>
                  <p>
                    <strong>Result:</strong> &quot;Your optimization appears to
                    have converged&quot;
                  </p>
                  <p>
                    <strong>Reasoning:</strong> &quot;No improvement for 8
                    consecutive experiments&quot;
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Mathematical References */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="text-primary size-5" />
              Mathematical References &amp; Further Reading
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <h4 className="font-medium">Key Concepts</h4>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>
                  <strong>Simple Regret:</strong> Measures distance from the
                  best solution found
                </li>
                <li>
                  <strong>Cumulative Regret:</strong> Sum of regrets over all
                  iterations (not used here)
                </li>
                <li>
                  <strong>Convergence Rate:</strong> How quickly the
                  optimization approaches the optimum
                </li>
                <li>
                  <strong>Plateau Detection:</strong> Statistical method for
                  identifying stagnation
                </li>
                <li>
                  <strong>Multi-objective Optimization:</strong> Handling
                  multiple competing targets
                </li>
              </ul>
            </div>

            <Alert>
              <Info className="size-4" />
              <AlertTitle>Implementation Details</AlertTitle>
              <AlertDescription className="text-sm">
                The mathematical formulations shown here are simplified for
                clarity. The actual implementation includes additional numerical
                stability checks, edge case handling, and optimizations for
                real-time calculation.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
