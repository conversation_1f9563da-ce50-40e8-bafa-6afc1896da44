/*
This client page provides help center content with development indicators.
*/

"use client"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { BRAND } from "@/lib/constants"
import {
  HelpCircle,
  Search,
  FileText,
  Book,
  Lightbulb,
  PlayCircle,
  MessagesSquare
} from "lucide-react"
import { isFeatureEnabled, isFeatureInDevelopment } from "@/lib/feature-flags"
import { DevelopmentBadge } from "@/components/ui/development-badge"
import { toast } from "@/components/ui/use-toast"
import { DashboardThemeWrapperClient } from "@/components/dashboard/dashboard-theme-wrapper-client"

export default function HelpCenterPage() {
  // Handle clicks on placeholder content
  const handlePlaceholderClick = (featureKey: string, featureName: string) => {
    if (!isFeatureEnabled(featureKey as any)) {
      toast({
        title: "🔧 Feature in Development",
        description: `${featureName} is currently being created. Coming soon!`,
        variant: "default"
      })
    }
  }

  // Handle search functionality
  const handleSearch = () => {
    if (!isFeatureEnabled("helpCenterSearch")) {
      toast({
        title: "🔧 Feature in Development",
        description:
          "Search functionality is currently being implemented. Coming soon!",
        variant: "default"
      })
    }
  }

  // Handle contact support
  const handleContactSupport = () => {
    if (!isFeatureEnabled("helpCenterContactSupport")) {
      toast({
        title: "🔧 Feature in Development",
        description:
          "Contact Support functionality is currently being set up. Coming soon!",
        variant: "default"
      })
      return
    }
    // When enabled, let the button work normally (no custom handler needed)
    // The original functionality should be preserved
  }

  // Handle schedule demo
  const handleScheduleDemo = () => {
    if (!isFeatureEnabled("helpCenterScheduleDemo")) {
      toast({
        title: "🔧 Feature in Development",
        description:
          "Schedule a Demo functionality is currently being configured. Coming soon!",
        variant: "default"
      })
    }
  }
  return (
    <DashboardThemeWrapperClient>
      <h1 className="text-3xl font-bold">Help Center</h1>

      {/* Search bar */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="text-muted-foreground absolute left-3 top-1/2 size-5 -translate-y-1/2" />
            <Input
              placeholder="Search help topics..."
              className="pl-10"
              disabled={isFeatureInDevelopment("helpCenterSearch")}
            />
            <Button
              className="absolute right-1 top-1/2 flex h-7 -translate-y-1/2 items-center gap-1"
              onClick={handleSearch}
              disabled={isFeatureInDevelopment("helpCenterSearch")}
            >
              Search
              {isFeatureInDevelopment("helpCenterSearch") && (
                <DevelopmentBadge variant="icon" size="sm" />
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick help categories */}
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <Card className="overflow-hidden">
          <div className="bg-primary text-primary-foreground p-4">
            <Book className="size-6" />
          </div>
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>New to {BRAND.NAME}? Start here!</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li>
                <button
                  onClick={() =>
                    handlePlaceholderClick(
                      "helpCenterPlatformOverview",
                      "Platform Overview"
                    )
                  }
                  className="flex items-center gap-2 text-left text-blue-600 hover:underline dark:text-blue-400"
                >
                  Platform overview
                  {isFeatureInDevelopment("helpCenterPlatformOverview") && (
                    <DevelopmentBadge variant="icon" size="sm" />
                  )}
                </button>
              </li>
              <li>
                <button
                  onClick={() =>
                    handlePlaceholderClick(
                      "helpCenterFirstExperiment",
                      "Setting up your first experiment"
                    )
                  }
                  className="flex items-center gap-2 text-left text-blue-600 hover:underline dark:text-blue-400"
                >
                  Setting up your first experiment
                  {isFeatureInDevelopment("helpCenterFirstExperiment") && (
                    <DevelopmentBadge variant="icon" size="sm" />
                  )}
                </button>
              </li>
              <li>
                <button
                  onClick={() =>
                    handlePlaceholderClick(
                      "helpCenterDashboardGuide",
                      "Understanding the dashboard"
                    )
                  }
                  className="flex items-center gap-2 text-left text-blue-600 hover:underline dark:text-blue-400"
                >
                  Understanding the dashboard
                  {isFeatureInDevelopment("helpCenterDashboardGuide") && (
                    <DevelopmentBadge variant="icon" size="sm" />
                  )}
                </button>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <div className="bg-primary text-primary-foreground p-4">
            <FileText className="size-6" />
          </div>
          <CardHeader>
            <CardTitle>Documentation</CardTitle>
            <CardDescription>Detailed guides and references</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href="/dashboard/help/optimization-results"
                  className="text-blue-600 hover:underline dark:text-blue-400"
                >
                  Understanding Optimization Results
                </Link>
              </li>
              <li>
                <Link
                  href="/dashboard/help/convergence-analysis"
                  className="text-blue-600 hover:underline dark:text-blue-400"
                >
                  Convergence Analysis Guide
                </Link>
              </li>
              <li>
                <Link
                  href="/dashboard/help/convergence-mathematics"
                  className="text-blue-600 hover:underline dark:text-blue-400"
                >
                  Convergence Mathematics & Calculations
                </Link>
              </li>
              <li>
                <Link
                  href="/dashboard/help/api-reference"
                  className="text-blue-600 hover:underline dark:text-blue-400"
                >
                  API Reference
                </Link>
              </li>
              <li>
                <button
                  onClick={() =>
                    handlePlaceholderClick(
                      "helpCenterDataImportExport",
                      "Data import/export"
                    )
                  }
                  className="flex items-center gap-2 text-left text-blue-600 hover:underline dark:text-blue-400"
                >
                  Data import/export
                  {isFeatureInDevelopment("helpCenterDataImportExport") && (
                    <DevelopmentBadge variant="icon" size="sm" />
                  )}
                </button>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <div className="bg-primary text-primary-foreground p-4">
            <PlayCircle className="size-6" />
          </div>
          <CardHeader>
            <CardTitle>Video Tutorials</CardTitle>
            <CardDescription>Learn by watching</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li>
                <button
                  onClick={() =>
                    handlePlaceholderClick(
                      "helpCenterOptimizationWalkthrough",
                      "Optimization walkthrough"
                    )
                  }
                  className="flex items-center gap-2 text-left text-blue-600 hover:underline dark:text-blue-400"
                >
                  Optimization walkthrough
                  {isFeatureInDevelopment(
                    "helpCenterOptimizationWalkthrough"
                  ) && <DevelopmentBadge variant="icon" size="sm" />}
                </button>
              </li>
              <li>
                <button
                  onClick={() =>
                    handlePlaceholderClick(
                      "helpCenterAdvancedAnalytics",
                      "Advanced analytics"
                    )
                  }
                  className="flex items-center gap-2 text-left text-blue-600 hover:underline dark:text-blue-400"
                >
                  Advanced analytics
                  {isFeatureInDevelopment("helpCenterAdvancedAnalytics") && (
                    <DevelopmentBadge variant="icon" size="sm" />
                  )}
                </button>
              </li>
              <li>
                <button
                  onClick={() =>
                    handlePlaceholderClick(
                      "helpCenterResultsInterpretation",
                      "Results interpretation"
                    )
                  }
                  className="flex items-center gap-2 text-left text-blue-600 hover:underline dark:text-blue-400"
                >
                  Results interpretation
                  {isFeatureInDevelopment(
                    "helpCenterResultsInterpretation"
                  ) && <DevelopmentBadge variant="icon" size="sm" />}
                </button>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="size-5" />
            <span>Frequently Asked Questions</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              {
                question: "How do I set up a new experimental design?",
                answer:
                  "Navigate to the Experiments tab and click the 'New Experiment' button. Follow the guided setup process to define your parameters, constraints, and optimization objectives."
              },
              {
                question:
                  "What's the difference between experiments and optimizations?",
                answer:
                  "Experiments are for data collection and hypothesis testing. Optimizations use existing experimental data to find the optimal settings for your process or product."
              },
              {
                question: "How can I export my results?",
                answer:
                  "On any results page, look for the Export button in the top-right corner. You can export data in CSV, Excel, or PDF formats."
              },
              {
                question: "Can I integrate with my existing lab systems?",
                answer:
                  "Yes, we support integration with major LIMS and automation systems. See our documentation for API details and integration guides."
              }
            ].map((faq, index) => (
              <div key={index} className="border-b pb-4">
                <h3 className="mb-2 font-medium">{faq.question}</h3>
                <p className="text-muted-foreground text-sm">{faq.answer}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Contact support */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessagesSquare className="size-5" />
            <span>Need More Help?</span>
          </CardTitle>
          <CardDescription>
            Our support team is ready to assist you
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-4 sm:flex-row">
          <Button
            className="flex items-center gap-2"
            onClick={
              isFeatureInDevelopment("helpCenterContactSupport")
                ? handleContactSupport
                : undefined
            }
            disabled={isFeatureInDevelopment("helpCenterContactSupport")}
          >
            <HelpCircle className="size-4" />
            <span>Contact Support</span>
            {isFeatureInDevelopment("helpCenterContactSupport") && (
              <DevelopmentBadge variant="icon" size="sm" />
            )}
          </Button>
          <Button
            variant="outline"
            onClick={handleScheduleDemo}
            disabled={isFeatureInDevelopment("helpCenterScheduleDemo")}
            className="flex items-center gap-2"
          >
            <span>Schedule a Demo</span>
            {isFeatureInDevelopment("helpCenterScheduleDemo") && (
              <DevelopmentBadge variant="icon" size="sm" />
            )}
          </Button>
        </CardContent>
      </Card>
    </DashboardThemeWrapperClient>
  )
}
