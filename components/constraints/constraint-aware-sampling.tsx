"use client"

import React, { useState, useEffect } from "react"
import {
  Play,
  Download,
  <PERSON><PERSON><PERSON>,
  AlertCircle,
  CheckCircle,
  BarChart3,
  Zap,
  Clock
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"
import { DiscretizationTransparency } from "@/components/ui/discretization-transparency"

// Import from centralized constraints library
import {
  useConstraintAwareSampling,
  useConstraintContext,
  Constraint,
  Parameter,
  SamplingOptions,
  SamplingResult,
  getRecommendedSamplingStrategy,
  estimateConstraintFeasibility
} from "@/lib/constraints"

interface ConstraintAwareSamplingProps {
  parameters: Parameter[]
  constraints: Constraint[]
  onSamplesGenerated?: (samples: any[]) => void
  className?: string
}

export function ConstraintAwareSampling({
  parameters,
  constraints,
  onSamplesGenerated,
  className = ""
}: ConstraintAwareSamplingProps) {
  const [samplingOptions, setSamplingOptions] = useState<SamplingOptions>({
    strategy: "LHS",
    nSamples: 10,
    respectConstraints: constraints.length > 0,
    maxAttempts: 1000,
    tolerance: 1e-6
  })

  const [showAdvanced, setShowAdvanced] = useState(false)

  // Use centralized constraint-aware sampling
  const {
    isGenerating,
    samplingResult,
    feasibilityEstimate,
    generateSamples,
    estimateFeasibility,
    clearResults
  } = useConstraintAwareSampling()

  // Use constraint context for validation
  const { isValid, hasConstraints, constraintCount, feasibilityRatio } =
    useConstraintContext(parameters, constraints, {
      validationEnabled: true,
      samplingEnabled: true
    })

  // Update recommended strategy when constraints change
  useEffect(() => {
    if (parameters.length > 0) {
      const recommended = getRecommendedSamplingStrategy(
        constraints.length,
        parameters.length,
        constraints.some(c => c.type === "DiscreteCustomConstraint")
      )
      setSamplingOptions(prev => ({
        ...prev,
        ...recommended,
        respectConstraints: constraints.length > 0
      }))
    }
  }, [constraints.length, parameters.length, constraints])

  // Estimate feasibility when constraints change
  useEffect(() => {
    if (hasConstraints && parameters.length > 0) {
      estimateFeasibility(parameters, constraints)
    }
  }, [constraints, parameters, hasConstraints, estimateFeasibility])

  const handleGenerateSamples = async () => {
    if (parameters.length === 0) {
      toast({
        title: "No parameters",
        description: "Please define parameters before generating samples.",
        variant: "destructive"
      })
      return
    }

    if (hasConstraints && !isValid) {
      toast({
        title: "Invalid constraints",
        description: "Please fix constraint validation errors before sampling.",
        variant: "destructive"
      })
      return
    }

    try {
      const result = await generateSamples(
        parameters,
        constraints,
        samplingOptions
      )

      if (result.samples.length === 0) {
        toast({
          title: "No feasible samples",
          description:
            "Could not generate any samples that satisfy the constraints.",
          variant: "destructive"
        })
        return
      }

      onSamplesGenerated?.(result.samples)

      toast({
        title: "Samples generated",
        description: `Successfully generated ${result.samples.length} samples.`
      })
    } catch (error) {
      toast({
        title: "Sampling failed",
        description:
          "Failed to generate samples. Please check your configuration.",
        variant: "destructive"
      })
    }
  }

  const handleExportSamples = () => {
    if (!samplingResult?.samples) return

    const exportData = {
      samples: samplingResult.samples,
      metadata: {
        parameters: parameters.map(p => ({ name: p.name, type: p.type })),
        constraints: constraints.length,
        samplingOptions,
        statistics: {
          totalSamples: samplingResult.samples.length,
          feasibleSamples: samplingResult.feasibleSamples,
          constraintViolations: samplingResult.constraintViolations,
          totalAttempts: samplingResult.totalAttempts
        },
        generatedAt: new Date().toISOString()
      }
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json"
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `constraint-aware-samples-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "Samples exported",
      description: "Samples have been exported to a JSON file."
    })
  }

  const updateSamplingOption = <K extends keyof SamplingOptions>(
    key: K,
    value: SamplingOptions[K]
  ) => {
    setSamplingOptions(prev => ({ ...prev, [key]: value }))
  }

  const feasibilityPercentage = feasibilityEstimate
    ? Math.round(feasibilityEstimate.feasibilityRatio * 100)
    : null

  const efficiencyPercentage = feasibilityEstimate
    ? Math.round(feasibilityEstimate.estimatedEfficiency * 100)
    : null

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">Constraint-Aware Sampling</h3>
          {hasConstraints && (
            <Badge variant="secondary">{constraintCount} constraints</Badge>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
        >
          <Settings className="mr-1 size-4" />
          {showAdvanced ? "Hide" : "Show"} Advanced
        </Button>
      </div>

      {/* Feasibility estimate */}
      {hasConstraints && feasibilityEstimate && (
        <Alert>
          <BarChart3 className="size-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  Constraint Feasibility
                </span>
                <span className="text-sm">{feasibilityPercentage}%</span>
              </div>
              <Progress value={feasibilityPercentage || 0} className="h-2" />
              <div className="text-muted-foreground text-xs">
                Estimated efficiency: {efficiencyPercentage}%
                {efficiencyPercentage && efficiencyPercentage < 50 && (
                  <span className="ml-1 text-orange-600">
                    (Consider relaxing constraints)
                  </span>
                )}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Sampling configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Sampling Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="strategy">Sampling Strategy</Label>
              <Select
                value={samplingOptions.strategy}
                onValueChange={(value: "LHS" | "random" | "sobol") =>
                  updateSamplingOption("strategy", value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LHS">Latin Hypercube Sampling</SelectItem>
                  <SelectItem value="sobol">Sobol Sequences</SelectItem>
                  <SelectItem value="random">Random Sampling</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="nSamples">Number of Samples</Label>
              <Input
                id="nSamples"
                type="number"
                min="1"
                max="1000"
                value={samplingOptions.nSamples}
                onChange={e =>
                  updateSamplingOption(
                    "nSamples",
                    parseInt(e.target.value) || 10
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="seed">Random Seed (Optional)</Label>
              <Input
                id="seed"
                type="number"
                placeholder="Auto"
                value={samplingOptions.seed || ""}
                onChange={e =>
                  updateSamplingOption(
                    "seed",
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="respectConstraints"
              checked={samplingOptions.respectConstraints}
              onCheckedChange={checked =>
                updateSamplingOption("respectConstraints", checked)
              }
              disabled={!hasConstraints}
            />
            <Label htmlFor="respectConstraints">
              Respect constraints during sampling
              {!hasConstraints && (
                <span className="text-muted-foreground ml-1">
                  (No constraints defined)
                </span>
              )}
            </Label>
          </div>

          {showAdvanced && (
            <>
              <Separator />
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="maxAttempts">Max Attempts</Label>
                  <Input
                    id="maxAttempts"
                    type="number"
                    min="100"
                    max="10000"
                    value={samplingOptions.maxAttempts}
                    onChange={e =>
                      updateSamplingOption(
                        "maxAttempts",
                        parseInt(e.target.value) || 1000
                      )
                    }
                  />
                  <div className="text-muted-foreground text-xs">
                    Maximum attempts for constraint satisfaction
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tolerance">Tolerance</Label>
                  <Input
                    id="tolerance"
                    type="number"
                    step="1e-6"
                    min="1e-12"
                    max="1e-3"
                    value={samplingOptions.tolerance}
                    onChange={e =>
                      updateSamplingOption(
                        "tolerance",
                        parseFloat(e.target.value) || 1e-6
                      )
                    }
                  />
                  <div className="text-muted-foreground text-xs">
                    Numerical tolerance for constraint satisfaction
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Generate button */}
      <div className="flex items-center gap-2">
        <Button
          onClick={handleGenerateSamples}
          disabled={isGenerating || parameters.length === 0}
          className="flex-1"
        >
          {isGenerating ? (
            <>
              <Clock className="mr-2 size-4 animate-spin" />
              Generating Samples...
            </>
          ) : (
            <>
              <Play className="mr-2 size-4" />
              Generate Samples
            </>
          )}
        </Button>

        {samplingResult && (
          <Button variant="outline" onClick={handleExportSamples}>
            <Download className="mr-1 size-4" />
            Export
          </Button>
        )}

        {samplingResult && (
          <Button variant="outline" onClick={clearResults}>
            Clear
          </Button>
        )}
      </div>

      {/* Results */}
      {samplingResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <CheckCircle className="size-4 text-green-600" />
              Sampling Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {samplingResult.samples.length}
                </div>
                <div className="text-muted-foreground text-sm">
                  Samples Generated
                </div>
              </div>

              {samplingResult.feasibleSamples !== undefined && (
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {samplingResult.feasibleSamples}
                  </div>
                  <div className="text-muted-foreground text-sm">
                    Feasible Samples
                  </div>
                </div>
              )}

              {samplingResult.constraintViolations !== undefined && (
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {samplingResult.constraintViolations}
                  </div>
                  <div className="text-muted-foreground text-sm">
                    Violations
                  </div>
                </div>
              )}

              {samplingResult.totalAttempts !== undefined && (
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">
                    {samplingResult.totalAttempts}
                  </div>
                  <div className="text-muted-foreground text-sm">
                    Total Attempts
                  </div>
                </div>
              )}
            </div>

            {samplingResult.constraintViolations !== undefined &&
              samplingResult.totalAttempts && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Success Rate</span>
                    <span>
                      {Math.round(
                        (samplingResult.feasibleSamples! /
                          samplingResult.totalAttempts) *
                          100
                      )}
                      %
                    </span>
                  </div>
                  <Progress
                    value={
                      (samplingResult.feasibleSamples! /
                        samplingResult.totalAttempts) *
                      100
                    }
                    className="h-2"
                  />
                </div>
              )}

            {/* Discretization Transparency */}
            <DiscretizationTransparency
              discretizationInfo={samplingResult.discretizationTransparency}
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
