#!/usr/bin/env node

/**
 * Comprehensive test for configuration updates across different scenarios:
 * - Single Target with different acquisition functions
 * - Multi-Target Desirability 
 * - Multi-Target Pareto
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, desc } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function testConfigurationUpdates() {
  console.log('🧪 COMPREHENSIVE CONFIGURATION UPDATE TESTING');
  console.log('==============================================');
  console.log('Testing across different acquisition functions and target scenarios');
  console.log('');

  let client;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    // Get all optimizations to test different scenarios
    const optimizations = await db.select().from(optimizationsTable)
      .orderBy(desc(optimizationsTable.createdAt));
    
    console.log(`📋 Found ${optimizations.length} optimizations to analyze`);
    console.log('');
    
    // Categorize optimizations by type
    const scenarios = {
      singleTarget: [],
      multiTargetDesirability: [],
      multiTargetPareto: []
    };
    
    optimizations.forEach(opt => {
      if (!opt.config) return;
      
      const config = opt.config;
      const objectiveType = config.objective_type;
      
      if (objectiveType === 'SingleTarget') {
        scenarios.singleTarget.push(opt);
      } else if (objectiveType === 'Desirability') {
        scenarios.multiTargetDesirability.push(opt);
      } else if (objectiveType === 'Pareto') {
        scenarios.multiTargetPareto.push(opt);
      }
    });
    
    console.log('📊 SCENARIO BREAKDOWN:');
    console.log('======================');
    console.log(`  Single Target: ${scenarios.singleTarget.length} optimizations`);
    console.log(`  Multi-Target Desirability: ${scenarios.multiTargetDesirability.length} optimizations`);
    console.log(`  Multi-Target Pareto: ${scenarios.multiTargetPareto.length} optimizations`);
    console.log('');
    
    // Test each scenario
    const testResults = {
      singleTarget: [],
      multiTargetDesirability: [],
      multiTargetPareto: []
    };
    
    // Test Single Target scenarios
    if (scenarios.singleTarget.length > 0) {
      console.log('🎯 TESTING SINGLE TARGET SCENARIOS');
      console.log('==================================');
      
      for (let i = 0; i < Math.min(3, scenarios.singleTarget.length); i++) {
        const opt = scenarios.singleTarget[i];
        console.log(`\n📋 Testing: ${opt.name} (${opt.optimizerId.slice(-12)})`);
        
        const result = await testSingleTargetUpdate(opt);
        testResults.singleTarget.push(result);
      }
    }
    
    // Test Multi-Target Desirability scenarios
    if (scenarios.multiTargetDesirability.length > 0) {
      console.log('\n🎯 TESTING MULTI-TARGET DESIRABILITY SCENARIOS');
      console.log('===============================================');
      
      for (let i = 0; i < Math.min(2, scenarios.multiTargetDesirability.length); i++) {
        const opt = scenarios.multiTargetDesirability[i];
        console.log(`\n📋 Testing: ${opt.name} (${opt.optimizerId.slice(-12)})`);
        
        const result = await testMultiTargetDesirabilityUpdate(opt);
        testResults.multiTargetDesirability.push(result);
      }
    }
    
    // Test Multi-Target Pareto scenarios
    if (scenarios.multiTargetPareto.length > 0) {
      console.log('\n🎯 TESTING MULTI-TARGET PARETO SCENARIOS');
      console.log('=========================================');
      
      for (let i = 0; i < Math.min(2, scenarios.multiTargetPareto.length); i++) {
        const opt = scenarios.multiTargetPareto[i];
        console.log(`\n📋 Testing: ${opt.name} (${opt.optimizerId.slice(-12)})`);
        
        const result = await testMultiTargetParetoUpdate(opt);
        testResults.multiTargetPareto.push(result);
      }
    }
    
    // Summary
    console.log('\n📊 COMPREHENSIVE TEST RESULTS');
    console.log('==============================');
    
    const allResults = [
      ...testResults.singleTarget,
      ...testResults.multiTargetDesirability,
      ...testResults.multiTargetPareto
    ];
    
    const successful = allResults.filter(r => r.success).length;
    const failed = allResults.filter(r => !r.success).length;
    
    console.log(`✅ Successful tests: ${successful}`);
    console.log(`❌ Failed tests: ${failed}`);
    console.log(`📊 Success rate: ${((successful / allResults.length) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      allResults.filter(r => !r.success).forEach(result => {
        console.log(`  - ${result.name}: ${result.error}`);
      });
    }
    
    console.log('\n🎯 ACQUISITION FUNCTION COVERAGE:');
    const acquisitionFunctions = new Set();
    allResults.forEach(r => {
      if (r.acquisitionFunction) {
        acquisitionFunctions.add(r.acquisitionFunction);
      }
    });
    
    acquisitionFunctions.forEach(af => {
      const count = allResults.filter(r => r.acquisitionFunction === af).length;
      const successCount = allResults.filter(r => r.acquisitionFunction === af && r.success).length;
      console.log(`  - ${af}: ${successCount}/${count} successful`);
    });
    
  } catch (error) {
    console.error('❌ Error during comprehensive testing:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

async function testSingleTargetUpdate(optimization) {
  const result = {
    name: optimization.name,
    type: 'SingleTarget',
    acquisitionFunction: optimization.config?.acquisition_config?.type,
    success: false,
    error: null,
    databaseUpdated: false
  };
  
  try {
    console.log(`   Acquisition Function: ${result.acquisitionFunction}`);
    console.log(`   Current Target Bounds: ${JSON.stringify(optimization.config?.target_config?.bounds)}`);
    
    // Test target bounds update
    const newBounds = [0, 999]; // Use a distinctive value
    
    const response = await fetch(`http://localhost:3000/api/optimizations/${optimization.optimizerId}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        target_bounds: {
          [optimization.config.target_config.name]: newBounds
        },
        preview_only: false
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      result.success = data.status === 'success';
      result.databaseUpdated = data.database_updated === true;
      
      console.log(`   ✅ Update successful: ${result.success}`);
      console.log(`   ✅ Database updated: ${result.databaseUpdated}`);
      
      if (data.updated_config) {
        console.log(`   ✅ Updated config provided`);
      }
    } else {
      result.error = `HTTP ${response.status}: ${response.statusText}`;
      console.log(`   ❌ Update failed: ${result.error}`);
    }
    
  } catch (error) {
    result.error = error.message;
    console.log(`   ❌ Exception: ${result.error}`);
  }
  
  return result;
}

async function testMultiTargetDesirabilityUpdate(optimization) {
  const result = {
    name: optimization.name,
    type: 'Desirability',
    acquisitionFunction: optimization.config?.acquisition_config?.type,
    success: false,
    error: null,
    databaseUpdated: false
  };
  
  try {
    console.log(`   Acquisition Function: ${result.acquisitionFunction}`);
    
    // For multi-target, test updating the first target
    const targets = optimization.config?.targets || [];
    if (targets.length === 0) {
      result.error = 'No targets found in configuration';
      return result;
    }
    
    const firstTarget = targets[0];
    console.log(`   Testing target: ${firstTarget.name}`);
    console.log(`   Current bounds: ${JSON.stringify(firstTarget.bounds)}`);
    
    const newBounds = [0, 888]; // Distinctive value
    
    const response = await fetch(`http://localhost:3000/api/optimizations/${optimization.optimizerId}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        target_bounds: {
          [firstTarget.name]: newBounds
        },
        preview_only: false
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      result.success = data.status === 'success';
      result.databaseUpdated = data.database_updated === true;
      
      console.log(`   ✅ Update successful: ${result.success}`);
      console.log(`   ✅ Database updated: ${result.databaseUpdated}`);
    } else {
      result.error = `HTTP ${response.status}: ${response.statusText}`;
      console.log(`   ❌ Update failed: ${result.error}`);
    }
    
  } catch (error) {
    result.error = error.message;
    console.log(`   ❌ Exception: ${result.error}`);
  }
  
  return result;
}

async function testMultiTargetParetoUpdate(optimization) {
  const result = {
    name: optimization.name,
    type: 'Pareto',
    acquisitionFunction: optimization.config?.acquisition_config?.type,
    success: false,
    error: null,
    databaseUpdated: false
  };
  
  try {
    console.log(`   Acquisition Function: ${result.acquisitionFunction}`);
    
    // For Pareto, test parameter bounds instead of target bounds
    const parameters = optimization.config?.parameters || [];
    if (parameters.length === 0) {
      result.error = 'No parameters found in configuration';
      return result;
    }
    
    const firstParam = parameters[0];
    console.log(`   Testing parameter: ${firstParam.name}`);
    console.log(`   Current bounds: ${JSON.stringify(firstParam.bounds)}`);
    
    const newBounds = [0, 777]; // Distinctive value
    
    const response = await fetch(`http://localhost:3000/api/optimizations/${optimization.optimizerId}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameter_bounds: {
          [firstParam.name]: newBounds
        },
        preview_only: false
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      result.success = data.status === 'success';
      result.databaseUpdated = data.database_updated === true;
      
      console.log(`   ✅ Update successful: ${result.success}`);
      console.log(`   ✅ Database updated: ${result.databaseUpdated}`);
    } else {
      result.error = `HTTP ${response.status}: ${response.statusText}`;
      console.log(`   ❌ Update failed: ${result.error}`);
    }
    
  } catch (error) {
    result.error = error.message;
    console.log(`   ❌ Exception: ${result.error}`);
  }
  
  return result;
}

// Run the comprehensive test
testConfigurationUpdates().catch(console.error);
