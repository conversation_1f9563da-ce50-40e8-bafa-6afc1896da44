// components/optimization/run-experiment.tsx
"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"

// Function to check if a string is a valid UUID
function isValidUUID(id: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(id)
}
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { SuggestionWarningBanner } from "./suggestion-warning-banner"
import { SuggestionDualView } from "./suggestion-dual-view"
import { SuggestionCard } from "./suggestion-card"
import {
  getSuggestionWorkflowAction,
  addMeasurementWorkflowAction,
  loadOptimizationWorkflowAction
} from "@/actions/optimization-workflow-actions"
import {
  generateSamplesWorkflowAction,
  getSavedSamplesWorkflowAction,
  updateSampleTargetValuesWorkflowAction,
  submitSamplesWorkflowAction,
  saveSamplesWorkflowAction
} from "@/actions/sample-workflow-actions"
import {
  getPendingSuggestionsWorkflowAction,
  markSuggestionSubmittedAction,
  updateSuggestionTargetValuesWorkflowAction,
  saveSuggestionsWorkflowAction
} from "@/actions/suggestion-workflow-actions"
import { SelectOptimization } from "@/db/schema/optimizations-schema"
// Removed SavedSuggestions import
import {
  AlertCircle,
  ArrowDown,
  ArrowUp,
  Beaker,
  Check,
  Lightbulb,
  Loader2,
  Save,
  X
} from "lucide-react"

interface RunExperimentProps {
  optimization: SelectOptimization
}

export function RunExperiment({ optimization }: RunExperimentProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("samples")
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false)
  const [loadingMode, setLoadingMode] = useState<"saved" | "new">("saved")
  const [isAddingMeasurement, setIsAddingMeasurement] = useState(false)
  const [isSavingSuggestions, setIsSavingSuggestions] = useState(false)
  const [isGeneratingSamples, setIsGeneratingSamples] = useState(false)
  const [isLoadingSamples, setIsLoadingSamples] = useState(false)
  const [isSavingSamples, setIsSavingSamples] = useState(false)
  const [samples, setSamples] = useState<any[]>([])
  const [samplingStrategy, setSamplingStrategy] = useState<"LHS" | "random">(
    "LHS"
  )
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [freshSuggestions, setFreshSuggestions] = useState<any[]>([])
  const [currentBatchId, setCurrentBatchId] = useState<string | null>(null)
  const [freshBatchId, setFreshBatchId] = useState<string | null>(null)
  const [suggestionsTimestamp, setSuggestionsTimestamp] = useState<number | null>(null)
  const [lastMeasurementTimestamp, setLastMeasurementTimestamp] = useState<number | null>(null)
  const [showFreshSuggestions, setShowFreshSuggestions] = useState(false)
  // Use an array of parameter objects for multiple manual experiments
  const [manualExperiments, setManualExperiments] = useState<
    Array<Record<string, any>>
  >([{}])

  // For backward compatibility, keep manualParameters as a reference to the first experiment
  const manualParameters = manualExperiments[0] || {}
  const setManualParameters = (params: Record<string, any>) => {
    setManualExperiments(prev => [params, ...prev.slice(1)])
  }
  // Use a nested object for target values to support multi-target optimizations and multiple suggestions
  // Format: { suggestionIndex: { targetName: value } }
  const [targetValues, setTargetValues] = useState<
    Record<number, Record<string, string>>
  >({})
  const [submittedSuggestions, setSubmittedSuggestions] = useState<
    Record<number, boolean>
  >({})
  // State to track batch submission progress
  const [batchProgress, setBatchProgress] = useState<{
    current: number
    total: number
  }>({ current: 0, total: 0 })

  // Keep track of which suggestion is currently being submitted
  const [activeSuggestionIndex, setActiveSuggestionIndex] = useState<number>(0)

  // Helper function to determine if this is a multi-target optimization
  const isMultiTarget = () => {
    return (
      optimization.targetMode === "MULTI" ||
      (optimization.config.objective_type === "Desirability" &&
        Array.isArray(optimization.config.target_config))
    )
  }

  // Helper function to get target configs
  const getTargetConfigs = () => {
    if (Array.isArray(optimization.config.target_config)) {
      return optimization.config.target_config
    } else {
      return [optimization.config.target_config]
    }
  }

  // Helper function to check if all target values are filled for a suggestion or sample
  const hasAllTargetValues = (index: number) => {
    console.log(`[FRONTEND] Checking target values for index ${index}`)

    // Determine if we're checking a sample or a suggestion
    const isSample = index >= 0 && samples.length > index
    const isSuggestion = index >= 0 && suggestions.length > index

    // Get the item (sample or suggestion)
    const item = isSample ? samples[index] : isSuggestion ? suggestions[index] : null

    if (!item) {
      console.log(`[FRONTEND] No item found at index ${index}`)
      return false
    }

    // Get the target values from both the UI state and the item's stored target values
    const uiTargetValues = targetValues[index] || {}
    const itemTargetValues = item._targetValues || {}

    console.log(`[FRONTEND] UI target values:`, uiTargetValues)
    console.log(`[FRONTEND] Item target values:`, itemTargetValues)

    // Combine both sources, with UI values taking precedence
    const combinedTargetValues: Record<string, any> = { ...itemTargetValues }

    // Add any UI values that might not be in the item yet
    for (const [key, value] of Object.entries(uiTargetValues)) {
      if (value && typeof value === "string" && value.trim() !== "") {
        const numValue = parseFloat(value)
        if (!isNaN(numValue)) {
          combinedTargetValues[key] = numValue
        }
      }
    }

    console.log(`[FRONTEND] Combined target values:`, combinedTargetValues)

    const targetConfigs = getTargetConfigs()

    // Check if all targets have values
    const result = targetConfigs.every(target => {
      const value = combinedTargetValues[target.name]

      if (value === undefined || value === null) {
        console.log(
          `[FRONTEND] Missing value for target ${target.name} at index ${index}`
        )
        return false
      }

      if (typeof value === "number") {
        if (isNaN(value) || !isFinite(value)) {
          console.log(
            `[FRONTEND] Invalid numeric value for target ${target.name} at index ${index}: ${value}`
          )
          return false
        }
        return true
      }

      console.log(
        `[FRONTEND] Invalid value type for target ${target.name} at index ${index}: ${typeof value}`
      )
      return false
    })

    if (!result) {
      console.log(
        `[FRONTEND] Item at index ${index} is missing some target values:`,
        combinedTargetValues
      )
    } else {
      console.log(
        `[FRONTEND] Item at index ${index} has all required target values`
      )
    }

    return result
  }

  // Initialize manual parameters and load pending suggestions and samples
  useEffect(() => {
    // Create initial parameters for a single experiment
    const createInitialParams = () => {
      return optimization.config.parameters.reduce(
        (acc, param) => {
          if (param.type === "NumericalDiscrete") {
            // Ensure we're using a valid value from the allowed values
            if (param.values && param.values.length > 0) {
              // Convert string values to numbers for numerical parameters
              acc[param.name] =
                typeof param.values[0] === "string"
                  ? parseFloat(param.values[0])
                  : param.values[0]
            } else {
              acc[param.name] = 0
            }
          } else if (param.type === "NumericalContinuous") {
            // Ensure we're within bounds
            acc[param.name] = param.bounds ? param.bounds[0] : 0
          } else if (param.type === "CategoricalParameter") {
            // Use the first allowed value
            acc[param.name] =
              param.values && param.values.length > 0 ? param.values[0] : ""
          }
          return acc
        },
        {} as Record<string, any>
      )
    }

    // Initialize with a single experiment
    setManualExperiments([createInitialParams()])

    // Initialize target values for manual entries
    const targetConfigs = getTargetConfigs()
    const initialTargetValues: Record<number, Record<string, string>> = {}

    // For the first manual experiment, use index -1 (for backward compatibility)
    initialTargetValues[-1] = targetConfigs.reduce(
      (acc, target) => {
        acc[target.name] = ""
        return acc
      },
      {} as Record<string, string>
    )

    // For additional experiments, use indices -2, -3, etc.
    // We'll start with just one experiment, so no need to add more yet

    setTargetValues(initialTargetValues)

    // Load pending suggestions from the database
    const loadPendingSuggestions = async () => {
      try {
        setIsLoadingSuggestions(true)
        setLoadingMode("saved")
        const result = await getPendingSuggestionsWorkflowAction(
          optimization.optimizerId
        )

        if (
          result.isSuccess &&
          result.data &&
          result.data.suggestions.length > 0
        ) {
          // Set the suggestions and batch ID
          setSuggestions(result.data.suggestions)
          setCurrentBatchId(result.data.batchId)

          // Extract timestamp from batch ID (format: batch_<optimizationId>_<timestamp>)
          const batchIdParts = result.data.batchId.split('_')
          if (batchIdParts.length >= 3) {
            const timestamp = parseInt(batchIdParts[2])
            if (!isNaN(timestamp)) {
              setSuggestionsTimestamp(timestamp)
              console.log(`[FRONTEND] Extracted timestamp from batch ID: ${timestamp}`)
            }
          }

          // Initialize target values from saved values
          const savedTargetValues: Record<number, Record<string, string>> = {}

          result.data.suggestions.forEach((suggestion, index) => {
            if (suggestion._targetValues) {
              savedTargetValues[index] = suggestion._targetValues as Record<
                string,
                string
              >
            }
          })

          if (Object.keys(savedTargetValues).length > 0) {
            // Merge with existing target values
            setTargetValues(prev => ({
              ...prev,
              ...savedTargetValues
            }))
          }

          // Switch to the suggested tab if we found pending suggestions
          setActiveTab("suggested")

          toast({
            title: "Saved suggestions loaded",
            description: `Loaded ${result.data.suggestions.length} previously saved suggestions`
          })
        }
      } catch (error) {
        console.error("Error loading pending suggestions:", error)
        // Don't show an error to the user - the UI still works without this
      } finally {
        setIsLoadingSuggestions(false)
      }
    }

    // Only load pending suggestions, not saved samples
    loadPendingSuggestions()
  }, [
    optimization.optimizerId,
    optimization.config.parameters,
    optimization.config.target_config
  ])

  // Load saved samples from the database when the user clicks the Load Saved Samples button
  const loadSavedSamples = async () => {
    try {
      setIsLoadingSamples(true)
      console.log(
        "[FRONTEND] Loading saved samples for optimization:",
        optimization.optimizerId
      )
      console.log("[FRONTEND] Optimization details:", {
        id: optimization.id,
        optimizerId: optimization.optimizerId,
        name: optimization.name
      })

      const result = await getSavedSamplesWorkflowAction(
        optimization.optimizerId,
        "pending"
      )

      if (result.isSuccess && result.data && result.data.samples.length > 0) {
        console.log(
          `Loaded ${result.data.samples.length} samples from database:`,
          result.data.samples.map(s => ({
            id: s._sampleId,
            isUUID: isValidUUID(s._sampleId),
            hasTargetValues:
              s._targetValues && Object.keys(s._targetValues).length > 0
          }))
        )

        // Set the samples
        setSamples(result.data.samples)

        // Initialize target values from saved values
        const savedTargetValues: Record<number, Record<string, string>> = {}

        result.data.samples.forEach((sample, index) => {
          if (sample._targetValues) {
            // Convert numeric target values to strings for the UI
            const stringTargetValues: Record<string, string> = {}

            for (const [key, value] of Object.entries(sample._targetValues)) {
              stringTargetValues[key] = value.toString()
            }

            savedTargetValues[index] = stringTargetValues
          }
        })

        if (Object.keys(savedTargetValues).length > 0) {
          console.log(
            "Setting target values from saved samples:",
            savedTargetValues
          )

          // Merge with existing target values
          setTargetValues(prev => ({
            ...prev,
            ...savedTargetValues
          }))
        }

        // Switch to the samples tab if we found pending samples
        setActiveTab("samples")

        toast({
          title: "Saved samples loaded",
          description: `Loaded ${result.data.samples.length} previously saved samples`
        })
      } else {
        console.log("No saved samples found or error loading samples:", result)
        toast({
          title: "No saved samples",
          description: "No saved samples found for this optimization"
        })
      }
    } catch (error) {
      console.error("Error loading saved samples:", error)
      toast({
        title: "Error",
        description: "Failed to load saved samples. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoadingSamples(false)
    }
  }

  // Save generated samples to the database
  const saveSamples = async () => {
    if (samples.length === 0) return

    setIsSavingSamples(true)
    try {
      // Log the samples being saved
      console.log(`Saving ${samples.length} samples to the database`)

      // Check if any samples already have valid UUIDs
      const alreadySavedSamples = samples.filter(sample =>
        isValidUUID(sample._sampleId)
      )
      if (alreadySavedSamples.length > 0) {
        console.log(
          `${alreadySavedSamples.length} samples already have valid UUIDs and may already be saved`
        )
      }

      // Generate a batch ID if one doesn't exist
      const batchId =
        samples[0]?._batchId || `${optimization.optimizerId}_${Date.now()}`

      // Call the workflow action to explicitly save samples
      const result = await saveSamplesWorkflowAction(
        optimization.optimizerId,
        samples,
        batchId
      )

      if (result.isSuccess) {
        // Update the samples with the database IDs
        setSamples(result.data?.savedSamples || samples)

        toast({
          title: "Samples saved",
          description: `Successfully saved ${samples.length} samples for later use`
        })
      } else {
        toast({
          title: "Error saving samples",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error saving samples:", error)
      toast({
        title: "Error",
        description: "Failed to save samples. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSavingSamples(false)
    }
  }

  const saveSuggestions = async (isFresh: boolean = false) => {
    // Use the appropriate suggestion array and batch ID based on isFresh
    const suggestionArray = isFresh ? freshSuggestions : suggestions
    const batchId = isFresh ? freshBatchId : currentBatchId

    if (suggestionArray.length === 0) return

    setIsSavingSuggestions(true)
    try {
      // Call the workflow action to explicitly save suggestions
      const result = await saveSuggestionsWorkflowAction(
        optimization.optimizerId,
        suggestionArray,
        batchId
      )

      if (result.isSuccess) {
        // If we're saving fresh suggestions, move them to the saved suggestions
        if (isFresh) {
          setSuggestions(freshSuggestions)
          setCurrentBatchId(freshBatchId)
          setFreshSuggestions([])
          setFreshBatchId(null)
          setShowFreshSuggestions(false)
        }

        toast({
          title: "Suggestions saved",
          description: `Successfully saved ${suggestionArray.length} suggestions for later use`
        })
      } else {
        toast({
          title: "Error saving suggestions",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error saving suggestions:", error)
      toast({
        title: "Error",
        description: "Failed to save suggestions. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSavingSuggestions(false)
    }
  }

  // Function to update sample target values
  const updateSampleTargetValue = async (
    sampleIndex: number,
    targetName: string,
    value: string
  ) => {
    console.log(
      `[FRONTEND] Updating target value for sample ${sampleIndex}, target ${targetName}, value ${value}`
    )

    // Update the local state first for immediate feedback
    setTargetValues(prev => {
      const newValues = { ...prev }
      if (!newValues[sampleIndex]) {
        newValues[sampleIndex] = {}
      }
      newValues[sampleIndex][targetName] = value
      return newValues
    })

    // Get the sample ID
    const sample = samples[sampleIndex]
    if (!sample || !sample._sampleId) {
      console.error(
        `[FRONTEND] No sample found at index ${sampleIndex} or missing sample ID`
      )
      return
    }

    console.log(
      `[FRONTEND] Sample ID: ${sample._sampleId}, isUUID: ${isValidUUID(sample._sampleId)}`
    )

    // Get all current target values for this sample
    const currentTargetValues = targetValues[sampleIndex] || {}
    console.log(`[FRONTEND] Current target values:`, currentTargetValues)

    // Convert target values to numbers for the API
    // Only convert the specific target that was updated
    const numericTargetValues = { ...sample._targetValues } || {}

    // Update only the specific target that was changed
    if (value && value.trim() !== "") {
      const numValue = parseFloat(value)
      if (!isNaN(numValue)) {
        numericTargetValues[targetName] = numValue
        console.log(
          `[FRONTEND] Updated target ${targetName} to value ${numValue}`
        )
      } else {
        console.log(
          `[FRONTEND] Invalid numeric value for target ${targetName}: ${value}`
        )
      }
    } else {
      // If the value is empty, remove this target
      delete numericTargetValues[targetName]
      console.log(`[FRONTEND] Removed empty target ${targetName}`)
    }

    console.log(`[FRONTEND] Updated target values:`, numericTargetValues)

    console.log(`[FRONTEND] Numeric target values:`, numericTargetValues)

    // Only update if we have at least one target value
    if (Object.keys(numericTargetValues).length === 0) {
      console.log(`[FRONTEND] No valid numeric target values to update`)
      return
    }

    // Update the sample in the local state to reflect the new target values
    setSamples(prev => {
      const newSamples = [...prev]
      newSamples[sampleIndex] = {
        ...newSamples[sampleIndex],
        _targetValues: numericTargetValues
      }
      return newSamples
    })

    // Check if the sample has been saved to the database (has a valid UUID)
    // Only attempt to update in the database if it's a valid UUID
    if (isValidUUID(sample._sampleId)) {
      try {
        console.log(
          `[FRONTEND] Updating target values in database for sample ${sample._sampleId}`
        )

        // Update the sample in the database
        const result = await updateSampleTargetValuesWorkflowAction(
          sample._sampleId,
          numericTargetValues
        )

        if (!result.isSuccess) {
          console.error(
            `[FRONTEND] Error updating sample target values:`,
            result.message
          )
        } else {
          console.log(
            `[FRONTEND] Successfully updated target values for sample ${sample._sampleId}`
          )
        }
      } catch (error) {
        console.error(`[FRONTEND] Error updating sample target values:`, error)
      }
    } else {
      // For samples not yet saved to the database, just log that we're skipping the update
      console.log(
        `[FRONTEND] Skipping database update for unsaved sample with ID ${sample._sampleId}`
      )
    }
  }

  // Function to submit samples with target values
  const submitSamples = async () => {
    console.log("[FRONTEND] Starting sample submission process")
    console.log("[FRONTEND] Total samples available:", samples.length)

    if (samples.length === 0) {
      console.log("[FRONTEND] No samples available to submit")
      return
    }

    // Get samples that have all target values filled
    let samplesToSubmit = samples.filter((sample, index) =>
      hasAllTargetValues(index)
    )

    console.log(
      "[FRONTEND] Samples with all target values:",
      samplesToSubmit.length
    )

    if (samplesToSubmit.length === 0) {
      console.log("[FRONTEND] No samples have complete target values")
      toast({
        title: "No samples to submit",
        description: "Please fill in target values for at least one sample",
        variant: "destructive"
      })
      return
    }

    console.log(
      "[FRONTEND] Samples to submit before saving:",
      samplesToSubmit.map(s => ({
        id: s._sampleId,
        isUUID: isValidUUID(s._sampleId),
        savedToDatabase: s._savedToDatabase,
        targetValues: s._targetValues
      }))
    )

    // Always save samples before submitting to ensure they have database IDs
    // This simplifies the workflow and ensures all samples are in the database
    try {
      setIsSavingSamples(true)
      console.log("[FRONTEND] Saving samples before submission...")
      console.log("[FRONTEND] Optimization ID:", optimization.optimizerId)
      console.log(
        "[FRONTEND] Batch ID:",
        samplesToSubmit[0]._batchId || `${Date.now()}`
      )

      // Check if all samples already have valid UUIDs
      const unsavedSamples = samplesToSubmit.filter(
        s => !isValidUUID(s._sampleId) || !s._savedToDatabase
      )
      console.log(
        `[FRONTEND] ${unsavedSamples.length} of ${samplesToSubmit.length} samples need to be saved`
      )

      const saveResult = await saveSamplesWorkflowAction(
        optimization.optimizerId,
        samplesToSubmit,
        samplesToSubmit[0]._batchId || `${Date.now()}`
      )

      console.log("[FRONTEND] Save result:", {
        isSuccess: saveResult.isSuccess,
        message: saveResult.message,
        hasSavedSamples: !!saveResult.data?.savedSamples,
        savedSamplesCount: saveResult.data?.savedSamples?.length
      })

      if (saveResult.isSuccess && saveResult.data?.savedSamples) {
        // Update samples with database IDs
        setSamples(saveResult.data.savedSamples)

        // Continue with the updated samples
        samplesToSubmit = saveResult.data.savedSamples

        console.log(
          "[FRONTEND] Samples saved successfully:",
          samplesToSubmit.map(s => ({
            id: s._sampleId,
            isUUID: isValidUUID(s._sampleId),
            savedToDatabase: s._savedToDatabase
          }))
        )
      } else {
        console.error("[FRONTEND] Failed to save samples:", saveResult.message)
        toast({
          title: "Error saving samples",
          description:
            saveResult.message || "Failed to save samples before submission.",
          variant: "destructive"
        })
        setIsSavingSamples(false)
        return
      }
      setIsSavingSamples(false)
    } catch (error) {
      console.error("[FRONTEND] Error saving samples before submission:", error)
      toast({
        title: "Error",
        description:
          "Failed to save samples before submission. Please try again.",
        variant: "destructive"
      })
      setIsSavingSamples(false)
      return
    }

    setIsAddingMeasurement(true)
    try {
      // Get the sample IDs to submit
      const sampleIds = samplesToSubmit.map(sample => sample._sampleId)

      // Log the sample IDs being submitted
      console.log(
        `[FRONTEND] Submitting ${sampleIds.length} samples with IDs:`,
        sampleIds
      )

      // Double-check that all sample IDs are valid UUIDs
      const invalidIds = sampleIds.filter(id => !isValidUUID(id))
      if (invalidIds.length > 0) {
        console.error(
          `[FRONTEND] Found ${invalidIds.length} invalid sample IDs:`,
          invalidIds
        )
        toast({
          title: "Error submitting samples",
          description:
            "Some samples have invalid IDs. Please try saving samples again before submitting.",
          variant: "destructive"
        })
        setIsAddingMeasurement(false)
        return
      }

      console.log(
        `[FRONTEND] All ${sampleIds.length} sample IDs are valid UUIDs`
      )
      console.log(
        `[FRONTEND] Calling submitSamplesWorkflowAction with optimization ID: ${optimization.optimizerId}`
      )

      // Submit the samples
      const result = await submitSamplesWorkflowAction(
        optimization.optimizerId,
        sampleIds
      )

      console.log(`[FRONTEND] Submit result:`, {
        isSuccess: result.isSuccess,
        message: result.message
      })

      if (result.isSuccess) {
        toast({
          title: "Samples submitted",
          description: `Successfully submitted ${samplesToSubmit.length} samples to the optimization`
        })

        // Refresh the page to show the updated measurements
        router.refresh()
      } else {
        toast({
          title: "Error submitting samples",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error submitting samples:", error)
      toast({
        title: "Error",
        description: "Failed to submit samples. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsAddingMeasurement(false)
    }
  }

  const generateSamples = async (
    numSamples: number = 10,
    strategy: "LHS" | "random" = "LHS",
    seed?: number
  ) => {
    setSamples([])
    setIsGeneratingSamples(true)
    try {
      // First, check if the optimization exists in the API
      try {
        const loadResult = await loadOptimizationWorkflowAction(
          optimization.optimizerId
        )

        if (!loadResult.isSuccess) {
          // If loading fails, we'll try to continue anyway, but log the error
          console.warn(
            "Warning: Could not load optimization from API:",
            loadResult.message
          )
          // We don't return here - we'll try to generate samples anyway
        }
      } catch (error) {
        // If there's an error, we'll try to continue anyway, but log the error
        console.warn("Warning: Error checking optimization:", error)
        // We don't return here - we'll try to generate samples anyway
      }

      // Now generate samples
      const result = await generateSamplesWorkflowAction(
        optimization.optimizerId,
        numSamples,
        strategy,
        seed
      )

      if (result.isSuccess && result.data) {
        // Log the generated samples
        console.log(
          `Successfully generated ${result.data.samples.length} samples`
        )

        // Check the sample IDs to ensure they're valid UUIDs
        const sampleIds = result.data.samples.map(sample => sample._sampleId)
        const validUUIDs = sampleIds.filter(id => isValidUUID(id))
        console.log(
          `${validUUIDs.length} of ${sampleIds.length} samples have valid UUID IDs`
        )

        setSamples(result.data.samples)

        // Initialize target values for the samples
        const targetConfigs = getTargetConfigs()
        const initialTargetValues: Record<number, Record<string, string>> = {}

        // For each sample, create an entry in the target values object
        result.data.samples.forEach((sample, index) => {
          initialTargetValues[index] = targetConfigs.reduce(
            (acc, target) => {
              acc[target.name] = ""
              return acc
            },
            {} as Record<string, string>
          )
        })

        // Update the target values state
        setTargetValues(prev => ({
          ...prev,
          ...initialTargetValues
        }))

        toast({
          title: "Samples generated",
          description: `Successfully generated ${result.data.samples.length} samples using ${strategy === "LHS" ? "Latin Hypercube Sampling" : "random sampling"}`
        })
      } else {
        toast({
          title: "Error generating samples",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error generating samples:", error)
      toast({
        title: "Error",
        description:
          "Failed to generate samples. Please try again or refresh the page.",
        variant: "destructive"
      })
    } finally {
      setIsGeneratingSamples(false)
    }
  }

  const getSuggestions = async (batchSize: number = 1) => {
    // Clear existing fresh suggestions first to ensure we show "Generating new suggestions..."
    setFreshSuggestions([])
    setIsLoadingSuggestions(true)
    setLoadingMode("new")
    try {
      // First, check if the optimization exists in the API
      try {
        const loadResult = await loadOptimizationWorkflowAction(
          optimization.optimizerId
        )

        if (!loadResult.isSuccess) {
          // If loading fails, we'll try to continue anyway, but log the error
          console.warn(
            "Warning: Could not load optimization from API:",
            loadResult.message
          )
          // We don't return here - we'll try to get suggestions anyway
        }
      } catch (error) {
        // If there's an error, we'll try to continue anyway, but log the error
        console.warn("Warning: Error checking optimization:", error)
        // We don't return here - we'll try to get suggestions anyway
      }

      // Now get suggestions
      const result = await getSuggestionWorkflowAction(
        optimization.optimizerId,
        batchSize
      )

      if (result.isSuccess && result.data) {
        // Store fresh suggestions separately
        setFreshSuggestions(result.data.suggestions)
        setShowFreshSuggestions(true)

        // Reset submitted suggestions when getting new ones
        setSubmittedSuggestions({})

        // Set batch ID if provided by the API, regardless of the number of suggestions
        // This ensures that all measurements from the same batch share the same batch ID
        const batchId = result.data.batchId
        setFreshBatchId(batchId)
        console.log(
          `Setting fresh batch ID: ${batchId || "null"} for ${result.data.suggestions.length} suggestions`
        )

        // Extract timestamp from batch ID
        const batchIdParts = batchId ? batchId.split('_') : []
        if (batchIdParts.length >= 3) {
          const timestamp = parseInt(batchIdParts[2])
          if (!isNaN(timestamp)) {
            console.log(`[FRONTEND] Fresh suggestions timestamp: ${timestamp}`)
          }
        }

        // Log batch information
        if (batchId) {
          console.log(
            `Loaded batch suggestions (${result.data.suggestions.length}) with batch ID: ${batchId}`
          )
        } else {
          console.log(
            "Loaded sequential suggestion (no batch ID or single suggestion)"
          )
        }

        toast({
          title: "New suggestions generated",
          description: `Successfully generated ${result.data.suggestions.length} new suggestions`
        })
      } else {
        toast({
          title: "Error loading suggestions",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error getting suggestions:", error)
      toast({
        title: "Error",
        description:
          "Failed to get suggestions. Please try again or refresh the page.",
        variant: "destructive"
      })
    } finally {
      setIsLoadingSuggestions(false)
    }
  }

  const addMeasurement = async (
    parameters: Record<string, any>,
    value: number | Record<string, number>,
    isRecommended: boolean = true,
    batchId: string | null = null
  ) => {
    setIsAddingMeasurement(true)
    try {
      // First, check if the optimization exists in the API
      try {
        const loadResult = await loadOptimizationWorkflowAction(
          optimization.optimizerId
        )

        if (!loadResult.isSuccess) {
          // If loading fails, we'll try to continue anyway, but log the error
          console.warn(
            "Warning: Could not load optimization from API:",
            loadResult.message
          )
          // We don't return here - we'll try to add the measurement anyway
        }
      } catch (error) {
        // If there's an error, we'll try to continue anyway, but log the error
        console.warn("Warning: Error checking optimization:", error)
        // We don't return here - we'll try to add the measurement anyway
      }

      // Now add the measurement
      // Pass batch ID for all API-recommended measurements if we have one
      // This ensures that all measurements from the same batch share the same batch ID
      const result = await addMeasurementWorkflowAction(
        optimization.optimizerId,
        parameters,
        value,
        isRecommended,
        isRecommended && currentBatchId ? currentBatchId : null
      )

      // Log for debugging
      console.log(
        `Added measurement with batch ID: ${isRecommended && currentBatchId ? currentBatchId : "null"}`
      )

      if (result.isSuccess) {
        // Update the last measurement timestamp
        setLastMeasurementTimestamp(Date.now())

        toast({
          title: "Measurement added",
          description: "Successfully added measurement to the optimization"
        })

        // Clear target values for the submitted suggestion
        setTargetValues(prev => {
          const newValues = { ...prev }

          if (isRecommended) {
            // For suggested experiments, clear the active suggestion
            delete newValues[activeSuggestionIndex]
          } else {
            // For manual experiments, clear the manual entry
            newValues[-1] = getTargetConfigs().reduce(
              (acc, target) => {
                acc[target.name] = ""
                return acc
              },
              {} as Record<string, string>
            )
          }

          return newValues
        })

        // If this was a suggested measurement, mark it as submitted
        if (isRecommended) {
          // Instead of clearing all suggestions, just mark this one as submitted
          // This allows users to submit multiple measurements from the same batch
          setSubmittedSuggestions(prev => ({
            ...prev,
            [activeSuggestionIndex]: true
          }))

          // Log for debugging
          console.log(
            `Submitted measurement for suggestion ${activeSuggestionIndex} with batch ID ${currentBatchId}`
          )
          console.log(
            `Submitted suggestions: ${Object.keys(submittedSuggestions).join(", ")}`
          )

          // Mark the suggestion as submitted in the database
          // This ensures it won't be loaded again on next visit
          const suggestion = suggestions[activeSuggestionIndex]
          if (suggestion && suggestion._suggestionId) {
            try {
              console.log(
                `Marking suggestion ${suggestion._suggestionId} as submitted in database`
              )
              // Use await to ensure the database update completes before continuing
              const result = await markSuggestionSubmittedAction(
                suggestion._suggestionId
              )
              if (result.isSuccess) {
                console.log(
                  `Successfully marked suggestion ${suggestion._suggestionId} as submitted`
                )
              } else {
                console.warn(
                  `Failed to mark suggestion as submitted: ${result.message}`
                )
              }
            } catch (error) {
              console.error(`Error marking suggestion as submitted:`, error)
            }
          }

          // Don't refresh the page if we're in the middle of a batch submission
          // This allows all measurements to be submitted before refreshing
          if (!isAddingMeasurement) {
            // Refresh the page to show the updated measurements
            router.refresh()
          }
        } else {
          // For manual measurements, always refresh
          router.refresh()
        }
      } else {
        toast({
          title: "Error adding measurement",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error adding measurement:", error)
      toast({
        title: "Error",
        description:
          "Failed to add measurement. Please try again or refresh the page.",
        variant: "destructive"
      })
    } finally {
      setIsAddingMeasurement(false)
    }
  }

  const handleManualParameterChange = (
    experimentIndex: number,
    name: string,
    value: any,
    paramType?: string
  ) => {
    // Convert numerical values to numbers
    let processedValue = value
    if (
      paramType &&
      (paramType === "NumericalDiscrete" || paramType === "NumericalContinuous")
    ) {
      // Convert to number if it's a numerical parameter
      const numValue = parseFloat(value)
      if (!isNaN(numValue)) {
        processedValue = numValue
      }
    }

    setManualExperiments(prev => {
      const updated = [...prev]
      if (!updated[experimentIndex]) {
        updated[experimentIndex] = {}
      }
      updated[experimentIndex] = {
        ...updated[experimentIndex],
        [name]: processedValue
      }
      return updated
    })
  }

  // Function to add a new manual experiment
  const addManualExperiment = () => {
    // Create initial parameters for a new experiment
    const createInitialParams = () => {
      return optimization.config.parameters.reduce(
        (acc, param) => {
          if (param.type === "NumericalDiscrete") {
            // Ensure we're using a valid value from the allowed values
            if (param.values && param.values.length > 0) {
              // Convert string values to numbers for numerical parameters
              acc[param.name] =
                typeof param.values[0] === "string"
                  ? parseFloat(param.values[0])
                  : param.values[0]
            } else {
              acc[param.name] = 0
            }
          } else if (param.type === "NumericalContinuous") {
            // Ensure we're within bounds
            acc[param.name] = param.bounds ? param.bounds[0] : 0
          } else if (param.type === "CategoricalParameter") {
            // Use the first allowed value
            acc[param.name] =
              param.values && param.values.length > 0 ? param.values[0] : ""
          }
          return acc
        },
        {} as Record<string, any>
      )
    }

    // Add a new experiment to the list
    setManualExperiments(prev => [...prev, createInitialParams()])

    // Initialize target values for the new experiment
    const targetConfigs = getTargetConfigs()
    const newExperimentIndex = -1 - manualExperiments.length // Use -2, -3, etc. for additional experiments

    setTargetValues(prev => ({
      ...prev,
      [newExperimentIndex]: targetConfigs.reduce(
        (acc, target) => {
          acc[target.name] = ""
          return acc
        },
        {} as Record<string, string>
      )
    }))
  }

  // Function to remove a manual experiment
  const removeManualExperiment = (experimentIndex: number) => {
    // Don't allow removing the last experiment
    if (manualExperiments.length <= 1) return

    // Remove the experiment from the list
    setManualExperiments(prev => {
      const updated = [...prev]
      updated.splice(experimentIndex, 1)
      return updated
    })

    // Remove target values for this experiment
    const targetIndex = experimentIndex === 0 ? -1 : -1 - experimentIndex
    setTargetValues(prev => {
      const updated = { ...prev }
      delete updated[targetIndex]
      return updated
    })
  }

  const handleTargetValueChange = (
    index: number,
    name: string,
    value: string
  ) => {
    try {
      // Update local state first for immediate UI feedback
      setTargetValues(prev => {
        // Create a deep copy of the previous state
        const newValues = { ...prev }

        // Initialize the index if it doesn't exist
        if (!newValues[index]) {
          newValues[index] = {}
        }

        // Update the target value
        newValues[index] = {
          ...newValues[index],
          [name]: value
        }

        return newValues
      })

      // Determine if this is a suggestion or a sample
      const isSuggestion = index >= 0 && suggestions[index]
      const isSample = index >= 0 && samples[index]

      // If this is a suggestion from the database, save the target value
      if (isSuggestion) {
        try {
          const suggestion = suggestions[index]
          const suggestionId = suggestion._suggestionId

          if (suggestionId) {
            // Get the current values for this suggestion
            const currentValues = targetValues[index] || {}

            // Create updated values with the new value
            const updatedValues = {
              ...currentValues,
              [name]: value
            }

            // Save to database in the background
            // We don't need to await this or show errors to the user
            // It's just for persistence between sessions
            try {
              // Use the workflow action to update target values
              updateSuggestionTargetValuesWorkflowAction(
                suggestionId,
                updatedValues
              )
                .then(result => {
                  if (!result.isSuccess) {
                    console.warn(
                      "Failed to save suggestion target value to database:",
                      result.message
                    )
                  }
                })
                .catch(error => {
                  console.error(
                    "Error saving suggestion target value to database:",
                    error
                  )
                })
            } catch (error) {
              console.error(
                "Error calling updateSuggestionTargetValuesWorkflowAction:",
                error
              )
              // Don't show an error to the user - the UI still works
            }
          }
        } catch (error) {
          console.error(
            "Error preparing to save suggestion target value:",
            error
          )
          // Don't show an error to the user - the UI still works
        }
      }
      // If this is a sample from the database, save the target value
      else if (isSample) {
        try {
          const sample = samples[index]
          const sampleId = sample._sampleId

          if (sampleId) {
            // Get the current values for this sample
            const currentValues = targetValues[index] || {}

            // Create updated values with the new value
            const updatedValues = {
              ...currentValues,
              [name]: value
            }

            // Save to database in the background
            // We don't need to await this or show errors to the user
            // It's just for persistence between sessions
            try {
              // Use the workflow action to update target values
              updateSampleTargetValuesWorkflowAction(sampleId, updatedValues)
                .then(result => {
                  if (!result.isSuccess) {
                    console.warn(
                      "Failed to save sample target value to database:",
                      result.message
                    )
                  }
                })
                .catch(error => {
                  console.error(
                    "Error saving sample target value to database:",
                    error
                  )
                })
            } catch (error) {
              console.error(
                "Error calling updateSampleTargetValuesWorkflowAction:",
                error
              )
              // Don't show an error to the user - the UI still works
            }
          }
        } catch (error) {
          console.error("Error preparing to save sample target value:", error)
          // Don't show an error to the user - the UI still works
        }
      }
    } catch (error) {
      console.error("Error in handleTargetValueChange:", error)
      // Don't show an error to the user - the UI still works
    }
  }

  // Function to submit all suggestions at once
  const handleSubmitAllSuggestions = async (isFresh: boolean = false) => {
    console.log("[FRONTEND] Starting to submit all suggestions")

    // Use the appropriate suggestion array based on isFresh
    const suggestionArray = isFresh ? freshSuggestions : suggestions
    console.log("[FRONTEND] Total suggestions available:", suggestionArray.length)

    if (suggestionArray.length === 0) {
      toast({
        title: "No suggestions",
        description: "There are no suggestions to submit.",
        variant: "destructive"
      })
      return
    }

    // Check if any suggestion has target values
    const hasAnyTargetValues = Object.keys(targetValues).some(key => {
      const index = parseInt(key)
      return index >= 0 && Object.keys(targetValues[index] || {}).length > 0
    })

    if (!hasAnyTargetValues) {
      toast({
        title: "Missing target values",
        description: "Please enter target values for at least one suggestion.",
        variant: "destructive"
      })
      return
    }

    // Get target configs
    const targetConfigs = getTargetConfigs()

    // Track if we've submitted at least one measurement
    let submittedCount = 0
    let errorCount = 0

    // Create a local copy of the submitted suggestions state that we'll update during the process
    // This helps ensure we have the most up-to-date state when making decisions
    const updatedSubmittedSuggestions = { ...submittedSuggestions }

    // Set a flag to prevent multiple submissions
    setIsAddingMeasurement(true)

    // Count how many suggestions we need to process
    const suggestionsToProcess = suggestionArray.filter((_, index) => {
      // Only count suggestions that have target values and haven't been submitted yet
      const hasTargetValues = Object.keys(targetValues[index] || {}).length > 0
      const notSubmitted = !updatedSubmittedSuggestions[index]
      return hasTargetValues && notSubmitted
    }).length

    // Initialize progress
    setBatchProgress({ current: 0, total: suggestionsToProcess })

    // Process each suggestion that has target values
    for (let index = 0; index < suggestionArray.length; index++) {
      // Skip if already submitted
      if (updatedSubmittedSuggestions[index]) {
        continue
      }

      // Get the target values for this suggestion
      const suggestionTargetValues = targetValues[index] || {}

      // Skip if no target values
      if (Object.keys(suggestionTargetValues).length === 0) {
        continue
      }

      try {
        if (isMultiTarget()) {
          // For multi-target, validate all target values
          const missingTargets = targetConfigs.filter(
            target =>
              !suggestionTargetValues[target.name] ||
              suggestionTargetValues[target.name].trim() === ""
          )

          if (missingTargets.length > 0) {
            console.log(
              `Skipping suggestion ${index} due to missing targets: ${missingTargets.map(t => t.name).join(", ")}`
            )
            continue
          }

          // Parse and validate all target values
          const parsedTargetValues: Record<string, number> = {}
          let hasInvalidValue = false

          for (const target of targetConfigs) {
            const parsedValue = parseFloat(suggestionTargetValues[target.name])
            if (isNaN(parsedValue)) {
              console.log(
                `Skipping suggestion ${index} due to invalid value for ${target.name}`
              )
              hasInvalidValue = true
              break
            }
            parsedTargetValues[target.name] = parsedValue
          }

          if (!hasInvalidValue) {
            // Set the active suggestion index
            setActiveSuggestionIndex(index)

            // Submit the measurement
            await addMeasurement(suggestionArray[index], parsedTargetValues, true, isFresh ? freshBatchId : currentBatchId)

            // Ensure the suggestion is marked as submitted in the database
            const suggestion = suggestionArray[index]
            if (suggestion && suggestion._suggestionId) {
              try {
                console.log(
                  `Batch submission: Marking suggestion ${suggestion._suggestionId} as submitted in database`
                )
                const result = await markSuggestionSubmittedAction(
                  suggestion._suggestionId
                )
                if (result.isSuccess) {
                  console.log(
                    `Batch submission: Successfully marked suggestion ${suggestion._suggestionId} as submitted`
                  )
                } else {
                  console.warn(
                    `Batch submission: Failed to mark suggestion as submitted: ${result.message}`
                  )
                }
              } catch (error) {
                console.error(
                  `Batch submission: Error marking suggestion as submitted:`,
                  error
                )
              }
            }

            submittedCount++

            // Update progress
            setBatchProgress(prev => ({ ...prev, current: prev.current + 1 }))

            // Explicitly update the submittedSuggestions state to ensure UI reflects the change
            setSubmittedSuggestions(prev => ({
              ...prev,
              [index]: true
            }))

            // Update our local copy as well
            updatedSubmittedSuggestions[index] = true
          }
        } else {
          // For single target
          const targetName = targetConfigs[0].name
          const value = parseFloat(suggestionTargetValues[targetName] || "")

          if (!isNaN(value)) {
            // Set the active suggestion index
            setActiveSuggestionIndex(index)

            // Submit the measurement
            await addMeasurement(suggestionArray[index], value, true, isFresh ? freshBatchId : currentBatchId)

            // Ensure the suggestion is marked as submitted in the database
            const suggestion = suggestionArray[index]
            if (suggestion && suggestion._suggestionId) {
              try {
                console.log(
                  `Batch submission: Marking suggestion ${suggestion._suggestionId} as submitted in database`
                )
                const result = await markSuggestionSubmittedAction(
                  suggestion._suggestionId
                )
                if (result.isSuccess) {
                  console.log(
                    `Batch submission: Successfully marked suggestion ${suggestion._suggestionId} as submitted`
                  )
                } else {
                  console.warn(
                    `Batch submission: Failed to mark suggestion as submitted: ${result.message}`
                  )
                }
              } catch (error) {
                console.error(
                  `Batch submission: Error marking suggestion as submitted:`,
                  error
                )
              }
            }

            submittedCount++

            // Update progress
            setBatchProgress(prev => ({ ...prev, current: prev.current + 1 }))

            // Explicitly update the submittedSuggestions state to ensure UI reflects the change
            setSubmittedSuggestions(prev => ({
              ...prev,
              [index]: true
            }))

            // Update our local copy as well
            updatedSubmittedSuggestions[index] = true
          }
        }
      } catch (error) {
        console.error(`Error submitting suggestion ${index}:`, error)
        errorCount++
      }
    }

    // Reset the flag and progress
    setIsAddingMeasurement(false)
    setBatchProgress({ current: 0, total: 0 })

    // Force a final update of the submittedSuggestions state with all the values from our local copy
    // This ensures the UI is fully up-to-date
    setSubmittedSuggestions(updatedSubmittedSuggestions)

    // Log the final state of submitted suggestions for debugging
    console.log("Final submitted suggestions state:", submittedSuggestions)
    console.log(
      "Local copy of submitted suggestions:",
      updatedSubmittedSuggestions
    )

    // Show a toast with the results
    if (submittedCount > 0) {
      toast({
        title: "Batch submission complete",
        description: `Successfully submitted ${submittedCount} measurements${errorCount > 0 ? ` (${errorCount} errors)` : ""}.`,
        variant: errorCount > 0 ? "destructive" : "default"
      })

      // Refresh the page to show the updated measurements
      router.refresh()
    } else if (errorCount > 0) {
      toast({
        title: "Batch submission failed",
        description: `Failed to submit any measurements (${errorCount} errors).`,
        variant: "destructive"
      })
    } else {
      toast({
        title: "No measurements submitted",
        description: "No valid measurements were found to submit.",
        variant: "destructive"
      })
    }
  }

  const handleManualSubmit = async () => {
    // Get target configs
    const targetConfigs = getTargetConfigs()

    // Generate a batch ID for multiple experiments
    // Always generate a batch ID when there are multiple experiments
    // This ensures they're grouped together in the History tab
    const batchId =
      manualExperiments.length > 1 ? `manual-batch-${Date.now()}` : null

    // Log batch information
    if (batchId) {
      console.log(
        `Generated batch ID for manual experiments: ${batchId} (count: ${manualExperiments.length})`
      )
    }

    // Track submission progress
    let submittedCount = 0
    let errorCount = 0

    // Set a flag to prevent multiple submissions
    setIsAddingMeasurement(true)

    // Initialize progress
    setBatchProgress({ current: 0, total: manualExperiments.length })

    // Process each manual experiment
    for (
      let experimentIndex = 0;
      experimentIndex < manualExperiments.length;
      experimentIndex++
    ) {
      // Get the experiment parameters
      const experimentParams = manualExperiments[experimentIndex]

      // Get the target values for this experiment
      const targetIndex = experimentIndex === 0 ? -1 : -1 - experimentIndex
      const experimentTargetValues = targetValues[targetIndex] || {}

      // Validate parameters
      let hasInvalidParams = false
      for (const param of optimization.config.parameters) {
        const value = experimentParams[param.name]

        if (value === undefined || value === "") {
          toast({
            title: "Missing parameter",
            description: `Please provide a value for ${param.name} in experiment ${experimentIndex + 1}`,
            variant: "destructive"
          })
          hasInvalidParams = true
          break
        }

        if (param.type === "NumericalDiscrete") {
          const numValue = typeof value === "string" ? parseFloat(value) : value
          if (isNaN(numValue)) {
            toast({
              title: "Invalid parameter value",
              description: `Please enter a valid number for ${param.name} in experiment ${experimentIndex + 1}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }

          // For NumericalDiscrete, check if the value is in the allowed values
          if (Array.isArray(param.values) && !param.values.includes(numValue)) {
            toast({
              title: "Invalid parameter value",
              description: `${numValue} is not a valid value for ${param.name}. Allowed values are: ${param.values.join(", ")}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }

          // Convert to number for submission
          experimentParams[param.name] = numValue
        } else if (param.type === "NumericalContinuous") {
          const numValue = typeof value === "string" ? parseFloat(value) : value
          if (isNaN(numValue)) {
            toast({
              title: "Invalid parameter value",
              description: `Please enter a valid number for ${param.name} in experiment ${experimentIndex + 1}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }

          // For NumericalContinuous, check if the value is within bounds
          if (
            param.bounds &&
            (numValue < param.bounds[0] || numValue > param.bounds[1])
          ) {
            toast({
              title: "Invalid parameter value",
              description: `${numValue} is outside the allowed range for ${param.name}. Range is: ${param.bounds[0]} to ${param.bounds[1]}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }

          // Convert to number for submission
          experimentParams[param.name] = numValue
        } else if (param.type === "CategoricalParameter") {
          // For CategoricalParameter, check if the value is in the allowed values
          if (Array.isArray(param.values) && !param.values.includes(value)) {
            toast({
              title: "Invalid parameter value",
              description: `"${value}" is not a valid value for ${param.name}. Allowed values are: ${param.values.join(", ")}`,
              variant: "destructive"
            })
            hasInvalidParams = true
            break
          }
        }
      }

      if (hasInvalidParams) {
        errorCount++
        continue
      }

      // For multi-target optimizations, validate all target values
      if (isMultiTarget()) {
        // Check if any target value is missing
        const missingTargets = targetConfigs.filter(
          target =>
            !experimentTargetValues[target.name] ||
            experimentTargetValues[target.name].trim() === ""
        )

        if (missingTargets.length > 0) {
          toast({
            title: "Missing target values",
            description: `Please enter values for all targets in experiment ${experimentIndex + 1}: ${missingTargets.map(t => t.name).join(", ")}.`,
            variant: "destructive"
          })
          errorCount++
          continue
        }

        // Parse and validate all target values
        const parsedTargetValues: Record<string, number> = {}
        let hasInvalidValue = false

        for (const target of targetConfigs) {
          const parsedValue = parseFloat(experimentTargetValues[target.name])
          if (isNaN(parsedValue)) {
            toast({
              title: "Invalid target value",
              description: `Please enter a valid number for ${target.name} in experiment ${experimentIndex + 1}`,
              variant: "destructive"
            })
            hasInvalidValue = true
            break
          }
          parsedTargetValues[target.name] = parsedValue
        }

        if (hasInvalidValue) {
          errorCount++
          continue
        }

        try {
          // Submit the measurement with the batch ID for multiple experiments
          console.log(
            `Submitting manual experiment ${experimentIndex + 1} with batch ID: ${batchId || "null"}`
          )
          const result = await addMeasurementWorkflowAction(
            optimization.optimizerId,
            experimentParams,
            parsedTargetValues,
            false,
            batchId
          )
          console.log(
            `Manual experiment ${experimentIndex + 1} submission result:`,
            result.isSuccess ? "success" : "failure"
          )

          if (result.isSuccess) {
            // Mark this experiment as submitted
            const targetIndex =
              experimentIndex === 0 ? -1 : -1 - experimentIndex
            setSubmittedSuggestions(prev => ({
              ...prev,
              [targetIndex]: true
            }))
            submittedCount++
          }

          // Update progress
          setBatchProgress(prev => ({ ...prev, current: prev.current + 1 }))
        } catch (error) {
          console.error(
            `Error submitting manual experiment ${experimentIndex + 1}:`,
            error
          )
          errorCount++
        }
      } else {
        // Single target optimization
        const targetName = targetConfigs[0].name
        const parsedValue = parseFloat(experimentTargetValues[targetName] || "")

        if (isNaN(parsedValue)) {
          toast({
            title: "Invalid target value",
            description: `Please enter a valid number for the target value in experiment ${experimentIndex + 1}`,
            variant: "destructive"
          })
          errorCount++
          continue
        }

        try {
          // Submit the measurement with the batch ID for multiple experiments
          console.log(
            `Submitting manual experiment ${experimentIndex + 1} with batch ID: ${batchId || "null"}`
          )
          const result = await addMeasurementWorkflowAction(
            optimization.optimizerId,
            experimentParams,
            parsedValue,
            false,
            batchId
          )
          console.log(
            `Manual experiment ${experimentIndex + 1} submission result:`,
            result.isSuccess ? "success" : "failure"
          )

          if (result.isSuccess) {
            // Mark this experiment as submitted
            const targetIndex =
              experimentIndex === 0 ? -1 : -1 - experimentIndex
            setSubmittedSuggestions(prev => ({
              ...prev,
              [targetIndex]: true
            }))
            submittedCount++
          }

          // Update progress
          setBatchProgress(prev => ({ ...prev, current: prev.current + 1 }))
        } catch (error) {
          console.error(
            `Error submitting manual experiment ${experimentIndex + 1}:`,
            error
          )
          errorCount++
        }
      }
    }

    // Reset the flag and progress
    setIsAddingMeasurement(false)
    setBatchProgress({ current: 0, total: 0 })

    // Show a toast with the results
    if (submittedCount > 0) {
      toast({
        title:
          submittedCount > 1
            ? "Batch submission complete"
            : "Measurement submitted",
        description:
          submittedCount > 1
            ? `Successfully submitted ${submittedCount} measurements${errorCount > 0 ? ` (${errorCount} errors)` : ""}.`
            : "Successfully added measurement to the optimization",
        variant: errorCount > 0 ? "destructive" : "default"
      })

      // Show a success message similar to the suggested experiments
      if (errorCount === 0) {
        toast({
          title: "Optimization updated",
          description:
            submittedCount > 1
              ? "All experiments have been added to the optimization history. You can now enter new target values for the same parameters or modify parameters and submit again."
              : "The experiment has been added to the optimization history. You can now enter new target values for the same parameters or modify parameters and submit again."
        })
      }

      // Keep the parameter values but reset the submission state
      // This allows users to submit new target values for the same parameters

      // Clear the submittedSuggestions state for manual experiments
      setSubmittedSuggestions(prev => {
        const updated = { ...prev }
        // Remove all negative indices (manual experiments)
        Object.keys(updated).forEach(key => {
          const index = parseInt(key)
          if (index < 0) delete updated[index]
        })
        return updated
      })

      // Reset target values
      const initialTargetValues: Record<number, Record<string, string>> = {}
      initialTargetValues[-1] = targetConfigs.reduce(
        (acc, target) => {
          acc[target.name] = ""
          return acc
        },
        {} as Record<string, string>
      )
      setTargetValues(prev => {
        const updated = { ...prev }
        // Keep suggested experiment values, but clear manual ones
        Object.keys(updated).forEach(key => {
          const index = parseInt(key)
          if (index < 0) delete updated[index]
        })
        return { ...updated, ...initialTargetValues }
      })

      // Refresh the page to show the updated measurements
      router.refresh()
    } else if (errorCount > 0) {
      toast({
        title: "Submission failed",
        description: `Failed to submit any measurements (${errorCount} errors).`,
        variant: "destructive"
      })
    } else {
      toast({
        title: "No measurements submitted",
        description: "No valid measurements were found to submit.",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Run Experiments</h2>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="samples">
            <svg
              className="mr-2 size-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" />
              <circle cx="8.5" cy="8.5" r="1.5" />
              <circle cx="15.5" cy="8.5" r="1.5" />
              <circle cx="8.5" cy="15.5" r="1.5" />
              <circle cx="15.5" cy="15.5" r="1.5" />
            </svg>
            Sample Generation
          </TabsTrigger>
          <TabsTrigger value="suggested">
            <Lightbulb className="mr-2 size-4" />
            Suggested Experiments
          </TabsTrigger>
          <TabsTrigger value="manual">
            <Beaker className="mr-2 size-4" />
            Manual Experiments
          </TabsTrigger>
        </TabsList>

        <TabsContent value="samples" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Sample Generation</CardTitle>
              <CardDescription>
                Generate sample points using Latin Hypercube Sampling or random
                sampling
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <h3 className="text-sm font-medium">
                      Generation Parameters
                    </h3>
                    <p className="text-muted-foreground text-xs">
                      Configure how samples are generated
                    </p>
                  </div>

                  {/* Add Save Samples button */}
                  {samples.length > 0 && (
                    <Button
                      onClick={saveSamples}
                      disabled={isGeneratingSamples || isSavingSamples}
                      variant="secondary"
                      size="sm"
                    >
                      {isSavingSamples ? (
                        <>
                          <Loader2 className="mr-2 size-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 size-4" />
                          Save Samples
                        </>
                      )}
                    </Button>
                  )}
                </div>

                <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-end">
                  <div className="w-full sm:w-64">
                    <Label htmlFor="sampleCount" className="text-sm">
                      Number of Samples
                    </Label>
                    <div className="mt-1 flex items-center gap-2">
                      <Input
                        id="sampleCount"
                        type="number"
                        min="1"
                        max="100"
                        defaultValue="10"
                        className="w-full"
                        onChange={e => {
                          // Ensure value is between 1 and 100
                          const value = parseInt(e.target.value)
                          if (value < 1) e.target.value = "1"
                          if (value > 100) e.target.value = "100"
                        }}
                      />
                      <div className="text-muted-foreground text-xs">
                        (1-100)
                      </div>
                    </div>
                  </div>

                  <div className="w-full sm:w-64">
                    <Label htmlFor="samplingStrategy" className="text-sm">
                      Sampling Strategy
                    </Label>
                    <select
                      id="samplingStrategy"
                      className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={samplingStrategy}
                      onChange={e =>
                        setSamplingStrategy(e.target.value as "LHS" | "random")
                      }
                    >
                      <option value="LHS">Latin Hypercube Sampling</option>
                      <option value="random">Random Sampling</option>
                    </select>
                  </div>

                  <div className="w-full sm:w-64">
                    <Label htmlFor="seed" className="text-sm">
                      Random Seed (Optional)
                    </Label>
                    <Input
                      id="seed"
                      type="number"
                      placeholder="Leave empty for random"
                      className="w-full"
                    />
                  </div>

                  <div className="flex w-full items-center gap-2 sm:w-auto">
                    <Button
                      className="w-full sm:w-auto"
                      onClick={() => {
                        const sampleCountInput = document.getElementById(
                          "sampleCount"
                        ) as HTMLInputElement
                        const seedInput = document.getElementById(
                          "seed"
                        ) as HTMLInputElement
                        const sampleCount =
                          parseInt(sampleCountInput.value) || 10
                        const seed = seedInput.value
                          ? parseInt(seedInput.value)
                          : undefined
                        generateSamples(sampleCount, samplingStrategy, seed)
                      }}
                      disabled={isGeneratingSamples || isLoadingSamples}
                    >
                      {isGeneratingSamples ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <svg
                          className="mr-2 size-4"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <rect x="3" y="3" width="18" height="18" rx="2" />
                          <circle cx="8.5" cy="8.5" r="1.5" />
                          <circle cx="15.5" cy="8.5" r="1.5" />
                          <circle cx="8.5" cy="15.5" r="1.5" />
                          <circle cx="15.5" cy="15.5" r="1.5" />
                        </svg>
                      )}
                      {isGeneratingSamples
                        ? "Generating Samples..."
                        : "Generate Samples"}
                    </Button>

                    <Button
                      className="w-full sm:w-auto"
                      onClick={loadSavedSamples}
                      disabled={isGeneratingSamples || isLoadingSamples}
                      variant="outline"
                    >
                      {isLoadingSamples ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <ArrowDown className="mr-2 size-4" />
                      )}
                      {isLoadingSamples
                        ? "Loading Samples..."
                        : "Load Saved Samples"}
                    </Button>

                    {samples.length > 0 && (
                      <>
                        <Button
                          variant="outline"
                          className="w-full border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 sm:w-auto"
                          onClick={() => {
                            // Clear all samples
                            setSamples([])
                            toast({
                              title: "Cleared",
                              description: "All samples have been cleared."
                            })
                          }}
                        >
                          <X className="mr-2 size-4" />
                          Clear All
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {samples.length > 0 ? (
                <div className="mt-6 space-y-6">
                  {/* Submit All button for batch submissions */}
                  <div className="flex items-center justify-end">
                    <Button
                      variant="default"
                      size="lg"
                      className="bg-green-600 hover:bg-green-700"
                      onClick={submitSamples}
                      disabled={
                        isAddingMeasurement ||
                        samples.filter((_, index) => hasAllTargetValues(index))
                          .length === 0
                      }
                    >
                      {isAddingMeasurement ? (
                        <>
                          <Loader2 className="mr-2 size-5 animate-spin" />
                          {batchProgress.total > 0 &&
                            `${batchProgress.current}/${batchProgress.total}`}
                        </>
                      ) : (
                        <Check className="mr-2 size-5" />
                      )}
                      {isAddingMeasurement && batchProgress.total > 0
                        ? "Submitting..."
                        : "Submit Samples"}
                    </Button>
                  </div>

                  {/* Samples table with target value inputs */}
                  {samples.map((sample, index) => (
                    <Card
                      key={index}
                      className={`border-l-4 ${
                        submittedSuggestions[index]
                          ? "border-l-green-500 dark:border-l-green-400"
                          : hasAllTargetValues(index)
                            ? "border-l-blue-500 dark:border-l-blue-400"
                            : "border-l-amber-500 dark:border-l-amber-400"
                      }`}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <CardTitle className="text-lg">
                              Sample #{index + 1}
                            </CardTitle>
                            <Badge
                              variant="outline"
                              className="border-gray-200 bg-gray-100 text-gray-800"
                            >
                              {sample._samplingMethod || "LHS"}
                            </Badge>
                          </div>
                          {submittedSuggestions[index] ? (
                            <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                              Submitted
                            </span>
                          ) : hasAllTargetValues(index) ? (
                            <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                              Ready to submit
                            </span>
                          ) : (
                            <span className="rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-800">
                              Needs target values
                            </span>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                          {Object.entries(sample)
                            .filter(([key]) => !key.startsWith("_"))
                            .map(([key, value]) => (
                              <div key={key} className="space-y-1.5">
                                <Label htmlFor={`param-${index}-${key}`}>
                                  {key}
                                </Label>
                                <Input
                                  id={`param-${index}-${key}`}
                                  value={
                                    typeof value === "number"
                                      ? value.toFixed(value % 1 === 0 ? 0 : 2)
                                      : String(value)
                                  }
                                  readOnly
                                  className="bg-muted"
                                />
                              </div>
                            ))}
                          {/* Show target inputs based on whether it's multi-target or not */}
                          {getTargetConfigs().map((target, targetIndex) => (
                            <div
                              key={`${index}-${target.name}`}
                              className="space-y-1.5"
                            >
                              <Label
                                htmlFor={`sample-result-${index}-${targetIndex}`}
                                className="flex items-center"
                              >
                                {target.name}
                                {target.mode === "MAX" ? (
                                  <ArrowUp className="ml-1 size-4 text-green-500" />
                                ) : (
                                  <ArrowDown className="ml-1 size-4 text-green-500" />
                                )}
                              </Label>
                              <div className="space-y-1">
                                <Input
                                  id={`sample-result-${index}-${targetIndex}`}
                                  placeholder={`25.00`}
                                  type="number"
                                  step="0.01"
                                  value={
                                    targetValues[index]?.[target.name] || ""
                                  }
                                  onChange={e =>
                                    handleTargetValueChange(
                                      index,
                                      target.name,
                                      e.target.value
                                    )
                                  }
                                  onBlur={e =>
                                    updateSampleTargetValue(
                                      index,
                                      target.name,
                                      e.target.value
                                    )
                                  }
                                  className={
                                    !targetValues[index]?.[target.name] &&
                                    !submittedSuggestions[index]
                                      ? "border-amber-300 focus:ring-amber-500"
                                      : ""
                                  }
                                  autoFocus={
                                    index === 0 &&
                                    targetIndex === 0 &&
                                    !submittedSuggestions[index] &&
                                    samples.length > 0
                                  }
                                  disabled={
                                    submittedSuggestions[index] ||
                                    isAddingMeasurement
                                  }
                                />
                                <p className="text-muted-foreground text-xs">
                                  Use decimal point (e.g., 25.08)
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                      <CardFooter className="justify-end space-x-2">
                        <Button
                          onClick={() => {
                            // Submit this individual sample
                            const sampleTargetValues = targetValues[index] || {}

                            if (isMultiTarget()) {
                              // For multi-target, validate all target values
                              const targetConfigs = getTargetConfigs()
                              const missingTargets = targetConfigs.filter(
                                target =>
                                  !sampleTargetValues[target.name] ||
                                  sampleTargetValues[target.name].trim() === ""
                              )

                              if (missingTargets.length > 0) {
                                toast({
                                  title: "Missing target values",
                                  description: `Please enter values for all targets: ${missingTargets.map(t => t.name).join(", ")}.`,
                                  variant: "destructive"
                                })
                                return
                              }

                              // Check if the sample has been saved to the database
                              const sampleId = sample._sampleId
                              if (!sampleId) {
                                toast({
                                  title: "Error",
                                  description:
                                    "Sample ID not found. Please try again.",
                                  variant: "destructive"
                                })
                                return
                              }

                              // If the sample ID is a client-side ID (contains an underscore), we'll continue anyway
                              // The sample will be saved automatically during submission

                              // Submit the sample
                              submitSamplesWorkflowAction(
                                optimization.optimizerId,
                                [sampleId]
                              )
                                .then(result => {
                                  if (result.isSuccess) {
                                    toast({
                                      title: "Sample submitted",
                                      description:
                                        "Successfully submitted sample to the optimization"
                                    })

                                    // Mark as submitted
                                    setSubmittedSuggestions(prev => ({
                                      ...prev,
                                      [index]: true
                                    }))

                                    // Refresh the page
                                    router.refresh()
                                  } else {
                                    toast({
                                      title: "Error submitting sample",
                                      description: result.message,
                                      variant: "destructive"
                                    })
                                  }
                                })
                                .catch(error => {
                                  console.error(
                                    "Error submitting sample:",
                                    error
                                  )
                                  toast({
                                    title: "Error",
                                    description:
                                      "Failed to submit sample. Please try again.",
                                    variant: "destructive"
                                  })
                                })
                            } else {
                              // For single target
                              const targetName = getTargetConfigs()[0].name
                              const value = parseFloat(
                                sampleTargetValues[targetName] || ""
                              )
                              if (isNaN(value)) {
                                toast({
                                  title: "Invalid value",
                                  description: "Please enter a valid number",
                                  variant: "destructive"
                                })
                                return
                              }

                              // Check if the sample has been saved to the database
                              const sampleId = sample._sampleId
                              if (!sampleId) {
                                toast({
                                  title: "Error",
                                  description:
                                    "Sample ID not found. Please try again.",
                                  variant: "destructive"
                                })
                                return
                              }

                              // If the sample ID is a client-side ID (contains an underscore), we'll continue anyway
                              // The sample will be saved automatically during submission

                              // Submit the sample
                              submitSamplesWorkflowAction(
                                optimization.optimizerId,
                                [sampleId]
                              )
                                .then(result => {
                                  if (result.isSuccess) {
                                    toast({
                                      title: "Sample submitted",
                                      description:
                                        "Successfully submitted sample to the optimization"
                                    })

                                    // Mark as submitted
                                    setSubmittedSuggestions(prev => ({
                                      ...prev,
                                      [index]: true
                                    }))

                                    // Refresh the page
                                    router.refresh()
                                  } else {
                                    toast({
                                      title: "Error submitting sample",
                                      description: result.message,
                                      variant: "destructive"
                                    })
                                  }
                                })
                                .catch(error => {
                                  console.error(
                                    "Error submitting sample:",
                                    error
                                  )
                                  toast({
                                    title: "Error",
                                    description:
                                      "Failed to submit sample. Please try again.",
                                    variant: "destructive"
                                  })
                                })
                            }
                          }}
                          disabled={
                            isAddingMeasurement ||
                            !hasAllTargetValues(index) ||
                            submittedSuggestions[index]
                          }
                        >
                          {isAddingMeasurement ? (
                            <Loader2 className="mr-2 size-4 animate-spin" />
                          ) : submittedSuggestions[index] ? (
                            <Check className="mr-2 size-4" />
                          ) : (
                            <Check className="mr-2 size-4" />
                          )}
                          {submittedSuggestions[index]
                            ? "Submitted"
                            : "Submit Sample"}
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="mt-8 text-center">
                  {isGeneratingSamples || isLoadingSamples ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <Loader2 className="text-muted-foreground mb-4 size-12 animate-spin" />
                      <p className="text-muted-foreground">
                        {isGeneratingSamples
                          ? "Generating samples..."
                          : "Loading saved samples..."}
                      </p>
                    </div>
                  ) : (
                    <div className="text-muted-foreground">
                      <p className="mb-4">No samples generated yet.</p>
                      <p>
                        Click "Generate Samples" to create sample points for
                        your parameter space.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Alert>
            <svg
              className="size-5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" />
              <circle cx="8.5" cy="8.5" r="1.5" />
              <circle cx="15.5" cy="8.5" r="1.5" />
              <circle cx="8.5" cy="15.5" r="1.5" />
              <circle cx="15.5" cy="15.5" r="1.5" />
            </svg>
            <AlertTitle>About Sample Generation</AlertTitle>
            <AlertDescription>
              <p className="mb-2">
                Sample generation creates points within your parameter space
                that can be used for initial experiments or exploration.
              </p>
              <p className="text-muted-foreground text-sm">
                <strong>Latin Hypercube Sampling (LHS):</strong> Provides better
                coverage of the parameter space than random sampling by ensuring
                samples are distributed across the entire range of each
                parameter. This is the recommended method for most cases.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Random Sampling:</strong> Generates completely random
                points within the parameter space. This can be useful for
                certain applications but may not provide as even coverage as
                LHS.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Random Seed:</strong> Setting a specific seed value
                ensures reproducible results. Leave empty for truly random
                generation.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Saving Samples:</strong> Click the "Save Samples" button
                to save generated samples to the database. This allows you to
                close the application and return later to input target values.
                Samples will be automatically saved when submitted if they
                haven't been saved already.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Submitting Samples:</strong> After entering target
                values for your samples, submit them to the Bayesian
                optimization system to improve future suggestions. You can
                submit samples without explicitly saving them first.
              </p>
            </AlertDescription>
          </Alert>
        </TabsContent>

        <TabsContent value="suggested" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI-Suggested Experiments</CardTitle>
              <CardDescription>
                Get experiment suggestions from the optimization API
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <h3 className="text-sm font-medium">Request Parameters</h3>
                    <p className="text-muted-foreground text-xs">
                      Get suggestions for your next experiments
                    </p>
                  </div>

                  {/* Add Save Suggestions button */}
                  {suggestions.length > 0 && (
                    <Button
                      onClick={saveSuggestions}
                      disabled={isLoadingSuggestions || isSavingSuggestions}
                      variant="secondary"
                      size="sm"
                    >
                      {isSavingSuggestions ? (
                        <>
                          <Loader2 className="mr-2 size-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 size-4" />
                          Save Suggestions
                        </>
                      )}
                    </Button>
                  )}
                </div>

                <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-end">
                  <div className="w-full sm:w-64">
                    <Label htmlFor="batchSize" className="text-sm">
                      Number of Suggestions
                    </Label>
                    <div className="mt-1 flex items-center gap-2">
                      <Input
                        id="batchSize"
                        type="number"
                        min="1"
                        max="100"
                        defaultValue="1"
                        className="w-full"
                        onChange={e => {
                          // Ensure value is between 1 and 100
                          const value = parseInt(e.target.value)
                          if (value < 1) e.target.value = "1"
                          if (value > 100) e.target.value = "100"
                        }}
                      />
                      <div className="text-muted-foreground text-xs">
                        (1-100)
                      </div>
                    </div>
                  </div>

                  <div className="flex w-full items-center gap-2 sm:w-auto">
                    <Button
                      className="w-full sm:w-auto"
                      onClick={() => {
                        const batchSizeInput = document.getElementById(
                          "batchSize"
                        ) as HTMLInputElement
                        const batchSize = parseInt(batchSizeInput.value) || 1
                        getSuggestions(batchSize)
                      }}
                      disabled={isLoadingSuggestions}
                    >
                      {isLoadingSuggestions ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <Lightbulb className="mr-2 size-4" />
                      )}
                      Get Suggestions
                    </Button>

                    {suggestions.length > 0 && (
                      <Button
                        variant="outline"
                        className="w-full border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 sm:w-auto"
                        onClick={() => {
                          // Clear all suggestions and target values
                          setSuggestions([])
                          setTargetValues({})
                          setSubmittedSuggestions({})
                          setCurrentBatchId(null)
                          toast({
                            title: "Cleared",
                            description: "All suggestions have been cleared."
                          })
                        }}
                      >
                        <X className="mr-2 size-4" />
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {suggestions.length > 0 ? (
                <div className="mt-6 space-y-6">
                  {/* Submit All button for batch submissions */}
                  <div className="mb-4 flex justify-end">
                      <Button
                        variant="default"
                        size="lg"
                        className="bg-green-600 hover:bg-green-700"
                        onClick={handleSubmitAllSuggestions}
                        disabled={
                          isAddingMeasurement ||
                          suggestions.filter((_, index) => {
                            // Check if this suggestion has target values and hasn't been submitted
                            const suggestionTargetValues = targetValues[index] || {};
                            return Object.keys(suggestionTargetValues).length > 0 &&
                                   !submittedSuggestions[index];
                          }).length === 0
                        }
                      >
                        {isAddingMeasurement ? (
                          <>
                            <Loader2 className="mr-2 size-5 animate-spin" />
                            {batchProgress.total > 0 &&
                              `${batchProgress.current}/${batchProgress.total}`}
                          </>
                        ) : (
                          <Check className="mr-2 size-5" />
                        )}
                        {isAddingMeasurement && batchProgress.total > 0
                          ? "Submitting..."
                          : suggestions.length > 1 ? "Submit All Suggestions" : "Submit Suggestion"}
                      </Button>
                    </div>
                  {suggestions.map((suggestion, index) => (
                    <Card
                      key={index}
                      className={`border-l-4 ${
                        submittedSuggestions[index]
                          ? "border-l-green-500 dark:border-l-green-400"
                          : hasAllTargetValues(index)
                            ? "border-l-blue-500 dark:border-l-blue-400"
                            : "border-l-amber-500 dark:border-l-amber-400"
                      }`}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">
                            Suggested Experiment #{index + 1}
                          </CardTitle>
                          {submittedSuggestions[index] ? (
                            <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                              Submitted
                            </span>
                          ) : hasAllTargetValues(index) ? (
                            <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                              Ready to submit
                            </span>
                          ) : (
                            <span className="rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-800">
                              Needs target values
                            </span>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                          {Object.entries(suggestion)
                            .filter(([key]) => !key.startsWith("_"))
                            .map(([key, value]) => (
                              <div key={key} className="space-y-1.5">
                                <Label htmlFor={`param-${index}-${key}`}>
                                  {key}
                                </Label>
                                <Input
                                  id={`param-${index}-${key}`}
                                  value={
                                    typeof value === "number"
                                      ? value.toFixed(value % 1 === 0 ? 0 : 2)
                                      : String(value)
                                  }
                                  readOnly
                                  className="bg-muted"
                                />
                              </div>
                            ))}
                          {/* Show target inputs based on whether it's multi-target or not */}
                          {getTargetConfigs().map((target, targetIndex) => (
                            <div
                              key={`${index}-${target.name}`}
                              className="space-y-1.5"
                            >
                              <Label
                                htmlFor={`result-${index}-${targetIndex}`}
                                className="flex items-center"
                              >
                                {target.name}
                                {target.mode === "MAX" ? (
                                  <ArrowUp className="ml-1 size-4 text-green-500" />
                                ) : (
                                  <ArrowDown className="ml-1 size-4 text-green-500" />
                                )}
                              </Label>
                              <div className="space-y-1">
                                <Input
                                  id={`result-${index}-${targetIndex}`}
                                  placeholder={`25.00`}
                                  type="number"
                                  step="0.01"
                                  value={
                                    targetValues[index]?.[target.name] || ""
                                  }
                                  onChange={e =>
                                    handleTargetValueChange(
                                      index,
                                      target.name,
                                      e.target.value
                                    )
                                  }
                                  onFocus={() =>
                                    setActiveSuggestionIndex(index)
                                  }
                                  className={
                                    !targetValues[index]?.[target.name] &&
                                    !submittedSuggestions[index]
                                      ? "border-amber-300 focus:ring-amber-500"
                                      : ""
                                  }
                                  autoFocus={
                                    index === 0 &&
                                    targetIndex === 0 &&
                                    !submittedSuggestions[index] &&
                                    suggestions.length > 0
                                  }
                                  disabled={
                                    submittedSuggestions[index] ||
                                    isAddingMeasurement
                                  }
                                />
                                <p className="text-muted-foreground text-xs">
                                  Use decimal point (e.g., 25.08)
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                      <CardFooter className="justify-end space-x-2">
                        <Button
                          onClick={() => {
                            // Set the active suggestion index to this card
                            setActiveSuggestionIndex(index)

                            // Get the target values for this suggestion
                            const suggestionTargetValues =
                              targetValues[index] || {}

                            if (isMultiTarget()) {
                              // For multi-target, validate all target values
                              const targetConfigs = getTargetConfigs()
                              const missingTargets = targetConfigs.filter(
                                target =>
                                  !suggestionTargetValues[target.name] ||
                                  suggestionTargetValues[target.name].trim() ===
                                    ""
                              )

                              if (missingTargets.length > 0) {
                                toast({
                                  title: "Missing target values",
                                  description: `Please enter values for all targets: ${missingTargets.map(t => t.name).join(", ")}.`,
                                  variant: "destructive"
                                })
                                return
                              }

                              // Parse and validate all target values
                              const parsedTargetValues: Record<string, number> =
                                {}
                              let hasInvalidValue = false

                              for (const target of targetConfigs) {
                                const parsedValue = parseFloat(
                                  suggestionTargetValues[target.name]
                                )
                                if (isNaN(parsedValue)) {
                                  toast({
                                    title: "Invalid target value",
                                    description: `Please enter a valid number for ${target.name}`,
                                    variant: "destructive"
                                  })
                                  hasInvalidValue = true
                                  break
                                }
                                parsedTargetValues[target.name] = parsedValue
                              }

                              if (!hasInvalidValue) {
                                addMeasurement(
                                  suggestion,
                                  parsedTargetValues,
                                  true
                                )
                              }
                            } else {
                              // For single target
                              const targetName = getTargetConfigs()[0].name
                              const value = parseFloat(
                                suggestionTargetValues[targetName] || ""
                              )
                              if (!isNaN(value)) {
                                addMeasurement(suggestion, value, true)
                              } else {
                                toast({
                                  title: "Invalid value",
                                  description: "Please enter a valid number",
                                  variant: "destructive"
                                })
                              }
                            }
                          }}
                          disabled={
                            isAddingMeasurement ||
                            !hasAllTargetValues(index) ||
                            submittedSuggestions[index]
                          }
                        >
                          {isAddingMeasurement ? (
                            <Loader2 className="mr-2 size-4 animate-spin" />
                          ) : submittedSuggestions[index] ? (
                            <Check className="mr-2 size-4" />
                          ) : (
                            <Check className="mr-2 size-4" />
                          )}
                          {submittedSuggestions[index]
                            ? "Submitted"
                            : "Submit Measurement"}
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="mt-8 text-center">
                  {isLoadingSuggestions ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <Loader2 className="text-muted-foreground mb-4 size-12 animate-spin" />
                      <p className="text-muted-foreground">
                        {loadingMode === "saved"
                          ? "Loading saved suggestions..."
                          : "Generating new suggestions..."}
                      </p>
                    </div>
                  ) : (
                    <div className="text-muted-foreground">
                      <p className="mb-4">No suggestions loaded yet.</p>
                      <p>
                        Click "Get Suggestions" to receive recommendations for
                        your next experiments.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Alert>
            <Lightbulb className="size-5" />
            <AlertTitle>Using AI suggestions</AlertTitle>
            <AlertDescription>
              <p className="mb-2">
                The optimizer uses Bayesian optimization to suggest the most
                promising experiments based on your results so far. Each new
                measurement helps the AI learn and improve its suggestions.
              </p>
              <p className="text-muted-foreground text-sm">
                <strong>Batch Size:</strong> You can request between 1 and 100
                suggestions at once. Smaller batches (1-5) are more focused on
                immediate improvement, while larger batches provide more diverse
                exploration options. For most cases, 1-10 suggestions is
                optimal.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Target Values:</strong> Enter numerical values using a
                decimal point (e.g., 25.08). The system does not accept comma
                separators (25,08) or other formats. Values are displayed with
                two decimal places by default.
              </p>
            </AlertDescription>
          </Alert>

          <Alert
            variant="outline"
            className="border-amber-200 bg-amber-50 dark:border-amber-900 dark:bg-amber-950/50"
          >
            <AlertCircle className="size-5 text-amber-600 dark:text-amber-400" />
            <AlertTitle>Troubleshooting</AlertTitle>
            <AlertDescription>
              <p className="mb-2">
                If you encounter errors when getting suggestions or adding
                measurements, it may be because:
              </p>
              <ul className="list-disc space-y-1 pl-5 text-sm">
                <li>The optimization was deleted from the backend</li>
                <li>The backend server was restarted and lost its state</li>
                <li>There's a mismatch between frontend and backend IDs</li>
              </ul>
              <p className="mt-2 text-sm">
                The app will try to recover automatically in most cases. If
                errors persist, try refreshing the page or creating a new
                optimization.
              </p>
            </AlertDescription>
          </Alert>
        </TabsContent>

        <TabsContent value="manual" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Manual Experiment Entry</CardTitle>
              <CardDescription>
                Submit your own experiment parameters and results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <h3 className="text-sm font-medium">Experiment Controls</h3>
                    <p className="text-muted-foreground text-xs">
                      Add and manage your manual experiments
                    </p>
                  </div>
                </div>

                <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-end">
                  <div className="w-full sm:w-64">
                    <Label htmlFor="experimentCount" className="text-sm">
                      Number of Experiments
                    </Label>
                    <div className="mt-1 flex items-center gap-2">
                      <Input
                        id="experimentCount"
                        type="number"
                        min="1"
                        max="10"
                        defaultValue="1"
                        className="w-full"
                        onChange={e => {
                          // Ensure value is between 1 and 10
                          let value = parseInt(e.target.value)
                          if (isNaN(value)) value = 1
                          if (value < 1) value = 1
                          if (value > 10) value = 10

                          // Update the input value
                          e.target.value = value.toString()
                        }}
                      />
                      <div className="text-muted-foreground text-xs">
                        (1-10)
                      </div>
                    </div>
                  </div>

                  <div className="flex w-full items-center gap-2 sm:w-auto">
                    <Button
                      variant="default"
                      className="w-full sm:w-auto"
                      onClick={() => {
                        const countInput = document.getElementById(
                          "experimentCount"
                        ) as HTMLInputElement
                        const targetCount = parseInt(countInput.value) || 1
                        const currentCount = manualExperiments.length

                        // Calculate how many experiments to add
                        const toAdd = Math.max(
                          0,
                          Math.min(
                            targetCount - currentCount,
                            10 - currentCount
                          )
                        )

                        // Create initial parameters for a new experiment with valid values
                        const createInitialParams = () => {
                          return optimization.config.parameters.reduce(
                            (acc, param) => {
                              if (param.type === "NumericalDiscrete") {
                                // Ensure we're using a valid value from the allowed values
                                if (param.values && param.values.length > 0) {
                                  // Convert string values to numbers for numerical parameters
                                  acc[param.name] =
                                    typeof param.values[0] === "string"
                                      ? parseFloat(param.values[0])
                                      : param.values[0]
                                } else {
                                  acc[param.name] = 0
                                }
                              } else if (param.type === "NumericalContinuous") {
                                // Ensure we're within bounds
                                acc[param.name] = param.bounds
                                  ? param.bounds[0]
                                  : 0
                              } else if (
                                param.type === "CategoricalParameter"
                              ) {
                                // Use the first allowed value
                                acc[param.name] =
                                  param.values && param.values.length > 0
                                    ? param.values[0]
                                    : ""
                              }
                              return acc
                            },
                            {} as Record<string, any>
                          )
                        }

                        // Add the required number of experiments with valid initial values
                        if (toAdd > 0) {
                          const newExperiments = []
                          for (let i = 0; i < toAdd; i++) {
                            newExperiments.push(createInitialParams())
                          }
                          setManualExperiments(prev => [
                            ...prev,
                            ...newExperiments
                          ])
                        }

                        // If we need to remove experiments
                        if (targetCount < currentCount) {
                          // Keep the first targetCount experiments and remove the rest
                          setManualExperiments(prev =>
                            prev.slice(0, targetCount)
                          )

                          // Clear target values for removed experiments
                          setTargetValues(prev => {
                            const updated = { ...prev }
                            Object.keys(updated).forEach(key => {
                              const index = parseInt(key)
                              // Keep indices for experiments we want to keep (-1 for first experiment, -2 for second, etc.)
                              // Remove indices for experiments beyond our target count
                              if (index < -1 && index < -targetCount) {
                                delete updated[index]
                              }
                            })
                            return updated
                          })

                          // Show notification
                          toast({
                            title: "Experiments updated",
                            description: `Reduced to ${targetCount} experiment${targetCount !== 1 ? "s" : ""}.`
                          })
                        } else if (toAdd > 0) {
                          // Show notification for added experiments
                          toast({
                            title: "Experiments updated",
                            description: `Set to ${targetCount} experiment${targetCount !== 1 ? "s" : ""}.`
                          })
                        }
                      }}
                      disabled={isAddingMeasurement}
                    >
                      {isAddingMeasurement ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <Beaker className="mr-2 size-4" />
                      )}
                      Set Experiments
                    </Button>

                    {manualExperiments.length > 1 && (
                      <Button
                        variant="outline"
                        className="w-full border-red-200 text-red-500 hover:bg-red-50 hover:text-red-600 sm:w-auto"
                        onClick={() => {
                          // Remove all but the first experiment
                          setManualExperiments(prev => [prev[0]])

                          // Clear target values for removed experiments
                          setTargetValues(prev => {
                            const updated = { ...prev }
                            Object.keys(updated).forEach(key => {
                              const index = parseInt(key)
                              if (index < -1) delete updated[index]
                            })
                            return updated
                          })

                          toast({
                            title: "Cleared",
                            description:
                              "All additional experiments have been cleared."
                          })
                        }}
                        disabled={isAddingMeasurement}
                      >
                        <X className="mr-2 size-4" />
                        Clear All
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {/* Submit All button for batch submissions */}
              {manualExperiments.length > 1 && (
                <div className="mb-4 flex justify-end">
                  <Button
                    variant="default"
                    size="lg"
                    className="bg-green-600 hover:bg-green-700"
                    onClick={handleManualSubmit}
                    disabled={
                      isAddingMeasurement ||
                      Object.values(targetValues).every(v => !v)
                    }
                  >
                    {isAddingMeasurement ? (
                      <>
                        <Loader2 className="mr-2 size-5 animate-spin" />
                        {batchProgress.total > 0 &&
                          `${batchProgress.current}/${batchProgress.total}`}
                      </>
                    ) : (
                      <Check className="mr-2 size-5" />
                    )}
                    {isAddingMeasurement && batchProgress.total > 0
                      ? "Submitting..."
                      : "Submit All Experiments"}
                  </Button>
                </div>
              )}

              {/* Manual experiments */}
              <div className="mt-6 space-y-6">
                {manualExperiments.map((experimentParams, experimentIndex) => {
                  // Calculate the correct target value index
                  const targetValueIndex =
                    experimentIndex === 0 ? -1 : -1 - experimentIndex

                  // Check if all target values are filled for this experiment
                  const hasAllTargetsForExperiment = () => {
                    if (!targetValues[targetValueIndex]) return false

                    const targetConfigs = getTargetConfigs()
                    const experimentTargetValues =
                      targetValues[targetValueIndex] || {}

                    return targetConfigs.every(
                      target =>
                        experimentTargetValues[target.name] &&
                        experimentTargetValues[target.name].trim() !== ""
                    )
                  }

                  return (
                    <Card
                      key={experimentIndex}
                      className={`border-l-4 ${
                        submittedSuggestions[targetValueIndex]
                          ? "border-l-green-500 dark:border-l-green-400"
                          : hasAllTargetsForExperiment()
                            ? "border-l-blue-500 dark:border-l-blue-400"
                            : "border-l-amber-500 dark:border-l-amber-400"
                      }`}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">
                            Manual Experiment #{experimentIndex + 1}
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            {submittedSuggestions[targetValueIndex] ? (
                              <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                                Submitted
                              </span>
                            ) : hasAllTargetsForExperiment() ? (
                              <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                                Ready to submit
                              </span>
                            ) : (
                              <span className="rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-800">
                                Needs target values
                              </span>
                            )}
                            {experimentIndex > 0 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="size-7 p-0 text-red-500 hover:bg-red-50 hover:text-red-700"
                                onClick={() =>
                                  removeManualExperiment(experimentIndex)
                                }
                                disabled={isAddingMeasurement}
                              >
                                <X className="size-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                          {optimization.config.parameters.map(param => (
                            <div
                              key={`${experimentIndex}-${param.name}`}
                              className="space-y-1.5"
                            >
                              <Label
                                htmlFor={`manual-${experimentIndex}-${param.name}`}
                              >
                                {param.name}
                              </Label>
                              {/* Use dropdown for both Categorical and NumericalDiscrete parameters with predefined values */}
                              {param.type === "CategoricalParameter" ||
                              (param.type === "NumericalDiscrete" &&
                                Array.isArray(param.values)) ? (
                                <select
                                  id={`manual-${experimentIndex}-${param.name}`}
                                  value={experimentParams[param.name] || ""}
                                  onChange={e =>
                                    handleManualParameterChange(
                                      experimentIndex,
                                      param.name,
                                      param.type === "NumericalDiscrete"
                                        ? parseFloat(e.target.value)
                                        : e.target.value,
                                      param.type
                                    )
                                  }
                                  className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                  disabled={isAddingMeasurement}
                                >
                                  <option value="">Select a value</option>
                                  {param.values.map(value => (
                                    <option key={value} value={value}>
                                      {value}
                                    </option>
                                  ))}
                                </select>
                              ) : (
                                <Input
                                  id={`manual-${experimentIndex}-${param.name}`}
                                  type={
                                    param.type.startsWith("Numerical")
                                      ? "number"
                                      : "text"
                                  }
                                  step={
                                    param.type.startsWith("Numerical")
                                      ? "0.01"
                                      : undefined
                                  }
                                  min={
                                    param.type === "NumericalContinuous" &&
                                    param.bounds
                                      ? param.bounds[0]
                                      : undefined
                                  }
                                  max={
                                    param.type === "NumericalContinuous" &&
                                    param.bounds
                                      ? param.bounds[1]
                                      : undefined
                                  }
                                  value={experimentParams[param.name] || ""}
                                  onChange={e => {
                                    handleManualParameterChange(
                                      experimentIndex,
                                      param.name,
                                      e.target.value,
                                      param.type
                                    )
                                  }}
                                  placeholder={`Enter ${param.name} value`}
                                  disabled={isAddingMeasurement}
                                />
                              )}

                              {param.type === "NumericalDiscrete" &&
                                Array.isArray(param.values) && (
                                  <p className="text-muted-foreground text-xs">
                                    Allowed values: {param.values.join(", ")}
                                  </p>
                                )}
                              {param.type === "NumericalContinuous" &&
                                param.bounds && (
                                  <p className="text-muted-foreground text-xs">
                                    Range: {param.bounds[0]} to{" "}
                                    {param.bounds[1]}
                                  </p>
                                )}
                            </div>
                          ))}

                          {/* Target value inputs */}
                          {getTargetConfigs().map((target, targetIndex) => (
                            <div
                              key={`${experimentIndex}-${target.name}`}
                              className="space-y-1.5"
                            >
                              <Label
                                htmlFor={`manual-${experimentIndex}-target-${targetIndex}`}
                                className="flex items-center"
                              >
                                {target.name} Value
                                {target.mode === "MAX" ? (
                                  <ArrowUp className="ml-1 size-4 text-green-500" />
                                ) : (
                                  <ArrowDown className="ml-1 size-4 text-green-500" />
                                )}
                              </Label>
                              <div className="space-y-1">
                                <Input
                                  id={`manual-${experimentIndex}-target-${targetIndex}`}
                                  type="number"
                                  step="0.01"
                                  value={
                                    targetValues[targetValueIndex]?.[
                                      target.name
                                    ] || ""
                                  }
                                  onChange={e =>
                                    handleTargetValueChange(
                                      targetValueIndex,
                                      target.name,
                                      e.target.value
                                    )
                                  }
                                  placeholder={`25.00`}
                                  className={
                                    !targetValues[targetValueIndex]?.[
                                      target.name
                                    ]
                                      ? "border-amber-300 focus:ring-amber-500"
                                      : ""
                                  }
                                  disabled={isAddingMeasurement}
                                />
                                <p className="text-muted-foreground text-xs">
                                  Use decimal point (e.g., 25.08)
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                      <CardFooter className="justify-end space-x-2">
                        <Button
                          onClick={handleManualSubmit}
                          disabled={
                            isAddingMeasurement ||
                            !hasAllTargetsForExperiment() ||
                            submittedSuggestions[targetValueIndex]
                          }
                        >
                          {isAddingMeasurement ? (
                            <Loader2 className="mr-2 size-4 animate-spin" />
                          ) : submittedSuggestions[targetValueIndex] ? (
                            <Check className="mr-2 size-4" />
                          ) : (
                            <Check className="mr-2 size-4" />
                          )}
                          {submittedSuggestions[targetValueIndex]
                            ? "Submitted"
                            : "Submit Measurement"}
                        </Button>
                      </CardFooter>
                    </Card>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          <Alert>
            <Beaker className="size-5" />
            <AlertTitle>Manual measurements</AlertTitle>
            <AlertDescription>
              <p className="mb-2">
                Use this form to enter results from experiments you've conducted
                outside the optimization system. These measurements will be
                incorporated into the model to improve future suggestions.
              </p>
              <p className="text-muted-foreground text-sm">
                <strong>Batch Submission:</strong> You can add multiple
                experiments and submit them as a batch. When submitted together,
                they will be classified as a batch in the History tab.
              </p>
              <p className="text-muted-foreground mt-2 text-sm">
                <strong>Target Values:</strong> Enter numerical values using a
                decimal point (e.g., 25.08). The system does not accept comma
                separators (25,08) or other formats. Values are displayed with
                two decimal places by default.
              </p>
            </AlertDescription>
          </Alert>
        </TabsContent>
      </Tabs>
    </div>
  )
}
