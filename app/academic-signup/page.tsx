/*
This server page provides the academic signup form for users to apply for extended academic access.
*/

"use server"

import {
  <PERSON>,
  Card<PERSON><PERSON>nt,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import AcademicSignupForm from "./_components/academic-signup-form"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"
import { checkAcademicVerificationStatusAction } from "@/actions/academic-verification-actions"
import { AcademicSignupWrapper } from "./_components/academic-signup-wrapper"
import Link from "next/link"
import { Loader2 } from "lucide-react"

export default async function AcademicSignupPage() {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  // Always use client-side wrapper for auth detection to avoid server/client mismatch
  // The wrapper will handle authentication properly on the client side
  if (!userId) {
    // Return the full form content wrapped - client-side will handle auth
    return (
      <AcademicSignupWrapper>
        <div className="container mx-auto max-w-5xl px-4 py-12">
          <div className="mx-auto mb-12 max-w-2xl text-center">
            <h1 className="mb-4 text-4xl font-bold">
              Academic Access Application
            </h1>
            <p className="text-muted-foreground">
              Eligible academic institutions can qualify for extended 90-day
              access. Complete the form below to apply.
            </p>
          </div>

          <Card className="mx-auto max-w-2xl">
            <CardHeader>
              <CardTitle>Academic Verification</CardTitle>
              <CardDescription>
                Please provide your academic information for verification. We'll
                verify your institutional email to confirm your academic status.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AcademicSignupForm
                existingData={null}
                verificationStatus={null}
              />
            </CardContent>
          </Card>
        </div>
      </AcademicSignupWrapper>
    )
  }

  // Check if the user has already submitted a verification request
  const verificationStatus = await checkAcademicVerificationStatusAction()

  // If the user has already been verified, redirect to the academic survey
  if (
    verificationStatus.isSuccess &&
    verificationStatus.status === "approved"
  ) {
    redirect("/academic-survey")
  }

  return (
    <AcademicSignupWrapper>
      <div className="container mx-auto max-w-5xl px-4 py-12">
        <div className="mx-auto mb-12 max-w-2xl text-center">
          <h1 className="mb-4 text-4xl font-bold">
            Academic Access Application
          </h1>
          <p className="text-muted-foreground">
            Eligible academic institutions can qualify for extended 90-day
            access. Complete the form below to apply.
          </p>
        </div>

        <Card className="mx-auto max-w-2xl">
          <CardHeader>
            <CardTitle>Academic Verification</CardTitle>
            <CardDescription>
              Please provide your academic information for verification. We'll
              verify your institutional email to confirm your academic status.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AcademicSignupForm
              existingData={verificationStatus.data}
              verificationStatus={verificationStatus.status}
            />
          </CardContent>
        </Card>
      </div>
    </AcademicSignupWrapper>
  )
}
