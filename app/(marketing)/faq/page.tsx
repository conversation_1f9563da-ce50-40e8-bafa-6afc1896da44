/*
This server page displays the FAQ for INNOptimizer™ Bayesian optimization.
*/

"use client"

import { useState, useMemo, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  Search,
  Filter,
  X,
  BookOpen,
  Play,
  MessageCircle
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface FAQItem {
  question: string
  answer: string
  category?: string
}

const initialFAQs: FAQItem[] = [
  {
    question:
      "What is Bayesian optimization and how does it help with experiments?",
    answer:
      "Bayesian optimization is a machine learning-based approach that intelligently suggests parameter combinations to efficiently find optimal experimental conditions. It reduces the number of experiments needed by learning from previous results and focusing on promising regions of the parameter space.",
    category: "Basic Concepts"
  },
  {
    question:
      "How does INNOptimizer™ differ from traditional Design of Experiments (DoE) approaches?",
    answer:
      "Unlike traditional DoE which uses fixed experimental designs, INNOptimizer™ adapts in real-time based on your results. It uses machine learning to build a surrogate model of your experimental space and suggests the most informative experiments to run next.",
    category: "Basic Concepts"
  },
  {
    question: "What types of problems can I solve with INNOptimizer™?",
    answer:
      "INNOptimizer™ is ideal for optimizing complex processes with multiple parameters, including chemical reactions, material formulations, process optimization, hyperparameter tuning for ML models, and any experimental workflow where running experiments is time-consuming or expensive.",
    category: "Basic Concepts"
  },
  {
    question:
      "What are the key benefits of using Bayesian optimization for my experiments?",
    answer:
      "Key benefits include: reduced number of experiments needed, faster convergence to optimal conditions, ability to handle complex parameter spaces, automatic balancing of exploration vs. exploitation, and insightful visualizations of parameter relationships.",
    category: "Basic Concepts"
  },
  {
    question: "What do I need to get started with INNOptimizer™?",
    answer:
      "You need access to the INNOptimizer™ platform (via web browser), experimental parameters you want to optimize, a target metric to maximize or minimize, and the ability to conduct experiments and record results.",
    category: "Getting Started"
  }
]

const additionalFAQs: FAQItem[] = [
  {
    question: "How do I create my first optimization experiment?",
    answer:
      'Navigate to the Dashboard → Optimizations → "New Optimization" and follow the wizard steps: (1) Basic Info, (2) Target Configuration, (3) Parameter Definition, (4) Advanced Settings (optional), and (5) Review & Create.',
    category: "Getting Started"
  },
  {
    question: "What types of parameters can I optimize?",
    answer:
      "INNOptimizer™ supports three main parameter types: Numerical Continuous (ranges like temperature: 50-100°C), Numerical Discrete (specific values like pH: 4, 5, 6, 7, 8), and Categorical (options like catalyst type: A, B, C, D).",
    category: "Getting Started"
  },
  {
    question: "How many parameters can I optimize at once?",
    answer:
      "While technically you can optimize many parameters simultaneously, performance is best with 2-10 parameters. With more parameters, you'll need more initial experiments to build an accurate model.",
    category: "Getting Started"
  },
  {
    question: "How does the optimization workflow typically proceed?",
    answer:
      "The typical workflow is: 1) Define parameters and targets, 2) Get initial suggestions (exploratory phase), 3) Conduct experiments and record results, 4) Get new suggestions based on previous results, 5) Repeat steps 3-4 until convergence, 6) Analyze results and parameter relationships.",
    category: "Optimization Process"
  },
  {
    question:
      "What is the difference between exploratory and sequential suggestions?",
    answer:
      "Exploratory suggestions (initial phase) focus on exploring the parameter space broadly to build a basic understanding. Sequential suggestions use the model to balance exploration of uncertain regions with exploitation of promising areas.",
    category: "Optimization Process"
  },
  {
    question: "When should I stop the optimization process?",
    answer:
      "Consider stopping when: (1) you observe convergence (no significant improvement in recent iterations), (2) you've reached a satisfactory target value, or (3) the uncertainty in the model predictions is sufficiently low in the region of interest.",
    category: "Optimization Process"
  },
  {
    question:
      "What acquisition functions are available and how do I choose between them?",
    answer:
      "INNOptimizer™ offers several acquisition functions: Expected Improvement (EI) - Balanced approach, good for most cases; Upper Confidence Bound (UCB) - More exploratory, good when you want to explore the parameter space more thoroughly; Probability of Improvement (PI) - More exploitative, good when you want to refine around promising areas.",
    category: "Advanced Features"
  },
  {
    question:
      "What is Sobol sensitivity analysis and how does it support Quality by Design (QbD)?",
    answer:
      "Sobol sensitivity analysis quantifies how much each parameter contributes to the variation in your target metric through three key indices: First-order (S1) for direct effects, Second-order (S2) for parameter interactions, and Total-order (ST) for combined effects.",
    category: "Advanced Features"
  },
  {
    question:
      "How do I interpret Sobol sensitivity indices to make practical QbD decisions?",
    answer:
      "Use these thresholds for QbD decision-making: High Sensitivity (ST > 0.3) - Designate as Critical Process Parameters, implement tight control limits, and consider real-time monitoring; Medium Sensitivity (0.1 < ST < 0.3) - Treat as Key Process Parameters with appropriate control ranges and validation monitoring; Low Sensitivity (ST < 0.1) - Consider non-critical with wider acceptable ranges and less frequent monitoring; Significant Interactions (S2 > 0.05) - Parameters requiring coordinated control strategies and special attention in Design Space boundaries.",
    category: "Advanced Features"
  },
  {
    question: "Can I optimize multiple targets simultaneously?",
    answer:
      "Yes, INNOptimizer™ supports multi-objective optimization. You can define multiple targets with different optimization directions (maximize/minimize) and assign weights to indicate their relative importance.",
    category: "Advanced Features"
  },

  {
    question:
      "What visualizations are available to understand my optimization results?",
    answer:
      "INNOptimizer™ provides several visualizations: 3D surface plots showing the predicted response surface, parameter impact analysis showing which parameters matter most, convergence plots tracking optimization progress, correlation matrices showing parameter relationships, and parameter distribution plots showing explored parameter space.",
    category: "Data and Visualization"
  },
  {
    question: "How can I interpret the 3D surface plots?",
    answer:
      "The 3D surface plots show the predicted relationship between two parameters and the target metric. Peaks (for maximization) or valleys (for minimization) indicate optimal regions. The color gradient provides an additional visual cue for target values.",
    category: "Data and Visualization"
  },
  {
    question: "What does the parameter impact analysis tell me?",
    answer:
      "Parameter impact analysis (based on SHAP values) shows which parameters have the strongest influence on your target metric. Parameters with larger impact values contribute more to changes in the target, helping you identify the most critical factors.",
    category: "Data and Visualization"
  },
  {
    question: "Can I incorporate prior knowledge into my optimization?",
    answer:
      "Yes, you can incorporate prior knowledge by: carefully defining parameter ranges based on domain expertise, adding constraints that reflect known relationships, including previous experimental data as initial measurements, and using the parameter impact analysis to validate your understanding.",
    category: "Data and Visualization"
  },
  {
    question: "What machine learning models are used behind the scenes?",
    answer:
      "INNOptimizer™ uses Gaussian Process (GP) regression models as surrogate models of your experimental space. GPs are particularly well-suited for Bayesian optimization because they provide both predictions and uncertainty estimates.",
    category: "Technical Details"
  },
  {
    question: "How does INNOptimizer™ handle categorical parameters?",
    answer:
      "Categorical parameters are handled through encoding methods: One-Hot Encoding (OHE) creates separate binary features for each category, and Label Encoding (LE) maps categories to numerical values.",
    category: "Technical Details"
  },
  {
    question: "How are parameter suggestions generated?",
    answer:
      "Parameter suggestions are generated through a multi-step process: 1) A Gaussian Process model is trained on existing data, 2) An acquisition function evaluates the utility of potential parameter combinations, 3) An optimization algorithm finds parameter values that maximize this utility, 4) These optimal points become the next suggestions.",
    category: "Technical Details"
  },
  {
    question: "What happens if I have missing or failed experiments?",
    answer:
      "INNOptimizer™ is designed to handle missing data. You can skip recording results for failed experiments, and the system will continue to generate suggestions based on available data. However, if possible, recording even failed experiments (with appropriate notes) can provide valuable information.",
    category: "Technical Details"
  },
  {
    question: "Can I import existing experimental data?",
    answer:
      "Yes, you can import previous experimental data to jumpstart your optimization. This allows the system to learn from your historical experiments rather than starting from scratch.",
    category: "Integration and Workflow"
  },
  {
    question: "Is there an API available for programmatic access?",
    answer:
      "Yes, SynSilico can provide a RESTful API that allows programmatic access to all functionality, including creating optimizations, getting suggestions, and recording measurements. Contact us for more information.",
    category: "Integration and Workflow"
  },
  {
    question: "How secure is my experimental data?",
    answer:
      "INNOptimizer™ implements several security measures: authentication and authorization controls, encrypted data transmission, secure data storage, regular security updates, and optional on-premises deployment for sensitive data.",
    category: "Integration and Workflow"
  },
  {
    question:
      "How can INNOptimizer™ be used for chemical reaction optimization?",
    answer:
      "For chemical reactions, you can optimize parameters like temperature, pressure, catalyst loading, reaction time, and reagent ratios. The system can help identify conditions that maximize yield, selectivity, or other quality metrics while minimizing side products or energy consumption.",
    category: "Domain-Specific Applications"
  },
  {
    question: "Can INNOptimizer™ handle formulation optimization problems?",
    answer:
      "Yes, INNOptimizer™ is well-suited for formulation optimization. You can define components as parameters (with constraints ensuring they sum to 100%) and optimize for properties like stability, efficacy, or cost.",
    category: "Domain-Specific Applications"
  },
  {
    question:
      "Can I use INNOptimizer™ for process optimization in manufacturing?",
    answer:
      "Yes, INNOptimizer™ can optimize manufacturing processes by finding optimal settings for equipment parameters, process conditions, and material inputs to maximize quality, throughput, or efficiency while minimizing defects or costs.",
    category: "Domain-Specific Applications"
  },
  {
    question: "How might materials scientists use INNOptimizer™?",
    answer:
      "Materials scientists can optimize composition, processing conditions, and treatment parameters to develop materials with desired properties like strength, conductivity, or thermal stability.",
    category: "Domain-Specific Applications"
  },
  {
    question: "How can I get help if I encounter issues?",
    answer:
      "Support options include: in-app help documentation and tooltips, user manual and guides, email support, community forum for user discussions, and training webinars and workshops.",
    category: "Resources and Support"
  },
  {
    question:
      "How many initial experiments are required to start the Bayesian optimization?",
    answer:
      "The minimum number depends on your parameter space complexity, but generally you need at least n+1 experiments (where n is the number of parameters). However, for reliable model building, 5-10 experiments per parameter is recommended.",
    category: "Initial Sampling and Experimental Design"
  },
  {
    question:
      "What is Latin Hypercube Sampling (LHS) and how does it work for my initial experiments?",
    answer:
      "LHS is a statistical method for generating near-random samples with better coverage of the parameter space than purely random sampling. It divides each parameter range into equal intervals and places exactly one sample in each interval, ensuring more uniform coverage.",
    category: "Initial Sampling and Experimental Design"
  },
  {
    question:
      "How many experiments do I need to run until I reach my optimization goals?",
    answer:
      "This varies based on problem complexity, but typically 10-15 iterations (beyond initial sampling) show significant improvement. Complex problems with many parameters may require 20-30 iterations.",
    category: "Initial Sampling and Experimental Design"
  },
  {
    question:
      "Can I customize the initial sampling strategy, and when should I choose alternatives to LHS?",
    answer:
      "Yes, besides LHS, you can choose random sampling or import your own design. Consider random sampling for very high-dimensional spaces (10+ parameters), or your own design when you have prior knowledge about promising regions.",
    category: "Initial Sampling and Experimental Design"
  },
  {
    question:
      "What's the difference between exploration and exploitation in Bayesian optimization, and how does it affect my experiments?",
    answer:
      "Exploration focuses on sampling uncertain regions to improve the model's understanding of the entire parameter space, while exploitation focuses on sampling promising regions to refine the optimum. The acquisition function balances these competing objectives.",
    category: "Initial Sampling and Experimental Design"
  },
  {
    question:
      "How does the Gaussian Process model handle different parameter scales in my experiments?",
    answer:
      "The system automatically normalizes all parameters to a standard scale before model training. This prevents parameters with larger numerical ranges from dominating those with smaller ranges.",
    category: "Technical Implementation Questions"
  },

  {
    question:
      "How does the system handle measurement noise in my experimental data?",
    answer:
      "The Gaussian Process model includes a noise parameter that's automatically estimated from your data. This allows the model to distinguish between actual trends and random experimental variation.",
    category: "Technical Implementation Questions"
  },
  {
    question:
      "What's the mathematical difference between the available acquisition functions, and how should I choose?",
    answer:
      "Expected Improvement (EI) calculates the expected amount by which a new point would improve upon the current best observation. Upper Confidence Bound (UCB) combines the prediction and uncertainty weighted by a beta parameter (higher beta = more exploration). Probability of Improvement (PI) calculates the probability that a new point will improve upon the current best.",
    category: "Technical Implementation Questions"
  },
  {
    question:
      "How does batch suggestion generation work when I request multiple experiment suggestions at once?",
    answer:
      'When generating batches, the system uses a technique called "fantasizing" where it selects the best point according to the acquisition function, simulates the outcome of that experiment using the current model, temporarily updates the model with this "fantasy" result, and repeats until the batch is filled.',
    category: "Technical Implementation Questions"
  },
  {
    question:
      "How does the multi-objective optimization actually work under the hood?",
    answer:
      "Multi-objective optimization uses a scalarization approach that combines multiple objectives into a single function. INNOptimizer™ is configured with Geometric Mean scalarizer.",
    category: "Advanced Technical Questions"
  },
  {
    question:
      'What is the "beta" parameter in UCB acquisition function, and how should I tune it for my experiments?',
    answer:
      "Beta controls the exploration-exploitation trade-off in Upper Confidence Bound. Higher beta values increase exploration by placing more emphasis on uncertainty.",
    category: "Advanced Technical Questions"
  },
  {
    question:
      "How does INNOptimizer™ handle categorical parameters in the Gaussian Process model?",
    answer:
      "Categorical parameters are handled through specialized kernels after encoding, such as One-Hot Encoding with a Hamming distance kernel or Label Encoding with an ordinal kernel.",
    category: "Advanced Technical Questions"
  },

  {
    question:
      "How does the system quantify uncertainty in its predictions, and how can I use this information?",
    answer:
      "The Gaussian Process model provides a posterior distribution (mean and variance) for each prediction. The variance represents prediction uncertainty, which can be used to identify where additional experiments would be most informative.",
    category: "Advanced Technical Questions"
  }
]

function FAQCard({
  faq,
  isOpen,
  onToggle
}: {
  faq: FAQItem
  isOpen: boolean
  onToggle: () => void
}) {
  return (
    <Card className="hover:border-l-primary mb-4 border-l-4 border-l-transparent transition-all duration-200 hover:shadow-md">
      <CardContent className="p-0">
        <button
          onClick={onToggle}
          className="focus:ring-primary flex w-full items-center justify-between rounded-lg p-6 text-left transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2"
          aria-expanded={isOpen}
          aria-controls={`faq-answer-${faq.question.slice(0, 20)}`}
        >
          <div className="flex-1 pr-4">
            <h3 className="font-medium leading-relaxed text-gray-900">
              {faq.question}
            </h3>
            {faq.category && (
              <Badge variant="secondary" className="mt-2 text-xs">
                {faq.category}
              </Badge>
            )}
          </div>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
            className="shrink-0"
          >
            <ChevronDown className="size-5 text-gray-500" />
          </motion.div>
        </button>
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
              id={`faq-answer-${faq.question.slice(0, 20)}`}
            >
              <div className="border-t border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50 px-6 pb-6 leading-relaxed text-gray-600">
                {faq.answer}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  )
}

// Get unique categories
const categories = Array.from(
  new Set(
    [...initialFAQs, ...additionalFAQs].map(faq => faq.category).filter(Boolean)
  )
) as string[]

// Popular questions (most commonly asked)
const popularQuestions = [
  "What is Bayesian optimization and how does it help with experiments?",
  "How do I create my first optimization experiment?",
  "What types of parameters can I optimize?",
  "How many parameters can I optimize at once?"
]

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<Set<number>>(new Set())
  const [showAdditional, setShowAdditional] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [showFilters, setShowFilters] = useState(false)

  const toggleItem = (index: number) => {
    const newOpenItems = new Set(openItems)
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index)
    } else {
      newOpenItems.add(index)
    }
    setOpenItems(newOpenItems)
  }

  const toggleAdditional = () => {
    setShowAdditional(!showAdditional)
  }

  // Filter FAQs based on search and category
  const allFAQs = [...initialFAQs, ...additionalFAQs]
  const filteredFAQs = useMemo(() => {
    let filtered = allFAQs

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        faq =>
          faq.question.toLowerCase().includes(query) ||
          faq.answer.toLowerCase().includes(query) ||
          faq.category?.toLowerCase().includes(query)
      )
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(faq => faq.category === selectedCategory)
    }

    return filtered
  }, [searchQuery, selectedCategory])

  // Determine which FAQs to show
  const faqsToShow =
    searchQuery.trim() || selectedCategory
      ? filteredFAQs
      : showAdditional
        ? allFAQs
        : initialFAQs

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedCategory(null)
  }

  const hasActiveFilters = searchQuery.trim() || selectedCategory

  // Expand/Collapse all functionality
  const expandAll = () => {
    const allIndices = new Set(
      faqsToShow.map((_, index) => {
        if (hasActiveFilters) {
          return allFAQs.findIndex(f => f === faqsToShow[index])
        }
        return showAdditional
          ? index
          : index < initialFAQs.length
            ? index
            : index + initialFAQs.length
      })
    )
    setOpenItems(allIndices)
  }

  const collapseAll = () => {
    setOpenItems(new Set())
  }

  const allExpanded =
    faqsToShow.length > 0 &&
    faqsToShow.every((faq, index) => {
      const originalIndex = hasActiveFilters
        ? allFAQs.findIndex(f => f === faq)
        : showAdditional
          ? index
          : index < initialFAQs.length
            ? index
            : index + initialFAQs.length
      return openItems.has(originalIndex)
    })

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + K to focus search
      if ((event.ctrlKey || event.metaKey) && event.key === "k") {
        event.preventDefault()
        const searchInput = document.querySelector(
          'input[placeholder="Search FAQs..."]'
        ) as HTMLInputElement
        searchInput?.focus()
      }
      // Escape to clear search and filters
      if (event.key === "Escape") {
        if (searchQuery || selectedCategory) {
          clearFilters()
        }
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [searchQuery, selectedCategory])

  return (
    <div className="container mx-auto max-w-4xl py-12">
      <div className="mb-12 text-center">
        <div className="mb-4 flex items-center justify-center">
          <HelpCircle className="text-primary mr-3 size-8" />
          <h1 className="text-4xl font-bold">Frequently Asked Questions</h1>
        </div>

        {/* Search and Filter Section */}
        <div className="mx-auto max-w-2xl space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="py-3 pl-10 pr-20 text-base"
            />
            <div className="absolute right-3 top-1/2 hidden -translate-y-1/2 text-xs text-gray-400 sm:block">
              <kbd className="rounded bg-gray-100 px-2 py-1 text-xs">⌘K</kbd>
            </div>
          </div>

          {/* Filter Toggle and Categories */}
          <div className="flex flex-wrap items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="size-4" />
              Categories
              <ChevronDown
                className={`size-4 transition-transform ${showFilters ? "rotate-180" : ""}`}
              />
            </Button>

            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="flex items-center gap-2 text-red-600 hover:text-red-700"
              >
                <X className="size-4" />
                Clear Filters
              </Button>
            )}
          </div>

          {/* Category Filters */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="flex flex-wrap justify-center gap-2 rounded-lg bg-gray-50 p-4">
                  {categories.map(category => (
                    <Badge
                      key={category}
                      variant={
                        selectedCategory === category ? "default" : "secondary"
                      }
                      className="hover:bg-primary hover:text-primary-foreground cursor-pointer transition-colors"
                      onClick={() =>
                        setSelectedCategory(
                          selectedCategory === category ? null : category
                        )
                      }
                    >
                      {category}
                    </Badge>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Results Summary and Actions */}
          {hasActiveFilters && (
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div>
                {filteredFAQs.length === 0 ? (
                  <p>No FAQs found matching your criteria.</p>
                ) : (
                  <p>
                    Showing {filteredFAQs.length} of {allFAQs.length} FAQs
                  </p>
                )}
              </div>
              {filteredFAQs.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={allExpanded ? collapseAll : expandAll}
                  className="text-xs"
                >
                  {allExpanded ? "Collapse All" : "Expand All"}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions Section */}
      {!hasActiveFilters && (
        <div className="mb-12">
          <h2 className="mb-6 text-center text-xl font-semibold text-gray-800">
            Quick Start
          </h2>
          <div className="mx-auto grid max-w-4xl gap-6 md:grid-cols-3">
            <Card className="group h-full cursor-pointer transition-shadow hover:shadow-lg">
              <CardContent className="flex h-full flex-col p-6 text-center">
                <div className="mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-blue-100 transition-colors group-hover:bg-blue-200">
                  <Play className="size-6 text-blue-600" />
                </div>
                <h3 className="mb-3 text-lg font-semibold">Getting Started</h3>
                <p className="mb-6 grow text-sm text-gray-600">
                  New to Bayesian optimization? Start here.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => {
                    setSelectedCategory("Getting Started")
                    setShowFilters(false)
                  }}
                >
                  View Questions
                </Button>
              </CardContent>
            </Card>

            <Card className="group h-full cursor-pointer transition-shadow hover:shadow-lg">
              <CardContent className="flex h-full flex-col p-6 text-center">
                <div className="mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-green-100 transition-colors group-hover:bg-green-200">
                  <BookOpen className="size-6 text-green-600" />
                </div>
                <h3 className="mb-3 text-lg font-semibold">
                  Technical Details
                </h3>
                <p className="mb-6 grow text-sm text-gray-600">
                  Deep dive into how it works.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => {
                    setSelectedCategory("Technical Details")
                    setShowFilters(false)
                  }}
                >
                  View Questions
                </Button>
              </CardContent>
            </Card>

            <Card className="group h-full cursor-pointer transition-shadow hover:shadow-lg">
              <CardContent className="flex h-full flex-col p-6 text-center">
                <div className="mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-purple-100 transition-colors group-hover:bg-purple-200">
                  <MessageCircle className="size-6 text-purple-600" />
                </div>
                <h3 className="mb-3 text-lg font-semibold">Need Help?</h3>
                <p className="mb-6 grow text-sm text-gray-600">
                  Can't find your answer? Contact us.
                </p>
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <a href="/contact">Get Support</a>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* FAQ Results */}
      <div className="mb-8">
        {faqsToShow.length === 0 ? (
          <div className="py-12 text-center">
            <HelpCircle className="mx-auto mb-4 size-16 text-gray-300" />
            <h3 className="mb-2 text-xl font-semibold text-gray-600">
              No FAQs found
            </h3>
            <p className="mb-4 text-gray-500">
              Try adjusting your search terms or filters.
            </p>
            <Button onClick={clearFilters} variant="outline">
              Clear all filters
            </Button>
          </div>
        ) : (
          <>
            {/* Category Headers for organized display */}
            {!hasActiveFilters && (
              <>
                {/* Initial FAQs */}
                <div className="mb-8">
                  <h2 className="mb-6 text-center text-2xl font-semibold text-gray-800">
                    Most Common Questions
                  </h2>
                  {initialFAQs.map((faq, index) => (
                    <FAQCard
                      key={index}
                      faq={faq}
                      isOpen={openItems.has(index)}
                      onToggle={() => toggleItem(index)}
                    />
                  ))}
                </div>

                {/* Toggle button for additional FAQs */}
                <div className="mb-8 text-center">
                  <Button
                    onClick={toggleAdditional}
                    variant="outline"
                    size="lg"
                    className="flex items-center gap-2"
                  >
                    {showAdditional ? (
                      <>
                        <ChevronUp className="size-4" />
                        Show Less Questions
                      </>
                    ) : (
                      <>
                        <ChevronDown className="size-4" />
                        Show More Questions ({additionalFAQs.length} more)
                      </>
                    )}
                  </Button>
                </div>

                {/* Additional FAQs */}
                <AnimatePresence>
                  {showAdditional && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.5 }}
                      className="overflow-hidden"
                    >
                      <h2 className="mb-6 text-center text-2xl font-semibold text-gray-800">
                        All Questions
                      </h2>
                      {additionalFAQs.map((faq, index) => (
                        <FAQCard
                          key={index + initialFAQs.length}
                          faq={faq}
                          isOpen={openItems.has(index + initialFAQs.length)}
                          onToggle={() =>
                            toggleItem(index + initialFAQs.length)
                          }
                        />
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </>
            )}

            {/* Filtered Results */}
            {hasActiveFilters && (
              <div>
                {selectedCategory && (
                  <h2 className="mb-6 text-center text-2xl font-semibold text-gray-800">
                    {selectedCategory}
                  </h2>
                )}
                {faqsToShow.map((faq, index) => {
                  const originalIndex = allFAQs.findIndex(f => f === faq)
                  return (
                    <FAQCard
                      key={originalIndex}
                      faq={faq}
                      isOpen={openItems.has(originalIndex)}
                      onToggle={() => toggleItem(originalIndex)}
                    />
                  )
                })}
              </div>
            )}
          </>
        )}
      </div>

      {/* Contact section */}
      <div className="mt-12 text-center">
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardContent className="p-8">
            <h2 className="mb-4 text-2xl font-bold text-gray-900">
              Still have questions?
            </h2>
            <p className="mb-6 text-gray-600">
              Can't find what you're looking for? Our team is here to help you
              get the most out of INNOptimizer™.
            </p>
            <Button
              asChild
              className="from-primary hover:from-primary/90 bg-gradient-to-r to-blue-600 hover:to-blue-700"
            >
              <a href="/contact">Contact Support</a>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Floating Expand/Collapse Button */}
      {!hasActiveFilters && faqsToShow.length > 3 && (
        <div className="fixed bottom-8 right-8 z-50">
          <Button
            onClick={allExpanded ? collapseAll : expandAll}
            className="bg-primary hover:bg-primary/90 rounded-full shadow-lg transition-all duration-200 hover:shadow-xl"
            size="lg"
          >
            {allExpanded ? (
              <>
                <ChevronUp className="mr-2 size-4" />
                Collapse All
              </>
            ) : (
              <>
                <ChevronDown className="mr-2 size-4" />
                Expand All
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
