#!/usr/bin/env node

/**
 * Simple test script to investigate target filtering behavior
 * Tests if target filtering has similar NaN/infinite detection issues as parameter filtering
 */

require('dotenv').config({ path: '.env.local' });

async function testTargetFiltering() {
  console.log('🎯 TESTING TARGET FILTERING FOR NaN/INFINITE ISSUES');
  console.log('===================================================');
  
  // Use the known optimization ID from previous tests
  const optimizerId = "user_30EFn0yPauqlMMorn5hfsn3of5X_Test_CRUD_Filtering2_1757675394674";
  
  try {
    console.log(`✅ Testing optimization: ${optimizerId}`);
    
    // Test 1: Test target bounds filtering with preview
    console.log('\n🧪 STEP 1: TESTING TARGET BOUNDS FILTERING');
    console.log('==========================================');
    
    // Test changing Target 1 bounds from [0, 100] to [40, 80]
    // This should filter out measurements where Target 1 < 40 or Target 1 > 80
    const testTargetBounds = {
      "Target 1": [40, 80]
    };
    
    console.log(`📤 Testing Target 1 bounds change to [40, 80]...`);
    
    const targetFilterResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target_bounds: testTargetBounds,
        preview_only: true
      })
    });
    
    if (!targetFilterResponse.ok) {
      const errorText = await targetFilterResponse.text();
      console.error(`❌ Target filtering test failed: ${targetFilterResponse.statusText} - ${errorText}`);
      return;
    }
    
    const targetFilterResult = await targetFilterResponse.json();
    console.log(`📥 Backend response:`, JSON.stringify(targetFilterResult, null, 2));
    
    // Test 2: Analyze the filtering results for NaN/infinite issues
    console.log('\n📊 STEP 2: ANALYZING TARGET FILTERING RESULTS');
    console.log('==============================================');
    
    if (targetFilterResult.dropped_measurements) {
      console.log(`\n🔍 DROPPED MEASUREMENTS ANALYSIS:`);
      console.log(`Total dropped: ${targetFilterResult.dropped_measurements.length}`);
      
      // Group by exclusion reason
      const reasonGroups = {};
      targetFilterResult.dropped_measurements.forEach(measurement => {
        const reason = measurement.exclusion_reason || 'Unknown';
        if (!reasonGroups[reason]) {
          reasonGroups[reason] = [];
        }
        reasonGroups[reason].push(measurement);
      });
      
      console.log(`\n📋 EXCLUSION REASONS BREAKDOWN:`);
      Object.entries(reasonGroups).forEach(([reason, measurements]) => {
        console.log(`\n  Reason: "${reason}"`);
        console.log(`  Count: ${measurements.length}`);
        
        // Check if this reason involves NaN/infinite detection
        if (reason.toLowerCase().includes('nan') || reason.toLowerCase().includes('infinite')) {
          console.log(`    🚨 POTENTIAL BUG: NaN/infinite detection in target filtering!`);
          
          // Analyze the flagged values
          measurements.slice(0, 5).forEach((measurement, index) => {
            const target1 = measurement['Target 1'];
            const target2 = measurement['Target 2'];
            
            console.log(`      ${index + 1}. Target 1: ${target1}, Target 2: ${target2}`);
            
            const target1Valid = Number.isFinite(target1) && !Number.isNaN(target1);
            const target2Valid = Number.isFinite(target2) && !Number.isNaN(target2);
            
            if (target1Valid && target2Valid) {
              console.log(`         ❌ FALSE POSITIVE: Valid targets flagged as NaN/infinite`);
              console.log(`         Target 1: ${target1} (${typeof target1}) - isFinite: ${Number.isFinite(target1)}, isNaN: ${Number.isNaN(target1)}`);
              console.log(`         Target 2: ${target2} (${typeof target2}) - isFinite: ${Number.isFinite(target2)}, isNaN: ${Number.isNaN(target2)}`);
            } else {
              console.log(`         ✅ CORRECTLY FLAGGED: Actually has NaN/infinite values`);
            }
          });
        } else if (reason.includes('outside target bounds')) {
          console.log(`    ✅ CORRECT: Target bounds violation`);
          
          // Show a few examples
          measurements.slice(0, 3).forEach((measurement, index) => {
            const target1 = measurement['Target 1'];
            console.log(`      ${index + 1}. Target 1: ${target1} (should be in [40, 80])`);
          });
        } else {
          console.log(`    ❓ OTHER REASON: ${reason}`);
        }
      });
    }
    
    // Test 3: Test with different target bounds to see consistency
    console.log('\n🧪 STEP 3: TESTING DIFFERENT TARGET BOUNDS');
    console.log('==========================================');
    
    // Test changing Target 2 bounds from [0, 100] to [50, 90]
    const testTargetBounds2 = {
      "Target 2": [50, 90]
    };
    
    console.log(`📤 Testing Target 2 bounds change to [50, 90]...`);
    
    const targetFilterResponse2 = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target_bounds: testTargetBounds2,
        preview_only: true
      })
    });
    
    if (targetFilterResponse2.ok) {
      const targetFilterResult2 = await targetFilterResponse2.json();
      console.log(`📥 Target 2 filtering results:`);
      console.log(`  - Valid measurements: ${targetFilterResult2.valid_count || 0}`);
      console.log(`  - Dropped measurements: ${targetFilterResult2.dropped_count || 0}`);
      
      // Check for NaN/infinite issues in Target 2 filtering
      if (targetFilterResult2.dropped_measurements) {
        const nanInfiniteCount = targetFilterResult2.dropped_measurements.filter(m => 
          m.exclusion_reason && (
            m.exclusion_reason.toLowerCase().includes('nan') || 
            m.exclusion_reason.toLowerCase().includes('infinite')
          )
        ).length;
        
        if (nanInfiniteCount > 0) {
          console.log(`  🚨 FOUND ${nanInfiniteCount} measurements flagged as NaN/infinite in Target 2 filtering!`);
        } else {
          console.log(`  ✅ No NaN/infinite false positives in Target 2 filtering`);
        }
      }
    } else {
      console.log(`❌ Target 2 filtering test failed: ${targetFilterResponse2.statusText}`);
    }
    
    // Test 4: Test both targets together
    console.log('\n🧪 STEP 4: TESTING BOTH TARGETS TOGETHER');
    console.log('========================================');
    
    const testBothTargets = {
      "Target 1": [45, 75],
      "Target 2": [40, 85]
    };
    
    console.log(`📤 Testing both targets: Target 1 [45, 75], Target 2 [40, 85]...`);
    
    const bothTargetsResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target_bounds: testBothTargets,
        preview_only: true
      })
    });
    
    if (bothTargetsResponse.ok) {
      const bothTargetsResult = await bothTargetsResponse.json();
      console.log(`📥 Both targets filtering results:`);
      console.log(`  - Valid measurements: ${bothTargetsResult.valid_count || 0}`);
      console.log(`  - Dropped measurements: ${bothTargetsResult.dropped_count || 0}`);
      
      // Final analysis
      if (bothTargetsResult.dropped_measurements) {
        const reasonCounts = {};
        bothTargetsResult.dropped_measurements.forEach(m => {
          const reason = m.exclusion_reason || 'Unknown';
          reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
        });
        
        console.log(`\n📊 FINAL REASON BREAKDOWN:`);
        Object.entries(reasonCounts).forEach(([reason, count]) => {
          console.log(`  - "${reason}": ${count} measurements`);
          
          if (reason.toLowerCase().includes('nan') || reason.toLowerCase().includes('infinite')) {
            console.log(`    🚨 CRITICAL: NaN/infinite detection issue in target filtering!`);
          }
        });
      }
    } else {
      console.log(`❌ Both targets filtering test failed: ${bothTargetsResponse.statusText}`);
    }
    
    console.log('\n🎯 TARGET FILTERING ANALYSIS COMPLETE');
    console.log('====================================');
    
  } catch (error) {
    console.error('❌ Error during target filtering test:', error);
  }
}

// Run the test
testTargetFiltering().catch(console.error);
