# Test Plan for Acquisition Function Configuration

## Changes Made

1. **Removed Acquisition Function from Step 3**: The dedicated "Acquisition" step has been removed from the wizard
2. **Updated Step Numbers**:
   - Step 1: Basic Info (unchanged)
   - Step 2: Target (unchanged)
   - Step 3: Parameters (was step 4)
   - Step 4: Advanced (was step 5, now includes acquisition function)
   - Step 5: Review (was step 6)

3. **Added Acquisition Function to Advanced Section**:
   - Added Enable button similar to Constraints
   - Moved all acquisition function controls to advanced section
   - Includes function type selection and beta parameter for UCB

4. **Enhanced User Guidance**:
   - Added "Ready to Optimize" message explaining defaults are sufficient
   - Added "Expert Mode Available" section for advanced users
   - Improved descriptions for each advanced option with "Enable if" guidance
   - Clear messaging that optimization can run without enabling advanced options

## Test Cases

### Test 1: Basic Form Navigation
- [ ] Navigate through all steps without errors
- [ ] Verify step numbers are correct (1-5 instead of 1-6)
- [ ] Ensure Previous/Next buttons work correctly

### Test 2: Acquisition Function in Advanced Section
- [ ] Go to Advanced step (step 4)
- [ ] Verify "Acquisition Function" section is visible with "Enable" button
- [ ] Click Enable button - should show acquisition function controls
- [ ] Select different acquisition function types
- [ ] For UCB, verify beta parameter field appears
- [ ] For EI/PI, verify beta parameter field is hidden

### Test 3: Form Validation
- [ ] Try to submit form with default acquisition function (EI)
- [ ] Try to submit with UCB and valid beta value (e.g., 0.2)
- [ ] Try to submit with UCB and invalid beta value (e.g., 0.05)
- [ ] Verify validation messages appear correctly

### Test 4: Form Submission
- [ ] Complete entire form with acquisition function enabled
- [ ] Submit form and verify it processes correctly
- [ ] Check that acquisition function data is included in submission

### Test 5: Default Behavior
- [ ] Verify that when acquisition function is disabled, it defaults to Expected Improvement
- [ ] Verify that form can be submitted without enabling acquisition function section

## Expected Behavior

- Acquisition function configuration should only be available in the Advanced section
- Default acquisition function should be Expected Improvement (EI)
- Beta parameter should only appear for Upper Confidence Bound (UCB)
- Form validation should work correctly for all acquisition function types
- Form submission should include acquisition function configuration
