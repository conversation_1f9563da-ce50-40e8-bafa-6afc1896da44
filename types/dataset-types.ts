// types/dataset-types.ts

export interface UploadedDataRow {
  [parameterName: string]: string | number
}

export interface ParsedDataset {
  headers: string[]
  rows: UploadedDataRow[]
  parameterColumns: string[]
  targetColumns: string[]
  errors: DataValidationError[]
}

export interface DataValidationError {
  row: number
  column: string
  value: any
  message: string
  type: "missing" | "invalid_type" | "out_of_bounds" | "invalid_value"
}

export interface ColumnMapping {
  csvColumn: string
  optimizationParameter: string
  type: "parameter" | "target"
  required: boolean
}

export interface DatasetUploadResult {
  uploadedSamples: any[]
  batchId: string
  validRows: number
  totalRows: number
  errors: DataValidationError[]
}

export interface CSVTemplateConfig {
  parameters: Array<{
    name: string
    type: string
    values?: any[]
    bounds?: [number, number]
  }>
  targets: Array<{
    name: string
    mode: "MAX" | "MIN"
  }>
}
