/*
Email service for sending verification emails using console logging.
This is a development-only service that logs emails to the console.
*/

// The base URL for verification links
const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"

/**
 * Send a verification email to the user by logging it to the console
 */
export async function sendVerificationEmail(
  to: string,
  token: string,
  name: string,
  institution: string
) {
  try {
    const verificationLink = `${baseUrl}/verify-email?token=${token}`

    // Log the email to the console
    console.log("==================================================")
    console.log("VERIFICATION EMAIL")
    console.log("==================================================")
    console.log(`To: ${to}`)
    console.log(`Subject: Verify your academic email address`)
    console.log("--------------------------------------------------")
    console.log(`Hello ${name},`)
    console.log(
      `Thank you for applying for academic access to Optimizer™. To complete your verification as a member of ${institution}, please click the link below:`
    )
    console.log(`${verificationLink}`)
    console.log("This verification link will expire in 24 hours.")
    console.log(
      "If you did not request this verification, please ignore this email."
    )
    console.log("==================================================")

    return { success: true, data: { message: "Email logged to console" } }
  } catch (error) {
    console.error("Error sending verification email:", error)
    return { success: false, error }
  }
}
