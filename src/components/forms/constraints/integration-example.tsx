// components/constraints/integration-example.tsx
// Example of how to integrate centralized constraints into existing forms

"use client"

import React, { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"

// Import centralized constraints components
import { CentralizedConstraintBuilder } from "./centralized-constraint-builder"
import { ConstraintManager } from "./constraint-manager"
import { ConstraintAwareSampling } from "./constraint-aware-sampling"

// Import centralized constraints library
import {
  Constraint,
  Parameter,
  SamplingOptions,
  useConstraints,
  useConstraintContext
} from "@/lib/constraints"

// Example parameters for demonstration
const exampleParameters: Parameter[] = [
  {
    name: "temperature",
    type: "NumericalContinuous",
    bounds: [20, 200]
  },
  {
    name: "pressure",
    type: "NumericalContinuous",
    bounds: [1, 10]
  },
  {
    name: "catalyst",
    type: "CategoricalParameter",
    values: ["A", "B", "C"]
  },
  {
    name: "concentration",
    type: "NumericalDiscrete",
    values: [0.1, 0.5, 1.0, 2.0]
  }
]

export function ConstraintIntegrationExample() {
  const [constraints, setConstraints] = useState<Constraint[]>([])
  const [activeTab, setActiveTab] = useState("builder")

  // Example of using centralized constraint context
  const { isValid, hasConstraints, constraintCount, feasibilityRatio } =
    useConstraintContext(exampleParameters, constraints, {
      validationEnabled: true,
      samplingEnabled: true
    })

  const handleConstraintsChange = (newConstraints: Constraint[]) => {
    setConstraints(newConstraints)
    console.log("Constraints updated:", newConstraints)
  }

  const handleSamplesGenerated = (samples: any[]) => {
    console.log("Generated samples:", samples)
    // Handle the generated samples (e.g., save to state, send to API, etc.)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2 text-center">
        <h2 className="text-2xl font-bold">
          Centralized Constraints Integration
        </h2>
        <p className="text-muted-foreground">
          Example of how to use the centralized constraints system across
          different use cases
        </p>
      </div>

      {/* Status overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Constraint Status</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4 md:grid-cols-4">
          <div className="text-center">
            <div className="text-2xl font-bold">{constraintCount}</div>
            <div className="text-muted-foreground text-sm">Constraints</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{exampleParameters.length}</div>
            <div className="text-muted-foreground text-sm">Parameters</div>
          </div>
          <div className="text-center">
            <div
              className={`text-2xl font-bold ${isValid ? "text-green-600" : "text-red-600"}`}
            >
              {isValid ? "Valid" : "Invalid"}
            </div>
            <div className="text-muted-foreground text-sm">Validation</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {Math.round(feasibilityRatio * 100)}%
            </div>
            <div className="text-muted-foreground text-sm">Feasibility</div>
          </div>
        </CardContent>
      </Card>

      {/* Integration examples */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="builder">Create New Optimization</TabsTrigger>
          <TabsTrigger value="manager">Configuration Tab</TabsTrigger>
          <TabsTrigger value="sampling">Statistical Sampling</TabsTrigger>
        </TabsList>

        <TabsContent value="builder" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Use Case 1: Create New Optimization Form</CardTitle>
              <p className="text-muted-foreground text-sm">
                Interactive constraint builder with real-time validation and
                templates
              </p>
            </CardHeader>
            <CardContent>
              <CentralizedConstraintBuilder
                parameters={exampleParameters}
                constraints={constraints}
                onConstraintsChange={handleConstraintsChange}
                enableSampling={true}
                samplingOptions={{
                  strategy: "LHS",
                  nSamples: 20,
                  respectConstraints: true
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="manager" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>
                Use Case 2: Configuration Tab CRUD Operations
              </CardTitle>
              <p className="text-muted-foreground text-sm">
                Full constraint management with save/cancel functionality
              </p>
            </CardHeader>
            <CardContent>
              <ConstraintManager
                constraints={constraints}
                availableParameters={exampleParameters}
                onSave={updatedConstraints => {
                  setConstraints(updatedConstraints)
                  console.log("Constraints saved:", updatedConstraints)
                }}
                onCancel={() => {
                  console.log("Constraint editing cancelled")
                }}
                readOnly={false}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sampling" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Use Case 3: Statistical Sample Generation</CardTitle>
              <p className="text-muted-foreground text-sm">
                Constraint-aware sampling with LHS, Random, and Sobol strategies
              </p>
            </CardHeader>
            <CardContent>
              <ConstraintAwareSampling
                parameters={exampleParameters}
                constraints={constraints}
                onSamplesGenerated={handleSamplesGenerated}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Integration code examples */}
      <Card>
        <CardHeader>
          <CardTitle>Integration Code Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">1. Basic Integration</h4>
            <pre className="bg-muted overflow-x-auto rounded p-3 text-sm">
              {`import { 
  CentralizedConstraintBuilder,
  useConstraintContext,
  Constraint,
  Parameter 
} from "@/lib/constraints"

function MyOptimizationForm() {
  const [constraints, setConstraints] = useState<Constraint[]>([])
  
  const { isValid, hasConstraints } = useConstraintContext(
    parameters, 
    constraints
  )
  
  return (
    <CentralizedConstraintBuilder
      parameters={parameters}
      constraints={constraints}
      onConstraintsChange={setConstraints}
      enableSampling={true}
    />
  )
}`}
            </pre>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">2. Constraint-Aware Sampling</h4>
            <pre className="bg-muted overflow-x-auto rounded p-3 text-sm">
              {`import { 
  generateConstraintAwareSamples,
  SamplingOptions 
} from "@/lib/constraints"

const options: SamplingOptions = {
  strategy: "LHS",
  nSamples: 50,
  respectConstraints: true,
  maxAttempts: 1000
}

const result = await generateConstraintAwareSamples(
  parameters,
  constraints,
  options
)

console.log(\`Generated \${result.samples.length} feasible samples\`)`}
            </pre>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">3. Validation and Hooks</h4>
            <pre className="bg-muted overflow-x-auto rounded p-3 text-sm">
              {`import { 
  useConstraintValidation,
  useConstraints 
} from "@/lib/constraints"

function MyComponent() {
  const { constraints, addConstraint, updateConstraint } = useConstraints()
  
  const { 
    isValidating, 
    isValid, 
    validateAll 
  } = useConstraintValidation()
  
  // Validate all constraints
  const result = await validateAll(constraints, parameters)
  
  return (
    <div>
      {isValidating && <div>Validating...</div>}
      {isValid ? "All valid" : "Has errors"}
    </div>
  )
}`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
