// lib/constraints/builders.ts
// Constraint builder utilities and factories

import { Constraint, Parameter } from "./types"
import { generateConstraintId } from "./utils"

/**
 * Create a new constraint with default values
 */
export function createConstraint(
  type: string,
  parameters: string[] = [],
  name?: string
): Constraint {
  const baseConstraint = {
    id: generateConstraintId(),
    type,
    parameters,
    name: name || `${type.replace("Constraint", "")} ${Date.now()}`
  }

  switch (type) {
    case "ContinuousLinearConstraint":
      return {
        ...baseConstraint,
        type: "ContinuousLinearConstraint",
        operator: "<=",
        coefficients: parameters.map(() => 1.0),
        rhs: 0.0
      } as Constraint

    case "ContinuousCardinalityConstraint":
      return {
        ...baseConstraint,
        type: "ContinuousCardinalityConstraint",
        min_cardinality: 0,
        max_cardinality: parameters.length
      } as Constraint

    case "DiscreteCardinalityConstraint":
      return {
        ...baseConstraint,
        type: "DiscreteCardinalityConstraint",
        min_cardinality: 0,
        max_cardinality: parameters.length
      } as Constraint

    case "DiscreteExcludeConstraint":
      return {
        ...baseConstraint,
        type: "DiscreteExcludeConstraint",
        combiner: "AND",
        conditions: []
      } as Constraint

    case "DiscreteSumConstraint":
      return {
        ...baseConstraint,
        type: "DiscreteSumConstraint",
        condition: {
          type: "threshold",
          operator: "<=",
          threshold: 0
        }
      } as Constraint

    case "DiscreteProductConstraint":
      return {
        ...baseConstraint,
        type: "DiscreteProductConstraint",
        condition: {
          type: "threshold",
          operator: "<=",
          threshold: 0
        }
      } as Constraint

    case "DiscreteDependenciesConstraint":
      return {
        ...baseConstraint,
        type: "DiscreteDependenciesConstraint",
        affected_parameters: []
      } as Constraint

    case "DiscreteLinkedParametersConstraint":
      return {
        ...baseConstraint,
        type: "DiscreteLinkedParametersConstraint",
        dependencies: {}
      } as Constraint

    case "DiscreteNoLabelDuplicatesConstraint":
      return {
        ...baseConstraint,
        type: "DiscreteNoLabelDuplicatesConstraint"
      } as Constraint

    case "DiscretePermutationInvarianceConstraint":
      return {
        ...baseConstraint,
        type: "DiscretePermutationInvarianceConstraint",
        dependencies: {}
      } as Constraint

    case "DiscreteCustomConstraint":
      return {
        ...baseConstraint,
        type: "DiscreteCustomConstraint",
        validator: "def validate(params):\n    return True"
      } as Constraint

    default:
      return baseConstraint as Constraint
  }
}

/**
 * Clone a constraint with a new ID
 */
export function cloneConstraint(constraint: Constraint): Constraint {
  return {
    ...constraint,
    id: generateConstraintId(),
    name: `${constraint.name} (Copy)`
  }
}

/**
 * Update constraint parameters and adjust related fields
 */
export function updateConstraintParameters(
  constraint: Constraint,
  newParameters: string[]
): Constraint {
  const updated = { ...constraint, parameters: newParameters }

  // Adjust type-specific fields based on new parameters
  switch (constraint.type) {
    case "ContinuousLinearConstraint":
      const linearConstraint = updated as Constraint & {
        type: "ContinuousLinearConstraint"
      }
      // Adjust coefficients array to match parameter count
      const currentCoeffs = linearConstraint.coefficients || []
      const newCoeffs = newParameters.map((_, i) => currentCoeffs[i] || 1.0)
      return { ...linearConstraint, coefficients: newCoeffs }

    case "ContinuousCardinalityConstraint":
    case "DiscreteCardinalityConstraint":
      const cardConstraint = updated as Constraint & {
        type:
          | "ContinuousCardinalityConstraint"
          | "DiscreteCardinalityConstraint"
      }
      // Adjust max cardinality if it exceeds new parameter count
      const maxCard = cardConstraint.max_cardinality
      if (maxCard && maxCard > newParameters.length) {
        return { ...cardConstraint, max_cardinality: newParameters.length }
      }
      return cardConstraint

    default:
      return updated
  }
}

/**
 * Create a linear constraint from a simple equation string
 * Example: "2*x1 + 3*x2 <= 10"
 */
export function createLinearConstraintFromEquation(
  equation: string,
  availableParameters: string[]
): Constraint | null {
  try {
    // Simple parser for linear equations
    const parts = equation.split(/([<>=]+)/)
    if (parts.length !== 3) return null

    const leftSide = parts[0].trim()
    const operator = parts[1].trim() as ">=" | "<=" | "=" | ">" | "<"
    const rightSide = parseFloat(parts[2].trim())

    if (isNaN(rightSide)) return null

    // Parse left side for coefficients and parameters
    const terms = leftSide.split(/[+-]/).filter(term => term.trim())
    const parameters: string[] = []
    const coefficients: number[] = []

    for (const term of terms) {
      const match = term.match(
        /([+-]?\d*\.?\d*)\s*\*?\s*([a-zA-Z_][a-zA-Z0-9_]*)/
      )
      if (match) {
        const coeff = match[1] ? parseFloat(match[1]) : 1
        const param = match[2]

        if (availableParameters.includes(param)) {
          parameters.push(param)
          coefficients.push(coeff)
        }
      }
    }

    if (parameters.length === 0) return null

    return createConstraint(
      "ContinuousLinearConstraint",
      parameters
    ) as Constraint & {
      type: "ContinuousLinearConstraint"
      operator: typeof operator
      coefficients: number[]
      rhs: number
    } & {
      operator: typeof operator
      coefficients: typeof coefficients
      rhs: typeof rightSide
    }
  } catch (error) {
    console.error("Error parsing linear equation:", error)
    return null
  }
}

/**
 * Create constraint from template
 */
export function createConstraintFromTemplate(
  templateConstraint: Partial<Constraint>,
  parameterMapping: Record<string, string>
): Constraint {
  const constraint = { ...templateConstraint } as Constraint

  // Replace parameter placeholders with actual parameter names
  if (constraint.parameters) {
    constraint.parameters = constraint.parameters.map(
      placeholder => parameterMapping[placeholder] || placeholder
    )
  }

  // Update other parameter references in type-specific fields
  if (constraint.type === "DiscreteDependenciesConstraint") {
    const depConstraint = constraint as Constraint & {
      type: "DiscreteDependenciesConstraint"
    }
    if (depConstraint.affected_parameters) {
      depConstraint.affected_parameters = depConstraint.affected_parameters.map(
        group =>
          group.map(placeholder => parameterMapping[placeholder] || placeholder)
      )
    }
  }

  // Ensure constraint has an ID
  if (!constraint.id) {
    constraint.id = generateConstraintId()
  }

  return constraint
}

/**
 * Validate constraint configuration completeness
 */
export function isConstraintComplete(constraint: Constraint): boolean {
  if (
    !constraint.type ||
    !constraint.parameters ||
    constraint.parameters.length === 0
  ) {
    return false
  }

  switch (constraint.type) {
    case "ContinuousLinearConstraint":
      const linear = constraint as Constraint & {
        type: "ContinuousLinearConstraint"
      }
      return !!(
        linear.operator &&
        linear.coefficients &&
        linear.coefficients.length === linear.parameters.length &&
        linear.rhs !== undefined
      )

    case "DiscreteExcludeConstraint":
      const exclude = constraint as Constraint & {
        type: "DiscreteExcludeConstraint"
      }
      return !!(
        exclude.combiner &&
        exclude.conditions &&
        exclude.conditions.length > 0
      )

    case "DiscreteSumConstraint":
    case "DiscreteProductConstraint":
      const math = constraint as Constraint & {
        type: "DiscreteSumConstraint" | "DiscreteProductConstraint"
      }
      return !!(math.condition && math.condition.type)

    case "DiscreteDependenciesConstraint":
      const deps = constraint as Constraint & {
        type: "DiscreteDependenciesConstraint"
      }
      return !!(deps.affected_parameters && deps.affected_parameters.length > 0)

    case "DiscreteCustomConstraint":
      const custom = constraint as Constraint & {
        type: "DiscreteCustomConstraint"
      }
      return !!(custom.validator && custom.validator.trim())

    default:
      return true // Other constraint types are considered complete if they have type and parameters
  }
}

/**
 * Get required fields for a constraint type
 */
export function getRequiredFieldsForConstraintType(
  constraintType: string
): string[] {
  switch (constraintType) {
    case "ContinuousLinearConstraint":
      return ["parameters", "operator", "coefficients", "rhs"]

    case "ContinuousCardinalityConstraint":
    case "DiscreteCardinalityConstraint":
      return ["parameters"]

    case "DiscreteExcludeConstraint":
      return ["parameters", "combiner", "conditions"]

    case "DiscreteSumConstraint":
    case "DiscreteProductConstraint":
      return ["parameters", "condition"]

    case "DiscreteDependenciesConstraint":
      return ["parameters", "affected_parameters"]

    case "DiscreteCustomConstraint":
      return ["parameters", "validator"]

    default:
      return ["parameters"]
  }
}
