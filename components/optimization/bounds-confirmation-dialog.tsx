"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Loader2 } from "lucide-react"

interface PreviewData {
  valid_count: number
  dropped_count: number
  dropped_measurements: any[]
  impact_summary: {
    total_measurements: number
    valid_measurements: number
    dropped_measurements: number
    retention_rate: number
    affected_experiments?: {
      experiment_number: number
      reason: string
      parameter_violations: string[]
      target_violations: string[]
    }[]
  }
}

interface BoundsConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  previewData: PreviewData | null
  onConfirm: () => void
  onCancel: () => void
  isLoading?: boolean
}

export function BoundsConfirmationDialog({
  open,
  onOpenChange,
  previewData,
  onConfirm,
  onCancel,
  isLoading = false
}: BoundsConfirmationDialogProps) {
  if (!previewData) return null

  const retentionPercentage = Math.round(
    previewData.impact_summary.retention_rate * 100
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Confirm Bounds Update</DialogTitle>
          <DialogDescription>
            Review the impact of your bounds changes before applying them.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Impact Summary Alert */}
          <Alert>
            <AlertCircle className="size-4" />
            <AlertTitle>Data Impact Summary</AlertTitle>
            <AlertDescription>
              <div className="space-y-1">
                <div>
                  • <strong>{previewData.valid_count}</strong> measurements will
                  be kept ({retentionPercentage}%)
                </div>
                <div>
                  • <strong>{previewData.dropped_count}</strong> measurements
                  will be excluded
                </div>
                <div>• Campaign will be retrained with valid data only</div>
                {previewData.dropped_count === 0 && (
                  <div className="font-medium text-green-600">
                    ✓ No measurements will be lost
                  </div>
                )}
                {previewData.valid_count === 0 && (
                  <div className="font-medium text-red-600">
                    ⚠ All measurements would be excluded - update cannot
                    proceed
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>

          {/* Detailed Statistics */}
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            <div className="bg-card rounded-lg border p-3">
              <div className="text-2xl font-bold text-blue-600">
                {previewData.impact_summary.total_measurements}
              </div>
              <div className="text-muted-foreground text-sm">
                Total Measurements
              </div>
            </div>
            <div className="bg-card rounded-lg border p-3">
              <div className="text-2xl font-bold text-green-600">
                {previewData.valid_count}
              </div>
              <div className="text-muted-foreground text-sm">Will Be Kept</div>
            </div>
            <div className="bg-card rounded-lg border p-3">
              <div className="text-2xl font-bold text-red-600">
                {previewData.dropped_count}
              </div>
              <div className="text-muted-foreground text-sm">
                Will Be Excluded
              </div>
            </div>
            <div className="bg-card rounded-lg border p-3">
              <div className="text-2xl font-bold text-purple-600">
                {retentionPercentage}%
              </div>
              <div className="text-muted-foreground text-sm">
                Retention Rate
              </div>
            </div>
          </div>

          {/* Dropped Measurements Table */}
          {previewData.dropped_count > 0 && (
            <div className="space-y-2">
              <h4 className="text-destructive font-medium">
                Experiments to be Excluded ({previewData.dropped_count}):
              </h4>
              <p className="text-muted-foreground text-sm">
                These experiments will be marked as inactive and excluded from
                the campaign. They will appear greyed out in the history tab.
              </p>
              <div className="rounded-md border">
                <div className="max-h-60 overflow-y-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[100px]">Index</TableHead>
                        <TableHead>Parameters</TableHead>
                        <TableHead>Target Values</TableHead>
                        <TableHead>Reason</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {previewData.dropped_measurements
                        .slice(0, 10)
                        .map((measurement, idx) => {
                          // Extract parameter and target information
                          const params = Object.entries(measurement)
                            .filter(
                              ([key]) =>
                                !key.toLowerCase().includes("target") &&
                                !key.startsWith("_") &&
                                key !== "exclusion_reason"
                            )
                            .map(([key, value]) => `${key}: ${value}`)
                            .join(", ")

                          const targets = Object.entries(measurement)
                            .filter(
                              ([key]) =>
                                key.toLowerCase().includes("target") ||
                                key.startsWith("_")
                            )
                            .map(([key, value]) => `${key}: ${value}`)
                            .join(", ")

                          // Get the detailed exclusion reason
                          const exclusionReason =
                            measurement.exclusion_reason || "Outside new bounds"

                          return (
                            <TableRow key={idx}>
                              <TableCell className="font-medium">
                                Exp #{idx + 1}
                              </TableCell>
                              <TableCell className="text-sm">
                                {params || "N/A"}
                              </TableCell>
                              <TableCell className="text-sm">
                                {targets || "N/A"}
                              </TableCell>
                              <TableCell className="text-sm text-red-600">
                                <div className="max-w-xs">
                                  <div
                                    className="truncate"
                                    title={exclusionReason}
                                  >
                                    {exclusionReason}
                                  </div>
                                </div>
                              </TableCell>
                            </TableRow>
                          )
                        })}
                    </TableBody>
                  </Table>
                </div>
                {previewData.dropped_count > 10 && (
                  <div className="text-muted-foreground border-t p-3 text-sm">
                    ...and {previewData.dropped_count - 10} more measurements
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Warning for high data loss */}
          {retentionPercentage < 50 && previewData.valid_count > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="size-4" />
              <AlertTitle>High Data Loss Warning</AlertTitle>
              <AlertDescription>
                You will lose {100 - retentionPercentage}% of your measurement
                data. Consider adjusting your bounds to retain more data for
                better model performance.
              </AlertDescription>
            </Alert>
          )}

          {/* No data warning */}
          {previewData.valid_count === 0 && (
            <Alert variant="destructive">
              <AlertCircle className="size-4" />
              <AlertTitle>Cannot Proceed</AlertTitle>
              <AlertDescription>
                All measurements would be excluded by the new bounds. Please
                adjust your bounds to retain at least some measurement data.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex flex-col gap-2 sm:flex-row sm:justify-end">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isLoading || previewData.valid_count === 0}
            variant={previewData.valid_count === 0 ? "destructive" : "default"}
            className={
              previewData.valid_count === 0
                ? ""
                : "bg-primary text-primary-foreground hover:bg-primary/90"
            }
          >
            {isLoading && <Loader2 className="mr-2 size-4 animate-spin" />}
            {previewData.valid_count === 0
              ? "Cannot Apply Changes"
              : "Apply Changes & Retrain"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
