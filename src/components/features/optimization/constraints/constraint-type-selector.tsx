"use client"

import { useState } from "react"
import {
  Check,
  ChevronDown,
  Calculator,
  Hash,
  X,
  Sigma,
  GitBranch,
  Code,
  HelpCircle
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import { CONSTRAINT_CATEGORIES, getConstraintCategory } from "@/lib/constraints"

// Icon mapping for constraint categories
const CATEGORY_ICONS = {
  linear: Calculator,
  cardinality: Hash,
  exclusion: X,
  mathematical: Sigma,
  dependency: GitBranch,
  custom: Code
} as const

// Constraint type definitions with metadata
const CONSTRAINT_TYPES = [
  {
    value: "ContinuousLinearConstraint",
    label: "Linear Constraint",
    description: "Mathematical relationships like ax + by ≤ c, x < y, etc.",
    difficulty: "beginner",
    example: "x6 + x15 ≤ 1.0, x19 < x20"
  },
  {
    value: "DiscreteExcludeConstraint",
    label: "Exclusion Rule",
    description: "Exclude invalid parameter combinations",
    difficulty: "beginner",
    example: "Avoid high temperature with sensitive catalyst"
  },
  {
    value: "ContinuousCardinalityConstraint",
    label: "Cardinality (Continuous)",
    description: "Control number of active continuous parameters",
    difficulty: "intermediate",
    example: "Use at most 3 components in mixture"
  },
  {
    value: "DiscreteCardinalityConstraint",
    label: "Cardinality (Discrete)",
    description: "Control number of active discrete parameters",
    difficulty: "intermediate",
    example: "Select exactly 2 solvents"
  },
  {
    value: "DiscreteSumConstraint",
    label: "Sum Constraint",
    description: "Constraint on sum of parameter values",
    difficulty: "intermediate",
    example: "Sum of fractions equals 100%"
  },
  {
    value: "DiscreteProductConstraint",
    label: "Product Constraint",
    description: "Constraint on product of parameter values",
    difficulty: "advanced",
    example: "Temperature × Pressure ≤ 1000"
  },
  {
    value: "DiscreteDependenciesConstraint",
    label: "Dependencies",
    description: "Parameters depend on other parameters",
    difficulty: "advanced",
    example: "Catalyst only relevant when reaction enabled"
  },
  {
    value: "DiscreteNoLabelDuplicatesConstraint",
    label: "No Duplicates",
    description: "Prevent duplicate values across parameters",
    difficulty: "intermediate",
    example: "Each solvent slot must be different"
  },
  {
    value: "DiscreteCustomConstraint",
    label: "Custom Function",
    description: "User-defined constraint function",
    difficulty: "advanced",
    example: "sqrt(temp) * pressure^3 ≤ 5.6"
  }
] as const

interface ConstraintTypeSelectorProps {
  value?: string
  onValueChange: (value: string) => void
  availableParameters?: string[]
  className?: string
  placeholder?: string
}

export function ConstraintTypeSelector({
  value,
  onValueChange,
  availableParameters = [],
  className,
  placeholder = "Select constraint type..."
}: ConstraintTypeSelectorProps) {
  const [open, setOpen] = useState(false)

  const selectedType = CONSTRAINT_TYPES.find(type => type.value === value)

  // Group constraint types by category
  const groupedTypes = CONSTRAINT_TYPES.reduce(
    (acc, type) => {
      const category = getConstraintCategory(type.value)
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(type)
      return acc
    },
    {} as Record<string, (typeof CONSTRAINT_TYPES)[number][]>
  )

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return "bg-green-100 text-green-800"
      case "intermediate":
        return "bg-yellow-100 text-yellow-800"
      case "advanced":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <TooltipProvider>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between", className)}
          >
            {selectedType ? (
              <div className="flex items-center gap-2">
                {(() => {
                  const category = getConstraintCategory(selectedType.value)
                  const Icon =
                    CATEGORY_ICONS[category as keyof typeof CATEGORY_ICONS] ||
                    HelpCircle
                  return <Icon className="size-4" />
                })()}
                <span>{selectedType.label}</span>
                <Badge
                  variant="secondary"
                  className={cn(
                    "text-xs",
                    getDifficultyColor(selectedType.difficulty)
                  )}
                >
                  {selectedType.difficulty}
                </Badge>
              </div>
            ) : (
              placeholder
            )}
            <ChevronDown className="ml-2 size-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-[400px] p-0">
          <Command>
            <CommandInput placeholder="Search constraint types..." />
            <CommandEmpty>No constraint type found.</CommandEmpty>
            <CommandList className="max-h-[300px]">
              {Object.entries(groupedTypes).map(([categoryKey, types]) => {
                const category =
                  CONSTRAINT_CATEGORIES[
                    categoryKey as keyof typeof CONSTRAINT_CATEGORIES
                  ]
                const Icon =
                  CATEGORY_ICONS[categoryKey as keyof typeof CATEGORY_ICONS]

                return (
                  <CommandGroup
                    key={categoryKey}
                    heading={
                      <div className="flex items-center gap-2">
                        <Icon className="size-4" />
                        <span>{category.name}</span>
                      </div>
                    }
                  >
                    {types.map(type => (
                      <CommandItem
                        key={type.value}
                        value={type.value}
                        onSelect={currentValue => {
                          onValueChange(currentValue)
                          setOpen(false)
                        }}
                        className="flex items-start gap-2 p-3"
                      >
                        <Check
                          className={cn(
                            "mt-1 size-4",
                            value === type.value ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{type.label}</span>
                            <Badge
                              variant="secondary"
                              className={cn(
                                "text-xs",
                                getDifficultyColor(type.difficulty)
                              )}
                            >
                              {type.difficulty}
                            </Badge>
                            <Tooltip>
                              <TooltipTrigger>
                                <HelpCircle className="text-muted-foreground size-3" />
                              </TooltipTrigger>
                              <TooltipContent
                                side="right"
                                className="max-w-[250px]"
                              >
                                <p className="text-sm">{type.description}</p>
                                <p className="text-muted-foreground mt-1 text-xs">
                                  Example: {type.example}
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <p className="text-muted-foreground text-sm">
                            {type.description}
                          </p>
                          <p className="text-muted-foreground text-xs italic">
                            {type.example}
                          </p>
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )
              })}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </TooltipProvider>
  )
}
