"use client"

import { useState } from "react"
import {
  ChevronDown,
  ChevronRight,
  Settings,
  Zap,
  Brain,
  Link
} from "lucide-react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import { cn } from "@/lib/utils"
import { DevelopmentBadge } from "@/components/ui/development-badge"
import { isFeatureInDevelopment } from "@/lib/feature-flags"
import { toast } from "@/components/ui/use-toast"

interface AdvancedOptionsSectionProps {
  children?: React.ReactNode
  defaultOpen?: boolean
  className?: string
  showConstraints?: boolean
  constraintCount?: number
  onConstraintsToggle?: (enabled: boolean) => void
  showAcquisitionFunction?: boolean
  acquisitionFunctionChildren?: React.ReactNode
  onAcquisitionFunctionToggle?: (enabled: boolean) => void
}

export function AdvancedOptionsSection({
  children,
  defaultOpen = false,
  className,
  showConstraints = true,
  constraintCount = 0,
  onConstraintsToggle,
  showAcquisitionFunction = true,
  acquisitionFunctionChildren,
  onAcquisitionFunctionToggle
}: AdvancedOptionsSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen)
  const [constraintsEnabled, setConstraintsEnabled] = useState(false)
  const [acquisitionFunctionEnabled, setAcquisitionFunctionEnabled] =
    useState(false)

  const handleConstraintsToggle = () => {
    // Check if constraints feature is in development
    if (isFeatureInDevelopment("optimizationConstraints")) {
      toast({
        title: "🔧 Feature in Development",
        description:
          "Constraints configuration is currently being built. Coming soon!",
        variant: "default"
      })
      return
    }

    const newState = !constraintsEnabled
    setConstraintsEnabled(newState)
    onConstraintsToggle?.(newState)
  }

  const handleAcquisitionFunctionToggle = () => {
    const newState = !acquisitionFunctionEnabled
    setAcquisitionFunctionEnabled(newState)
    onAcquisitionFunctionToggle?.(newState)
  }

  return (
    <Card
      className={cn(
        "border-muted-foreground/20 border-2 border-dashed",
        className
      )}
    >
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="hover:bg-muted/50 cursor-pointer transition-colors">
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-2">
                <Settings className="text-muted-foreground size-5" />
                <span>Advanced Options</span>
                <Badge variant="secondary" className="text-xs">
                  Expert Mode
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                {constraintCount > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {constraintCount} constraint
                    {constraintCount !== 1 ? "s" : ""}
                  </Badge>
                )}
                {isOpen ? (
                  <ChevronDown className="size-4" />
                ) : (
                  <ChevronRight className="size-4" />
                )}
              </div>
            </CardTitle>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="space-y-6">
            {/* Constraints Section */}
            {showConstraints && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Link className="size-4 text-blue-600" />
                    <h4 className="font-medium">Constraints</h4>
                    <Badge variant="outline" className="text-xs">
                      Advanced
                    </Badge>
                    {isFeatureInDevelopment("optimizationConstraints") && (
                      <DevelopmentBadge variant="icon" size="sm" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant={constraintsEnabled ? "default" : "outline"}
                    size="sm"
                    onClick={handleConstraintsToggle}
                    disabled={isFeatureInDevelopment("optimizationConstraints")} // button is enabled when it is switched to false
                    className="text-xs"
                  >
                    {constraintsEnabled ? "Enabled" : "Enable"}
                  </Button>
                </div>

                <div className="text-muted-foreground space-y-1 text-sm">
                  <p>
                    Add mathematical constraints to restrict the search space
                    when parameters have dependencies or limitations.
                  </p>
                  <p className="text-xs">
                    <strong>Enable if:</strong> You have mixture ratios,
                    exclusion rules, or parameter relationships that must be
                    maintained.
                  </p>
                </div>

                {constraintsEnabled &&
                  !isFeatureInDevelopment("optimizationConstraints") && (
                    <div className="bg-background rounded-md border p-4">
                      {children}
                    </div>
                  )}
              </div>
            )}

            {/* Acquisition Function Section */}
            {showAcquisitionFunction && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Zap className="size-4 text-orange-600" />
                    <h4 className="font-medium">Acquisition Function</h4>
                    <Badge variant="outline" className="text-xs">
                      Advanced
                    </Badge>
                  </div>
                  <Button
                    type="button"
                    variant={acquisitionFunctionEnabled ? "default" : "outline"}
                    size="sm"
                    onClick={handleAcquisitionFunctionToggle}
                    className="text-xs"
                  >
                    {acquisitionFunctionEnabled ? "Enabled" : "Enable"}
                  </Button>
                </div>

                <div className="text-muted-foreground space-y-1 text-sm">
                  <p>
                    Control how the optimizer balances exploration vs
                    exploitation. Default uses Expected Improvement (qEI).
                  </p>
                  <p className="text-xs">
                    <strong>Enable if:</strong> You want to fine-tune
                    exploration behavior or use specific acquisition strategies
                    like UCB with custom beta values.
                  </p>
                </div>

                {acquisitionFunctionEnabled && (
                  <div className="bg-background rounded-md border p-4">
                    {acquisitionFunctionChildren}
                  </div>
                )}
              </div>
            )}

            {/* Surrogate Model Section */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Brain className="size-4 text-purple-600" />
                <h4 className="font-medium">Surrogate Model</h4>
                <Badge variant="outline" className="text-xs">
                  Advanced
                </Badge>
                {isFeatureInDevelopment("optimizationSurrogateModel") && (
                  <DevelopmentBadge variant="text" size="sm" />
                )}
              </div>
              <div className="text-muted-foreground space-y-1 text-sm">
                <p>
                  Configure the underlying machine learning model. Default uses
                  Gaussian Process with automatic kernel selection.
                </p>
                <p className="text-xs">
                  <strong>Coming Soon:</strong> Advanced model configuration for
                  specialized optimization scenarios.
                </p>
              </div>
              {/* Surrogate model controls would go here when implemented */}
            </div>

            {/* Help Section */}
            <div className="border-t pt-4">
              <div className="text-muted-foreground text-xs">
                <p className="mb-1 flex items-center gap-1 font-medium">
                  Need Help?
                  {(isFeatureInDevelopment("optimizationConstraints") ||
                    isFeatureInDevelopment("optimizationSurrogateModel")) && (
                    <DevelopmentBadge
                      variant="icon"
                      size="sm"
                      showTooltip={false}
                    />
                  )}
                </p>
                <p>
                  Advanced options require understanding of Bayesian
                  optimization. See our{" "}
                  <a
                    href="/docs/advanced"
                    className="text-blue-600 hover:underline"
                  >
                    documentation
                  </a>{" "}
                  for detailed explanations.
                </p>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}

// Export a simpler version for basic use cases
export function SimpleAdvancedToggle({
  children,
  title = "Advanced Options",
  description,
  defaultOpen = false
}: {
  children: React.ReactNode
  title?: string
  description?: string
  defaultOpen?: boolean
}) {
  const [isOpen, setIsOpen] = useState(defaultOpen)

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className="h-auto w-full justify-between border border-dashed p-4"
        >
          <div className="flex items-center gap-2">
            <Settings className="size-4" />
            <span className="font-medium">{title}</span>
          </div>
          {isOpen ? (
            <ChevronDown className="size-4" />
          ) : (
            <ChevronRight className="size-4" />
          )}
        </Button>
      </CollapsibleTrigger>

      <CollapsibleContent className="mt-2">
        <div className="space-y-4 rounded-md border p-4">
          {description && (
            <p className="text-muted-foreground text-sm">{description}</p>
          )}
          {children}
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
