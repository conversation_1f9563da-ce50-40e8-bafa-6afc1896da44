// actions/optimization-workflow-actions.ts
"use server"

import { ActionState } from "@/types";
import { auth } from "@clerk/nextjs/server";
import { getActualUserId } from "@/lib/auth-utils";
import {
  createOptimization,
  getSuggestion,
  addMeasurement as addApiMeasurement,
  getBestPoint,
  loadOptimization,
  checkOptimizationExists
} from "./optimization-actions";
import {
  createOptimizationDBAction,
  getOptimizationByOptimizerIdAction,
  createMeasurementAction
} from "./db/optimizations-actions";
import {
  InsertOptimization,
  SelectOptimization,
  SelectMeasurement
} from "@/db/schema/optimizations-schema";

/**
 * Creates a new optimization and stores its metadata in the database
 */
export async function createOptimizationWorkflowAction(
  name: string,
  description: string,
  config: {
    parameters: any[];
    target_config: any;
    recommender_config?: any;
    constraints?: any[];
  }
): Promise<ActionState<SelectOptimization>> {
  console.log("🔍 CREATE OPTIMIZATION WORKFLOW - Starting function");

  // Safety wrapper to ensure we always return a proper ActionState
  try {
    const result = await _createOptimizationWorkflowActionInternal(name, description, config);

    // Validate the result structure
    if (!result || typeof result !== 'object' || typeof result.isSuccess !== 'boolean') {
      console.error("🔍 CREATE OPTIMIZATION WORKFLOW - Invalid result structure:", result);
      return {
        isSuccess: false,
        message: "Internal error: Invalid response structure"
      };
    }

    return result;
  } catch (error) {
    console.error("🔍 CREATE OPTIMIZATION WORKFLOW - Top-level error:", error);
    return {
      isSuccess: false,
      message: `Unexpected error: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Internal implementation of createOptimizationWorkflowAction
 */
async function _createOptimizationWorkflowActionInternal(
  name: string,
  description: string,
  config: {
    parameters: any[];
    target_config: any;
    recommender_config?: any;
    constraints?: any[];
  }
): Promise<ActionState<SelectOptimization>> {
  console.log("🔍 CREATE OPTIMIZATION WORKFLOW - Starting internal function");

  // Get the current user session from Clerk
  const authResult = await auth();
  const { userId: clerkUserId, getToken } = authResult;

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId();

  console.log("🔍 CREATE OPTIMIZATION WORKFLOW - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  });

  if (!userId) {
    console.log("🔍 CREATE OPTIMIZATION WORKFLOW - No user ID, returning error");
    return {
      isSuccess: false,
      message: "You must be signed in to create an optimization"
    };
  }

  // Get the authentication token (not used directly but might be needed in the future)
  await getToken();

  try {
    console.log("Starting optimization workflow with name:", name);

    // The optimizer ID will be generated in the createOptimization function
    // We'll use a similar format for the database entry

    // Create the optimization in the API
    console.log("Calling API to create optimization");
    // Log the config to check for any issues
    console.log("Config:", JSON.stringify(config, null, 2));

    // Additional logging for categorical parameters
    const categoricalParams = config.parameters.filter(p => p.type === "CategoricalParameter");
    if (categoricalParams.length > 0) {
      console.log("Categorical parameters found:", categoricalParams.length);
      categoricalParams.forEach((param, index) => {
        console.log(`Categorical parameter ${index + 1}: ${param.name}`);
        console.log(`  Values: ${JSON.stringify(param.values)}`);
        console.log(`  Encoding: ${param.encoding || 'OHE'}`);
      });
    }

    // Ensure recommender_config has the required fields
    if (config.recommender_config) {
      // Ensure n_restarts is an integer
      if (config.recommender_config.n_restarts === undefined ||
          config.recommender_config.n_restarts === null) {
        config.recommender_config.n_restarts = 10;
      }

      // Ensure n_raw_samples is an integer
      if (config.recommender_config.n_raw_samples === undefined ||
          config.recommender_config.n_raw_samples === null) {
        config.recommender_config.n_raw_samples = 64;
      }
    }

    // Special handling for categorical parameters
    if (Array.isArray(config.parameters)) {
      config.parameters = config.parameters.map(param => {
        if (param.type === "CategoricalParameter") {
          // Ensure values is an array and not empty
          if (!Array.isArray(param.values) || param.values.length === 0) {
            console.error(`Categorical parameter ${param.name} has invalid or missing values:`, param.values);
            throw new Error(`Categorical parameter ${param.name} must have at least one value`);
          }

          // Log the values for debugging
          console.log(`Categorical parameter ${param.name} values:`, JSON.stringify(param.values));

          // Ensure encoding is set
          if (!param.encoding) {
            param.encoding = "OHE";
          }
        }
        return param;
      });
    }

    // We'll use the name directly and let the createOptimization function handle sanitization
    const apiResult = await createOptimization(name, config);
    console.log("API result:", apiResult);

    if (!apiResult.isSuccess) {
      console.error("API error:", apiResult.message);
      return {
        isSuccess: false,
        message: `API Error: ${apiResult.message}`
      };
    }

    // Create an entry in our database
    console.log("Creating entry in database");

    // Extract the optimizer ID from the API response or generate one based on the name
    // The API might not return optimizer_id directly in the response
    console.log("API result data:", apiResult.data);
    const optimizerId = (apiResult.data as unknown as { optimizer_id: string }).optimizer_id || name.toLowerCase().replace(/\s+/g, '_') + '_' + Date.now();


    // Handle target name and mode differently for single vs multi-target optimizations
    let targetName: string;
    let targetMode: string;

    // Check if this is a multi-target optimization by examining the target_config
    if (Array.isArray(config.target_config) && config.target_config.length > 1) {
      // For multi-target optimizations, use the first target's name and a combined description
      targetName = config.target_config[0].name || "Multi-target";
      targetMode = "MULTI";
      console.log("Using multi-target configuration with primary target:", targetName);
    } else {
      // For single-target optimizations
      const targetConfig = Array.isArray(config.target_config)
        ? config.target_config[0]
        : config.target_config;
      targetName = targetConfig.name || "Target";
      targetMode = targetConfig.mode || "MAX";
      console.log("Using single-target configuration:", targetName, targetMode);
    }

    const dbOptimization: InsertOptimization = {
      userId,
      name,
      description,
      optimizerId, // Use the optimizer ID from the API
      config,
      targetName,
      targetMode,
      status: "active" // Start as active
    };

    console.log("Database entry to create:", dbOptimization);
    const dbResult = await createOptimizationDBAction(dbOptimization);
    console.log("Database result:", dbResult);

    if (!dbResult.isSuccess) {
      console.error("Database error:", dbResult.message);
      return {
        isSuccess: false,
        message: `Database Error: ${dbResult.message}`
      };
    }

    console.log("Optimization workflow completed successfully");
    return {
      isSuccess: true,
      message: "Optimization created successfully",
      data: dbResult.data
    };

  } catch (error) {
    console.error("🔍 CREATE OPTIMIZATION WORKFLOW - Unexpected error:", error);
    return {
      isSuccess: false,
      message: `Failed to create optimization workflow: ${error instanceof Error ? error.message : String(error)}`
    };
  } finally {
    console.log("🔍 CREATE OPTIMIZATION WORKFLOW - Function completed");
  }
}

/**
 * Gets the next suggestion and stores it in the database
 */
export async function getSuggestionWorkflowAction(
  optimizationId: string,
  batchSize: number = 1
): Promise<ActionState<{ suggestions: any[], batchId: string | null }>> {
  console.log(`[getSuggestionWorkflowAction] Starting with optimizationId=${optimizationId}, batchSize=${batchSize}`);

  const { userId } = await auth();

  if (!userId) {
    console.log(`[getSuggestionWorkflowAction] No user ID found, aborting`);
    return {
      isSuccess: false,
      message: "You must be signed in to get suggestions"
    };
  }

  console.log(`[getSuggestionWorkflowAction] User ID: ${userId}`);

  try {
    // Get the optimization from our database
    console.log(`[getSuggestionWorkflowAction] Fetching optimization from database with ID: ${optimizationId}`);
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId);

    if (!optResult.isSuccess) {
      console.error(`[getSuggestionWorkflowAction] Database error: ${optResult.message}`);
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      };
    }

    console.log(`[getSuggestionWorkflowAction] Successfully retrieved optimization from database`);

    // Check if optimization exists in the backend and recreate if needed
    console.log(`[getSuggestionWorkflowAction] Checking if optimization exists in backend`);
    const existsResult = await checkOptimizationExists(optimizationId);

    if (existsResult.isSuccess && existsResult.data === false) {
      console.log(`[getSuggestionWorkflowAction] Optimization doesn't exist in backend. Recreating...`);

      const recreateResult = await recreateOptimizationWorkflowAction(optimizationId);
      if (!recreateResult.isSuccess) {
        console.error(`[getSuggestionWorkflowAction] Failed to recreate optimization: ${recreateResult.message}`);
        return {
          isSuccess: false,
          message: `Failed to recreate optimization: ${recreateResult.message}`
        };
      }

      console.log(`[getSuggestionWorkflowAction] Successfully recreated optimization in backend`);
    } else if (existsResult.isSuccess && existsResult.data === true) {
      console.log(`[getSuggestionWorkflowAction] Optimization exists in backend, but checking if it has measurements...`);

      // Check if the backend has measurements by looking at the response
      // If it says "No measurements yet", we should restore measurements
      try {
        const { getBestPoint } = await import("./optimization-actions");
        const bestResult = await getBestPoint(optimizationId);

        if (bestResult.isSuccess && bestResult.data?.message === "No measurements yet") {
          console.log(`[getSuggestionWorkflowAction] Backend has no measurements, checking if we have measurements to restore...`);

          // Check if we have measurements in our database
          const { getMeasurementsAction } = await import("./db/optimizations-actions");
          const measurementsResult = await getMeasurementsAction(optResult.data.id);

          if (measurementsResult.isSuccess && measurementsResult.data && measurementsResult.data.length > 0) {
            console.log(`[getSuggestionWorkflowAction] Found ${measurementsResult.data.length} measurements in database but none in backend. Restoring...`);

            // Restore measurements without recreating the optimization
            await restoreMeasurementsToBackend(optimizationId, measurementsResult.data);
          } else {
            console.log(`[getSuggestionWorkflowAction] No measurements found in database to restore`);
          }
        }
      } catch (error) {
        console.warn(`[getSuggestionWorkflowAction] Error checking measurements:`, error);
      }
    } else if (!existsResult.isSuccess) {
      console.warn(`[getSuggestionWorkflowAction] Could not verify optimization existence: ${existsResult.message}`);
      // Continue anyway, but if the API call fails with "not found", we'll try recreation
    }

    // Validate batch size
    const validatedBatchSize = Math.min(Math.max(1, batchSize), 100);
    if (validatedBatchSize !== batchSize) {
      console.warn(`[getSuggestionWorkflowAction] Adjusted batch size from ${batchSize} to ${validatedBatchSize} (valid range: 1-100)`);
    }

    // Always generate a batch ID, regardless of batch size
    // This ensures that all measurements from the same batch share the same batch ID
    // We use a consistent format: batch_<optimizationId>_<timestamp>
    const batchId = `batch_${optimizationId}_${Date.now()}`;
    console.log(`[getSuggestionWorkflowAction] Generated batch ID: ${batchId} for batch size ${validatedBatchSize}`);

    // Get suggestion from the API
    console.log(`[getSuggestionWorkflowAction] Calling API to get ${validatedBatchSize} suggestions`);
    let apiResult = await getSuggestion(optimizationId, validatedBatchSize);

    // If the API call failed with "not found", try to recreate and retry once
    if (!apiResult.isSuccess && apiResult.message && (
      apiResult.message.includes("not found") ||
      apiResult.message.includes("Optimizer") && apiResult.message.includes("not found")
    )) {
      console.log(`[getSuggestionWorkflowAction] API call failed with "not found". Attempting recreation and retry...`);

      const recreateResult = await recreateOptimizationWorkflowAction(optimizationId);
      if (recreateResult.isSuccess) {
        console.log(`[getSuggestionWorkflowAction] Recreation successful, retrying API call...`);
        apiResult = await getSuggestion(optimizationId, validatedBatchSize);
      } else {
        console.error(`[getSuggestionWorkflowAction] Recreation failed: ${recreateResult.message}`);
        return {
          isSuccess: false,
          message: `Failed to recreate optimization: ${recreateResult.message}`
        };
      }
    }

    if (!apiResult.isSuccess || !apiResult.data) {
      console.error(`[getSuggestionWorkflowAction] API error: ${apiResult.message}`);
      return {
        isSuccess: false,
        message: `API Error: ${apiResult.message}`
      };
    }

    console.log(`[getSuggestionWorkflowAction] API returned ${apiResult.data.suggestions.length} suggestions successfully`);

    // Log the first suggestion for debugging
    if (apiResult.data.suggestions.length > 0) {
      console.log(`[getSuggestionWorkflowAction] First suggestion sample:`,
        JSON.stringify(apiResult.data.suggestions[0], null, 2).substring(0, 200) + '...');
    }

    return {
      isSuccess: true,
      message: "Got suggestions successfully",
      data: {
        suggestions: apiResult.data.suggestions,
        batchId: batchId
      }
    };

  } catch (error) {
    console.error(`[getSuggestionWorkflowAction] Error in workflow:`, error);
    return {
      isSuccess: false,
      message: `Failed to get suggestions: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Adds a measurement to the optimization and stores it in the database
 */
export async function addMeasurementWorkflowAction(
  optimizationId: string,
  parameters: Record<string, any>,
  targetValue: number | Record<string, number>,
  isRecommended: boolean = true,
  batchId: string | null = null
): Promise<ActionState<SelectMeasurement>> {
  const authResult = await auth();
  const { userId: clerkUserId } = authResult;

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId();

  console.log("🔍 ADD MEASUREMENT WORKFLOW - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  });

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to add measurements"
    };
  }

  try {
    // Get the optimization from our database
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId);

    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      };
    }

    // Add measurement to the API
    // For multi-target optimizations, we need to handle this differently
    let apiResult;

    // Handle both multi-target and single-target measurements the same way
    // The addApiMeasurement function will handle the different formats
    console.log("Adding measurement for optimization");
    if (optResult.data.targetMode === "MULTI") {
      console.log("Multi-target optimization detected");
    }

    // Cast targetValue to any to handle both single and multi-target cases
    // The addApiMeasurement function in the API client handles both formats
    apiResult = await addApiMeasurement(
      optimizationId,
      parameters,
      targetValue as any
    );

    if (!apiResult.isSuccess) {
      return {
        isSuccess: false,
        message: `API Error: ${apiResult.message}`
      };
    }

    // Add measurement to our database
    let dbTargetValue: string;
    let dbTargetValues: Record<string, number> | null = null;

    if (typeof targetValue === "object") {
      // For multi-target, use the first target value as the main target value
      // and store all target values in the targetValues field
      const firstTargetKey = Object.keys(targetValue)[0];
      dbTargetValue = targetValue[firstTargetKey].toString();
      dbTargetValues = targetValue;
      console.log("Multi-target values:", dbTargetValues);
    } else {
      dbTargetValue = targetValue.toString();
      // For single target, we can also store it in targetValues for consistency
      if (optResult.data.targetName) {
        dbTargetValues = { [optResult.data.targetName]: targetValue as number };
      }
    }

    const measurement = {
      optimizationId: optResult.data.id,
      parameters,
      targetValue: dbTargetValue,
      targetValues: dbTargetValues,
      isRecommended,
      batchId
    };

    // Log batch information with more details
    if (batchId) {
      console.log(`Adding measurement with batch ID: ${batchId} (isRecommended: ${isRecommended})`);
    } else {
      console.log(`Adding measurement without batch ID (isRecommended: ${isRecommended})`);
    }

    console.log("Saving measurement to database:", measurement);

    const dbResult = await createMeasurementAction(measurement);

    if (!dbResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Database Error: ${dbResult.message}`
      };
    }

    // Update best value in the optimization if it's better
    try {
      const bestResult = await getBestPoint(optimizationId);

      if (bestResult.isSuccess && bestResult.data && bestResult.data.best_value !== undefined) {
        // Update the optimization with the current best values and ensure it's marked as active
        // Use the status action to track history
        await import("./optimization-status-actions").then(module =>
          module.updateOptimizationStatusAction(
            optResult.data.id,
            "active",
            "measurement_added"
          )
        );
      }
    } catch (error) {
      console.error("Error updating best value:", error);
      // Continue even if this part fails
    }

    return {
      isSuccess: true,
      message: "Measurement added successfully",
      data: dbResult.data
    };

  } catch (error) {
    console.error("Error in workflow:", error);
    return {
      isSuccess: false,
      message: "Failed to add measurement"
    };
  }
}

/**
 * Helper function to restore measurements to the backend
 */
async function restoreMeasurementsToBackend(
  optimizationId: string,
  measurements: any[]
): Promise<void> {
  console.log(`[restoreMeasurementsToBackend] Restoring ${measurements.length} measurements to backend...`);

  try {
    // Import the addMeasurementAction function that supports both single and multi-target
    const { addMeasurementAction } = await import("../lib/api/baybe-client");

    // Get authentication token
    const { getToken } = await import("@clerk/nextjs/server").then(m => m.auth());
    const token = await getToken();

    // Restore each measurement
    let restoredCount = 0;
    for (const measurement of measurements) {
      try {
        console.log(`[restoreMeasurementsToBackend] Restoring measurement ${restoredCount + 1}/${measurements.length}`);

        // Determine if this is single or multi-target
        let targetValue: number | Record<string, number>;
        if (measurement.targetValues) {
          // Multi-target case
          targetValue = measurement.targetValues as Record<string, number>;
        } else {
          // Single-target case
          targetValue = parseFloat(measurement.targetValue);
        }

        const restoreResult = await addMeasurementAction(
          optimizationId,
          measurement.parameters as Record<string, any>,
          targetValue,
          token ?? undefined
        );

        if (restoreResult.isSuccess) {
          restoredCount++;
        } else {
          console.warn(`[restoreMeasurementsToBackend] Failed to restore measurement ${restoredCount + 1}:`, restoreResult.message);
        }
      } catch (error) {
        console.error(`[restoreMeasurementsToBackend] Error restoring measurement ${restoredCount + 1}:`, error);
      }
    }

    console.log(`[restoreMeasurementsToBackend] Successfully restored ${restoredCount}/${measurements.length} measurements`);
  } catch (error) {
    console.error(`[restoreMeasurementsToBackend] Error during restoration:`, error);
  }
}

/**
 * Recreates an optimization campaign in the backend using stored configuration
 */
export async function recreateOptimizationWorkflowAction(
  optimizationId: string
): Promise<ActionState<{ status: string; message: string }>> {
  console.log(`[recreateOptimizationWorkflowAction] Starting recreation for optimization: ${optimizationId}`);

  const { userId } = await auth();

  if (!userId) {
    console.error(`[recreateOptimizationWorkflowAction] No user ID found`);
    return {
      isSuccess: false,
      message: "You must be signed in to recreate an optimization"
    };
  }

  console.log(`[recreateOptimizationWorkflowAction] User ID: ${userId}`);

  try {
    // Get the optimization from our database
    console.log(`[recreateOptimizationWorkflowAction] Fetching optimization from database`);
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId);

    if (!optResult.isSuccess) {
      console.error(`[recreateOptimizationWorkflowAction] Database error: ${optResult.message}`);
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      };
    }

    const optimization = optResult.data;
    console.log(`[recreateOptimizationWorkflowAction] Retrieved optimization from database:`, {
      id: optimization.id,
      name: optimization.name,
      optimizerId: optimization.optimizerId,
      hasConfig: !!optimization.config
    });

    // Check if optimization exists in the backend
    console.log("Checking if optimization exists in backend...");
    const existsResult = await checkOptimizationExists(optimizationId);

    if (existsResult.isSuccess && existsResult.data === true) {
      console.log("Optimization already exists in backend");
      return {
        isSuccess: true,
        message: "Optimization already exists in the backend",
        data: { status: "exists", message: "Campaign already active" }
      };
    } else if (existsResult.isSuccess && existsResult.data === false) {
      console.log("Optimization confirmed not to exist, proceeding with recreation");
    } else {
      console.warn("Could not determine optimization existence, attempting recreation anyway:", existsResult.message);
    }

    // Recreate the optimization in the backend with the exact same ID
    console.log("Recreating optimization in the backend with exact ID...");
    console.log("Optimization ID:", optimizationId);
    console.log("Config summary:", {
      parameters: (optimization.config as any)?.parameters?.length || 0,
      target_config: (optimization.config as any)?.target_config ? "present" : "missing",
      recommender_config: (optimization.config as any)?.recommender_config ? "present" : "missing"
    });

    // Use forceExactId=true to ensure we get the same optimizer ID back
    const createResult = await createOptimization(
      optimizationId,
      optimization.config as any,
      true // forceExactId = true
    );

    if (!createResult.isSuccess) {
      console.error("Failed to recreate optimization:", createResult.message);
      return {
        isSuccess: false,
        message: `Failed to recreate optimization: ${createResult.message}`
      };
    }

    console.log("Successfully recreated optimization in backend");

    // Verify the returned optimizer ID matches what we requested
    const returnedOptimizerId = createResult.data?.optimizer_id;
    if (returnedOptimizerId && returnedOptimizerId !== optimizationId) {
      console.warn(`Backend returned different ID: ${returnedOptimizerId} (requested: ${optimizationId})`);
      console.warn("This should not happen with forceExactId=true, but recreation was successful");
    } else {
      console.log(`Confirmed: Backend used exact optimizer ID: ${optimizationId}`);
    }

    // Now restore all existing measurements to the recreated optimization
    console.log("Restoring existing measurements to recreated optimization...");
    try {
      const { getMeasurementsAction } = await import("./db/optimizations-actions");
      const measurementsResult = await getMeasurementsAction(optimization.id);

      if (measurementsResult.isSuccess && measurementsResult.data && measurementsResult.data.length > 0) {
        console.log(`Found ${measurementsResult.data.length} existing measurements to restore`);

        // Import the addMeasurementAction function that supports both single and multi-target
        const { addMeasurementAction } = await import("../lib/api/baybe-client");

        // Get authentication token
        const { getToken } = await import("@clerk/nextjs/server").then(m => m.auth());
        const token = await getToken();

        // Restore each measurement
        let restoredCount = 0;
        for (const measurement of measurementsResult.data) {
          try {
            console.log(`Restoring measurement ${restoredCount + 1}/${measurementsResult.data.length}`);

            // Determine if this is single or multi-target
            let targetValue: number | Record<string, number>;
            if (measurement.targetValues) {
              // Multi-target case
              targetValue = measurement.targetValues as Record<string, number>;
            } else {
              // Single-target case
              targetValue = parseFloat(measurement.targetValue);
            }

            const restoreResult = await addMeasurementAction(
              optimizationId,
              measurement.parameters as Record<string, any>,
              targetValue,
              token ?? undefined
            );

            if (restoreResult.isSuccess) {
              restoredCount++;
            } else {
              console.warn(`Failed to restore measurement ${restoredCount + 1}:`, restoreResult.message);
            }
          } catch (error) {
            console.error(`Error restoring measurement ${restoredCount + 1}:`, error);
          }
        }

        console.log(`Successfully restored ${restoredCount}/${measurementsResult.data.length} measurements`);

        return {
          isSuccess: true,
          message: `Optimization campaign recreated successfully with ${restoredCount} measurements restored`,
          data: { status: "recreated", message: `Campaign is now active with ${restoredCount} measurements` }
        };
      } else {
        console.log("No existing measurements found to restore");
        return {
          isSuccess: true,
          message: "Optimization campaign recreated successfully (no measurements to restore)",
          data: { status: "recreated", message: "Campaign is now active and ready for suggestions" }
        };
      }
    } catch (error) {
      console.error("Error restoring measurements:", error);
      // Don't fail the recreation if measurement restoration fails
      return {
        isSuccess: true,
        message: "Optimization campaign recreated successfully (measurement restoration failed)",
        data: { status: "recreated", message: "Campaign is active but measurements may need to be re-added" }
      };
    }

  } catch (error) {
    console.error("Error recreating optimization:", error);
    return {
      isSuccess: false,
      message: `Failed to recreate optimization: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Loads an existing optimization with automatic recreation if needed
 */
export async function loadOptimizationWorkflowAction(
  optimizationId: string
): Promise<ActionState<any>> {
  const { userId } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to load an optimization"
    };
  }

  try {
    // Get the optimization from our database to verify access
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId);

    if (!optResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      };
    }

    // Check if optimization exists in the backend
    const existsResult = await checkOptimizationExists(optimizationId);

    // If the optimization doesn't exist in the backend, recreate it
    if (existsResult.isSuccess && existsResult.data === false) {
      console.log("Optimization doesn't exist in the backend. Recreating it...");

      const recreateResult = await recreateOptimizationWorkflowAction(optimizationId);
      if (!recreateResult.isSuccess) {
        return recreateResult;
      }
    }

    // Load the optimization from the API
    const apiResult = await loadOptimization(optimizationId);

    if (!apiResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Failed to load optimization: ${apiResult.message}`
      };
    }

    return {
      isSuccess: true,
      message: "Optimization loaded successfully",
      data: apiResult.data
    };

  } catch (error) {
    console.error("Error in workflow:", error);
    return {
      isSuccess: false,
      message: `Failed to load optimization: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Generate samples using Latin Hypercube Sampling or random sampling
 *
 * @deprecated Use the generateSamplesWorkflowAction from sample-workflow-actions.ts instead
 */
export async function generateSamplesWorkflowAction(
  optimizationId: string,
  numSamples: number = 10,
  samplingStrategy: string = "LHS",
  seed?: number,
  respectConstraintsOverride?: boolean
): Promise<ActionState<{ samples: any[] }>> {
  // Forward to the new implementation
  // Cast samplingStrategy to the correct type (SamplingMethod)
  const result = await import("./sample-workflow-actions").then(module =>
    module.generateSamplesWorkflowAction(optimizationId, numSamples, samplingStrategy as any, seed, respectConstraintsOverride)
  );

  // Convert the result format to maintain backward compatibility
  if (result.isSuccess && result.data) {
    return {
      isSuccess: true,
      message: result.message,
      data: {
        samples: result.data.samples
      }
    };
  }

  return {
    isSuccess: false,
    message: result.message
  };
}

export async function getBestPointWorkflowAction(
  optimizationId: string
): Promise<ActionState<{
  best_parameters?: Record<string, any>;
  best_value?: number;
  best_values?: Record<string, number>;
  composite_score?: number;
  normalized_values?: Record<string, number>;
  target_weights?: Record<string, number>;
}>> {
  const { userId } = await auth();

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to get the best point"
    };
  }

  try {
    // Get the optimization from our database to verify access
    const optResult = await getOptimizationByOptimizerIdAction(optimizationId);

    if (!optResult.isSuccess) {
      return {
        isSuccess: false,
        message: `Database Error: ${optResult.message}`
      };
    }

    // First, check if the optimization exists
    console.log(`Checking if optimization exists with ID: ${optimizationId}`);
    try {
      const existsResult = await checkOptimizationExists(optimizationId);
      if (!existsResult.isSuccess) {
        console.error(`Error checking if optimization exists: ${existsResult.message}`);
        return {
          isSuccess: false,
          message: `Error checking if optimization exists: ${existsResult.message}`
        };
      }

      if (!existsResult.data) {
        console.error(`Optimization does not exist: ${optimizationId}`);
        return {
          isSuccess: false,
          message: `Optimization does not exist: ${optimizationId}`
        };
      }

      console.log(`Optimization exists: ${optimizationId}`);
    } catch (error) {
      console.error(`Error checking if optimization exists: ${error}`);
      return {
        isSuccess: false,
        message: `Error checking if optimization exists: ${error instanceof Error ? error.message : String(error)}`
      };
    }

    // Note: We're skipping the load step because the /load endpoint doesn't exist in the backend API
    // For newly created optimizations, this is fine because they're already loaded in memory
    // For existing optimizations, we rely on the backend to load them when needed

    // Get the best point from the API
    console.log(`Getting best point for optimizer ID: ${optimizationId}`);
    try {
      const apiResult = await getBestPoint(optimizationId);

      if (!apiResult.isSuccess) {
        console.error(`API Error getting best point: ${apiResult.message}`);
        return {
          isSuccess: false,
          message: `API Error: ${apiResult.message}`
        };
      }

      // Check if we have data or just a message (no measurements yet)
      if (!apiResult.data || !apiResult.data.best_parameters) {
        console.log(`No best point available yet: ${apiResult.message || 'No measurements yet'}`);
        return {
          isSuccess: true,
          message: apiResult.message || "No best point available yet",
          data: {
            best_parameters: undefined,
            best_value: undefined,
            best_values: undefined,
            composite_score: undefined,
            normalized_values: undefined,
            target_weights: undefined
          }
        };
      }

      // Return the best point with all available data
      // Use optional chaining to safely access properties that might not exist
      return {
        isSuccess: true,
        message: "Got best point successfully",
        data: {
          best_parameters: apiResult.data.best_parameters,
          best_value: apiResult.data.best_value,
          // For multi-target optimizations, these fields might be present
          // Use undefined as fallback if they don't exist
          best_values: (apiResult.data as any).best_values,
          composite_score: (apiResult.data as any).composite_score,
          normalized_values: (apiResult.data as any).normalized_values,
          target_weights: (apiResult.data as any).target_weights
        }
      };
    } catch (error) {
      console.error(`Error getting best point from API: ${error}`);

      // Handle the specific Python error
      if (error instanceof Error && error.message.includes("'tuple' object has no attribute 'get'")) {
        console.log("Handling tuple error gracefully - this is likely a backend API issue with a new optimization");
        // Return a successful response with empty data
        return {
          isSuccess: true,
          message: "No best point available yet",
          data: {
            best_parameters: undefined,
            best_value: undefined,
            best_values: undefined,
            composite_score: undefined,
            normalized_values: undefined,
            target_weights: undefined
          }
        };
      }

      return {
        isSuccess: false,
        message: `Error getting best point: ${error instanceof Error ? error.message : String(error)}`
      };
    }

  } catch (error) {
    console.error("Error in workflow:", error);
    return {
      isSuccess: false,
      message: "Failed to get best point"
    };
  }
}