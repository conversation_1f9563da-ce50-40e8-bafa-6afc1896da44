"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useApiLoading } from "@/lib/hooks/use-api-loading"
import { fetchWithLoading } from "@/lib/api/loading-fetch"

/**
 * Example component showing how to use the loading state
 */
export function LoadingExample() {
  const [data, setData] = useState<any>(null)
  const { withLoading } = useApiLoading()

  // Method 1: Using the withLoading hook wrapper
  const handleFetchWithHook = withLoading(async () => {
    // Simulate API call with delay
    await new Promise(resolve => setTimeout(resolve, 2000))
    const result = { message: "Data fetched successfully with hook!" }
    setData(result)
    return result
  })

  // Method 2: Using the fetchWithLoading utility directly
  const handleFetchWithUtility = async () => {
    try {
      // This will automatically trigger the loading overlay
      const result = await fetchWithLoading("/api/example", "GET")
      setData(result)
    } catch (error) {
      console.error("Error fetching data:", error)
    }
  }

  return (
    <div className="space-y-4 p-4">
      <h2 className="text-xl font-bold">Loading State Example</h2>
      
      <div className="flex space-x-4">
        <Button onClick={handleFetchWithHook}>
          Fetch with Hook
        </Button>
        
        <Button onClick={handleFetchWithUtility}>
          Fetch with Utility
        </Button>
      </div>
      
      {data && (
        <div className="mt-4 rounded-md bg-gray-100 p-4">
          <pre>{JSON.stringify(data, null, 2)}</pre>
        </div>
      )}
    </div>
  )
}
