"use client"

import { motion } from "framer-motion"
import { CheckCircle, Shield, Award, Globe } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const complianceItems = [
  {
    icon: Globe,
    title: "GDPR Compliance",
    status: "Compliant",
    statusType: "success",
    description:
      "Full compliance with European General Data Protection Regulation",
    details: [
      "Data subject rights implementation",
      "Privacy by design principles",
      "Data processing transparency",
      "Consent management system"
    ]
  },
  {
    icon: Shield,
    title: "SOC 2 Type II",
    status: "In Progress",
    statusType: "warning",
    description: "Security, availability, and confidentiality controls audit",
    details: [
      "Security controls assessment",
      "Availability monitoring",
      "Processing integrity",
      "Confidentiality measures"
    ]
  },
  {
    icon: Award,
    title: "ISO 27001",
    status: "Planned",
    statusType: "info",
    description: "International standard for information security management",
    details: [
      "Information security management system",
      "Risk assessment and treatment",
      "Security controls implementation",
      "Continuous improvement process"
    ]
  },
  {
    icon: CheckCircle,
    title: "CCPA Compliance",
    status: "Compliant",
    statusType: "success",
    description: "California Consumer Privacy Act compliance",
    details: [
      "Consumer rights implementation",
      "Data disclosure transparency",
      "Opt-out mechanisms",
      "Data deletion capabilities"
    ]
  }
]

const getBadgeVariant = (type: string) => {
  switch (type) {
    case "success":
      return "default"
    case "warning":
      return "secondary"
    case "info":
      return "outline"
    default:
      return "outline"
  }
}

const getStatusColor = (type: string) => {
  switch (type) {
    case "success":
      return "text-green-600"
    case "warning":
      return "text-yellow-600"
    case "info":
      return "text-blue-600"
    default:
      return "text-gray-600"
  }
}

export function ComplianceBadges() {
  return (
    <section className="py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Security Certifications & Compliance
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            We maintain the highest standards of security and compliance to
            protect your data and meet regulatory requirements across different
            jurisdictions.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          {complianceItems.map((item, index) => (
            <motion.div
              key={item.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full transition-shadow hover:shadow-lg">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex size-12 items-center justify-center rounded-lg bg-blue-100">
                        <item.icon className="size-6 text-blue-600" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">{item.title}</CardTitle>
                        <Badge variant={getBadgeVariant(item.statusType)}>
                          {item.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <CardDescription className="text-gray-600">
                    {item.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {item.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start">
                        <CheckCircle
                          className={`mr-2 mt-0.5 size-4 shrink-0 ${getStatusColor(item.statusType)}`}
                        />
                        <span className="text-sm text-gray-700">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-12 text-center"
        >
          <Card className="bg-blue-50">
            <CardContent className="pt-6">
              <h3 className="mb-4 text-xl font-semibold text-gray-900">
                Continuous Compliance Monitoring
              </h3>
              <p className="mb-6 text-gray-600">
                We continuously monitor and improve our security posture to
                maintain compliance with evolving regulations and industry
                standards.
              </p>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-blue-600">
                    Quarterly
                  </div>
                  <div className="text-sm text-gray-600">Security Audits</div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-green-600">
                    Annual
                  </div>
                  <div className="text-sm text-gray-600">
                    Compliance Reviews
                  </div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-purple-600">
                    Real-time
                  </div>
                  <div className="text-sm text-gray-600">Threat Monitoring</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
