"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, AlertTriangle, Loader2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { getObjectiveType } from "@/lib/optimization-utils"

// Import centralized constraints
import { Constraint, Parameter as ConstraintParameter } from "@/lib/constraints"
import { CentralizedConstraintBuilder } from "@/components/constraints/centralized-constraint-builder"

// Import enhanced error handling
import { useErrorHandling } from "@/lib/errors/hooks"
import { ErrorFactory } from "@/lib/errors/factory"
import { ErrorCode } from "@/lib/errors/types"
import { ErrorList } from "@/components/errors/error-display"

// Import measurement filtering components
import {
  MeasurementImpactAnalysis,
  MeasurementFilteringPreview as MeasurementFilteringPreviewType
} from "@/components/measurements/measurement-impact-analysis"
import { MeasurementFilteringPreview } from "@/components/measurements/measurement-filtering-preview"
import { MeasurementFilteringService } from "@/lib/services/measurement-filtering-service"

// Import audit logging
import { useAuditLogging } from "@/lib/audit/hooks"
import { AuditChange } from "@/lib/audit/types"

// Import Phase 2 features
import { useIncrementalUpdates } from "@/lib/incremental-updates/hooks"
import { useVersioning } from "@/lib/versioning/hooks"
import { useRealTimeConstraintValidation } from "@/lib/constraints/hooks"
import { UpdateContext, UpdateResult } from "@/lib/incremental-updates/types"
import { ConfigurationVersion } from "@/lib/versioning/types"
import { RealTimeConstraintEditor } from "@/components/constraints/real-time-constraint-editor"

interface ConfigurationUpdateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  optimization: any
  onConfigurationUpdated: (result: any) => void
}

interface ParameterConfig {
  [paramName: string]: {
    type:
      | "NumericalContinuous"
      | "NumericalDiscrete"
      | "Categorical"
      | "CategoricalParameter"
    bounds?: [number, number]
    values?: (number | string)[]
    encoding?: string
    tolerance?: number
    // Additional configuration options
    name?: string
    rawValuesInput?: string // Store the raw input string to preserve user typing
    rawBoundsInput?: { [key: number]: string } // Store raw bound inputs for editing
  }
}

interface TargetConfig {
  [targetName: string]: {
    bounds?: [number, number]
    mode?: "MAX" | "MIN"
    weight?: number
    transformation?: "LINEAR" | "LOG" | "SQRT"
    type?: "Numerical" | "Categorical"
    name?: string
    rawBoundsInput?: { [key: number]: string } // Store raw bound inputs for editing
  }
}

// Removed old PreviewData interface - now using Phase 2 incremental update system

interface AcquisitionConfig {
  type:
    | "qExpectedImprovement"
    | "qProbabilityOfImprovement"
    | "qUpperConfidenceBound"
    | "qNoisyExpectedHypervolumeImprovement"
    | "qLogNoisyExpectedHypervolumeImprovement"
    | "qLogNParEGO"
  beta?: number
  ref_point?: number[]
  weights?: number[]
  rho?: number
  prune_baseline?: boolean
}

// Helper function to get available acquisition functions based on objective type
const getAvailableAcquisitionFunctions = (objectiveType: string) => {
  if (objectiveType === "SINGLE") {
    return [
      { value: "qExpectedImprovement", label: "Expected Improvement (qEI)" },
      {
        value: "qProbabilityOfImprovement",
        label: "Probability of Improvement (qPI)"
      },
      { value: "qUpperConfidenceBound", label: "Upper Confidence Bound (qUCB)" }
    ]
  } else if (objectiveType === "MULTI_DESIRABILITY") {
    return [
      { value: "qExpectedImprovement", label: "Expected Improvement (qEI)" },
      {
        value: "qProbabilityOfImprovement",
        label: "Probability of Improvement (qPI)"
      },
      { value: "qUpperConfidenceBound", label: "Upper Confidence Bound (qUCB)" }
    ]
  } else if (objectiveType === "MULTI_PARETO") {
    return [
      {
        value: "qNoisyExpectedHypervolumeImprovement",
        label: "Noisy Expected Hypervolume Improvement (qNEHVI)"
      },
      {
        value: "qLogNoisyExpectedHypervolumeImprovement",
        label: "Log Noisy Expected Hypervolume Improvement (qLogNEHVI)"
      },
      { value: "qLogNParEGO", label: "Log ParEGO (qLogParEGO)" }
    ]
  }
  // Default fallback
  return [
    { value: "qExpectedImprovement", label: "Expected Improvement (qEI)" },
    {
      value: "qProbabilityOfImprovement",
      label: "Probability of Improvement (qPI)"
    },
    { value: "qUpperConfidenceBound", label: "Upper Confidence Bound (qUCB)" }
  ]
}

export function ConfigurationUpdateDialog({
  open,
  onOpenChange,
  optimization,
  onConfigurationUpdated
}: ConfigurationUpdateDialogProps) {
  // Enhanced error handling
  const {
    errors,
    hasErrors,
    hasWarnings,
    addError,
    clearErrors,
    clearErrorsByField,
    handleBackendError,
    setErrorContext
  } = useErrorHandling()

  // Audit logging
  const {
    logConfigurationChange,
    logParameterChange,
    logTargetChange,
    logMeasurementFiltering,
    logValidationError,
    logUserAction
  } = useAuditLogging({
    optimizationId: optimization.optimizerId,
    userId: "current_user", // TODO: Get from auth context
    autoLog: true
  })

  // Phase 2: Incremental Updates
  const {
    isUpdating: isIncrementalUpdating,
    updateProgress,
    lastUpdateResult,
    updateStrategy,
    analyzeChanges,
    executeUpdate,
    previewUpdate,
    getUpdateRecommendations,
    getUpdateStatistics
  } = useIncrementalUpdates({
    optimizationId: optimization.optimizerId,
    onUpdateStart: () => {
      logUserAction(
        "Incremental update started",
        "Started applying configuration changes incrementally"
      )
    },
    onUpdateComplete: (result: UpdateResult) => {
      logConfigurationChange(
        "update",
        [],
        {},
        {
          reason: `Incremental update completed - Applied ${result.operationsApplied.length} operations successfully`,
          triggeredBy: "user"
        }
      )
    },
    onUpdateError: (error: Error) => {
      addError(
        ErrorFactory.createSystemError(ErrorCode.SYSTEM_SAVE_FAILED, {
          message: error.message,
          optimizationId: optimization.optimizerId
        })
      )
    }
  })

  // Phase 2: Configuration Versioning
  const {
    versions,
    activeVersion,
    conflicts,
    currentLock,
    isLoading: isVersionLoading,
    createVersion,
    rollbackToVersion,
    compareVersions,
    lockVersion,
    releaseLock,
    resolveConflict,
    getPendingConflicts,
    hasUnsavedChanges,
    getVersionStatistics
  } = useVersioning({
    optimizationId: optimization.optimizerId,
    userId: "current_user", // TODO: Get from auth context
    userName: "Current User", // TODO: Get from auth context
    autoSave: false, // Manual save for now
    conflictDetection: true
  })

  // Phase 2: Real-time Constraint Validation
  const {
    validationResults: constraintValidationResults,
    isValidating: isConstraintValidating,
    validateWithDebounce: validateConstraintWithDebounce,
    validateOnBlur: validateConstraintOnBlur,
    generateAutoComplete,
    getValidationResult: getConstraintValidationResult
  } = useRealTimeConstraintValidation({
    enabled: true,
    debounceMs: 300,
    validateOnType: true,
    validateOnBlur: true,
    showInlineErrors: true,
    showSuggestions: true,
    enableFeasibilityCheck: true,
    feasibilitySampleSize: 100,
    enableInteractionDetection: true,
    maxValidationTime: 5000
  })

  const [parameterConfigs, setParameterConfigs] = useState<ParameterConfig>({})
  const [parameterOrder, setParameterOrder] = useState<string[]>([])
  const [targetConfigs, setTargetConfigs] = useState<TargetConfig>({})
  const [acquisitionConfig, setAcquisitionConfig] = useState<AcquisitionConfig>(
    {
      type: "qExpectedImprovement"
    }
  )
  const [objectiveType, setObjectiveType] = useState<
    "SINGLE" | "MULTI_DESIRABILITY" | "MULTI_PARETO"
  >("SINGLE")
  const [isLoading, setIsLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false) // Keep for UI state, but use Phase 2 logic
  const [constraints, setConstraints] = useState<Constraint[]>([])
  const [showConstraints, setShowConstraints] = useState(false)
  const [measurementPreview, setMeasurementPreview] =
    useState<MeasurementFilteringPreviewType | null>(null)
  const [showDetailedMeasurementAnalysis, setShowDetailedMeasurementAnalysis] =
    useState(false)

  // Phase 2 state
  const [showIncrementalPreview, setShowIncrementalPreview] = useState(false)
  const [showVersionHistory, setShowVersionHistory] = useState(false)
  const [showConflictResolution, setShowConflictResolution] = useState(false)
  const [currentUpdatePreview, setCurrentUpdatePreview] = useState<any>(null)

  // Set error context when dialog opens
  useEffect(() => {
    if (open && optimization) {
      setErrorContext({
        optimizationId: optimization.optimizerId,
        operation: "configuration_update",
        entityType: "optimization"
      })
      clearErrors() // Clear any previous errors
    }
  }, [open, optimization, setErrorContext, clearErrors])

  // Initialize bounds from optimization config
  useEffect(() => {
    if (open && optimization) {
      console.log(
        "ConfigurationUpdateDialog: Dialog opened, fetching latest configuration..."
      )
      console.log(
        "ConfigurationUpdateDialog: Current optimization:",
        optimization.optimizerId
      )
      // Fetch the latest configuration from the backend to ensure we have the most up-to-date data
      fetchLatestConfiguration()
    }
  }, [open, optimization])

  const fetchLatestConfiguration = async () => {
    try {
      setIsLoading(true)
      console.log(
        "ConfigurationUpdateDialog: Using optimization config directly (no backend fetch needed)",
        optimization.optimizerId
      )

      // 🔧 FIX: Check if we need to fetch the latest configuration from backend
      // to ensure we have the most up-to-date constraints and parameter values
      let config = optimization.config

      // If the config seems incomplete (missing constraints that should be there),
      // try to fetch the latest from backend
      if (!config.constraints || config.constraints.length === 0) {
        console.log(
          "🔧 CONFIG FIX: Configuration missing constraints, fetching from backend"
        )
        try {
          const response = await fetch(
            `/api/optimizations/${optimization.optimizerId}`
          )
          if (response.ok) {
            const backendData = await response.json()
            if (backendData.config && backendData.config.constraints) {
              console.log(
                "🔧 CONFIG FIX: Retrieved constraints from backend:",
                backendData.config.constraints
              )
              config = {
                ...config,
                constraints: backendData.config.constraints
              }
            }
          }
        } catch (fetchError) {
          console.warn(
            "🔧 CONFIG FIX: Failed to fetch from backend, using existing config:",
            fetchError
          )
        }
      }

      console.log("ConfigurationUpdateDialog: Using configuration:", config)
      initializeConfiguration(config)
    } catch (error) {
      console.error(
        "ConfigurationUpdateDialog: Error initializing configuration:",
        error
      )
      // Fall back to using the optimization config
      console.log(
        "ConfigurationUpdateDialog: Falling back to optimization.config"
      )
      initializeConfiguration(optimization.config)
    } finally {
      setIsLoading(false)
    }
  }

  const initializeConfiguration = (config: any) => {
    console.log(
      "ConfigurationUpdateDialog: Initializing configuration with:",
      config
    )

    // Initialize parameter configurations
    const paramConfigs: ParameterConfig = {}
    let paramOrder: string[] = []

    // Use stored parameter order if available, otherwise derive from parameters array
    if (config.parameter_order && Array.isArray(config.parameter_order)) {
      paramOrder = [...config.parameter_order]
      console.log(
        "ConfigurationUpdateDialog: Using stored parameter order:",
        paramOrder
      )
    }

    if (config.parameters && Array.isArray(config.parameters)) {
      console.log(
        "ConfigurationUpdateDialog: Processing parameters:",
        config.parameters
      )

      // If no stored order, derive from parameters array
      if (paramOrder.length === 0) {
        paramOrder = config.parameters.map((param: any) => param.name)
        console.log(
          "ConfigurationUpdateDialog: Derived parameter order from array:",
          paramOrder
        )
      }

      config.parameters.forEach((param: any) => {
        console.log(
          `ConfigurationUpdateDialog: Processing parameter ${param.name}:`,
          param
        )

        // Skip parameters that don't have required properties
        if (!param.name || !param.type) {
          console.warn(
            `ConfigurationUpdateDialog: Skipping parameter with missing name or type:`,
            param
          )
          return
        }

        console.log(
          `ConfigurationUpdateDialog: Parameter ${param.name} has type: ${param.type}`
        )

        paramConfigs[param.name] = {
          type: param.type,
          encoding: param.encoding,
          tolerance: param.tolerance
        }

        if (param.type === "NumericalContinuous" && param.bounds) {
          // Handle different bounds formats
          if (Array.isArray(param.bounds) && param.bounds.length === 2) {
            paramConfigs[param.name].bounds = [param.bounds[0], param.bounds[1]]
          } else if (typeof param.bounds === "string") {
            // Handle string format like "0, 100"
            const parts = param.bounds
              .split(",")
              .map((s: string) => parseFloat(s.trim()))
            if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
              paramConfigs[param.name].bounds = [parts[0], parts[1]]
            }
          }
        } else if (
          (param.type === "NumericalDiscrete" ||
            param.type === "Categorical" ||
            param.type === "CategoricalParameter") &&
          param.values
        ) {
          // Handle values for discrete and categorical parameters
          if (Array.isArray(param.values)) {
            paramConfigs[param.name].values = [...param.values]
            // Initialize rawValuesInput with the current values
            paramConfigs[param.name].rawValuesInput = param.values.join(", ")
          } else if (typeof param.values === "string") {
            // Handle string format like "1, 2, 3" or "option1, option2, option3"
            const parts = param.values.split(",").map((s: string) => s.trim())
            if (param.type === "NumericalDiscrete") {
              const numValues = parts
                .map((p: string) => parseFloat(p))
                .filter((n: number) => !isNaN(n))
              if (numValues.length > 0) {
                paramConfigs[param.name].values = numValues
                paramConfigs[param.name].rawValuesInput = numValues.join(", ")
              }
            } else {
              const filteredValues = parts.filter((p: string) => p.length > 0)
              paramConfigs[param.name].values = filteredValues
              paramConfigs[param.name].rawValuesInput =
                filteredValues.join(", ")
            }
          } else if (param.values != null) {
            // Handle other formats (tuples, objects, etc.) by converting to array
            try {
              // Try to convert to array if it's iterable
              const valuesArray = Array.from(param.values as any) as (
                | string
                | number
              )[]
              paramConfigs[param.name].values = valuesArray
              paramConfigs[param.name].rawValuesInput = valuesArray.join(", ")
              console.log(
                `Converted parameter ${param.name} values from ${typeof param.values} to array:`,
                valuesArray
              )
            } catch (error) {
              console.warn(
                `Could not convert parameter ${param.name} values to array:`,
                param.values,
                error
              )
              // Fallback: wrap single value in array
              const fallbackValues = [param.values as string | number]
              paramConfigs[param.name].values = fallbackValues
              paramConfigs[param.name].rawValuesInput =
                fallbackValues.join(", ")
            }
          }
        }
      })
    }
    console.log(
      "ConfigurationUpdateDialog: Final parameter configs:",
      paramConfigs
    )
    console.log("ConfigurationUpdateDialog: Parameter order:", paramOrder)
    console.log(
      "ConfigurationUpdateDialog: Number of parameters configured:",
      Object.keys(paramConfigs).length
    )
    setParameterConfigs(paramConfigs)
    setParameterOrder(paramOrder)

    // Initialize target configurations
    const targConfigs: TargetConfig = {}
    const targetConfig = config.target_config

    if (Array.isArray(targetConfig)) {
      // Multi-target case
      targetConfig.forEach((target: any) => {
        targConfigs[target.name] = {
          name: target.name,
          mode: target.mode || "MAX",
          weight: target.weight || 1.0,
          transformation: target.transformation || "LINEAR",
          type: target.type || "Numerical"
        }

        if (target.bounds) {
          if (Array.isArray(target.bounds) && target.bounds.length === 2) {
            targConfigs[target.name].bounds = [
              target.bounds[0],
              target.bounds[1]
            ]
          } else if (
            typeof target.bounds === "object" &&
            target.bounds.lower !== undefined &&
            target.bounds.upper !== undefined
          ) {
            targConfigs[target.name].bounds = [
              target.bounds.lower,
              target.bounds.upper
            ]
          }
        }
      })
    } else if (targetConfig) {
      // Single target case
      const targetName =
        targetConfig.name || optimization.targetName || "Target"
      targConfigs[targetName] = {
        name: targetName,
        mode: targetConfig.mode || "MAX",
        weight: targetConfig.weight || 1.0,
        transformation: targetConfig.transformation || "LINEAR",
        type: targetConfig.type || "Numerical"
      }

      if (targetConfig.bounds) {
        if (
          Array.isArray(targetConfig.bounds) &&
          targetConfig.bounds.length === 2
        ) {
          targConfigs[targetName].bounds = [
            targetConfig.bounds[0],
            targetConfig.bounds[1]
          ]
          // Initialize rawBoundsInput with the current bounds
          targConfigs[targetName].rawBoundsInput = {
            0: targetConfig.bounds[0]?.toString() ?? "",
            1: targetConfig.bounds[1]?.toString() ?? ""
          }
        } else if (
          typeof targetConfig.bounds === "object" &&
          targetConfig.bounds.lower !== undefined &&
          targetConfig.bounds.upper !== undefined
        ) {
          targConfigs[targetName].bounds = [
            targetConfig.bounds.lower,
            targetConfig.bounds.upper
          ]
          // Initialize rawBoundsInput with the current bounds
          targConfigs[targetName].rawBoundsInput = {
            0: targetConfig.bounds.lower?.toString() ?? "",
            1: targetConfig.bounds.upper?.toString() ?? ""
          }
        }
      }
    }
    console.log("🔧 Config Dialog - Setting target configs:", targConfigs)
    setTargetConfigs(targConfigs)

    // Determine objective type from optimization configuration
    console.log("🔧 Config Dialog - Full optimization object:", optimization)
    console.log("🔧 Config Dialog - Optimization config:", config)
    console.log(
      "🔧 Config Dialog - About to call getObjectiveType with optimization:",
      {
        id: optimization.id,
        name: optimization.name,
        targetMode: optimization.targetMode,
        config: optimization.config
      }
    )
    const detectedObjectiveType = getObjectiveType(optimization)
    setObjectiveType(detectedObjectiveType)
    console.log(
      "🔧 Config Dialog - Detected objective type:",
      detectedObjectiveType
    )
    console.log(
      "🔧 Config Dialog - Setting objective type state to:",
      detectedObjectiveType
    )

    // Initialize acquisition function configuration
    const acqConfig = config.acquisition_config
    if (acqConfig && typeof acqConfig === "object") {
      // Validate that the current acquisition function is valid for the objective type
      const availableFunctions = getAvailableAcquisitionFunctions(
        detectedObjectiveType
      )
      const isValidFunction = availableFunctions.some(
        func => func.value === acqConfig.type
      )

      setAcquisitionConfig({
        type: isValidFunction
          ? acqConfig.type
          : (availableFunctions[0]?.value as AcquisitionConfig["type"]) ||
            "qExpectedImprovement",
        beta: acqConfig.beta,
        ref_point: acqConfig.ref_point,
        weights: acqConfig.weights,
        rho: acqConfig.rho,
        prune_baseline: acqConfig.prune_baseline
      })

      if (!isValidFunction) {
        console.warn(
          `🔧 Config Dialog - Acquisition function ${acqConfig.type} is not valid for objective type ${detectedObjectiveType}. Defaulting to ${availableFunctions[0]?.value}`
        )
      }
    } else {
      // Default configuration based on objective type
      const availableFunctions = getAvailableAcquisitionFunctions(
        detectedObjectiveType
      )
      setAcquisitionConfig({
        type:
          (availableFunctions[0]?.value as AcquisitionConfig["type"]) ||
          "qExpectedImprovement"
      })
    }

    // Initialize constraints
    const configConstraints = config.constraints || []
    console.log("🔧 Config Dialog - Loading constraints:", configConstraints)

    // Convert constraints to the centralized format if needed
    const formattedConstraints: Constraint[] = configConstraints.map(
      (constraint: any) => ({
        id:
          constraint.id ||
          `constraint_${Math.random().toString(36).substr(2, 9)}`,
        type: constraint.type,
        parameters: constraint.parameters || [],
        name: constraint.name || constraint.type,
        description: constraint.description,
        // Add constraint-specific fields
        ...(constraint.type === "ContinuousLinearConstraint" && {
          operator: constraint.operator,
          coefficients: constraint.coefficients,
          rhs: constraint.rhs
        }),
        ...(constraint.type.includes("CardinalityConstraint") && {
          min_cardinality: constraint.min_cardinality,
          max_cardinality: constraint.max_cardinality,
          relative_threshold: constraint.relative_threshold
        }),
        ...(constraint.type === "DiscreteExcludeConstraint" && {
          combiner: constraint.combiner,
          conditions: constraint.conditions
        }),
        ...((constraint.type === "DiscreteSumConstraint" ||
          constraint.type === "DiscreteProductConstraint") && {
          condition: constraint.condition
        }),
        ...(constraint.type === "DiscreteDependenciesConstraint" && {
          affected_parameters: constraint.affected_parameters
        }),
        ...(constraint.type === "DiscreteCustomConstraint" && {
          validator: constraint.validator
        })
      })
    )

    setConstraints(formattedConstraints)
    setShowConstraints(formattedConstraints.length > 0)

    // Reset preview state (Phase 2)
    setCurrentUpdatePreview(null)
    setShowPreview(false)
    setShowIncrementalPreview(false)
  }

  const handleParameterBoundChange = (
    paramName: string,
    index: 0 | 1,
    value: string
  ) => {
    console.log("🔧 Config Dialog - Parameter bound change:", {
      paramName,
      index,
      value
    })

    setParameterConfigs(prev => {
      const param = prev[paramName]
      if (!param) return prev

      const oldBounds = param.bounds || [0, 0]
      const newBounds = [...oldBounds] as [number, number]

      // Allow empty string for editing, but convert to 0 for storage
      if (value === "") {
        newBounds[index] = 0
      } else {
        const numValue = parseFloat(value)
        if (!isNaN(numValue)) {
          newBounds[index] = numValue
        } else {
          // For invalid input, keep the previous value but allow the input to show
          return {
            ...prev,
            [paramName]: {
              ...param,
              rawBoundsInput: {
                ...param.rawBoundsInput,
                [index]: value
              }
            }
          }
        }
      }

      // Log the parameter bounds change
      if (JSON.stringify(oldBounds) !== JSON.stringify(newBounds)) {
        logParameterChange(paramName, "bounds", oldBounds, newBounds).catch(
          error => {
            console.warn("Failed to log parameter bounds change:", error)
          }
        )
      }

      console.log("🔧 Config Dialog - New parameter bounds:", newBounds)
      return {
        ...prev,
        [paramName]: {
          ...param,
          bounds: newBounds,
          rawBoundsInput: {
            ...param.rawBoundsInput,
            [index]: value
          }
        }
      }
    })
  }

  const handleParameterValuesChange = (
    paramName: string,
    valuesString: string
  ) => {
    console.log(
      "🔧 Config Dialog - Values input changed for",
      paramName,
      ":",
      valuesString
    )
    setParameterConfigs(prev => {
      const param = prev[paramName]
      if (!param) return prev

      let values: (number | string)[] = []

      if (param.type === "NumericalDiscrete") {
        // Parse as numbers, but only for non-empty parts
        values = valuesString
          .split(",")
          .map(s => s.trim())
          .filter(s => s.length > 0) // Keep empty strings for trailing commas
          .map(s => parseFloat(s))
          .filter(n => !isNaN(n))
        console.log(
          "🔧 Config Dialog - Parsed numerical values for",
          paramName,
          ":",
          values
        )
      } else if (param.type === "CategoricalParameter") {
        // Parse as strings, but only for non-empty parts
        values = valuesString
          .split(",")
          .map(s => s.trim())
          .filter(s => s.length > 0) // Keep empty strings for trailing commas
        console.log(
          "🔧 Config Dialog - Parsed categorical values for",
          paramName,
          ":",
          values
        )
      }

      return {
        ...prev,
        [paramName]: {
          ...param,
          rawValuesInput: valuesString, // Store the raw input
          values
        }
      }
    })
  }

  const handleParameterEncodingChange = (
    paramName: string,
    encoding: string
  ) => {
    setParameterConfigs(prev => ({
      ...prev,
      [paramName]: {
        ...prev[paramName],
        encoding
      }
    }))
  }

  const handleTargetBoundChange = (
    targetName: string,
    index: 0 | 1,
    value: string
  ) => {
    console.log("🔧 Config Dialog - Target bound change START:", {
      targetName,
      index,
      value,
      valueType: typeof value
    })

    setTargetConfigs(prev => {
      const target = prev[targetName]
      console.log("🔧 Config Dialog - Current target config:", target)

      if (!target) {
        console.log("🔧 Config Dialog - No target found, returning prev")
        return prev
      }

      const currentBounds = target.bounds || [0, 0]
      const newBounds = [...currentBounds] as [number, number]
      console.log(
        "🔧 Config Dialog - Current bounds:",
        currentBounds,
        "New bounds (before):",
        newBounds
      )

      // Always update rawBoundsInput to preserve user input
      const newRawBoundsInput = {
        ...target.rawBoundsInput,
        [index]: value
      }
      console.log("🔧 Config Dialog - New rawBoundsInput:", newRawBoundsInput)

      // Handle the actual bound value
      if (value === "") {
        newBounds[index] = 0
        console.log("🔧 Config Dialog - Empty input, setting bound to 0")
      } else {
        const numValue = parseFloat(value)
        console.log(
          "🔧 Config Dialog - Parsed value:",
          numValue,
          "isNaN:",
          isNaN(numValue)
        )

        if (!isNaN(numValue)) {
          newBounds[index] = numValue
          console.log(
            "🔧 Config Dialog - Valid number, setting bound to:",
            numValue
          )
        } else {
          console.log(
            "🔧 Config Dialog - Invalid number, keeping previous bound:",
            newBounds[index]
          )
        }
      }

      // Log the target bounds change
      if (JSON.stringify(currentBounds) !== JSON.stringify(newBounds)) {
        logTargetChange(targetName, "bounds", currentBounds, newBounds).catch(
          error => {
            console.warn("Failed to log target bounds change:", error)
          }
        )
      }

      const result = {
        ...prev,
        [targetName]: {
          ...target,
          bounds: newBounds,
          rawBoundsInput: newRawBoundsInput
        }
      }

      console.log(
        "🔧 Config Dialog - Final result for",
        targetName,
        ":",
        result[targetName]
      )
      return result
    })
  }

  const handleTargetModeChange = (targetName: string, mode: "MAX" | "MIN") => {
    setTargetConfigs(prev => ({
      ...prev,
      [targetName]: {
        ...prev[targetName],
        mode
      }
    }))
  }

  /**
   * Recalculates and redistributes weights when one target weight is changed
   * Ensures all weights sum to 1.0 by proportionally adjusting other weights
   */
  const recalculateWeights = (
    currentConfigs: Record<string, any>,
    changedTargetName: string,
    newWeight: number
  ): Record<string, any> => {
    const targetNames = Object.keys(currentConfigs)
    const totalTargets = targetNames.length

    // If only one target, weight should be 1.0
    if (totalTargets === 1) {
      return {
        ...currentConfigs,
        [changedTargetName]: {
          ...currentConfigs[changedTargetName],
          weight: 1.0
        }
      }
    }

    // Clamp the new weight between 0.01 and 0.99 to ensure other targets can have meaningful weights
    const clampedNewWeight = Math.max(0.01, Math.min(0.99, newWeight))

    // Calculate remaining weight to distribute among other targets
    const remainingWeight = 1.0 - clampedNewWeight
    const otherTargetNames = targetNames.filter(
      name => name !== changedTargetName
    )

    // Get current weights of other targets for proportional redistribution
    const otherCurrentWeights = otherTargetNames.map(
      name => currentConfigs[name]?.weight || 1.0 / totalTargets
    )
    const otherWeightsSum = otherCurrentWeights.reduce((sum, w) => sum + w, 0)

    // Create updated configs
    const updatedConfigs = { ...currentConfigs }

    // Update the changed target
    updatedConfigs[changedTargetName] = {
      ...currentConfigs[changedTargetName],
      weight: clampedNewWeight
    }

    // Redistribute remaining weight proportionally among other targets
    if (otherWeightsSum > 0) {
      otherTargetNames.forEach((name, index) => {
        const currentWeight = otherCurrentWeights[index]
        const proportion = currentWeight / otherWeightsSum
        const newWeight = remainingWeight * proportion

        updatedConfigs[name] = {
          ...currentConfigs[name],
          weight: Math.max(0.01, newWeight) // Ensure minimum weight
        }
      })
    } else {
      // If other weights sum to 0, distribute equally
      const equalWeight = remainingWeight / otherTargetNames.length
      otherTargetNames.forEach(name => {
        updatedConfigs[name] = {
          ...currentConfigs[name],
          weight: equalWeight
        }
      })
    }

    return updatedConfigs
  }

  const handleTargetWeightChange = (targetName: string, weight: string) => {
    console.log(
      `🔧 WEIGHT CHANGE: User changed ${targetName} weight to ${weight}%`
    )
    const numValue = parseFloat(weight)
    // Accept percentage values (1-100) and convert to decimal (0.01-1.0) for internal storage
    if (!isNaN(numValue) && numValue >= 1 && numValue <= 100) {
      const decimalWeight = numValue / 100 // Convert percentage to decimal
      console.log(
        `🔧 WEIGHT CHANGE: Converting ${weight}% to decimal ${decimalWeight}`
      )
      const newConfigs = recalculateWeights(
        targetConfigs,
        targetName,
        decimalWeight
      )
      console.log(
        `🔧 WEIGHT CHANGE: After redistribution:`,
        Object.entries(newConfigs).map(([name, config]) => ({
          name,
          weight: config.weight,
          percentage: Math.round((config.weight || 0) * 100)
        }))
      )
      setTargetConfigs(newConfigs)
    } else {
      console.log(`🔧 WEIGHT CHANGE: Invalid weight value ${weight}, ignoring`)
    }
  }

  const handleTargetTransformationChange = (
    targetName: string,
    transformation: "LINEAR" | "LOG" | "SQRT"
  ) => {
    setTargetConfigs(prev => ({
      ...prev,
      [targetName]: {
        ...prev[targetName],
        transformation
      }
    }))
  }

  const validateConfigs = (): boolean => {
    clearErrors() // Clear previous validation errors

    // Validate parameter configurations
    for (const [paramName, config] of Object.entries(parameterConfigs)) {
      if (config.type === "NumericalContinuous" && config.bounds) {
        if (config.bounds[0] >= config.bounds[1]) {
          const error = ErrorFactory.createParameterError(
            ErrorCode.PARAMETER_BOUNDS_INVALID,
            paramName,
            {
              lowerBound: config.bounds[0],
              upperBound: config.bounds[1]
            }
          )
          addError(error)

          // Log validation error
          logValidationError(
            error.message,
            `parameter.${paramName}.bounds`
          ).catch(err => {
            console.warn("Failed to log validation error:", err)
          })
        }
      } else if (
        (config.type === "NumericalDiscrete" ||
          config.type === "CategoricalParameter") &&
        config.values
      ) {
        if (config.values.length === 0) {
          const error = ErrorFactory.createParameterError(
            ErrorCode.PARAMETER_VALUES_EMPTY,
            paramName,
            { type: config.type }
          )
          addError(error)
        }
        if (config.type === "NumericalDiscrete") {
          // Check for duplicate values
          const uniqueValues = new Set(config.values)
          if (uniqueValues.size !== config.values.length) {
            const error = ErrorFactory.createParameterError(
              ErrorCode.PARAMETER_VALUES_DUPLICATE,
              paramName,
              { values: config.values }
            )
            addError(error)
          }
        }
      }
    }

    // Validate target configurations
    for (const [targetName, config] of Object.entries(targetConfigs)) {
      if (config.bounds && config.bounds[0] >= config.bounds[1]) {
        const error = ErrorFactory.createTargetError(
          ErrorCode.TARGET_BOUNDS_INVALID,
          targetName,
          {
            lowerBound: config.bounds[0],
            upperBound: config.bounds[1]
          }
        )
        addError(error)
      }

      if (
        config.weight !== undefined &&
        (config.weight < 0 || config.weight > 1)
      ) {
        const error = ErrorFactory.createTargetError(
          ErrorCode.TARGET_WEIGHT_INVALID,
          targetName,
          { weight: config.weight }
        )
        addError(error)
      }
    }

    // Validate weight sum for multi-target configurations
    const targetNames = Object.keys(targetConfigs)
    if (targetNames.length > 1) {
      const weights = targetNames.map(name => targetConfigs[name]?.weight || 0)
      const weightSum = weights.reduce((sum, w) => sum + w, 0)

      // Allow small floating point tolerance
      if (Math.abs(weightSum - 1.0) > 0.001) {
        const error = ErrorFactory.createValidationError(
          ErrorCode.VALIDATION_OUT_OF_RANGE,
          "target_weights",
          weightSum,
          1.0,
          {
            optimizationId: optimization.optimizerId,
            operation: "weight_validation",
            additionalData: {
              message: `Multi-target weights sum to ${weightSum.toFixed(3)}, but must sum to 1.0. Weights will be automatically normalized.`
            }
          }
        )
        addError(error)
      }
    }

    return !hasErrors // Return true if no errors, false if there are errors
  }

  // Phase 2: Incremental update preview with measurement filtering
  const handleIncrementalPreview = async () => {
    console.log(
      "🎯 FRONTEND TARGETS CRUD: === INCREMENTAL PREVIEW GENERATION ==="
    )
    console.log(
      "🎯 FRONTEND TARGETS CRUD: Starting incremental preview for optimization:",
      optimization.optimizerId
    )

    try {
      // Build current and target configurations
      const currentConfig = optimization.config
      const targetConfig = buildTargetConfiguration()

      console.log("🎯 FRONTEND TARGETS CRUD: === CONFIGURATION COMPARISON ===")
      console.log("🎯 FRONTEND TARGETS CRUD: Current Config summary:", {
        objective_type: currentConfig?.objective_type,
        parameters_count: currentConfig?.parameters?.length || 0,
        targets_count: Array.isArray(currentConfig?.target_config)
          ? currentConfig.target_config.length
          : 1,
        target_config_type: typeof currentConfig?.target_config
      })
      console.log("🎯 FRONTEND TARGETS CRUD: Target Config summary:", {
        parameters_count: targetConfig?.parameters?.length || 0,
        targets_count: Array.isArray(targetConfig?.target_config)
          ? targetConfig.target_config.length
          : 1,
        target_config_type: typeof targetConfig?.target_config,
        has_constraints: targetConfig?.constraints?.length > 0
      })
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Total Measurements:",
        optimization.measurements?.length || 0
      )

      // Log detailed target configuration changes
      if (targetConfig?.target_config) {
        console.log(
          "🎯 FRONTEND TARGETS CRUD: === TARGET CONFIGURATION DETAILS ==="
        )
        if (Array.isArray(targetConfig.target_config)) {
          targetConfig.target_config.forEach((target: any, index: number) => {
            console.log(`🎯 FRONTEND TARGETS CRUD: Target ${index + 1}:`, {
              name: target.name,
              mode: target.mode,
              weight: target.weight,
              transformation: target.transformation,
              bounds: target.bounds,
              type: target.type
            })
          })
        } else {
          console.log(
            "🎯 FRONTEND TARGETS CRUD: Single target config:",
            targetConfig.target_config
          )
        }
      }

      // Create update context
      const updateContext: UpdateContext = {
        optimizationId: optimization.optimizerId,
        currentConfiguration: optimization.config,
        targetConfiguration: buildTargetConfiguration(),
        measurementCount: optimization.measurements?.length || 0,
        modelComplexity: "medium",
        lastUpdateTimestamp: new Date(),
        userPreferences: {
          prioritizeSpeed: true,
          allowDataLoss: false,
          maxAcceptableDelay: 30000
        }
      }

      // Generate incremental update preview
      console.log("⚙️ PREVIEW DEBUG - Analyzing configuration changes...")
      const preview = await previewUpdate(
        currentConfig,
        targetConfig,
        updateContext
      )
      console.log("📋 Update Strategy:", preview.strategy)
      console.log("✅ Can Optimize:", preview.canOptimize)
      console.log("⏱️ Estimated Duration:", preview.estimatedDuration, "ms")

      // Generate measurement filtering preview using the actual backend
      console.log(
        "🔬 PREVIEW DEBUG - Generating measurement filtering preview..."
      )
      try {
        const filteringPreview =
          await MeasurementFilteringService.generateFilteringPreview(
            optimization.optimizerId,
            {
              parameterBounds: targetConfig.parameter_bounds || {},
              targetBounds: targetConfig.target_bounds || {},
              updatedParameters: targetConfig.parameters || [],
              constraints: targetConfig.constraints || []
            }
          )

        console.log("📊 MEASUREMENT FILTERING RESULTS:")
        console.log(
          "  - Total Measurements:",
          filteringPreview.totalMeasurements
        )
        console.log(
          "  - Retained Measurements:",
          filteringPreview.retainedMeasurements
        )
        console.log(
          "  - Filtered Measurements:",
          filteringPreview.filteredMeasurements.length
        )
        console.log(
          "  - Retention Percentage:",
          filteringPreview.filteringSummary.retentionPercentage + "%"
        )
        console.log(
          "  - Data Quality Impact:",
          filteringPreview.impactAnalysis.dataQualityImpact
        )
        console.log(
          "  - Optimization Impact:",
          filteringPreview.impactAnalysis.optimizationImpact
        )

        // Log detailed information about filtered measurements
        if (filteringPreview.filteredMeasurements.length > 0) {
          console.log("🚫 FILTERED MEASUREMENTS DETAILS:")
          filteringPreview.filteredMeasurements.forEach(
            (measurement, index) => {
              console.log(`  ${index + 1}. Measurement ${measurement.id}:`)
              console.log(`     - Parameters:`, measurement.parameters)
              console.log(`     - Target Values:`, measurement.targetValues)
              console.log(
                `     - Violations:`,
                measurement.violationReasons.map(
                  v => `${v.field}: ${v.message}`
                )
              )
            }
          )
        }

        setMeasurementPreview(filteringPreview)

        // Log measurement filtering preview
        logMeasurementFiltering(
          filteringPreview.totalMeasurements,
          filteringPreview.retainedMeasurements,
          filteringPreview.filteredMeasurements.length
        ).catch(error => {
          console.warn("Failed to log measurement filtering:", error)
        })
      } catch (error) {
        console.error(
          "❌ PREVIEW DEBUG - Failed to generate measurement filtering preview:",
          error
        )
        // Continue with incremental preview even if measurement preview fails
      }

      setCurrentUpdatePreview(preview)
      setShowIncrementalPreview(true)
      setShowPreview(true)

      // Log the preview action
      logUserAction(
        "Configuration preview generated",
        `Preview generated with ${preview.strategy.simpleUpdates.length} simple, ${preview.strategy.moderateUpdates.length} moderate, ${preview.strategy.complexUpdates.length} complex updates`
      )

      toast({
        title: "Preview Generated",
        description: `Update strategy: ${preview.canOptimize ? "Incremental" : "Full Recreation"} (${preview.estimatedDuration}ms estimated)`,
        variant: "default"
      })
    } catch (error) {
      console.error("❌ PREVIEW DEBUG - Incremental preview failed:", error)
      addError(
        ErrorFactory.createSystemError(ErrorCode.SYSTEM_LOAD_FAILED, {
          message:
            error instanceof Error
              ? error.message
              : "Failed to generate preview"
        })
      )
    }
  }

  // Helper function to build target configuration
  const buildTargetConfiguration = () => {
    console.log(
      "🚨🚨🚨 BUILD TARGET CONFIG CALLED - THIS SHOULD BE VISIBLE! 🚨🚨🚨"
    )

    const parameterBounds: Record<string, [number, number]> = {}
    const targetBounds: Record<string, [number, number]> = {}
    const updatedParameters: any[] = []

    console.log("🏗️ BUILDING TARGET CONFIGURATION:")
    console.log("  - Parameter Configs:", parameterConfigs)
    console.log("  - Target Configs:", targetConfigs)
    console.log("  - Constraints:", constraints)
    console.log("  - Original optimization config:", optimization.config)

    // 🔧 CRITICAL FIX: Apply weight redistribution FIRST before any other processing
    console.log("🔧 WEIGHT FIX: Checking target weights before processing...")
    const targetNames = Object.keys(targetConfigs)
    if (targetNames.length > 1) {
      const currentWeights = targetNames.map(
        name => targetConfigs[name]?.weight || 0
      )
      const weightSum = currentWeights.reduce((sum, w) => sum + w, 0)
      console.log(
        `🔧 WEIGHT FIX: Current weights: ${currentWeights}, sum: ${weightSum}`
      )

      if (Math.abs(weightSum - 1.0) > 0.001) {
        console.log(
          `🔧 WEIGHT FIX: Weights need normalization (sum=${weightSum}), applying fix...`
        )

        if (weightSum > 0) {
          // Apply proportional redistribution to targetConfigs state
          const normalizedTargetConfigs = { ...targetConfigs }
          targetNames.forEach(name => {
            const currentWeight = normalizedTargetConfigs[name]?.weight || 0
            const normalizedWeight = currentWeight / weightSum
            normalizedTargetConfigs[name] = {
              ...normalizedTargetConfigs[name],
              weight: Math.max(0.01, normalizedWeight) // Ensure minimum weight
            }
          })

          // Update the state (this will be used in the rest of the function)
          Object.assign(targetConfigs, normalizedTargetConfigs)
          console.log(
            `🔧 WEIGHT FIX: Applied normalization:`,
            Object.entries(targetConfigs).map(([name, config]) => ({
              name,
              weight: config.weight,
              percentage: Math.round((config.weight || 0) * 100)
            }))
          )
        } else {
          // If all weights are 0, distribute equally
          const equalWeight = 1.0 / targetNames.length
          targetNames.forEach(name => {
            targetConfigs[name] = {
              ...targetConfigs[name],
              weight: equalWeight
            }
          })
          console.log(
            `🔧 WEIGHT FIX: Applied equal distribution:`,
            Object.entries(targetConfigs).map(([name, config]) => ({
              name,
              weight: config.weight,
              percentage: Math.round((config.weight || 0) * 100)
            }))
          )
        }
      } else {
        console.log(
          `🔧 WEIGHT FIX: Weights already normalized, sum = ${weightSum}`
        )
      }
    } else {
      console.log(`🔧 WEIGHT FIX: Single target, no normalization needed`)
    }

    // 🔧 FIX: Ensure we preserve the original parameter configuration
    // and only update what the user has actually changed
    Object.entries(parameterConfigs).forEach(([paramName, config]) => {
      console.log(`  📊 Processing parameter ${paramName}:`, config)

      if (config.type === "NumericalContinuous" && config.bounds) {
        parameterBounds[paramName] = config.bounds
        console.log(`    ✅ Added bounds for ${paramName}:`, config.bounds)
      }

      const updatedParam: any = {
        name: paramName,
        type:
          config.type === "CategoricalParameter" ? "Categorical" : config.type
      }

      if (config.type === "NumericalContinuous" && config.bounds) {
        updatedParam.bounds = config.bounds
      } else if (config.values) {
        // 🔧 FIX: Use the values from the current configuration, not potentially stale ones
        updatedParam.values = config.values
        console.log(
          `    📝 Using parameter values for ${paramName}:`,
          config.values
        )
      }

      updatedParameters.push(updatedParam)
      console.log(`    📝 Updated parameter:`, updatedParam)
    })

    Object.entries(targetConfigs).forEach(([targetName, config]) => {
      console.log(`  🎯 Processing target ${targetName}:`, config)
      if (config.bounds) {
        targetBounds[targetName] = config.bounds
        console.log(
          `    ✅ Added target bounds for ${targetName}:`,
          config.bounds
        )
      }
    })

    // 🔧 FIX: Ensure constraints are properly included
    const finalConstraints =
      constraints && constraints.length > 0 ? constraints : []
    console.log(
      "🔧 CONSTRAINT FIX: Final constraints to send:",
      finalConstraints
    )

    // Ensure weights are properly normalized for multi-target configurations
    const targetEntries = Object.entries(targetConfigs)
    console.log(
      `🔧 BUILD CONFIG: Input targetConfigs:`,
      Object.entries(targetConfigs).map(([name, config]) => ({
        name,
        weight: config.weight,
        percentage: Math.round((config.weight || 0) * 100)
      }))
    )

    let normalizedTargetConfig = targetEntries.map(([name, config]) => ({
      name,
      bounds: config.bounds,
      mode: config.mode,
      weight: config.weight || 1.0 / targetEntries.length,
      transformation: config.transformation || "LINEAR",
      type: config.type || "Numerical"
    }))

    // For multi-target configurations, ensure weights sum to exactly 1.0
    if (normalizedTargetConfig.length > 1) {
      const weightSum = normalizedTargetConfig.reduce(
        (sum, target) => sum + (target.weight || 0),
        0
      )
      console.log(
        `🔧 BUILD CONFIG: Weight sum before normalization: ${weightSum}`
      )

      if (weightSum > 0) {
        if (Math.abs(weightSum - 1.0) > 0.001) {
          console.log(
            `🔧 WEIGHT NORMALIZATION: Normalizing weights from sum ${weightSum} to 1.0`
          )
          normalizedTargetConfig = normalizedTargetConfig.map(target => ({
            ...target,
            weight: (target.weight || 0) / weightSum
          }))
          console.log(
            "🔧 NORMALIZED WEIGHTS:",
            normalizedTargetConfig.map(t => ({
              name: t.name,
              weight: t.weight,
              percentage: Math.round((t.weight || 0) * 100)
            }))
          )
        } else {
          console.log(
            `🔧 BUILD CONFIG: Weights already normalized, sum = ${weightSum}`
          )
        }
      } else {
        // If all weights are 0, distribute equally
        console.log(`🔧 BUILD CONFIG: All weights are 0, distributing equally`)
        const equalWeight = 1.0 / normalizedTargetConfig.length
        normalizedTargetConfig = normalizedTargetConfig.map(target => ({
          ...target,
          weight: equalWeight
        }))
      }
    } else {
      // Single target should have weight 1.0
      if (normalizedTargetConfig.length === 1) {
        normalizedTargetConfig[0].weight = 1.0
        console.log(`🔧 BUILD CONFIG: Single target, setting weight to 1.0`)
      }
    }

    const finalConfig = {
      parameters: updatedParameters,
      target_config: normalizedTargetConfig,
      constraints: finalConstraints, // 🔧 FIX: Ensure constraints are included
      acquisition_config: acquisitionConfig,
      parameter_bounds: parameterBounds,
      target_bounds: targetBounds,
      // 🔧 FIX: Include parameter order to maintain consistency
      parameter_order: parameterOrder
    }

    console.log("🎯 FINAL TARGET CONFIGURATION:")
    console.log("  - Parameter Bounds:", parameterBounds)
    console.log("  - Target Bounds:", targetBounds)
    console.log("  - Updated Parameters:", updatedParameters)
    console.log("  - Constraints Count:", finalConstraints.length)
    console.log("  - Parameter Order:", parameterOrder)
    console.log("  - Full Config:", finalConfig)

    return finalConfig
  }

  const handlePreview = async () => {
    const isValid = validateConfigs()
    if (!isValid) {
      toast({
        title: "Validation Errors",
        description: `Please fix ${errors.filter(e => e.severity === "error").length} validation error(s) before proceeding.`,
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      // Phase 2: Always use incremental update preview (remove old system)
      await handleIncrementalPreview()
    } catch (error) {
      console.error("Preview failed:", error)
      addError(
        ErrorFactory.createSystemError(ErrorCode.SYSTEM_LOAD_FAILED, {
          message:
            error instanceof Error
              ? error.message
              : "Failed to generate preview"
        })
      )
    } finally {
      setIsLoading(false)
    }
  }
  // Old preview logic removed - now handled by handleIncrementalPreview

  const handleConfirm = async () => {
    setIsLoading(true)
    try {
      // Phase 2: Always use incremental update system
      if (currentUpdatePreview) {
        await handleIncrementalApply()
      } else {
        // If no preview available, generate one first
        await handleIncrementalPreview()
        if (currentUpdatePreview) {
          await handleIncrementalApply()
        } else {
          throw new Error("Failed to generate update preview")
        }
      }
    } catch (error) {
      console.error("Configuration update failed:", error)
      addError(
        ErrorFactory.createSystemError(ErrorCode.SYSTEM_SAVE_FAILED, {
          message:
            error instanceof Error
              ? error.message
              : "Configuration update failed"
        })
      )
    } finally {
      setIsLoading(false)
    }
  }

  // Phase 2: Incremental apply
  const handleIncrementalApply = async () => {
    console.log("🎯 FRONTEND TARGETS CRUD: === INCREMENTAL APPLY OPERATION ===")
    console.log(
      "🎯 FRONTEND TARGETS CRUD: Starting incremental apply for optimization:",
      optimization.optimizerId
    )
    console.log(
      "🎯 FRONTEND TARGETS CRUD: Current update preview available:",
      !!currentUpdatePreview
    )

    try {
      // Create version before applying changes
      const changes = currentUpdatePreview?.strategy
        ? [
            ...currentUpdatePreview.strategy.simpleUpdates,
            ...currentUpdatePreview.strategy.moderateUpdates,
            ...currentUpdatePreview.strategy.complexUpdates
          ]
        : []

      console.log("🎯 FRONTEND TARGETS CRUD: === CHANGE SUMMARY ===")
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Total changes to apply:",
        changes.length
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Simple updates:",
        currentUpdatePreview?.strategy?.simpleUpdates?.length || 0
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Moderate updates:",
        currentUpdatePreview?.strategy?.moderateUpdates?.length || 0
      )
      console.log(
        "🎯 FRONTEND TARGETS CRUD: Complex updates:",
        currentUpdatePreview?.strategy?.complexUpdates?.length || 0
      )

      console.log(
        "🎯 FRONTEND TARGETS CRUD: Creating version before applying changes..."
      )
      const newVersion = await createVersion(
        buildTargetConfiguration(),
        changes.map((op: any) => ({
          id: op.id,
          type: op.type,
          action: "modify",
          field: op.field,
          oldValue: op.oldValue,
          newValue: op.newValue,
          impact: {
            measurementsAffected: 0,
            dataLossRisk: "low",
            performanceImpact: "minimal",
            compatibilityImpact: "minor",
            estimatedDuration: op.estimatedDuration
          },
          validation: {
            isValid: true,
            errors: [],
            warnings: [],
            recommendations: []
          }
        })),
        "Configuration update via incremental strategy"
      )

      // Execute incremental update
      const currentConfig = optimization.config
      const targetConfig = buildTargetConfiguration()
      const updateContext: UpdateContext = {
        optimizationId: optimization.optimizerId,
        currentConfiguration: currentConfig,
        targetConfiguration: targetConfig,
        measurementCount: optimization.measurements?.length || 0,
        modelComplexity: "medium",
        lastUpdateTimestamp: new Date(),
        userPreferences: {
          prioritizeSpeed: true,
          allowDataLoss: false,
          maxAcceptableDelay: 30000
        }
      }

      const result = await executeUpdate(
        currentConfig,
        targetConfig,
        updateContext
      )

      if (result.success) {
        toast({
          title: "Configuration Updated Successfully",
          description: `Applied ${result.operationsApplied.length} operations in ${result.timeTaken}ms. ${result.measurementsAffected} measurements affected.`,
          variant: "default"
        })

        // Log successful update
        logConfigurationChange(
          "update",
          [],
          {},
          {
            reason: `Incremental configuration update completed - Successfully applied ${result.operationsApplied.length} operations`,
            triggeredBy: "user"
          }
        )

        onConfigurationUpdated(result)
        onOpenChange(false)
      } else {
        throw new Error(`Update failed: ${result.errors.join(", ")}`)
      }
    } catch (error) {
      console.error("Incremental update failed:", error)
      toast({
        title: "Incremental Update Failed",
        description: "Falling back to full configuration update...",
        variant: "destructive"
      })

      // If incremental update fails, throw error (no fallback to old system)
      throw new Error(
        `Incremental update failed: ${error instanceof Error ? error.message : "Unknown error"}`
      )
    }
  }

  const handleCancel = () => {
    setShowPreview(false)
    setCurrentUpdatePreview(null)
    setShowIncrementalPreview(false)
  }

  const handleClose = () => {
    setShowPreview(false)
    setCurrentUpdatePreview(null)
    setShowIncrementalPreview(false)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {showPreview
              ? "Confirm Configuration Update"
              : "Edit Parameter & Target Configuration"}
          </DialogTitle>
          <DialogDescription>
            {showPreview
              ? "Review the impact of your configuration changes before applying them."
              : "Modify parameter ranges/values and target bounds. This will filter existing measurements and retrain the model."}
          </DialogDescription>
        </DialogHeader>

        {/* Error Display */}
        {(hasErrors || hasWarnings) && (
          <div className="mb-4">
            <ErrorList
              errors={errors}
              onDismiss={_errorId => {
                // Remove specific error (implement if needed)
              }}
              onDismissAll={clearErrors}
              compact={true}
              maxHeight="200px"
            />
          </div>
        )}

        {/* Phase 2: Version Control Info */}
        {!showPreview && (
          <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-3">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-blue-800">
                  Smart Configuration Updates
                </h4>
                <p className="text-xs text-blue-600">
                  Intelligent analysis determines the optimal update strategy
                  for your changes
                </p>
              </div>
              <div className="text-xs text-blue-600">⚡ Phase 2 Enhanced</div>
            </div>
            {versions.length > 0 && (
              <div className="mt-2 flex items-center gap-2 text-xs text-blue-600">
                <span>Version {activeVersion?.version || "Unknown"}</span>
                <span>•</span>
                <span>{versions.length} total versions</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs text-blue-600 hover:bg-blue-100"
                  onClick={() => setShowVersionHistory(true)}
                >
                  View History
                </Button>
              </div>
            )}
          </div>
        )}

        {!showPreview ? (
          <div className="space-y-6">
            {/* Parameter Configuration Section */}
            {Object.keys(parameterConfigs).length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Parameter Configuration</h3>
                <div className="grid gap-4">
                  {/* Show all parameter configurations */}
                  {(() => {
                    console.log(
                      "ConfigurationUpdateDialog: Rendering parameters, parameterConfigs:",
                      parameterConfigs
                    )
                    return null
                  })()}
                  {parameterOrder.length === 0 && (
                    <div className="text-muted-foreground rounded-lg border p-4 text-sm">
                      No parameter configurations found. This might indicate an
                      issue with parameter parsing.
                    </div>
                  )}
                  {parameterOrder.map(paramName => {
                    const config = parameterConfigs[paramName]
                    if (!config) return null
                    return (
                      <div
                        key={paramName}
                        className="space-y-3 rounded-lg border p-4"
                      >
                        <div className="flex items-center justify-between">
                          <Label className="text-base font-medium">
                            {paramName}
                          </Label>
                          <span className="text-muted-foreground text-sm">
                            {config.type}
                          </span>
                        </div>

                        {config.type === "NumericalContinuous" && (
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label className="text-muted-foreground text-sm">
                                Minimum
                              </Label>
                              <Input
                                type="number"
                                value={
                                  config.rawBoundsInput?.[0] ??
                                  config.bounds?.[0] ??
                                  ""
                                }
                                onChange={e =>
                                  handleParameterBoundChange(
                                    paramName,
                                    0,
                                    e.target.value
                                  )
                                }
                                step="any"
                                placeholder="Enter minimum"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label className="text-muted-foreground text-sm">
                                Maximum
                              </Label>
                              <Input
                                type="number"
                                value={
                                  config.rawBoundsInput?.[1] ??
                                  config.bounds?.[1] ??
                                  ""
                                }
                                onChange={e =>
                                  handleParameterBoundChange(
                                    paramName,
                                    1,
                                    e.target.value
                                  )
                                }
                                step="any"
                                placeholder="Enter maximum"
                              />
                            </div>
                          </div>
                        )}

                        {(config.type === "NumericalDiscrete" ||
                          config.type === "Categorical" ||
                          config.type === "CategoricalParameter") && (
                          <div className="space-y-3">
                            <div className="space-y-2">
                              <Label className="text-muted-foreground text-sm">
                                Values{" "}
                                {config.type === "NumericalDiscrete"
                                  ? "(comma-separated numbers)"
                                  : "(comma-separated options)"}
                              </Label>
                              <Input
                                type="text"
                                value={
                                  config.rawValuesInput ??
                                  config.values?.join(", ") ??
                                  ""
                                }
                                onChange={e => {
                                  console.log(
                                    "🔧 Config Dialog - Input onChange for",
                                    paramName,
                                    ":",
                                    e.target.value
                                  )
                                  handleParameterValuesChange(
                                    paramName,
                                    e.target.value
                                  )
                                }}
                                onKeyDown={e => {
                                  console.log(
                                    "🔧 Config Dialog - Key pressed for",
                                    paramName,
                                    ":",
                                    e.key,
                                    "Code:",
                                    e.code
                                  )
                                  // Allow all keys including comma
                                }}
                                placeholder={
                                  config.type === "NumericalDiscrete"
                                    ? "e.g., 1, 2, 5, 10"
                                    : "e.g., option1, option2, option3"
                                }
                              />
                            </div>

                            {/* Encoding selection for categorical parameters */}
                            {(config.type === "Categorical" ||
                              config.type === "CategoricalParameter") && (
                              <div className="space-y-2">
                                <Label className="text-muted-foreground text-sm">
                                  Encoding Method
                                </Label>
                                <Select
                                  value={config.encoding || "OHE"}
                                  onValueChange={value =>
                                    handleParameterEncodingChange(
                                      paramName,
                                      value
                                    )
                                  }
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select encoding method" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="OHE">
                                      One-Hot Encoding (OHE)
                                    </SelectItem>
                                    <SelectItem value="LE">
                                      Label Encoding (LE)
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {/* Target Configuration Section */}
            <>
              <Separator />
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Target Configuration</h3>
                <div className="grid gap-4">
                  {/* Show existing target configurations */}
                  {Object.entries(targetConfigs).map(([targetName, config]) => (
                    <div
                      key={targetName}
                      className="space-y-4 rounded-lg border p-4"
                    >
                      <div className="flex items-center justify-between">
                        <Label className="text-base font-medium">
                          {targetName}
                        </Label>
                        <span className="text-muted-foreground text-sm">
                          {config.type || "Numerical"}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        {/* Optimization Mode */}
                        <div className="space-y-2">
                          <Label className="text-muted-foreground text-sm">
                            Optimization Mode
                          </Label>
                          <select
                            value={config.mode || "MAX"}
                            onChange={e =>
                              handleTargetModeChange(
                                targetName,
                                e.target.value as "MAX" | "MIN"
                              )
                            }
                            className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          >
                            <option value="MAX">Maximize</option>
                            <option value="MIN">Minimize</option>
                          </select>
                        </div>

                        {/* Weight (for multi-target) */}
                        <div className="space-y-2">
                          <Label className="text-muted-foreground text-sm">
                            Weight (1-100%)
                          </Label>
                          <Input
                            type="number"
                            value={Math.round((config.weight || 1.0) * 100)}
                            onChange={e =>
                              handleTargetWeightChange(
                                targetName,
                                e.target.value
                              )
                            }
                            step="1"
                            min="1"
                            max="100"
                            placeholder="50"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        {/* Transformation */}
                        <div className="space-y-2">
                          <Label className="text-muted-foreground text-sm">
                            Transformation
                          </Label>
                          <select
                            value={config.transformation || "LINEAR"}
                            onChange={e =>
                              handleTargetTransformationChange(
                                targetName,
                                e.target.value as "LINEAR" | "LOG" | "SQRT"
                              )
                            }
                            className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          >
                            <option value="LINEAR">Linear</option>
                            <option value="LOG">Logarithmic</option>
                            <option value="SQRT">Square Root</option>
                          </select>
                        </div>

                        {/* Bounds */}
                        <div className="space-y-2">
                          <Label className="text-muted-foreground text-sm">
                            Minimum Bound
                          </Label>
                          <Input
                            type="number"
                            value={(() => {
                              const rawInput = config.rawBoundsInput?.[0]
                              const boundsValue = config.bounds?.[0]
                              const displayValue =
                                rawInput !== undefined
                                  ? rawInput
                                  : boundsValue !== undefined
                                    ? boundsValue.toString()
                                    : ""
                              console.log(
                                "🔧 Config Dialog - Target min input display:",
                                {
                                  rawInput,
                                  boundsValue,
                                  displayValue,
                                  rawInputType: typeof rawInput,
                                  boundsValueType: typeof boundsValue
                                }
                              )
                              return displayValue
                            })()}
                            onChange={e => {
                              console.log(
                                "🔧 Config Dialog - Target min input onChange:",
                                e.target.value
                              )
                              handleTargetBoundChange(
                                targetName,
                                0,
                                e.target.value
                              )
                            }}
                            step="any"
                            placeholder="Min value"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-muted-foreground text-sm">
                            Maximum Bound
                          </Label>
                          <Input
                            type="number"
                            value={(() => {
                              const rawInput = config.rawBoundsInput?.[1]
                              const boundsValue = config.bounds?.[1]
                              const displayValue =
                                rawInput !== undefined
                                  ? rawInput
                                  : boundsValue !== undefined
                                    ? boundsValue.toString()
                                    : ""
                              console.log(
                                "🔧 Config Dialog - Target max input display:",
                                {
                                  rawInput,
                                  boundsValue,
                                  displayValue,
                                  rawInputType: typeof rawInput,
                                  boundsValueType: typeof boundsValue
                                }
                              )
                              return displayValue
                            })()}
                            onChange={e => {
                              console.log(
                                "🔧 Config Dialog - Target max input onChange:",
                                e.target.value
                              )
                              handleTargetBoundChange(
                                targetName,
                                1,
                                e.target.value
                              )
                            }}
                            step="any"
                            placeholder="Max value"
                          />
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Show targets without bounds */}
                  {(() => {
                    const config = optimization?.config as any
                    const targetConfig = config?.target_config
                    const targetsToShow = []

                    if (Array.isArray(targetConfig)) {
                      // Multi-target case
                      targetConfig.forEach((target: any) => {
                        if (!targetConfigs[target.name]) {
                          targetsToShow.push(target.name)
                        }
                      })
                    } else if (
                      targetConfig &&
                      !targetConfigs[
                        targetConfig.name || optimization.targetName || "Target"
                      ]
                    ) {
                      // Single target case
                      targetsToShow.push(
                        targetConfig.name || optimization.targetName || "Target"
                      )
                    }

                    return targetsToShow.map(targetName => (
                      <div
                        key={targetName}
                        className="grid grid-cols-3 items-center gap-4"
                      >
                        <Label className="font-medium">{targetName}</Label>
                        <div className="space-y-2">
                          <Label className="text-muted-foreground text-sm">
                            Minimum
                          </Label>
                          <Input
                            type="number"
                            placeholder="Enter minimum"
                            onChange={e =>
                              handleTargetBoundChange(
                                targetName,
                                0,
                                e.target.value
                              )
                            }
                            step="any"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-muted-foreground text-sm">
                            Maximum
                          </Label>
                          <Input
                            type="number"
                            placeholder="Enter maximum"
                            onChange={e =>
                              handleTargetBoundChange(
                                targetName,
                                1,
                                e.target.value
                              )
                            }
                            step="any"
                          />
                        </div>
                      </div>
                    ))
                  })()}
                </div>

                {/* Weight Sum Validation for Multi-Target */}
                {Object.keys(targetConfigs).length > 1 && (
                  <div className="mt-4">
                    {(() => {
                      const weights = Object.values(targetConfigs).map(
                        config => config.weight || 0
                      )
                      const sum = weights.reduce((acc, w) => acc + w, 0)
                      const isValid = Math.abs(sum - 1.0) < 0.001

                      return (
                        <div
                          className={`rounded-md border p-3 text-sm ${
                            isValid
                              ? "bg-muted border-border"
                              : "border-destructive bg-destructive/10"
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span>
                              Target Weight Sum: {Math.round(sum * 100)}%
                            </span>
                            <span
                              className={
                                isValid
                                  ? "text-muted-foreground"
                                  : "text-destructive"
                              }
                            >
                              {isValid ? "✓ Valid" : "⚠ Must sum to 100%"}
                            </span>
                          </div>
                          {!isValid && (
                            <p className="text-destructive mt-1 text-xs">
                              Weights are automatically redistributed to
                              maintain sum of 100%
                            </p>
                          )}
                        </div>
                      )
                    })()}
                  </div>
                )}
              </div>
            </>

            {/* Acquisition Function Configuration Section */}
            <>
              <Separator />
              <div className="space-y-4">
                <h3 className="text-lg font-medium">
                  Acquisition Function Configuration
                </h3>
                <div className="text-muted-foreground space-y-1 text-sm">
                  <p>
                    The acquisition function determines how the algorithm
                    explores the parameter space.
                  </p>
                  <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
                    <p className="font-medium text-blue-800">
                      ℹ️ Detected Objective Type:{" "}
                      {(() => {
                        switch (objectiveType) {
                          case "SINGLE":
                            return "Single Target Objective"
                          case "MULTI_DESIRABILITY":
                            return "Multi-Target Objective (Desirability)"
                          case "MULTI_PARETO":
                            return "Multi-Target Objective (Pareto)"
                          default:
                            return objectiveType
                        }
                      })()}{" "}
                      - Only compatible acquisition functions are shown.
                    </p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">
                        Function Type
                      </Label>
                      <select
                        value={acquisitionConfig.type}
                        onChange={e =>
                          setAcquisitionConfig(prev => ({
                            ...prev,
                            type: e.target.value as AcquisitionConfig["type"],
                            // Clear parameters when switching acquisition functions
                            beta:
                              e.target.value === "qUpperConfidenceBound"
                                ? prev.beta || 0.2
                                : undefined,
                            ref_point:
                              e.target.value ===
                                "qNoisyExpectedHypervolumeImprovement" ||
                              e.target.value ===
                                "qLogNoisyExpectedHypervolumeImprovement"
                                ? prev.ref_point
                                : undefined,
                            weights:
                              e.target.value === "qLogNParEGO"
                                ? prev.weights
                                : undefined,
                            rho:
                              e.target.value === "qLogNParEGO"
                                ? prev.rho
                                : undefined,
                            prune_baseline:
                              e.target.value ===
                                "qNoisyExpectedHypervolumeImprovement" ||
                              e.target.value ===
                                "qLogNoisyExpectedHypervolumeImprovement"
                                ? prev.prune_baseline
                                : undefined
                          }))
                        }
                        className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        {getAvailableAcquisitionFunctions(objectiveType).map(
                          func => (
                            <option key={func.value} value={func.value}>
                              {func.label}
                            </option>
                          )
                        )}
                      </select>
                    </div>

                    {acquisitionConfig.type === "qUpperConfidenceBound" && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">
                          Beta Parameter
                        </Label>
                        <Input
                          type="number"
                          value={acquisitionConfig.beta || 0.2}
                          onChange={e =>
                            setAcquisitionConfig(prev => ({
                              ...prev,
                              beta: parseFloat(e.target.value) || 0.2
                            }))
                          }
                          step="0.1"
                          min="0.1"
                          placeholder="0.2"
                        />
                        <p className="text-muted-foreground text-xs">
                          Controls exploration vs exploitation balance. Higher
                          values favor exploration.
                        </p>
                      </div>
                    )}

                    {/* Reference Point parameter - show for qNEHVI and qLogNEHVI */}
                    {(acquisitionConfig.type ===
                      "qNoisyExpectedHypervolumeImprovement" ||
                      acquisitionConfig.type ===
                        "qLogNoisyExpectedHypervolumeImprovement") && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">
                          Reference Point
                        </Label>
                        <Input
                          type="text"
                          value={acquisitionConfig.ref_point?.join(", ") || ""}
                          onChange={e => {
                            const values = e.target.value
                              .split(",")
                              .map(v => parseFloat(v.trim()))
                              .filter(v => !isNaN(v))
                            setAcquisitionConfig(prev => ({
                              ...prev,
                              ref_point: values.length > 0 ? values : undefined
                            }))
                          }}
                          placeholder="e.g., 0.0, 0.0"
                        />
                        <p className="text-muted-foreground text-xs">
                          Comma-separated reference point values for hypervolume
                          calculation. Should represent worst acceptable
                          performance for each objective.
                        </p>
                      </div>
                    )}

                    {/* Weights parameter - show for qLogNParEGO */}
                    {acquisitionConfig.type === "qLogNParEGO" && (
                      <>
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">
                            Objective Weights
                          </Label>
                          <Input
                            type="text"
                            value={acquisitionConfig.weights?.join(", ") || ""}
                            onChange={e => {
                              const values = e.target.value
                                .split(",")
                                .map(v => parseFloat(v.trim()))
                                .filter(v => !isNaN(v))
                              setAcquisitionConfig(prev => ({
                                ...prev,
                                weights: values.length > 0 ? values : undefined
                              }))
                            }}
                            placeholder="e.g., 0.5, 0.5"
                          />
                          <p className="text-muted-foreground text-xs">
                            Comma-separated weights for each objective. Should
                            sum to 1.0.
                          </p>
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">
                            Rho Parameter (Optional)
                          </Label>
                          <Input
                            type="number"
                            value={acquisitionConfig.rho || ""}
                            onChange={e =>
                              setAcquisitionConfig(prev => ({
                                ...prev,
                                rho: e.target.value
                                  ? parseFloat(e.target.value)
                                  : undefined
                              }))
                            }
                            step="0.01"
                            min="0"
                            placeholder="0.05 (default)"
                          />
                          <p className="text-muted-foreground text-xs">
                            Augmented Chebyshev scalarization parameter.
                            Default: 0.05.
                          </p>
                        </div>
                      </>
                    )}

                    {/* Prune Baseline parameter - show for qNEHVI and qLogNEHVI */}
                    {(acquisitionConfig.type ===
                      "qNoisyExpectedHypervolumeImprovement" ||
                      acquisitionConfig.type ===
                        "qLogNoisyExpectedHypervolumeImprovement") && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">
                          Prune Baseline (Optional)
                        </Label>
                        <select
                          value={
                            acquisitionConfig.prune_baseline?.toString() || ""
                          }
                          onChange={e =>
                            setAcquisitionConfig(prev => ({
                              ...prev,
                              prune_baseline: e.target.value
                                ? e.target.value === "true"
                                : undefined
                            }))
                          }
                          className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                          <option value="">Default</option>
                          <option value="true">True</option>
                          <option value="false">False</option>
                        </select>
                        <p className="text-muted-foreground text-xs">
                          Whether to prune the baseline for hypervolume
                          calculations.
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="text-muted-foreground text-sm">
                    {objectiveType === "SINGLE" ||
                    objectiveType === "MULTI_DESIRABILITY" ? (
                      <>
                        <p>
                          <strong>Expected Improvement (qEI):</strong> Balances
                          exploration and exploitation by considering both the
                          predicted improvement and uncertainty.
                        </p>
                        <p>
                          <strong>Probability of Improvement (qPI):</strong>{" "}
                          Focuses on the probability of finding a better
                          solution.
                        </p>
                        <p>
                          <strong>Upper Confidence Bound (qUCB):</strong> Uses
                          confidence bounds to balance exploration and
                          exploitation. The beta parameter controls this
                          balance.
                        </p>
                      </>
                    ) : (
                      <>
                        <p>
                          <strong>
                            Noisy Expected Hypervolume Improvement (qNEHVI):
                          </strong>
                          Optimizes the expected improvement in hypervolume for
                          multi-objective Pareto optimization with noise
                          handling.
                        </p>
                        <p>
                          <strong>
                            Log Noisy Expected Hypervolume Improvement
                            (qLogNEHVI):
                          </strong>
                          Log-space version of qNEHVI for improved numerical
                          stability.
                        </p>
                        <p>
                          <strong>Log ParEGO (qLogParEGO):</strong>
                          Scalarization-based approach using augmented Chebyshev
                          scalarization for multi-objective optimization.
                        </p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Constraints Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Constraints</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowConstraints(!showConstraints)}
                  >
                    {showConstraints ? "Hide" : "Show"} Constraints
                  </Button>
                </div>

                {showConstraints && (
                  <div className="space-y-4">
                    <p className="text-muted-foreground text-sm">
                      Add constraints to limit the parameter space and ensure
                      feasible solutions.
                    </p>

                    <CentralizedConstraintBuilder
                      parameters={Object.entries(parameterConfigs).map(
                        ([name, config]) => ({
                          name,
                          type: config.type as
                            | "NumericalDiscrete"
                            | "NumericalContinuous"
                            | "CategoricalParameter",
                          values:
                            config.type === "CategoricalParameter" ||
                            config.type === "Categorical" ||
                            config.type === "NumericalDiscrete"
                              ? ((config.values || []) as string[] | number[])
                              : undefined,
                          bounds:
                            config.type === "NumericalContinuous" &&
                            config.bounds
                              ? config.bounds
                              : undefined,
                          tolerance: config.tolerance,
                          encoding: config.encoding as "OHE" | "LE" | undefined
                        })
                      )}
                      constraints={constraints}
                      onConstraintsChange={setConstraints}
                      enableSampling={false}
                      className="rounded-lg border p-4"
                    />

                    {constraints.length > 0 && (
                      <div className="text-muted-foreground text-sm">
                        {constraints.length} constraint
                        {constraints.length !== 1 ? "s" : ""} configured
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          </div>
        ) : (
          // Enhanced Preview Section with Phase 2 Features
          <div className="space-y-6">
            {/* Phase 2: Incremental Update Strategy Preview */}
            {showIncrementalPreview && currentUpdatePreview && (
              <div className="space-y-4">
                <Alert className="border-blue-500 bg-blue-50">
                  <AlertCircle className="size-4 text-blue-600" />
                  <AlertTitle className="text-blue-800">
                    Update Strategy:{" "}
                    {currentUpdatePreview.canOptimize
                      ? "Incremental"
                      : "Full Recreation"}
                  </AlertTitle>
                  <AlertDescription className="text-blue-700">
                    <div className="mt-2 grid grid-cols-2 gap-4">
                      <div>
                        <strong>Operations:</strong>
                        <ul className="mt-1 text-sm">
                          <li>
                            •{" "}
                            {currentUpdatePreview.strategy.simpleUpdates.length}{" "}
                            simple updates
                          </li>
                          <li>
                            •{" "}
                            {
                              currentUpdatePreview.strategy.moderateUpdates
                                .length
                            }{" "}
                            moderate updates
                          </li>
                          <li>
                            •{" "}
                            {
                              currentUpdatePreview.strategy.complexUpdates
                                .length
                            }{" "}
                            complex updates
                          </li>
                        </ul>
                      </div>
                      <div>
                        <strong>Performance:</strong>
                        <ul className="mt-1 text-sm">
                          <li>
                            • Estimated time:{" "}
                            {currentUpdatePreview.estimatedDuration}ms
                          </li>
                          <li>
                            • Strategy:{" "}
                            {
                              currentUpdatePreview.strategy.recommendations
                                .optimizationStrategy
                            }
                          </li>
                          {currentUpdatePreview.canOptimize && (
                            <li>
                              • Time saving:{" "}
                              {
                                currentUpdatePreview.strategy.recommendations
                                  .potentialTimeSaving
                              }
                              ms
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>

                {/* Detailed Changes Breakdown */}
                <div className="space-y-4">
                  <h4 className="text-md font-medium">Configuration Changes</h4>

                  {/* Simple Updates */}
                  {currentUpdatePreview.strategy.simpleUpdates.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="text-sm font-medium text-green-700">
                        Simple Updates (
                        {currentUpdatePreview.strategy.simpleUpdates.length})
                      </h5>
                      <div className="grid gap-2">
                        {currentUpdatePreview.strategy.simpleUpdates.map(
                          (operation: any) => (
                            <div
                              key={operation.id}
                              className="rounded border border-green-200 bg-green-50 p-2 text-sm"
                            >
                              <div className="flex items-center justify-between">
                                <span className="font-medium">
                                  {operation.field}
                                </span>
                                <span className="text-xs text-green-600">
                                  {operation.type}
                                </span>
                              </div>
                              <div className="text-muted-foreground mt-1 text-xs">
                                {operation.oldValue !== undefined &&
                                operation.newValue !== undefined ? (
                                  <>
                                    {typeof operation.oldValue === "number" &&
                                    operation.field.includes("weight")
                                      ? `${Math.round(operation.oldValue * 100)}% → ${Math.round(operation.newValue * 100)}%`
                                      : Array.isArray(operation.oldValue)
                                        ? `[${operation.oldValue.join(", ")}] → [${operation.newValue.join(", ")}]`
                                        : `${operation.oldValue} → ${operation.newValue}`}
                                  </>
                                ) : (
                                  "Modified"
                                )}
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                  {/* Moderate Updates */}
                  {currentUpdatePreview.strategy.moderateUpdates.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="text-sm font-medium text-yellow-700">
                        Moderate Updates (
                        {currentUpdatePreview.strategy.moderateUpdates.length})
                        - Requires Model Retraining
                      </h5>
                      <div className="grid gap-2">
                        {currentUpdatePreview.strategy.moderateUpdates.map(
                          (operation: any) => (
                            <div
                              key={operation.id}
                              className="rounded border border-yellow-200 bg-yellow-50 p-2 text-sm"
                            >
                              <div className="flex items-center justify-between">
                                <span className="font-medium">
                                  {operation.field}
                                </span>
                                <span className="text-xs text-yellow-600">
                                  {operation.type}
                                </span>
                              </div>
                              <div className="text-muted-foreground mt-1 text-xs">
                                {operation.oldValue !== undefined &&
                                operation.newValue !== undefined ? (
                                  <>
                                    {typeof operation.oldValue === "number" &&
                                    operation.field.includes("weight")
                                      ? `${Math.round(operation.oldValue * 100)}% → ${Math.round(operation.newValue * 100)}%`
                                      : Array.isArray(operation.oldValue)
                                        ? `[${operation.oldValue.join(", ")}] → [${operation.newValue.join(", ")}]`
                                        : `${operation.oldValue} → ${operation.newValue}`}
                                  </>
                                ) : (
                                  "Modified"
                                )}
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                  {/* Complex Updates */}
                  {currentUpdatePreview.strategy.complexUpdates.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="text-sm font-medium text-red-700">
                        Complex Updates (
                        {currentUpdatePreview.strategy.complexUpdates.length}) -
                        Requires Campaign Recreation
                      </h5>
                      <div className="grid gap-2">
                        {currentUpdatePreview.strategy.complexUpdates.map(
                          (operation: any) => (
                            <div
                              key={operation.id}
                              className="rounded border border-red-200 bg-red-50 p-2 text-sm"
                            >
                              <div className="flex items-center justify-between">
                                <span className="font-medium">
                                  {operation.field}
                                </span>
                                <span className="text-xs text-red-600">
                                  {operation.type}
                                </span>
                              </div>
                              <div className="text-muted-foreground mt-1 text-xs">
                                {operation.oldValue !== undefined &&
                                operation.newValue !== undefined ? (
                                  <>
                                    {typeof operation.oldValue === "number" &&
                                    operation.field.includes("weight")
                                      ? `${Math.round(operation.oldValue * 100)}% → ${Math.round(operation.newValue * 100)}%`
                                      : Array.isArray(operation.oldValue)
                                        ? `[${operation.oldValue.join(", ")}] → [${operation.newValue.join(", ")}]`
                                        : `${operation.oldValue} → ${operation.newValue}`}
                                  </>
                                ) : (
                                  "Modified"
                                )}
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Version Information */}
                {activeVersion && (
                  <Alert className="border-green-500 bg-green-50">
                    <AlertCircle className="size-4 text-green-600" />
                    <AlertTitle className="text-green-800">
                      Version Control
                    </AlertTitle>
                    <AlertDescription className="text-green-700">
                      <div className="flex items-center justify-between">
                        <span>
                          Current: Version {activeVersion.version} • New version
                          will be created
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowVersionHistory(true)}
                        >
                          View History
                        </Button>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Conflict Detection */}
                {getPendingConflicts().length > 0 && (
                  <Alert variant="destructive">
                    <AlertTriangle className="size-4" />
                    <AlertTitle>Configuration Conflicts Detected</AlertTitle>
                    <AlertDescription>
                      {getPendingConflicts().length} conflict(s) detected with
                      concurrent edits.
                      <Button
                        variant="outline"
                        size="sm"
                        className="ml-2"
                        onClick={() => setShowConflictResolution(true)}
                      >
                        Resolve Conflicts
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {/* Measurement Impact Analysis */}
            {measurementPreview ? (
              <MeasurementImpactAnalysis
                preview={measurementPreview}
                onExpandDetails={() => setShowDetailedMeasurementAnalysis(true)}
                showDetailedBreakdown={false}
              />
            ) : (
              // Fallback message when measurement preview is not available
              <Alert>
                <AlertCircle className="size-4" />
                <AlertTitle>Data Impact Summary</AlertTitle>
                <AlertDescription>
                  Measurement impact analysis will be shown here once preview is
                  generated.
                </AlertDescription>
              </Alert>
            )}

            {/* Detailed Measurement Analysis Modal/Section */}
            {showDetailedMeasurementAnalysis && measurementPreview && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">
                    Detailed Measurement Analysis
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowDetailedMeasurementAnalysis(false)}
                  >
                    Hide Details
                  </Button>
                </div>
                <MeasurementFilteringPreview
                  filteredMeasurements={measurementPreview.filteredMeasurements}
                  retainedCount={measurementPreview.retainedMeasurements}
                  maxHeight="400px"
                />
              </div>
            )}

            {/* Legacy preview data display removed - now using Phase 2 measurement preview */}
          </div>
        )}

        <DialogFooter className="flex flex-col gap-2 sm:flex-row sm:justify-end">
          {!showPreview ? (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button onClick={handlePreview} disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 size-4 animate-spin" />}
                Analyze & Preview
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={handleCancel}>
                Back to Edit
              </Button>
              <Button
                onClick={handleConfirm}
                disabled={isLoading || getPendingConflicts().length > 0}
                className="bg-primary text-primary-foreground hover:bg-primary/90"
              >
                {isLoading && <Loader2 className="mr-2 size-4 animate-spin" />}
                {isIncrementalUpdating && updateProgress > 0 && (
                  <span className="mr-2">({updateProgress}%)</span>
                )}
                {currentUpdatePreview?.canOptimize
                  ? `Apply Incrementally (${currentUpdatePreview.estimatedDuration}ms)`
                  : "Apply Changes & Retrain"}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
