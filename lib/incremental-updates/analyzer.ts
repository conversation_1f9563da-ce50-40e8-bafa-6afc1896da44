import {
  UpdateType,
  UpdateComplexity,
  UpdateOperation,
  UpdateBatch,
  IncrementalUpdateStrategy,
  UpdateContext,
  ConfigurationDiff,
  UpdateValidation,
  UpdateOptimizationHints
} from "./types"

export class IncrementalUpdateAnalyzer {
  /**
   * Analyze configuration changes and determine update strategy
   */
  static analyzeConfigurationChanges(
    currentConfig: any,
    targetConfig: any,
    context: UpdateContext
  ): IncrementalUpdateStrategy {
    // 1. Detect configuration differences
    const diff = this.detectConfigurationDiff(currentConfig, targetConfig)

    // 2. Convert differences to update operations
    const operations = this.createUpdateOperations(diff, context)

    // 3. Classify operations by complexity
    const { simpleUpdates, moderateUpdates, complexUpdates } =
      this.classifyOperations(operations)

    // 4. Create execution plan
    const executionPlan = this.createExecutionPlan(
      simpleUpdates,
      moderateUpdates,
      complexUpdates
    )

    // 5. Generate optimization recommendations
    const recommendations = this.generateOptimizationRecommendations(
      operations,
      context
    )

    return {
      simpleUpdates,
      moderateUpdates,
      complexUpdates,
      executionPlan,
      recommendations
    }
  }

  /**
   * Detect differences between current and target configurations
   */
  private static detectConfigurationDiff(
    currentConfig: any,
    targetConfig: any
  ): ConfigurationDiff {
    const diff: ConfigurationDiff = {
      parameterChanges: { added: [], removed: [], modified: [] },
      targetChanges: { added: [], removed: [], modified: [] },
      constraintChanges: { added: [], removed: [], modified: [] },
      acquisitionChanges: {
        functionChanged: false,
        parametersChanged: false,
        oldConfig: null,
        newConfig: null
      },
      recommenderChanges: {
        configChanged: false,
        oldConfig: null,
        newConfig: null
      }
    }

    // Analyze parameter changes
    this.analyzeParameterChanges(currentConfig, targetConfig, diff)

    // Analyze target changes
    this.analyzeTargetChanges(currentConfig, targetConfig, diff)

    // Analyze constraint changes
    this.analyzeConstraintChanges(currentConfig, targetConfig, diff)

    // Analyze acquisition function changes
    this.analyzeAcquisitionChanges(currentConfig, targetConfig, diff)

    // Analyze recommender changes
    this.analyzeRecommenderChanges(currentConfig, targetConfig, diff)

    return diff
  }

  /**
   * Analyze parameter configuration changes
   */
  private static analyzeParameterChanges(
    currentConfig: any,
    targetConfig: any,
    diff: ConfigurationDiff
  ): void {
    const currentParams = this.normalizeParameters(
      currentConfig.parameters || []
    )
    const targetParams = this.normalizeParameters(targetConfig.parameters || [])

    // Find added parameters
    for (const paramName of Object.keys(targetParams)) {
      if (!currentParams[paramName]) {
        diff.parameterChanges.added.push(paramName)
      }
    }

    // Find removed parameters
    for (const paramName of Object.keys(currentParams)) {
      if (!targetParams[paramName]) {
        diff.parameterChanges.removed.push(paramName)
      }
    }

    // Find modified parameters
    for (const paramName of Object.keys(currentParams)) {
      if (targetParams[paramName]) {
        const currentParam = currentParams[paramName]
        const targetParam = targetParams[paramName]

        // Check for type changes
        if (currentParam.type !== targetParam.type) {
          diff.parameterChanges.modified.push({
            name: paramName,
            field: "type",
            oldValue: currentParam.type,
            newValue: targetParam.type,
            changeType: UpdateType.PARAMETER_TYPE_CHANGE
          })
        }

        // Check for bounds changes
        if (this.hasBoundsChanged(currentParam.bounds, targetParam.bounds)) {
          diff.parameterChanges.modified.push({
            name: paramName,
            field: "bounds",
            oldValue: currentParam.bounds,
            newValue: targetParam.bounds,
            changeType: UpdateType.PARAMETER_BOUNDS
          })
        }

        // Check for values changes
        if (this.hasValuesChanged(currentParam.values, targetParam.values)) {
          diff.parameterChanges.modified.push({
            name: paramName,
            field: "values",
            oldValue: currentParam.values,
            newValue: targetParam.values,
            changeType: UpdateType.PARAMETER_VALUES
          })
        }

        // Check for encoding changes
        if (currentParam.encoding !== targetParam.encoding) {
          diff.parameterChanges.modified.push({
            name: paramName,
            field: "encoding",
            oldValue: currentParam.encoding,
            newValue: targetParam.encoding,
            changeType: UpdateType.PARAMETER_ENCODING
          })
        }
      }
    }
  }

  /**
   * Analyze target configuration changes
   */
  private static analyzeTargetChanges(
    currentConfig: any,
    targetConfig: any,
    diff: ConfigurationDiff
  ): void {
    const currentTargets = this.normalizeTargets(
      currentConfig.target_config || []
    )
    const targetTargets = this.normalizeTargets(
      targetConfig.target_config || []
    )

    // Find added targets
    for (const targetName of Object.keys(targetTargets)) {
      if (!currentTargets[targetName]) {
        diff.targetChanges.added.push(targetName)
      }
    }

    // Find removed targets
    for (const targetName of Object.keys(currentTargets)) {
      if (!targetTargets[targetName]) {
        diff.targetChanges.removed.push(targetName)
      }
    }

    // Find modified targets
    for (const targetName of Object.keys(currentTargets)) {
      if (targetTargets[targetName]) {
        const currentTarget = currentTargets[targetName]
        const targetTarget = targetTargets[targetName]

        // Check for bounds changes
        if (this.hasBoundsChanged(currentTarget.bounds, targetTarget.bounds)) {
          diff.targetChanges.modified.push({
            name: targetName,
            field: "bounds",
            oldValue: currentTarget.bounds,
            newValue: targetTarget.bounds,
            changeType: UpdateType.TARGET_BOUNDS
          })
        }

        // Check for mode changes
        if (currentTarget.mode !== targetTarget.mode) {
          diff.targetChanges.modified.push({
            name: targetName,
            field: "mode",
            oldValue: currentTarget.mode,
            newValue: targetTarget.mode,
            changeType: UpdateType.TARGET_MODE
          })
        }

        // Check for weight changes
        if (currentTarget.weight !== targetTarget.weight) {
          diff.targetChanges.modified.push({
            name: targetName,
            field: "weight",
            oldValue: currentTarget.weight,
            newValue: targetTarget.weight,
            changeType: UpdateType.TARGET_WEIGHT
          })
        }
      }
    }
  }

  /**
   * Analyze constraint changes
   */
  private static analyzeConstraintChanges(
    currentConfig: any,
    targetConfig: any,
    diff: ConfigurationDiff
  ): void {
    const currentConstraints = currentConfig.constraints || []
    const targetConstraints = targetConfig.constraints || []

    // Simple constraint comparison (can be enhanced)
    if (currentConstraints.length !== targetConstraints.length) {
      // For now, treat any constraint count change as add/remove
      if (targetConstraints.length > currentConstraints.length) {
        diff.constraintChanges.added = targetConstraints.slice(
          currentConstraints.length
        )
      } else {
        diff.constraintChanges.removed = currentConstraints.slice(
          targetConstraints.length
        )
      }
    }
  }

  /**
   * Analyze acquisition function changes
   */
  private static analyzeAcquisitionChanges(
    currentConfig: any,
    targetConfig: any,
    diff: ConfigurationDiff
  ): void {
    const currentAcq = currentConfig.acquisition_config
    const targetAcq = targetConfig.acquisition_config

    if (JSON.stringify(currentAcq) !== JSON.stringify(targetAcq)) {
      diff.acquisitionChanges.functionChanged = true
      diff.acquisitionChanges.oldConfig = currentAcq
      diff.acquisitionChanges.newConfig = targetAcq
    }
  }

  /**
   * Analyze recommender changes
   */
  private static analyzeRecommenderChanges(
    currentConfig: any,
    targetConfig: any,
    diff: ConfigurationDiff
  ): void {
    const currentRec = currentConfig.recommender_config
    const targetRec = targetConfig.recommender_config

    if (JSON.stringify(currentRec) !== JSON.stringify(targetRec)) {
      diff.recommenderChanges.configChanged = true
      diff.recommenderChanges.oldConfig = currentRec
      diff.recommenderChanges.newConfig = targetRec
    }
  }

  /**
   * Create update operations from configuration diff
   */
  private static createUpdateOperations(
    diff: ConfigurationDiff,
    context: UpdateContext
  ): UpdateOperation[] {
    const operations: UpdateOperation[] = []
    let operationId = 1

    // Parameter operations
    diff.parameterChanges.modified.forEach(change => {
      operations.push({
        id: `param_${operationId++}`,
        type: change.changeType,
        complexity: this.determineUpdateComplexity(change.changeType),
        field: `parameter.${change.name}.${change.field}`,
        oldValue: change.oldValue,
        newValue: change.newValue,
        requiresValidation: true,
        requiresModelRetrain: this.requiresModelRetrain(change.changeType),
        requiresCampaignRecreation: this.requiresCampaignRecreation(
          change.changeType
        ),
        estimatedDuration: this.estimateOperationDuration(
          change.changeType,
          context
        ),
        dependencies: []
      })
    })

    // Target operations
    diff.targetChanges.modified.forEach(change => {
      operations.push({
        id: `target_${operationId++}`,
        type: change.changeType,
        complexity: this.determineUpdateComplexity(change.changeType),
        field: `target.${change.name}.${change.field}`,
        oldValue: change.oldValue,
        newValue: change.newValue,
        requiresValidation: true,
        requiresModelRetrain: this.requiresModelRetrain(change.changeType),
        requiresCampaignRecreation: this.requiresCampaignRecreation(
          change.changeType
        ),
        estimatedDuration: this.estimateOperationDuration(
          change.changeType,
          context
        ),
        dependencies: []
      })
    })

    return operations
  }

  /**
   * Classify operations by complexity
   */
  private static classifyOperations(operations: UpdateOperation[]) {
    return {
      simpleUpdates: operations.filter(
        op => op.complexity === UpdateComplexity.SIMPLE
      ),
      moderateUpdates: operations.filter(
        op => op.complexity === UpdateComplexity.MODERATE
      ),
      complexUpdates: operations.filter(
        op => op.complexity === UpdateComplexity.COMPLEX
      )
    }
  }

  /**
   * Create execution plan
   */
  private static createExecutionPlan(
    simpleUpdates: UpdateOperation[],
    moderateUpdates: UpdateOperation[],
    complexUpdates: UpdateOperation[]
  ) {
    const plan = []

    if (simpleUpdates.length > 0) {
      plan.push({
        phase: "simple" as const,
        operations: simpleUpdates,
        estimatedDuration: simpleUpdates.reduce(
          (sum, op) => sum + op.estimatedDuration,
          0
        )
      })
    }

    if (moderateUpdates.length > 0) {
      plan.push({
        phase: "moderate" as const,
        operations: moderateUpdates,
        estimatedDuration: moderateUpdates.reduce(
          (sum, op) => sum + op.estimatedDuration,
          0
        )
      })
    }

    if (complexUpdates.length > 0) {
      plan.push({
        phase: "complex" as const,
        operations: complexUpdates,
        estimatedDuration: complexUpdates.reduce(
          (sum, op) => sum + op.estimatedDuration,
          0
        )
      })
    }

    return plan
  }

  /**
   * Generate optimization recommendations
   */
  private static generateOptimizationRecommendations(
    operations: UpdateOperation[],
    context: UpdateContext
  ) {
    const hasComplexUpdates = operations.some(
      op => op.complexity === UpdateComplexity.COMPLEX
    )
    const totalDuration = operations.reduce(
      (sum, op) => sum + op.estimatedDuration,
      0
    )

    return {
      canOptimize: !hasComplexUpdates && operations.length > 1,
      optimizationStrategy: hasComplexUpdates
        ? "full_recreation"
        : "incremental_updates",
      potentialTimeSaving: hasComplexUpdates
        ? 0
        : Math.floor(totalDuration * 0.6),
      risks: hasComplexUpdates
        ? ["Full campaign recreation required"]
        : ["Minimal risk with incremental updates"]
    }
  }

  /**
   * Validate update operations before execution
   */
  static validateUpdateOperations(
    operations: UpdateOperation[],
    context: UpdateContext
  ): UpdateValidation {
    const validation: UpdateValidation = {
      isValid: true,
      errors: [],
      warnings: [],
      recommendations: []
    }

    operations.forEach(operation => {
      // Validate bounds
      if (
        operation.type === UpdateType.PARAMETER_BOUNDS ||
        operation.type === UpdateType.TARGET_BOUNDS
      ) {
        const bounds = operation.newValue
        if (Array.isArray(bounds) && bounds.length === 2) {
          if (bounds[0] >= bounds[1]) {
            validation.errors.push({
              operation,
              errorCode: "INVALID_BOUNDS",
              message: `Lower bound (${bounds[0]}) must be less than upper bound (${bounds[1]})`,
              severity: "error"
            })
            validation.isValid = false
          }
        }
      }

      // Check for conflicting operations
      const conflictingOps = operations.filter(
        other => other.id !== operation.id && other.field === operation.field
      )

      if (conflictingOps.length > 0) {
        validation.warnings.push({
          operation,
          warningCode: "CONFLICTING_OPERATIONS",
          message: `Multiple operations targeting the same field: ${operation.field}`,
          impact: "Only the last operation will be applied"
        })
      }
    })

    return validation
  }

  // Helper methods
  private static normalizeParameters(params: any[]): Record<string, any> {
    const normalized: Record<string, any> = {}
    params.forEach(param => {
      normalized[param.name] = param
    })
    return normalized
  }

  private static normalizeTargets(targets: any[]): Record<string, any> {
    const normalized: Record<string, any> = {}
    if (Array.isArray(targets)) {
      targets.forEach(target => {
        normalized[target.name] = target
      })
    }
    return normalized
  }

  private static hasBoundsChanged(oldBounds: any, newBounds: any): boolean {
    return JSON.stringify(oldBounds) !== JSON.stringify(newBounds)
  }

  private static hasValuesChanged(oldValues: any, newValues: any): boolean {
    return JSON.stringify(oldValues) !== JSON.stringify(newValues)
  }

  private static determineUpdateComplexity(
    updateType: UpdateType
  ): UpdateComplexity {
    switch (updateType) {
      case UpdateType.PARAMETER_BOUNDS:
      case UpdateType.TARGET_BOUNDS:
      case UpdateType.TARGET_WEIGHT:
        return UpdateComplexity.SIMPLE

      case UpdateType.PARAMETER_VALUES:
      case UpdateType.TARGET_MODE:
      case UpdateType.ACQUISITION_FUNCTION:
        return UpdateComplexity.MODERATE

      case UpdateType.PARAMETER_TYPE_CHANGE:
      case UpdateType.OBJECTIVE_TYPE_CHANGE:
      case UpdateType.SEARCHSPACE_STRUCTURE:
        return UpdateComplexity.COMPLEX

      default:
        return UpdateComplexity.MODERATE
    }
  }

  private static requiresModelRetrain(updateType: UpdateType): boolean {
    return [
      UpdateType.PARAMETER_VALUES,
      UpdateType.TARGET_MODE,
      UpdateType.ACQUISITION_FUNCTION,
      UpdateType.PARAMETER_TYPE_CHANGE,
      UpdateType.OBJECTIVE_TYPE_CHANGE
    ].includes(updateType)
  }

  private static requiresCampaignRecreation(updateType: UpdateType): boolean {
    return [
      UpdateType.PARAMETER_TYPE_CHANGE,
      UpdateType.OBJECTIVE_TYPE_CHANGE,
      UpdateType.SEARCHSPACE_STRUCTURE
    ].includes(updateType)
  }

  private static estimateOperationDuration(
    updateType: UpdateType,
    context: UpdateContext
  ): number {
    const baseTime = 100 // milliseconds
    const measurementFactor = Math.log(context.measurementCount + 1) * 10

    switch (updateType) {
      case UpdateType.PARAMETER_BOUNDS:
      case UpdateType.TARGET_BOUNDS:
        return baseTime + measurementFactor

      case UpdateType.PARAMETER_VALUES:
      case UpdateType.TARGET_MODE:
        return (baseTime + measurementFactor) * 2

      case UpdateType.PARAMETER_TYPE_CHANGE:
      case UpdateType.OBJECTIVE_TYPE_CHANGE:
        return (baseTime + measurementFactor) * 10

      default:
        return baseTime + measurementFactor
    }
  }
}
