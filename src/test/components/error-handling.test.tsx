import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockFetch, mockToast } from '../test-utils'

// Mock component that simulates parameter editing with error handling
const MockParameterEditWithErrorHandling = () => {
  const [error, setError] = React.useState<string | null>(null)
  const [isLoading, setIsLoading] = React.useState(false)
  const [parameter, setParameter] = React.useState({
    name: 'temperature',
    type: 'NumericalContinuous',
    bounds: [20, 100]
  })

  const handleSave = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/optimizations/test-optimizer/bounds', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          parameter_bounds: {
            [parameter.name]: parameter.bounds
          },
          preview_only: false
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || result.detail || 'Update failed')
      }

      if (result.status === 'error') {
        throw new Error(result.message)
      }

      // Success handling
      mockToast({ title: 'Success', description: 'Parameter updated successfully' })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      mockToast({ 
        title: 'Error', 
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleBoundsChange = (index: number, value: string) => {
    const numValue = parseFloat(value)
    if (isNaN(numValue)) {
      setError('Bounds must be numeric values')
      return
    }

    const newBounds = [...parameter.bounds]
    newBounds[index] = numValue

    if (newBounds[0] >= newBounds[1]) {
      setError('Lower bound must be less than upper bound')
      return
    }

    setError(null)
    setParameter(prev => ({ ...prev, bounds: newBounds }))
  }

  return (
    <div data-testid="parameter-edit-with-error-handling">
      <div>
        <label>Parameter Name:</label>
        <input
          data-testid="parameter-name"
          value={parameter.name}
          onChange={(e) => setParameter(prev => ({ ...prev, name: e.target.value }))}
        />
      </div>

      <div>
        <label>Lower Bound:</label>
        <input
          data-testid="lower-bound"
          type="number"
          value={parameter.bounds[0]}
          onChange={(e) => handleBoundsChange(0, e.target.value)}
        />
      </div>

      <div>
        <label>Upper Bound:</label>
        <input
          data-testid="upper-bound"
          type="number"
          value={parameter.bounds[1]}
          onChange={(e) => handleBoundsChange(1, e.target.value)}
        />
      </div>

      {error && (
        <div data-testid="error-message" className="error">
          {error}
        </div>
      )}

      <button
        data-testid="save-button"
        onClick={handleSave}
        disabled={isLoading || !!error}
      >
        {isLoading ? 'Saving...' : 'Save Parameter'}
      </button>
    </div>
  )
}

describe('Error Handling in Parameter CRUD Operations', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('displays validation error for invalid bounds', async () => {
    const user = userEvent.setup()

    render(<MockParameterEditWithErrorHandling />)

    // Set lower bound higher than upper bound
    const lowerBoundInput = screen.getByTestId('lower-bound')
    await user.clear(lowerBoundInput)
    await user.type(lowerBoundInput, '100')

    const upperBoundInput = screen.getByTestId('upper-bound')
    await user.clear(upperBoundInput)
    await user.type(upperBoundInput, '50')

    expect(screen.getByTestId('error-message')).toHaveTextContent(
      'Lower bound must be less than upper bound'
    )
    expect(screen.getByTestId('save-button')).toBeDisabled()
  })

  it('displays validation error for non-numeric bounds', async () => {
    const user = userEvent.setup()

    render(<MockParameterEditWithErrorHandling />)

    const lowerBoundInput = screen.getByTestId('lower-bound')
    await user.clear(lowerBoundInput)
    await user.type(lowerBoundInput, 'abc')

    expect(screen.getByTestId('error-message')).toHaveTextContent(
      'Bounds must be numeric values'
    )
    expect(screen.getByTestId('save-button')).toBeDisabled()
  })

  it('handles API error responses correctly', async () => {
    const user = userEvent.setup()
    
    // Mock API error response
    mockFetch({
      error: 'Invalid parameter bounds: lower bound must be less than upper bound'
    }, false)

    render(<MockParameterEditWithErrorHandling />)

    const saveButton = screen.getByTestId('save-button')
    await user.click(saveButton)

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent(
        'Invalid parameter bounds: lower bound must be less than upper bound'
      )
    })

    expect(mockToast).toHaveBeenCalledWith({
      title: 'Error',
      description: 'Invalid parameter bounds: lower bound must be less than upper bound',
      variant: 'destructive'
    })
  })

  it('handles network errors gracefully', async () => {
    const user = userEvent.setup()
    
    // Mock network error
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))

    render(<MockParameterEditWithErrorHandling />)

    const saveButton = screen.getByTestId('save-button')
    await user.click(saveButton)

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Network error')
    })

    expect(mockToast).toHaveBeenCalledWith({
      title: 'Error',
      description: 'Network error',
      variant: 'destructive'
    })
  })

  it('handles server error status codes', async () => {
    const user = userEvent.setup()
    
    // Mock 500 server error
    global.fetch = vi.fn().mockResolvedValue({
      ok: false,
      status: 500,
      json: async () => ({ detail: 'Internal server error' })
    })

    render(<MockParameterEditWithErrorHandling />)

    const saveButton = screen.getByTestId('save-button')
    await user.click(saveButton)

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Internal server error')
    })
  })

  it('handles successful API responses correctly', async () => {
    const user = userEvent.setup()
    
    // Mock successful response
    mockFetch({
      status: 'success',
      message: 'Parameter bounds updated successfully'
    })

    render(<MockParameterEditWithErrorHandling />)

    const saveButton = screen.getByTestId('save-button')
    await user.click(saveButton)

    await waitFor(() => {
      expect(screen.queryByTestId('error-message')).not.toBeInTheDocument()
    })

    expect(mockToast).toHaveBeenCalledWith({
      title: 'Success',
      description: 'Parameter updated successfully'
    })
  })

  it('shows loading state during API calls', async () => {
    const user = userEvent.setup()
    
    // Mock delayed response
    global.fetch = vi.fn().mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({ status: 'success' })
        }), 100)
      )
    )

    render(<MockParameterEditWithErrorHandling />)

    const saveButton = screen.getByTestId('save-button')
    await user.click(saveButton)

    expect(saveButton).toHaveTextContent('Saving...')
    expect(saveButton).toBeDisabled()

    await waitFor(() => {
      expect(saveButton).toHaveTextContent('Save Parameter')
      expect(saveButton).not.toBeDisabled()
    })
  })

  it('clears error when valid input is provided', async () => {
    const user = userEvent.setup()

    render(<MockParameterEditWithErrorHandling />)

    // First, create an error
    const lowerBoundInput = screen.getByTestId('lower-bound')
    await user.clear(lowerBoundInput)
    await user.type(lowerBoundInput, '100')

    expect(screen.getByTestId('error-message')).toBeInTheDocument()

    // Then fix the error
    await user.clear(lowerBoundInput)
    await user.type(lowerBoundInput, '10')

    expect(screen.queryByTestId('error-message')).not.toBeInTheDocument()
    expect(screen.getByTestId('save-button')).not.toBeDisabled()
  })

  it('handles empty parameter name validation', async () => {
    const user = userEvent.setup()

    render(<MockParameterEditWithErrorHandling />)

    const nameInput = screen.getByTestId('parameter-name')
    await user.clear(nameInput)

    // In a real implementation, this would trigger validation
    // For this test, we'll assume the component validates empty names
    expect(nameInput).toHaveValue('')
  })

  it('handles API response with error status but 200 HTTP code', async () => {
    const user = userEvent.setup()
    
    // Mock API response with error status
    mockFetch({
      status: 'error',
      message: 'Optimizer not found'
    })

    render(<MockParameterEditWithErrorHandling />)

    const saveButton = screen.getByTestId('save-button')
    await user.click(saveButton)

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Optimizer not found')
    })
  })

  it('handles malformed API responses', async () => {
    const user = userEvent.setup()
    
    // Mock malformed response
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: async () => { throw new Error('Invalid JSON') }
    })

    render(<MockParameterEditWithErrorHandling />)

    const saveButton = screen.getByTestId('save-button')
    await user.click(saveButton)

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Invalid JSON')
    })
  })
})
