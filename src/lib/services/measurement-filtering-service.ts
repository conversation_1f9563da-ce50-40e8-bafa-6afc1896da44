import {
  MeasurementFilteringPreview,
  FilteredMeasurement,
  ViolationReason,
  FilteringSummary,
  ImpactAnalysis
} from "@/components/measurements/measurement-impact-analysis"

export interface ConfigurationChange {
  parameterBounds?: Record<string, [number, number]>
  targetBounds?: Record<string, [number, number]>
  updatedParameters?: Record<string, any>
  constraints?: any[]
}

export class MeasurementFilteringService {
  /**
   * Generate a preview of measurement filtering impact
   */
  static async generateFilteringPreview(
    optimizerId: string,
    configurationChanges: ConfigurationChange
  ): Promise<MeasurementFilteringPreview> {
    try {
      console.log("🔬 MeasurementFilteringService: Starting preview generation")
      console.log("  - Optimizer ID:", optimizerId)
      console.log("  - Configuration Changes:", configurationChanges)

      // Use the existing bounds API with preview_only flag
      const response = await fetch(`/api/optimizations/${optimizerId}/bounds`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          parameter_bounds: configurationChanges.parameterBounds,
          target_bounds: configurationChanges.targetBounds,
          updated_parameters: configurationChanges.updatedParameters,
          constraints: configurationChanges.constraints,
          preview_only: true
        })
      })

      console.log(
        "🔬 MeasurementFilteringService: API Response status:",
        response.status
      )

      if (!response.ok) {
        const errorText = await response.text()
        console.error("🔬 MeasurementFilteringService: API Error:", errorText)
        throw new Error(
          `Preview request failed: ${response.statusText} - ${errorText}`
        )
      }

      const data = await response.json()
      console.log("🔬 MeasurementFilteringService: Raw API Response:", data)

      const transformedData = this.transformBackendResponse(data)
      console.log(
        "🔬 MeasurementFilteringService: Transformed Response:",
        transformedData
      )

      return transformedData
    } catch (error) {
      console.error(
        "🔬 MeasurementFilteringService: Error generating filtering preview:",
        error
      )
      throw error
    }
  }

  /**
   * Transform backend response to frontend format
   */
  private static transformBackendResponse(
    backendData: any
  ): MeasurementFilteringPreview {
    console.log("🔄 MeasurementFilteringService: Transforming backend response")
    console.log("  - Raw backend data keys:", Object.keys(backendData))
    console.log("  - Total measurements:", backendData.total_measurements)
    console.log("  - Retained measurements:", backendData.retained_measurements)
    console.log(
      "  - Filtered measurements count:",
      (backendData.filtered_measurements || []).length
    )
    console.log("  - Preview data:", backendData.preview_data)
    console.log("  - Measurement impact:", backendData.measurement_impact)

    const filteredMeasurements: FilteredMeasurement[] = (
      backendData.filtered_measurements || []
    ).map((measurement: any) => {
      console.log("  📊 Processing filtered measurement:", measurement)
      return {
        id:
          measurement.id ||
          `measurement_${Math.random().toString(36).substr(2, 9)}`,
        parameters: measurement.parameters || {},
        targetValues: measurement.target_values || measurement.targets || {},
        violationReasons: this.extractViolationReasons(measurement),
        suggestedFixes: this.generateSuggestedFixes(measurement)
      }
    })

    const filteringSummary: FilteringSummary = {
      totalMeasurements: backendData.total_measurements || 0,
      retainedMeasurements: backendData.retained_measurements || 0,
      filteredMeasurements: filteredMeasurements.length,
      retentionPercentage: this.calculateRetentionPercentage(
        backendData.retained_measurements || 0,
        backendData.total_measurements || 0
      ),
      filteringReasons: this.aggregateFilteringReasons(filteredMeasurements)
    }

    console.log(
      "📈 MeasurementFilteringService: Filtering summary:",
      filteringSummary
    )

    const impactAnalysis: ImpactAnalysis = this.analyzeImpact(
      filteringSummary,
      filteredMeasurements
    )

    console.log(
      "🎯 MeasurementFilteringService: Impact analysis:",
      impactAnalysis
    )

    const result = {
      totalMeasurements: filteringSummary.totalMeasurements,
      retainedMeasurements: filteringSummary.retainedMeasurements,
      filteredMeasurements,
      filteringSummary,
      impactAnalysis
    }

    console.log(
      "✅ MeasurementFilteringService: Final transformed result:",
      result
    )
    return result
  }

  /**
   * Extract violation reasons from backend measurement data
   */
  private static extractViolationReasons(measurement: any): ViolationReason[] {
    const violations: ViolationReason[] = []

    // Extract from backend violation data
    if (measurement.violations) {
      for (const violation of measurement.violations) {
        violations.push({
          type: this.mapViolationType(violation.type),
          field: violation.field || violation.parameter || "unknown",
          currentValue: violation.current_value,
          expectedRange: violation.expected_range,
          constraintName: violation.constraint_name,
          message: violation.message || this.generateViolationMessage(violation)
        })
      }
    }

    // Extract from filtering reasons if available
    if (measurement.filtering_reasons) {
      for (const reason of measurement.filtering_reasons) {
        violations.push({
          type: this.mapViolationType(reason.type),
          field: reason.field,
          currentValue: reason.current_value,
          expectedRange: reason.expected_range,
          message: reason.message
        })
      }
    }

    return violations
  }

  /**
   * Map backend violation types to frontend types
   */
  private static mapViolationType(
    backendType: string
  ): ViolationReason["type"] {
    switch (backendType?.toLowerCase()) {
      case "bounds_violation":
      case "parameter_bounds":
      case "target_bounds":
        return "bounds"
      case "constraint_violation":
      case "constraint":
        return "constraint"
      case "parameter_type":
      case "type_mismatch":
        return "parameter_type"
      case "nan_infinite":
      case "invalid_value":
        return "nan_infinite"
      default:
        return "bounds"
    }
  }

  /**
   * Generate violation message if not provided by backend
   */
  private static generateViolationMessage(violation: any): string {
    const type = violation.type?.toLowerCase()
    const field = violation.field || "parameter"
    const currentValue = violation.current_value
    const expectedRange = violation.expected_range

    switch (type) {
      case "bounds_violation":
        return `${field} value ${currentValue} is outside the allowed range ${expectedRange?.[0]} - ${expectedRange?.[1]}`
      case "constraint_violation":
        return `${field} violates constraint "${violation.constraint_name || "unknown"}"`
      case "parameter_type":
        return `${field} type is incompatible with new parameter configuration`
      case "nan_infinite":
        return `${field} contains invalid value (NaN or infinite)`
      default:
        return `${field} violates configuration requirements`
    }
  }

  /**
   * Generate suggested fixes for measurements
   */
  private static generateSuggestedFixes(measurement: any): string[] {
    const fixes: string[] = []
    const violations =
      measurement.violations || measurement.filtering_reasons || []

    for (const violation of violations) {
      switch (violation.type?.toLowerCase()) {
        case "bounds_violation":
          if (violation.expected_range) {
            fixes.push(
              `Expand ${violation.field} bounds to include ${violation.current_value}`
            )
          }
          break
        case "constraint_violation":
          fixes.push(
            `Relax or remove constraint "${violation.constraint_name}"`
          )
          break
        case "parameter_type":
          fixes.push(`Revert ${violation.field} to previous parameter type`)
          break
        case "nan_infinite":
          fixes.push(`Clean or remove invalid data for ${violation.field}`)
          break
      }
    }

    return fixes
  }

  /**
   * Calculate retention percentage
   */
  private static calculateRetentionPercentage(
    retained: number,
    total: number
  ): number {
    if (total === 0) return 100
    return Math.round((retained / total) * 100)
  }

  /**
   * Aggregate filtering reasons for summary
   */
  private static aggregateFilteringReasons(
    filteredMeasurements: FilteredMeasurement[]
  ): Record<string, number> {
    const reasons: Record<string, number> = {}

    for (const measurement of filteredMeasurements) {
      for (const violation of measurement.violationReasons) {
        const reasonKey = violation.type.replace("_", " ")
        reasons[reasonKey] = (reasons[reasonKey] || 0) + 1
      }
    }

    return reasons
  }

  /**
   * Analyze the impact of filtering
   */
  private static analyzeImpact(
    summary: FilteringSummary,
    filteredMeasurements: FilteredMeasurement[]
  ): ImpactAnalysis {
    const retentionPercentage = summary.retentionPercentage
    const totalFiltered = summary.filteredMeasurements

    // Determine data quality impact
    let dataQualityImpact: ImpactAnalysis["dataQualityImpact"] = "low"
    if (retentionPercentage < 50) {
      dataQualityImpact = "high"
    } else if (retentionPercentage < 80) {
      dataQualityImpact = "medium"
    }

    // Determine optimization impact
    let optimizationImpact: ImpactAnalysis["optimizationImpact"] = "minimal"
    if (summary.retainedMeasurements < 10) {
      optimizationImpact = "significant"
    } else if (retentionPercentage < 70) {
      optimizationImpact = "moderate"
    }

    // Generate recommendations
    const recommendations: string[] = []
    if (retentionPercentage < 80) {
      recommendations.push(
        "Consider expanding parameter bounds to retain more measurements"
      )
    }
    if (summary.filteringReasons["constraint"] > 0) {
      recommendations.push(
        "Review constraint restrictions - some may be too strict"
      )
    }
    if (summary.retainedMeasurements < 20) {
      recommendations.push(
        "Collect additional measurements to improve optimization quality"
      )
    }

    // Generate warnings
    const warnings: string[] = []
    if (retentionPercentage < 50) {
      warnings.push(
        "Low data retention may significantly impact optimization performance"
      )
    }
    if (summary.retainedMeasurements < 5) {
      warnings.push(
        "Very few measurements remaining - optimization may not be reliable"
      )
    }

    return {
      dataQualityImpact,
      optimizationImpact,
      recommendations,
      warnings
    }
  }
}
