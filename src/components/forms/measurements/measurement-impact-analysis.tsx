"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  ChevronDown,
  ChevronRight,
  TrendingDown,
  TrendingUp,
  Info,
  Filter
} from "lucide-react"

export interface ViolationReason {
  type: "bounds" | "constraint" | "parameter_type" | "nan_infinite"
  field: string
  currentValue: any
  expectedRange?: [number, number]
  constraintName?: string
  message: string
}

export interface FilteredMeasurement {
  id: string
  parameters: Record<string, any>
  targetValues: Record<string, number>
  violationReasons: ViolationReason[]
  suggestedFixes: string[]
}

export interface FilteringSummary {
  totalMeasurements: number
  retainedMeasurements: number
  filteredMeasurements: number
  retentionPercentage: number
  filteringReasons: Record<string, number>
}

export interface ImpactAnalysis {
  dataQualityImpact: "low" | "medium" | "high"
  optimizationImpact: "minimal" | "moderate" | "significant"
  recommendations: string[]
  warnings: string[]
}

export interface MeasurementFilteringPreview {
  totalMeasurements: number
  retainedMeasurements: number
  filteredMeasurements: FilteredMeasurement[]
  filteringSummary: FilteringSummary
  impactAnalysis: ImpactAnalysis
}

interface MeasurementImpactAnalysisProps {
  preview: MeasurementFilteringPreview
  onExpandDetails?: () => void
  showDetailedBreakdown?: boolean
}

export function MeasurementImpactAnalysis({
  preview,
  onExpandDetails,
  showDetailedBreakdown = false
}: MeasurementImpactAnalysisProps) {
  const [showDetails, setShowDetails] = React.useState(showDetailedBreakdown)

  const { filteringSummary, impactAnalysis } = preview
  const retentionPercentage = Math.round(
    (filteringSummary.retainedMeasurements /
      filteringSummary.totalMeasurements) *
      100
  )

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "low":
      case "minimal":
        return "text-green-600"
      case "medium":
      case "moderate":
        return "text-yellow-600"
      case "high":
      case "significant":
        return "text-red-600"
      default:
        return "text-gray-600"
    }
  }

  const getRetentionColor = (percentage: number) => {
    if (percentage >= 80) return "bg-green-500"
    if (percentage >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="size-5" />
          Measurement Impact Analysis
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Statistics */}
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {filteringSummary.totalMeasurements}
            </div>
            <div className="text-muted-foreground text-sm">Total</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {filteringSummary.retainedMeasurements}
            </div>
            <div className="text-muted-foreground text-sm">Retained</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {filteringSummary.filteredMeasurements}
            </div>
            <div className="text-muted-foreground text-sm">Filtered</div>
          </div>
          <div className="text-center">
            <div
              className={`text-2xl font-bold ${getImpactColor(retentionPercentage >= 80 ? "high" : retentionPercentage >= 60 ? "medium" : "low")}`}
            >
              {retentionPercentage}%
            </div>
            <div className="text-muted-foreground text-sm">Retention</div>
          </div>
        </div>

        {/* Retention Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Data Retention</span>
            <span>{retentionPercentage}%</span>
          </div>
          <Progress value={retentionPercentage} className="h-2" />
        </div>

        {/* Impact Assessment */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Data Quality Impact:</span>
              <Badge
                variant={
                  impactAnalysis.dataQualityImpact === "low"
                    ? "default"
                    : impactAnalysis.dataQualityImpact === "medium"
                      ? "secondary"
                      : "destructive"
                }
                className={getImpactColor(impactAnalysis.dataQualityImpact)}
              >
                {impactAnalysis.dataQualityImpact.toUpperCase()}
              </Badge>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Optimization Impact:</span>
              <Badge
                variant={
                  impactAnalysis.optimizationImpact === "minimal"
                    ? "default"
                    : impactAnalysis.optimizationImpact === "moderate"
                      ? "secondary"
                      : "destructive"
                }
                className={getImpactColor(impactAnalysis.optimizationImpact)}
              >
                {impactAnalysis.optimizationImpact.toUpperCase()}
              </Badge>
            </div>
          </div>
        </div>

        {/* Warnings */}
        {impactAnalysis.warnings.length > 0 && (
          <Alert>
            <AlertTriangle className="size-4" />
            <AlertDescription>
              <div className="space-y-1">
                {impactAnalysis.warnings.map((warning, index) => (
                  <div key={index} className="text-sm">
                    {warning}
                  </div>
                ))}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Filtering Breakdown */}
        <Collapsible open={showDetails} onOpenChange={setShowDetails}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between">
              <span>Filtering Breakdown</span>
              {showDetails ? (
                <ChevronDown className="size-4" />
              ) : (
                <ChevronRight className="size-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3">
            <div className="grid gap-2">
              {Object.entries(filteringSummary.filteringReasons).map(
                ([reason, count]) => (
                  <div
                    key={reason}
                    className="bg-muted flex items-center justify-between rounded p-2"
                  >
                    <span className="text-sm capitalize">
                      {reason.replace("_", " ")}
                    </span>
                    <Badge variant="outline">{count} measurements</Badge>
                  </div>
                )
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Recommendations */}
        {impactAnalysis.recommendations.length > 0 && (
          <div className="space-y-2">
            <h4 className="flex items-center gap-2 text-sm font-medium">
              <Info className="size-4" />
              Recommendations
            </h4>
            <ul className="space-y-1">
              {impactAnalysis.recommendations.map((recommendation, index) => (
                <li
                  key={index}
                  className="text-muted-foreground flex items-start gap-2 text-sm"
                >
                  <CheckCircle className="mt-0.5 size-3 shrink-0 text-green-600" />
                  {recommendation}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action Button */}
        {onExpandDetails && (
          <Button
            variant="outline"
            onClick={onExpandDetails}
            className="w-full"
          >
            View Detailed Measurement Analysis
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
