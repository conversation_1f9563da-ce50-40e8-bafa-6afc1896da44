// Demo steps for optimization creation process
export const optimizationDemoSteps = [
  {
    id: "welcome",
    title: "Welcome to the Demo!",
    description:
      "Let's create your first optimization together. This is the real platform with guided assistance.",
    target: "[data-demo='wizard-container']",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 4000,
    wizardStep: 0
  },
  {
    id: "step-1-basic-info",
    title: "Step 1: Basic Information",
    description:
      "First, we'll give our optimization a name and description. This helps you organize multiple optimizations.",
    target: "[data-demo='basic-info-section']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 0
  },
  {
    id: "optimization-name",
    title: "Enter Optimization Name",
    description:
      "Let's call this 'My First Demo Optimization'. You can change this to anything meaningful for your project.",
    target: "input#name",
    position: "bottom" as const,
    action: "type" as const,
    content: "My First Demo Optimization",
    duration: 4000,
    wizardStep: 0
  },
  {
    id: "optimization-description",
    title: "Add Description (Optional)",
    description:
      "Descriptions help you remember what this optimization is for. Let's add a brief description.",
    target: "textarea#description",
    position: "bottom" as const,
    action: "type" as const,
    content: "Demo optimization to learn the platform features",
    duration: 4000,
    wizardStep: 0
  },
  {
    id: "next-to-target",
    title: "Continue to Target Configuration",
    description:
      "Great! Now let's move to the next step to configure our optimization target. Click 'Next' to continue.",
    target: "[data-demo='next-button']",
    position: "top" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 0
  },
  {
    id: "target-config",
    title: "Step 2: Target Configuration",
    description:
      "Now we define what we want to optimize. This could be yield, efficiency, cost, or any measurable outcome.",
    target: "[data-demo='target-config-section']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 3500,
    wizardStep: 1
  },
  {
    id: "objective-type",
    title: "Choose Objective Type",
    description:
      "Single target means we're optimizing for one goal. Multi-target allows optimizing multiple objectives simultaneously.",
    target: "[data-demo='objective-type-section']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 3500,
    wizardStep: 1
  },
  {
    id: "target-name",
    title: "Target Name",
    description:
      "Let's optimize for 'Yield' - a common optimization target in many industries.",
    target: "input#singleTarget\\.name",
    position: "bottom" as const,
    action: "type" as const,
    content: "Yield",
    duration: 3000,
    wizardStep: 1
  },
  {
    id: "target-mode",
    title: "Optimization Direction",
    description:
      "We want to maximize yield, so we'll select MAX. Choose MIN if you want to minimize something like cost or error.",
    target: "[data-demo='target-mode-select']",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 1
  },
  {
    id: "next-to-acquisition",
    title: "Continue to Acquisition Function",
    description:
      "Perfect! Now let's configure the acquisition function. Click 'Next' to continue.",
    target: "[data-demo='next-button']",
    position: "top" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 1
  },
  {
    id: "acquisition-function",
    title: "Step 3: Acquisition Function",
    description:
      "This controls how the AI chooses the next experiments. Expected Improvement is a great default choice.",
    target: "[data-demo='acquisition-section']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 4000,
    wizardStep: 2
  },
  {
    id: "next-to-parameters",
    title: "Continue to Parameters",
    description:
      "Great! Now let's define our optimization parameters. Click 'Next' to continue.",
    target: "[data-demo='next-button']",
    position: "top" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 2
  },
  {
    id: "parameters-section",
    title: "Step 4: Define Parameters",
    description:
      "Parameters are the variables you can control. These are the 'knobs' you can turn to affect your target. We already have two parameters set up for this demo.",
    target: "[data-demo='parameters-section']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 4000,
    wizardStep: 3
  },
  {
    id: "first-parameter-review",
    title: "First Parameter: Temperature",
    description:
      "Here's our first parameter 'Temperature' - already configured as a continuous variable. Notice it's set to 'Numerical Continuous' type.",
    target: "input#parameters\\.0\\.name",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 3
  },
  {
    id: "first-parameter-type",
    title: "Parameter Type",
    description:
      "Temperature is continuous (can be any value within a range), so it's set to 'Numerical Continuous'.",
    target: "[data-demo='parameter-type-0']",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 3
  },
  {
    id: "first-parameter-bounds",
    title: "Temperature Bounds",
    description:
      "The bounds define the range for temperature: 20°C to 100°C. This tells the optimizer what values to explore.",
    target: "input#parameters\\.0\\.bounds",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 3500,
    wizardStep: 3
  },
  {
    id: "second-parameter-review",
    title: "Second Parameter: Pressure",
    description:
      "Here's our second parameter 'Pressure' - also configured as a continuous variable with its own range.",
    target: "input#parameters\\.1\\.name",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 3
  },
  {
    id: "second-parameter-bounds",
    title: "Pressure Range",
    description:
      "The pressure range is set from 1 to 10 bar. With two parameters, the optimizer can explore combinations of temperature and pressure.",
    target: "input#parameters\\.1\\.bounds",
    position: "bottom" as const,
    action: "highlight" as const,
    duration: 3500,
    wizardStep: 3
  },
  {
    id: "parameters-complete",
    title: "Parameters Complete",
    description:
      "Perfect! We have two well-defined parameters. This gives us a 2D optimization space to explore. You could add more parameters if needed.",
    target: "[data-demo='add-parameter-button']",
    position: "left" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 3
  },
  {
    id: "next-to-review",
    title: "Continue to Review",
    description:
      "Perfect! Now let's review our configuration. Click 'Next' to continue.",
    target: "[data-demo='next-button']",
    position: "top" as const,
    action: "highlight" as const,
    duration: 3000,
    wizardStep: 3
  },
  {
    id: "review-step",
    title: "Step 5: Review Your Configuration",
    description:
      "Let's review everything we've set up. This is your chance to make any final adjustments.",
    target: "[data-demo='review-section']",
    position: "right" as const,
    action: "highlight" as const,
    duration: 4000,
    wizardStep: 4
  },
  {
    id: "create-optimization",
    title: "Complete the Demo!",
    description:
      "Everything looks good! Click 'Complete Demo' to finish this guided tour. This will show you how the final step works and then redirect you to the dashboard.",
    target: "[data-demo='create-button']",
    position: "top" as const,
    action: "highlight" as const,
    duration: 5000,
    wizardStep: 4
  }
]

// Demo data to pre-populate the form
export const demoFormData = {
  name: "My First Demo Optimization",
  description: "Demo optimization to learn the platform features",
  objectiveType: "SINGLE" as const,
  singleTarget: {
    name: "Yield",
    mode: "MAX" as const
  },
  multiTargets: [
    {
      name: "Target 1",
      mode: "MAX" as const,
      weight: 50,
      lowerBound: 0,
      upperBound: 100
    },
    {
      name: "Target 2",
      mode: "MAX" as const,
      weight: 50,
      lowerBound: 0,
      upperBound: 100
    }
  ],
  parameters: [
    {
      name: "Temperature",
      type: "NumericalContinuous" as const,
      bounds: "20,100",
      values: "",
      encoding: "OHE" as const,
      tolerance: ""
    },
    {
      name: "Pressure",
      type: "NumericalContinuous" as const,
      bounds: "1,10",
      values: "",
      encoding: "OHE" as const,
      tolerance: ""
    }
  ],
  acquisitionFunction: {
    type: "qExpectedImprovement" as const
    // Note: beta is only included for qUpperConfidenceBound
  }
}

// Industry-specific demo variations
export const industryDemoVariations = {
  "Pharmaceutical/Biotech": {
    name: "Drug Formulation Optimization",
    description:
      "Optimize drug formulation parameters for maximum bioavailability",
    objectiveType: "SINGLE" as const,
    singleTarget: { name: "Bioavailability", mode: "MAX" as const },
    multiTargets: [
      {
        name: "Target 1",
        mode: "MAX" as const,
        weight: 50,
        lowerBound: 0,
        upperBound: 100
      },
      {
        name: "Target 2",
        mode: "MAX" as const,
        weight: 50,
        lowerBound: 0,
        upperBound: 100
      }
    ],
    parameters: [
      {
        name: "API Concentration",
        type: "NumericalContinuous" as const,
        bounds: "1,20",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      },
      {
        name: "pH",
        type: "NumericalContinuous" as const,
        bounds: "3,9",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      },
      {
        name: "Excipient Ratio",
        type: "NumericalContinuous" as const,
        bounds: "0.1,2.0",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      }
    ],
    acquisitionFunction: {
      type: "qExpectedImprovement" as const
    }
  },
  Manufacturing: {
    name: "Process Efficiency Optimization",
    description:
      "Optimize manufacturing process for maximum efficiency and minimum waste",
    objectiveType: "SINGLE" as const,
    singleTarget: { name: "Efficiency", mode: "MAX" as const },
    multiTargets: [
      {
        name: "Target 1",
        mode: "MAX" as const,
        weight: 50,
        lowerBound: 0,
        upperBound: 100
      },
      {
        name: "Target 2",
        mode: "MAX" as const,
        weight: 50,
        lowerBound: 0,
        upperBound: 100
      }
    ],
    parameters: [
      {
        name: "Speed",
        type: "NumericalContinuous" as const,
        bounds: "10,100",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      },
      {
        name: "Temperature",
        type: "NumericalContinuous" as const,
        bounds: "150,300",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      },
      {
        name: "Pressure",
        type: "NumericalContinuous" as const,
        bounds: "1,15",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      }
    ],
    acquisitionFunction: {
      type: "qExpectedImprovement" as const
    }
  },
  "Technology/Software": {
    name: "Algorithm Performance Optimization",
    description:
      "Optimize machine learning hyperparameters for best performance",
    objectiveType: "SINGLE" as const,
    singleTarget: { name: "Accuracy", mode: "MAX" as const },
    multiTargets: [
      {
        name: "Target 1",
        mode: "MAX" as const,
        weight: 50,
        lowerBound: 0,
        upperBound: 100
      },
      {
        name: "Target 2",
        mode: "MAX" as const,
        weight: 50,
        lowerBound: 0,
        upperBound: 100
      }
    ],
    parameters: [
      {
        name: "Learning Rate",
        type: "NumericalContinuous" as const,
        bounds: "0.001,0.1",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      },
      {
        name: "Hidden Units",
        type: "NumericalContinuous" as const,
        bounds: "16,128",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      },
      {
        name: "Regularization",
        type: "NumericalContinuous" as const,
        bounds: "0.0001,0.01",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      }
    ],
    acquisitionFunction: {
      type: "qExpectedImprovement" as const
    }
  },
  "Academic/Research": {
    name: "Experimental Design Optimization",
    description: "Optimize experimental conditions for maximum response",
    objectiveType: "SINGLE" as const,
    singleTarget: { name: "Response", mode: "MAX" as const },
    multiTargets: [
      {
        name: "Target 1",
        mode: "MAX" as const,
        weight: 50,
        lowerBound: 0,
        upperBound: 100
      },
      {
        name: "Target 2",
        mode: "MAX" as const,
        weight: 50,
        lowerBound: 0,
        upperBound: 100
      }
    ],
    parameters: [
      {
        name: "Factor A",
        type: "NumericalContinuous" as const,
        bounds: "0,10",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      },
      {
        name: "Factor B",
        type: "NumericalContinuous" as const,
        bounds: "5,25",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      },
      {
        name: "Factor C",
        type: "NumericalContinuous" as const,
        bounds: "0.1,1.0",
        values: "",
        encoding: "OHE" as const,
        tolerance: ""
      }
    ],
    acquisitionFunction: {
      type: "qExpectedImprovement" as const
    }
  }
}
