"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { DiscretizationTransparency } from "@/components/ui/discretization-transparency"

// Mock discretization data for testing
const mockDiscretizationInfo = {
  has_discretization: true,
  discrete_parameters: [
    {
      parameter: "x1",
      type: "NumericalDiscrete",
      allowed_values: [12, 78, 101],
      original_values: [45.3341, 13.832, 28.323],
      discretized_values: [78.0, 12.0, 12.0],
      has_changes: true
    },
    {
      parameter: "x2",
      type: "NumericalDiscrete",
      allowed_values: [10, 20, 30, 40, 50],
      original_values: [48.0286, 48.7964, 22.1697],
      discretized_values: [50.0, 50.0, 20.0],
      has_changes: true
    },
    {
      parameter: "x3",
      type: "NumericalDiscrete",
      allowed_values: [10, 100],
      original_values: [75.8795, 84.9198, 57.2281],
      discretized_values: [100.0, 100.0, 100.0],
      has_changes: true
    }
  ],
  sample_transparency: [
    {
      sample_index: 0,
      parameter_changes: {
        x1: {
          original: 45.3341,
          discretized: 78.0,
          allowed_values: [12, 78, 101]
        },
        x2: {
          original: 48.0286,
          discretized: 50.0,
          allowed_values: [10, 20, 30, 40, 50]
        },
        x3: {
          original: 75.8795,
          discretized: 100.0,
          allowed_values: [10, 100]
        }
      }
    },
    {
      sample_index: 1,
      parameter_changes: {
        x1: {
          original: 13.832,
          discretized: 12.0,
          allowed_values: [12, 78, 101]
        },
        x2: {
          original: 48.7964,
          discretized: 50.0,
          allowed_values: [10, 20, 30, 40, 50]
        },
        x3: {
          original: 84.9198,
          discretized: 100.0,
          allowed_values: [10, 100]
        }
      }
    },
    {
      sample_index: 2,
      parameter_changes: {
        x1: {
          original: 28.323,
          discretized: 12.0,
          allowed_values: [12, 78, 101]
        },
        x2: {
          original: 22.1697,
          discretized: 20.0,
          allowed_values: [10, 20, 30, 40, 50]
        },
        x3: {
          original: 57.2281,
          discretized: 100.0,
          allowed_values: [10, 100]
        }
      }
    }
  ]
}

export default function TestDiscretizationPage() {
  return (
    <div className="container mx-auto space-y-8 py-8">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">Discretization Transparency Test</h1>
        <p className="text-muted-foreground">
          This page demonstrates the discretization transparency feature that
          shows users when discrete parameter values have been rounded from
          continuous values.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Test Case 1: With Discretization */}
        <Card>
          <CardHeader>
            <CardTitle>Test Case 1: With Discretization Changes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4 text-sm">
              This shows the transparency component when discrete parameters
              were rounded.
            </p>
            <DiscretizationTransparency
              discretizationInfo={mockDiscretizationInfo}
            />
          </CardContent>
        </Card>

        {/* Test Case 2: No Discretization */}
        <Card>
          <CardHeader>
            <CardTitle>Test Case 2: No Discretization Changes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4 text-sm">
              This shows what happens when no discretization occurred (component
              should not render).
            </p>
            <DiscretizationTransparency
              discretizationInfo={{
                has_discretization: false,
                discrete_parameters: [],
                sample_transparency: []
              }}
            />
            <p className="text-muted-foreground mt-2 text-xs">
              ↑ No component should be visible above this text
            </p>
          </CardContent>
        </Card>

        {/* Test Case 3: Undefined */}
        <Card>
          <CardHeader>
            <CardTitle>Test Case 3: Undefined Discretization Info</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4 text-sm">
              This shows what happens when discretization info is undefined.
            </p>
            <DiscretizationTransparency discretizationInfo={undefined} />
            <p className="text-muted-foreground mt-2 text-xs">
              ↑ No component should be visible above this text
            </p>
          </CardContent>
        </Card>

        {/* Sample Data Display */}
        <Card>
          <CardHeader>
            <CardTitle>Mock Sample Data</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <h4 className="font-medium">
                Generated Samples (after discretization):
              </h4>
              <div className="grid gap-2">
                <div className="grid grid-cols-4 gap-2 border-b pb-2 text-sm font-medium">
                  <span>Sample</span>
                  <span>x1</span>
                  <span>x2</span>
                  <span>x3</span>
                </div>
                {[0, 1, 2].map(i => (
                  <div key={i} className="grid grid-cols-4 gap-2 text-sm">
                    <span>#{i + 1}</span>
                    <span className="font-mono">
                      {
                        mockDiscretizationInfo.discrete_parameters[0]
                          .discretized_values[i]
                      }
                    </span>
                    <span className="font-mono">
                      {
                        mockDiscretizationInfo.discrete_parameters[1]
                          .discretized_values[i]
                      }
                    </span>
                    <span className="font-mono">
                      {
                        mockDiscretizationInfo.discrete_parameters[2]
                          .discretized_values[i]
                      }
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
