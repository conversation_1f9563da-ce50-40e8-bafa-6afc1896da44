import { Metadata } from "next"
import { SecurityPolicyContent } from "@/components/security/security-policy-content"

export const metadata: Metadata = {
  title: "Information Security Policy | INNOptimizer™",
  description:
    "Comprehensive information security policy governing data protection, access controls, and security measures for INNOptimizer™ platform.",
  keywords: [
    "security policy",
    "information security",
    "data protection",
    "compliance",
    "GDPR",
    "security governance"
  ],
  openGraph: {
    title: "Information Security Policy | INNOptimizer™",
    description:
      "Our comprehensive information security policy and governance framework.",
    type: "website"
  }
}

export default function SecurityPolicyPage() {
  return (
    <div className="min-h-screen">
      <SecurityPolicyContent />
    </div>
  )
}
