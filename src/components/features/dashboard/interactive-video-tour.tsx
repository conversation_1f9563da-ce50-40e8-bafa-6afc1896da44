"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Play,
  Pause,
  RotateCcw,
  Volume2,
  VolumeX,
  Maximize,
  MousePointer,
  Keyboard,
  Eye
} from "lucide-react"
import { cn } from "@/lib/utils"

interface InteractionStep {
  id: string
  type: "click" | "type" | "hover" | "scroll" | "wait"
  selector?: string
  text?: string
  description: string
  duration: number
  x?: number
  y?: number
}

interface VideoTourData {
  id: string
  title: string
  description: string
  totalDuration: number
  steps: InteractionStep[]
}

// Sample tour data - this would come from a CMS or API
const sampleTour: VideoTourData = {
  id: "create-optimization",
  title: "Creating Your First Optimization",
  description:
    "Watch as we create a complete optimization from start to finish",
  totalDuration: 45000, // 45 seconds
  steps: [
    {
      id: "step-1",
      type: "click",
      selector: "[data-tour='create-button']",
      description: "Click the 'Create Optimization' button",
      duration: 2000,
      x: 200,
      y: 100
    },
    {
      id: "step-2",
      type: "type",
      selector: "[data-tour='name-input']",
      text: "My First Optimization",
      description: "Enter a name for your optimization",
      duration: 3000
    },
    {
      id: "step-3",
      type: "click",
      selector: "[data-tour='target-dropdown']",
      description: "Select your optimization target",
      duration: 2000,
      x: 300,
      y: 200
    },
    {
      id: "step-4",
      type: "type",
      selector: "[data-tour='parameter-input']",
      text: "Temperature",
      description: "Add your first parameter",
      duration: 2500
    },
    {
      id: "step-5",
      type: "click",
      selector: "[data-tour='launch-button']",
      description: "Launch your optimization",
      duration: 1500,
      x: 400,
      y: 350
    }
  ]
}

interface InteractiveVideoTourProps {
  tour: VideoTourData
  onComplete?: () => void
  className?: string
}

export function InteractiveVideoTour({
  tour,
  onComplete,
  className
}: InteractiveVideoTourProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 })
  const [showCursor, setShowCursor] = useState(false)
  const [currentText, setCurrentText] = useState("")

  const containerRef = useRef<HTMLDivElement>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Calculate total progress
  const totalProgress = (currentStep / tour.steps.length) * 100

  // Handle play/pause
  const togglePlayback = () => {
    if (isPlaying) {
      pause()
    } else {
      play()
    }
  }

  const play = () => {
    setIsPlaying(true)
    if (currentStep < tour.steps.length) {
      executeStep(tour.steps[currentStep])
    }
  }

  const pause = () => {
    setIsPlaying(false)
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  const reset = () => {
    pause()
    setCurrentStep(0)
    setProgress(0)
    setCursorPosition({ x: 0, y: 0 })
    setShowCursor(false)
    setCurrentText("")
  }

  // Execute a single step
  const executeStep = (step: InteractionStep) => {
    setShowCursor(true)

    // Animate cursor to position if coordinates provided
    if (step.x !== undefined && step.y !== undefined) {
      animateCursor(step.x, step.y)
    }

    // Execute step based on type
    switch (step.type) {
      case "click":
        setTimeout(() => {
          // Simulate click animation
          setShowCursor(false)
          setTimeout(() => setShowCursor(true), 200)
        }, 1000)
        break

      case "type":
        if (step.text) {
          animateTyping(step.text, step.duration)
        }
        break

      case "hover":
        // Simulate hover effect
        break

      case "wait":
        // Just wait for the duration
        break
    }

    // Move to next step after duration
    setTimeout(() => {
      if (currentStep < tour.steps.length - 1) {
        setCurrentStep(prev => prev + 1)
        executeStep(tour.steps[currentStep + 1])
      } else {
        // Tour completed
        setIsPlaying(false)
        setShowCursor(false)
        onComplete?.()
      }
    }, step.duration)
  }

  // Animate cursor movement
  const animateCursor = (targetX: number, targetY: number) => {
    const startX = cursorPosition.x
    const startY = cursorPosition.y
    const duration = 1000 // 1 second animation
    const startTime = Date.now()

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      // Easing function for smooth movement
      const easeProgress = 1 - Math.pow(1 - progress, 3)

      const currentX = startX + (targetX - startX) * easeProgress
      const currentY = startY + (targetY - startY) * easeProgress

      setCursorPosition({ x: currentX, y: currentY })

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    requestAnimationFrame(animate)
  }

  // Animate typing effect
  const animateTyping = (text: string, duration: number) => {
    const chars = text.split("")
    const charDuration = duration / chars.length
    let currentIndex = 0

    const typeChar = () => {
      if (currentIndex < chars.length) {
        setCurrentText(prev => prev + chars[currentIndex])
        currentIndex++
        setTimeout(typeChar, charDuration)
      }
    }

    setCurrentText("")
    typeChar()
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  const currentStepData = tour.steps[currentStep]

  return (
    <Card className={cn("relative flex flex-col overflow-hidden", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="min-w-0 flex-1">
            <CardTitle className="truncate text-sm">{tour.title}</CardTitle>
            <CardDescription className="truncate text-xs">
              {tour.description}
            </CardDescription>
          </div>
          <Badge variant="outline" className="ml-2 text-xs">
            {Math.round(tour.totalDuration / 1000)}s
          </Badge>
        </div>
        <Progress value={totalProgress} className="h-1 w-full" />
      </CardHeader>

      <CardContent className="flex flex-1 flex-col space-y-2">
        {/* Video Viewport */}
        <div
          ref={containerRef}
          className="relative min-h-0 w-full flex-1 overflow-hidden rounded border bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20"
        >
          {/* Simulated UI Elements */}
          <div className="absolute inset-4 space-y-4">
            <div className="flex h-8 items-center rounded bg-white/80 px-3 shadow-sm">
              <div className="mr-2 size-3 rounded-full bg-red-400"></div>
              <div className="mr-2 size-3 rounded-full bg-yellow-400"></div>
              <div className="mr-2 size-3 rounded-full bg-green-400"></div>
              <span className="ml-2 text-xs text-gray-600">
                INNOptimizer Dashboard
              </span>
            </div>

            <div className="grid grid-cols-3 gap-2">
              <div
                data-tour="create-button"
                className="flex h-16 items-center justify-center rounded bg-white/80 shadow-sm"
              >
                <span className="text-xs">Create Optimization</span>
              </div>
              <div className="h-16 rounded bg-white/60"></div>
              <div className="h-16 rounded bg-white/60"></div>
            </div>

            <div className="space-y-2">
              <input
                data-tour="name-input"
                className="h-8 w-full rounded border bg-white/80 px-2 text-xs"
                placeholder="Optimization name..."
                value={currentText}
                readOnly
              />
              <div
                data-tour="target-dropdown"
                className="flex h-8 items-center rounded border bg-white/80 px-2"
              >
                <span className="text-xs text-gray-500">Select target...</span>
              </div>
              <input
                data-tour="parameter-input"
                className="h-8 w-full rounded border bg-white/80 px-2 text-xs"
                placeholder="Parameter name..."
              />
            </div>

            <div className="flex justify-end">
              <div
                data-tour="launch-button"
                className="rounded bg-blue-500 px-4 py-2 text-xs text-white"
              >
                Launch
              </div>
            </div>
          </div>

          {/* Animated Cursor */}
          {showCursor && (
            <div
              className="pointer-events-none absolute z-10 transition-all duration-300"
              style={{
                left: cursorPosition.x,
                top: cursorPosition.y,
                transform: "translate(-2px, -2px)"
              }}
            >
              <MousePointer className="size-6 text-blue-600 drop-shadow-lg" />
            </div>
          )}

          {/* Step Description Overlay */}
          {currentStepData && isPlaying && (
            <div className="absolute inset-x-4 bottom-4 rounded-lg bg-black/80 p-3 text-white">
              <div className="flex items-center gap-2">
                {currentStepData.type === "click" && (
                  <MousePointer className="size-4" />
                )}
                {currentStepData.type === "type" && (
                  <Keyboard className="size-4" />
                )}
                {currentStepData.type === "hover" && <Eye className="size-4" />}
                <span className="text-sm">{currentStepData.description}</span>
              </div>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={togglePlayback}
              className="h-7 px-2"
            >
              {isPlaying ? (
                <Pause className="size-3" />
              ) : (
                <Play className="size-3" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={reset}
              className="h-7 px-2"
            >
              <RotateCcw className="size-3" />
            </Button>
          </div>

          <div className="text-muted-foreground text-xs">
            Step {currentStep + 1}/{tour.steps.length}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
