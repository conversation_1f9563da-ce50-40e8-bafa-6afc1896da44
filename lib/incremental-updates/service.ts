import {
  UpdateOperation,
  UpdateBatch,
  IncrementalUpdateStrategy,
  UpdateContext,
  UpdateResult,
  UpdateComplexity,
  UpdateType,
  UpdatePerformanceMetrics
} from "./types"
import { IncrementalUpdateAnalyzer } from "./analyzer"

export class IncrementalUpdateService {
  private static instance: IncrementalUpdateService
  private updateQueue: Map<string, UpdateBatch> = new Map()
  private performanceMetrics: UpdatePerformanceMetrics[] = []

  private constructor() {}

  static getInstance(): IncrementalUpdateService {
    if (!IncrementalUpdateService.instance) {
      IncrementalUpdateService.instance = new IncrementalUpdateService()
    }
    return IncrementalUpdateService.instance
  }

  /**
   * Plan and execute incremental updates
   */
  async executeIncrementalUpdate(
    optimizationId: string,
    currentConfig: any,
    targetConfig: any,
    context: UpdateContext
  ): Promise<UpdateResult> {
    const startTime = new Date()

    try {
      console.log("🔧 INCREMENTAL UPDATE: Starting execution")
      console.log("  - Current config:", currentConfig)
      console.log("  - Target config:", targetConfig)

      // 🔧 FIX: Check if this is a complex update that requires full configuration
      const hasConstraintChanges = this.hasConstraintChanges(
        currentConfig,
        targetConfig
      )
      const hasParameterStructureChanges = this.hasParameterStructureChanges(
        currentConfig,
        targetConfig
      )

      if (hasConstraintChanges || hasParameterStructureChanges) {
        console.log(
          "🔧 INCREMENTAL UPDATE: Detected complex changes, using full configuration update"
        )
        return await this.executeFullConfigurationUpdate(
          optimizationId,
          targetConfig,
          context,
          startTime
        )
      }

      // 1. Analyze configuration changes
      const strategy = IncrementalUpdateAnalyzer.analyzeConfigurationChanges(
        currentConfig,
        targetConfig,
        context
      )

      // 2. Validate operations
      const allOperations = [
        ...strategy.simpleUpdates,
        ...strategy.moderateUpdates,
        ...strategy.complexUpdates
      ]

      const validation = IncrementalUpdateAnalyzer.validateUpdateOperations(
        allOperations,
        context
      )

      if (!validation.isValid) {
        return {
          success: false,
          operationsApplied: [],
          operationsFailed: allOperations,
          timeTaken: Date.now() - startTime.getTime(),
          measurementsAffected: 0,
          modelRetrained: false,
          campaignRecreated: false,
          warnings: validation.warnings.map(w => w.message),
          errors: validation.errors.map(e => e.message),
          nextRecommendedActions: ["Fix validation errors and retry"]
        }
      }

      // 3. Execute update strategy
      return await this.executeUpdateStrategy(
        optimizationId,
        strategy,
        context,
        startTime
      )
    } catch (error) {
      return {
        success: false,
        operationsApplied: [],
        operationsFailed: [],
        timeTaken: Date.now() - startTime.getTime(),
        measurementsAffected: 0,
        modelRetrained: false,
        campaignRecreated: false,
        warnings: [],
        errors: [error instanceof Error ? error.message : "Unknown error"],
        nextRecommendedActions: ["Check error logs and retry"]
      }
    }
  }

  /**
   * Execute the update strategy
   */
  private async executeUpdateStrategy(
    optimizationId: string,
    strategy: IncrementalUpdateStrategy,
    context: UpdateContext,
    startTime: Date
  ): Promise<UpdateResult> {
    const appliedOperations: UpdateOperation[] = []
    const failedOperations: UpdateOperation[] = []
    let modelRetrained = false
    let campaignRecreated = false
    let measurementsAffected = 0

    // Execute according to the execution plan
    for (const phase of strategy.executionPlan) {
      try {
        const phaseResult = await this.executePhase(
          optimizationId,
          phase.phase,
          phase.operations,
          context
        )

        appliedOperations.push(...phaseResult.appliedOperations)
        failedOperations.push(...phaseResult.failedOperations)

        if (phaseResult.modelRetrained) modelRetrained = true
        if (phaseResult.campaignRecreated) campaignRecreated = true
        measurementsAffected += phaseResult.measurementsAffected

        // If any operation in this phase failed and it's critical, stop execution
        if (
          phaseResult.failedOperations.length > 0 &&
          phase.phase === "complex"
        ) {
          break
        }
      } catch (error) {
        // Phase execution failed
        failedOperations.push(...phase.operations)
        break
      }
    }

    return {
      success: failedOperations.length === 0,
      operationsApplied: appliedOperations,
      operationsFailed: failedOperations,
      timeTaken: Date.now() - startTime.getTime(),
      measurementsAffected,
      modelRetrained,
      campaignRecreated,
      warnings: [],
      errors: failedOperations.map(
        op => `Failed to apply ${op.type} for ${op.field}`
      ),
      nextRecommendedActions: this.generateNextActions(
        appliedOperations,
        failedOperations
      )
    }
  }

  /**
   * Execute a specific phase of updates
   */
  private async executePhase(
    optimizationId: string,
    phase: "simple" | "moderate" | "complex",
    operations: UpdateOperation[],
    context: UpdateContext
  ): Promise<{
    appliedOperations: UpdateOperation[]
    failedOperations: UpdateOperation[]
    modelRetrained: boolean
    campaignRecreated: boolean
    measurementsAffected: number
  }> {
    const appliedOperations: UpdateOperation[] = []
    const failedOperations: UpdateOperation[] = []
    let modelRetrained = false
    let campaignRecreated = false
    let measurementsAffected = 0

    switch (phase) {
      case "simple":
        // Simple updates can be applied directly without model retraining
        for (const operation of operations) {
          try {
            await this.applySimpleUpdate(optimizationId, operation)
            appliedOperations.push(operation)
          } catch (error) {
            failedOperations.push(operation)
          }
        }
        break

      case "moderate":
        // Moderate updates require model retraining but not recreation
        if (operations.length > 0) {
          try {
            await this.applyModerateUpdates(optimizationId, operations)
            appliedOperations.push(...operations)
            modelRetrained = true
            measurementsAffected = context.measurementCount
          } catch (error) {
            failedOperations.push(...operations)
          }
        }
        break

      case "complex":
        // Complex updates require full campaign recreation
        if (operations.length > 0) {
          try {
            await this.applyComplexUpdates(optimizationId, operations, context)
            appliedOperations.push(...operations)
            campaignRecreated = true
            modelRetrained = true
            measurementsAffected = context.measurementCount
          } catch (error) {
            failedOperations.push(...operations)
          }
        }
        break
    }

    return {
      appliedOperations,
      failedOperations,
      modelRetrained,
      campaignRecreated,
      measurementsAffected
    }
  }

  /**
   * Apply simple updates (bounds changes that don't require retraining)
   */
  private async applySimpleUpdate(
    optimizationId: string,
    operation: UpdateOperation
  ): Promise<void> {
    const startTime = new Date()

    try {
      // For simple bounds updates, we can potentially update the campaign in-place
      // This would require backend support for incremental bounds updates

      if (operation.type === UpdateType.PARAMETER_BOUNDS) {
        await this.updateParameterBounds(optimizationId, operation)
      } else if (operation.type === UpdateType.TARGET_BOUNDS) {
        await this.updateTargetBounds(optimizationId, operation)
      }

      // Record performance metrics
      this.recordPerformanceMetrics({
        operationId: operation.id,
        startTime,
        endTime: new Date(),
        duration: Date.now() - startTime.getTime(),
        memoryUsage: 0, // Would need actual measurement
        cpuUsage: 0, // Would need actual measurement
        measurementsProcessed: 0,
        success: true
      })
    } catch (error) {
      this.recordPerformanceMetrics({
        operationId: operation.id,
        startTime,
        endTime: new Date(),
        duration: Date.now() - startTime.getTime(),
        memoryUsage: 0,
        cpuUsage: 0,
        measurementsProcessed: 0,
        success: false,
        errorMessage: error instanceof Error ? error.message : "Unknown error"
      })
      throw error
    }
  }

  /**
   * Apply moderate updates (require model retraining)
   * TODO: Implement true incremental retraining API for better performance
   */
  private async applyModerateUpdates(
    optimizationId: string,
    operations: UpdateOperation[]
  ): Promise<void> {
    console.log("⚙️ IncrementalUpdateService: Applying moderate updates")
    console.log("  - Operations:", operations)

    // For moderate updates, we need to retrain the model but can keep the campaign structure
    // This would require backend support for model retraining without full recreation

    const updateRequest = this.buildUpdateRequest(operations)
    console.log("  - Update request:", updateRequest)

    // 🔧 FIX: For moderate updates, only send the specific bounds changes
    // since these don't require full configuration recreation
    const response = await fetch(
      `/api/optimizations/${optimizationId}/bounds`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          ...updateRequest,
          preview_only: false
        })
      }
    )

    console.log(
      "⚙️ IncrementalUpdateService: Moderate update response:",
      response.status
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error(
        "⚙️ IncrementalUpdateService: Moderate update failed:",
        errorText
      )
      throw new Error(
        `Moderate update failed: ${response.statusText} - ${errorText}`
      )
    }

    const result = await response.json()
    console.log("⚙️ IncrementalUpdateService: Moderate update result:", result)
  }

  /**
   * Apply complex updates (require full recreation)
   */
  private async applyComplexUpdates(
    optimizationId: string,
    operations: UpdateOperation[],
    context: UpdateContext
  ): Promise<void> {
    console.log(
      "🔧 COMPLEX UPDATE FIX: Applying complex updates with full configuration"
    )
    console.log("  - Operations:", operations)
    console.log("  - Target Configuration:", context.targetConfiguration)

    // 🔧 FIX: For complex updates, send the complete target configuration
    // instead of just the operations to ensure all configuration is preserved
    const targetConfig = context.targetConfiguration

    const requestBody: any = {
      preview_only: false
    }

    // Include the complete configuration from target
    if (targetConfig.parameters) {
      requestBody.updated_parameters = targetConfig.parameters
      console.log(
        "🔧 COMPLEX UPDATE: Including updated_parameters:",
        targetConfig.parameters
      )
    }

    if (targetConfig.target_config) {
      requestBody.target_config = targetConfig.target_config
      console.log(
        "🔧 COMPLEX UPDATE: Including target_config:",
        targetConfig.target_config
      )
    }

    if (targetConfig.constraints) {
      requestBody.constraints = targetConfig.constraints
      console.log(
        "🔧 COMPLEX UPDATE: Including constraints:",
        targetConfig.constraints
      )
    }

    if (targetConfig.acquisition_config) {
      requestBody.acquisition_config = targetConfig.acquisition_config
      console.log(
        "🔧 COMPLEX UPDATE: Including acquisition_config:",
        targetConfig.acquisition_config
      )
    }

    if (targetConfig.parameter_order) {
      requestBody.parameter_order = targetConfig.parameter_order
      console.log(
        "🔧 COMPLEX UPDATE: Including parameter_order:",
        targetConfig.parameter_order
      )
    }

    // Also include any parameter/target bounds from operations
    const operationRequest = this.buildUpdateRequest(operations)
    if (operationRequest.parameter_bounds) {
      requestBody.parameter_bounds = operationRequest.parameter_bounds
    }
    if (operationRequest.target_bounds) {
      requestBody.target_bounds = operationRequest.target_bounds
    }

    console.log("🔧 COMPLEX UPDATE: Final request body:", requestBody)

    const response = await fetch(
      `/api/optimizations/${optimizationId}/bounds`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(requestBody)
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error("🔧 COMPLEX UPDATE: Failed:", errorText)
      throw new Error(
        `Complex update failed: ${response.statusText} - ${errorText}`
      )
    }

    const result = await response.json()
    console.log("🔧 COMPLEX UPDATE: Success:", result)
  }

  /**
   * Update parameter bounds incrementally
   * TODO: Implement true incremental API endpoints for better performance
   */
  private async updateParameterBounds(
    optimizationId: string,
    operation: UpdateOperation
  ): Promise<void> {
    console.log("🔧 IncrementalUpdateService: Applying parameter bounds update")
    console.log("  - Operation:", operation)

    // Extract parameter name from field (e.g., "parameter.temperature.bounds" -> "temperature")
    const paramName = operation.field.split(".")[1]

    console.log("  - Parameter name:", paramName)
    console.log("  - New bounds:", operation.newValue)

    // For now, fall back to full bounds update since incremental endpoints don't exist yet
    // TODO: Replace with true incremental endpoint when available
    const response = await fetch(
      `/api/optimizations/${optimizationId}/bounds`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          parameter_bounds: {
            [paramName]: operation.newValue
          },
          preview_only: false
        })
      }
    )

    console.log(
      "🔧 IncrementalUpdateService: Parameter bounds update response:",
      response.status
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error(
        "🔧 IncrementalUpdateService: Parameter bounds update failed:",
        errorText
      )
      throw new Error(
        `Failed to update parameter bounds: ${response.statusText} - ${errorText}`
      )
    }

    const result = await response.json()
    console.log(
      "🔧 IncrementalUpdateService: Parameter bounds update result:",
      result
    )
  }

  /**
   * Update target bounds incrementally
   * TODO: Implement true incremental API endpoints for better performance
   */
  private async updateTargetBounds(
    optimizationId: string,
    operation: UpdateOperation
  ): Promise<void> {
    console.log("🎯 IncrementalUpdateService: Applying target bounds update")
    console.log("  - Operation:", operation)

    // Extract target name from field (e.g., "target.yield.bounds" -> "yield")
    const targetName = operation.field.split(".")[1]

    console.log("  - Target name:", targetName)
    console.log("  - New bounds:", operation.newValue)

    // For now, fall back to full bounds update since incremental endpoints don't exist yet
    // TODO: Replace with true incremental endpoint when available
    const response = await fetch(
      `/api/optimizations/${optimizationId}/bounds`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          target_bounds: {
            [targetName]: operation.newValue
          },
          preview_only: false
        })
      }
    )

    console.log(
      "🎯 IncrementalUpdateService: Target bounds update response:",
      response.status
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error(
        "🎯 IncrementalUpdateService: Target bounds update failed:",
        errorText
      )
      throw new Error(
        `Failed to update target bounds: ${response.statusText} - ${errorText}`
      )
    }

    const result = await response.json()
    console.log(
      "🎯 IncrementalUpdateService: Target bounds update result:",
      result
    )
  }

  /**
   * Build update request from operations
   */
  private buildUpdateRequest(operations: UpdateOperation[]): any {
    console.log(
      "🏗️ IncrementalUpdateService: Building update request from operations"
    )
    console.log("  - Operations to process:", operations)

    const request: any = {}

    operations.forEach(operation => {
      console.log("  📝 Processing operation:", operation)

      if (operation.type === UpdateType.PARAMETER_BOUNDS) {
        if (!request.parameter_bounds) request.parameter_bounds = {}
        const paramName = operation.field.split(".")[1]
        request.parameter_bounds[paramName] = operation.newValue
        console.log(
          `    ✅ Added parameter bounds for ${paramName}:`,
          operation.newValue
        )
      } else if (operation.type === UpdateType.TARGET_BOUNDS) {
        if (!request.target_bounds) request.target_bounds = {}
        const targetName = operation.field.split(".")[1]
        request.target_bounds[targetName] = operation.newValue
        console.log(
          `    ✅ Added target bounds for ${targetName}:`,
          operation.newValue
        )
      } else {
        console.log(`    ⚠️ Unhandled operation type: ${operation.type}`)
      }
      // Add other operation types as needed
    })

    console.log("🏗️ IncrementalUpdateService: Final update request:", request)
    return request
  }

  /**
   * Generate next recommended actions
   */
  private generateNextActions(
    appliedOperations: UpdateOperation[],
    failedOperations: UpdateOperation[]
  ): string[] {
    const actions: string[] = []

    if (failedOperations.length > 0) {
      actions.push("Review and fix failed operations")
      actions.push("Check validation errors and retry")
    }

    if (appliedOperations.some(op => op.requiresModelRetrain)) {
      actions.push("Monitor model performance after retraining")
    }

    if (appliedOperations.length > 0) {
      actions.push("Verify configuration changes are working as expected")
    }

    return actions
  }

  /**
   * Record performance metrics
   */
  private recordPerformanceMetrics(metrics: UpdatePerformanceMetrics): void {
    this.performanceMetrics.push(metrics)

    // Keep only the last 1000 metrics
    if (this.performanceMetrics.length > 1000) {
      this.performanceMetrics = this.performanceMetrics.slice(-1000)
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(operationId?: string): UpdatePerformanceMetrics[] {
    if (operationId) {
      return this.performanceMetrics.filter(m => m.operationId === operationId)
    }
    return [...this.performanceMetrics]
  }

  /**
   * 🔧 FIX: Check if there are constraint changes
   */
  private hasConstraintChanges(currentConfig: any, targetConfig: any): boolean {
    const currentConstraints = currentConfig?.constraints || []
    const targetConstraints = targetConfig?.constraints || []

    console.log("🔧 CONSTRAINT CHECK: Current constraints:", currentConstraints)
    console.log("🔧 CONSTRAINT CHECK: Target constraints:", targetConstraints)

    const hasChanges =
      JSON.stringify(currentConstraints) !== JSON.stringify(targetConstraints)
    console.log("🔧 CONSTRAINT CHECK: Has changes:", hasChanges)

    return hasChanges
  }

  /**
   * 🔧 FIX: Check if there are parameter structure changes
   */
  private hasParameterStructureChanges(
    currentConfig: any,
    targetConfig: any
  ): boolean {
    const currentParams = currentConfig?.parameters || []
    const targetParams = targetConfig?.parameters || []

    // Check if parameter count changed
    if (currentParams.length !== targetParams.length) {
      console.log("🔧 PARAM CHECK: Parameter count changed")
      return true
    }

    // Check if parameter types or structure changed
    for (let i = 0; i < currentParams.length; i++) {
      const current = currentParams[i]
      const target = targetParams[i]

      if (current.name !== target.name || current.type !== target.type) {
        console.log(
          "🔧 PARAM CHECK: Parameter structure changed:",
          current,
          "->",
          target
        )
        return true
      }
    }

    return false
  }

  /**
   * 🔧 FIX: Execute full configuration update for complex changes
   */
  private async executeFullConfigurationUpdate(
    optimizationId: string,
    targetConfig: any,
    context: UpdateContext,
    startTime: Date
  ): Promise<UpdateResult> {
    console.log("🔧 FULL CONFIG UPDATE: Starting full configuration update")
    console.log("  - Target config:", targetConfig)

    try {
      const requestBody: any = {
        preview_only: false
      }

      // Include the complete configuration
      if (targetConfig.parameters) {
        requestBody.updated_parameters = targetConfig.parameters
        console.log(
          "🔧 FULL CONFIG: Including updated_parameters:",
          targetConfig.parameters
        )
      }

      if (targetConfig.target_config) {
        requestBody.target_config = targetConfig.target_config
        console.log(
          "🔧 FULL CONFIG: Including target_config:",
          targetConfig.target_config
        )
      }

      if (targetConfig.constraints) {
        requestBody.constraints = targetConfig.constraints
        console.log(
          "🔧 FULL CONFIG: Including constraints:",
          targetConfig.constraints
        )
      }

      if (targetConfig.acquisition_config) {
        requestBody.acquisition_config = targetConfig.acquisition_config
        console.log(
          "🔧 FULL CONFIG: Including acquisition_config:",
          targetConfig.acquisition_config
        )
      }

      if (targetConfig.parameter_order) {
        requestBody.parameter_order = targetConfig.parameter_order
        console.log(
          "🔧 FULL CONFIG: Including parameter_order:",
          targetConfig.parameter_order
        )
      }

      if (targetConfig.parameter_bounds) {
        requestBody.parameter_bounds = targetConfig.parameter_bounds
      }

      if (targetConfig.target_bounds) {
        requestBody.target_bounds = targetConfig.target_bounds
      }

      console.log("🔧 FULL CONFIG: Final request body:", requestBody)

      const response = await fetch(
        `/api/optimizations/${optimizationId}/bounds`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(requestBody)
        }
      )

      if (!response.ok) {
        const errorText = await response.text()
        console.error("🔧 FULL CONFIG: Failed:", errorText)
        throw new Error(
          `Full configuration update failed: ${response.statusText} - ${errorText}`
        )
      }

      const result = await response.json()
      console.log("🔧 FULL CONFIG: Success:", result)

      return {
        success: true,
        operationsApplied: [
          { id: "full-config-update", type: "FULL_CONFIGURATION_UPDATE" } as any
        ],
        operationsFailed: [],
        timeTaken: Date.now() - startTime.getTime(),
        measurementsAffected: context.measurementCount,
        modelRetrained: true,
        campaignRecreated: true,
        warnings: [],
        errors: [],
        nextRecommendedActions: [
          "Verify configuration changes are working as expected"
        ]
      }
    } catch (error) {
      console.error("🔧 FULL CONFIG: Error:", error)
      return {
        success: false,
        operationsApplied: [],
        operationsFailed: [
          { id: "full-config-update", type: "FULL_CONFIGURATION_UPDATE" } as any
        ],
        timeTaken: Date.now() - startTime.getTime(),
        measurementsAffected: 0,
        modelRetrained: false,
        campaignRecreated: false,
        warnings: [],
        errors: [error instanceof Error ? error.message : "Unknown error"],
        nextRecommendedActions: ["Check logs and retry"]
      }
    }
  }
}

// Export singleton instance
export const incrementalUpdateService = IncrementalUpdateService.getInstance()
