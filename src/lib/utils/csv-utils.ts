// lib/csv-utils.ts

import {
  ParsedDataset,
  DataValidationError,
  UploadedDataRow,
  CSVTemplateConfig
} from "@/types/dataset-types"
import { OptimizationConfig } from "@/types/optimization-types"

/**
 * Parse CSV content into structured data
 */
export function parseCSVContent(csvContent: string): {
  headers: string[]
  rows: UploadedDataRow[]
  errors: DataValidationError[]
} {
  const errors: DataValidationError[] = []
  const lines = csvContent.trim().split("\n")

  if (lines.length === 0) {
    errors.push({
      row: 0,
      column: "",
      value: "",
      message: "CSV file is empty",
      type: "missing"
    })
    return { headers: [], rows: [], errors }
  }

  // Parse headers - ensure clean column names
  const headers = lines[0]
    .split(",")
    .map(h => h.trim().replace(/"/g, "").replace(/\s+/g, " ").trim())

  if (headers.length === 0) {
    errors.push({
      row: 1,
      column: "",
      value: "",
      message: "No headers found in CSV",
      type: "missing"
    })
    return { headers: [], rows: [], errors }
  }

  // Parse data rows
  const rows: UploadedDataRow[] = []

  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue // Skip empty lines

    const values = line.split(",").map(v => v.trim().replace(/"/g, ""))

    if (values.length !== headers.length) {
      errors.push({
        row: i + 1,
        column: "",
        value: line,
        message: `Row has ${values.length} columns but expected ${headers.length}`,
        type: "invalid_value"
      })
      continue
    }

    const row: UploadedDataRow = {}
    headers.forEach((header, index) => {
      const value = values[index]
      // Try to convert to number if it looks like a number
      const numValue = parseFloat(value)
      row[header] = !isNaN(numValue) && value !== "" ? numValue : value
    })

    rows.push(row)
  }

  return { headers, rows, errors }
}

/**
 * Validate parsed data against optimization configuration
 */
export function validateDataset(
  headers: string[],
  rows: UploadedDataRow[],
  optimizationConfig: OptimizationConfig
): DataValidationError[] {
  const errors: DataValidationError[] = []
  const parameters = optimizationConfig.parameters
  const targetConfigs = Array.isArray(optimizationConfig.target_config)
    ? optimizationConfig.target_config
    : [optimizationConfig.target_config]

  console.log("[VALIDATION] CSV headers:", headers)
  console.log(
    "[VALIDATION] Expected parameters:",
    parameters.map(p => p.name)
  )
  console.log(
    "[VALIDATION] Expected targets:",
    targetConfigs.map(t => t.name)
  )

  // Helper function to find column with flexible matching
  const findColumn = (targetName: string, headers: string[]): string | null => {
    const cleanTargetName = targetName.trim()

    // First try exact match
    if (headers.includes(cleanTargetName)) {
      return cleanTargetName
    }

    // Then try case-insensitive match with trimmed spaces
    const match = headers.find(
      h => h.trim().toLowerCase() === cleanTargetName.toLowerCase()
    )
    return match || null
  }

  // Check for required parameter columns
  parameters.forEach(param => {
    const foundColumn = findColumn(param.name, headers)
    if (!foundColumn) {
      errors.push({
        row: 0,
        column: param.name,
        value: "",
        message: `Required parameter column '${param.name}' is missing`,
        type: "missing"
      })
    }
  })

  // Check for required target columns
  targetConfigs.forEach(target => {
    const foundColumn = findColumn(target.name, headers)
    if (!foundColumn) {
      errors.push({
        row: 0,
        column: target.name,
        value: "",
        message: `Required target column '${target.name}' is missing`,
        type: "missing"
      })
    }
  })

  // Validate each row
  rows.forEach((row, rowIndex) => {
    // Validate parameters
    parameters.forEach(param => {
      const columnName = findColumn(param.name, headers)
      const value = columnName ? row[columnName] : undefined

      if (value === undefined || value === null || value === "") {
        errors.push({
          row: rowIndex + 2, // +2 because row 1 is headers and array is 0-indexed
          column: param.name,
          value: value,
          message: `Missing value for parameter '${param.name}'`,
          type: "missing"
        })
        return
      }

      // Validate based on parameter type
      if (
        param.type === "NumericalDiscrete" ||
        param.type === "NumericalContinuous"
      ) {
        const numValue =
          typeof value === "number" ? value : parseFloat(String(value))

        if (isNaN(numValue)) {
          errors.push({
            row: rowIndex + 2,
            column: param.name,
            value: value,
            message: `Invalid numeric value for parameter '${param.name}'`,
            type: "invalid_type"
          })
          return
        }

        // Check bounds for continuous parameters
        if (param.type === "NumericalContinuous" && param.bounds) {
          const [min, max] = param.bounds
          if (numValue < min || numValue > max) {
            errors.push({
              row: rowIndex + 2,
              column: param.name,
              value: value,
              message: `Value ${numValue} for parameter '${param.name}' is outside bounds [${min}, ${max}]`,
              type: "out_of_bounds"
            })
          }
        }

        // Check allowed values for discrete parameters
        if (param.type === "NumericalDiscrete" && param.values) {
          if (!param.values.includes(numValue)) {
            errors.push({
              row: rowIndex + 2,
              column: param.name,
              value: value,
              message: `Value ${numValue} for parameter '${param.name}' is not in allowed values: [${param.values.join(", ")}]`,
              type: "invalid_value"
            })
          }
        }
      } else if (param.type === "CategoricalParameter") {
        const strValue = String(value)
        if (param.values && !param.values.includes(strValue)) {
          errors.push({
            row: rowIndex + 2,
            column: param.name,
            value: value,
            message: `Value '${strValue}' for parameter '${param.name}' is not in allowed values: [${param.values.join(", ")}]`,
            type: "invalid_value"
          })
        }
      }
    })

    // Validate targets
    targetConfigs.forEach(target => {
      const columnName = findColumn(target.name, headers)
      const value = columnName ? row[columnName] : undefined

      if (value === undefined || value === null || value === "") {
        errors.push({
          row: rowIndex + 2,
          column: target.name,
          value: value,
          message: `Missing value for target '${target.name}'`,
          type: "missing"
        })
        return
      }

      const numValue =
        typeof value === "number" ? value : parseFloat(String(value))
      if (isNaN(numValue)) {
        errors.push({
          row: rowIndex + 2,
          column: target.name,
          value: value,
          message: `Invalid numeric value for target '${target.name}'`,
          type: "invalid_type"
        })
      }
    })
  })

  return errors
}

/**
 * Generate CSV template based on optimization configuration
 */
export function generateCSVTemplate(
  optimizationConfig: OptimizationConfig
): string {
  const parameters = optimizationConfig.parameters
  const targetConfigs = Array.isArray(optimizationConfig.target_config)
    ? optimizationConfig.target_config
    : [optimizationConfig.target_config]

  console.log("[CSV_TEMPLATE] Parameters:", parameters)
  console.log("[CSV_TEMPLATE] Target configs:", targetConfigs)

  // Create headers - ensure clean column names without extra spaces
  const headers = [
    ...parameters.map(p => p.name.trim()),
    ...targetConfigs.map(t => t.name.trim())
  ]

  console.log("[CSV_TEMPLATE] Generated headers:", headers)

  // Create example rows
  const exampleRows: string[][] = []

  // Generate 3 example rows
  for (let i = 0; i < 3; i++) {
    const row: string[] = []

    // Add parameter example values
    parameters.forEach(param => {
      if (param.type === "NumericalDiscrete" && param.values) {
        row.push(String(param.values[i % param.values.length]))
      } else if (param.type === "NumericalContinuous" && param.bounds) {
        const [min, max] = param.bounds
        const value = min + ((max - min) * (i + 1)) / 4
        row.push(value.toFixed(2))
      } else if (param.type === "CategoricalParameter" && param.values) {
        row.push(String(param.values[i % param.values.length]))
      } else {
        row.push(`value_${i + 1}`)
      }
    })

    // Add target example values
    targetConfigs.forEach(() => {
      row.push((25.0 + i * 5).toFixed(2))
    })

    exampleRows.push(row)
  }

  // Combine headers and rows - ensure no extra spaces in CSV output
  const csvLines = [headers.join(","), ...exampleRows.map(row => row.join(","))]

  return csvLines.join("\n")
}
