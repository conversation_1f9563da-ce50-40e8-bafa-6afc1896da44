"use client"

import { motion } from "framer-motion"
import { Cloud, Server, Shield, Monitor, Globe, Zap } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const infrastructureComponents = [
  {
    icon: Cloud,
    title: "Cloud Infrastructure",
    description:
      "AWS cloud infrastructure with enterprise-grade security and compliance.",
    features: [
      {
        name: "Provider",
        value: "Amazon Web Services (AWS)",
        status: "active"
      },
      { name: "Regions", value: "Multi-AZ deployment", status: "active" },
      { name: "Availability", value: "99.99% SLA", status: "active" },
      { name: "Compliance", value: "SOC 2, HIPAA eligible", status: "active" }
    ]
  },
  {
    icon: Server,
    title: "Database Security",
    description:
      "AWS RDS PostgreSQL with enterprise-grade security and compliance features.",
    features: [
      { name: "Database", value: "AWS RDS PostgreSQL 15+", status: "active" },
      { name: "Encryption", value: "AWS KMS AES-256", status: "active" },
      {
        name: "Backups",
        value: "Automated with point-in-time recovery",
        status: "active"
      },
      {
        name: "Network",
        value: "VPC isolation with security groups",
        status: "active"
      }
    ]
  },
  {
    icon: Globe,
    title: "Content Delivery",
    description:
      "Global CDN for fast, secure content delivery with DDoS protection.",
    features: [
      { name: "CDN", value: "Global edge network", status: "active" },
      { name: "SSL/TLS", value: "TLS 1.3 everywhere", status: "active" },
      { name: "DDoS Protection", value: "Layer 3/4/7", status: "active" },
      { name: "Caching", value: "Intelligent caching", status: "active" }
    ]
  },
  {
    icon: Monitor,
    title: "Monitoring & Alerting",
    description:
      "24/7 monitoring with real-time alerting and incident response.",
    features: [
      { name: "Uptime Monitoring", value: "24/7 automated", status: "active" },
      {
        name: "Security Monitoring",
        value: "Real-time SIEM",
        status: "active"
      },
      { name: "Performance", value: "APM enabled", status: "active" },
      { name: "Alerting", value: "Multi-channel", status: "active" }
    ]
  }
]

const securityLayers = [
  {
    layer: "Application Layer",
    protections: [
      "Input validation",
      "Authentication",
      "Authorization",
      "Session management"
    ]
  },
  {
    layer: "Network Layer",
    protections: [
      "Firewall rules",
      "VPC isolation",
      "DDoS protection",
      "Traffic encryption"
    ]
  },
  {
    layer: "Infrastructure Layer",
    protections: [
      "Access controls",
      "Patch management",
      "Vulnerability scanning",
      "Compliance monitoring"
    ]
  },
  {
    layer: "Data Layer",
    protections: [
      "Encryption at rest",
      "Backup encryption",
      "Data isolation",
      "Secure deletion"
    ]
  }
]

export function InfrastructureSecurity() {
  return (
    <section className="py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Infrastructure Security
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            Our infrastructure is built on enterprise-grade cloud platforms with
            multiple layers of security, monitoring, and redundancy.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          {infrastructureComponents.map((component, index) => (
            <motion.div
              key={component.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <div className="mb-4 flex size-12 items-center justify-center rounded-lg bg-purple-100">
                    <component.icon className="size-6 text-purple-600" />
                  </div>
                  <CardTitle className="text-xl">{component.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {component.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {component.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="flex items-center justify-between"
                      >
                        <span className="text-sm font-medium text-gray-700">
                          {feature.name}
                        </span>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{feature.value}</Badge>
                          <div className="size-2 rounded-full bg-green-500" />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-12"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">
                Multi-Layer Security Architecture
              </CardTitle>
              <CardDescription>
                Defense in depth with multiple security layers protecting your
                data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {securityLayers.map((layer, index) => (
                  <div
                    key={layer.layer}
                    className="border-l-4 border-blue-500 pl-4"
                  >
                    <h4 className="mb-2 font-semibold text-gray-900">
                      {layer.layer}
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {layer.protections.map((protection, protectionIndex) => (
                        <Badge key={protectionIndex} variant="secondary">
                          {protection}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-8"
        >
          <Card className="bg-gradient-to-r from-purple-50 to-blue-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="mb-4 text-xl font-semibold text-gray-900">
                  Infrastructure Metrics
                </h3>
                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-green-600">
                      99.9%
                    </div>
                    <div className="text-sm text-gray-600">Uptime SLA</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-blue-600">
                      &lt;100ms
                    </div>
                    <div className="text-sm text-gray-600">Response Time</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-purple-600">
                      24/7
                    </div>
                    <div className="text-sm text-gray-600">Monitoring</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-orange-600">
                      Global
                    </div>
                    <div className="text-sm text-gray-600">CDN Coverage</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
