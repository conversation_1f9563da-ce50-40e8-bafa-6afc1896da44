// lib/status-utils.ts
import { OptimizationStatus } from "@/types"

/**
 * Status color mapping for UI components
 */
export const statusColors: Record<OptimizationStatus, string> = {
  active: "bg-green-500",
  paused: "bg-amber-500",
  completed: "bg-blue-500",
  draft: "bg-gray-500",
  failed: "bg-red-500",
  error: "bg-red-500"
}

/**
 * Status labels for display
 */
export const statusLabels: Record<OptimizationStatus, string> = {
  active: "Running",
  paused: "Paused",
  completed: "Completed",
  draft: "Draft",
  failed: "Failed",
  error: "Error"
}

/**
 * Status descriptions
 */
export const statusDescriptions: Record<OptimizationStatus, string> = {
  active: "Optimization is running and accepting new measurements",
  paused: "Optimization is temporarily halted",
  completed: "Optimization has reached its goal or been manually completed",
  draft: "Initial setup, not yet started",
  failed: "Optimization failed to complete successfully",
  error: "Optimization encountered an error"
}

/**
 * Gets the status description for a given status
 */
export function getStatusDescription(status: OptimizationStatus): string {
  return statusDescriptions[status] || "Unknown status"
}

/**
 * Gets the status color for a given status
 */
export function getStatusColor(status: OptimizationStatus): string {
  return statusColors[status] || "bg-gray-500"
}

/**
 * Gets the status label for a given status
 */
export function getStatusLabel(status: OptimizationStatus): string {
  return statusLabels[status] || status
}
