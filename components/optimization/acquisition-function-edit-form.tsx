"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Save,
  X,
  RotateCcw,
  Zap,
  CheckCircle2,
  AlertCircle
} from "lucide-react"
import { getObjectiveType } from "@/lib/optimization-utils"

interface AcquisitionConfig {
  type:
    | "qExpectedImprovement"
    | "qProbabilityOfImprovement"
    | "qUpperConfidenceBound"
    | "qNoisyExpectedHypervolumeImprovement"
    | "qLogNoisyExpectedHypervolumeImprovement"
    | "qLogNParEGO"
  beta?: number
  ref_point?: number[]
  weights?: number[]
  rho?: number
  prune_baseline?: boolean
}

interface AcquisitionFunctionEditFormProps {
  acquisitionConfig: AcquisitionConfig
  onSave: (updatedConfig: AcquisitionConfig) => void
  onCancel: () => void
  className?: string
  optimization?: any // Add optimization prop for objective type detection
}

export function AcquisitionFunctionEditForm({
  acquisitionConfig,
  onSave,
  onCancel,
  className = "",
  optimization
}: AcquisitionFunctionEditFormProps) {
  const [editedConfig, setEditedConfig] =
    useState<AcquisitionConfig>(acquisitionConfig)
  const [hasChanges, setHasChanges] = useState(false)

  // String state for JSON inputs to allow free typing
  const [refPointInput, setRefPointInput] = useState("")
  const [weightsInput, setWeightsInput] = useState("")

  // Validation state
  const [refPointError, setRefPointError] = useState<string | null>(null)
  const [weightsError, setWeightsError] = useState<string | null>(null)

  // Detect objective type from optimization
  const objectiveType = optimization ? getObjectiveType(optimization) : "SINGLE"

  // Reset form when config changes
  useEffect(() => {
    console.log("🔍 AcquisitionFunctionEditForm - Config changed:", {
      newConfig: acquisitionConfig,
      previousEdited: editedConfig
    })
    setEditedConfig(acquisitionConfig)
    setHasChanges(false)

    // Initialize string inputs from config (convert array to comma-separated string)
    setRefPointInput(
      acquisitionConfig.ref_point ? acquisitionConfig.ref_point.join(", ") : ""
    )
    setWeightsInput(
      acquisitionConfig.weights ? acquisitionConfig.weights.join(", ") : ""
    )
    setRefPointError(null)
    setWeightsError(null)
  }, [acquisitionConfig])

  // Check for changes
  useEffect(() => {
    const changed =
      JSON.stringify(editedConfig) !== JSON.stringify(acquisitionConfig)
    setHasChanges(changed)
  }, [editedConfig, acquisitionConfig])

  const handleTypeChange = (type: AcquisitionConfig["type"]) => {
    setEditedConfig(prev => ({
      ...prev,
      type,
      // Clear all parameters first
      beta: undefined,
      ref_point: undefined,
      weights: undefined,
      rho: undefined,
      prune_baseline: undefined,
      // Set appropriate defaults based on function type
      ...(type === "qUpperConfidenceBound" && { beta: prev.beta || 0.2 }),
      ...(type === "qNoisyExpectedHypervolumeImprovement" && {
        ref_point: prev.ref_point
      }),
      ...(type === "qLogNoisyExpectedHypervolumeImprovement" && {
        ref_point: prev.ref_point
      }),
      ...(type === "qLogNParEGO" && { weights: prev.weights })
    }))

    // Reset string inputs and errors when type changes
    if (
      type !== "qNoisyExpectedHypervolumeImprovement" &&
      type !== "qLogNoisyExpectedHypervolumeImprovement"
    ) {
      setRefPointInput("")
      setRefPointError(null)
    }
    if (type !== "qLogNParEGO") {
      setWeightsInput("")
      setWeightsError(null)
    }
  }

  const handleBetaChange = (value: string) => {
    const numValue = parseFloat(value)
    if (!isNaN(numValue) && numValue > 0) {
      setEditedConfig(prev => ({
        ...prev,
        beta: numValue
      }))
    }
  }

  const handleRefPointChange = (value: string) => {
    setRefPointInput(value)

    // Try to parse and validate
    if (value.trim() === "") {
      setRefPointError(null)
      setEditedConfig(prev => ({ ...prev, ref_point: undefined }))
      return
    }

    // Parse comma-separated values
    const values = value
      .split(",")
      .map(v => v.trim())
      .filter(v => v !== "")

    // Check if all values are valid numbers
    const numbers = values.map(v => parseFloat(v))
    const hasInvalidNumbers = numbers.some(n => isNaN(n))

    if (hasInvalidNumbers) {
      setRefPointError("Please enter valid numbers separated by commas")
    } else if (numbers.length === 0) {
      setRefPointError("Please enter at least one value")
    } else {
      setRefPointError(null)
      setEditedConfig(prev => ({ ...prev, ref_point: numbers }))
    }
  }

  const handleWeightsChange = (value: string) => {
    setWeightsInput(value)

    // Try to parse and validate
    if (value.trim() === "") {
      setWeightsError(null)
      setEditedConfig(prev => ({ ...prev, weights: undefined }))
      return
    }

    // Parse comma-separated values
    const values = value
      .split(",")
      .map(v => v.trim())
      .filter(v => v !== "")

    // Check if all values are valid numbers
    const numbers = values.map(v => parseFloat(v))
    const hasInvalidNumbers = numbers.some(n => isNaN(n))

    if (hasInvalidNumbers) {
      setWeightsError("Please enter valid numbers separated by commas")
    } else if (numbers.length === 0) {
      setWeightsError("Please enter at least one value")
    } else {
      // Check if weights sum to 1.0
      const sum = numbers.reduce((a, b) => a + b, 0)
      if (Math.abs(sum - 1.0) > 0.01) {
        setWeightsError(`Weights sum to ${sum.toFixed(3)}, should sum to 1.0`)
      } else {
        setWeightsError(null)
      }
      setEditedConfig(prev => ({ ...prev, weights: numbers }))
    }
  }

  const handleSave = () => {
    onSave(editedConfig)
  }

  const handleReset = () => {
    setEditedConfig(acquisitionConfig)
    setHasChanges(false)
    // Reset string inputs (convert array to comma-separated string)
    setRefPointInput(
      acquisitionConfig.ref_point ? acquisitionConfig.ref_point.join(", ") : ""
    )
    setWeightsInput(
      acquisitionConfig.weights ? acquisitionConfig.weights.join(", ") : ""
    )
    setRefPointError(null)
    setWeightsError(null)
  }

  // Get available acquisition functions based on objective type
  const getAvailableAcquisitionFunctions = (objectiveType: string) => {
    if (objectiveType === "SINGLE") {
      return [
        { value: "qExpectedImprovement", label: "Expected Improvement (EI)" },
        {
          value: "qProbabilityOfImprovement",
          label: "Probability of Improvement (PI)"
        },
        {
          value: "qUpperConfidenceBound",
          label: "Upper Confidence Bound (UCB)"
        }
      ]
    } else if (objectiveType === "MULTI_DESIRABILITY") {
      return [
        { value: "qExpectedImprovement", label: "Expected Improvement (EI)" },
        {
          value: "qProbabilityOfImprovement",
          label: "Probability of Improvement (PI)"
        },
        {
          value: "qUpperConfidenceBound",
          label: "Upper Confidence Bound (UCB)"
        }
      ]
    } else if (objectiveType === "MULTI_PARETO") {
      return [
        {
          value: "qNoisyExpectedHypervolumeImprovement",
          label: "Noisy Expected Hypervolume Improvement (qNEHVI)"
        },
        {
          value: "qLogNoisyExpectedHypervolumeImprovement",
          label: "Log Noisy Expected Hypervolume Improvement (qLogNEHVI)"
        },
        { value: "qLogNParEGO", label: "Log Noisy ParEGO (qLogParEGO)" }
      ]
    }
    return []
  }

  const availableFunctions = getAvailableAcquisitionFunctions(objectiveType)

  const getFunctionDescription = (type: AcquisitionConfig["type"]) => {
    switch (type) {
      case "qExpectedImprovement":
        return "Balances exploration and exploitation by considering both the predicted improvement and uncertainty"
      case "qProbabilityOfImprovement":
        return "Focuses on areas with high probability of improvement over the current best"
      case "qUpperConfidenceBound":
        return "Uses confidence bounds to balance exploration and exploitation with adjustable beta parameter"
      case "qNoisyExpectedHypervolumeImprovement":
        return "Multi-objective acquisition function that maximizes the expected hypervolume improvement for Pareto optimization"
      case "qLogNoisyExpectedHypervolumeImprovement":
        return "Log-space version of qNEHVI for better numerical stability with many objectives"
      case "qLogNParEGO":
        return "Multi-objective acquisition function using scalarization with weighted objectives for Pareto optimization"
      default:
        return ""
    }
  }

  const getFunctionDisplayName = (type: AcquisitionConfig["type"]) => {
    switch (type) {
      case "qExpectedImprovement":
        return "Expected Improvement (EI)"
      case "qProbabilityOfImprovement":
        return "Probability of Improvement (PI)"
      case "qUpperConfidenceBound":
        return "Upper Confidence Bound (UCB)"
      case "qNoisyExpectedHypervolumeImprovement":
        return "Noisy Expected Hypervolume Improvement (qNEHVI)"
      case "qLogNoisyExpectedHypervolumeImprovement":
        return "Log Noisy Expected Hypervolume Improvement (qLogNEHVI)"
      case "qLogNParEGO":
        return "Log Noisy ParEGO (qLogParEGO)"
      default:
        return type
    }
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="space-y-4 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="size-4 text-blue-600" />
            <h4 className="font-medium">Acquisition Function</h4>
            <Badge variant="outline" className="text-xs">
              Advanced
            </Badge>
          </div>
          {hasChanges && (
            <Badge variant="secondary" className="text-xs">
              Modified
            </Badge>
          )}
        </div>

        <div className="text-muted-foreground text-sm">
          Controls how the optimization algorithm explores the parameter space
          and balances exploration vs exploitation.
        </div>

        {/* Objective Type Info */}
        <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
          <p className="text-sm font-medium text-blue-800">
            📊 Detected Objective Type:{" "}
            {objectiveType === "SINGLE"
              ? "Single Target"
              : objectiveType === "MULTI_DESIRABILITY"
                ? "Multi Target (Desirability)"
                : "Multi Target (Pareto)"}{" "}
            - Only compatible acquisition functions are shown.
          </p>
        </div>

        {/* Function Type Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Function Type</Label>
          <Select value={editedConfig.type} onValueChange={handleTypeChange}>
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availableFunctions.map(func => (
                <SelectItem key={func.value} value={func.value}>
                  {func.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <div className="text-muted-foreground text-xs">
            {getFunctionDescription(editedConfig.type)}
          </div>
        </div>

        {/* Beta Parameter for UCB */}
        {editedConfig.type === "qUpperConfidenceBound" && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Beta Parameter</Label>
            <Input
              type="number"
              value={editedConfig.beta || 0.2}
              onChange={e => handleBetaChange(e.target.value)}
              step="0.1"
              min="0.1"
              max="2.0"
              placeholder="0.2"
              className="h-8"
            />
            <div className="text-muted-foreground text-xs">
              Controls exploration vs exploitation trade-off. Higher values
              encourage more exploration. Recommended range: 0.1 - 2.0, Default:
              0.2
            </div>
          </div>
        )}

        {/* Reference Point for qNEHVI and qLogNEHVI */}
        {(editedConfig.type === "qNoisyExpectedHypervolumeImprovement" ||
          editedConfig.type === "qLogNoisyExpectedHypervolumeImprovement") && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Reference Point</Label>
            <div className="relative">
              <Input
                type="text"
                placeholder="e.g., -10, -10 (for 2 objectives)"
                value={refPointInput}
                onChange={e => handleRefPointChange(e.target.value)}
                className={`h-8 pr-8 ${refPointError ? "border-red-500" : refPointInput && !refPointError ? "border-green-500" : ""}`}
              />
              {refPointInput && (
                <div className="absolute right-2 top-1/2 -translate-y-1/2">
                  {refPointError ? (
                    <AlertCircle className="size-4 text-red-500" />
                  ) : (
                    <CheckCircle2 className="size-4 text-green-500" />
                  )}
                </div>
              )}
            </div>
            {refPointError && (
              <div className="flex items-center gap-1 text-xs text-red-600">
                <AlertCircle className="size-3" />
                {refPointError}
              </div>
            )}
            {!refPointError && refPointInput && (
              <div className="flex items-center gap-1 text-xs text-green-600">
                <CheckCircle2 className="size-3" />
                Valid reference point ({editedConfig.ref_point?.length} value
                {editedConfig.ref_point?.length !== 1 ? "s" : ""})
              </div>
            )}
            <div className="text-muted-foreground text-xs">
              Enter comma-separated values for each objective. Should represent
              worst acceptable performance.
            </div>
          </div>
        )}

        {/* Weights for qLogNParEGO */}
        {editedConfig.type === "qLogNParEGO" && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Objective Weights</Label>
            <div className="relative">
              <Input
                type="text"
                placeholder="e.g., 0.5, 0.5 (for equal weights)"
                value={weightsInput}
                onChange={e => handleWeightsChange(e.target.value)}
                className={`h-8 pr-8 ${weightsError ? "border-red-500" : weightsInput && !weightsError ? "border-green-500" : ""}`}
              />
              {weightsInput && (
                <div className="absolute right-2 top-1/2 -translate-y-1/2">
                  {weightsError ? (
                    <AlertCircle className="size-4 text-red-500" />
                  ) : (
                    <CheckCircle2 className="size-4 text-green-500" />
                  )}
                </div>
              )}
            </div>
            {weightsError && (
              <div className="flex items-center gap-1 text-xs text-red-600">
                <AlertCircle className="size-3" />
                {weightsError}
              </div>
            )}
            {!weightsError && weightsInput && (
              <div className="flex items-center gap-1 text-xs text-green-600">
                <CheckCircle2 className="size-3" />
                Valid weights (sum ={" "}
                {editedConfig.weights?.reduce((a, b) => a + b, 0).toFixed(2)})
              </div>
            )}
            <div className="text-muted-foreground text-xs">
              Enter comma-separated weights for each objective. Must sum to 1.0.
            </div>
          </div>
        )}

        {/* Current Configuration Summary */}
        <div className="bg-muted/30 rounded-md p-3">
          <div className="text-muted-foreground mb-1 text-xs font-medium">
            Current Configuration:
          </div>
          <div className="text-sm">
            <span className="font-medium">
              {getFunctionDisplayName(editedConfig.type)}
            </span>
            {editedConfig.type === "qUpperConfidenceBound" &&
              editedConfig.beta && (
                <span className="text-muted-foreground">
                  {" "}
                  (β = {editedConfig.beta})
                </span>
              )}
            {(editedConfig.type === "qNoisyExpectedHypervolumeImprovement" ||
              editedConfig.type ===
                "qLogNoisyExpectedHypervolumeImprovement") &&
              editedConfig.ref_point && (
                <span className="text-muted-foreground">
                  {" "}
                  (ref_point = {JSON.stringify(editedConfig.ref_point)})
                </span>
              )}
            {editedConfig.type === "qLogNParEGO" && editedConfig.weights && (
              <span className="text-muted-foreground">
                {" "}
                (weights = {JSON.stringify(editedConfig.weights)})
              </span>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2 pt-2 sm:flex-row sm:items-center sm:justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            disabled={!hasChanges}
            className="h-8 w-full sm:w-auto"
          >
            <RotateCcw className="mr-1 size-3" />
            Reset
          </Button>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              className="h-8 flex-1 sm:flex-none"
            >
              <X className="mr-1 size-3" />
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges || !!refPointError || !!weightsError}
              className="h-8 flex-1 sm:flex-none"
            >
              <Save className="mr-1 size-3" />
              Save
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
