// app/api/feedback/[id]/route.ts
import { NextRequest, NextResponse } from "next/server"
import { auth } from "@clerk/nextjs/server"
import {
  getFeedbackByIdAction,
  updateFeedbackStatusAction
} from "@/actions/db/feedback-actions"
import { z } from "zod"
import { getActualUserId } from "@/lib/auth-utils"

// Validation schema for status update
const statusUpdateSchema = z.object({
  status: z.enum(["new", "in_progress", "resolved", "closed"])
})

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 FEEDBACK API GET - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return NextResponse.json(
        { error: "You must be signed in to view feedback" },
        { status: 401 }
      )
    }

    // Get the feedback
    const { id } = await params
    const result = await getFeedbackByIdAction(id)

    if (result.isSuccess && result.data) {
      return NextResponse.json(result)
    } else {
      return NextResponse.json({ error: result.message }, { status: 404 })
    }
  } catch (error) {
    console.error("Error retrieving feedback:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 FEEDBACK API PATCH - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return NextResponse.json(
        { error: "You must be signed in to update feedback" },
        { status: 401 }
      )
    }

    // Parse and validate the request body
    const body = await request.json()

    try {
      const { status } = statusUpdateSchema.parse(body)

      // Update the feedback status
      const { id } = await params
      const result = await updateFeedbackStatusAction(id, status)

      if (result.isSuccess) {
        return NextResponse.json(result)
      } else {
        return NextResponse.json({ error: result.message }, { status: 400 })
      }
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: validationError.errors },
          { status: 400 }
        )
      }
      throw validationError
    }
  } catch (error) {
    console.error("Error updating feedback:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
