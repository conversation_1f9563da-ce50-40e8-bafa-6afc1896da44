"use client"

import { motion } from "framer-motion"
import { Database, Shield, Eye, Trash2, Download, Lock } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"

const dataProtectionMeasures = [
  {
    icon: Lock,
    title: "Data Encryption",
    description:
      "AWS KMS-managed encryption for all data with industry-leading security standards.",
    measures: [
      "AWS KMS AES-256 encryption for data at rest",
      "TLS 1.3 for data in transit",
      "End-to-end encryption for sensitive operations",
      "Automated key rotation and management"
    ]
  },
  {
    icon: Database,
    title: "Data Isolation",
    description:
      "AWS RDS-powered data isolation with enterprise-grade security architecture.",
    measures: [
      "AWS VPC network isolation and security groups",
      "Row-level security (RLS) in PostgreSQL",
      "User-specific data partitioning",
      "AWS CloudTrail audit trails for all data access"
    ]
  },
  {
    icon: Eye,
    title: "Data Minimization",
    description:
      "We collect only the data necessary for providing our optimization services.",
    measures: [
      "Purpose limitation for data collection",
      "Regular data usage audits",
      "Automatic data anonymization",
      "Opt-in for non-essential data"
    ]
  },
  {
    icon: Trash2,
    title: "Data Retention",
    description:
      "Automated data lifecycle management with secure deletion processes.",
    measures: [
      "Configurable retention periods",
      "Automatic data purging",
      "Secure data destruction",
      "Compliance with legal requirements"
    ]
  },
  {
    icon: Download,
    title: "Data Portability",
    description:
      "Easy export of your data in standard formats for portability and backup.",
    measures: [
      "One-click data export",
      "Standard file formats (CSV, JSON)",
      "Complete data package downloads",
      "API access for programmatic export"
    ]
  },
  {
    icon: Shield,
    title: "Access Controls",
    description: "Granular access controls and authentication mechanisms.",
    measures: [
      "Multi-factor authentication",
      "Role-based access control",
      "Session management",
      "API key authentication"
    ]
  }
]

export function DataProtection() {
  return (
    <section className="bg-gray-50 py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Data Protection Measures
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            We implement comprehensive data protection measures to ensure your
            optimization data and personal information remain secure and
            private.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {dataProtectionMeasures.map((measure, index) => (
            <motion.div
              key={measure.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full transition-shadow hover:shadow-lg">
                <CardHeader>
                  <div className="mb-4 flex size-12 items-center justify-center rounded-lg bg-green-100">
                    <measure.icon className="size-6 text-green-600" />
                  </div>
                  <CardTitle className="text-xl">{measure.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {measure.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {measure.measures.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start">
                        <div className="mr-2 mt-1.5 size-1.5 shrink-0 rounded-full bg-green-600" />
                        <span className="text-sm text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-12"
        >
          <Card className="bg-gradient-to-r from-green-50 to-blue-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="mb-4 text-xl font-semibold text-gray-900">
                  Your Data, Your Control
                </h3>
                <p className="mb-6 text-gray-600">
                  You have complete control over your data with comprehensive
                  rights and tools to manage your information.
                </p>
                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-blue-600">
                      View
                    </div>
                    <div className="text-sm text-gray-600">
                      Access all your data
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-green-600">
                      Export
                    </div>
                    <div className="text-sm text-gray-600">
                      Download your data
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-purple-600">
                      Correct
                    </div>
                    <div className="text-sm text-gray-600">
                      Update information
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-red-600">
                      Delete
                    </div>
                    <div className="text-sm text-gray-600">
                      Remove your data
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
