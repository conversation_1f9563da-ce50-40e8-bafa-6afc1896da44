#!/usr/bin/env node

/**
 * This script runs the migration to add academic verification tables and fields.
 * It should be run after deploying the code that includes the academic signup feature.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const readline = require('readline');
const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

// Get the database URL from environment variables
let databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Modify the connection string to disable SSL verification
databaseUrl = databaseUrl.replace('sslmode=require', 'sslmode=no-verify');
console.log(`🔍 Using database: ${databaseUrl.replace(/:[^:]*@/, ':****@')}`);

// Create a client with SSL disabled
const client = new Client({
  connectionString: databaseUrl,
  ssl: {
    rejectUnauthorized: false
  }
});

// Function to ask for confirmation
function askForConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(question, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function runMigration() {
  try {
    // Connect to the database
    await client.connect();
    console.log('✅ Connected to database');
    
    // Check if the academic_verifications table already exists
    const tableCheckResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'academic_verifications'
      );
    `);
    
    const tableExists = tableCheckResult.rows[0].exists;
    
    if (tableExists) {
      console.log('⚠️ The academic_verifications table already exists');
      const shouldContinue = await askForConfirmation('Do you want to continue with the migration anyway? (y/n): ');
      
      if (!shouldContinue) {
        console.log('Migration cancelled');
        await client.end();
        return;
      }
    }
    
    // Check if the survey_type column exists in the user_surveys table
    const columnCheckResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_surveys' 
        AND column_name = 'survey_type'
      );
    `);
    
    const columnExists = columnCheckResult.rows[0].exists;
    
    if (columnExists) {
      console.log('⚠️ The survey_type column already exists in the user_surveys table');
      const shouldContinue = await askForConfirmation('Do you want to continue with the migration anyway? (y/n): ');
      
      if (!shouldContinue) {
        console.log('Migration cancelled');
        await client.end();
        return;
      }
    }
    
    // Run the migration
    console.log('🚀 Running migration...');
    
    const migrationPath = path.join(__dirname, '../db/migrations/0004_add_academic_verification.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    await client.query(migrationSql);
    
    console.log('✅ Migration completed successfully');
    
    // Disconnect from the database
    await client.end();
    console.log('👋 Disconnected from database');
    
  } catch (error) {
    console.error('❌ Error running migration:', error);
    await client.end();
    process.exit(1);
  }
}

// Run the migration
runMigration();
