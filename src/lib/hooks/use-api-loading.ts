"use client"

import { useCallback } from "react"
import { useLoading } from "@/contexts/loading-context"

/**
 * Hook for managing loading state during API calls in components
 *
 * @returns An object with functions to wrap API calls with loading state
 */
export function useApiLoading() {
  const { startLoading, stopLoading } = useLoading()

  /**
   * Wraps an async function with loading state
   *
   * @param asyncFn - The async function to wrap
   * @returns A new function that shows loading state while the original function executes
   */
  const withLoading = useCallback(
    <T extends any[], R>(asyncFn: (...args: T) => Promise<R>) => {
      return async (...args: T): Promise<R> => {
        startLoading()
        try {
          return await asyncFn(...args)
        } finally {
          stopLoading()
        }
      }
    },
    [startLoading, stopLoading]
  )

  return { withLoading }
}
