"use client"

import { motion } from "framer-motion"
import { Shield, Lock, Eye, CheckCircle, Award, Globe } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"

export function SecurityHero() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <div className="mx-auto mb-4 flex size-20 items-center justify-center rounded-full bg-blue-100">
              <Shield className="size-10 text-blue-600" />
            </div>
            <h1 className="mb-4 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
              Security & Privacy
              <span className="block text-blue-600">Center</span>
            </h1>
            <p className="mx-auto max-w-3xl text-xl text-gray-600">
              Your data security and privacy are our top priorities. Learn about
              our comprehensive security measures, data protection practices,
              and commitment to transparency.
            </p>

            {/* Trust Indicators */}
            <div className="mt-6 flex flex-wrap justify-center gap-3">
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800 hover:bg-green-200"
              >
                <CheckCircle className="mr-1 size-3" />
                SOC 2 Compliant
              </Badge>
              <Badge
                variant="secondary"
                className="bg-blue-100 text-blue-800 hover:bg-blue-200"
              >
                <Globe className="mr-1 size-3" />
                GDPR Ready
              </Badge>
              <Badge
                variant="secondary"
                className="bg-purple-100 text-purple-800 hover:bg-purple-200"
              >
                <Award className="mr-1 size-3" />
                Enterprise Grade
              </Badge>
              <Badge
                variant="secondary"
                className="bg-orange-100 text-orange-800 hover:bg-orange-200"
              >
                <Shield className="mr-1 size-3" />
                AWS Secured
              </Badge>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-12 flex flex-wrap justify-center gap-4"
          >
            <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
              <Link href="#security-measures">
                <Lock className="mr-2 size-4" />
                Security Measures
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/data-processing">
                <Eye className="mr-2 size-4" />
                Data Transparency
              </Link>
            </Button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-1 gap-6 md:grid-cols-3"
          >
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <CheckCircle className="mx-auto mb-3 size-8 text-green-600" />
              <h3 className="mb-2 font-semibold text-gray-900">
                GDPR Compliant
              </h3>
              <p className="text-sm text-gray-600">
                Full compliance with European data protection regulations
              </p>
            </div>
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <CheckCircle className="mx-auto mb-3 size-8 text-green-600" />
              <h3 className="mb-2 font-semibold text-gray-900">
                Enterprise Security
              </h3>
              <p className="text-sm text-gray-600">
                Bank-level encryption and security measures
              </p>
            </div>
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <CheckCircle className="mx-auto mb-3 size-8 text-green-600" />
              <h3 className="mb-2 font-semibold text-gray-900">
                Data Transparency
              </h3>
              <p className="text-sm text-gray-600">
                Complete visibility into how your data is handled
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
