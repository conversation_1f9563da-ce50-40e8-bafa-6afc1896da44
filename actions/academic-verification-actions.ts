"use server"

import { db } from "@/db/db"
import { academicVerificationsTable } from "@/db/schema"
import { eq } from "drizzle-orm"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { revalidatePath } from "next/cache"
import { generateVerificationToken, generateTokenExpiry, isTokenExpired } from "@/lib/token-service"
import { sendVerificationEmail } from "@/lib/email-service"

// Define the academic verification request type
export type AcademicVerificationRequest = {
  fullName: string
  email: string
  institutionalEmail: string
  institution: string
  role: "student" | "researcher" | "professor" | "other"
}

// Define the academic survey response type
export type AcademicSurveyResponses = {
  // Section 2: Experience with Bayesian Optimization Tools
  toolsAware: string[] // Array of tools the user is aware of
  toolsAwareOther?: string // Other tools the user is aware of
  toolsUsed: string[] // Array of tools the user has used
  toolsUsedOther?: string // Other tools the user has used

  // Section 4: Pricing Perspective
  fairPriceAcademic: string // Fair yearly subscription fee for academic users
  fairPriceAcademicOther?: string // Other fair price for academic users
  fairPriceCommercial: string // Fair yearly subscription fee for commercial users
  fairPriceCommercialOther?: string // Other fair price for commercial users

  // Optional
  researchArea?: string // Primary field of study or research area
}

// Action to submit academic verification request
export async function submitAcademicVerificationAction(
  request: AcademicVerificationRequest,
  clientUserId: string // Accept user ID from client
) {
  try {
    // Basic validation that we have a user ID
    if (!clientUserId || typeof clientUserId !== 'string' || clientUserId.trim() === '') {
      return {
        isSuccess: false,
        message: "Invalid user authentication",
        isVerified: false,
        emailSent: false
      }
    }

    // Generate a verification token and expiry date
    const verificationToken = generateVerificationToken()
    const tokenExpiry = generateTokenExpiry()

    // Check if a verification request already exists
    const existingRequest = await db
      .select()
      .from(academicVerificationsTable)
      .where(eq(academicVerificationsTable.userId, clientUserId))
      .limit(1)

    if (existingRequest.length > 0) {
      // Update existing request
      await db
        .update(academicVerificationsTable)
        .set({
          fullName: request.fullName,
          email: request.email,
          institutionalEmail: request.institutionalEmail,
          institution: request.institution,
          role: request.role,
          verificationStatus: "pending", // Reset to pending if resubmitting
          verificationToken,
          verificationTokenExpiry: tokenExpiry,
          updatedAt: new Date()
        })
        .where(eq(academicVerificationsTable.userId, clientUserId))
    } else {
      // Insert new request
      await db.insert(academicVerificationsTable).values({
        userId: clientUserId,
        fullName: request.fullName,
        email: request.email,
        institutionalEmail: request.institutionalEmail,
        institution: request.institution,
        role: request.role,
        verificationToken,
        verificationTokenExpiry: tokenExpiry
      })
    }

    // Perform domain-based pre-verification
    const verificationResult = await performAutomatedVerification(request.institutionalEmail)

    // If the domain is pre-verified, we can mark it as "domain_verified"
    // but we still require email verification
    if (verificationResult.isVerified) {
      await db
        .update(academicVerificationsTable)
        .set({
          verificationStatus: "domain_verified", // Domain is verified, but email verification is still required
          verificationMethod: "domain",
          updatedAt: new Date()
        })
        .where(eq(academicVerificationsTable.userId, clientUserId))
    }

    // Send verification email regardless of domain verification
    if (verificationResult.requiresEmailVerification) {
      // For development with Resend's shared domain, we need to handle the restriction
      // that we can only send to the email associated with the Resend account
      const fromEmail = process.env.EMAIL_FROM || '';
      const isUsingResendSharedDomain = fromEmail.includes('resend.dev');

      // If using Resend's shared domain, we'll send the email to the developer's email
      // This is only for development purposes
      if (isUsingResendSharedDomain) {
        const verificationLink = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/verify-email?token=${verificationToken}`;

        // Log the verification link to the console for reference
        console.log('==================================================');
        console.log('VERIFICATION EMAIL (DEVELOPMENT MODE)');
        console.log('==================================================');
        console.log(`Original recipient: ${request.institutionalEmail}`);
        console.log(`Actual recipient: <EMAIL> (developer email)`);
        console.log(`Verification link: ${verificationLink}`);
        console.log('==================================================');

        // Send the actual email to the developer's email address
        const emailResult = await sendVerificationEmail(
          '<EMAIL>', // Send to developer's email instead
          verificationToken,
          request.fullName,
          request.institution
        );

        if (!emailResult.success) {
          console.error("Error sending verification email:", emailResult.error);
          return {
            isSuccess: true,
            message: "Verification request submitted, but there was an error sending the verification email. Please try again later.",
            isVerified: false,
            emailSent: false
          };
        }

        return {
          isSuccess: true,
          message: "Verification request submitted. In development mode, verification email sent to developer's email.",
          isVerified: false,
          emailSent: true,
          verificationLink // Include the verification link in the response for development
        };
      }

      // In production or when using a verified domain, send the email normally
      const emailResult = await sendVerificationEmail(
        request.institutionalEmail,
        verificationToken,
        request.fullName,
        request.institution
      )

      if (!emailResult.success) {
        console.error("Error sending verification email:", emailResult.error)
        return {
          isSuccess: true,
          message: "Verification request submitted, but there was an error sending the verification email. Please try again later.",
          isVerified: false,
          emailSent: false
        }
      }
    }

    revalidatePath("/academic-signup")
    revalidatePath("/academic-survey")
    revalidatePath("/verify-email")

    return {
      isSuccess: true,
      message: "Academic verification request submitted successfully. Please check your institutional email for verification.",
      isVerified: false, // Always false until email is verified
      emailSent: true
    }
  } catch (error) {
    console.error("ACADEMIC VERIFICATION - Error submitting request:", error)

    return {
      isSuccess: false,
      message: "Failed to submit academic verification request",
      isVerified: false,
      emailSent: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// Action to check academic verification status
export async function checkAcademicVerificationStatusAction() {
  try {
    const { userId } = await auth()

    if (!userId) {
      return {
        isSuccess: false,
        message: "User not authenticated",
        status: null
      }
    }

    const verification = await db
      .select()
      .from(academicVerificationsTable)
      .where(eq(academicVerificationsTable.userId, userId))
      .limit(1)

    if (verification.length === 0) {
      return {
        isSuccess: true,
        message: "No verification request found",
        status: null
      }
    }

    return {
      isSuccess: true,
      message: "Verification status retrieved",
      status: verification[0].verificationStatus,
      data: verification[0]
    }
  } catch (error) {
    console.error("Error checking academic verification status:", error)
    return {
      isSuccess: false,
      message: "Failed to check academic verification status",
      status: null
    }
  }
}

// Action to verify email token
export async function verifyEmailTokenAction(token: string) {
  try {
    console.log("Verifying token:", token?.substring(0, 10) + "...")

    if (!token) {
      console.log("No token provided")
      return {
        isSuccess: false,
        message: "Invalid verification token"
      }
    }

    // Find the verification request with this token
    const verification = await db
      .select()
      .from(academicVerificationsTable)
      .where(eq(academicVerificationsTable.verificationToken, token))
      .limit(1)

    console.log("Database query result:", verification.length > 0 ? "Token found" : "Token not found")

    if (verification.length === 0) {
      console.log("Token not found in database")
      return {
        isSuccess: false,
        message: "Invalid verification token"
      }
    }

    const verificationRecord = verification[0]

    // Check if token has expired
    if (
      verificationRecord.verificationTokenExpiry &&
      isTokenExpired(new Date(verificationRecord.verificationTokenExpiry))
    ) {
      return {
        isSuccess: false,
        message: "Verification token has expired"
      }
    }

    // Update verification status
    await db
      .update(academicVerificationsTable)
      .set({
        verificationStatus: "approved",
        verificationMethod: "email",
        verificationTimestamp: new Date(),
        updatedAt: new Date()
      })
      .where(eq(academicVerificationsTable.verificationToken, token))

    // Initialize or upgrade to a 90-day academic trial
    try {
      const { initializeTrialAction } = await import("@/actions/trial-actions")
      const trialResult = await initializeTrialAction(
        verificationRecord.userId,
        90, // 90-day trial for academic users
        true // Mark as academic trial
      )

      console.log(`Academic trial initialization result: ${trialResult.isSuccess ? 'Success' : 'Failed'}`)
      if (!trialResult.isSuccess) {
        console.log(`Academic trial initialization message: ${trialResult.message}`)
      }
    } catch (trialError) {
      console.error("Error initializing academic trial:", trialError)
      // Continue with verification even if trial initialization fails
    }

    revalidatePath("/academic-signup")
    revalidatePath("/academic-survey")
    revalidatePath("/verify-email")

    return {
      isSuccess: true,
      message: "Email verified successfully. You now have a 90-day academic trial!",
      userId: verificationRecord.userId
    }
  } catch (error) {
    console.error("Error verifying email token:", error)
    return {
      isSuccess: false,
      message: "Failed to verify email token"
    }
  }
}

// Action to resend verification email
export async function resendVerificationEmailAction() {
  try {
    const { userId } = await auth()

    if (!userId) {
      return {
        isSuccess: false,
        message: "User not authenticated"
      }
    }

    // Get the verification record
    const verification = await db
      .select()
      .from(academicVerificationsTable)
      .where(eq(academicVerificationsTable.userId, userId))
      .limit(1)

    if (verification.length === 0) {
      return {
        isSuccess: false,
        message: "No verification request found"
      }
    }

    const verificationRecord = verification[0]

    // Generate a new token and expiry
    const verificationToken = generateVerificationToken()
    const tokenExpiry = generateTokenExpiry()

    // Update the verification record with the new token
    await db
      .update(academicVerificationsTable)
      .set({
        verificationToken,
        verificationTokenExpiry: tokenExpiry,
        updatedAt: new Date()
      })
      .where(eq(academicVerificationsTable.userId, userId))

    // For development with Resend's shared domain, we need to handle the restriction
    // that we can only send to the email associated with the Resend account
    const fromEmail = process.env.EMAIL_FROM || '';
    const isUsingResendSharedDomain = fromEmail.includes('resend.dev');

    // Determine the recipient email
    const recipientEmail = isUsingResendSharedDomain
      ? '<EMAIL>' // In development, send to developer's email
      : verificationRecord.institutionalEmail; // In production, send to actual email

    // If using Resend's shared domain, log the verification link
    if (isUsingResendSharedDomain) {
      const verificationLink = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/verify-email?token=${verificationToken}`;
      console.log('==================================================');
      console.log('RESEND VERIFICATION EMAIL (DEVELOPMENT MODE)');
      console.log('==================================================');
      console.log(`Original recipient: ${verificationRecord.institutionalEmail}`);
      console.log(`Actual recipient: ${recipientEmail} (developer email)`);
      console.log(`Verification link: ${verificationLink}`);
      console.log('==================================================');
    }

    // Send the verification email
    const emailResult = await sendVerificationEmail(
      recipientEmail,
      verificationToken,
      verificationRecord.fullName,
      verificationRecord.institution
    )

    if (!emailResult.success) {
      console.error("Error sending verification email:", emailResult.error)
      return {
        isSuccess: false,
        message: "Failed to send verification email"
      }
    }

    return {
      isSuccess: true,
      message: "Verification email sent successfully"
    }
  } catch (error) {
    console.error("Error resending verification email:", error)
    return {
      isSuccess: false,
      message: "Failed to resend verification email"
    }
  }
}

// Helper function to perform domain-based pre-verification
async function performAutomatedVerification(email: string) {
  // Check if the email domain is from a known academic institution
  const academicDomains = [
    // Academic domains
    "edu", "ac.uk", "edu.au", "ac.jp", "uni-", ".uni.",
    "university", "college", "institute", "school",

    // Company-specific domains requested to be included
    "synsilico.com", "innosyn.com",

    // Common academic domain patterns
    "ac.", ".ac.", ".edu.", "research.", ".research.",

    // Common email domains for testing purposes
    "gmail.com", "yahoo.com", "outlook.com", "hotmail.com", "aol.com", "icloud.com", "protonmail.com"
  ]

  const domain = email.split("@")[1]?.toLowerCase()

  if (!domain) {
    return { isVerified: false, requiresEmailVerification: true }
  }

  // Check if the domain is in our list of academic domains
  const isAcademicDomain = academicDomains.some(acadDomain => {
    if (acadDomain.startsWith(".")) {
      return domain.includes(acadDomain)
    } else {
      return domain.endsWith(acadDomain) || domain.includes(`-${acadDomain}`) || domain.includes(acadDomain)
    }
  })

  // If it's a known academic domain, we still require email verification
  // but we can mark it as pre-verified
  return {
    isVerified: isAcademicDomain,
    requiresEmailVerification: true // Always require email verification
  }
}
