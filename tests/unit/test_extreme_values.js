// Test script to verify handling of extremely low/high numerical values
const fetch = require('node-fetch');

// Function to test adding a measurement with extreme values
async function testExtremeLowValue() {
  console.log('Testing extremely low value (1e-20) for n-type doping parameter...');
  
  const optimizerId = 'user_2xJk5B5dUfTe8TUGc1gARPTAEAL_Photovoltaic_device_optimization_1747821537343';
  const url = `http://localhost:8000/optimizations/${optimizerId}/measurements`;
  
  // Create a measurement with an extremely low value
  const data = {
    parameters: {
      "n-type doping (cm^-3)": 1e-20,
      "p-type doping (cm^-3)": 1e16,
      "thickness (nm)": 100
    },
    target_values: 15.5
  };
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_TOKEN_HERE' // Replace with actual token if needed
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(result, null, 2));
    
    if (response.status === 200) {
      console.log('✅ Test PASSED: Successfully handled extremely low value');
    } else {
      console.log('❌ Test FAILED: Error handling extremely low value');
    }
  } catch (error) {
    console.error('Error during test:', error);
    console.log('❌ Test FAILED: Exception occurred');
  }
}

async function testExtremeHighValue() {
  console.log('\nTesting extremely high value (1e20) for n-type doping parameter...');
  
  const optimizerId = 'user_2xJk5B5dUfTe8TUGc1gARPTAEAL_Photovoltaic_device_optimization_1747821537343';
  const url = `http://localhost:8000/optimizations/${optimizerId}/measurements`;
  
  // Create a measurement with an extremely high value
  const data = {
    parameters: {
      "n-type doping (cm^-3)": 1e20,
      "p-type doping (cm^-3)": 1e16,
      "thickness (nm)": 100
    },
    target_values: 15.5
  };
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_TOKEN_HERE' // Replace with actual token if needed
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(result, null, 2));
    
    if (response.status === 200) {
      console.log('✅ Test PASSED: Successfully handled extremely high value');
    } else {
      console.log('❌ Test FAILED: Error handling extremely high value');
    }
  } catch (error) {
    console.error('Error during test:', error);
    console.log('❌ Test FAILED: Exception occurred');
  }
}

// Run the tests
async function runTests() {
  await testExtremeLowValue();
  await testExtremeHighValue();
}

runTests();
