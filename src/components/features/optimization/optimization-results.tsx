// components/optimization/optimization-results.tsx
"use client"

import React, { useEffect, useState, useMemo } from "react"
import { useRouter } from "next/navigation"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import {
  getBestPointWorkflowAction,
  recreateOptimizationWorkflowAction
} from "@/actions/optimization-workflow-actions"
import { getObjectiveType } from "@/lib/utils/optimization-utils"
import { getMeasurementsAction } from "@/actions/db/optimizations-actions"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer
} from "recharts"
import {
  ArrowUp,
  ArrowDown,
  RefreshCw,
  Beaker,
  Trophy,
  History,
  ChevronRight,
  ChevronDown,
  Download,
  RotateCw,
  Target,
  Check,
  Layers,
  AreaChart,
  Edit,
  FileSpreadsheet,
  Trash2,
  MoreHorizontal,
  Settings,
  Zap,
  Link
} from "lucide-react"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"
import { ParameterImpactChart } from "@/components/optimization/parameter-impact-chart"
import { OptimizationStatusControl } from "@/components/optimization/optimization-status-control"
import { OptimizationStatusHistory } from "@/components/optimization/optimization-status-history"
import { BestResultsDocumentation } from "@/components/documentation/best-results-docs"
import { AnalysisTab } from "@/components/optimization/analysis/analysis-tab"

import { downloadCSV, downloadExcel } from "@/lib/export-utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { EditMeasurementDialog } from "@/components/optimization/edit-measurement-dialog"
import { DeleteMeasurementDialog } from "@/components/optimization/delete-measurement-dialog"
import { ParameterEditForm } from "@/components/optimization/parameter-edit-form"
import { AcquisitionFunctionEditForm } from "@/components/optimization/acquisition-function-edit-form"
import { ConstraintsEditSection } from "@/components/optimization/constraints-edit-section"
import { TargetEditForm } from "@/components/optimization/target-edit-form"
import { ConfigurationPreviewDialog } from "@/components/optimization/configuration-preview-dialog"
import { getTargetMode } from "@/lib/optimization-utils"

// Helper function to format dates consistently for server and client
const formatDate = (date: Date) => {
  // Use a consistent format that doesn't depend on locale
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}:${String(date.getSeconds()).padStart(2, "0")}`
}

// Helper function to format data for the chart
const formatChartData = (
  measurements: SelectMeasurement[],
  targetName?: string
) => {
  // Group measurements into batches based on their batch ID (same logic as in History tab)
  const batches: SelectMeasurement[][] = []
  const measurementsCopy = [...measurements]

  // Sort by creation date (newest first)
  measurementsCopy.sort((a, b) => {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  })

  // Group by batch ID
  while (measurementsCopy.length > 0) {
    const current = measurementsCopy.shift()!
    const batchId = current.batchId

    // If this measurement has a batch ID, find all others with the same ID
    if (batchId) {
      const batchItems = [current]
      let i = 0
      while (i < measurementsCopy.length) {
        if (measurementsCopy[i].batchId === batchId) {
          batchItems.push(measurementsCopy.splice(i, 1)[0])
        } else {
          i++
        }
      }
      batches.push(batchItems)
    } else {
      // If no batch ID, treat as a single-item batch
      batches.push([current])
    }
  }

  // Sort batches by creation date (newest first)
  batches.sort((a, b) => {
    const aTime = new Date(a[0].createdAt).getTime()
    const bTime = new Date(b[0].createdAt).getTime()
    return bTime - aTime
  })

  // Reverse batches to show oldest first in the chart (consistent with History tab)
  const reversedBatches = [...batches].reverse()

  // Create chart data points with experiment numbers matching the History tab
  const chartData: Record<string, any>[] = []
  let experimentNumber = 1

  reversedBatches.forEach(batch => {
    if (batch.length === 1) {
      // Single measurement (not a batch)
      const measurement = batch[0]
      let targetValue = parseFloat(measurement.targetValue)

      // Create the data point with experiment number
      const dataPoint: Record<string, any> = {
        iteration: experimentNumber, // Use experiment number instead of sequential index
        experimentNumber: experimentNumber, // Store the experiment number explicitly
        experimentLabel: `${experimentNumber}`, // Label for the x-axis
        targetValue,
        isRecommended: measurement.isRecommended,
        parameters: measurement.parameters,
        timestamp: measurement.createdAt,
        isBatchItem: false
      }

      // Add all target values as separate properties for multi-target visualizations
      if (measurement.targetValues) {
        const targetValues = measurement.targetValues as Record<string, number>
        Object.entries(targetValues).forEach(([key, value]) => {
          dataPoint[key] = value
        })
      }

      // Make sure the primary target is included as a named property
      if (targetName) {
        // If we have targetValues and a specific target name, use that value
        if (
          measurement.targetValues &&
          measurement.targetValues[
            targetName as keyof typeof measurement.targetValues
          ] !== undefined
        ) {
          dataPoint[targetName] = measurement.targetValues[
            targetName as keyof typeof measurement.targetValues
          ] as number
        } else {
          // Otherwise use the targetValue
          dataPoint[targetName] = targetValue
        }
      }

      chartData.push(dataPoint)
    } else {
      // For batches, create individual data points for each item in the batch
      // Sort batch items by creation time (oldest first) to match the History tab's ordering
      const sortedBatch = [...batch].sort((a, b) => {
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      })

      sortedBatch.forEach((measurement, batchIndex) => {
        const targetValue = parseFloat(measurement.targetValue)

        // Create the data point with experiment number and sub-index
        const dataPoint: Record<string, any> = {
          iteration: `${experimentNumber}.${batchIndex + 1}`, // Use format like "8.1", "8.2"
          experimentNumber: experimentNumber, // Store the main experiment number
          experimentLabel: `${experimentNumber}.${batchIndex + 1}`, // Label for the x-axis
          batchIndex: batchIndex + 1, // Store the batch index (1-based)
          targetValue,
          isRecommended: measurement.isRecommended,
          parameters: measurement.parameters,
          timestamp: measurement.createdAt,
          isBatchItem: true,
          batchSize: batch.length
        }

        // Add all target values as separate properties for multi-target visualizations
        if (measurement.targetValues) {
          const targetValues = measurement.targetValues as Record<
            string,
            number
          >
          Object.entries(targetValues).forEach(([key, value]) => {
            dataPoint[key] = value
          })
        }

        // Make sure the primary target is included as a named property
        if (targetName) {
          // If we have targetValues and a specific target name, use that value
          if (
            measurement.targetValues &&
            measurement.targetValues[
              targetName as keyof typeof measurement.targetValues
            ] !== undefined
          ) {
            dataPoint[targetName] = measurement.targetValues[
              targetName as keyof typeof measurement.targetValues
            ] as number
          } else {
            // Otherwise use the targetValue
            dataPoint[targetName] = targetValue
          }
        }

        chartData.push(dataPoint)
      })
    }

    // Increment experiment number for the next batch
    experimentNumber++
  })

  return chartData
}

// Helper function to calculate best values at each iteration
const calculateBestValues = (
  chartData: any[],
  mode: "MAX" | "MIN",
  targetName?: string,
  isMultiTarget: boolean = false
) => {
  let bestValue = mode === "MAX" ? -Infinity : Infinity
  let bestValuesByExperiment: Record<number, number> = {}

  // First pass: calculate best values by main experiment number
  chartData.forEach(point => {
    // For multi-target optimizations, use the specific target value if available
    let value
    if (isMultiTarget && targetName && point[targetName] !== undefined) {
      value = parseFloat(point[targetName])
    } else {
      value = parseFloat(point.targetValue)
    }

    const experimentNumber = point.experimentNumber

    // Initialize if not exists
    if (!bestValuesByExperiment[experimentNumber]) {
      bestValuesByExperiment[experimentNumber] =
        mode === "MAX" ? -Infinity : Infinity
    }

    // Update best value for this experiment
    if (mode === "MAX") {
      bestValuesByExperiment[experimentNumber] = Math.max(
        bestValuesByExperiment[experimentNumber],
        value
      )
      bestValue = Math.max(bestValue, value)
    } else {
      bestValuesByExperiment[experimentNumber] = Math.min(
        bestValuesByExperiment[experimentNumber],
        value
      )
      bestValue = Math.min(bestValue, value)
    }
  })

  // Second pass: assign best values to each point
  return chartData.map(point => {
    // For the best value line, we want to show the best value up to this experiment
    // Get all experiment numbers up to and including this one
    const experimentNumber = point.experimentNumber
    const experimentsToConsider = Object.keys(bestValuesByExperiment)
      .map(Number)
      .filter(expNum => expNum <= experimentNumber)

    // Find the best value among all these experiments
    let bestValueSoFar = mode === "MAX" ? -Infinity : Infinity
    experimentsToConsider.forEach(expNum => {
      if (mode === "MAX") {
        bestValueSoFar = Math.max(
          bestValueSoFar,
          bestValuesByExperiment[expNum]
        )
      } else {
        bestValueSoFar = Math.min(
          bestValueSoFar,
          bestValuesByExperiment[expNum]
        )
      }
    })

    return { ...point, bestValue: bestValueSoFar }
  })
}

interface OptimizationResultsProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  initialBestPoint?: {
    best_parameters?: Record<string, any>
    best_value?: number
    best_values?: Record<string, number>
    composite_score?: number
    normalized_values?: Record<string, number>
    target_weights?: Record<string, number>
  }
}

export function OptimizationResults({
  optimization,
  measurements: initialMeasurements,
  initialBestPoint
}: OptimizationResultsProps) {
  const router = useRouter()
  const [measurements, setMeasurements] =
    useState<SelectMeasurement[]>(initialMeasurements)
  const [bestPoint, setBestPoint] = useState(initialBestPoint || {})
  const [isLoadingBest, setIsLoadingBest] = useState(false)
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")
  const [activeAnalysisSection, setActiveAnalysisSection] =
    useState("parameters")
  const [expandedBatches, setExpandedBatches] = useState<
    Record<string, boolean>
  >({})
  const [decimalPrecision, setDecimalPrecision] = useState(2) // Default to 2 decimal places
  const [batchIds, setBatchIds] = useState<string[]>([]) // Track all batch IDs for expand/collapse all
  const [allExpanded, setAllExpanded] = useState(false) // Track whether all batches are expanded
  const [optimizationState, setOptimizationState] = useState(optimization) // Track optimization state

  const [isRecreating, setIsRecreating] = useState(false) // Track campaign recreation state
  const [isEditMode, setIsEditMode] = useState(false) // Track edit mode for parameters
  const [expandedParameters, setExpandedParameters] = useState<string[]>([]) // Track expanded parameter rows
  const [pendingParameterChanges, setPendingParameterChanges] = useState<
    Record<string, any>
  >({}) // Track pending parameter changes
  const [expandedAcquisitionFunction, setExpandedAcquisitionFunction] =
    useState(false) // Track acquisition function expansion
  const [pendingAcquisitionChanges, setPendingAcquisitionChanges] =
    useState<any>(null) // Track pending acquisition function changes
  const [expandedConstraints, setExpandedConstraints] = useState(false) // Track constraints expansion
  const [pendingConstraintsChanges, setPendingConstraintsChanges] =
    useState<any>(null) // Track pending constraints changes
  const [expandedTargets, setExpandedTargets] = useState<string[]>([]) // Track expanded target rows
  const [allParametersExpanded, setAllParametersExpanded] = useState(false) // Track whether all parameters/targets are expanded
  const [pendingTargetChanges, setPendingTargetChanges] = useState<
    Record<string, any>
  >({}) // Track pending target changes
  const [showPreviewDialog, setShowPreviewDialog] = useState(false) // Track preview dialog state
  const [configurationKey, setConfigurationKey] = useState(0) // Force re-render key

  // Memoize acquisition config to prevent unnecessary re-renders
  // This fixes the issue where input fields become unresponsive due to object reference changes
  const memoizedAcquisitionConfig = useMemo(() => {
    return (
      (optimizationState.config as any)?.acquisition_config || {
        type: "qExpectedImprovement" as const
      }
    )
  }, [optimizationState.config])

  // Debug: Log when optimization state changes
  useEffect(() => {
    console.log("Optimization state changed:", optimizationState)
    console.log(
      "Parameters in config:",
      (optimizationState.config as any)?.parameters
    )
    console.log(
      "Configuration tab will display from optimizationState.config:",
      optimizationState.config as any
    )
  }, [optimizationState])

  // Force configuration re-render when optimization state config changes
  useEffect(() => {
    if (optimizationState.config) {
      console.log(
        "Configuration changed, forcing re-render. Config hash:",
        JSON.stringify(optimizationState.config).slice(0, 100)
      )
      console.log("Current configurationKey:", configurationKey)
      console.log(
        "Parameters count:",
        (optimizationState.config as any)?.parameters?.length
      )
      console.log(
        "Target config:",
        (optimizationState.config as any)?.target_config
      )
      setConfigurationKey(prev => {
        const newKey = prev + 1
        console.log("Updated configurationKey to:", newKey)
        return newKey
      })
    }
  }, [optimizationState.config, (optimizationState as any).lastUpdated])

  // Helper function to format numbers with the specified precision
  const formatNumber = (
    value: number,
    precision: number = decimalPrecision
  ) => {
    return value.toFixed(precision)
  }

  const refreshBestPoint = async () => {
    setIsLoadingBest(true)
    try {
      const result = await getBestPointWorkflowAction(optimization.optimizerId)
      if (result.isSuccess && result.data) {
        setBestPoint(result.data)
      } else {
        toast({
          title: "Error loading best point",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error refreshing best point:", error)
      toast({
        title: "Error",
        description: "Failed to refresh best point",
        variant: "destructive"
      })
    } finally {
      setIsLoadingBest(false)
    }
  }

  const refreshMeasurements = async () => {
    setIsLoadingHistory(true)
    try {
      const result = await getMeasurementsAction(optimization.id)
      if (result.isSuccess && result.data) {
        setMeasurements(result.data)
      } else {
        toast({
          title: "Error loading measurements",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error refreshing measurements:", error)
      toast({
        title: "Error",
        description: "Failed to refresh measurements history",
        variant: "destructive"
      })
    } finally {
      setIsLoadingHistory(false)
    }
  }

  // Handle measurement update with optimistic updates
  const handleMeasurementUpdated = (updatedMeasurement: SelectMeasurement) => {
    // Optimistically update the UI
    setMeasurements(prev =>
      prev.map(m => (m.id === updatedMeasurement.id ? updatedMeasurement : m))
    )

    // Refresh best point as it might have changed
    // This is handled by the dependency action, but we refresh here for immediate UI feedback
    refreshBestPoint().catch(error => {
      console.error(
        "Error refreshing best point after measurement update:",
        error
      )
      // Don't show error to user as this is a background operation
    })
  }

  // Handle measurement deletion with optimistic updates
  const handleMeasurementDeleted = (measurementId: string) => {
    // Optimistically update the UI
    setMeasurements(prev => prev.filter(m => m.id !== measurementId))

    // Refresh best point as it might have changed
    // This is handled by the dependency action, but we refresh here for immediate UI feedback
    refreshBestPoint().catch(error => {
      console.error(
        "Error refreshing best point after measurement deletion:",
        error
      )
      // If there's an error, we could potentially rollback the UI state
      // For now, we'll just log the error
    })
  }

  // Handle bounds update
  const handleBoundsUpdated = async (result: any) => {
    console.log(
      "🔥🔥🔥 handleBoundsUpdated CALLED - NEW VERSION with result:",
      result
    )
    try {
      // First, optimistically update the local state with the result data
      console.log("handleBoundsUpdated - result data:", result)
      if (
        result.updated_parameters ||
        result.updated_targets ||
        pendingAcquisitionChanges ||
        pendingConstraintsChanges
      ) {
        const currentConfig = optimizationState.config as any
        const updatedConfig = { ...currentConfig }

        // Merge updated parameters while preserving original structure
        if (result.updated_parameters) {
          console.log("Merging updated parameters:", result.updated_parameters)
          console.log("Current parameters:", currentConfig.parameters)

          // Create a map of updated parameters by name for easy lookup
          const updatedParamsMap = new Map()
          result.updated_parameters.forEach((param: any) => {
            updatedParamsMap.set(param.name, param)
          })

          // Merge with existing parameters, preserving original structure but updating bounds/values
          const mergedParameters = currentConfig.parameters.map(
            (originalParam: any) => {
              const updatedParam = updatedParamsMap.get(originalParam.name)
              if (updatedParam) {
                console.log(`Merging parameter ${originalParam.name}:`, {
                  original: originalParam,
                  updated: updatedParam
                })
                // Preserve original structure but update specific fields
                return {
                  ...originalParam,
                  bounds: updatedParam.bounds || originalParam.bounds,
                  values: updatedParam.values || originalParam.values
                  // Preserve other fields like encoding, tolerance, etc.
                }
              }
              return originalParam
            }
          )

          updatedConfig.parameters = mergedParameters
          console.log("Merged parameters result:", mergedParameters)
        }

        // Merge updated targets while preserving original structure
        if (result.updated_targets) {
          console.log("Merging updated targets:", result.updated_targets)
          console.log("Current target_config:", currentConfig.target_config)

          // Handle both single target (object) and multi-target (array) cases
          if (Array.isArray(currentConfig.target_config)) {
            // Multi-target case - merge array
            const updatedTargetsMap = new Map()
            if (Array.isArray(result.updated_targets)) {
              result.updated_targets.forEach((target: any) => {
                updatedTargetsMap.set(target.name, target)
              })
            }

            const mergedTargets = currentConfig.target_config.map(
              (originalTarget: any) => {
                const updatedTarget = updatedTargetsMap.get(originalTarget.name)
                if (updatedTarget) {
                  console.log(`Merging target ${originalTarget.name}:`, {
                    original: originalTarget,
                    updated: updatedTarget
                  })
                  return {
                    ...originalTarget,
                    bounds: updatedTarget.bounds || originalTarget.bounds,
                    mode: updatedTarget.mode || originalTarget.mode,
                    weight: updatedTarget.weight || originalTarget.weight,
                    transformation:
                      updatedTarget.transformation ||
                      originalTarget.transformation,
                    type: updatedTarget.type || originalTarget.type
                    // Preserve any other fields
                  }
                }
                return originalTarget
              }
            )

            updatedConfig.target_config = mergedTargets
          } else {
            // Single target case - merge object
            console.log("Merging single target:", {
              original: currentConfig.target_config,
              updated: result.updated_targets
            })
            updatedConfig.target_config = {
              ...currentConfig.target_config,
              bounds:
                result.updated_targets.bounds ||
                currentConfig.target_config.bounds,
              mode:
                result.updated_targets.mode || currentConfig.target_config.mode,
              weight:
                result.updated_targets.weight ||
                currentConfig.target_config.weight,
              transformation:
                result.updated_targets.transformation ||
                currentConfig.target_config.transformation,
              type:
                result.updated_targets.type || currentConfig.target_config.type
              // Preserve any other fields
            }
          }

          console.log("Merged targets result:", updatedConfig.target_config)
        }

        // Merge acquisition function changes if they exist
        if (pendingAcquisitionChanges) {
          console.log(
            "Merging acquisition function changes:",
            pendingAcquisitionChanges
          )
          console.log(
            "Current acquisition_config:",
            currentConfig.acquisition_config
          )

          updatedConfig.acquisition_config = {
            ...currentConfig.acquisition_config,
            ...pendingAcquisitionChanges
          }

          console.log(
            "Merged acquisition config result:",
            updatedConfig.acquisition_config
          )
        }

        // Merge constraints changes if they exist
        if (pendingConstraintsChanges) {
          console.log(
            "🔧 CONSTRAINTS MERGE - Merging constraints changes:",
            pendingConstraintsChanges
          )
          console.log(
            "🔧 CONSTRAINTS MERGE - Current constraints:",
            currentConfig.constraints
          )

          updatedConfig.constraints = pendingConstraintsChanges

          console.log(
            "🔧 CONSTRAINTS MERGE - Merged constraints result:",
            updatedConfig.constraints
          )
        }

        // Use React.startTransition to ensure state updates are properly batched and prioritized
        React.startTransition(() => {
          // Update optimization state with new configuration
          setOptimizationState(
            prev =>
              ({
                ...prev,
                config: updatedConfig,
                lastUpdated: Date.now() // Force re-render
              }) as any
          )

          // Clear all pending changes since they've been applied
          setPendingParameterChanges({})
          setPendingTargetChanges({})
          setPendingAcquisitionChanges(null)
          setPendingConstraintsChanges(null)

          // Force configuration re-render with a slight delay to ensure state is updated
          setTimeout(() => {
            setConfigurationKey(prev => prev + 1)
            console.log("Incremented configuration key to force re-render")
          }, 0)
        })

        console.log(
          "Optimistically updated optimization state with merged data",
          updatedConfig
        )
        console.log(
          "Cleared all pending changes after successful bounds update"
        )

        // Update the database with the new configuration
        try {
          const { updateOptimizationConfigByOptimizerIdAction } = await import(
            "@/actions/db/optimizations-actions"
          )
          console.log("🔍 Database Update - Updating config:", {
            optimizerId: optimizationState.optimizerId,
            updatedConfig: updatedConfig,
            acquisitionConfig: updatedConfig.acquisition_config
          })
          const dbUpdateResult =
            await updateOptimizationConfigByOptimizerIdAction(
              optimizationState.optimizerId,
              updatedConfig
            )
          if (dbUpdateResult.isSuccess) {
            console.log("Database configuration updated successfully")
          } else {
            console.warn(
              "Failed to update database configuration:",
              dbUpdateResult.message
            )
          }
        } catch (dbError) {
          console.error("Error updating database configuration:", dbError)
        }
      } else {
        console.warn(
          "No updated_parameters or updated_targets in result:",
          result
        )
      }

      // Refresh measurements to show filtered data
      await refreshMeasurements()

      // Refresh best point as it will have changed
      await refreshBestPoint()

      // The optimistic update above should be sufficient since the backend
      // already returned the updated configuration in the result
      console.log(
        "Configuration update complete - using optimistic update from result data"
      )

      toast({
        title: "Bounds Updated Successfully!",
        description: `Campaign retrained with ${result.measurements_kept} measurements. ${result.new_suggestions?.length || 0} new suggestions available.`,
        variant: "default"
      })
    } catch (error) {
      console.error("Error handling bounds update:", error)
      toast({
        title: "Error",
        description: "Failed to refresh data after bounds update",
        variant: "destructive"
      })
    }
  }

  // Handle campaign recreation
  const handleRecreateOptimization = async () => {
    console.log(
      `[handleRecreateOptimization] Starting recreation for optimization: ${optimization.optimizerId}`
    )
    setIsRecreating(true)
    try {
      console.log(
        `[handleRecreateOptimization] Calling recreateOptimizationWorkflowAction`
      )
      const result = await recreateOptimizationWorkflowAction(
        optimization.optimizerId
      )

      if (result.isSuccess) {
        toast({
          title: "Campaign Recreated Successfully!",
          description: result.message,
          variant: "default"
        })

        // Refresh the page data - the optimizer ID should be the same
        try {
          await refreshMeasurements()
        } catch (error) {
          console.warn(
            "Could not refresh measurements after recreation:",
            error
          )
        }

        try {
          await refreshBestPoint()
        } catch (error) {
          console.warn(
            "Could not refresh best point after recreation (this is normal for newly recreated campaigns):",
            error
          )
        }
      } else {
        toast({
          title: "Recreation Failed",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error recreating optimization:", error)
      toast({
        title: "Error",
        description: "Failed to recreate optimization campaign",
        variant: "destructive"
      })
    } finally {
      setIsRecreating(false)
    }
  }

  // Helper functions for parameter editing
  const toggleParameterExpansion = (parameterName: string) => {
    setExpandedParameters(prev => {
      const newExpanded = prev.includes(parameterName)
        ? prev.filter(name => name !== parameterName)
        : [...prev, parameterName]

      // Update allParametersExpanded state based on whether all are expanded
      const allParameterNames = (
        optimizationState.config as any
      ).parameters.map((p: any) => p.name)
      const allTargetNames = isMultiTarget
        ? (optimizationState.config as any).target_config.map(
            (t: any) => t.name
          )
        : [optimizationState.targetName]

      // Check if all parameters and targets will be expanded
      const allExpanded =
        allParameterNames.every((name: string) => newExpanded.includes(name)) &&
        allTargetNames.every((name: string) => expandedTargets.includes(name))
      setAllParametersExpanded(allExpanded)

      return newExpanded
    })
  }

  const handleParameterSave = async (updatedParameter: any) => {
    try {
      // Store the pending change
      setPendingParameterChanges(prev => ({
        ...prev,
        [updatedParameter.name]: updatedParameter
      }))

      // Collapse the expanded parameter
      setExpandedParameters(prev =>
        prev.filter(name => name !== updatedParameter.name)
      )

      toast({
        title: "Parameter Updated",
        description: `${updatedParameter.name} has been updated. Click "Preview & Apply" to review and save all modifications.`,
        variant: "default"
      })
    } catch (error) {
      console.error("Error saving parameter:", error)
      toast({
        title: "Error",
        description: "Failed to update parameter",
        variant: "destructive"
      })
    }
  }

  const handleParameterCancel = (parameterName: string) => {
    // Remove from pending changes if it exists
    setPendingParameterChanges(prev => {
      const updated = { ...prev }
      delete updated[parameterName]
      return updated
    })

    // Collapse the expanded parameter
    setExpandedParameters(prev => prev.filter(name => name !== parameterName))
  }

  const applyAllParameterChanges = () => {
    const hasParameterChanges = Object.keys(pendingParameterChanges).length > 0
    const hasTargetChanges = Object.keys(pendingTargetChanges).length > 0
    const hasAcquisitionChanges = pendingAcquisitionChanges !== null
    const hasConstraintsChanges = pendingConstraintsChanges !== null

    console.log("🎯 APPLY CHANGES - Checking pending changes:")
    console.log(
      "  - Parameter changes:",
      Object.keys(pendingParameterChanges).length,
      pendingParameterChanges
    )
    console.log(
      "  - Target changes:",
      Object.keys(pendingTargetChanges).length,
      pendingTargetChanges
    )
    console.log(
      "  - Acquisition changes:",
      hasAcquisitionChanges,
      pendingAcquisitionChanges
    )
    console.log(
      "  - Constraints changes:",
      hasConstraintsChanges,
      pendingConstraintsChanges
    )

    if (
      hasParameterChanges ||
      hasTargetChanges ||
      hasAcquisitionChanges ||
      hasConstraintsChanges
    ) {
      console.log("🎯 APPLY CHANGES - Opening preview dialog")
      // Open the preview dialog to review changes before applying
      setShowPreviewDialog(true)
    } else {
      console.log("🎯 APPLY CHANGES - No changes to apply")
    }
  }

  const discardAllParameterChanges = () => {
    setPendingParameterChanges({})
    setPendingTargetChanges({})
    setPendingAcquisitionChanges(null)
    setPendingConstraintsChanges(null)
    setExpandedParameters([])
    setExpandedTargets([])
    setAllParametersExpanded(false)
    setExpandedAcquisitionFunction(false)
    setExpandedConstraints(false)
    toast({
      title: "Changes Discarded",
      description: "All pending configuration changes have been discarded.",
      variant: "default"
    })
  }

  const toggleAllParameters = () => {
    const newExpandedState = !allParametersExpanded
    setAllParametersExpanded(newExpandedState)

    if (newExpandedState) {
      // Expand all parameters
      const allParameterNames = (
        optimizationState.config as any
      ).parameters.map((p: any) => p.name)
      setExpandedParameters(allParameterNames)

      // Also expand targets
      const allTargetNames = isMultiTarget
        ? (optimizationState.config as any).target_config.map(
            (t: any) => t.name
          )
        : [optimizationState.targetName]
      setExpandedTargets(allTargetNames)
    } else {
      // Collapse all
      setExpandedParameters([])
      setExpandedTargets([])
    }
  }

  // Reset edit mode when switching tabs or when edit mode is disabled
  const handleEditModeChange = (enabled: boolean) => {
    setIsEditMode(enabled)
    if (!enabled) {
      // When disabling edit mode, collapse all and clear pending changes
      setExpandedParameters([])
      setExpandedTargets([])
      setAllParametersExpanded(false)
      setExpandedAcquisitionFunction(false)
      setExpandedConstraints(false)
      const hasChanges =
        Object.keys(pendingParameterChanges).length > 0 ||
        Object.keys(pendingTargetChanges).length > 0 ||
        pendingAcquisitionChanges ||
        pendingConstraintsChanges
      if (hasChanges) {
        // Ask user if they want to discard changes
        const shouldDiscard = confirm(
          "You have unsaved changes. Do you want to discard them?"
        )
        if (shouldDiscard) {
          setPendingParameterChanges({})
          setPendingTargetChanges({})
          setPendingAcquisitionChanges(null)
          setPendingConstraintsChanges(null)
        } else {
          // Re-enable edit mode if user doesn't want to discard
          setIsEditMode(true)
          return
        }
      }
    }
  }

  const handleAcquisitionFunctionSave = (updatedConfig: any) => {
    setPendingAcquisitionChanges(updatedConfig)
    setExpandedAcquisitionFunction(false)

    toast({
      title: "Acquisition Function Updated",
      description:
        'Acquisition function configuration has been updated. Click "Preview & Apply" to review and save all modifications.',
      variant: "default"
    })
  }

  const handleAcquisitionFunctionCancel = () => {
    setPendingAcquisitionChanges(null)
    setExpandedAcquisitionFunction(false)
  }

  const handleConstraintsSave = (updatedConstraints: any[]) => {
    console.log(
      "🔧 CONSTRAINTS SAVE - Received updated constraints:",
      updatedConstraints
    )
    setPendingConstraintsChanges(updatedConstraints)
    setExpandedConstraints(false)

    toast({
      title: "Constraints Updated",
      description:
        'Constraints configuration has been updated. Click "Preview & Apply" to review and save all modifications.',
      variant: "default"
    })
  }

  const handleConstraintsCancel = () => {
    setPendingConstraintsChanges(null)
    setExpandedConstraints(false)
  }

  const toggleTargetExpansion = (targetName: string) => {
    setExpandedTargets(prev => {
      const newExpanded = prev.includes(targetName)
        ? prev.filter(name => name !== targetName)
        : [...prev, targetName]

      // Update allParametersExpanded state based on whether all are expanded
      const allParameterNames = (
        optimizationState.config as any
      ).parameters.map((p: any) => p.name)
      const allTargetNames = isMultiTarget
        ? (optimizationState.config as any).target_config.map(
            (t: any) => t.name
          )
        : [optimizationState.targetName]

      // Check if all parameters and targets will be expanded
      const allExpanded =
        allParameterNames.every((name: string) =>
          expandedParameters.includes(name)
        ) && allTargetNames.every((name: string) => newExpanded.includes(name))
      setAllParametersExpanded(allExpanded)

      return newExpanded
    })
  }

  const handleTargetSave = async (updatedTarget: any) => {
    try {
      console.log("🎯 TARGET SAVE - Saving target:", updatedTarget)

      // Store the pending change
      setPendingTargetChanges(prev => {
        const newChanges = {
          ...prev,
          [updatedTarget.name]: updatedTarget
        }
        console.log(
          "🎯 TARGET SAVE - Updated pending target changes:",
          newChanges
        )
        return newChanges
      })

      // Collapse the expanded target
      setExpandedTargets(prev =>
        prev.filter(name => name !== updatedTarget.name)
      )

      toast({
        title: "Target Updated",
        description: `${updatedTarget.name} has been updated. Click "Preview & Apply" to review and save all modifications.`,
        variant: "default"
      })
    } catch (error) {
      console.error("Error saving target:", error)
      toast({
        title: "Error",
        description: "Failed to update target",
        variant: "destructive"
      })
    }
  }

  const handleTargetCancel = (targetName: string) => {
    // Remove from pending changes if it exists
    setPendingTargetChanges(prev => {
      const updated = { ...prev }
      delete updated[targetName]
      return updated
    })

    // Collapse the expanded target
    setExpandedTargets(prev => prev.filter(name => name !== targetName))
  }

  const handlePreviewConfirm = async () => {
    console.log("🔥🔥🔥 handlePreviewConfirm CALLED - NEW VERSION")
    console.log("🔥🔥🔥 pendingParameterChanges:", pendingParameterChanges)
    try {
      // Apply the changes by calling the configuration update API
      const hasParameterChanges =
        Object.keys(pendingParameterChanges).length > 0
      const hasTargetChanges = Object.keys(pendingTargetChanges).length > 0
      const hasAcquisitionChanges = pendingAcquisitionChanges !== null
      const hasConstraintsChanges = pendingConstraintsChanges !== null

      // Prepare parameter bounds from pending changes - match original dialog format
      const parameterBounds: Record<string, [number, number]> = {}
      const parameterOrder: string[] = []
      const updatedParameters: any[] = []

      // Get original parameters and apply pending changes
      const originalParams = (optimizationState.config as any).parameters || []
      originalParams.forEach((param: any) => {
        const pendingParam = pendingParameterChanges[param.name]
        const finalParam = pendingParam || param

        parameterOrder.push(param.name)

        // IMPORTANT: Include ALL parameters in updated_parameters, not just those with pending changes
        // This ensures that previously applied changes are preserved when making subsequent updates
        updatedParameters.push({
          name: finalParam.name,
          type: finalParam.type,
          bounds: finalParam.bounds,
          values: finalParam.values,
          tolerance: finalParam.tolerance,
          encoding: finalParam.encoding
        })

        // Only add bounds for NumericalContinuous parameters (matches original dialog)
        if (finalParam.type === "NumericalContinuous" && finalParam.bounds) {
          parameterBounds[param.name] = finalParam.bounds
        }
      })

      // Prepare target configuration - match the original dialog logic
      const targetConfigForAPI: any[] = []
      const originalTargetConfig = (optimizationState.config as any)
        .target_config

      console.log("=== APPLY TARGET CONFIG DEBUG ===")
      console.log("Original target config:", originalTargetConfig)
      console.log("Pending target changes:", pendingTargetChanges)

      if (Array.isArray(originalTargetConfig)) {
        // Multi-target case
        originalTargetConfig.forEach((target: any) => {
          const pendingTarget = pendingTargetChanges[target.name]
          const finalTarget = pendingTarget || target

          console.log(`Multi-target ${target.name}:`, {
            original: target,
            pending: pendingTarget,
            final: finalTarget
          })

          targetConfigForAPI.push({
            name: finalTarget.name,
            mode: finalTarget.mode || "MAX",
            weight: finalTarget.weight || 1.0,
            transformation: finalTarget.transformation || "LINEAR",
            type: finalTarget.type || "Numerical",
            bounds: finalTarget.bounds
          })
        })
      } else {
        // Single target case
        const targetName = optimizationState.targetName
        const pendingTarget = pendingTargetChanges[targetName]
        const finalTarget = pendingTarget || {
          name: targetName,
          mode: originalTargetConfig?.mode || "MAX",
          weight: originalTargetConfig?.weight || 1.0,
          transformation: originalTargetConfig?.transformation || "LINEAR",
          type: originalTargetConfig?.type || "Numerical",
          bounds: originalTargetConfig?.bounds
        }

        console.log(`Single target ${targetName}:`, {
          original: originalTargetConfig,
          pending: pendingTarget,
          final: finalTarget
        })

        targetConfigForAPI.push({
          name: finalTarget.name,
          mode: finalTarget.mode,
          weight: finalTarget.weight || 1.0,
          transformation: finalTarget.transformation || "LINEAR",
          type: finalTarget.type || "Numerical",
          bounds: finalTarget.bounds
        })
      }

      console.log("Final target config for API:", targetConfigForAPI)
      console.log("=== END APPLY TARGET CONFIG DEBUG ===")

      // Prepare acquisition function config
      const acquisitionConfig = pendingAcquisitionChanges ||
        (optimizationState.config as any).acquisition_config || {
          type: "qExpectedImprovement"
        }

      // Prepare constraints config
      const constraintsConfig =
        pendingConstraintsChanges ||
        (optimizationState.config as any).constraints ||
        []

      console.log("🔧 CONSTRAINTS DEBUG - Preparing constraints for API:")
      console.log("  - pendingConstraintsChanges:", pendingConstraintsChanges)
      console.log(
        "  - original constraints:",
        (optimizationState.config as any).constraints
      )
      console.log("  - final constraintsConfig:", constraintsConfig)

      const requestBody = {
        parameter_bounds: parameterBounds,
        updated_parameters:
          updatedParameters.length > 0 ? updatedParameters : undefined,
        target_config: targetConfigForAPI,
        acquisition_config: acquisitionConfig,
        constraints:
          constraintsConfig.length > 0 ? constraintsConfig : undefined,
        parameter_order: parameterOrder,
        preview_only: false
        // Note: suggestion_count removed - suggestions will only be generated if explicitly requested
      }

      console.log(
        "Apply request - pending parameter changes:",
        pendingParameterChanges
      )
      console.log("Apply request - updated parameters:", updatedParameters)
      console.log("Apply request body:", requestBody)

      const response = await fetch(
        `/api/optimizations/${optimizationState.optimizerId}/bounds`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(requestBody)
        }
      )

      console.log("Apply response status:", response.status)
      console.log("Apply response headers:", response.headers)

      // Check if response has content
      const responseText = await response.text()
      console.log("Apply response text:", responseText)

      if (!responseText) {
        throw new Error("Empty response from server")
      }

      let result
      try {
        result = JSON.parse(responseText)
      } catch (parseError) {
        console.error("JSON parse error:", parseError)
        throw new Error(
          `Invalid JSON response: ${responseText.slice(0, 100)}...`
        )
      }

      if (!response.ok) {
        throw new Error(
          result.error ||
            `Server error: ${response.status} ${response.statusText}`
        )
      }

      // Close preview dialog
      setShowPreviewDialog(false)

      // Call the onConfigurationUpdated callback to refresh the data
      // This will handle all the state updates properly
      if (handleBoundsUpdated) {
        await handleBoundsUpdated(result)
      } else {
        console.warn("handleBoundsUpdated function not available")
      }

      // Clear UI state after successful update
      setExpandedParameters([])
      setExpandedTargets([])
      setAllParametersExpanded(false)
      setExpandedAcquisitionFunction(false)
      setExpandedConstraints(false)

      toast({
        title: "Configuration Updated",
        description:
          "Your changes have been applied successfully. The optimization will be retrained with the new configuration.",
        variant: "default"
      })
    } catch (error) {
      console.error("Apply configuration error:", error)
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      })
    }
  }

  const handlePreviewCancel = () => {
    setShowPreviewDialog(false)
  }

  // Helper function to get total pending changes count
  const getTotalPendingChanges = () => {
    return (
      Object.keys(pendingParameterChanges).length +
      Object.keys(pendingTargetChanges).length +
      (pendingAcquisitionChanges ? 1 : 0) +
      (pendingConstraintsChanges ? 1 : 0)
    )
  }

  // Helper function to check if any changes are pending
  const hasPendingChanges = () => {
    return (
      Object.keys(pendingParameterChanges).length > 0 ||
      Object.keys(pendingTargetChanges).length > 0 ||
      pendingAcquisitionChanges !== null ||
      pendingConstraintsChanges !== null
    )
  }

  // Prepare data for export
  const prepareExportData = () => {
    // Create a flattened array of all measurements with experiment numbers
    const exportData: Record<string, any>[] = []

    // Group measurements into batches (similar to the table rendering logic)
    const batches: (typeof measurements)[] = []
    const measurementsCopy = [...measurements]

    // Sort by creation date (newest first)
    measurementsCopy.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    })

    // Group by batch ID (simplified version of the table rendering logic)
    while (measurementsCopy.length > 0) {
      const current = measurementsCopy.shift()!
      const batchId = current.batchId

      if (batchId) {
        const batchItems = [current]
        let i = 0
        while (i < measurementsCopy.length) {
          if (measurementsCopy[i].batchId === batchId) {
            batchItems.push(measurementsCopy.splice(i, 1)[0])
          } else {
            i++
          }
        }
        batches.push(batchItems)
      } else {
        batches.push([current])
      }
    }

    // Sort batches by creation date (newest first)
    batches.sort((a, b) => {
      const aTime = new Date(a[0].createdAt).getTime()
      const bTime = new Date(b[0].createdAt).getTime()
      return bTime - aTime
    })

    // Reverse batches to show oldest first in the export (consistent with History tab)
    const reversedBatches = [...batches].reverse()

    // Create export data with experiment numbers
    let experimentNumber = 1

    reversedBatches.forEach(batch => {
      if (batch.length === 1) {
        // Single measurement
        const measurement = batch[0]

        // Create the data point with experiment number
        const dataPoint: Record<string, any> = {
          "Experiment #": experimentNumber,
          Date: formatDate(new Date(measurement.createdAt)),
          [optimization.targetName]: parseFloat(
            measurement.targetValue
          ).toFixed(decimalPrecision),
          Source: measurement.isRecommended ? "API" : "Manual",
          "Suggestion Type":
            (measurement.parameters as any)?._sampleClass === "exploratory"
              ? "Exploratory"
              : "Sequential"
        }

        // Add all target values for multi-target optimizations
        if (isMultiTarget && measurement.targetValues) {
          const targetValues = measurement.targetValues as Record<
            string,
            number
          >
          Object.entries(targetValues).forEach(([key, value]) => {
            if (key !== optimization.targetName) {
              dataPoint[key] = value.toFixed(decimalPrecision)
            }
          })
        }

        // Add all parameter values
        const params = measurement.parameters as Record<string, any>
        parameterNames.forEach((name: string) => {
          if (typeof params[name] === "number") {
            dataPoint[name] = params[name].toFixed(decimalPrecision)
          } else if (params[name] !== undefined) {
            dataPoint[name] = String(params[name])
          } else {
            dataPoint[name] = ""
          }
        })

        exportData.push(dataPoint)
      } else {
        // For batches, create individual data points for each item
        batch.forEach((measurement, batchIndex) => {
          // Create the data point with experiment number and sub-index
          const dataPoint: Record<string, any> = {
            "Experiment #": `${experimentNumber}.${batchIndex + 1}`,
            Date: formatDate(new Date(measurement.createdAt)),
            [optimization.targetName]: parseFloat(
              measurement.targetValue
            ).toFixed(decimalPrecision),
            Source: measurement.isRecommended ? "API" : "Manual",
            "Suggestion Type":
              (measurement.parameters as any)?._sampleClass === "exploratory"
                ? "Exploratory"
                : "Batch Item"
          }

          // Add all target values for multi-target optimizations
          if (isMultiTarget && measurement.targetValues) {
            const targetValues = measurement.targetValues as Record<
              string,
              number
            >
            Object.entries(targetValues).forEach(([key, value]) => {
              if (key !== optimization.targetName) {
                dataPoint[key] = value.toFixed(decimalPrecision)
              }
            })
          }

          // Add all parameter values
          const params = measurement.parameters as Record<string, any>
          parameterNames.forEach((name: string) => {
            if (typeof params[name] === "number") {
              dataPoint[name] = params[name].toFixed(decimalPrecision)
            } else if (params[name] !== undefined) {
              dataPoint[name] = String(params[name])
            } else {
              dataPoint[name] = ""
            }
          })

          exportData.push(dataPoint)
        })
      }

      // Increment experiment number for the next batch
      experimentNumber++
    })

    return exportData
  }

  // Handle export to CSV
  const handleExportCSV = async () => {
    if (measurements.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no measurements available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export
      const exportData = prepareExportData()

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-measurements-${new Date().toISOString().split("T")[0]}.csv`

      // Download the CSV
      downloadCSV(exportData, filename)

      toast({
        title: "Export successful",
        description: "The measurements data has been exported as CSV.",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting measurements to CSV:", error)
      toast({
        title: "Export failed",
        description: "Failed to export measurements data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Handle export to Excel (.xlsx)
  const handleExportExcel = async () => {
    if (measurements.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no measurements available to export.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsExporting(true)

      // Format the data for export
      const exportData = prepareExportData()

      // Generate filename with optimization name and current date
      const sanitizedName = optimization.name.replace(/[^a-zA-Z0-9-_]/g, "_")
      const filename = `${sanitizedName}-measurements-${new Date().toISOString().split("T")[0]}.xlsx`

      // Download as Excel (.xlsx) with template-style formatting
      await downloadExcel(
        exportData,
        filename,
        undefined,
        true,
        optimizationState.config
      )

      toast({
        title: "Export successful",
        description:
          "The measurements data has been exported as Excel (.xlsx) file.",
        variant: "default"
      })
    } catch (error) {
      console.error("Error exporting measurements to Excel:", error)
      toast({
        title: "Export failed",
        description: "Failed to export measurements data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Determine if this is a multi-target optimization
  const isMultiTarget = optimization.targetMode === "MULTI"

  // Determine if this is a Pareto objective (weights should be hidden)
  const objectiveType = getObjectiveType(optimization)
  const isPareto = objectiveType === "MULTI_PARETO"

  // Get the total number of targets from the configuration
  const getTargetCount = () => {
    if (!isMultiTarget) return 1

    const targetConfig = (optimizationState.config as any).target_config
    if (Array.isArray(targetConfig)) {
      return targetConfig.length
    }
    return 1
  }

  const targetCount = getTargetCount()

  // Prepare chart data
  const primaryTargetMode = getTargetMode(optimization, optimization.targetName)
  let chartData = calculateBestValues(
    formatChartData(measurements, optimization.targetName),
    primaryTargetMode,
    optimization.targetName,
    isMultiTarget
  )

  // Debug chart data
  console.log("Chart data:", chartData)

  // For multi-target optimization, add composite score to chart data
  if (isMultiTarget && measurements.length > 0) {
    const targetConfigs = (optimizationState.config as any).target_config || []

    // Create a map of target names to their modes
    const targetModes: Record<string, "MAX" | "MIN"> = {}
    targetConfigs.forEach((target: any) => {
      targetModes[target.name] = target.mode as "MAX" | "MIN"
    })

    // Calculate min/max values for each target for normalization
    const targetMinMax: Record<string, { min: number; max: number }> = {}
    Object.keys(targetModes).forEach(targetName => {
      const values = measurements
        .map(m => {
          if (
            m.targetValues &&
            typeof m.targetValues === "object" &&
            targetName in (m.targetValues as Record<string, number>)
          ) {
            return (m.targetValues as Record<string, number>)[targetName]
          }
          return targetName === optimization.targetName
            ? parseFloat(m.targetValue)
            : NaN
        })
        .filter(v => !isNaN(v))

      if (values.length > 0) {
        targetMinMax[targetName] = {
          min: Math.min(...values),
          max: Math.max(...values)
        }
      }
    })

    // If we have best point data but no composite score, calculate it
    if (bestPoint.best_values && !bestPoint.composite_score) {
      console.log("Calculating composite score for best point")
      let bestScore = 0
      let totalWeight = 0
      const normalizedValues: Record<string, number> = {}
      const targetWeights: Record<string, number> = {}

      Object.keys(targetModes).forEach(targetName => {
        if (
          bestPoint.best_values &&
          targetName in bestPoint.best_values &&
          targetMinMax[targetName]
        ) {
          const value = bestPoint.best_values[targetName]
          const { min, max } = targetMinMax[targetName]
          const weight = 1 / Object.keys(targetModes).length // Equal weights

          // Skip if min equals max (no variation)
          if (min === max) return

          // Normalize value between 0 and 1
          let normalized = (value - min) / (max - min)

          // Invert for MIN targets
          if (targetModes[targetName] === "MIN") {
            normalized = 1 - normalized
          }

          bestScore += normalized * weight
          totalWeight += weight

          // Store normalized value and weight
          normalizedValues[targetName] = normalized
          targetWeights[targetName] = weight
        }
      })

      // Add composite score to the best point
      if (totalWeight > 0) {
        bestPoint.composite_score = bestScore
        bestPoint.normalized_values = normalizedValues
        bestPoint.target_weights = targetWeights
        console.log("Calculated composite score:", bestScore)
        console.log("Normalized values:", normalizedValues)
        console.log("Target weights:", targetWeights)
      }
    }
  }

  // Prepare data series for other targets in multi-target optimization
  const otherTargets: string[] = []

  if (
    isMultiTarget &&
    measurements.length > 0 &&
    measurements[0].targetValues
  ) {
    // Get all target names from the first measurement's targetValues
    const firstMeasurement = measurements[0]
    if (firstMeasurement.targetValues) {
      Object.keys(firstMeasurement.targetValues).forEach(targetName => {
        if (targetName !== optimization.targetName) {
          otherTargets.push(targetName)
        }
      })
    }
    console.log("Other targets for multi-target visualization:", otherTargets)
  }

  // Determine the parameter names from the configuration
  const parameterNames = (
    (optimizationState.config as any).parameters || []
  ).map((param: any) => param.name)

  // Function to toggle expand/collapse all batches
  const toggleAllBatches = () => {
    // Toggle the state
    const newExpandedState = !allExpanded
    setAllExpanded(newExpandedState)

    // Update all batch states
    const batchStates: Record<string, boolean> = {}
    batchIds.forEach(id => {
      batchStates[id] = newExpandedState
    })
    setExpandedBatches(batchStates)

    // Show toast notification
    toast({
      title: newExpandedState
        ? "Expanded all batches"
        : "Collapsed all batches",
      description: `${batchIds.length} batch${batchIds.length !== 1 ? "es" : ""} ${newExpandedState ? "expanded" : "collapsed"}`
    })
  }

  return (
    <>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="mb-6 flex flex-col justify-between sm:flex-row sm:items-center">
          <h2 className="text-2xl font-bold">{optimization.name}</h2>
          <TabsList className="mt-4 sm:mt-0">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="configuration">Configuration</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview" className="space-y-6">
          {/* Best Point Card */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div className="space-y-1">
                <CardTitle className="flex items-center text-lg">
                  <Trophy className="mr-2 size-5 text-yellow-500" />
                  Best Result
                  <BestResultsDocumentation />
                </CardTitle>
                <CardDescription>
                  Current best point found by the optimizer
                </CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshBestPoint}
                disabled={isLoadingBest}
              >
                {isLoadingBest ? (
                  <RotateCw className="mr-2 size-4 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 size-4" />
                )}
                Refresh
              </Button>
            </CardHeader>
            <CardContent>
              {bestPoint.best_parameters ? (
                <div className="space-y-4">
                  {/* For multi-target: Show Composite Score as primary metric */}
                  {isMultiTarget && bestPoint.composite_score !== undefined ? (
                    <div className="bg-primary/10 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Composite Score</h4>
                          <p className="text-muted-foreground mt-1 text-xs">
                            Weighted score combining all targets
                          </p>
                        </div>
                        <span className="text-primary flex items-center text-xl font-bold">
                          {bestPoint.composite_score.toFixed(4)}
                          <Trophy className="text-primary ml-1 size-5" />
                        </span>
                      </div>
                    </div>
                  ) : (
                    /* For single-target: Show target value with smart contextual notes */
                    (() => {
                      const targetConfig = (optimizationState.config as any)
                        .target_config
                      const bounds = targetConfig?.bounds
                      const bestValue = bestPoint.best_value
                      let contextualNote = ""

                      // Generate smart contextual notes for out-of-bounds results
                      if (bounds && bestValue !== undefined) {
                        let lowerBound, upperBound

                        if (Array.isArray(bounds)) {
                          ;[lowerBound, upperBound] = bounds
                        } else if (typeof bounds === "object") {
                          lowerBound = bounds.lower
                          upperBound = bounds.upper
                        }

                        if (
                          lowerBound !== undefined &&
                          upperBound !== undefined
                        ) {
                          const range = upperBound - lowerBound
                          const exceedsUpper = bestValue > upperBound
                          const exceedsLower = bestValue < lowerBound

                          if (exceedsUpper) {
                            const exceedAmount = bestValue - upperBound
                            const exceedPercent = (exceedAmount / range) * 100

                            if (exceedPercent > 50) {
                              contextualNote =
                                "Result significantly exceeds expected bounds - consider expanding the optimization space"
                            } else if (exceedPercent > 20) {
                              contextualNote =
                                "Result suggests broader optimization potential - bounds may need adjustment"
                            } else {
                              contextualNote =
                                "Result slightly above expected range - optimization performing well"
                            }
                          } else if (exceedsLower) {
                            const exceedAmount = lowerBound - bestValue
                            const exceedPercent = (exceedAmount / range) * 100

                            if (exceedPercent > 50) {
                              contextualNote =
                                "Result well below expected bounds - review optimization constraints"
                            } else if (exceedPercent > 20) {
                              contextualNote =
                                "Result below expected range - consider adjusting bounds or parameters"
                            } else {
                              contextualNote =
                                "Result slightly below expected range - within reasonable variation"
                            }
                          }
                        }
                      }

                      return (
                        <div className="bg-muted rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">
                                {optimization.targetName}
                              </h4>
                              {contextualNote && (
                                <p className="mt-1 text-xs text-blue-600">
                                  💡 {contextualNote}
                                </p>
                              )}
                            </div>
                            <span className="flex items-center text-xl font-bold">
                              {bestPoint.best_value?.toFixed(4)}
                              {optimization.targetMode === "MAX" ||
                              optimization.targetMode.includes("MAX") ? (
                                <ArrowUp className="ml-1 size-5 text-green-500" />
                              ) : (
                                <ArrowDown className="ml-1 size-5 text-green-500" />
                              )}
                            </span>
                          </div>
                        </div>
                      )
                    })()
                  )}

                  {/* Target Contributions for multi-target optimization */}
                  {isMultiTarget &&
                    bestPoint.normalized_values &&
                    bestPoint.target_weights && (
                      <div className="mt-2 space-y-2">
                        <h4 className="font-medium">Target Contributions:</h4>
                        <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                          {Object.entries(bestPoint.normalized_values).map(
                            ([targetName, normalizedValue]) => {
                              // Get the raw value from best_values
                              const rawValue =
                                bestPoint.best_values?.[targetName]
                              if (rawValue === undefined) return null

                              // Get the weight
                              const weight =
                                bestPoint.target_weights?.[targetName] ||
                                1.0 /
                                  Object.keys(bestPoint.normalized_values || {})
                                    .length

                              // Get the target config to determine if it's MAX or MIN
                              const targetConfig = Array.isArray(
                                (optimizationState.config as any).target_config
                              )
                                ? (
                                    optimizationState.config as any
                                  ).target_config.find(
                                    (t: any) => t.name === targetName
                                  )
                                : null
                              const targetMode = targetConfig?.mode || ""
                              const isMax =
                                targetMode === "MAX" ||
                                (typeof targetMode === "string" &&
                                  targetMode.includes("MAX"))

                              // Calculate contribution to composite score
                              const contribution = weight * normalizedValue

                              // Generate smart contextual note for this target
                              const bounds = targetConfig?.bounds
                              let targetNote = ""
                              if (bounds && rawValue !== undefined) {
                                let lowerBound, upperBound

                                if (Array.isArray(bounds)) {
                                  ;[lowerBound, upperBound] = bounds
                                } else if (typeof bounds === "object") {
                                  lowerBound = bounds.lower
                                  upperBound = bounds.upper
                                }

                                if (
                                  lowerBound !== undefined &&
                                  upperBound !== undefined
                                ) {
                                  const range = upperBound - lowerBound
                                  const exceedsUpper = rawValue > upperBound
                                  const exceedsLower = rawValue < lowerBound

                                  if (exceedsUpper) {
                                    const exceedPercent =
                                      ((rawValue - upperBound) / range) * 100
                                    if (exceedPercent > 30) {
                                      targetNote =
                                        "Significantly exceeds bounds - consider expanding range"
                                    } else if (exceedPercent > 10) {
                                      targetNote =
                                        "Above expected range - good performance"
                                    }
                                  } else if (exceedsLower) {
                                    const exceedPercent =
                                      ((lowerBound - rawValue) / range) * 100
                                    if (exceedPercent > 30) {
                                      targetNote =
                                        "Well below bounds - review constraints"
                                    } else if (exceedPercent > 10) {
                                      targetNote =
                                        "Below expected range - consider adjustment"
                                    }
                                  }
                                }
                              }

                              return (
                                <div
                                  key={targetName}
                                  className="bg-muted rounded-lg p-3"
                                >
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <h5 className="text-sm font-medium">
                                        {targetName}
                                      </h5>
                                      <div className="text-muted-foreground mt-1 flex items-center text-xs">
                                        <span>
                                          Weight: {(weight * 100).toFixed(0)}%
                                        </span>
                                        <span className="mx-1">•</span>
                                        <span>
                                          Contribution:{" "}
                                          {(contribution * 100).toFixed(0)}%
                                        </span>
                                      </div>
                                      {targetNote && (
                                        <div className="mt-1 text-xs text-blue-600">
                                          💡 {targetNote}
                                        </div>
                                      )}
                                    </div>
                                    <span className="flex items-center text-lg font-bold">
                                      {rawValue.toFixed(4)}
                                      {isMax ? (
                                        <ArrowUp className="ml-1 size-4 text-green-500" />
                                      ) : (
                                        <ArrowDown className="ml-1 size-4 text-green-500" />
                                      )}
                                    </span>
                                  </div>
                                  {/* Progress bar showing normalized value */}
                                  <div className="bg-muted-foreground/20 mt-2 h-1.5 w-full rounded-full">
                                    <div
                                      className="bg-primary h-1.5 rounded-full"
                                      style={{
                                        width: `${normalizedValue * 100}%`
                                      }}
                                    ></div>
                                  </div>
                                </div>
                              )
                            }
                          )}
                        </div>
                      </div>
                    )}

                  <div className="space-y-2">
                    <h4 className="font-medium">Best Parameters:</h4>
                    <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
                      {/* Display parameters in the same order as defined in the Parameter Configuration */}
                      {parameterNames.map((paramName: string) => {
                        // Check if best_parameters exists
                        if (!bestPoint.best_parameters) return null

                        const value = bestPoint.best_parameters[paramName]
                        // Skip if parameter is not in best_parameters
                        if (value === undefined) return null

                        return (
                          <div
                            key={paramName}
                            className="bg-card rounded-md border p-3"
                          >
                            <div className="text-sm font-medium">
                              {paramName}
                            </div>
                            <div className="mt-1 text-sm">
                              {typeof value === "number"
                                ? value.toFixed(value % 1 === 0 ? 0 : 2)
                                : String(value)}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-muted-foreground py-8 text-center">
                  {isLoadingBest ? (
                    <div className="flex flex-col items-center">
                      <RefreshCw className="mb-2 size-8 animate-spin" />
                      <p>Loading best point...</p>
                    </div>
                  ) : (
                    <p>
                      No best point available yet. Add measurements to get
                      started.
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Progress Chart Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Optimization Progress</CardTitle>
              <CardDescription>
                {isMultiTarget
                  ? "Target values over iterations"
                  : "Target value improvement over iterations"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {chartData.length > 0 ? (
                <div className="h-[300px] w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={chartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 30 }} // Increased bottom margin for legend
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis
                        dataKey="experimentLabel"
                        label={{
                          value: "Experiment",
                          position: "insideBottom",
                          offset: -5
                        }}
                        tickFormatter={value => value}
                      />
                      <YAxis
                        label={{
                          value: isMultiTarget
                            ? "Target Values"
                            : optimization.targetName,
                          angle: -90,
                          position: "insideLeft",
                          offset: 0, // Adjust offset to center vertically
                          style: { textAnchor: "middle" } // Center text
                        }}
                      />
                      <Tooltip
                        labelFormatter={value => `Experiment ${value}`}
                      />
                      <Legend
                        verticalAlign="bottom"
                        height={36}
                        wrapperStyle={{
                          paddingTop: "10px",
                          marginBottom: "10px"
                        }}
                      />
                      {/* Always show the primary target line */}
                      <Line
                        type="monotone"
                        dataKey={
                          isMultiTarget
                            ? optimization.targetName
                            : "targetValue"
                        }
                        stroke="#8884d8"
                        name={optimization.targetName}
                        activeDot={{ r: 8 }}
                        isAnimationActive={false}
                        connectNulls
                        strokeWidth={2}
                      />

                      {/* For single-target, show best value line */}
                      {!isMultiTarget && (
                        <Line
                          type="monotone"
                          dataKey="bestValue"
                          stroke="#82ca9d"
                          name={`Best ${optimization.targetName}`}
                          strokeWidth={2}
                          isAnimationActive={false}
                          connectNulls
                        />
                      )}

                      {/* For multi-target, show other target lines */}
                      {isMultiTarget &&
                        otherTargets.map((targetName, index) => (
                          <Line
                            key={targetName}
                            type="monotone"
                            dataKey={targetName}
                            stroke={
                              [
                                "#82ca9d",
                                "#ff7300",
                                "#0088FE",
                                "#00C49F",
                                "#FFBB28"
                              ][index % 5]
                            }
                            name={targetName}
                            activeDot={{ r: 6 }}
                            isAnimationActive={false}
                            connectNulls
                          />
                        ))}
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-muted-foreground py-8 text-center">
                  <p>No measurement data available yet.</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Parameter Impact Analysis */}
          {measurements.length > 0 && (
            <ParameterImpactChart
              measurements={measurements}
              parameterNames={parameterNames}
              targetName={optimization.targetName}
              targetMode={isMultiTarget ? "MULTI" : primaryTargetMode}
              availableTargets={
                isMultiTarget ? [optimization.targetName, ...otherTargets] : []
              }
            />
          )}

          {/* Summary Stats Card */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Measurements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{measurements.length}</div>
                <p className="text-muted-foreground text-xs">
                  Experiments conducted
                </p>
              </CardContent>
            </Card>

            <OptimizationStatusControl
              optimization={optimizationState}
              onStatusChange={newStatus => {
                // Update the local state with the new status
                setOptimizationState(prev => ({
                  ...prev,
                  status: newStatus
                }))

                // Show a toast notification
                toast({
                  title: "Status Updated",
                  description: `Optimization status changed to ${newStatus}`
                })
              }}
            />

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Target Objective
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <div className="text-2xl font-bold">
                    {optimization.targetMode}
                  </div>
                  {optimization.targetMode === "MAX" ||
                  optimization.targetMode === "MULTI" ||
                  (typeof optimization.targetMode === "string" &&
                    optimization.targetMode.includes("MAX")) ? (
                    <ArrowUp className="ml-2 size-5 text-green-500" />
                  ) : (
                    <ArrowDown className="ml-2 size-5 text-green-500" />
                  )}
                </div>
                {optimization.targetMode === "MULTI" ? (
                  <div>
                    <p className="text-muted-foreground text-xs">
                      Multi-target optimization
                    </p>
                    <div className="mt-2 flex items-center gap-1">
                      <Target className="text-muted-foreground size-3" />
                      <span className="text-xs">{targetCount} targets</span>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground text-xs">
                    {optimization.targetMode === "MAX"
                      ? "Maximizing"
                      : "Minimizing"}{" "}
                    {optimization.targetName}
                  </p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">API ID</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-muted-foreground truncate text-sm font-medium">
                  {optimization.optimizerId}
                </div>
                <p className="mt-2 text-xs">
                  <Button
                    variant="link"
                    size="sm"
                    className="h-auto p-0"
                    onClick={() =>
                      router.push(
                        `/dashboard/optimizations/${optimization.id}/run`
                      )
                    }
                  >
                    Run experiments <ChevronRight className="ml-1 size-3" />
                  </Button>
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div className="space-y-1">
                <CardTitle className="flex items-center text-lg">
                  <History className="mr-2 size-5" />
                  Measurement History
                </CardTitle>
                <CardDescription>
                  All recorded measurements for this optimization
                  {measurements &&
                    measurements.length > 0 &&
                    (() => {
                      const totalMeasurements = measurements.length
                      const excludedCount = measurements.filter(m => {
                        if (!m.parameters || !optimizationState.config)
                          return false
                        const config = optimizationState.config as any
                        const parameters = config.parameters || []
                        const measurementParams = m.parameters as Record<
                          string,
                          any
                        >

                        for (const param of parameters) {
                          const paramName = param.name
                          const measurementValue = measurementParams[paramName]
                          if (
                            measurementValue === undefined ||
                            measurementValue === null
                          )
                            continue

                          if (
                            param.type === "NumericalContinuous" &&
                            param.bounds
                          ) {
                            const [min, max] = param.bounds
                            const numValue = parseFloat(measurementValue)
                            if (
                              isNaN(numValue) ||
                              numValue < min ||
                              numValue > max
                            )
                              return true
                          } else if (
                            param.type === "NumericalDiscrete" &&
                            param.values
                          ) {
                            const allowedValues = param.values
                            const numValue = parseFloat(measurementValue)
                            if (
                              isNaN(numValue) ||
                              !allowedValues.includes(numValue)
                            )
                              return true
                          } else if (
                            (param.type === "CategoricalParameter" ||
                              param.type === "Categorical") &&
                            param.values
                          ) {
                            const allowedValues = param.values
                            if (!allowedValues.includes(measurementValue))
                              return true
                          }
                        }
                        return false
                      }).length

                      return excludedCount > 0 ? (
                        <span className="mt-1 block text-sm text-orange-600">
                          {excludedCount} of {totalMeasurements} experiments
                          excluded due to parameter changes
                        </span>
                      ) : null
                    })()}
                </CardDescription>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <label
                    htmlFor="decimal-precision"
                    className="whitespace-nowrap text-sm"
                  >
                    Decimal Places:
                  </label>
                  <select
                    id="decimal-precision"
                    className="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
                    value={decimalPrecision}
                    onChange={e => setDecimalPrecision(Number(e.target.value))}
                  >
                    {[0, 1, 2, 3, 4, 5, 6].map(num => (
                      <option key={num} value={num}>
                        {num}
                      </option>
                    ))}
                  </select>
                </div>
                {batchIds.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleAllBatches}
                    disabled={isLoadingHistory}
                    className="px-2"
                  >
                    <ChevronRight
                      className={`mr-1 size-4 transition-transform ${allExpanded ? "rotate-90" : ""}`}
                    />
                    {allExpanded ? "Collapse All" : "Expand All"}
                  </Button>
                )}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={
                        isLoadingHistory ||
                        isExporting ||
                        measurements.length === 0
                      }
                    >
                      {isExporting ? (
                        <RotateCw className="mr-2 size-4 animate-spin" />
                      ) : (
                        <Download className="mr-2 size-4" />
                      )}
                      Export
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleExportCSV}>
                      <Download className="mr-2 size-4" />
                      <span>Export as CSV</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleExportExcel}>
                      <FileSpreadsheet className="mr-2 size-4" />
                      <span>Export as Excel (.xlsx)</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshMeasurements}
                  disabled={isLoadingHistory}
                >
                  {isLoadingHistory ? (
                    <RotateCw className="mr-2 size-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 size-4" />
                  )}
                  Refresh
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRecreateOptimization}
                  disabled={isRecreating}
                >
                  {isRecreating ? (
                    <RotateCw className="mr-2 size-4 animate-spin" />
                  ) : (
                    <Settings className="mr-2 size-4" />
                  )}
                  Recreate Campaign
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {measurements.length > 0 ? (
                <div className="rounded-md border">
                  {/* Table with horizontal scrolling */}
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {/* Fixed columns - always visible */}
                          <TableHead className="bg-background sticky left-0 z-20 w-[100px]">
                            Experiment #
                          </TableHead>
                          <TableHead className="bg-background sticky left-[100px] z-20">
                            Date
                          </TableHead>

                          {/* Target columns */}
                          <TableHead className="border-l text-right">
                            {optimization.targetName}
                            {isMultiTarget && (
                              <span className="text-muted-foreground block text-xs">
                                (Avg, Min, Max)
                              </span>
                            )}
                          </TableHead>

                          {/* Other targets for multi-target optimization */}
                          {isMultiTarget &&
                            otherTargets.map(targetName => (
                              <TableHead
                                key={targetName}
                                className="text-right"
                              >
                                {targetName}
                                <span className="text-muted-foreground block text-xs">
                                  (Avg, Min, Max)
                                </span>
                              </TableHead>
                            ))}

                          {/* All parameter columns */}
                          {parameterNames.map((name: string) => (
                            <TableHead key={name} className="min-w-[120px]">
                              {name}
                            </TableHead>
                          ))}

                          {/* Fixed columns at the end */}
                          <TableHead className="bg-background sticky right-[200px] z-20 border-l text-right">
                            Source
                          </TableHead>
                          <TableHead className="bg-background sticky right-[80px] z-20 text-right">
                            Suggestion Type
                          </TableHead>
                          <TableHead className="bg-background sticky right-0 z-20 w-[80px] text-center">
                            Actions
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {/* Measurements with simple batch detection */}
                        {(() => {
                          // Group measurements into batches based on their batch ID
                          const batches: Array<
                            Array<(typeof measurements)[0]>
                          > = []

                          // Debug output with more details
                          console.log("All measurements (raw):", measurements)
                          console.log(
                            "All measurements (formatted):",
                            measurements.map(m => ({
                              id: m.id,
                              createdAt: formatDate(new Date(m.createdAt)),
                              batchId: m.batchId,
                              targetValue: m.targetValue,
                              isRecommended: m.isRecommended,
                              parameters: m.parameters
                            }))
                          )

                          // Sort measurements by creation time (newest first)
                          const sortedMeasurements = [...measurements].sort(
                            (a, b) =>
                              new Date(b.createdAt).getTime() -
                              new Date(a.createdAt).getTime()
                          )

                          // Group measurements into batches based on their batch ID
                          // In Bayesian optimization, a batch is a set of measurements that were suggested together
                          // and are part of the same iteration of the optimization loop

                          // First, separate measurements by source (API vs Manual)
                          const apiMeasurements: typeof measurements = []
                          const manualMeasurements: typeof measurements = []

                          sortedMeasurements.forEach(m => {
                            if (m.isRecommended) {
                              apiMeasurements.push(m)
                            } else {
                              manualMeasurements.push(m)
                            }
                          })

                          // Create a map to track which measurements have been assigned to batches
                          const assignedMeasurements = new Set<string>()

                          // Group API measurements by batch ID
                          if (apiMeasurements.length > 0) {
                            // First, group by batch ID
                            const batchGroups: Record<
                              string,
                              typeof measurements
                            > = {}

                            // Process all API measurements
                            for (const measurement of apiMeasurements) {
                              // IMPORTANT: Use the exact batch ID as the key if available
                              // This ensures measurements with the same batch ID are grouped together
                              const batchKey = measurement.batchId
                                ? measurement.batchId
                                : `single_${measurement.id}`

                              // Log for debugging
                              console.log(
                                `Processing measurement ${measurement.id} with batchKey ${batchKey}`
                              )

                              if (!batchGroups[batchKey]) {
                                batchGroups[batchKey] = []
                              }

                              batchGroups[batchKey].push(measurement)
                              assignedMeasurements.add(measurement.id)
                            }

                            // Log batch groups for debugging
                            console.log(
                              "Batch groups:",
                              Object.keys(batchGroups).map(key => ({
                                key,
                                count: batchGroups[key].length,
                                ids: batchGroups[key].map(m => m.id),
                                batchIds: batchGroups[key].map(m => m.batchId)
                              }))
                            )

                            // Convert the groups to batches
                            for (const batchKey in batchGroups) {
                              // Sort measurements within a batch by creation time
                              const sortedBatch = batchGroups[batchKey].sort(
                                (a, b) =>
                                  new Date(a.createdAt).getTime() -
                                  new Date(b.createdAt).getTime()
                              )

                              batches.push(sortedBatch)
                            }
                          }

                          // Group manual measurements by batch ID, similar to API measurements
                          if (manualMeasurements.length > 0) {
                            // First, group by batch ID
                            const manualBatchGroups: Record<
                              string,
                              typeof measurements
                            > = {}

                            // Process all manual measurements
                            for (const measurement of manualMeasurements) {
                              if (!assignedMeasurements.has(measurement.id)) {
                                // Use the batch ID as the key if available, otherwise use a unique key
                                const batchKey = measurement.batchId
                                  ? measurement.batchId
                                  : `single_${measurement.id}`

                                // Log for debugging
                                console.log(
                                  `Processing manual measurement ${measurement.id} with batchKey ${batchKey}`
                                )

                                if (!manualBatchGroups[batchKey]) {
                                  manualBatchGroups[batchKey] = []
                                }

                                manualBatchGroups[batchKey].push(measurement)
                                assignedMeasurements.add(measurement.id)
                              }
                            }

                            // Log manual batch groups for debugging
                            console.log(
                              "Manual batch groups:",
                              Object.keys(manualBatchGroups).map(key => ({
                                key,
                                count: manualBatchGroups[key].length,
                                ids: manualBatchGroups[key].map(m => m.id),
                                batchIds: manualBatchGroups[key].map(
                                  m => m.batchId
                                )
                              }))
                            )

                            // Convert the groups to batches
                            for (const batchKey in manualBatchGroups) {
                              // Sort measurements within a batch by creation time
                              const sortedBatch = manualBatchGroups[
                                batchKey
                              ].sort(
                                (a, b) =>
                                  new Date(a.createdAt).getTime() -
                                  new Date(b.createdAt).getTime()
                              )

                              batches.push(sortedBatch)
                            }
                          }

                          // Sort batches by the timestamp of their first measurement (newest first)
                          batches.sort((a, b) => {
                            const aTime = new Date(a[0].createdAt).getTime()
                            const bTime = new Date(b[0].createdAt).getTime()
                            return bTime - aTime
                          })

                          // We no longer create artificial batches
                          // This ensures that only real batches (with the same batch ID) are shown as batches

                          // Render all measurements with collapsible batches
                          // In Bayesian optimization, iteration refers to the optimization loop, not individual measurements
                          // We'll use the batch index as the iteration number (newest first)
                          // For batch items, we use a consistent format: ExperimentNumber.ItemNumber (e.g., 3.1, 3.2, 3.3)
                          let currentIteration = batches.length
                          const rows: React.ReactElement[] = []

                          // Create a unique ID for each batch based on its content
                          const getBatchId = (
                            batch: typeof measurements,
                            index: number
                          ) => {
                            // Create a unique ID based on the first measurement's ID and the batch index
                            return `batch-${batch[0]?.id || index}-${index}`
                          }

                          // Determine if a batch is from the API (suggested points) or manual entry
                          const isBatchFromAPI = (
                            batch: typeof measurements
                          ) => {
                            return batch.every(m => m.isRecommended)
                          }

                          // Helper function to check if a measurement would be dropped based on current parameter settings
                          const isMeasurementDropped = (measurement: any) => {
                            if (
                              !measurement.parameters ||
                              !optimizationState.config
                            ) {
                              return false
                            }

                            const config = optimizationState.config as any
                            const parameters = config.parameters || []
                            const measurementParams = measurement.parameters

                            // Check each parameter against current configuration
                            for (const param of parameters) {
                              const paramName = param.name
                              const measurementValue =
                                measurementParams[paramName]

                              if (
                                measurementValue === undefined ||
                                measurementValue === null
                              ) {
                                continue // Skip if measurement doesn't have this parameter
                              }

                              // Validate based on parameter type
                              if (
                                param.type === "NumericalContinuous" &&
                                param.bounds
                              ) {
                                const [min, max] = param.bounds
                                const numValue = parseFloat(measurementValue)
                                if (
                                  isNaN(numValue) ||
                                  numValue < min ||
                                  numValue > max
                                ) {
                                  return true // Measurement is outside bounds
                                }
                              } else if (
                                param.type === "NumericalDiscrete" &&
                                param.values
                              ) {
                                const allowedValues = param.values
                                const numValue = parseFloat(measurementValue)
                                if (
                                  isNaN(numValue) ||
                                  !allowedValues.includes(numValue)
                                ) {
                                  return true // Measurement value not in allowed discrete values
                                }
                              } else if (
                                (param.type === "CategoricalParameter" ||
                                  param.type === "Categorical") &&
                                param.values
                              ) {
                                const allowedValues = param.values
                                if (!allowedValues.includes(measurementValue)) {
                                  return true // Measurement value not in allowed categorical values
                                }
                              }
                            }

                            // Check target bounds if they exist
                            const targetConfig = config.target_config
                            if (
                              targetConfig &&
                              measurement.targetValue !== undefined
                            ) {
                              if (Array.isArray(targetConfig)) {
                                // Multi-target case
                                for (const target of targetConfig) {
                                  if (
                                    target.bounds &&
                                    measurement.targetValues
                                  ) {
                                    const targetValue =
                                      measurement.targetValues[target.name]
                                    if (targetValue !== undefined) {
                                      const [min, max] = target.bounds
                                      if (
                                        targetValue < min ||
                                        targetValue > max
                                      ) {
                                        return true // Target value outside bounds
                                      }
                                    }
                                  }
                                }
                              } else {
                                // Single target case
                                if (targetConfig.bounds) {
                                  const [min, max] = targetConfig.bounds
                                  const targetValue = parseFloat(
                                    measurement.targetValue
                                  )
                                  if (
                                    !isNaN(targetValue) &&
                                    (targetValue < min || targetValue > max)
                                  ) {
                                    return true // Target value outside bounds
                                  }
                                }
                              }
                            }

                            return false // Measurement is valid
                          }

                          // Helper function to get the reason why a measurement was dropped
                          const getDroppedReason = (measurement: any) => {
                            if (
                              !measurement.parameters ||
                              !optimizationState.config
                            ) {
                              return ""
                            }

                            const config = optimizationState.config as any
                            const parameters = config.parameters || []
                            const measurementParams = measurement.parameters
                            const violations = []

                            // Check each parameter against current configuration
                            for (const param of parameters) {
                              const paramName = param.name
                              const measurementValue =
                                measurementParams[paramName]

                              if (
                                measurementValue === undefined ||
                                measurementValue === null
                              ) {
                                continue
                              }

                              // Check violations based on parameter type
                              if (
                                param.type === "NumericalContinuous" &&
                                param.bounds
                              ) {
                                const [min, max] = param.bounds
                                const numValue = parseFloat(measurementValue)
                                if (
                                  isNaN(numValue) ||
                                  numValue < min ||
                                  numValue > max
                                ) {
                                  violations.push(
                                    `${paramName}: ${measurementValue} outside bounds [${min}, ${max}]`
                                  )
                                }
                              } else if (
                                param.type === "NumericalDiscrete" &&
                                param.values
                              ) {
                                const allowedValues = param.values
                                const numValue = parseFloat(measurementValue)
                                if (
                                  isNaN(numValue) ||
                                  !allowedValues.includes(numValue)
                                ) {
                                  violations.push(
                                    `${paramName}: ${measurementValue} not in allowed values`
                                  )
                                }
                              } else if (
                                (param.type === "CategoricalParameter" ||
                                  param.type === "Categorical") &&
                                param.values
                              ) {
                                const allowedValues = param.values
                                if (!allowedValues.includes(measurementValue)) {
                                  violations.push(
                                    `${paramName}: ${measurementValue} not in allowed categories`
                                  )
                                }
                              }
                            }

                            // Check target bounds violations
                            const targetConfig = config.target_config
                            if (
                              targetConfig &&
                              measurement.targetValue !== undefined
                            ) {
                              if (Array.isArray(targetConfig)) {
                                for (const target of targetConfig) {
                                  if (
                                    target.bounds &&
                                    measurement.targetValues
                                  ) {
                                    const targetValue =
                                      measurement.targetValues[target.name]
                                    if (targetValue !== undefined) {
                                      const [min, max] = target.bounds
                                      if (
                                        targetValue < min ||
                                        targetValue > max
                                      ) {
                                        violations.push(
                                          `${target.name}: ${targetValue} outside target bounds [${min}, ${max}]`
                                        )
                                      }
                                    }
                                  }
                                }
                              } else {
                                if (targetConfig.bounds) {
                                  const [min, max] = targetConfig.bounds
                                  const targetValue = parseFloat(
                                    measurement.targetValue
                                  )
                                  if (
                                    !isNaN(targetValue) &&
                                    (targetValue < min || targetValue > max)
                                  ) {
                                    violations.push(
                                      `Target: ${targetValue} outside bounds [${min}, ${max}]`
                                    )
                                  }
                                }
                              }
                            }

                            return violations.length > 0
                              ? violations.join("; ")
                              : ""
                          }

                          // Determine if a batch is a true batch (has multiple measurements) or sequential
                          const isTrueBatch = (batch: typeof measurements) => {
                            // For a batch to be considered a "true batch", it must have multiple measurements
                            // This aligns with the requirement that batch suggestions are only when multiple suggestions are generated

                            // Check if the batch has more than one measurement
                            const hasMultipleMeasurements = batch.length > 1

                            // Log for debugging
                            console.log(
                              `Batch check: batchSize=${batch.length}, hasMultipleMeasurements=${hasMultipleMeasurements}, batchIds=${JSON.stringify(batch.map(m => m.batchId))}`
                            )

                            // Only return true if there are multiple measurements
                            return hasMultipleMeasurements
                          }

                          // Debug output
                          console.log(
                            "Batches detected:",
                            batches.map(batch => {
                              // Get all batch IDs (excluding null/undefined)
                              const batchIds = batch
                                .map(m => m.batchId)
                                .filter(id => id !== null && id !== undefined)

                              // Count occurrences of each batch ID
                              const batchIdCounts: Record<string, number> = {}
                              batchIds.forEach(id => {
                                if (id) {
                                  batchIdCounts[id] =
                                    (batchIdCounts[id] || 0) + 1
                                }
                              })

                              return {
                                size: batch.length,
                                batchIds: batch.map(m => m.batchId),
                                batchIdCounts,
                                isTrueBatch: isTrueBatch(batch),
                                timestamps: batch.map(m =>
                                  formatDate(new Date(m.createdAt))
                                ),
                                ids: batch.map(m => m.id)
                              }
                            })
                          )

                          // Collect all batch IDs for expand/collapse all functionality
                          const allBatchIds: string[] = []
                          batches.forEach((batch, batchIndex) => {
                            if (batch.length > 1) {
                              const batchId = getBatchId(batch, batchIndex)
                              allBatchIds.push(batchId)
                            }
                          })

                          // Update the batchIds state if it has changed
                          if (
                            JSON.stringify(allBatchIds) !==
                            JSON.stringify(batchIds)
                          ) {
                            setBatchIds(allBatchIds)
                          }

                          batches.forEach((batch, batchIndex) => {
                            const isBatch = batch.length > 1
                            const batchId = getBatchId(batch, batchIndex)
                            const isExpanded = expandedBatches[batchId] || false

                            if (isBatch) {
                              // For batches, render a collapsible header
                              const firstMeasurement = batch[0]
                              const lastIteration = currentIteration
                              const firstIteration =
                                currentIteration - batch.length + 1

                              // Calculate average target value for the primary target
                              let avgTargetValue = 0
                              if (isMultiTarget) {
                                // For multi-target, use the targetValues field if available
                                const validValues = batch
                                  .map(m => {
                                    if (
                                      m.targetValues &&
                                      typeof m.targetValues === "object" &&
                                      (
                                        m.targetValues as Record<string, number>
                                      )[optimization.targetName] !== undefined
                                    ) {
                                      return (
                                        m.targetValues as Record<string, number>
                                      )[optimization.targetName]
                                    }
                                    return parseFloat(m.targetValue)
                                  })
                                  .filter(v => !isNaN(v))

                                avgTargetValue =
                                  validValues.length > 0
                                    ? validValues.reduce(
                                        (sum, v) => sum + v,
                                        0
                                      ) / validValues.length
                                    : 0
                              } else {
                                // For single target, use the targetValue field
                                avgTargetValue =
                                  batch.reduce(
                                    (sum, m) => sum + parseFloat(m.targetValue),
                                    0
                                  ) / batch.length
                              }

                              // Calculate statistics for other targets
                              const targetStats: Record<
                                string,
                                { avg: number; min: number; max: number }
                              > = {}

                              if (isMultiTarget && otherTargets.length > 0) {
                                otherTargets.forEach(targetName => {
                                  // Get all values for this target from the batch
                                  const values = batch
                                    .map(m => {
                                      if (
                                        m.targetValues &&
                                        typeof m.targetValues === "object"
                                      ) {
                                        const targetValues =
                                          m.targetValues as Record<
                                            string,
                                            number
                                          >
                                        return targetValues[targetName]
                                      }
                                      return NaN
                                    })
                                    .filter(v => !isNaN(v))

                                  if (values.length > 0) {
                                    targetStats[targetName] = {
                                      avg:
                                        values.reduce((sum, v) => sum + v, 0) /
                                        values.length,
                                      min: Math.min(...values),
                                      max: Math.max(...values)
                                    }
                                  }
                                })
                              }

                              // Determine if this is an API batch or manual batch
                              const isAPIBatch = isBatchFromAPI(batch)

                              // Check if any measurements in this batch are dropped
                              const hasDroppedMeasurements =
                                batch.some(isMeasurementDropped)

                              // Batch header row
                              rows.push(
                                <TableRow
                                  key={`header-${batchId}`}
                                  className={`hover:bg-muted/50 cursor-pointer border-t-2 ${
                                    hasDroppedMeasurements
                                      ? "border-gray-300/50 bg-gray-100 text-gray-500"
                                      : isAPIBatch
                                        ? "border-primary/20"
                                        : "border-orange-300/30"
                                  }`}
                                  onClick={() => {
                                    // Update the expanded state for this batch
                                    const newExpandedState =
                                      !expandedBatches[batchId]
                                    setExpandedBatches(prev => ({
                                      ...prev,
                                      [batchId]: newExpandedState
                                    }))

                                    // Check if all batches are now expanded or collapsed
                                    // and update the allExpanded state accordingly
                                    if (batchIds.length > 0) {
                                      const expandedCount = Object.values({
                                        ...expandedBatches,
                                        [batchId]: newExpandedState
                                      }).filter(Boolean).length

                                      // If all batches are expanded, set allExpanded to true
                                      // If no batches are expanded, set allExpanded to false
                                      if (expandedCount === batchIds.length) {
                                        setAllExpanded(true)
                                      } else if (expandedCount === 0) {
                                        setAllExpanded(false)
                                      }
                                    }
                                  }}
                                >
                                  <TableCell className="bg-background sticky left-0 z-10 font-medium">
                                    <div className="flex items-center">
                                      <ChevronRight
                                        className={`mr-1 size-4 transition-transform ${isExpanded ? "rotate-90" : ""}`}
                                      />
                                      <span>Experiment {currentIteration}</span>
                                    </div>
                                  </TableCell>
                                  <TableCell className="bg-background sticky left-[100px] z-10">
                                    <div className="flex flex-col">
                                      <span>
                                        {formatDate(
                                          new Date(firstMeasurement.createdAt)
                                        )}
                                      </span>
                                      <span className="text-muted-foreground text-xs">
                                        {isExpanded
                                          ? "Click to collapse"
                                          : "Click to expand"}
                                      </span>
                                    </div>
                                  </TableCell>
                                  {/* First target summary */}
                                  <TableCell className="text-right font-medium">
                                    <div className="flex flex-col">
                                      <span>
                                        Avg: {formatNumber(avgTargetValue)}
                                      </span>
                                      <span className="text-muted-foreground text-xs">
                                        Min:{" "}
                                        {formatNumber(
                                          Math.min(
                                            ...batch.map(m => {
                                              if (
                                                isMultiTarget &&
                                                m.targetValues &&
                                                typeof m.targetValues ===
                                                  "object" &&
                                                (
                                                  m.targetValues as Record<
                                                    string,
                                                    number
                                                  >
                                                )[optimization.targetName] !==
                                                  undefined
                                              ) {
                                                return (
                                                  m.targetValues as Record<
                                                    string,
                                                    number
                                                  >
                                                )[optimization.targetName]
                                              }
                                              return parseFloat(m.targetValue)
                                            })
                                          )
                                        )}
                                        {" | "}
                                        Max:{" "}
                                        {formatNumber(
                                          Math.max(
                                            ...batch.map(m => {
                                              if (
                                                isMultiTarget &&
                                                m.targetValues &&
                                                typeof m.targetValues ===
                                                  "object" &&
                                                (
                                                  m.targetValues as Record<
                                                    string,
                                                    number
                                                  >
                                                )[optimization.targetName] !==
                                                  undefined
                                              ) {
                                                return (
                                                  m.targetValues as Record<
                                                    string,
                                                    number
                                                  >
                                                )[optimization.targetName]
                                              }
                                              return parseFloat(m.targetValue)
                                            })
                                          )
                                        )}
                                      </span>
                                    </div>
                                  </TableCell>

                                  {/* Other target statistics */}
                                  {isMultiTarget &&
                                    otherTargets.map(targetName => (
                                      <TableCell
                                        key={targetName}
                                        className="text-right font-medium"
                                      >
                                        {targetStats[targetName] ? (
                                          <div className="flex flex-col">
                                            <span>
                                              Avg:{" "}
                                              {formatNumber(
                                                targetStats[targetName].avg
                                              )}
                                            </span>
                                            <span className="text-muted-foreground text-xs">
                                              Min:{" "}
                                              {formatNumber(
                                                targetStats[targetName].min
                                              )}
                                              {" | "}
                                              Max:{" "}
                                              {formatNumber(
                                                targetStats[targetName].max
                                              )}
                                            </span>
                                          </div>
                                        ) : (
                                          <span className="text-muted-foreground">
                                            No data
                                          </span>
                                        )}
                                      </TableCell>
                                    ))}

                                  {/* Placeholder cells for all parameters */}
                                  {parameterNames.map((name: string) => (
                                    <TableCell key={name}></TableCell>
                                  ))}

                                  <TableCell className="bg-background sticky right-[200px] z-10 text-right">
                                    <Badge
                                      variant="outline"
                                      className={
                                        isAPIBatch
                                          ? "bg-primary/10 text-primary border-primary/20"
                                          : "border-orange-200 bg-orange-100 text-orange-700"
                                      }
                                    >
                                      {isAPIBatch ? "API" : "Manual Entry"}
                                    </Badge>
                                  </TableCell>
                                  <TableCell className="bg-background sticky right-[80px] z-10 text-right">
                                    <Badge
                                      variant="outline"
                                      className={
                                        firstMeasurement.parameters &&
                                        typeof firstMeasurement.parameters ===
                                          "object" &&
                                        (firstMeasurement.parameters as any)
                                          ?._sampleClass === "exploratory"
                                          ? "border-purple-200 bg-purple-100 text-purple-800"
                                          : isTrueBatch(batch)
                                            ? "border-green-200 bg-green-100 text-green-700"
                                            : "border-blue-200 bg-blue-100 text-blue-700"
                                      }
                                    >
                                      {firstMeasurement.parameters &&
                                      typeof firstMeasurement.parameters ===
                                        "object" &&
                                      (firstMeasurement.parameters as any)
                                        ?._sampleClass === "exploratory"
                                        ? "Exploratory"
                                        : isTrueBatch(batch)
                                          ? `Batch${batch.length > 1 ? ` (${batch.length})` : ""}`
                                          : "Sequential"}
                                    </Badge>
                                  </TableCell>
                                  <TableCell className="bg-background sticky right-0 z-10 text-center">
                                    {/* Empty cell for batch header - actions are on individual items */}
                                  </TableCell>
                                </TableRow>
                              )

                              // If expanded, show all measurements in the batch
                              if (isExpanded) {
                                batch.forEach(
                                  (measurement, measurementIndex) => {
                                    // Use consistent numbering: currentIteration.1, currentIteration.2, etc.
                                    // This ensures all items in a batch have the same experiment number prefix

                                    const isDropped =
                                      isMeasurementDropped(measurement)

                                    rows.push(
                                      <TableRow
                                        key={`${batchId}-item-${measurement.id}`}
                                        className={`${
                                          isDropped
                                            ? "bg-gray-100 text-gray-500 opacity-60"
                                            : "bg-muted/30"
                                        }`}
                                      >
                                        <TableCell
                                          className={`sticky left-0 z-10 pl-6 font-medium ${
                                            isDropped
                                              ? "bg-gray-100"
                                              : "bg-muted/30"
                                          }`}
                                        >
                                          <span className="flex items-center">
                                            <span className="text-muted-foreground mr-1">
                                              └
                                            </span>
                                            <span
                                              className={
                                                isDropped ? "line-through" : ""
                                              }
                                            >
                                              {currentIteration}.
                                              {measurementIndex + 1}
                                            </span>
                                            {isDropped && (
                                              <span
                                                className="ml-2 cursor-help text-xs text-gray-500"
                                                title={`Experiment excluded: ${getDroppedReason(measurement)}`}
                                              >
                                                (excluded)
                                              </span>
                                            )}
                                          </span>
                                        </TableCell>
                                        <TableCell className="bg-muted/30 sticky left-[100px] z-10">
                                          {formatDate(
                                            new Date(measurement.createdAt)
                                          )}
                                        </TableCell>
                                        {/* First target value */}
                                        <TableCell className="text-right font-medium">
                                          {isMultiTarget &&
                                          measurement.targetValues &&
                                          typeof measurement.targetValues ===
                                            "object" &&
                                          (
                                            measurement.targetValues as Record<
                                              string,
                                              number
                                            >
                                          )[optimization.targetName] !==
                                            undefined
                                            ? formatNumber(
                                                (
                                                  measurement.targetValues as Record<
                                                    string,
                                                    number
                                                  >
                                                )[optimization.targetName]
                                              )
                                            : formatNumber(
                                                parseFloat(
                                                  measurement.targetValue
                                                )
                                              )}
                                        </TableCell>

                                        {/* Other target values for multi-target optimization */}
                                        {isMultiTarget &&
                                          otherTargets.map(targetName => {
                                            // Get the target value from targetValues if available
                                            let value = "N/A"
                                            if (measurement.targetValues) {
                                              const targetValues =
                                                measurement.targetValues as Record<
                                                  string,
                                                  number
                                                >
                                              if (
                                                targetValues[targetName] !==
                                                undefined
                                              ) {
                                                const numValue =
                                                  targetValues[targetName]
                                                value =
                                                  typeof numValue === "number"
                                                    ? formatNumber(numValue)
                                                    : String(numValue)
                                              }
                                            }
                                            return (
                                              <TableCell
                                                key={targetName}
                                                className="text-right font-medium"
                                              >
                                                {value}
                                              </TableCell>
                                            )
                                          })}

                                        {/* All parameter values */}
                                        {parameterNames.map((name: string) => {
                                          const params =
                                            measurement.parameters as Record<
                                              string,
                                              any
                                            >
                                          return (
                                            <TableCell key={name}>
                                              {typeof params[name] === "number"
                                                ? formatNumber(params[name])
                                                : String(params[name])}
                                            </TableCell>
                                          )
                                        })}

                                        <TableCell className="bg-muted/30 sticky right-[200px] z-10 text-right">
                                          <Badge
                                            variant={
                                              measurement.isRecommended
                                                ? "default"
                                                : "outline"
                                            }
                                            className="opacity-75"
                                          >
                                            {measurement.isRecommended
                                              ? "API"
                                              : "Manual"}
                                          </Badge>
                                        </TableCell>
                                        <TableCell className="bg-muted/30 sticky right-[80px] z-10 text-right">
                                          <Badge
                                            variant="outline"
                                            className={
                                              measurement.parameters &&
                                              typeof measurement.parameters ===
                                                "object" &&
                                              (measurement.parameters as any)
                                                ?._sampleClass === "exploratory"
                                                ? "border-purple-200 bg-purple-100 text-purple-800 opacity-75"
                                                : "border-green-200 bg-green-100 text-green-700 opacity-75"
                                            }
                                          >
                                            {measurement.parameters &&
                                            typeof measurement.parameters ===
                                              "object" &&
                                            (measurement.parameters as any)
                                              ?._sampleClass === "exploratory"
                                              ? "Exploratory"
                                              : "Batch Item"}
                                          </Badge>
                                        </TableCell>
                                        <TableCell className="bg-muted/30 sticky right-0 z-10 text-center">
                                          <div className="flex items-center justify-center gap-1">
                                            <EditMeasurementDialog
                                              measurement={measurement}
                                              optimization={optimization}
                                              onMeasurementUpdated={
                                                handleMeasurementUpdated
                                              }
                                              trigger={
                                                <Button
                                                  variant="ghost"
                                                  size="sm"
                                                  className="size-8 p-0"
                                                >
                                                  <Edit className="size-3" />
                                                </Button>
                                              }
                                            />
                                            <DeleteMeasurementDialog
                                              measurement={measurement}
                                              optimization={optimization}
                                              experimentNumber={`${currentIteration}.${measurementIndex + 1}`}
                                              onMeasurementDeleted={
                                                handleMeasurementDeleted
                                              }
                                              totalMeasurements={
                                                measurements.length
                                              }
                                              trigger={
                                                <Button
                                                  variant="ghost"
                                                  size="sm"
                                                  className="size-8 p-0 text-red-600 hover:text-red-700"
                                                >
                                                  <Trash2 className="size-3" />
                                                </Button>
                                              }
                                            />
                                          </div>
                                        </TableCell>
                                      </TableRow>
                                    )
                                  }
                                )
                              }

                              // Update the iteration counter
                              currentIteration--
                            } else {
                              // For single measurements, treat them as their own iteration
                              const measurement = batch[0]
                              const isAPIEntry = measurement.isRecommended
                              const isDropped =
                                isMeasurementDropped(measurement)

                              rows.push(
                                <TableRow
                                  key={measurement.id}
                                  className={
                                    isDropped
                                      ? "border-t-2 border-gray-300/50 bg-gray-100 text-gray-500 opacity-60"
                                      : isAPIEntry
                                        ? ""
                                        : "border-t-2 border-orange-300/30"
                                  }
                                >
                                  <TableCell
                                    className={`sticky left-0 z-10 font-medium ${
                                      isDropped
                                        ? "bg-gray-100"
                                        : "bg-background"
                                    }`}
                                  >
                                    <span
                                      className={
                                        isDropped ? "line-through" : ""
                                      }
                                    >
                                      Experiment {currentIteration}
                                    </span>
                                    {isDropped && (
                                      <span
                                        className="ml-2 cursor-help text-xs text-gray-500"
                                        title={`Experiment excluded: ${getDroppedReason(measurement)}`}
                                      >
                                        (excluded)
                                      </span>
                                    )}
                                  </TableCell>
                                  <TableCell className="bg-background sticky left-[100px] z-10">
                                    {formatDate(
                                      new Date(measurement.createdAt)
                                    )}
                                  </TableCell>
                                  {/* First target value */}
                                  <TableCell className="text-right font-medium">
                                    {isMultiTarget &&
                                    measurement.targetValues &&
                                    typeof measurement.targetValues ===
                                      "object" &&
                                    (
                                      measurement.targetValues as Record<
                                        string,
                                        number
                                      >
                                    )[optimization.targetName] !== undefined
                                      ? formatNumber(
                                          (
                                            measurement.targetValues as Record<
                                              string,
                                              number
                                            >
                                          )[optimization.targetName]
                                        )
                                      : formatNumber(
                                          parseFloat(measurement.targetValue)
                                        )}
                                  </TableCell>

                                  {/* Other target values for multi-target optimization */}
                                  {isMultiTarget &&
                                    otherTargets.map(targetName => {
                                      // Get the target value from targetValues if available
                                      let value = "N/A"
                                      if (measurement.targetValues) {
                                        const targetValues =
                                          measurement.targetValues as Record<
                                            string,
                                            number
                                          >
                                        if (
                                          targetValues[targetName] !== undefined
                                        ) {
                                          const numValue =
                                            targetValues[targetName]
                                          value =
                                            typeof numValue === "number"
                                              ? formatNumber(numValue)
                                              : String(numValue)
                                        }
                                      }
                                      return (
                                        <TableCell
                                          key={targetName}
                                          className="text-right font-medium"
                                        >
                                          {value}
                                        </TableCell>
                                      )
                                    })}

                                  {/* All parameter values */}
                                  {parameterNames.map((name: string) => {
                                    const params =
                                      measurement.parameters as Record<
                                        string,
                                        any
                                      >
                                    return (
                                      <TableCell key={name}>
                                        {typeof params[name] === "number"
                                          ? formatNumber(params[name])
                                          : String(params[name])}
                                      </TableCell>
                                    )
                                  })}

                                  <TableCell className="bg-background sticky right-[200px] z-10 text-right">
                                    <Badge
                                      variant="outline"
                                      className={
                                        isAPIEntry
                                          ? "bg-primary/10 text-primary border-primary/20"
                                          : "border-orange-200 bg-orange-100 text-orange-700"
                                      }
                                    >
                                      {isAPIEntry ? "API" : "Manual Entry"}
                                    </Badge>
                                  </TableCell>
                                  <TableCell className="bg-background sticky right-[80px] z-10 text-right">
                                    <Badge
                                      variant="outline"
                                      className={
                                        measurement.parameters &&
                                        typeof measurement.parameters ===
                                          "object" &&
                                        (measurement.parameters as any)
                                          ?._sampleClass === "exploratory"
                                          ? "border-purple-200 bg-purple-100 text-purple-800"
                                          : "border-blue-200 bg-blue-100 text-blue-700"
                                      }
                                    >
                                      {measurement.parameters &&
                                      typeof measurement.parameters ===
                                        "object" &&
                                      (measurement.parameters as any)
                                        ?._sampleClass === "exploratory"
                                        ? "Exploratory"
                                        : "Sequential"}
                                    </Badge>
                                  </TableCell>
                                  <TableCell className="bg-background sticky right-0 z-10 text-center">
                                    <div className="flex items-center justify-center gap-1">
                                      <EditMeasurementDialog
                                        measurement={measurement}
                                        optimization={optimization}
                                        onMeasurementUpdated={
                                          handleMeasurementUpdated
                                        }
                                        trigger={
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="size-8 p-0"
                                          >
                                            <Edit className="size-3" />
                                          </Button>
                                        }
                                      />
                                      <DeleteMeasurementDialog
                                        measurement={measurement}
                                        optimization={optimization}
                                        experimentNumber={String(
                                          currentIteration
                                        )}
                                        onMeasurementDeleted={
                                          handleMeasurementDeleted
                                        }
                                        totalMeasurements={measurements.length}
                                        trigger={
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="size-8 p-0 text-red-600 hover:text-red-700"
                                          >
                                            <Trash2 className="size-3" />
                                          </Button>
                                        }
                                      />
                                    </div>
                                  </TableCell>
                                </TableRow>
                              )

                              // Update the iteration counter for single measurements
                              currentIteration--
                            }
                          })

                          console.log("Rows to render:", rows.length)
                          return rows
                        })()}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              ) : (
                <div className="text-muted-foreground py-8 text-center">
                  {isLoadingHistory ? (
                    <div className="flex flex-col items-center">
                      <RefreshCw className="mb-2 size-8 animate-spin" />
                      <p>Loading measurement history...</p>
                    </div>
                  ) : (
                    <p>No measurement history available yet.</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent
          value="configuration"
          className="space-y-6"
          key={`configuration-${configurationKey}-${(optimizationState as any).lastUpdated || 0}`}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center text-lg">
                  <Beaker className="mr-2 size-5" />
                  Optimization Configuration
                </CardTitle>
                <CardDescription>
                  Parameters, targets, acquisition functions, and constraints
                  for this optimization
                </CardDescription>
              </div>
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4">
                {/* Edit Mode Toggle */}
                <div className="flex items-center gap-2">
                  <Switch
                    id="edit-mode"
                    checked={isEditMode}
                    onCheckedChange={handleEditModeChange}
                    disabled={measurements.length === 0}
                  />
                  <Label htmlFor="edit-mode" className="text-sm">
                    Edit Mode
                  </Label>
                </div>

                {/* Edit Mode Actions */}
                {isEditMode && (
                  <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2">
                    {/* Expand/Collapse All */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={toggleAllParameters}
                      className="px-2"
                    >
                      <ChevronRight
                        className={`mr-1 size-4 transition-transform ${allParametersExpanded ? "rotate-90" : ""}`}
                      />
                      {allParametersExpanded ? "Collapse All" : "Expand All"}
                    </Button>

                    {/* Batch Actions - only show when have pending changes */}
                    {hasPendingChanges() && (
                      <>
                        <div className="bg-border hidden h-4 w-px sm:block" />
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={discardAllParameterChanges}
                            className="h-8 text-xs"
                          >
                            Discard Changes
                          </Button>
                          <Button
                            size="sm"
                            onClick={applyAllParameterChanges}
                            className="h-8 text-xs"
                          >
                            Preview & Apply ({getTotalPendingChanges()})
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {/* 1. Parameters Section */}
                <div className="space-y-4">
                  <h3 className="flex items-center text-lg font-medium">
                    <Beaker className="mr-2 size-5" />
                    Parameters
                  </h3>
                  <div className="overflow-x-auto rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {isEditMode && (
                            <TableHead className="w-[50px]"></TableHead>
                          )}
                          <TableHead className="w-[180px]">Name</TableHead>
                          <TableHead className="w-[120px]">Type</TableHead>
                          <TableHead>Values/Range</TableHead>
                          <TableHead className="w-[120px] text-right">
                            {isEditMode ? "Actions" : "Options"}
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {(optimizationState.config as any).parameters.map(
                          (param: any, index: number) => {
                            const isExpanded = expandedParameters.includes(
                              param.name
                            )
                            const hasPendingChanges =
                              pendingParameterChanges[param.name]
                            // Use pending changes if available, otherwise use original param
                            const displayParam = hasPendingChanges
                              ? pendingParameterChanges[param.name]
                              : param

                            return (
                              <React.Fragment key={index}>
                                <TableRow className="group">
                                  {isEditMode && (
                                    <TableCell className="w-[50px]">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                          toggleParameterExpansion(param.name)
                                        }
                                        className="size-6 p-0"
                                      >
                                        {isExpanded ? (
                                          <ChevronDown className="size-4" />
                                        ) : (
                                          <ChevronRight className="size-4" />
                                        )}
                                      </Button>
                                    </TableCell>
                                  )}
                                  <TableCell className="font-medium">
                                    <div className="flex items-center gap-2">
                                      {param.name}
                                      {hasPendingChanges && (
                                        <Badge
                                          variant="secondary"
                                          className="text-xs"
                                        >
                                          Modified
                                        </Badge>
                                      )}
                                    </div>
                                  </TableCell>
                                  <TableCell>{displayParam.type}</TableCell>
                                  <TableCell>
                                    {displayParam.type ===
                                      "NumericalDiscrete" &&
                                      (Array.isArray(displayParam.values) ? (
                                        displayParam.values.join(", ")
                                      ) : (
                                        <span className="text-muted-foreground">
                                          No values
                                        </span>
                                      ))}
                                    {displayParam.type ===
                                      "NumericalContinuous" &&
                                      (() => {
                                        // Handle both array format [min, max] and string format "min, max"
                                        if (displayParam.bounds) {
                                          let boundsText = ""
                                          if (
                                            Array.isArray(
                                              displayParam.bounds
                                            ) &&
                                            displayParam.bounds.length === 2
                                          ) {
                                            boundsText = `${displayParam.bounds[0]} to ${displayParam.bounds[1]}`
                                          } else if (
                                            typeof displayParam.bounds ===
                                              "string" &&
                                            displayParam.bounds.trim()
                                          ) {
                                            const parts = displayParam.bounds
                                              .split(",")
                                              .map((v: string) =>
                                                Number(v.trim())
                                              )
                                            if (
                                              parts.length === 2 &&
                                              !isNaN(parts[0]) &&
                                              !isNaN(parts[1])
                                            ) {
                                              boundsText = `${parts[0]} to ${parts[1]}`
                                            }
                                          }

                                          if (boundsText) {
                                            return <span>{boundsText}</span>
                                          }
                                        }
                                        return (
                                          <span className="text-muted-foreground">
                                            No bounds
                                          </span>
                                        )
                                      })()}
                                    {displayParam.type ===
                                      "CategoricalParameter" &&
                                      (Array.isArray(displayParam.values) ? (
                                        displayParam.values.join(", ")
                                      ) : (
                                        <span className="text-muted-foreground">
                                          No values
                                        </span>
                                      ))}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    {isEditMode ? (
                                      <div className="flex items-center justify-end gap-1 sm:gap-2">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() =>
                                            toggleParameterExpansion(param.name)
                                          }
                                          className="size-6 p-0"
                                          title="Expand for detailed editing"
                                        >
                                          <Edit className="size-3" />
                                        </Button>
                                      </div>
                                    ) : (
                                      <>
                                        {param.type === "NumericalDiscrete" &&
                                          param.tolerance &&
                                          `Tolerance: ${param.tolerance}`}
                                        {param.type ===
                                          "CategoricalParameter" &&
                                          param.encoding &&
                                          `Encoding: ${param.encoding}`}
                                      </>
                                    )}
                                  </TableCell>
                                </TableRow>

                                {/* Expandable edit form row */}
                                {isEditMode && isExpanded && (
                                  <TableRow>
                                    <TableCell colSpan={5} className="p-0">
                                      <div className="bg-muted/30 border-t p-2 sm:p-4">
                                        <ParameterEditForm
                                          parameter={param}
                                          onSave={handleParameterSave}
                                          onCancel={() =>
                                            handleParameterCancel(param.name)
                                          }
                                          className="max-w-none"
                                        />
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                )}
                              </React.Fragment>
                            )
                          }
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>

                {/* 2. Target Configuration */}
                <div className="space-y-4">
                  <h3 className="flex items-center text-lg font-medium">
                    <Target className="mr-2 size-5" />
                    Targets
                  </h3>
                  <div className="overflow-x-auto rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {isEditMode && (
                            <TableHead className="w-[50px]"></TableHead>
                          )}
                          <TableHead className="w-[180px]">Name</TableHead>
                          <TableHead className="w-[120px]">Mode</TableHead>
                          <TableHead>Bounds</TableHead>
                          <TableHead className="w-[120px] text-right">
                            {isEditMode ? "Actions" : isPareto ? "Mode" : "Weight"}
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {/* For multi-target optimization */}
                        {isMultiTarget
                          ? // Map through all targets in the target_config array
                            Array.isArray(
                              (optimizationState.config as any).target_config
                            ) &&
                            (optimizationState.config as any).target_config.map(
                              (target: any, index: number) => {
                                const isExpanded = expandedTargets.includes(
                                  target.name
                                )
                                const hasPendingChanges =
                                  pendingTargetChanges[target.name]

                                return (
                                  <React.Fragment key={index}>
                                    <TableRow className="group">
                                      {isEditMode && (
                                        <TableCell className="w-[50px]">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                              toggleTargetExpansion(target.name)
                                            }
                                            className="size-6 p-0"
                                          >
                                            {isExpanded ? (
                                              <ChevronDown className="size-4" />
                                            ) : (
                                              <ChevronRight className="size-4" />
                                            )}
                                          </Button>
                                        </TableCell>
                                      )}
                                      <TableCell className="font-medium">
                                        <div className="flex items-center gap-2">
                                          {target.name}
                                          {hasPendingChanges && (
                                            <Badge
                                              variant="secondary"
                                              className="text-xs"
                                            >
                                              Modified
                                            </Badge>
                                          )}
                                        </div>
                                      </TableCell>
                                      <TableCell>
                                        <div className="flex items-center">
                                          {(() => {
                                            // Check for pending changes first
                                            const pendingTarget =
                                              pendingTargetChanges[target.name]
                                            const displayMode =
                                              pendingTarget?.mode || target.mode
                                            return (
                                              <>
                                                {displayMode}
                                                {displayMode === "MAX" ||
                                                (typeof displayMode ===
                                                  "string" &&
                                                  displayMode
                                                    .toUpperCase()
                                                    .includes("MAX")) ? (
                                                  <ArrowUp className="ml-1 size-4 text-green-500" />
                                                ) : (
                                                  <ArrowDown className="ml-1 size-4 text-green-500" />
                                                )}
                                              </>
                                            )
                                          })()}
                                        </div>
                                      </TableCell>
                                      <TableCell>
                                        {(() => {
                                          // Check for pending changes first
                                          const pendingTarget =
                                            pendingTargetChanges[target.name]
                                          const displayBounds =
                                            pendingTarget?.bounds ||
                                            target.bounds

                                          if (displayBounds) {
                                            return (
                                              <span>
                                                {Array.isArray(displayBounds)
                                                  ? `${displayBounds[0] !== undefined ? displayBounds[0] : "None"} to ${displayBounds[1] !== undefined ? displayBounds[1] : "None"}`
                                                  : `${displayBounds.lower !== undefined ? displayBounds.lower : "None"} to ${displayBounds.upper !== undefined ? displayBounds.upper : "None"}`}
                                              </span>
                                            )
                                          } else {
                                            return (
                                              <span className="text-muted-foreground">
                                                None
                                              </span>
                                            )
                                          }
                                        })()}
                                      </TableCell>
                                      <TableCell className="text-right">
                                        {isEditMode ? (
                                          <div className="flex items-center justify-end gap-1 sm:gap-2">
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() =>
                                                toggleTargetExpansion(
                                                  target.name
                                                )
                                              }
                                              className="size-6 p-0"
                                              title="Expand for detailed editing"
                                            >
                                              <Edit className="size-3" />
                                            </Button>
                                          </div>
                                        ) : (() => {
                                          // For Pareto objectives, don't show weights
                                          if (isPareto) {
                                            return (
                                              <span className="text-muted-foreground text-sm">
                                                N/A
                                              </span>
                                            )
                                          }

                                          // Check for pending changes first
                                          const pendingTarget = pendingTargetChanges[target.name]
                                          const displayWeight = pendingTarget?.weight !== undefined ? pendingTarget.weight : target.weight

                                          if (displayWeight !== undefined) {
                                            return `${Math.round(displayWeight * 100)}%`
                                          } else {
                                            return "100%"
                                          }
                                        })()}
                                      </TableCell>
                                    </TableRow>

                                    {/* Expandable edit form row */}
                                    {isEditMode && isExpanded && (
                                      <TableRow>
                                        <TableCell colSpan={5} className="p-0">
                                          <div className="bg-muted/30 border-t p-2 sm:p-4">
                                            <TargetEditForm
                                              target={{
                                                ...target,
                                                ...(pendingTargetChanges[
                                                  target.name
                                                ] || {})
                                              }}
                                              originalTarget={target}
                                              onSave={handleTargetSave}
                                              onCancel={() =>
                                                handleTargetCancel(target.name)
                                              }
                                              isMultiTarget={isMultiTarget}
                                              isPareto={isPareto}
                                              className="max-w-none"
                                            />
                                          </div>
                                        </TableCell>
                                      </TableRow>
                                    )}
                                  </React.Fragment>
                                )
                              }
                            )
                          : // Single target optimization
                            (() => {
                              const targetName = optimization.targetName
                              const isExpanded =
                                expandedTargets.includes(targetName)
                              const hasPendingChanges =
                                pendingTargetChanges[targetName]

                              // Create target object for single target
                              const config = optimizationState.config as any
                              const targetConfig = config.target_config || {}
                              const singleTarget = {
                                name: targetName,
                                mode: getTargetMode(optimization, targetName),
                                bounds: targetConfig.bounds,
                                weight: targetConfig.weight || 1.0,
                                transformation:
                                  targetConfig.transformation || "LINEAR",
                                type: targetConfig.type || "Numerical"
                              }

                              return (
                                <React.Fragment>
                                  <TableRow className="group">
                                    {isEditMode && (
                                      <TableCell className="w-[50px]">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() =>
                                            toggleTargetExpansion(targetName)
                                          }
                                          className="size-6 p-0"
                                        >
                                          {isExpanded ? (
                                            <ChevronDown className="size-4" />
                                          ) : (
                                            <ChevronRight className="size-4" />
                                          )}
                                        </Button>
                                      </TableCell>
                                    )}
                                    <TableCell className="font-medium">
                                      <div className="flex items-center gap-2">
                                        {targetName}
                                        {hasPendingChanges && (
                                          <Badge
                                            variant="secondary"
                                            className="text-xs"
                                          >
                                            Modified
                                          </Badge>
                                        )}
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <div className="flex items-center">
                                        {(() => {
                                          // Check for pending changes first
                                          const pendingTarget =
                                            pendingTargetChanges[targetName]
                                          const displayMode =
                                            pendingTarget?.mode ||
                                            singleTarget.mode
                                          return (
                                            <>
                                              {displayMode}
                                              {displayMode === "MAX" ? (
                                                <ArrowUp className="ml-1 size-4 text-green-500" />
                                              ) : (
                                                <ArrowDown className="ml-1 size-4 text-green-500" />
                                              )}
                                            </>
                                          )
                                        })()}
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      {(() => {
                                        // Check for pending changes first
                                        const pendingTarget =
                                          pendingTargetChanges[targetName]
                                        const displayBounds =
                                          pendingTarget?.bounds ||
                                          targetConfig?.bounds

                                        if (displayBounds) {
                                          let boundsText = ""
                                          if (
                                            Array.isArray(displayBounds) &&
                                            displayBounds.length === 2
                                          ) {
                                            boundsText = `${displayBounds[0] !== undefined ? displayBounds[0] : "None"} to ${displayBounds[1] !== undefined ? displayBounds[1] : "None"}`
                                          } else if (
                                            displayBounds.lower !== undefined ||
                                            displayBounds.upper !== undefined
                                          ) {
                                            boundsText = `${displayBounds.lower !== undefined ? displayBounds.lower : "None"} to ${displayBounds.upper !== undefined ? displayBounds.upper : "None"}`
                                          }

                                          if (boundsText) {
                                            return <span>{boundsText}</span>
                                          }
                                        }
                                        return (
                                          <span className="text-muted-foreground">
                                            None
                                          </span>
                                        )
                                      })()}
                                    </TableCell>
                                    <TableCell className="text-right">
                                      {isEditMode ? (
                                        <div className="flex items-center justify-end gap-1 sm:gap-2">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                              toggleTargetExpansion(targetName)
                                            }
                                            className="size-6 p-0"
                                            title="Expand for detailed editing"
                                          >
                                            <Edit className="size-3" />
                                          </Button>
                                        </div>
                                      ) : (() => {
                                        // For Pareto objectives, don't show weights
                                        if (isPareto) {
                                          return (
                                            <span className="text-muted-foreground text-sm">
                                              N/A
                                            </span>
                                          )
                                        }

                                        // Check for pending changes first
                                        const pendingTarget = pendingTargetChanges[targetName]
                                        const displayWeight = pendingTarget?.weight !== undefined ? pendingTarget.weight : singleTarget.weight

                                        if (displayWeight !== undefined) {
                                          return `${Math.round(displayWeight * 100)}%`
                                        } else {
                                          return "100%"
                                        }
                                      })()}
                                    </TableCell>
                                  </TableRow>

                                  {/* Expandable edit form row */}
                                  {isEditMode && isExpanded && (
                                    <TableRow>
                                      <TableCell colSpan={5} className="p-0">
                                        <div className="bg-muted/30 border-t p-2 sm:p-4">
                                          <TargetEditForm
                                            target={{
                                              ...singleTarget,
                                              ...(pendingTargetChanges[
                                                targetName
                                              ] || {})
                                            }}
                                            originalTarget={singleTarget}
                                            onSave={handleTargetSave}
                                            onCancel={() =>
                                              handleTargetCancel(targetName)
                                            }
                                            isMultiTarget={isMultiTarget}
                                            isPareto={isPareto}
                                            className="max-w-none"
                                          />
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                  )}
                                </React.Fragment>
                              )
                            })()}
                      </TableBody>
                    </Table>
                  </div>
                </div>

                {/* 3. Constraints Configuration */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="flex items-center text-lg font-medium">
                      <Link className="mr-2 size-5 text-blue-600" />
                      Constraints
                    </h3>
                    {isEditMode && (
                      <Button
                        variant={expandedConstraints ? "default" : "outline"}
                        size="sm"
                        onClick={() =>
                          setExpandedConstraints(!expandedConstraints)
                        }
                        className="h-8"
                      >
                        {expandedConstraints ? "Collapse" : "Edit"}
                      </Button>
                    )}
                  </div>

                  {/* Always show informational view */}
                  {(!isEditMode || !expandedConstraints) && (
                    <div className="rounded-md border p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          {(() => {
                            const constraints =
                              pendingConstraintsChanges ||
                              (optimizationState.config as any).constraints ||
                              []

                            if (constraints.length === 0) {
                              return (
                                <div>
                                  <div className="font-medium">
                                    No Constraints
                                  </div>
                                  <div className="text-muted-foreground mt-1 text-sm">
                                    No mathematical constraints are currently
                                    configured
                                  </div>
                                </div>
                              )
                            }

                            return (
                              <div>
                                <div className="font-medium">
                                  {constraints.length} Constraint
                                  {constraints.length !== 1 ? "s" : ""}
                                </div>
                                <div className="text-muted-foreground mt-1 text-sm">
                                  {constraints
                                    .map((c: any) => c.type)
                                    .join(", ")}
                                </div>
                                {/* Show constraint details in read-only mode */}
                                {!isEditMode && constraints.length > 0 && (
                                  <div className="mt-3 space-y-2">
                                    {constraints
                                      .slice(0, 3)
                                      .map((constraint: any, index: number) => (
                                        <div
                                          key={index}
                                          className="bg-muted/50 rounded p-2 text-xs"
                                        >
                                          <span className="font-medium">
                                            {constraint.type}
                                          </span>
                                          {constraint.parameters && (
                                            <span className="text-muted-foreground ml-2">
                                              (
                                              {constraint.parameters.join(", ")}
                                              )
                                            </span>
                                          )}
                                        </div>
                                      ))}
                                    {constraints.length > 3 && (
                                      <div className="text-muted-foreground text-xs">
                                        ... and {constraints.length - 3} more
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            )
                          })()}
                        </div>
                        {pendingConstraintsChanges && (
                          <Badge variant="secondary" className="text-xs">
                            Modified
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Show edit form only when in edit mode and expanded */}
                  {isEditMode && expandedConstraints && (
                    <ConstraintsEditSection
                      constraints={
                        pendingConstraintsChanges ||
                        (optimizationState.config as any).constraints ||
                        []
                      }
                      availableParameters={(
                        optimizationState.config as any
                      ).parameters.map((p: any) => ({
                        name: p.name,
                        type: p.type,
                        bounds: p.bounds,
                        values: p.values,
                        encoding: p.encoding,
                        tolerance: p.tolerance
                      }))}
                      onSave={handleConstraintsSave}
                      onCancel={handleConstraintsCancel}
                    />
                  )}
                </div>

                {/* 4. Acquisition Function Configuration */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="flex items-center text-lg font-medium">
                      <Zap className="mr-2 size-5 text-blue-600" />
                      Acquisition Function
                    </h3>
                    {isEditMode && (
                      <Button
                        variant={
                          expandedAcquisitionFunction ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          setExpandedAcquisitionFunction(
                            !expandedAcquisitionFunction
                          )
                        }
                        className="h-8"
                      >
                        {expandedAcquisitionFunction ? "Collapse" : "Edit"}
                      </Button>
                    )}
                  </div>

                  {/* Always show informational view */}
                  {(!isEditMode || !expandedAcquisitionFunction) && (
                    <div className="rounded-md border p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="font-medium">
                            {(() => {
                              const config = pendingAcquisitionChanges ||
                                (optimizationState.config as any)
                                  .acquisition_config || {
                                  type: "qExpectedImprovement"
                                }

                              console.log(
                                "🔍 Acquisition Function Display - Current config:",
                                {
                                  pending: pendingAcquisitionChanges,
                                  fromState: (optimizationState.config as any)
                                    .acquisition_config,
                                  final: config,
                                  stateTimestamp: (optimizationState as any)
                                    .lastUpdated
                                }
                              )

                              const typeLabels: Record<string, string> = {
                                qExpectedImprovement:
                                  "Expected Improvement (EI)",
                                qProbabilityOfImprovement:
                                  "Probability of Improvement (PI)",
                                qUpperConfidenceBound:
                                  "Upper Confidence Bound (UCB)"
                              }

                              return typeLabels[config.type] || config.type
                            })()}
                          </div>
                          <div className="text-muted-foreground mt-1 text-sm">
                            {(() => {
                              const config = pendingAcquisitionChanges ||
                                (optimizationState.config as any)
                                  .acquisition_config || {
                                  type: "qExpectedImprovement"
                                }

                              if (
                                config.type === "qUpperConfidenceBound" &&
                                config.beta
                              ) {
                                return `Beta parameter: ${config.beta} • Controls exploration vs. exploitation balance`
                              }
                              return "Controls how the optimization algorithm explores the parameter space"
                            })()}
                          </div>
                          {/* Show additional details in read-only mode */}
                          {!isEditMode && (
                            <div className="bg-muted/50 mt-3 rounded p-2 text-xs">
                              {(() => {
                                const config = pendingAcquisitionChanges ||
                                  (optimizationState.config as any)
                                    .acquisition_config || {
                                    type: "qExpectedImprovement"
                                  }

                                switch (config.type) {
                                  case "qExpectedImprovement":
                                    return "Balances exploration and exploitation by considering both predicted improvement and uncertainty"
                                  case "qProbabilityOfImprovement":
                                    return "Focuses on areas with high probability of improvement over the current best"
                                  case "qUpperConfidenceBound":
                                    return `Uses confidence bounds with β=${config.beta || 0.2} to balance exploration and exploitation`
                                  default:
                                    return "Advanced acquisition function for optimization guidance"
                                }
                              })()}
                            </div>
                          )}
                        </div>
                        {pendingAcquisitionChanges && (
                          <Badge variant="secondary" className="text-xs">
                            Modified
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Show edit form only when in edit mode and expanded */}
                  {isEditMode && expandedAcquisitionFunction && (
                    <AcquisitionFunctionEditForm
                      acquisitionConfig={memoizedAcquisitionConfig}
                      onSave={handleAcquisitionFunctionSave}
                      onCancel={handleAcquisitionFunctionCancel}
                      optimization={optimizationState}
                    />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-6">
          <AnalysisTab
            optimization={optimization}
            measurements={measurements}
            parameterNames={parameterNames}
            isMultiTarget={isMultiTarget}
            otherTargets={otherTargets}
            initialSection={activeAnalysisSection}
          />
        </TabsContent>

        <TabsContent value="status" className="space-y-6">
          <OptimizationStatusHistory
            optimizationId={optimization.id}
            optimizationName={optimization.name}
          />
        </TabsContent>
      </Tabs>

      {/* Configuration Preview Dialog */}
      <ConfigurationPreviewDialog
        open={showPreviewDialog}
        onOpenChange={setShowPreviewDialog}
        optimization={optimizationState}
        pendingChanges={{
          parameters: pendingParameterChanges,
          targets: pendingTargetChanges,
          acquisitionFunction: pendingAcquisitionChanges,
          constraints: pendingConstraintsChanges
        }}
        onConfirm={handlePreviewConfirm}
        onCancel={handlePreviewCancel}
      />
    </>
  )
}
