#!/usr/bin/env node

/**
 * Test script to verify frontend acquisition function default handling
 * 
 * This script simulates the frontend logic for setting default acquisition function
 * parameters when users don't specify them explicitly.
 */

// Simulate the frontend helper functions
function getDefaultAcquisitionFunction(objectiveType) {
  if (objectiveType === "MULTI_PARETO") {
    return "qNoisyExpectedHypervolumeImprovement"
  }
  return "qExpectedImprovement"
}

function generateSmartReferencePoints(multiTargets) {
  if (!multiTargets || multiTargets.length === 0) {
    return [0.0, 0.0] // Fallback for 2 objectives
  }

  return multiTargets.map((target) => {
    const { lowerBound, upperBound, mode } = target
    
    // Handle cases where bounds are not available
    if (lowerBound === undefined || upperBound === undefined) {
      // Use conservative defaults based on mode
      if (mode === "MAX") {
        return 0.0 // Conservative reference point for maximization
      } else {
        return 100.0 // Conservative reference point for minimization
      }
    }

    const range = upperBound - lowerBound

    if (mode === "MAX") {
      // For maximization, reference point should be below lower bound
      if (lowerBound <= 0) {
        return lowerBound - Math.max(range * 0.1, 1)
      } else {
        return lowerBound * 0.9
      }
    } else {
      // For minimization, reference point should be above upper bound
      if (upperBound >= 0) {
        return upperBound + Math.max(range * 0.1, 1)
      } else {
        return upperBound * 0.9
      }
    }
  })
}

function generateEqualWeights(numObjectives) {
  return Array(numObjectives).fill(1.0 / numObjectives)
}

// Test cases
const testCases = [
  {
    name: "Pareto with bounds - qNEHVI",
    data: {
      objectiveType: "MULTI_PARETO",
      multiTargets: [
        { name: "Target 1", mode: "MAX", lowerBound: 0, upperBound: 100 },
        { name: "Target 2", mode: "MIN", lowerBound: 0, upperBound: 50 }
      ],
      acquisitionFunction: {
        type: "qNoisyExpectedHypervolumeImprovement"
        // No ref_point specified - should auto-generate
      }
    }
  },
  {
    name: "Pareto without bounds - qNEHVI",
    data: {
      objectiveType: "MULTI_PARETO",
      multiTargets: [
        { name: "Target 1", mode: "MAX" },
        { name: "Target 2", mode: "MIN" }
      ],
      acquisitionFunction: {
        type: "qNoisyExpectedHypervolumeImprovement"
      }
    }
  },
  {
    name: "Pareto with qLogNParEGO - no weights",
    data: {
      objectiveType: "MULTI_PARETO",
      multiTargets: [
        { name: "Target 1", mode: "MAX", lowerBound: 0, upperBound: 100 },
        { name: "Target 2", mode: "MIN", lowerBound: 0, upperBound: 50 },
        { name: "Target 3", mode: "MAX", lowerBound: 10, upperBound: 90 }
      ],
      acquisitionFunction: {
        type: "qLogNParEGO"
        // No weights or rho specified - should auto-generate
      }
    }
  },
  {
    name: "Pareto with explicit parameters",
    data: {
      objectiveType: "MULTI_PARETO",
      multiTargets: [
        { name: "Target 1", mode: "MAX", lowerBound: 0, upperBound: 100 },
        { name: "Target 2", mode: "MIN", lowerBound: 0, upperBound: 50 }
      ],
      acquisitionFunction: {
        type: "qLogNParEGO",
        weights: [0.7, 0.3],
        rho: 0.2
      }
    }
  }
]

function simulateAcquisitionConfigGeneration(data) {
  const acquisitionConfig = {
    type: data.acquisitionFunction.type
  }

  // Handle parameters based on acquisition function type
  if (data.acquisitionFunction.type === "qUpperConfidenceBound") {
    acquisitionConfig.beta = data.acquisitionFunction.beta || 0.2 // Use BayBE's default
  } else if (
    data.acquisitionFunction.type === "qNoisyExpectedHypervolumeImprovement" ||
    data.acquisitionFunction.type === "qLogNoisyExpectedHypervolumeImprovement"
  ) {
    // Add reference point for hypervolume-based acquisition functions
    if (data.acquisitionFunction.ref_point) {
      acquisitionConfig.ref_point = data.acquisitionFunction.ref_point
    } else if (data.objectiveType === "MULTI_PARETO") {
      // Auto-generate reference points when not provided for Pareto objectives
      const smartRefPoints = generateSmartReferencePoints(data.multiTargets)
      acquisitionConfig.ref_point = smartRefPoints
      console.log("  🎯 Auto-generated reference points:", smartRefPoints)
    }
    // Add prune_baseline parameter for qNEHVI/qLogNEHVI
    if (data.acquisitionFunction.prune_baseline !== undefined) {
      acquisitionConfig.prune_baseline = data.acquisitionFunction.prune_baseline
    }
  } else if (data.acquisitionFunction.type === "qLogNParEGO") {
    // Add weights for qNParEGO
    if (data.acquisitionFunction.weights) {
      acquisitionConfig.weights = data.acquisitionFunction.weights
    } else if (data.objectiveType === "MULTI_PARETO") {
      // Auto-generate equal weights when not provided for Pareto objectives
      const numObjectives = data.multiTargets.length
      const equalWeights = generateEqualWeights(numObjectives)
      acquisitionConfig.weights = equalWeights
      console.log("  ⚖️ Auto-generated equal weights:", equalWeights)
    }
    // Add rho parameter for augmented Chebyshev scalarization
    if (data.acquisitionFunction.rho !== undefined) {
      acquisitionConfig.rho = data.acquisitionFunction.rho
    } else {
      // Use BayBE's default rho value (0.05) when not specified
      acquisitionConfig.rho = 0.05
      console.log("  🔧 Using default rho value:", 0.05)
    }
  }

  return acquisitionConfig
}

function runTests() {
  console.log("🧪 Testing Frontend Acquisition Function Default Handling")
  console.log("=" * 60)

  testCases.forEach((testCase, index) => {
    console.log(`\n${index + 1}. ${testCase.name}`)
    console.log(`   Acquisition Function: ${testCase.data.acquisitionFunction.type}`)
    console.log(`   Objectives: ${testCase.data.multiTargets.length}`)
    
    const result = simulateAcquisitionConfigGeneration(testCase.data)
    
    console.log("   📤 Generated Config:")
    console.log("   " + JSON.stringify(result, null, 4).replace(/\n/g, '\n   '))
  })

  console.log("\n" + "=" * 60)
  console.log("✅ All frontend default handling tests completed!")
}

// Run the tests
if (require.main === module) {
  runTests()
}

module.exports = { 
  simulateAcquisitionConfigGeneration,
  generateSmartReferencePoints,
  generateEqualWeights,
  getDefaultAcquisitionFunction
}
