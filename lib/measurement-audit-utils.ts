// lib/measurement-audit-utils.ts
import { MeasurementAuditData } from "@/db/schema/measurement-audit-schema"

/**
 * Helper function to create audit data from a measurement object
 */
export function createAuditDataFromMeasurement(
  measurement: any
): MeasurementAuditData {
  return {
    id: measurement.id,
    parameters: measurement.parameters,
    targetValue: measurement.targetValue,
    targetValues: measurement.targetValues,
    isRecommended: measurement.isRecommended,
    batchId: measurement.batchId,
    createdAt: measurement.createdAt,
    updatedAt: measurement.updatedAt
  }
}

/**
 * Helper function to determine which fields changed between two measurement data objects
 */
export function getChangedFields(
  oldData: MeasurementAuditData,
  newData: MeasurementAuditData
): string[] {
  const changedFields: string[] = []

  // Check simple fields
  if (oldData.targetValue !== newData.targetValue) {
    changedFields.push("targetValue")
  }

  if (oldData.isRecommended !== newData.isRecommended) {
    changedFields.push("isRecommended")
  }

  if (oldData.batchId !== newData.batchId) {
    changedFields.push("batchId")
  }

  // Check parameters object
  if (
    JSON.stringify(oldData.parameters) !== JSON.stringify(newData.parameters)
  ) {
    changedFields.push("parameters")
  }

  // Check targetValues object
  if (
    JSON.stringify(oldData.targetValues) !==
    JSON.stringify(newData.targetValues)
  ) {
    changedFields.push("targetValues")
  }

  return changedFields
}
