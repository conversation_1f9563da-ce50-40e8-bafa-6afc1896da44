/*
This server page provides detailed documentation about convergence analysis.
*/

"use server"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  ArrowLeft,
  AreaChart,
  TrendingUp,
  Target,
  AlertCircle,
  BarChart3,
  LineChart,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Lightbulb,
  ExternalLink,
  Calculator
} from "lucide-react"
import Link from "next/link"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"

export default async function ConvergenceAnalysisPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/help">
            <ArrowLeft className="mr-2 size-4" />
            Back to Help Center
          </Link>
        </Button>
      </div>

      <h1 className="flex items-center gap-2 text-3xl font-bold">
        <AreaChart className="text-primary size-8" />
        Convergence Analysis Guide
      </h1>

      <p className="text-muted-foreground text-lg">
        Understanding how to interpret your optimization progress and make
        informed decisions about when to continue or stop experiments
      </p>

      {/* Overview */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AreaChart className="text-primary size-5" />
              What is Convergence Analysis?
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              Convergence analysis shows how your optimization is progressing
              toward finding the best solution. It helps you understand if your
              experiments are improving your results and when you might want to
              stop running more experiments. This is especially important when
              you have a limited budget for experiments.
            </p>

            <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
              <h3 className="mb-2 font-medium text-blue-800">
                Perfect for Limited Experiments
              </h3>
              <p className="text-sm text-blue-700">
                With 25 or fewer experiments, every measurement is valuable.
                Convergence analysis helps you make the most of your limited
                experimental budget by showing when you're making progress and
                when you might be reaching the point of diminishing returns.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Three Main Visualizations */}
      <div className="mt-8 space-y-8">
        <h2 className="text-2xl font-bold">
          Understanding the Three Main Plots
        </h2>

        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
          {/* Convergence Plot */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="size-5 text-blue-500" />
                Convergence Plot
              </CardTitle>
              <CardDescription>
                Track your progress toward the optimal solution
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-4 rounded bg-blue-500"></div>
                  <span className="text-sm">
                    Best value found so far (blue line)
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="size-4 rounded-full bg-gray-400 opacity-60"></div>
                  <span className="text-sm">
                    Individual measurements (gray dots)
                  </span>
                </div>
              </div>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h4 className="font-medium">How to interpret:</h4>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    Upward trend (MAX mode) or downward trend (MIN mode)
                    indicates good progress
                  </li>
                  <li>
                    Flattening curve suggests convergence or need for more
                    exploration
                  </li>
                  <li>
                    Gray dots scattered around the line show measurement noise -
                    normal for real experiments
                  </li>
                </ul>
              </div>

              <Alert>
                <Info className="size-4" />
                <AlertTitle>For limited experiments:</AlertTitle>
                <AlertDescription className="text-sm">
                  Expect some variation, but look for overall improvement trend.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {/* Regret Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="size-5 text-green-500" />
                Regret Analysis
              </CardTitle>
              <CardDescription>
                See how close you are to the best possible result
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm">
                Shows the gap between your current best result and the best
                result you've ever seen.
              </p>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h4 className="font-medium">How to interpret:</h4>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    Decreasing regret = getting closer to the optimal solution
                  </li>
                  <li>Flat regret = optimization has plateaued</li>
                  <li>Lower regret values are better</li>
                </ul>
              </div>

              <Alert>
                <Target className="size-4" />
                <AlertTitle>What this means:</AlertTitle>
                <AlertDescription className="text-sm">
                  Since we don't know the perfect answer, we compare against
                  your best result so far.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {/* Improvement Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="size-5 text-orange-500" />
                Improvement Analysis
              </CardTitle>
              <CardDescription>
                Track when experiments find better solutions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="h-6 w-4 rounded bg-green-500"></div>
                  <span className="text-sm">
                    Iterations that found better solutions
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-6 w-4 rounded bg-gray-400"></div>
                  <span className="text-sm">
                    No improvement found (same performance)
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-6 w-4 rounded bg-red-500"></div>
                  <span className="text-sm">Performance got worse</span>
                </div>
              </div>

              <div className="bg-muted space-y-2 rounded-md p-4">
                <h4 className="font-medium">How to interpret:</h4>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>
                    Many consecutive gray/red bars = potential convergence
                  </li>
                  <li>Regular green bars = good progress</li>
                  <li>Mix of colors is normal and expected</li>
                </ul>
              </div>

              <Alert>
                <AlertCircle className="size-4" />
                <AlertTitle>Important:</AlertTitle>
                <AlertDescription className="text-sm">
                  Don't expect improvement every time - the system balances
                  trying new areas vs refining known good areas.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Statistics Guide */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="text-primary size-5" />
              Understanding the Statistics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-medium">Key Metrics Explained:</h4>
                <div className="space-y-2">
                  <div className="bg-muted flex items-center justify-between rounded p-2">
                    <span className="text-sm font-medium">Iterations:</span>
                    <span className="text-sm">
                      Total number of experiments conducted
                    </span>
                  </div>
                  <div className="bg-muted flex items-center justify-between rounded p-2">
                    <span className="text-sm font-medium">Current Best:</span>
                    <span className="text-sm">Best value found so far</span>
                  </div>
                  <div className="bg-muted flex items-center justify-between rounded p-2">
                    <span className="text-sm font-medium">
                      Total Improvement:
                    </span>
                    <span className="text-sm">
                      Difference between first and best measurement
                    </span>
                  </div>
                  <div className="bg-muted flex items-center justify-between rounded p-2">
                    <span className="text-sm font-medium">Plateau Length:</span>
                    <span className="text-sm">
                      Number of iterations since last improvement
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium">Status Indicators:</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="default">Positive Improvement</Badge>
                    <span className="text-sm">Good progress</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Current Best</Badge>
                    <span className="text-sm">Latest optimal value</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Short Plateau</Badge>
                    <span className="text-sm">Normal (≤5 iterations)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive">Long Plateau</Badge>
                    <span className="text-sm">
                      Consider stopping (&gt;5 iterations)
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Intelligent Recommendation System */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="text-primary size-5" />
              Intelligent Recommendation System
            </CardTitle>
            <CardDescription>
              Understanding the automated insights that help guide your
              optimization decisions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
              <h3 className="mb-2 font-medium text-blue-800">
                What is the Recommendation System?
              </h3>
              <p className="text-sm text-blue-700">
                The recommendation system analyzes your optimization progress
                and provides gentle, data-driven insights to help you make
                informed decisions about when to continue or stop experiments.
                It's designed to be helpful without being pushy, giving you the
                information you need while respecting your judgment.
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium">How It Works</h3>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Data Analysis</h4>
                  <ul className="text-muted-foreground list-disc space-y-1 pl-5 text-sm">
                    <li>
                      Monitors plateau length (consecutive experiments without
                      improvement)
                    </li>
                    <li>Tracks improvement rate and total progress</li>
                    <li>
                      Analyzes regret levels (distance from optimal solution)
                    </li>
                    <li>
                      Considers experimental budget usage (especially for ≤25
                      samples)
                    </li>
                    <li>Evaluates recent progress trends</li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Decision Logic</h4>
                  <ul className="text-muted-foreground list-disc space-y-1 pl-5 text-sm">
                    <li>
                      Combines multiple indicators for robust recommendations
                    </li>
                    <li>Adapts thresholds for small dataset optimization</li>
                    <li>Provides confidence levels based on data strength</li>
                    <li>Considers cost-benefit of additional experiments</li>
                    <li>Accounts for multi-target optimization complexity</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium">Types of Insights You'll See</h3>
              <div className="grid gap-4">
                <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                  <div className="mb-2 flex items-center gap-2">
                    <TrendingUp className="size-4 text-green-600" />
                    <h4 className="font-medium text-green-700">
                      Progress Update
                    </h4>
                  </div>
                  <p className="mb-2 text-sm text-green-700">
                    "Optimization is showing positive progress"
                  </p>
                  <p className="text-xs text-green-600">
                    <strong>When you see this:</strong> Your experiments are
                    finding better solutions. The system detects positive
                    improvement rates and recent progress.
                  </p>
                </div>

                <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
                  <div className="mb-2 flex items-center gap-2">
                    <Clock className="size-4 text-amber-600" />
                    <h4 className="font-medium text-amber-700">
                      Progress Update
                    </h4>
                  </div>
                  <p className="mb-2 text-sm text-amber-700">
                    "Limited recent improvements detected"
                  </p>
                  <p className="text-xs text-amber-600">
                    <strong>When you see this:</strong> You've hit a plateau
                    (5-6 experiments without improvement). Consider whether
                    current results meet your needs before continuing.
                  </p>
                </div>

                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                  <div className="mb-2 flex items-center gap-2">
                    <Info className="size-4 text-blue-600" />
                    <h4 className="font-medium text-blue-700">
                      Optimization Insight
                    </h4>
                  </div>
                  <p className="mb-2 text-sm text-blue-700">
                    "Your optimization appears to have converged"
                  </p>
                  <p className="text-xs text-blue-600">
                    <strong>When you see this:</strong> Strong evidence suggests
                    you've found the optimal solution (7+ experiments without
                    improvement, very low regret). Current results are likely
                    your best.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium">Understanding the Reasoning</h3>
              <p className="text-muted-foreground text-sm">
                Each insight includes the main reason in parentheses. Here's
                what common reasons mean:
              </p>
              <div className="grid gap-3 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="bg-muted rounded p-3">
                    <p className="text-sm font-medium">
                      "No improvement for X consecutive experiments"
                    </p>
                    <p className="text-muted-foreground text-xs">
                      The optimization hasn't found better solutions recently,
                      suggesting convergence.
                    </p>
                  </div>
                  <div className="bg-muted rounded p-3">
                    <p className="text-sm font-medium">
                      "Very low regret suggests optimum found"
                    </p>
                    <p className="text-muted-foreground text-xs">
                      You're very close to the best possible result based on
                      your data.
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="bg-muted rounded p-3">
                    <p className="text-sm font-medium">
                      "Positive improvement rate with recent progress"
                    </p>
                    <p className="text-muted-foreground text-xs">
                      The optimization is actively finding better solutions and
                      should continue.
                    </p>
                  </div>
                  <div className="bg-muted rounded p-3">
                    <p className="text-sm font-medium">
                      "Approaching small dataset limit"
                    </p>
                    <p className="text-muted-foreground text-xs">
                      You're using most of your experimental budget (approaching
                      25 samples).
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Alert>
              <AlertCircle className="size-4" />
              <AlertTitle>Important: You're Always in Control</AlertTitle>
              <AlertDescription className="text-sm">
                The recommendation system provides insights, not commands. You
                know your experimental goals, constraints, and requirements
                better than any algorithm. Use these insights as one factor in
                your decision-making, alongside your domain expertise and
                practical considerations.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      {/* Decision Making Guide */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="text-primary size-5" />
              Making Decisions: When to Continue or Stop
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6 md:grid-cols-3">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="size-5 text-green-500" />
                  <h4 className="font-medium text-green-700">
                    Continue Optimizing
                  </h4>
                </div>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>Steady improvement in convergence plot</li>
                  <li>Decreasing regret over time</li>
                  <li>Regular green bars in improvement plot</li>
                  <li>Plateau length ≤ 3-5 iterations</li>
                  <li>Still within experimental budget</li>
                </ul>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <AlertCircle className="size-5 text-yellow-500" />
                  <h4 className="font-medium text-yellow-700">
                    Consider Stopping
                  </h4>
                </div>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>No improvement for 5-7 consecutive experiments</li>
                  <li>Very small improvements that don't justify the cost</li>
                  <li>Flat regret for several iterations</li>
                  <li>Convergence curve has clearly flattened</li>
                  <li>Approaching experimental budget limit</li>
                </ul>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <XCircle className="size-5 text-red-500" />
                  <h4 className="font-medium text-red-700">Warning Signs</h4>
                </div>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>No improvement after 10+ experiments</li>
                  <li>
                    Highly inconsistent results (check experimental setup)
                  </li>
                  <li>Performance getting worse over time</li>
                  <li>Regret increasing instead of decreasing</li>
                  <li>Experimental errors or measurement issues</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Best Practices */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="text-primary size-5" />
              Best Practices for Using Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-medium text-green-700">✅ Do</h4>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>Use insights as one factor in your decision-making</li>
                  <li>Consider your specific goals and constraints</li>
                  <li>Look at the reasoning provided with each insight</li>
                  <li>Combine recommendations with domain expertise</li>
                  <li>Consider the cost of additional experiments</li>
                  <li>Review all three plots together for full context</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-red-700">❌ Don't</h4>
                <ul className="list-disc space-y-1 pl-5 text-sm">
                  <li>Follow recommendations blindly without consideration</li>
                  <li>
                    Ignore your domain knowledge and practical constraints
                  </li>
                  <li>Stop immediately without evaluating current results</li>
                  <li>Continue indefinitely without budget considerations</li>
                  <li>Make decisions based on single data points</li>
                  <li>Forget to consider experimental costs and timelines</li>
                </ul>
              </div>
            </div>

            <Alert>
              <Lightbulb className="size-4" />
              <AlertTitle>Pro Tip: Context Matters</AlertTitle>
              <AlertDescription className="text-sm">
                The same recommendation might lead to different decisions
                depending on your situation. For example, "consider stopping"
                might mean continue if you have unlimited budget and time, or
                stop if you're working with expensive experiments or tight
                deadlines.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      {/* Mathematical Details */}
      <div className="mt-8">
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="size-5 text-blue-600" />
              Mathematical Details
            </CardTitle>
            <CardDescription>
              For users who want to understand the calculations behind
              convergence analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm">
              Interested in the mathematical foundations? Our detailed
              mathematics guide covers:
            </p>
            <ul className="list-disc space-y-1 pl-5 text-sm">
              <li>
                Best value tracking algorithms and running optimum calculations
              </li>
              <li>Improvement calculation methods for MAX and MIN modes</li>
              <li>Regret computation and theoretical optimum estimation</li>
              <li>
                Statistical measures: plateau detection, improvement rates, and
                convergence indicators
              </li>
              <li>
                Recommendation system decision logic and multi-criteria
                frameworks
              </li>
              <li>Worked examples with real optimization data</li>
            </ul>

            <div className="pt-4">
              <Link
                href="/dashboard/help/convergence-mathematics"
                className="inline-flex items-center gap-2 font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                <Calculator className="size-4" />
                View Complete Mathematical Guide
                <ExternalLink className="size-4" />
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
