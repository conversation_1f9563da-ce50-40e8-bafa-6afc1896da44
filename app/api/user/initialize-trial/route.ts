import { auth } from "@clerk/nextjs/server"
import { initializeTrialAction } from "@/actions/trial-actions"
import { getActualUserId } from "@/lib/auth-utils"
import { NextResponse } from "next/server"

export async function POST() {
  try {
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 INITIALIZE TRIAL API - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return new Response("Unauthorized", { status: 401 })
    }

    // Initialize trial for the current user
    const result = await initializeTrialAction(userId)

    if (!result.isSuccess) {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "Trial initialized successfully",
      trialEndsAt: result.data?.trialEndsAt
    })
  } catch (error) {
    console.error("Error initializing trial:", error)
    return NextResponse.json(
      {
        success: false,
        message: `Error initializing trial: ${error instanceof Error ? error.message : "Unknown error"}`
      },
      { status: 500 }
    )
  }
}
