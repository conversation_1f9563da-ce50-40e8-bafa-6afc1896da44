#!/usr/bin/env node

/**
 * Test script for Pareto acquisition functions with proper parameter handling
 * 
 * This script tests all three acquisition functions available for Pareto objectives:
 * 1. qNoisyExpectedHypervolumeImprovement (qNEHVI) - with ref_point and prune_baseline
 * 2. qLogNoisyExpectedHypervolumeImprovement (qLogNEHVI) - with ref_point and prune_baseline  
 * 3. qLogNParEGO - with weights and rho parameters
 */

const API_BASE_URL = "http://localhost:8000"

// Test configurations for each acquisition function
const testConfigurations = [
  {
    name: "qNEHVI with ref_point",
    config: {
      parameters: [
        { name: "x1", type: "NumericalContinuous", bounds: [0, 100] },
        { name: "x2", type: "NumericalContinuous", bounds: [0, 100] }
      ],
      target_config: [
        { name: "Target 1", mode: "MAX", type: "Numerical" },
        { name: "Target 2", mode: "MIN", type: "Numerical" }
      ],
      objective_type: "Pareto",
      acquisition_config: {
        type: "qNoisyExpectedHypervolumeImprovement",
        ref_point: [0.0, 100.0],  // Explicit reference point
        prune_baseline: true
      },
      initial_sampling_strategy: "LHS",
      num_initial_samples: 0
    }
  },
  {
    name: "qNEHVI with auto-generated ref_point",
    config: {
      parameters: [
        { name: "x1", type: "NumericalContinuous", bounds: [0, 100] },
        { name: "x2", type: "NumericalContinuous", bounds: [0, 100] }
      ],
      target_config: [
        { name: "Target 1", mode: "MAX", type: "Numerical" },
        { name: "Target 2", mode: "MIN", type: "Numerical" }
      ],
      objective_type: "Pareto",
      acquisition_config: {
        type: "qNoisyExpectedHypervolumeImprovement"
        // No ref_point - should auto-generate or use default
      },
      initial_sampling_strategy: "LHS",
      num_initial_samples: 0
    }
  },
  {
    name: "qLogNEHVI with ref_point",
    config: {
      parameters: [
        { name: "x1", type: "NumericalContinuous", bounds: [0, 100] },
        { name: "x2", type: "NumericalContinuous", bounds: [0, 100] }
      ],
      target_config: [
        { name: "Target 1", mode: "MAX", type: "Numerical" },
        { name: "Target 2", mode: "MIN", type: "Numerical" }
      ],
      objective_type: "Pareto",
      acquisition_config: {
        type: "qLogNoisyExpectedHypervolumeImprovement",
        ref_point: [0.0, 100.0],
        prune_baseline: false
      },
      initial_sampling_strategy: "LHS",
      num_initial_samples: 0
    }
  },
  {
    name: "qLogNParEGO with weights and rho",
    config: {
      parameters: [
        { name: "x1", type: "NumericalContinuous", bounds: [0, 100] },
        { name: "x2", type: "NumericalContinuous", bounds: [0, 100] }
      ],
      target_config: [
        { name: "Target 1", mode: "MAX", type: "Numerical" },
        { name: "Target 2", mode: "MIN", type: "Numerical" }
      ],
      objective_type: "Pareto",
      acquisition_config: {
        type: "qLogNParEGO",
        weights: [0.6, 0.4],  // Explicit weights
        rho: 0.1  // Custom rho value
      },
      initial_sampling_strategy: "LHS",
      num_initial_samples: 0
    }
  },
  {
    name: "qLogNParEGO with auto-generated weights",
    config: {
      parameters: [
        { name: "x1", type: "NumericalContinuous", bounds: [0, 100] },
        { name: "x2", type: "NumericalContinuous", bounds: [0, 100] }
      ],
      target_config: [
        { name: "Target 1", mode: "MAX", type: "Numerical" },
        { name: "Target 2", mode: "MIN", type: "Numerical" }
      ],
      objective_type: "Pareto",
      acquisition_config: {
        type: "qLogNParEGO"
        // No weights or rho - should auto-generate/use defaults
      },
      initial_sampling_strategy: "LHS",
      num_initial_samples: 0
    }
  }
]

async function testAcquisitionFunction(testCase) {
  console.log(`\n🧪 Testing: ${testCase.name}`)
  console.log(`📋 Acquisition Function: ${testCase.config.acquisition_config.type}`)
  
  const optimizerId = `test_acq_${testCase.config.acquisition_config.type}_${Date.now()}`
  const config = {
    ...testCase.config,
    optimizer_id: optimizerId
  }
  
  try {
    console.log(`📤 Creating optimization with config:`)
    console.log(JSON.stringify(config.acquisition_config, null, 2))
    
    const response = await fetch(`${API_BASE_URL}/optimizations`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(config)
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }
    
    const result = await response.json()
    console.log(`✅ Success! Optimizer created: ${result.optimizer_id}`)
    
    // Test getting recommendations to ensure the acquisition function works
    console.log(`🎯 Testing recommendation generation...`)
    const recResponse = await fetch(`${API_BASE_URL}/optimizations/${optimizerId}/initialize/predefined`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        n_samples: 2,
        sampling_strategy: "lhs"
      })
    })
    
    if (!recResponse.ok) {
      const errorText = await recResponse.text()
      throw new Error(`Recommendation failed - HTTP ${recResponse.status}: ${errorText}`)
    }
    
    const recommendations = await recResponse.json()
    console.log(`✅ Recommendations generated successfully: ${recommendations.length} samples`)
    
    return { success: true, optimizerId, recommendations }
    
  } catch (error) {
    console.error(`❌ Failed: ${error.message}`)
    return { success: false, error: error.message }
  }
}

async function runAllTests() {
  console.log("🚀 Starting Pareto Acquisition Function Tests")
  console.log("=" * 60)
  
  const results = []
  
  for (const testCase of testConfigurations) {
    const result = await testAcquisitionFunction(testCase)
    results.push({
      name: testCase.name,
      acquisitionFunction: testCase.config.acquisition_config.type,
      ...result
    })
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  // Summary
  console.log("\n" + "=" * 60)
  console.log("📊 TEST SUMMARY")
  console.log("=" * 60)
  
  const successful = results.filter(r => r.success)
  const failed = results.filter(r => !r.success)
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`)
  console.log(`❌ Failed: ${failed.length}/${results.length}`)
  
  if (successful.length > 0) {
    console.log("\n✅ Successful Tests:")
    successful.forEach(r => {
      console.log(`  - ${r.name} (${r.acquisitionFunction})`)
    })
  }
  
  if (failed.length > 0) {
    console.log("\n❌ Failed Tests:")
    failed.forEach(r => {
      console.log(`  - ${r.name} (${r.acquisitionFunction}): ${r.error}`)
    })
  }
  
  console.log("\n🏁 Testing completed!")
  
  return results
}

// Run the tests
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = { runAllTests, testAcquisitionFunction }
