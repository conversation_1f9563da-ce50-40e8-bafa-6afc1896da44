#!/usr/bin/env node

/**
 * Check the measurements table for "test CRUD filtering32" optimization
 * to see if any measurement results were saved there
 */

require('dotenv').config({ path: '.env.local' });

const { drizzle } = require("drizzle-orm/postgres-js")
const postgres = require("postgres")
const { eq, and, desc } = require("drizzle-orm")
const { pgTable, uuid, text, jsonb, timestamp, boolean } = require("drizzle-orm/pg-core")

// Database schema definitions
const optimizationsTable = pgTable("optimizations", {
  id: uuid("id").primaryKey(),
  optimizerId: text("optimizer_id").notNull(),
  name: text("name").notNull(),
  status: text("status").notNull(),
  config: jsonb("config"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

const measurementsTable = pgTable("measurements", {
  id: uuid("id").primaryKey(),
  optimizationId: uuid("optimization_id").notNull(),
  parameters: jsonb("parameters").notNull(),
  targetValue: text("target_value").notNull(),
  targetValues: jsonb("target_values"),
  isRecommended: boolean("is_recommended").notNull(),
  batchId: text("batch_id"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

async function checkMeasurementsTable() {
  console.log('🔍 CHECKING MEASUREMENTS TABLE FOR CRUD32');
  console.log('==========================================');

  let client;
  
  try {
    // Connect to database
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL not found in environment variables');
    }
    
    client = postgres(connectionString);
    const db = drizzle(client);
    
    // Find the optimization
    const optimizationName = "test CRUD filtering32";
    const optimizationsList = await db.select().from(optimizationsTable);
    const opt = optimizationsList.find(o => o.name === optimizationName);
    
    if (!opt) {
      console.error(`❌ Optimization "${optimizationName}" not found`);
      return;
    }
    
    console.log(`✅ Found optimization: ${opt.optimizerId}`);
    console.log(`   Database ID: ${opt.id}`);
    
    // Get ALL measurements for this optimization
    const measurements = await db.select().from(measurementsTable)
      .where(eq(measurementsTable.optimizationId, opt.id))
      .orderBy(desc(measurementsTable.createdAt));
    
    console.log(`\n📊 MEASUREMENTS TABLE ANALYSIS:`);
    console.log(`Found ${measurements.length} measurements in the measurements table`);
    
    if (measurements.length === 0) {
      console.log(`❌ NO MEASUREMENTS FOUND IN MEASUREMENTS TABLE!`);
      console.log(`This confirms that measurement results are not being saved to the database.`);
      
      // Check if there are ANY measurements in the table for any optimization
      const allMeasurements = await db.select().from(measurementsTable);
      console.log(`\n📊 GLOBAL MEASUREMENTS CHECK:`);
      console.log(`Total measurements in entire table: ${allMeasurements.length}`);
      
      if (allMeasurements.length === 0) {
        console.log(`❌ MEASUREMENTS TABLE IS COMPLETELY EMPTY!`);
        console.log(`This suggests the measurement saving functionality is broken globally.`);
      } else {
        console.log(`✅ Other optimizations have measurements, so the issue is specific to this optimization.`);
        
        // Show some examples from other optimizations
        console.log(`\nExample measurements from other optimizations:`);
        allMeasurements.slice(0, 3).forEach((measurement, index) => {
          console.log(`  ${index + 1}. Optimization: ${measurement.optimizationId}`);
          console.log(`     Target Value: ${measurement.targetValue}`);
          console.log(`     Target Values: ${JSON.stringify(measurement.targetValues)}`);
          console.log(`     Batch ID: ${measurement.batchId}`);
          console.log(`     Created: ${measurement.createdAt}`);
        });
      }
    } else {
      console.log(`✅ Found measurements! Analyzing...`);
      
      measurements.forEach((measurement, index) => {
        console.log(`\n  ${index + 1}. Measurement ${measurement.id.slice(-12)}:`);
        console.log(`     - Parameters: ${JSON.stringify(measurement.parameters)}`);
        console.log(`     - Target Value: ${measurement.targetValue}`);
        console.log(`     - Target Values: ${JSON.stringify(measurement.targetValues)}`);
        console.log(`     - Is Recommended: ${measurement.isRecommended}`);
        console.log(`     - Batch ID: ${measurement.batchId}`);
        console.log(`     - Created: ${measurement.createdAt}`);
        console.log(`     - Updated: ${measurement.updatedAt}`);
      });
      
      // Analyze by batch ID
      const measurementsByBatch = {};
      measurements.forEach(m => {
        const batchId = m.batchId || 'no-batch';
        if (!measurementsByBatch[batchId]) {
          measurementsByBatch[batchId] = [];
        }
        measurementsByBatch[batchId].push(m);
      });
      
      console.log(`\n📊 ANALYSIS BY BATCH:`);
      Object.entries(measurementsByBatch).forEach(([batchId, batchMeasurements]) => {
        console.log(`\n  Batch: ${batchId}`);
        console.log(`  - Count: ${batchMeasurements.length} measurements`);
        console.log(`  - Is Recommended: ${batchMeasurements.map(m => m.isRecommended).join(', ')}`);
        
        // Show Target 1 values for this batch
        const target1Values = batchMeasurements.map(m => {
          if (m.targetValues && typeof m.targetValues === 'object') {
            return m.targetValues['Target 1'];
          }
          return m.targetValue;
        }).filter(v => v !== undefined);
        
        if (target1Values.length > 0) {
          console.log(`  - Target 1 values: [${target1Values.join(', ')}]`);
        }
      });
    }
    
    // Now let's check what should have been saved by looking at the samples table
    const samplesTable = pgTable("samples", {
      id: uuid("id").primaryKey(),
      optimizationId: uuid("optimization_id").notNull(),
      parameters: jsonb("parameters").notNull(),
      batchId: text("batch_id").notNull(),
      sampleIndex: text("sample_index").notNull(),
      samplingMethod: text("sampling_method").notNull(),
      sampleClass: text("sample_class").notNull(),
      targetValues: jsonb("target_values"),
      status: text("status").notNull(),
      createdAt: timestamp("created_at").defaultNow(),
      updatedAt: timestamp("updated_at").defaultNow(),
    });
    
    const samples = await db.select().from(samplesTable)
      .where(eq(samplesTable.optimizationId, opt.id))
      .orderBy(desc(samplesTable.createdAt));
    
    console.log(`\n📊 SAMPLES VS MEASUREMENTS COMPARISON:`);
    console.log(`Samples in samples table: ${samples.length}`);
    console.log(`Measurements in measurements table: ${measurements.length}`);
    
    // Count samples with target values (submitted)
    const submittedSamples = samples.filter(s => s.targetValues && s.status === 'submitted');
    console.log(`Submitted samples (with target values): ${submittedSamples.length}`);
    
    if (submittedSamples.length > measurements.length) {
      console.log(`❌ DISCREPANCY: ${submittedSamples.length} submitted samples but only ${measurements.length} measurements saved!`);
      console.log(`This confirms that the measurement saving process is failing.`);
      
      // Show which samples have target values but no corresponding measurements
      console.log(`\n🔍 SUBMITTED SAMPLES THAT SHOULD BE IN MEASUREMENTS TABLE:`);
      submittedSamples.forEach((sample, index) => {
        console.log(`  ${index + 1}. Sample ${sample.id.slice(-12)}:`);
        console.log(`     - Target Values: ${JSON.stringify(sample.targetValues)}`);
        console.log(`     - Status: ${sample.status}`);
        console.log(`     - Batch ID: ${sample.batchId}`);
        console.log(`     - Created: ${sample.createdAt}`);
        
        // Try to find corresponding measurement
        const correspondingMeasurement = measurements.find(m => {
          if (!sample.targetValues || !m.targetValues) return false;
          
          // Compare target values
          const sampleTarget1 = sample.targetValues['Target 1'];
          const measurementTarget1 = m.targetValues['Target 1'];
          
          return sampleTarget1 === measurementTarget1;
        });
        
        if (correspondingMeasurement) {
          console.log(`     ✅ Found corresponding measurement`);
        } else {
          console.log(`     ❌ NO corresponding measurement found!`);
        }
      });
    } else if (submittedSamples.length === measurements.length) {
      console.log(`✅ MATCH: Same number of submitted samples and measurements`);
    } else {
      console.log(`❓ UNEXPECTED: More measurements than submitted samples`);
    }
    
    console.log(`\n🎯 DIAGNOSIS SUMMARY:`);
    console.log(`====================`);
    
    if (measurements.length === 0 && submittedSamples.length > 0) {
      console.log(`❌ CONFIRMED: Measurement saving is completely broken for this optimization`);
      console.log(`Root cause: The addMeasurementWorkflowAction() is failing to save to measurements table`);
      console.log(`\nPossible causes:`);
      console.log(`1. Database constraint violations`);
      console.log(`2. Authentication/authorization failures`);
      console.log(`3. Silent errors in createMeasurementAction()`);
      console.log(`4. Transaction rollbacks`);
      console.log(`5. Incorrect optimization ID mapping`);
    } else if (measurements.length < submittedSamples.length) {
      console.log(`❌ PARTIAL FAILURE: Some measurements saved, others failed`);
      console.log(`This suggests intermittent issues in the saving process`);
    } else {
      console.log(`✅ MEASUREMENTS SAVING APPEARS TO WORK`);
      console.log(`The issue might be elsewhere in the filtering logic`);
    }
    
  } catch (error) {
    console.error('❌ Error during investigation:', error);
  } finally {
    if (client) {
      client.end();
    }
  }
}

// Run the check
checkMeasurementsTable().catch(console.error);
