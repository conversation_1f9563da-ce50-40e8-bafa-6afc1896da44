/*
This file contains utility functions for multi-objective optimization visualizations.
*/

/**
 * Generate a Pareto front for two competing objectives
 *
 * @param numPoints - Number of points to generate on the Pareto front
 * @param noise - Amount of noise to add to the Pareto front (0-1)
 * @returns Array of [objective1, objective2] points
 */
export function generateParetoFront(
  numPoints: number = 20,
  noise: number = 0.1
): [number, number][] {
  // Generate points along a Pareto front (y = 1/x curve)
  const paretoPoints: [number, number][] = []

  // Generate evenly spaced points along the Pareto front
  for (let i = 0; i < numPoints; i++) {
    const x = 0.1 + (i / (numPoints - 1)) * 0.9 // Range from 0.1 to 1
    const y = 1 / x

    // Add some noise to make it look more realistic
    const noiseX = (Math.random() - 0.5) * noise * 0.5
    const noiseY = (Math.random() - 0.5) * noise * 0.5

    paretoPoints.push([x + noiseX, y + noiseY])
  }

  return paretoPoints
}

/**
 * Generate dominated points (non-Pareto optimal solutions)
 *
 * @param numPoints - Number of dominated points to generate
 * @param paretoFront - The Pareto front points
 * @returns Array of [objective1, objective2] points
 */
export function generateDominatedPoints(
  numPoints: number = 30,
  paretoFront: [number, number][]
): [number, number][] {
  const dominatedPoints: [number, number][] = []

  // Find the bounds of the Pareto front
  let minX = Math.min(...paretoFront.map(p => p[0]))
  let maxX = Math.max(...paretoFront.map(p => p[0]))
  let minY = Math.min(...paretoFront.map(p => p[1]))
  let maxY = Math.max(...paretoFront.map(p => p[1]))

  // Generate random dominated points
  for (let i = 0; i < numPoints; i++) {
    // Generate a point within the bounds
    const x = minX + Math.random() * (maxX - minX) * 1.5
    const y = minY + Math.random() * (maxY - minY) * 1.5

    // Make sure the point is dominated (worse in both objectives)
    // For a minimization problem, a dominated point has higher values
    const isDominated = paretoFront.some(
      p => (x > p[0] && y >= p[1]) || (x >= p[0] && y > p[1])
    )

    if (isDominated) {
      dominatedPoints.push([x, y])
    } else {
      // If not dominated, push it further away from the Pareto front
      dominatedPoints.push([x * 1.2, y * 1.2])
    }
  }

  return dominatedPoints
}

/**
 * Generate a set of points representing the optimization progress
 *
 * @param numIterations - Number of iterations to simulate
 * @param paretoFront - The Pareto front points
 * @returns Array of points with iteration information
 */
export function generateOptimizationProgress(
  numIterations: number = 10,
  paretoFront: [number, number][]
): Array<{ x: number; y: number; iteration: number }> {
  const progressPoints: Array<{ x: number; y: number; iteration: number }> = []

  // Start with some random points far from the Pareto front
  for (let i = 0; i < 5; i++) {
    progressPoints.push({
      x: 0.5 + Math.random() * 1.5,
      y: 1.5 + Math.random() * 2,
      iteration: 0
    })
  }

  // Gradually move toward the Pareto front
  for (let iter = 1; iter <= numIterations; iter++) {
    // Pick a random point on the Pareto front as a target
    const targetIndex = Math.floor(Math.random() * paretoFront.length)
    const target = paretoFront[targetIndex]

    // Generate a point that's closer to the Pareto front than previous iterations
    const progressFactor = iter / numIterations // Approaches 1 as iterations increase
    const randomFactor = 1 - progressFactor // Decreases as iterations increase

    const x =
      target[0] * progressFactor + (0.5 + Math.random() * 1.5) * randomFactor
    const y =
      target[1] * progressFactor + (1.5 + Math.random() * 2) * randomFactor

    progressPoints.push({
      x,
      y,
      iteration: iter
    })
  }

  return progressPoints
}

/**
 * Generate a set of weights for multi-objective optimization
 *
 * @param numWeights - Number of weight combinations to generate
 * @returns Array of [weight1, weight2] combinations
 */
export function generateWeightCombinations(
  numWeights: number = 5
): [number, number][] {
  const weights: [number, number][] = []

  for (let i = 0; i < numWeights; i++) {
    const w1 = i / (numWeights - 1)
    const w2 = 1 - w1
    weights.push([w1, w2])
  }

  return weights
}

/**
 * Calculate the weighted sum of objectives
 *
 * @param point - The point [obj1, obj2]
 * @param weights - The weights [weight1, weight2]
 * @returns The weighted sum
 */
export function calculateWeightedSum(
  point: [number, number],
  weights: [number, number]
): number {
  return point[0] * weights[0] + point[1] * weights[1]
}

/**
 * Find the optimal point for a given weight combination
 *
 * @param paretoFront - The Pareto front points
 * @param weights - The weights [weight1, weight2]
 * @returns The optimal point and its weighted sum
 */
export function findOptimalPoint(
  paretoFront: [number, number][],
  weights: [number, number]
): { point: [number, number]; weightedSum: number } {
  let bestPoint = paretoFront[0]
  let bestScore = calculateWeightedSum(bestPoint, weights)

  for (const point of paretoFront) {
    const score = calculateWeightedSum(point, weights)
    if (score < bestScore) {
      // Assuming minimization
      bestScore = score
      bestPoint = point
    }
  }

  return { point: bestPoint, weightedSum: bestScore }
}
