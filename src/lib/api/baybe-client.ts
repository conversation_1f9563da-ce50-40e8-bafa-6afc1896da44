// lib/api/baybe-client.ts
import { createOptimizationDBAction } from "@/actions/db/optimizations-actions"
import { InsertOptimization } from "@/db/schema"
import { ActionState } from "@/types"
import { API_CONFIG, ERROR_MESSAGES, TIMEOUTS } from "@/lib/config"
import { eventEmitter, EVENTS } from "@/lib/events"

/**
 * Generic fetch function for API requests
 */
export async function fetchFromAPI(
  endpoint: string,
  method: string = "GET",
  body?: any,
  token?: string
): Promise<any> {
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
  console.log(
    `[fetchFromAPI:${requestId}] Starting ${method} request to ${endpoint}`
  )

  const url = `${API_CONFIG.BASE_URL}${endpoint}`
  console.log(`[fetchFromAPI:${requestId}] Full URL: ${url}`)

  const headers: HeadersInit = {
    "Content-Type": "application/json",
    "X-API-Key": API_CONFIG.API_KEY,
    "X-Request-ID": requestId
  }

  // Add authorization header if token is provided
  if (token) {
    headers["Authorization"] = `Bearer ${token}`
    console.log(
      `[fetchFromAPI:${requestId}] Added authorization header (token length: ${token.length})`
    )
  }

  // Special handling for categorical parameters in the request body
  let processedBody = body
  if (body && method === "POST" && endpoint === "/optimizations") {
    console.log(
      `[fetchFromAPI:${requestId}] Processing body for optimization creation`
    )
    processedBody = { ...body }

    // Check for categorical parameters
    if (Array.isArray(processedBody.parameters)) {
      console.log(
        `[fetchFromAPI:${requestId}] Processing ${processedBody.parameters.length} parameters`
      )
      processedBody.parameters = processedBody.parameters.map((param: any) => {
        // Create a deep copy to avoid reference issues
        const processedParam = { ...param }

        // Convert CategoricalParameter to Categorical to match backend expectation
        if (processedParam.type === "CategoricalParameter") {
          console.log(
            `[fetchFromAPI:${requestId}] Converting CategoricalParameter ${processedParam.name} to Categorical`
          )
          processedParam.type = "Categorical"

          // Final check for values
          if (
            !Array.isArray(processedParam.values) ||
            processedParam.values.length === 0
          ) {
            console.error(
              `[fetchFromAPI:${requestId}] CRITICAL: Categorical parameter ${processedParam.name} has invalid values at JSON stringify time:`,
              processedParam.values
            )
            // Use default values as last resort (BayBE requires at least 2 values)
            return {
              ...processedParam,
              values: ["default_value_1", "default_value_2"]
            }
          } else if (processedParam.values.length === 1) {
            // BayBE requires at least 2 values
            console.error(
              `[fetchFromAPI:${requestId}] CRITICAL: Categorical parameter ${processedParam.name} has only one value at JSON stringify time, adding a second default value`
            )
            return {
              ...processedParam,
              values: [...processedParam.values, "default_value_2"]
            }
          }
        }
        return processedParam
      })
    }
  }

  // For suggest endpoint, log the batch size
  if (endpoint.includes("/suggest") && endpoint.includes("batch_size=")) {
    const batchSizeMatch = endpoint.match(/batch_size=(\d+)/)
    if (batchSizeMatch && batchSizeMatch[1]) {
      console.log(
        `[fetchFromAPI:${requestId}] Requesting batch size: ${batchSizeMatch[1]}`
      )
    }
  }

  // Set a timeout based on the endpoint and batch size
  let timeout = TIMEOUTS.API_REQUEST

  // For suggest endpoint with large batch sizes, increase the timeout
  if (endpoint.includes("/suggest") && endpoint.includes("batch_size=")) {
    const batchSizeMatch = endpoint.match(/batch_size=(\d+)/)
    if (batchSizeMatch && batchSizeMatch[1]) {
      const batchSize = parseInt(batchSizeMatch[1], 10)

      // Calculate a dynamic timeout based on batch size with a non-linear scaling
      // This better reflects the actual computational complexity
      let dynamicTimeout

      if (batchSize <= 2) {
        // For small batch sizes (1-2), use a fixed small timeout
        dynamicTimeout = TIMEOUTS.SUGGESTION_GENERATION
      } else if (batchSize <= 10) {
        // For medium batch sizes (3-10), use linear scaling
        dynamicTimeout =
          TIMEOUTS.SUGGESTION_GENERATION +
          batchSize * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM
      } else if (batchSize <= 20) {
        // For larger batch sizes (11-20), use slightly higher scaling factor
        dynamicTimeout =
          TIMEOUTS.SUGGESTION_GENERATION +
          10 * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM +
          (batchSize - 10) * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM * 1.5
      } else if (batchSize <= 50) {
        // For even larger batch sizes (21-50), use quadratic-like scaling
        // Base + medium batch time + larger batch time + quadratic component for 21-50
        const baseTime =
          TIMEOUTS.SUGGESTION_GENERATION +
          10 * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM +
          10 * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM * 1.5
        dynamicTimeout =
          baseTime +
          (batchSize - 20) * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM * 3
      } else {
        // For very large batch sizes (51-100), use an even higher scaling factor
        // This reflects the significant non-linear increase in computation time
        const baseTime =
          TIMEOUTS.SUGGESTION_GENERATION +
          10 * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM +
          10 * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM * 1.5 +
          30 * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM * 3
        dynamicTimeout =
          baseTime +
          (batchSize - 50) * TIMEOUTS.SUGGESTION_GENERATION_PER_ITEM * 5
      }

      // Cap at maximum timeout
      dynamicTimeout = Math.min(
        dynamicTimeout,
        TIMEOUTS.SUGGESTION_GENERATION_MAX
      )

      timeout = dynamicTimeout
      console.log(
        `[fetchFromAPI:${requestId}] Using dynamic timeout of ${timeout}ms for batch size ${batchSize}`
      )
    }
  }

  const requestOptions: RequestInit = {
    method,
    headers,
    body: processedBody ? JSON.stringify(processedBody) : undefined,
    // Add timeout using AbortController
    signal: AbortSignal.timeout(timeout)
  }

  console.log(
    `[fetchFromAPI:${requestId}] Sending ${method} request to ${url} with timeout ${timeout}ms`
  )
  if (processedBody) {
    console.log(
      `[fetchFromAPI:${requestId}] Request body size: ${JSON.stringify(processedBody).length} bytes`
    )
  }

  const startTime = Date.now()
  try {
    console.time(`fetchFromAPI:${requestId}`)
    const response = await fetch(url, requestOptions)
    const endTime = Date.now()
    const duration = endTime - startTime

    console.log(
      `[fetchFromAPI:${requestId}] Response received in ${duration}ms with status: ${response.status}`
    )
    console.log(
      `[fetchFromAPI:${requestId}] Response headers:`,
      Object.fromEntries([...response.headers.entries()])
    )

    // Get the response body as text first
    const responseText = await response.text()
    console.log(
      `[fetchFromAPI:${requestId}] Response body size: ${responseText.length} bytes`
    )

    if (responseText.length < 1000) {
      console.log(
        `[fetchFromAPI:${requestId}] Response body: ${responseText.substring(0, 500)}`
      )
    } else {
      console.log(
        `[fetchFromAPI:${requestId}] Response body preview: ${responseText.substring(0, 200)}...`
      )
    }

    if (!response.ok) {
      console.error(
        `[fetchFromAPI:${requestId}] Error response with status ${response.status}`
      )

      // Try to parse error as JSON
      let errorMessage = responseText
      try {
        const errorData = JSON.parse(responseText)
        console.error(
          `[fetchFromAPI:${requestId}] Parsed error data:`,
          errorData
        )

        // Handle structured error response
        if (errorData.detail) {
          // Handle FastAPI validation errors which are often nested
          if (Array.isArray(errorData.detail)) {
            errorMessage = errorData.detail
              .map((err: any) => `${err.loc.join(".")}: ${err.msg}`)
              .join("; ")
          } else {
            errorMessage = errorData.detail
          }
        } else if (errorData.message) {
          errorMessage = errorData.message
        } else {
          errorMessage = JSON.stringify(errorData, null, 2)
        }
      } catch (parseError) {
        // If not valid JSON, use the raw text
        console.error(
          `[fetchFromAPI:${requestId}] Failed to parse error response as JSON:`,
          parseError
        )
        // errorMessage is already set to responseText
      }

      throw new Error(`API error (${response.status}): ${errorMessage}`)
    }

    // Parse the successful response as JSON
    try {
      const jsonData = JSON.parse(responseText)
      console.log(
        `[fetchFromAPI:${requestId}] Successfully parsed JSON response`
      )

      // For suggest endpoint, log the number of suggestions
      if (endpoint.includes("/suggest") && jsonData.suggestions) {
        console.log(
          `[fetchFromAPI:${requestId}] Received ${jsonData.suggestions.length} suggestions`
        )
      }

      console.timeEnd(`fetchFromAPI:${requestId}`)
      return jsonData
    } catch (error) {
      console.error(
        `[fetchFromAPI:${requestId}] Error parsing response as JSON:`,
        error
      )
      throw new Error(
        `Failed to parse response as JSON: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  } catch (error: unknown) {
    const endTime = Date.now()
    const duration = endTime - startTime
    console.error(
      `[fetchFromAPI:${requestId}] Request failed after ${duration}ms:`,
      error
    )
    console.timeEnd(`fetchFromAPI:${requestId}`)

    // Handle timeout errors
    if (error instanceof Error) {
      if (error.name === "TimeoutError" || error.name === "AbortError") {
        console.error(
          `[fetchFromAPI:${requestId}] Request timed out after ${duration}ms`
        )
        throw new Error(`${ERROR_MESSAGES.API.TIMEOUT} (after ${duration}ms)`)
      }
      // Handle network errors
      if (
        error.message?.includes("fetch failed") ||
        error.message?.includes("network")
      ) {
        console.error(
          `[fetchFromAPI:${requestId}] Network error:`,
          error.message
        )
        throw new Error(
          `${ERROR_MESSAGES.API.CONNECTION_ERROR}: ${error.message}`
        )
      }
    }
    // Re-throw other errors
    throw error
  }
}

/**
 * Check the health status of the BayBE API
 */
export async function checkAPIHealthAction(
  token?: string
): Promise<
  ActionState<{ status: string; using_gpu: boolean; gpu_info?: any }>
> {
  try {
    // Use our proxy endpoint to avoid CORS issues
    const response = await fetch("/api/proxy/health")

    if (!response.ok) {
      throw new Error(`API returned status ${response.status}`)
    }

    const data = await response.json()

    return {
      isSuccess: true,
      message: "API is healthy",
      data
    }
  } catch (error) {
    console.error("Health check failed:", error)
    return {
      isSuccess: false,
      message: `API health check failed: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Creates a new optimization with the given configuration
 */
export async function createOptimizationAction(
  optimizerId: string,
  config: any,
  token?: string,
  forceExactId: boolean = false
): Promise<
  ActionState<{
    optimizer_id: string
    status: string
    message: string
    constraint_count?: number
  }>
> {
  try {
    // Add optimizer_id to the config
    const requestBody = {
      ...config,
      optimizer_id: optimizerId
    }

    // Ensure recommender_config has the required fields
    if (requestBody.recommender_config) {
      // Ensure n_restarts is an integer
      if (
        requestBody.recommender_config.n_restarts === undefined ||
        requestBody.recommender_config.n_restarts === null
      ) {
        requestBody.recommender_config.n_restarts = 10
      }

      // Ensure n_raw_samples is an integer
      if (
        requestBody.recommender_config.n_raw_samples === undefined ||
        requestBody.recommender_config.n_raw_samples === null
      ) {
        requestBody.recommender_config.n_raw_samples = 64
      }
    }

    // Ensure categorical parameters have values
    if (Array.isArray(requestBody.parameters)) {
      // Log all parameters before processing
      console.log(
        "Parameters before processing:",
        JSON.stringify(requestBody.parameters, null, 2)
      )

      requestBody.parameters = requestBody.parameters.map((param: any) => {
        // Create a deep copy to avoid reference issues
        const processedParam = { ...param }

        if (processedParam.type === "CategoricalParameter") {
          // Ensure values is an array and not empty
          if (
            !Array.isArray(processedParam.values) ||
            processedParam.values.length === 0
          ) {
            console.error(
              `Categorical parameter ${processedParam.name} has invalid or missing values:`,
              processedParam.values
            )
            // Provide a default value to prevent API errors
            processedParam.values = ["default_value"]
          }

          // Ensure encoding is set
          if (!processedParam.encoding) {
            processedParam.encoding = "OHE"
          }

          // Log the processed parameter
          console.log(
            `Processed categorical parameter ${processedParam.name}:`,
            {
              type: processedParam.type,
              values: processedParam.values,
              encoding: processedParam.encoding
            }
          )
        }
        return processedParam
      })

      // Log all parameters after processing
      console.log(
        "Parameters after processing:",
        JSON.stringify(requestBody.parameters, null, 2)
      )
    }

    // Ensure acquisition_config has the required fields
    if (requestBody.acquisition_config) {
      // Only add beta parameter for Upper Confidence Bound acquisition functions
      const acqType =
        requestBody.acquisition_config.type || "qExpectedImprovement"

      if (
        acqType === "qUpperConfidenceBound" ||
        acqType === "UpperConfidenceBound"
      ) {
        // Ensure beta is a number for UCB functions
        if (
          requestBody.acquisition_config.beta === undefined ||
          requestBody.acquisition_config.beta === null
        ) {
          requestBody.acquisition_config.beta = 1.0
        }
      } else {
        // Remove beta parameter for non-UCB acquisition functions to avoid validation errors
        if (requestBody.acquisition_config.beta !== undefined) {
          console.warn(
            `Removing beta parameter for acquisition function ${acqType} as it's only supported for Upper Confidence Bound functions`
          )
          delete requestBody.acquisition_config.beta
        }
      }
    }

    // For multi-target (Desirability) objective, ensure each target has the required fields
    if (
      requestBody.objective_type === "Desirability" &&
      Array.isArray(requestBody.target_config)
    ) {
      // Make a deep copy of the target_config to avoid modifying the original
      const updatedTargetConfig = requestBody.target_config.map(
        (target: any) => {
          const updatedTarget = { ...target }

          // Ensure each target has a type
          if (!updatedTarget.type) {
            updatedTarget.type = "Numerical"
          }

          // Ensure each target has a mode
          if (!updatedTarget.mode) {
            updatedTarget.mode = "MAX"
          } else {
            // Force mode to be uppercase
            updatedTarget.mode = updatedTarget.mode.toUpperCase()
          }

          // Ensure each target has bounds
          // This is CRITICAL for multi-target optimization with DesirabilityObjective
          if (!updatedTarget.bounds) {
            console.warn(
              `Target ${updatedTarget.name} has no bounds, using default [0, 100]`
            )
            updatedTarget.bounds = [0, 100]
          } else if (
            !Array.isArray(updatedTarget.bounds) ||
            updatedTarget.bounds.length !== 2
          ) {
            // Ensure bounds is a valid array with two elements
            console.warn(
              `Target ${updatedTarget.name} has invalid bounds, using default [0, 100]`
            )
            updatedTarget.bounds = [0, 100]
          } else if (updatedTarget.bounds[0] >= updatedTarget.bounds[1]) {
            // Ensure lower bound is less than upper bound
            console.warn(
              `Target ${updatedTarget.name} has invalid bounds (lower >= upper), adjusting to [${updatedTarget.bounds[0]}, ${updatedTarget.bounds[0] + 100}]`
            )
            updatedTarget.bounds = [
              updatedTarget.bounds[0],
              updatedTarget.bounds[0] + 100
            ]
          }

          // Ensure each target has a transformation
          // This is CRITICAL for multi-target optimization with DesirabilityObjective
          if (!updatedTarget.transformation) {
            // For MAX/MIN targets, LINEAR is the appropriate transformation
            updatedTarget.transformation = "LINEAR"
          } else {
            // Force transformation to be uppercase
            updatedTarget.transformation =
              updatedTarget.transformation.toUpperCase()
          }

          // Ensure each target has a weight
          if (
            updatedTarget.weight === undefined ||
            updatedTarget.weight === null
          ) {
            updatedTarget.weight = 1.0
          }

          return updatedTarget
        }
      )

      // Replace the target_config with the updated version
      requestBody.target_config = updatedTargetConfig

      // WORKAROUND: The backend has a bug with the scalarizer validator
      // Remove scalarizer from the request and let the backend use its default
      delete requestBody.scalarizer
    }

    // Final check for categorical parameters
    if (Array.isArray(requestBody.parameters)) {
      requestBody.parameters = requestBody.parameters.map((param: any) => {
        // Create a deep copy to avoid reference issues
        const processedParam = { ...param }

        // Convert CategoricalParameter to Categorical to match backend expectation
        if (processedParam.type === "CategoricalParameter") {
          processedParam.type = "Categorical"

          // Force values to be an array of strings
          if (
            !Array.isArray(processedParam.values) ||
            processedParam.values.length === 0
          ) {
            console.error(
              `CRITICAL: Categorical parameter ${processedParam.name} still has invalid values before API call:`,
              processedParam.values
            )
            // Use default values as last resort
            processedParam.values = ["default_value_1", "default_value_2"]
          } else if (processedParam.values.length === 1) {
            // BayBE requires at least 2 values
            console.error(
              `CRITICAL: Categorical parameter ${processedParam.name} has only one value, adding a second default value`
            )
            processedParam.values.push("default_value_2")
          }

          // Log the final parameter
          console.log(
            `Final categorical parameter ${processedParam.name} before API call:`,
            {
              type: processedParam.type,
              values: processedParam.values,
              encoding: processedParam.encoding || "OHE"
            }
          )
        }
        return processedParam
      })
    }

    console.log("Request body:", JSON.stringify(requestBody, null, 2))

    let data
    if (forceExactId) {
      console.log(
        `Forcing exact optimizer ID: ${optimizerId} using PUT request`
      )
      // Try to use PUT to overwrite/recreate with exact ID
      try {
        data = await fetchFromAPI(
          `/optimizations/${encodeURIComponent(optimizerId)}`,
          "PUT",
          requestBody,
          token
        )
      } catch (error) {
        console.log("PUT request failed, falling back to POST:", error)
        // If PUT fails, fall back to POST
        data = await fetchFromAPI(`/optimizations`, "POST", requestBody, token)
      }
    } else {
      data = await fetchFromAPI(`/optimizations`, "POST", requestBody, token)
    }
    return {
      isSuccess: true,
      message: forceExactId
        ? "Optimization recreated successfully"
        : "Optimization created successfully",
      data: {
        ...data,
        optimizer_id: forceExactId
          ? optimizerId
          : data.optimizer_id || optimizerId
      }
    }
  } catch (error) {
    console.error("Error creating optimization:", error)

    // Provide more detailed error messages for specific error types
    if (error instanceof Error) {
      // For multi-target optimization normalization errors
      if (
        error.message.includes("normalized computational representations") ||
        error.message.includes("appropriate target bounds and transformations")
      ) {
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.CREATE_FAILED}: Multi-target optimization with the Desirability objective requires each target to have both bounds and a LINEAR transformation. Please ensure all targets have proper bounds (lower < upper) and LINEAR transformation. The app will try to set these automatically, but you may need to adjust your configuration.`
        }
      }

      // For Body is unusable errors
      if (error.message.includes("Body is unusable")) {
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.CREATE_FAILED}: There was an error communicating with the API. Please try again.`
        }
      }

      // For JSON parsing errors
      if (error.message.includes("Failed to parse response as JSON")) {
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.CREATE_FAILED}: The API returned an invalid response. Please check the server logs.`
        }
      }

      // For timeout errors
      if (
        error.message.includes("timeout") ||
        error.message.includes("Timeout")
      ) {
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.CREATE_FAILED}: The API request timed out. Please try again later.`
        }
      }

      // For 500 Internal Server Error
      if (
        error.message.includes("500") ||
        error.message.includes("Internal Server Error")
      ) {
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.CREATE_FAILED}: The server encountered an internal error. This might be due to an issue with the multi-target configuration. Please try again with different parameters or contact support.`
        }
      }

      // For scalarizer validation error
      if (
        error.message.includes("validate_scalarizer") ||
        error.message.includes("got multiple values")
      ) {
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.CREATE_FAILED}: There is an issue with the scalarizer configuration. Please try again without specifying a scalarizer value.`
        }
      }

      // For categorical parameter errors
      if (
        error.message.includes("CategoricalParameter") &&
        error.message.includes("missing") &&
        error.message.includes("values")
      ) {
        // Log detailed information about the error
        console.error("Categorical parameter error details:", error.message)

        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.CREATE_FAILED}: Categorical parameters must have values. Please ensure all categorical parameters have at least one value specified. Error: ${error.message}`
        }
      }
    }

    // Default error message
    return {
      isSuccess: false,
      message: `${ERROR_MESSAGES.OPTIMIZATION.CREATE_FAILED}: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Generate samples using Latin Hypercube Sampling, random sampling, or Sobol sequences
 * Now supports constraint-aware sampling
 */
export async function generateSamplesAction(
  optimizerId: string,
  numSamples: number = 10,
  samplingStrategy: string = "LHS",
  seed?: number,
  token?: string,
  respectConstraints: boolean = false,
  maxAttempts: number = 1000,
  tolerance: number = 1e-6
): Promise<
  ActionState<{
    status: string
    samples: any[]
    constraint_violations?: number
    feasible_samples?: number
    total_attempts?: number
    discretization_transparency?: {
      has_discretization: boolean
      discrete_parameters: Array<{
        parameter: string
        type: string
        allowed_values: number[]
        original_values: number[]
        discretized_values: number[]
        has_changes: boolean
      }>
      sample_transparency: Array<{
        sample_index: number
        parameter_changes: Record<
          string,
          {
            original: number
            discretized: number
            allowed_values: number[]
          }
        >
      }>
    }
  }>
> {
  try {
    console.log(
      "🔍 generateSamplesAction: Starting with constraint awareness",
      {
        optimizerId,
        numSamples,
        samplingStrategy,
        respectConstraints,
        maxAttempts,
        tolerance
      }
    )

    // Use the dedicated initialization endpoint for sampling with constraint support
    const requestBody = {
      n_samples: numSamples,
      seed,
      sampling_strategy: samplingStrategy,
      respect_constraints: respectConstraints,
      max_attempts: maxAttempts,
      tolerance: tolerance
    }

    console.log("🔍 generateSamplesAction: Sending request to backend", {
      endpoint: `/optimizations/${encodeURIComponent(optimizerId)}/initialize/predefined`,
      requestBody
    })

    const data = await fetchFromAPI(
      `/optimizations/${encodeURIComponent(optimizerId)}/initialize/predefined`,
      "POST",
      requestBody,
      token
    )

    console.log("🔍 generateSamplesAction: Backend response", {
      status: data?.status,
      samplesCount: data?.samples?.length || 0,
      constraintViolations: data?.constraint_violations,
      feasibleSamples: data?.feasible_samples
    })

    return {
      isSuccess: true,
      message: `Successfully generated ${numSamples} samples using ${samplingStrategy}`,
      data: {
        ...data,
        // Ensure discretization_transparency is included if present
        discretization_transparency: data.discretization_transparency
      }
    }
  } catch (error) {
    console.error("Error generating samples:", error)
    return {
      isSuccess: false,
      message: `Failed to generate samples: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Gets the next suggestion for experimentation
 */
export async function getSuggestionAction(
  optimizerId: string,
  batchSize: number = 1,
  token?: string
): Promise<ActionState<{ status: string; suggestions: any[] }>> {
  console.log(
    `[getSuggestionAction] Starting with optimizerId=${optimizerId}, batchSize=${batchSize}`
  )

  // Validate batch size
  const validatedBatchSize = Math.min(Math.max(1, batchSize), 100)
  if (validatedBatchSize !== batchSize) {
    console.warn(
      `[getSuggestionAction] Adjusted batch size from ${batchSize} to ${validatedBatchSize} (valid range: 1-100)`
    )
  }

  // Emit event to notify that a GPU-intensive operation is starting
  // This will trigger more frequent API status updates
  eventEmitter.emit(EVENTS.GPU_INTENSIVE_OPERATION_STARTED)

  // Define url outside the try block so it's available in the catch block
  console.log(
    `[getSuggestionAction] Constructing API URL for batch size ${validatedBatchSize}`
  )
  const url = `/optimizations/${encodeURIComponent(optimizerId)}/suggest?batch_size=${validatedBatchSize}`
  console.log(`[getSuggestionAction] API URL: ${url}`)

  try {
    console.log(`[getSuggestionAction] Calling fetchFromAPI with method=GET`)
    console.time("getSuggestionAction-fetchFromAPI")

    // Request an immediate API status update before the operation
    eventEmitter.emit(EVENTS.API_STATUS_UPDATE_REQUESTED)

    const data = await fetchFromAPI(url, "GET", undefined, token)

    console.timeEnd("getSuggestionAction-fetchFromAPI")

    // Log the response data
    console.log(
      `[getSuggestionAction] API response received with status: ${data.status}`
    )
    console.log(
      `[getSuggestionAction] Received ${data.suggestions?.length || 0} suggestions`
    )

    // Log the first suggestion for debugging
    if (data.suggestions && data.suggestions.length > 0) {
      console.log(
        `[getSuggestionAction] First suggestion sample:`,
        JSON.stringify(data.suggestions[0], null, 2).substring(0, 200) + "..."
      )
    }

    // Emit event to notify that the GPU-intensive operation has completed
    eventEmitter.emit(EVENTS.GPU_INTENSIVE_OPERATION_ENDED)

    // Request an immediate API status update after the operation
    eventEmitter.emit(EVENTS.API_STATUS_UPDATE_REQUESTED)

    return {
      isSuccess: true,
      message: "Suggestions generated successfully",
      data
    }
  } catch (error) {
    console.error("[getSuggestionAction] Error getting suggestions:", error)

    // Try to extract more information from the error
    let errorDetails = ""
    if (error instanceof Error) {
      errorDetails = `Error type: ${error.constructor.name}, Message: ${error.message}`
      if (error.stack) {
        console.error("[getSuggestionAction] Error stack:", error.stack)
      }

      // For fetch errors, try to get more details
      if ("cause" in error) {
        console.error("[getSuggestionAction] Error cause:", error.cause)
      }
    }

    console.error(`[getSuggestionAction] Error details: ${errorDetails}`)

    // Provide more detailed error messages for specific error types
    if (error instanceof Error) {
      // For optimizer not found errors
      if (
        error.message.includes("not found") ||
        error.message.includes("404")
      ) {
        console.error(
          "[getSuggestionAction] Optimization not found error detected"
        )
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.SUGGESTION_FAILED}: Optimization not found. It may have been deleted or the server was restarted.`
        }
      }

      // For Body is unusable errors
      if (error.message.includes("Body is unusable")) {
        console.error("[getSuggestionAction] Body is unusable error detected")
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.SUGGESTION_FAILED}: There was an error communicating with the API. Please try again.`
        }
      }

      // For timeout errors
      if (
        error.message.includes("timeout") ||
        error.message.includes("timed out")
      ) {
        console.error("[getSuggestionAction] Timeout error detected")

        // Extract batch size from error message if available
        let batchSizeInfo = ""

        // Try to extract batch size from the URL if available
        const urlBatchSizeMatch = url?.match(/batch_size=(\d+)/)
        // Also try to extract from error message
        const errorBatchSizeMatch = error.message.match(/batch size (\d+)/i)

        // Use whichever match we found
        const batchSizeMatch = urlBatchSizeMatch || errorBatchSizeMatch

        if (batchSizeMatch && batchSizeMatch[1]) {
          const batchSize = parseInt(batchSizeMatch[1], 10)

          if (batchSize > 50) {
            batchSizeInfo = ` You requested ${batchSize} suggestions, which is very large. For batch sizes over 50, timeouts are common. We recommend using a batch size of 20 or less for reliable performance.`
          } else if (batchSize > 20) {
            batchSizeInfo = ` You requested ${batchSize} suggestions. For batch sizes between 20-50, timeouts may occur. Consider reducing to 10-20 suggestions for better reliability.`
          } else if (batchSize > 10) {
            batchSizeInfo = ` You requested ${batchSize} suggestions. Try reducing to 5-10 suggestions if this problem persists.`
          }
        }

        // Get the duration from the error message if available
        let durationInfo = ""
        const durationMatch = error.message.match(/after (\d+)ms/)
        if (durationMatch && durationMatch[1]) {
          durationInfo = ` after ${durationMatch[1]}ms`
        }

        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.SUGGESTION_FAILED}: The API request timed out${durationInfo}.${batchSizeInfo} This happens when generating multiple suggestions requires extensive computation. Try reducing the batch size or try again later.`
        }
      }
    }

    // Emit event to notify that the GPU-intensive operation has ended (even with error)
    eventEmitter.emit(EVENTS.GPU_INTENSIVE_OPERATION_ENDED)

    // Request an immediate API status update after the operation
    eventEmitter.emit(EVENTS.API_STATUS_UPDATE_REQUESTED)

    return {
      isSuccess: false,
      message: `${ERROR_MESSAGES.OPTIMIZATION.SUGGESTION_FAILED}: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Adds a measurement to the optimization
 */
export async function addMeasurementAction(
  optimizerId: string,
  parameters: Record<string, any>,
  targetValue: number | Record<string, number>,
  token?: string
): Promise<ActionState<{ status: string; message: string }>> {
  try {
    // Handle both single target value and multi-target values
    let requestBody

    // Ensure all numerical parameters are properly converted to numbers
    const processedParameters = { ...parameters }
    Object.entries(processedParameters).forEach(([key, value]) => {
      if (typeof value === "string") {
        const numValue = parseFloat(value)
        if (!isNaN(numValue)) {
          processedParameters[key] = numValue
        }
      }
    })

    if (typeof targetValue === "object") {
      // For multi-target optimizations
      console.log("Multi-target measurement detected", targetValue)

      // Convert to the new API format with target_values as an array of objects
      const targetValuesArray = Object.entries(targetValue).map(
        ([name, value]) => ({
          name,
          value: typeof value === "string" ? parseFloat(value) : value
        })
      )

      requestBody = {
        parameters: processedParameters,
        target_values: targetValuesArray
      }

      console.log("Using new multi-target API format:", requestBody)
    } else {
      // Standard single-target measurement
      // For backward compatibility, we'll use the target_values field with a number
      requestBody = {
        parameters: processedParameters,
        target_values: targetValue
      }
    }

    const data = await fetchFromAPI(
      `/optimizations/${encodeURIComponent(optimizerId)}/measurements`,
      "POST",
      requestBody,
      token
    )
    return {
      isSuccess: true,
      message: "Measurement added successfully",
      data
    }
  } catch (error) {
    console.error("Error adding measurement:", error)

    // Provide more detailed error messages for specific error types
    if (error instanceof Error) {
      // For optimizer not found errors
      if (
        error.message.includes("not found") ||
        error.message.includes("404")
      ) {
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.MEASUREMENT_FAILED}: Optimization not found. It may have been deleted or the server was restarted.`
        }
      }

      // For Body is unusable errors
      if (error.message.includes("Body is unusable")) {
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.MEASUREMENT_FAILED}: There was an error communicating with the API. Please try again.`
        }
      }

      // For invalid measurement errors
      if (
        error.message.includes("invalid") &&
        error.message.includes("measurement")
      ) {
        return {
          isSuccess: false,
          message: `${ERROR_MESSAGES.OPTIMIZATION.MEASUREMENT_FAILED}: The measurement data is invalid. Please check the parameter values.`
        }
      }
    }

    return {
      isSuccess: false,
      message: `${ERROR_MESSAGES.OPTIMIZATION.MEASUREMENT_FAILED}: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Adds multiple measurements to the optimization
 */
export async function addMultipleMeasurementsAction(
  optimizerId: string,
  measurements: {
    parameters: Record<string, any>
    target_value: number | Record<string, number>
  }[],
  token?: string
): Promise<ActionState<{ status: string; message: string }>> {
  try {
    // Process measurements to use the new API format
    const processedMeasurements = measurements.map(measurement => {
      // Ensure all numerical parameters are properly converted to numbers
      const processedParameters = { ...measurement.parameters }
      Object.entries(processedParameters).forEach(([key, value]) => {
        if (typeof value === "string") {
          const numValue = parseFloat(value)
          if (!isNaN(numValue)) {
            processedParameters[key] = numValue
          }
        }
      })

      if (typeof measurement.target_value === "object") {
        // For multi-target measurements
        // Convert to the new API format with target_values as an array of objects
        const targetValuesArray = Object.entries(measurement.target_value).map(
          ([name, value]) => ({
            name,
            value: typeof value === "string" ? parseFloat(value) : value
          })
        )

        return {
          parameters: processedParameters,
          target_values: targetValuesArray
        }
      }

      // For single-target measurements
      return {
        parameters: processedParameters,
        target_values: measurement.target_value
      }
    })

    console.log("Processed batch measurements:", processedMeasurements)

    const data = await fetchFromAPI(
      `/optimizations/${encodeURIComponent(optimizerId)}/measurements/batch`,
      "POST",
      { measurements: processedMeasurements },
      token
    )
    return {
      isSuccess: true,
      message: "Measurements added successfully",
      data
    }
  } catch (error) {
    console.error("Error adding multiple measurements:", error)
    return {
      isSuccess: false,
      message: `${ERROR_MESSAGES.OPTIMIZATION.MEASUREMENT_FAILED}: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Gets the current best point for the optimization
 */
export async function getBestPointAction(
  optimizerId: string,
  token?: string
): Promise<
  ActionState<{
    status: string
    best_parameters?: Record<string, any>
    best_value?: number
    best_values?: Record<string, number> // Added for multi-target optimizations
    message?: string
  }>
> {
  try {
    const data = await fetchFromAPI(
      `/optimizations/${encodeURIComponent(optimizerId)}/best`,
      "GET",
      undefined,
      token
    )

    // Check if the response has a message but no best_parameters
    // This happens when there are no measurements yet
    if (data.message && !data.best_parameters) {
      console.log(
        `Best point API returned message without data: ${data.message}`
      )
      return {
        isSuccess: true,
        message: data.message,
        data: {
          status: "success",
          message: data.message,
          best_parameters: undefined,
          best_value: undefined,
          best_values: undefined
        }
      }
    }

    return {
      isSuccess: true,
      message: "Best point retrieved successfully",
      data
    }
  } catch (error) {
    console.error("Error getting best point:", error)

    // Handle the specific Python error
    if (
      error instanceof Error &&
      error.message.includes("'tuple' object has no attribute 'get'")
    ) {
      console.log(
        "Handling tuple error gracefully - this is likely a backend API issue with a new optimization"
      )
      // Return a successful response with empty data
      return {
        isSuccess: true,
        message: "No best point available yet",
        data: {
          status: "success",
          message: "No best point available yet",
          best_parameters: undefined,
          best_value: undefined,
          best_values: undefined
        }
      }
    }

    return {
      isSuccess: false,
      message: `${ERROR_MESSAGES.OPTIMIZATION.LOAD_FAILED}: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Checks if an optimization exists
 */
export async function checkOptimizationExistsAction(
  optimizerId: string,
  token?: string
): Promise<ActionState<boolean>> {
  try {
    // Try to get the best point as a way to check if the optimization exists
    await fetchFromAPI(
      `/optimizations/${encodeURIComponent(optimizerId)}/best`,
      "GET",
      undefined,
      token
    )
    return {
      isSuccess: true,
      message: "Optimization exists",
      data: true
    }
  } catch (error) {
    // If we get a 404 or "not found" error, the optimization doesn't exist
    if (
      error instanceof Error &&
      (error.message.includes("404") ||
        error.message.includes("not found") ||
        (error.message.includes("Optimizer") &&
          error.message.includes("not found")))
    ) {
      return {
        isSuccess: true,
        message: "Optimization does not exist",
        data: false
      }
    }
    // For other errors, we're not sure
    return {
      isSuccess: false,
      message: `Error checking if optimization exists: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Loads an existing optimization
 *
 * Note: This function is a placeholder as the backend API doesn't have a /load endpoint.
 * It's kept for compatibility with existing code but will always return a success response
 * with a message indicating that the endpoint is not implemented.
 */
export async function loadOptimizationAction(
  optimizerId: string,
  token?: string
): Promise<ActionState<{ status: string; message: string }>> {
  console.log(
    `Note: The /load endpoint is not implemented in the backend API. Skipping load for ${optimizerId}`
  )

  // Return a success response to avoid breaking existing code
  return {
    isSuccess: true,
    message:
      "Load endpoint not implemented in backend API. Optimization will be loaded automatically when needed.",
    data: { status: "success", message: "Load endpoint not implemented" }
  }
}
