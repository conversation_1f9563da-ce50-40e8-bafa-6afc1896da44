"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Save, X, RotateCcw } from "lucide-react"

interface ParameterConfig {
  name: string
  type: "NumericalContinuous" | "NumericalDiscrete" | "CategoricalParameter"
  bounds?: [number, number]
  values?: (number | string)[]
  encoding?: string
  tolerance?: number
  rawValuesInput?: string // Store the raw input string to preserve user typing
  rawBoundsInput?: { [key: number]: string } // Store raw bound inputs for editing
}

interface ParameterEditFormProps {
  parameter: ParameterConfig
  onSave: (updatedParameter: ParameterConfig) => void
  onCancel: () => void
  className?: string
}

export function ParameterEditForm({
  parameter,
  onSave,
  onCancel,
  className = ""
}: ParameterEditFormProps) {
  const [editedParameter, setEditedParameter] =
    useState<ParameterConfig>(parameter)
  const [hasChanges, setHasChanges] = useState(false)

  // Reset form when parameter changes
  useEffect(() => {
    const initialParam = { ...parameter }
    // Initialize rawValuesInput with the current values if they exist
    if (parameter.values && parameter.values.length > 0) {
      initialParam.rawValuesInput = parameter.values.join(", ")
    }
    // Initialize rawBoundsInput with the current bounds if they exist
    if (parameter.bounds) {
      initialParam.rawBoundsInput = {
        0: parameter.bounds[0]?.toString() ?? "",
        1: parameter.bounds[1]?.toString() ?? ""
      }
    }
    setEditedParameter(initialParam)
    setHasChanges(false)
  }, [parameter])

  // Check for changes
  useEffect(() => {
    const changed =
      JSON.stringify(editedParameter) !== JSON.stringify(parameter)
    setHasChanges(changed)
  }, [editedParameter, parameter])

  const handleBoundChange = (index: 0 | 1, value: string) => {
    // Store the raw input to allow partial editing (like backspacing)
    setEditedParameter(prev => {
      const newBounds = [...(prev.bounds || [0, 0])] as [number, number]

      // Allow empty string for editing, but convert to 0 for storage
      if (value === "") {
        newBounds[index] = 0
      } else {
        const numValue = parseFloat(value)
        if (!isNaN(numValue)) {
          newBounds[index] = numValue
        } else {
          // For invalid input, keep the previous value but allow the input to show
          // This enables partial editing like typing "-" or "1."
          return {
            ...prev,
            rawBoundsInput: {
              ...prev.rawBoundsInput,
              [index]: value
            }
          }
        }
      }

      return {
        ...prev,
        bounds: newBounds,
        rawBoundsInput: {
          ...prev.rawBoundsInput,
          [index]: value
        }
      }
    })
  }

  const handleValuesChange = (valuesString: string) => {
    // Store the raw input string directly in a temporary state
    // This allows users to type commas anywhere, including at the end
    setEditedParameter(prev => {
      // Only parse the values when we need to use them (for display or submission)
      let parsedValues: (number | string)[] = []

      if (prev.type === "NumericalDiscrete") {
        // Parse as numbers, but only for non-empty parts
        parsedValues = valuesString
          .split(",")
          .map(s => s.trim())
          .filter(s => s.length > 0) // Keep empty strings for trailing commas
          .map(s => parseFloat(s))
          .filter(n => !isNaN(n))
      } else if (prev.type === "CategoricalParameter") {
        // Parse as strings, but only for non-empty parts
        parsedValues = valuesString
          .split(",")
          .map(s => s.trim())
          .filter(s => s.length > 0) // Keep empty strings for trailing commas
      }

      return {
        ...prev,
        // Store both the raw input string and the parsed values
        rawValuesInput: valuesString,
        values: parsedValues
      }
    })
  }

  const handleToleranceChange = (value: string) => {
    const numValue = parseFloat(value)
    if (!isNaN(numValue) && numValue >= 0) {
      setEditedParameter(prev => ({
        ...prev,
        tolerance: numValue
      }))
    }
  }

  const handleSave = () => {
    onSave(editedParameter)
  }

  const handleReset = () => {
    setEditedParameter(parameter)
    setHasChanges(false)
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="space-y-4 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h4 className="font-medium">{parameter.name}</h4>
            <Badge variant="outline">{parameter.type}</Badge>
          </div>
          {hasChanges && (
            <Badge variant="secondary" className="text-xs">
              Modified
            </Badge>
          )}
        </div>

        {/* Parameter Name Field */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Parameter Name</Label>
          <Input
            type="text"
            value={editedParameter.name}
            onChange={e =>
              setEditedParameter(prev => ({ ...prev, name: e.target.value }))
            }
            placeholder="Parameter name"
            className="h-8"
          />
        </div>

        {/* Numerical Continuous Parameters */}
        {editedParameter.type === "NumericalContinuous" && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Bounds</Label>
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
              <div className="space-y-1">
                <Label className="text-muted-foreground text-xs">Minimum</Label>
                <Input
                  type="number"
                  value={
                    editedParameter.rawBoundsInput?.[0] ??
                    editedParameter.bounds?.[0] ??
                    ""
                  }
                  onChange={e => handleBoundChange(0, e.target.value)}
                  step="any"
                  placeholder="Min value"
                  className="h-8"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-muted-foreground text-xs">Maximum</Label>
                <Input
                  type="number"
                  value={
                    editedParameter.rawBoundsInput?.[1] ??
                    editedParameter.bounds?.[1] ??
                    ""
                  }
                  onChange={e => handleBoundChange(1, e.target.value)}
                  step="any"
                  placeholder="Max value"
                  className="h-8"
                />
              </div>
            </div>
          </div>
        )}

        {/* Discrete and Categorical Parameters */}
        {(editedParameter.type === "NumericalDiscrete" ||
          editedParameter.type === "CategoricalParameter") && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Values{" "}
              {editedParameter.type === "NumericalDiscrete"
                ? "(comma-separated numbers)"
                : "(comma-separated options)"}
            </Label>
            <Input
              type="text"
              value={
                editedParameter.rawValuesInput ??
                editedParameter.values?.join(", ") ??
                ""
              }
              onChange={e => {
                handleValuesChange(e.target.value)
              }}
              onKeyDown={e => {
                // Allow all keys including comma
              }}
              placeholder={
                editedParameter.type === "NumericalDiscrete"
                  ? "e.g., 1, 2, 5, 10"
                  : "e.g., option1, option2, option3"
              }
              className="h-8"
            />
          </div>
        )}

        {/* Encoding for Categorical Parameters */}
        {editedParameter.type === "CategoricalParameter" && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Encoding</Label>
            <Select
              value={editedParameter.encoding || "OHE"}
              onValueChange={value =>
                setEditedParameter(prev => ({ ...prev, encoding: value }))
              }
            >
              <SelectTrigger className="h-8">
                <SelectValue placeholder="Select encoding method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="OHE">One-Hot Encoding (OHE)</SelectItem>
                <SelectItem value="LE">Label Encoding (LE)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Tolerance for Numerical Discrete */}
        {editedParameter.type === "NumericalDiscrete" && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Tolerance</Label>
            <Input
              type="number"
              value={editedParameter.tolerance || ""}
              onChange={e => handleToleranceChange(e.target.value)}
              step="any"
              min="0"
              placeholder="Tolerance value"
              className="h-8"
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col gap-2 pt-2 sm:flex-row sm:items-center sm:justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            disabled={!hasChanges}
            className="h-8 w-full sm:w-auto"
          >
            <RotateCcw className="mr-1 size-3" />
            Reset
          </Button>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              className="h-8 flex-1 sm:flex-none"
            >
              <X className="mr-1 size-3" />
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
              className="h-8 flex-1 sm:flex-none"
            >
              <Save className="mr-1 size-3" />
              Save
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
