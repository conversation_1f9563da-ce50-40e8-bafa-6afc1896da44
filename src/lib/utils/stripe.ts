/*
Contains the Stripe configuration for the app.
*/

import Stripe from "stripe"

// Create a mock Stripe client if the API key is not available
class MockStripe {
  constructor() {
    console.warn("Using MockStripe because STRIPE_SECRET_KEY is not available")
  }

  // Mock methods that might be called
  customers = {
    create: async () => ({ id: "mock_customer_id" }),
    retrieve: async () => ({ id: "mock_customer_id" })
  }

  checkout = {
    sessions: {
      create: async () => ({ id: "mock_session_id", url: "#" })
    }
  }

  products = {
    retrieve: async () => ({
      id: "mock_product_id",
      name: "Mock Product",
      metadata: { membership: "pro" }
    })
  }

  subscriptions = {
    retrieve: async () => ({
      id: "mock_subscription_id",
      status: "active",
      items: {
        data: [
          {
            price: {
              id: "mock_price_id",
              recurring: { interval: "month" }
            }
          }
        ]
      }
    })
  }
}

// Initialize Stripe or MockStripe based on environment
let stripeInstance

try {
  if (!process.env.STRIPE_SECRET_KEY) {
    console.warn("STRIPE_SECRET_KEY is not defined. Using MockStripe instead.")
    stripeInstance = new MockStripe() as unknown as Stripe
  } else {
    stripeInstance = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: "2025-01-27.acacia",
      appInfo: { name: "Optimizer", version: "0.1.0" }
    })
  }
} catch (error) {
  console.error("Failed to initialize Stripe:", error)
  stripeInstance = new MockStripe() as unknown as Stripe
}

export const stripe = stripeInstance
