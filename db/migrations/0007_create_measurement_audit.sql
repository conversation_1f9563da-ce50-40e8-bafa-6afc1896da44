-- Migration: 0007_create_measurement_audit.sql
-- Create measurement_audit table for tracking measurement changes

-- Create the table
CREATE TABLE IF NOT EXISTS "measurement_audit" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "measurement_id" uuid NOT NULL REFERENCES "measurements"("id") ON DELETE CASCADE,
  "action" text NOT NULL,
  "user_id" text NOT NULL,
  "old_data" jsonb,
  "new_data" jsonb,
  "changed_fields" jsonb,
  "reason" text,
  "user_agent" text,
  "ip_address" text,
  "created_at" timestamp DEFAULT now() NOT NULL
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS "measurement_audit_measurement_id_idx" ON "measurement_audit" ("measurement_id");
CREATE INDEX IF NOT EXISTS "measurement_audit_user_id_idx" ON "measurement_audit" ("user_id");
CREATE INDEX IF NOT EXISTS "measurement_audit_action_idx" ON "measurement_audit" ("action");
CREATE INDEX IF NOT EXISTS "measurement_audit_created_at_idx" ON "measurement_audit" ("created_at");

-- Add comments for documentation
COMMENT ON TABLE "measurement_audit" IS 'Audit trail for measurement CRUD operations';
COMMENT ON COLUMN "measurement_audit"."action" IS 'Type of operation: create, update, delete';
COMMENT ON COLUMN "measurement_audit"."old_data" IS 'Previous state of the measurement (null for create)';
COMMENT ON COLUMN "measurement_audit"."new_data" IS 'New state of the measurement (null for delete)';
COMMENT ON COLUMN "measurement_audit"."changed_fields" IS 'Array of field names that changed (for updates)';
