// actions/db/strategy-metrics-actions.ts
"use server"

import { db } from "@/db/db"
import { strategyMetricsTable, InsertStrategyMetrics, SelectStrategyMetrics } from "@/db/schema/strategy-metrics-schema"
import { optimizationsTable, measurementsTable } from "@/db/schema"
import { eq, desc } from "drizzle-orm"

export type ActionState<T = undefined> = {
  isSuccess: boolean
  message: string
  data?: T
}

/**
 * Get strategy metrics for an optimization
 */
export async function getStrategyMetricsAction(
  optimizationId: string
): Promise<ActionState<SelectStrategyMetrics>> {
  try {
    const metrics = await db
      .select()
      .from(strategyMetricsTable)
      .where(eq(strategyMetricsTable.optimizationId, optimizationId))
      .limit(1)

    if (metrics.length === 0) {
      return {
        isSuccess: false,
        message: "Strategy metrics not found"
      }
    }

    return {
      isSuccess: true,
      message: "Strategy metrics retrieved successfully",
      data: metrics[0]
    }
  } catch (error) {
    console.error("Error getting strategy metrics:", error)
    return {
      isSuccess: false,
      message: "Failed to get strategy metrics"
    }
  }
}

/**
 * Check if strategy metrics need to be updated
 */
export async function checkStrategyMetricsNeedUpdateAction(
  optimizationId: string
): Promise<ActionState<boolean>> {
  try {
    // Get the latest metrics
    const metrics = await db
      .select()
      .from(strategyMetricsTable)
      .where(eq(strategyMetricsTable.optimizationId, optimizationId))
      .limit(1)

    // If no metrics exist, they need to be created
    if (metrics.length === 0) {
      return {
        isSuccess: true,
        message: "Strategy metrics need to be created",
        data: true
      }
    }

    // Get the latest measurement
    const latestMeasurement = await db
      .select()
      .from(measurementsTable)
      .where(eq(measurementsTable.optimizationId, optimizationId))
      .orderBy(desc(measurementsTable.createdAt))
      .limit(1)

    // If no measurements exist, no update needed
    if (latestMeasurement.length === 0) {
      return {
        isSuccess: true,
        message: "No measurements exist, no update needed",
        data: false
      }
    }

    // Check if the latest measurement ID matches the last experiment ID in metrics
    const needsUpdate = !metrics[0].lastExperimentId ||
      metrics[0].lastExperimentId !== latestMeasurement[0].id

    return {
      isSuccess: true,
      message: needsUpdate
        ? "Strategy metrics need to be updated"
        : "Strategy metrics are up to date",
      data: needsUpdate
    }
  } catch (error) {
    console.error("Error checking if strategy metrics need update:", error)
    return {
      isSuccess: false,
      message: "Failed to check if strategy metrics need update",
      data: true // Default to updating if there's an error
    }
  }
}

/**
 * Create or update strategy metrics
 */
export async function upsertStrategyMetricsAction(
  metrics: Omit<InsertStrategyMetrics, "id" | "createdAt" | "updatedAt">
): Promise<ActionState<SelectStrategyMetrics>> {
  try {
    // Check if metrics already exist
    const existingMetrics = await db
      .select()
      .from(strategyMetricsTable)
      .where(eq(strategyMetricsTable.optimizationId, metrics.optimizationId))
      .limit(1)

    let result: SelectStrategyMetrics[]

    if (existingMetrics.length > 0) {
      // Update existing metrics
      result = await db
        .update(strategyMetricsTable)
        .set({
          ...metrics,
          updatedAt: new Date()
        })
        .where(eq(strategyMetricsTable.id, existingMetrics[0].id))
        .returning()
    } else {
      // Create new metrics
      result = await db
        .insert(strategyMetricsTable)
        .values(metrics)
        .returning()
    }

    if (result.length === 0) {
      return {
        isSuccess: false,
        message: "Failed to upsert strategy metrics"
      }
    }

    return {
      isSuccess: true,
      message: "Strategy metrics upserted successfully",
      data: result[0]
    }
  } catch (error) {
    console.error("Error upserting strategy metrics:", error)
    return {
      isSuccess: false,
      message: "Failed to upsert strategy metrics"
    }
  }
}

/**
 * Get the acquisition function type for an optimization
 */
export async function getAcquisitionFunctionTypeAction(
  optimizationId: string
): Promise<ActionState<string>> {
  try {
    const optimization = await db
      .select()
      .from(optimizationsTable)
      .where(eq(optimizationsTable.id, optimizationId))
      .limit(1)

    if (optimization.length === 0) {
      return {
        isSuccess: false,
        message: "Optimization not found"
      }
    }

    // Extract acquisition function from config
    const config = optimization[0].config as any
    const acquisitionFunction = config?.acquisition_function || "qUpperConfidenceBound"

    return {
      isSuccess: true,
      message: "Acquisition function retrieved successfully",
      data: acquisitionFunction
    }
  } catch (error) {
    console.error("Error getting acquisition function type:", error)
    return {
      isSuccess: false,
      message: "Failed to get acquisition function type",
      data: "qUpperConfidenceBound" // Default value
    }
  }
}
