/*
Utility functions for exporting data in various formats.
*/

import { saveAs } from "file-saver"
import * as XLSX from "xlsx"
import ExcelJS from "exceljs"

/**
 * Convert an array of objects to CSV format
 * @param data Array of objects to convert
 * @param headers Optional custom headers (if not provided, will use object keys)
 * @returns CSV string
 */
export function convertToCSV<T extends Record<string, any>>(
  data: T[],
  headers?: { key: keyof T; label: string }[]
): string {
  if (!data || data.length === 0) {
    return ""
  }

  // If headers are not provided, use the keys of the first object
  const keys = headers
    ? headers.map(h => h.key)
    : (Object.keys(data[0]) as (keyof T)[])
  const headerLabels = headers ? headers.map(h => h.label) : (keys as string[])

  // Create the CSV header row
  const headerRow = headerLabels.map(label => `"${label}"`).join(",")

  // Create the data rows
  const rows = data.map(item => {
    return keys
      .map(key => {
        // Handle different data types
        const value = item[key]
        if (value === null || value === undefined) {
          return '""'
        } else if (typeof value === "string") {
          // Escape quotes in strings
          return `"${value.replace(/"/g, '""')}"`
        } else if (
          value &&
          typeof value === "object" &&
          "toISOString" in value
        ) {
          return `"${value.toISOString()}"`
        } else {
          return `"${value}"`
        }
      })
      .join(",")
  })

  // Combine header and rows
  return [headerRow, ...rows].join("\n")
}

/**
 * Download data as a CSV file using file-saver with optional template formatting
 * @param data The data to download
 * @param filename The name of the file
 * @param headers Optional custom headers
 * @param optimizationConfig Optional optimization config for template-style formatting
 */
export function downloadCSV<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[],
  optimizationConfig?: any
): void {
  // Use template-style CSV generation if optimization config is provided
  let csv: string
  if (optimizationConfig) {
    csv = generateTemplateStyleCSV(data, optimizationConfig, headers)
  } else {
    // Fallback to basic CSV conversion
    csv = convertToCSV(data, headers)
  }

  // Create a Blob with the CSV data
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })

  // Use file-saver to save the file
  saveAs(blob, filename)
}

/**
 * Export data to Excel (.xlsx) format with template-style formatting
 * @param data The data to download
 * @param filename The name of the file
 * @param headers Optional custom headers
 * @param templateStyle Whether to use template-style formatting (default: true)
 * @param optimizationConfig Optional optimization config for advanced features
 */
export async function downloadExcel<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[],
  templateStyle: boolean = true,
  optimizationConfig?: any
): Promise<void> {
  // Make sure filename has .xlsx extension
  const excelFilename = filename.endsWith(".xlsx")
    ? filename
    : `${
        filename.endsWith(".csv")
          ? filename.substring(0, filename.lastIndexOf("."))
          : filename
      }.xlsx`

  if (templateStyle) {
    // Use template-style formatting with optimization config
    await downloadTemplateStyleExcel(
      data,
      excelFilename,
      headers,
      optimizationConfig
    )
  } else {
    // Use basic Excel export
    downloadBasicExcel(data, excelFilename, headers)
  }
}

/**
 * Export data to Excel-compatible CSV format with template-style structure
 * @param data The data to download
 * @param filename The name of the file
 * @param headers Optional custom headers
 * @param optimizationConfig Optional optimization config for template-style formatting
 */
export function downloadExcelCSV<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[],
  optimizationConfig?: any
): void {
  // Make sure filename has .csv extension
  const baseFilename =
    filename.endsWith(".xlsx") || filename.endsWith(".csv")
      ? filename.substring(0, filename.lastIndexOf("."))
      : filename

  const csvFilename = `${baseFilename}.csv`

  // Use template-style CSV generation if optimization config is provided
  let csv: string
  if (optimizationConfig) {
    csv = generateTemplateStyleCSV(data, optimizationConfig, headers)
  } else {
    // Fallback to basic CSV conversion
    csv = convertToCSV(data, headers)
  }

  // Add BOM (Byte Order Mark) for Excel compatibility with UTF-8
  const bom = new Uint8Array([0xef, 0xbb, 0xbf])

  // Use text/csv MIME type with UTF-8 encoding for better Excel compatibility
  const blob = new Blob([bom, csv], { type: "text/csv;charset=utf-8" })

  // Use file-saver to save the file
  saveAs(blob, csvFilename)
}

/**
 * Generate template-style CSV that matches Upload Dataset template format
 * @param data The data to export
 * @param optimizationConfig Optimization configuration
 * @param headers Optional custom headers
 */
function generateTemplateStyleCSV<T extends Record<string, any>>(
  data: T[],
  optimizationConfig: any,
  headers?: { key: keyof T; label: string }[]
): string {
  const parameters = optimizationConfig.parameters || []
  const targetConfigs = Array.isArray(optimizationConfig.target_config)
    ? optimizationConfig.target_config
    : optimizationConfig.target_config
      ? [optimizationConfig.target_config]
      : []

  // Create headers in template order: parameters first, then targets
  const templateHeaders = [
    ...parameters.map((p: any) => p.name.trim()),
    ...targetConfigs.map((t: any) => t.name.trim())
  ]

  console.log("[CSV_EXPORT] Template headers:", templateHeaders)

  if (data.length === 0) {
    // Return just headers if no data
    return templateHeaders.join(",")
  }

  // Convert data to template format
  const csvRows: string[] = []

  // Add header row
  csvRows.push(templateHeaders.join(","))

  // Add data rows in template column order
  data.forEach(item => {
    const row: string[] = []

    // Add parameter values in order
    parameters.forEach((param: any) => {
      const value = headers
        ? item[headers.find(h => h.label === param.name)?.key || param.name]
        : item[param.name]

      // Format value appropriately
      if (value === undefined || value === null || value === "") {
        row.push("")
      } else if (typeof value === "string" && value.includes(",")) {
        // Quote strings that contain commas
        row.push(`"${value}"`)
      } else {
        row.push(String(value))
      }
    })

    // Add target values in order
    targetConfigs.forEach((target: any) => {
      const value = headers
        ? item[headers.find(h => h.label === target.name)?.key || target.name]
        : item[target.name]

      // Format value appropriately
      if (value === undefined || value === null || value === "") {
        row.push("")
      } else if (typeof value === "number") {
        // Ensure numbers are properly formatted
        row.push(value.toString())
      } else if (typeof value === "string" && value.includes(",")) {
        // Quote strings that contain commas
        row.push(`"${value}"`)
      } else {
        row.push(String(value))
      }
    })

    csvRows.push(row.join(","))
  })

  console.log("[CSV_EXPORT] Generated", csvRows.length, "rows")
  return csvRows.join("\n")
}

/**
 * Export data to Excel with full template-style formatting and validation
 * @param data The data to download
 * @param filename The name of the file
 * @param headers Optional custom headers
 * @param optimizationConfig Optimization config for parameter/target info and validation
 */
export async function downloadTemplateStyleExcel<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[],
  optimizationConfig?: any
): Promise<void> {
  try {
    const workbook = new ExcelJS.Workbook()

    // Set workbook properties
    workbook.creator = "BOapp Optimization System"
    workbook.lastModifiedBy = "BOapp"
    workbook.created = new Date()
    workbook.modified = new Date()

    const worksheet = workbook.addWorksheet("Experiments")

    if (data.length === 0) {
      // Create empty template if no data
      const emptyBlob = new Blob([], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      })
      saveAs(emptyBlob, filename)
      return
    }

    // Get column names from data or headers
    const columnNames = headers
      ? headers.map(h => h.label)
      : Object.keys(data[0])

    // Get parameter and target configurations
    const parameters = optimizationConfig?.parameters || []
    const targetConfigs = Array.isArray(optimizationConfig?.target_config)
      ? optimizationConfig.target_config
      : optimizationConfig?.target_config
        ? [optimizationConfig.target_config]
        : []

    // Set up header row with styling
    const headerRow = worksheet.getRow(1)
    columnNames.forEach((columnName, index) => {
      const cell = headerRow.getCell(index + 1)
      cell.value = columnName

      // Header styling - blue for parameters, green for targets
      const isParameter = index < parameters.length
      cell.font = { bold: true, color: { argb: "FFFFFFFF" } }
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: isParameter ? "FF0066CC" : "FF009900" }
      }
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" }
      }
      cell.alignment = { horizontal: "center", vertical: "middle" }
    })

    // Auto-size columns
    columnNames.forEach((columnName, index) => {
      const column = worksheet.getColumn(index + 1)
      column.width = Math.max(columnName.length + 2, 12)
    })

    // Add advanced data validation and comments for parameters
    if (parameters.length > 0) {
      parameters.forEach((param: any, paramIndex: number) => {
        if (paramIndex >= columnNames.length) return

        const columnLetter = String.fromCharCode(65 + paramIndex) // A, B, C, etc.
        const dataRange = `${columnLetter}2:${columnLetter}1000` // Allow up to 1000 rows
        const headerCell = worksheet.getCell(`${columnLetter}1`)

        let comment = `Parameter: ${param.name}\nType: ${param.type}\n`

        if (param.type === "CategoricalParameter" && param.values) {
          comment += `Allowed values: ${param.values.join(", ")}`

          // Add dropdown data validation
          try {
            ;(worksheet as any).dataValidations.add(dataRange, {
              type: "list",
              allowBlank: false,
              formulae: [`"${param.values.join(",")}"`],
              showErrorMessage: true,
              errorStyle: "error",
              errorTitle: "Invalid Value",
              error: `Please select one of: ${param.values.join(", ")}`
            })
          } catch (error) {
            console.warn("Could not add categorical validation:", param.name)
          }
        } else if (param.type === "NumericalDiscrete" && param.values) {
          comment += `Allowed values: ${param.values.join(", ")}`

          // Add dropdown data validation for numerical discrete
          try {
            const numValues = param.values.map((v: any) => String(v))
            ;(worksheet as any).dataValidations.add(dataRange, {
              type: "list",
              allowBlank: false,
              formulae: [`"${numValues.join(",")}"`],
              showErrorMessage: true,
              errorStyle: "error",
              errorTitle: "Invalid Value",
              error: `Please select one of: ${numValues.join(", ")}`
            })
          } catch (error) {
            console.warn("Could not add discrete validation:", param.name)
          }
        } else if (param.type === "NumericalContinuous" && param.bounds) {
          const [min, max] = param.bounds
          comment += `Range: ${min} to ${max}`

          // Add range validation
          try {
            ;(worksheet as any).dataValidations.add(dataRange, {
              type: "decimal",
              operator: "between",
              allowBlank: false,
              formulae: [min, max],
              showErrorMessage: true,
              errorStyle: "error",
              errorTitle: "Value Out of Range",
              error: `Value must be between ${min} and ${max}`
            })
          } catch (error) {
            console.warn("Could not add range validation:", param.name)
          }
        }

        // Add comment to header cell
        try {
          headerCell.note = comment
        } catch (error) {
          console.warn("Could not add comment to header:", param.name)
        }
      })
    }

    // Add validation and comments for target columns
    if (targetConfigs.length > 0) {
      targetConfigs.forEach((target: any, targetIndex: number) => {
        const columnIndex = parameters.length + targetIndex
        if (columnIndex >= columnNames.length) return

        const columnLetter = String.fromCharCode(65 + columnIndex)
        const dataRange = `${columnLetter}2:${columnLetter}1000`
        const headerCell = worksheet.getCell(`${columnLetter}1`)

        let comment = `Target: ${target.name}\nObjective: ${target.mode === "MAX" ? "Maximize" : "Minimize"}\n`

        if (target.bounds) {
          const [min, max] = target.bounds
          comment += `Expected range: ${min} to ${max}`

          // Add range validation for targets with bounds
          try {
            ;(worksheet as any).dataValidations.add(dataRange, {
              type: "decimal",
              operator: "between",
              allowBlank: false,
              formulae: [min, max],
              showErrorMessage: true,
              errorStyle: "error",
              errorTitle: "Target Value Out of Range",
              error: `Target value must be between ${min} and ${max}`
            })
          } catch (error) {
            console.warn("Could not add target validation:", target.name)
          }
        } else {
          comment += "Enter numerical values"

          // Add general numerical validation
          try {
            ;(worksheet as any).dataValidations.add(dataRange, {
              type: "decimal",
              allowBlank: false,
              showErrorMessage: true,
              errorStyle: "error",
              errorTitle: "Invalid Target Value",
              error: "Please enter a valid number"
            })
          } catch (error) {
            console.warn("Could not add target validation:", target.name)
          }
        }

        // Add comment to header cell
        try {
          headerCell.note = comment
        } catch (error) {
          console.warn("Could not add comment to target header:", target.name)
        }
      })
    }

    // Add data rows
    data.forEach((item, rowIndex) => {
      const row = worksheet.getRow(rowIndex + 2)
      columnNames.forEach((columnName, colIndex) => {
        const cell = row.getCell(colIndex + 1)
        const value = headers ? item[headers[colIndex].key] : item[columnName]

        cell.value = value !== undefined ? value : ""

        // Style data cells
        cell.border = {
          top: { style: "thin", color: { argb: "FFE0E0E0" } },
          left: { style: "thin", color: { argb: "FFE0E0E0" } },
          bottom: { style: "thin", color: { argb: "FFE0E0E0" } },
          right: { style: "thin", color: { argb: "FFE0E0E0" } }
        }
      })
    })

    // Freeze the header row
    worksheet.views = [
      {
        state: "frozen",
        xSplit: 0,
        ySplit: 1,
        topLeftCell: "A2",
        activeCell: "A2"
      }
    ]

    // Add worksheet protection to prevent header modification
    try {
      worksheet.protect("", {
        selectLockedCells: false,
        selectUnlockedCells: true,
        formatCells: false,
        formatColumns: false,
        formatRows: false,
        insertColumns: false,
        insertRows: true,
        insertHyperlinks: false,
        deleteColumns: false,
        deleteRows: true,
        sort: false,
        autoFilter: false,
        pivotTables: false
      })

      // Lock header cells
      headerRow.eachCell(cell => {
        cell.protection = { locked: true }
      })

      // Unlock data cells (rows 2 and beyond)
      for (let rowNum = 2; rowNum <= 1000; rowNum++) {
        const row = worksheet.getRow(rowNum)
        columnNames.forEach((_, colIndex) => {
          const cell = row.getCell(colIndex + 1)
          cell.protection = { locked: false }
        })
      }
    } catch (error) {
      console.warn("Could not add worksheet protection:", error)
    }

    // Add comprehensive instructions worksheet
    const instructionsSheet = workbook.addWorksheet("Instructions")
    instructionsSheet.getCell("A1").value = "Excel Export Instructions"
    instructionsSheet.getCell("A1").font = { bold: true, size: 16 }

    const instructions = [
      "",
      "🔬 EXCEL EXPORT INSTRUCTIONS",
      "",
      "📋 HOW TO USE:",
      '1. Use the "Experiments" tab to view your exported data',
      "2. Each row represents one complete experiment",
      "3. Blue columns are optimization parameters (inputs)",
      "4. Green columns are target values (measured results)",
      "5. You can edit values and re-upload this file",
      "",
      "✨ ADVANCED FEATURES:",
      "• Dropdown menus for categorical parameters (click cell to see options)",
      "• Data validation prevents invalid entries with error messages",
      "• Range validation for numerical parameters",
      "• Header row is protected from accidental changes",
      "• Hover over headers to see detailed parameter information",
      "• Frozen header row stays visible when scrolling",
      "",
      "💾 RE-UPLOADING:",
      "• This file can be uploaded back to the optimization system",
      "• Use the Upload Dataset functionality",
      "• The system will validate your data before processing",
      "• Format is fully compatible with template structure",
      "",
      "📊 PARAMETER DETAILS:",
      ...parameters.map((p: any) => {
        let detail = `• ${p.name} (${p.type})`
        if (p.type === "CategoricalParameter" && p.values) {
          detail += `\n  └ Allowed values: ${p.values.join(", ")}`
        } else if (p.bounds) {
          detail += `\n  └ Valid range: ${p.bounds[0]} to ${p.bounds[1]}`
        } else if (p.values) {
          detail += `\n  └ Allowed values: ${p.values.join(", ")}`
        }
        return detail
      }),
      "",
      "🎯 TARGET DETAILS:",
      ...targetConfigs.map((t: any) => {
        let detail = `• ${t.name}: ${t.mode === "MAX" ? "MAXIMIZE" : "MINIMIZE"} this value`
        if (t.bounds) {
          detail += `\n  └ Expected range: ${t.bounds[0]} to ${t.bounds[1]}`
        }
        return detail
      }),
      "",
      "⚠️ IMPORTANT NOTES:",
      "• This file contains real experimental data",
      "• Values can be edited for re-upload",
      "• Use exact values for categorical parameters",
      "• Numerical values must be within specified ranges",
      "• Contact support if you encounter any issues"
    ]

    instructions.forEach((instruction, index) => {
      const cell = instructionsSheet.getCell(`A${index + 1}`)
      cell.value = instruction
      if (
        instruction.startsWith("📊 PARAMETER DETAILS:") ||
        instruction.startsWith("🎯 TARGET DETAILS:")
      ) {
        cell.font = { bold: true }
      }
    })

    instructionsSheet.getColumn("A").width = 80

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer()

    // Create Blob and save
    const blob = new Blob([buffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    })

    saveAs(blob, filename)
  } catch (error) {
    console.error("Error creating template-style Excel:", error)
    // Fallback to basic Excel export
    downloadBasicExcel(data, filename, headers)
  }
}

/**
 * Basic Excel export (original functionality)
 */
function downloadBasicExcel<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[]
): void {
  // Create a new workbook
  const workbook = XLSX.utils.book_new()

  // Process data for Excel
  let excelData: any[]

  if (headers) {
    // If headers are provided, use them to order and rename columns
    excelData = data.map(item => {
      const row: Record<string, any> = {}
      headers.forEach(header => {
        row[header.label] = item[header.key]
      })
      return row
    })
  } else {
    // Otherwise use the data as is
    excelData = data
  }

  // Convert data to worksheet
  const worksheet = XLSX.utils.json_to_sheet(excelData)

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data")

  // Generate Excel file as array buffer
  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" })

  // Create Blob from buffer
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  })

  // Use file-saver to save the file
  saveAs(blob, filename)
}
