"use server"

import { db } from "@/db/db"
import { userSurveysTable } from "@/db/schema"
import { eq } from "drizzle-orm"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { revalidatePath } from "next/cache"
import { AcademicSurveyResponses } from "./academic-verification-actions"

// Action to save academic survey responses
export async function saveAcademicSurveyAction(
  responses: AcademicSurveyResponses,
  completed: boolean = true
) {
  try {
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 ACADEMIC SURVEY - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "User not authenticated"
      }
    }

    // Check if a survey already exists for this user
    const existingSurvey = await db
      .select()
      .from(userSurveysTable)
      .where(eq(userSurveysTable.userId, userId))
      .limit(1)

    if (existingSurvey.length > 0) {
      // Update existing survey
      await db
        .update(userSurveysTable)
        .set({
          responses,
          completed: completed ? "true" : "false",
          surveyType: "academic",
          updatedAt: new Date()
        })
        .where(eq(userSurveysTable.userId, userId))
    } else {
      // Insert new survey
      await db.insert(userSurveysTable).values({
        userId,
        responses,
        completed: completed ? "true" : "false",
        surveyType: "academic"
      })
    }

    revalidatePath("/academic-survey")
    revalidatePath("/welcome")
    revalidatePath("/dashboard")

    return {
      isSuccess: true,
      message: "Academic survey responses saved successfully"
    }
  } catch (error) {
    console.error("Error saving academic survey responses:", error)
    return {
      isSuccess: false,
      message: "Failed to save academic survey responses"
    }
  }
}

// Action to check if a user has completed the academic survey
export async function checkAcademicSurveyCompletionAction() {
  try {
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 ACADEMIC SURVEY CHECK - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "User not authenticated",
        hasCompleted: false
      }
    }

    const survey = await db
      .select()
      .from(userSurveysTable)
      .where(eq(userSurveysTable.userId, userId))
      .limit(1)

    const hasCompleted =
      survey.length > 0 &&
      survey[0].completed === "true" &&
      survey[0].surveyType === "academic"

    return {
      isSuccess: true,
      message: "Academic survey completion status retrieved",
      hasCompleted
    }
  } catch (error) {
    console.error("Error checking academic survey completion:", error)
    return {
      isSuccess: false,
      message: "Failed to check academic survey completion",
      hasCompleted: false
    }
  }
}

// Action to get academic survey responses for a user
export async function getAcademicSurveyResponsesAction() {
  try {
    const authResult = await auth()
    const { userId: clerkUserId } = authResult

    // Get the actual user ID from session claims (our custom external ID)
    const userId = await getActualUserId()

    console.log("🔍 ACADEMIC SURVEY RESPONSES - Auth details:", {
      clerkUserId,
      customUserId: authResult.sessionClaims?.userId,
      actualUserId: userId
    })

    if (!userId) {
      return {
        isSuccess: false,
        message: "User not authenticated",
        responses: null
      }
    }

    const survey = await db
      .select()
      .from(userSurveysTable)
      .where(
        eq(userSurveysTable.userId, userId) &&
        eq(userSurveysTable.surveyType, "academic")
      )
      .limit(1)

    if (survey.length === 0) {
      return {
        isSuccess: true,
        message: "No academic survey responses found",
        responses: null
      }
    }

    return {
      isSuccess: true,
      message: "Academic survey responses retrieved",
      responses: survey[0].responses as AcademicSurveyResponses
    }
  } catch (error) {
    console.error("Error getting academic survey responses:", error)
    return {
      isSuccess: false,
      message: "Failed to get academic survey responses",
      responses: null
    }
  }
}
