/*
This server layout provides a restricted layout for academic signup that prevents navigation bypass.
*/

"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { checkAcademicVerificationStatusAction } from "@/actions/academic-verification-actions"
import AcademicSignupHeader from "./_components/academic-signup-header"
import { Footer } from "@/components/landing/footer"
import { AcademicSignupInterceptor } from "@/components/survey/navigation-interceptor"

export default async function AcademicSignupLayout({
  children
}: {
  children: React.ReactNode
}) {
  // Remove auth checks from layout - let the page handle authentication
  // This prevents redirect loops when users are in the middle of Clerk auth flow

  return (
    <div className="flex min-h-screen flex-col">
      <AcademicSignupInterceptor />
      <AcademicSignupHeader />
      <main className="flex-1">{children}</main>
      <Footer />
    </div>
  )
}
