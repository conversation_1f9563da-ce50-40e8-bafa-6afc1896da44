export enum AuditEventType {
  // Configuration Events
  CONFIGURATION_CREATED = "configuration_created",
  CONFIGURATION_UPDATED = "configuration_updated",
  CONFIGURATION_DELETED = "configuration_deleted",
  CONFIGURATION_PREVIEWED = "configuration_previewed",

  // Parameter Events
  PARAMETER_BOUNDS_CHANGED = "parameter_bounds_changed",
  PARAMETER_VALUES_CHANGED = "parameter_values_changed",
  PARAMETER_TYPE_CHANGED = "parameter_type_changed",
  PARAMETER_ADDED = "parameter_added",
  PARAMETER_REMOVED = "parameter_removed",

  // Target Events
  TARGET_BOUNDS_CHANGED = "target_bounds_changed",
  TARGET_WEIGHT_CHANGED = "target_weight_changed",
  TARGET_MODE_CHANGED = "target_mode_changed",
  TARGET_ADDED = "target_added",
  TARGET_REMOVED = "target_removed",

  // Constraint Events
  CONSTRAINT_ADDED = "constraint_added",
  CONSTRAINT_UPDATED = "constraint_updated",
  CONSTRAINT_REMOVED = "constraint_removed",
  CONSTRAINT_ENABLED = "constraint_enabled",
  CONSTRAINT_DISABLED = "constraint_disabled",

  // Measurement Events
  MEASUREMENTS_FILTERED = "measurements_filtered",
  MEASUREMENTS_IMPORTED = "measurements_imported",
  MEASUREMENTS_EXPORTED = "measurements_exported",

  // Campaign Events
  CAMPAIGN_RETRAINED = "campaign_retrained",
  CAMPAIGN_RESET = "campaign_reset",
  CAMPAIGN_PAUSED = "campaign_paused",
  CAMPAIGN_RESUMED = "campaign_resumed",

  // System Events
  VALIDATION_ERROR = "validation_error",
  SYSTEM_ERROR = "system_error",
  USER_ACTION = "user_action"
}

export enum AuditSeverity {
  INFO = "info",
  WARNING = "warning",
  ERROR = "error",
  CRITICAL = "critical"
}

export interface AuditContext {
  userId?: string
  sessionId?: string
  userAgent?: string
  ipAddress?: string
  timestamp: Date
  optimizationId: string
  campaignId?: string
}

export interface AuditChange {
  field: string
  oldValue: any
  newValue: any
  changeType: "create" | "update" | "delete"
}

export interface AuditMetadata {
  // Request/Response info
  requestId?: string
  responseTime?: number

  // User interaction info
  triggeredBy: "user" | "system" | "api"
  userInterface?: "web" | "api" | "cli"

  // Technical details
  version?: string
  environment?: string

  // Business context
  reason?: string
  impact?: {
    measurementsAffected?: number
    dataLoss?: boolean
    performanceImpact?: "low" | "medium" | "high"
  }

  // Error details (if applicable)
  errorCode?: string
  errorMessage?: string
  stackTrace?: string

  // Additional context
  tags?: string[]
  customData?: Record<string, any>
}

export interface AuditEvent {
  id: string
  eventType: AuditEventType
  severity: AuditSeverity
  title: string
  description: string
  context: AuditContext
  changes?: AuditChange[]
  metadata: AuditMetadata
  createdAt: Date
}

export interface AuditQuery {
  optimizationId?: string
  eventTypes?: AuditEventType[]
  severity?: AuditSeverity[]
  startDate?: Date
  endDate?: Date
  userId?: string
  limit?: number
  offset?: number
  search?: string
}

export interface AuditSummary {
  totalEvents: number
  eventsByType: Record<AuditEventType, number>
  eventsBySeverity: Record<AuditSeverity, number>
  recentActivity: AuditEvent[]
  criticalEvents: AuditEvent[]
  timeRange: {
    start: Date
    end: Date
  }
}

export interface ConfigurationAuditTrail {
  configurationId: string
  optimizationId: string
  events: AuditEvent[]
  summary: {
    totalChanges: number
    lastModified: Date
    lastModifiedBy?: string
    changeFrequency: "low" | "medium" | "high"
    riskLevel: "low" | "medium" | "high"
  }
}
