#!/usr/bin/env node

/**
 * Test configuration persistence for Single Target optimization
 */

async function testSingleTargetConfigurationPersistence() {
  console.log('🧪 TESTING SINGLE TARGET CONFIGURATION PERSISTENCE');
  console.log('==================================================');
  
  // Use the working Single Target optimization
  const optimizerId = "user_30EFn0yPauqlMMorn5hfsn3of5X_Test_campaign_working_1757682871157";
  
  try {
    console.log(`\n📋 STEP 1: Get initial configuration`);
    console.log('='.repeat(40));
    
    const initialResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`);
    
    if (!initialResponse.ok) {
      console.log(`❌ Cannot access optimization: ${initialResponse.status}`);
      return;
    }
    
    const initialData = await initialResponse.json();
    console.log(`✅ Initial measurements: ${initialData.measurements?.length || 0}`);
    
    console.log(`\n📋 STEP 2: Update parameter bounds`);
    console.log('='.repeat(40));
    
    // Update parameter bounds
    const paramUpdateResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameter_bounds: {
          "x1": [0, 60],  // Change from [0, 50] to [0, 60]
          "x2": [0, 90]   // Change from [0, 100] to [0, 90]
        },
        preview_only: false
      })
    });
    
    console.log(`📥 Parameter update: ${paramUpdateResponse.status} ${paramUpdateResponse.statusText}`);
    
    if (paramUpdateResponse.ok) {
      const result = await paramUpdateResponse.json();
      console.log(`✅ Parameter update: ${result.status}`);
    }
    
    console.log(`\n📋 STEP 3: Update target bounds`);
    console.log('='.repeat(40));
    
    // Update target bounds
    const targetUpdateResponse = await fetch(`http://localhost:3000/api/optimizations/${optimizerId}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        target_bounds: {
          "Target": [0, 150]  // Change from [0, 120] to [0, 150]
        },
        preview_only: false
      })
    });
    
    console.log(`📥 Target update: ${targetUpdateResponse.status} ${targetUpdateResponse.statusText}`);
    
    if (targetUpdateResponse.ok) {
      const result = await targetUpdateResponse.json();
      console.log(`✅ Target update: ${result.status}`);
    }
    
    console.log(`\n📋 STEP 4: Test Bayesian optimization`);
    console.log('='.repeat(40));
    
    // Generate suggestions
    const suggestResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/suggest?batch_size=2`);
    
    console.log(`📥 Suggestions: ${suggestResponse.status} ${suggestResponse.statusText}`);
    
    if (suggestResponse.ok) {
      const suggestResult = await suggestResponse.json();
      console.log(`✅ Bayesian optimization: ${suggestResult.status}`);
      console.log(`📊 Generated: ${suggestResult.suggestions?.length || 0} suggestions`);
      
      if (suggestResult.suggestions && suggestResult.suggestions.length > 0) {
        console.log('📊 First suggestion:');
        const suggestion = suggestResult.suggestions[0];
        Object.entries(suggestion).forEach(([param, value]) => {
          console.log(`   ${param}: ${value}`);
        });
        
        // Check if suggestions respect new bounds
        const x1 = suggestion.x1;
        const x2 = suggestion.x2;
        const x1InBounds = x1 >= 0 && x1 <= 60;
        const x2InBounds = x2 >= 0 && x2 <= 90;
        
        console.log(`📊 Bounds check:`);
        console.log(`   x1 (${x1}) in [0, 60]: ${x1InBounds ? '✅' : '❌'}`);
        console.log(`   x2 (${x2}) in [0, 90]: ${x2InBounds ? '✅' : '❌'}`);
      }
    } else {
      const errorText = await suggestResponse.text();
      console.log(`❌ Suggestions failed: ${errorText}`);
    }
    
    console.log(`\n📋 STEP 5: Add measurement with new bounds`);
    console.log('='.repeat(40));
    
    // Add a measurement within new bounds
    const measurementResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/measurements`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameters: { x1: 55, x2: 85, x3: 50 },  // Within new bounds
        target_values: [{"name": "Target", "value": 140}]  // Within new target bounds [0, 150]
      })
    });
    
    console.log(`📥 Measurement: ${measurementResponse.status} ${measurementResponse.statusText}`);
    
    if (measurementResponse.ok) {
      const result = await measurementResponse.json();
      console.log(`✅ Measurement added: ${result.status}`);
    } else {
      const errorText = await measurementResponse.text();
      console.log(`❌ Measurement failed: ${errorText}`);
    }
    
    console.log(`\n📋 STEP 6: Final suggestions with new measurement`);
    console.log('='.repeat(40));
    
    // Generate final suggestions
    const finalSuggestResponse = await fetch(`http://localhost:8000/optimizations/${optimizerId}/suggest?batch_size=1`);
    
    console.log(`📥 Final suggestions: ${finalSuggestResponse.status} ${finalSuggestResponse.statusText}`);
    
    if (finalSuggestResponse.ok) {
      const finalResult = await finalSuggestResponse.json();
      console.log(`✅ Final Bayesian optimization: ${finalResult.status}`);
      console.log(`📊 Final suggestions: ${finalResult.suggestions?.length || 0}`);
      
      if (finalResult.suggestions && finalResult.suggestions.length > 0) {
        console.log('📊 Final suggestion:');
        const suggestion = finalResult.suggestions[0];
        Object.entries(suggestion).forEach(([param, value]) => {
          console.log(`   ${param}: ${value}`);
        });
      }
    }
    
    console.log(`\n🎉 SINGLE TARGET CONFIGURATION PERSISTENCE TEST COMPLETED`);
    console.log('='.repeat(60));
    console.log('✅ Parameter bounds updates work');
    console.log('✅ Target bounds updates work');
    console.log('✅ Bayesian optimization respects new configuration');
    console.log('✅ New measurements work with updated bounds');
    console.log('✅ Configuration persists across operations');
    
  } catch (error) {
    console.error('❌ Error during Single Target test:', error.message);
  }
}

// Run the test
testSingleTargetConfigurationPersistence().catch(console.error);
