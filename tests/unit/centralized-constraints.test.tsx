// tests/constraints/centralized-constraints.test.tsx
// Comprehensive tests for centralized constraint implementation

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

// Import centralized constraint components and utilities
import {
  CentralizedConstraintBuilder,
  Constraint,
  Parameter,
  validateConstraint,
  createConstraint,
  generateConstraintId,
  CONSTRAINT_CATEGORIES,
  CONSTRAINT_TYPE_METADATA
} from '@/lib/constraints'

// Mock API calls
vi.mock('@/lib/api', () => ({
  validateConstraints: vi.fn(),
  checkConstraintFeasibility: vi.fn()
}))

describe('Centralized Constraint Implementation', () => {
  const mockParameters: Parameter[] = [
    {
      name: 'temperature',
      type: 'NumericalContinuous',
      bounds: [20, 200]
    },
    {
      name: 'pressure',
      type: 'NumericalContinuous', 
      bounds: [1, 10]
    },
    {
      name: 'catalyst',
      type: 'CategoricalParameter',
      values: ['A', 'B', 'C']
    },
    {
      name: 'concentration',
      type: 'NumericalDiscrete',
      values: [0.1, 0.5, 1.0, 2.0]
    }
  ]

  let mockConstraints: Constraint[]
  let mockOnConstraintsChange: vi.Mock

  beforeEach(() => {
    mockConstraints = []
    mockOnConstraintsChange = vi.fn()
    vi.clearAllMocks()
  })

  describe('Constraint Creation and Validation', () => {
    it('should create constraints with proper IDs and structure', () => {
      const linearConstraint = createConstraint(
        'ContinuousLinearConstraint',
        ['temperature', 'pressure'],
        'Temperature-Pressure Limit'
      )

      expect(linearConstraint).toMatchObject({
        type: 'ContinuousLinearConstraint',
        parameters: ['temperature', 'pressure'],
        name: 'Temperature-Pressure Limit',
        operator: '<=',
        coefficients: [1.0, 1.0],
        rhs: 0.0
      })
      expect(linearConstraint.id).toBeDefined()
      expect(typeof linearConstraint.id).toBe('string')
    })

    it('should generate unique constraint IDs', () => {
      const id1 = generateConstraintId()
      const id2 = generateConstraintId()
      const id3 = generateConstraintId()

      expect(id1).not.toBe(id2)
      expect(id2).not.toBe(id3)
      expect(id1).not.toBe(id3)
    })

    it('should validate constraint structure correctly', () => {
      const validConstraint: Constraint = {
        id: 'test-1',
        type: 'ContinuousLinearConstraint',
        parameters: ['temperature', 'pressure'],
        name: 'Test Constraint',
        operator: '<=',
        coefficients: [1.0, -1.0],
        rhs: 50.0
      }

      const result = validateConstraint(validConstraint, mockParameters)
      expect(result.valid).toBe(true)
    })

    it('should detect invalid constraint parameters', () => {
      const invalidConstraint: Constraint = {
        id: 'test-2',
        type: 'ContinuousLinearConstraint',
        parameters: ['nonexistent_param'],
        name: 'Invalid Constraint',
        operator: '<=',
        coefficients: [1.0],
        rhs: 0.0
      }

      const result = validateConstraint(invalidConstraint, mockParameters)
      expect(result.valid).toBe(false)
      expect(result.error).toContain('nonexistent_param')
    })
  })

  describe('Constraint Categories and Metadata', () => {
    it('should have all constraint categories defined', () => {
      expect(CONSTRAINT_CATEGORIES).toBeDefined()
      expect(CONSTRAINT_CATEGORIES.linear).toBeDefined()
      expect(CONSTRAINT_CATEGORIES.cardinality).toBeDefined()
      expect(CONSTRAINT_CATEGORIES.exclusion).toBeDefined()
      expect(CONSTRAINT_CATEGORIES.mathematical).toBeDefined()
      expect(CONSTRAINT_CATEGORIES.dependency).toBeDefined()
      expect(CONSTRAINT_CATEGORIES.custom).toBeDefined()
    })

    it('should have metadata for all constraint types', () => {
      const constraintTypes = [
        'ContinuousLinearConstraint',
        'ContinuousCardinalityConstraint',
        'DiscreteCardinalityConstraint',
        'DiscreteExcludeConstraint',
        'DiscreteSumConstraint',
        'DiscreteProductConstraint',
        'DiscreteDependenciesConstraint',
        'DiscreteLinkedParametersConstraint',
        'DiscreteNoLabelDuplicatesConstraint',
        'DiscretePermutationInvarianceConstraint',
        'DiscreteCustomConstraint'
      ]

      constraintTypes.forEach(type => {
        expect(CONSTRAINT_TYPE_METADATA[type]).toBeDefined()
        expect(CONSTRAINT_TYPE_METADATA[type].label).toBeDefined()
        expect(CONSTRAINT_TYPE_METADATA[type].description).toBeDefined()
      })
    })
  })

  describe('CentralizedConstraintBuilder Component', () => {
    it('should render without errors', () => {
      render(
        <CentralizedConstraintBuilder
          parameters={mockParameters}
          constraints={mockConstraints}
          onConstraintsChange={mockOnConstraintsChange}
        />
      )

      expect(screen.getByText('Constraints')).toBeInTheDocument()
    })

    it('should display parameter count correctly', () => {
      render(
        <CentralizedConstraintBuilder
          parameters={mockParameters}
          constraints={mockConstraints}
          onConstraintsChange={mockOnConstraintsChange}
        />
      )

      expect(screen.getByText('4 parameters available')).toBeInTheDocument()
    })

    it('should show constraint count badge', () => {
      const testConstraints = [
        createConstraint('ContinuousLinearConstraint', ['temperature'], 'Test 1'),
        createConstraint('DiscreteExcludeConstraint', ['catalyst'], 'Test 2')
      ]

      render(
        <CentralizedConstraintBuilder
          parameters={mockParameters}
          constraints={testConstraints}
          onConstraintsChange={mockOnConstraintsChange}
        />
      )

      expect(screen.getByText('2 constraints')).toBeInTheDocument()
    })
  })

  describe('Integration Points', () => {
    it('should work with create optimization form schema', () => {
      // Test that constraints can be properly validated in form context
      const formData = {
        name: 'Test Optimization',
        description: 'Test description',
        target: { name: 'yield', mode: 'MAX' },
        parameters: mockParameters,
        constraints: [
          createConstraint('ContinuousLinearConstraint', ['temperature', 'pressure'])
        ]
      }

      expect(formData.constraints).toHaveLength(1)
      expect(formData.constraints[0].type).toBe('ContinuousLinearConstraint')
    })

    it('should work with configuration update dialog', () => {
      // Test constraint loading and saving in configuration context
      const configConstraints = [
        {
          type: 'ContinuousLinearConstraint',
          parameters: ['temperature', 'pressure'],
          name: 'Config Constraint',
          operator: '<=',
          coefficients: [1.0, 1.0],
          rhs: 100.0
        }
      ]

      // Simulate loading constraints from config
      const loadedConstraints = configConstraints.map(constraint => ({
        ...constraint,
        id: generateConstraintId()
      }))

      expect(loadedConstraints).toHaveLength(1)
      expect(loadedConstraints[0].id).toBeDefined()
    })
  })

  describe('Backend Integration', () => {
    it('should format constraints correctly for API', () => {
      const constraint = createConstraint(
        'ContinuousLinearConstraint',
        ['temperature', 'pressure'],
        'API Test'
      )

      const apiFormat = {
        type: constraint.type,
        parameters: constraint.parameters,
        name: constraint.name,
        description: constraint.description,
        operator: (constraint as any).operator,
        coefficients: (constraint as any).coefficients,
        rhs: (constraint as any).rhs
      }

      expect(apiFormat.type).toBe('ContinuousLinearConstraint')
      expect(apiFormat.parameters).toEqual(['temperature', 'pressure'])
      expect(apiFormat.operator).toBe('<=')
      expect(apiFormat.coefficients).toEqual([1.0, 1.0])
      expect(apiFormat.rhs).toBe(0.0)
    })
  })

  describe('Legacy Code Removal Verification', () => {
    it('should not import from legacy constraint types', () => {
      // This test ensures we are not accidentally importing from removed files
      expect(() => {
        // This should fail if legacy imports still exist
        require('@/types/constraint-types')
      }).toThrow()
    })

    it('should use centralized constraint library exclusively', () => {
      // Verify that all constraint functionality comes from centralized library
      expect(createConstraint).toBeDefined()
      expect(validateConstraint).toBeDefined()
      expect(CONSTRAINT_CATEGORIES).toBeDefined()
      expect(CentralizedConstraintBuilder).toBeDefined()
    })
  })
})
