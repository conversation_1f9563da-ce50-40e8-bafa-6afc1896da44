// lib/constraints/types.ts
// Centralized Constraint Types and Interfaces

// Base constraint condition types
export interface ConstraintCondition {
  type: "threshold" | "subselection"
  threshold?: number
  operator?: ">=" | "<=" | ">" | "<" | "=" | "=="
  tolerance?: number
  selection?: (string | number)[]
}

// Base constraint interface
export interface BaseConstraint {
  id?: string
  type: string
  parameters: string[]
  name?: string
  description?: string
}

// Continuous constraint types
export interface ContinuousLinearConstraint extends BaseConstraint {
  type: "ContinuousLinearConstraint"
  operator: ">=" | "<=" | "=" | ">" | "<"
  coefficients: number[]
  rhs: number
}

export interface ContinuousCardinalityConstraint extends BaseConstraint {
  type: "ContinuousCardinalityConstraint"
  min_cardinality?: number
  max_cardinality?: number
  relative_threshold?: number
}

// Discrete constraint types
export interface DiscreteLinearConstraint extends BaseConstraint {
  type: "DiscreteLinearConstraint"
  operator: ">=" | "<=" | "=" | ">" | "<"
  coefficients: number[]
  rhs: number
  tolerance?: number // For equality constraints on discrete values
}

export interface DiscreteCardinalityConstraint extends BaseConstraint {
  type: "DiscreteCardinalityConstraint"
  min_cardinality?: number
  max_cardinality?: number
}

export interface DiscreteExcludeConstraint extends BaseConstraint {
  type: "DiscreteExcludeConstraint"
  combiner: "AND" | "OR"
  conditions: ConstraintCondition[]
}

export interface DiscreteSumConstraint extends BaseConstraint {
  type: "DiscreteSumConstraint"
  condition: ConstraintCondition
}

export interface DiscreteProductConstraint extends BaseConstraint {
  type: "DiscreteProductConstraint"
  condition: ConstraintCondition
}

export interface DiscreteDependenciesConstraint extends BaseConstraint {
  type: "DiscreteDependenciesConstraint"
  affected_parameters: string[][]
}

export interface DiscreteLinkedParametersConstraint extends BaseConstraint {
  type: "DiscreteLinkedParametersConstraint"
  dependencies: Record<string, any>
}

export interface DiscreteNoLabelDuplicatesConstraint extends BaseConstraint {
  type: "DiscreteNoLabelDuplicatesConstraint"
}

export interface DiscretePermutationInvarianceConstraint
  extends BaseConstraint {
  type: "DiscretePermutationInvarianceConstraint"
  dependencies: Record<string, any>
}

export interface DiscreteCustomConstraint extends BaseConstraint {
  type: "DiscreteCustomConstraint"
  validator: string
}

// Union type for all constraints
export type Constraint =
  | ContinuousLinearConstraint
  | ContinuousCardinalityConstraint
  | DiscreteLinearConstraint
  | DiscreteCardinalityConstraint
  | DiscreteExcludeConstraint
  | DiscreteSumConstraint
  | DiscreteProductConstraint
  | DiscreteDependenciesConstraint
  | DiscreteLinkedParametersConstraint
  | DiscreteNoLabelDuplicatesConstraint
  | DiscretePermutationInvarianceConstraint
  | DiscreteCustomConstraint

// Parameter types for constraint validation
export interface Parameter {
  name: string
  type: "NumericalDiscrete" | "NumericalContinuous" | "CategoricalParameter"
  values?: number[] | string[]
  bounds?: [number, number]
  tolerance?: number
  encoding?: "OHE" | "LE"
}

// Constraint validation result types
export interface ValidationResult {
  valid: boolean
  error?: string
  message?: string
}

export interface ConstraintValidationResult extends ValidationResult {
  constraintId: string
}

export interface GlobalValidationResult {
  valid: boolean
  errors: ConstraintValidationResult[]
  warnings?: string[]
}

// Constraint template types
export interface ConstraintTemplate {
  id: string
  name: string
  description: string
  category: string
  constraint: Partial<Constraint>
  parameterPlaceholders: string[]
}

// Constraint category definitions
export interface ConstraintCategory {
  name: string
  description: string
  icon: string
  color: string
}

// Sampling-related types
export interface SamplingOptions {
  strategy: "LHS" | "random" | "sobol"
  nSamples: number
  seed?: number
  respectConstraints?: boolean
  maxAttempts?: number
  tolerance?: number
}

export interface SamplingResult {
  samples: Record<string, any>[]
  constraintViolations?: number
  feasibleSamples?: number
  totalAttempts?: number
  discretizationTransparency?: {
    has_discretization: boolean
    discrete_parameters: Array<{
      parameter: string
      type: string
      allowed_values: number[]
      original_values: number[]
      discretized_values: number[]
      has_changes: boolean
    }>
    sample_transparency: Array<{
      sample_index: number
      parameter_changes: Record<
        string,
        {
          original: number
          discretized: number
          allowed_values: number[]
        }
      >
    }>
  }
}

// Constraint application context
export interface ConstraintContext {
  parameters: Parameter[]
  constraints: Constraint[]
  validationEnabled?: boolean
  samplingEnabled?: boolean
}

// Enhanced validation result types for real-time validation
export interface EnhancedValidationResult extends ValidationResult {
  warnings: ConstraintWarning[]
  suggestions: ConstraintSuggestion[]
  feasibilityScore: number
  interactionIssues: ConstraintInteraction[]
  performanceMetrics: {
    validationTime: number
    memoryUsage?: number
  }
}

export interface ConstraintWarning {
  id: string
  type: "performance" | "redundancy" | "compatibility" | "optimization"
  message: string
  impact: "low" | "medium" | "high"
  recommendation: string
  affectedParameters?: string[]
}

export interface ConstraintSuggestion {
  id: string
  type: "optimization" | "simplification" | "alternative" | "enhancement"
  title: string
  description: string
  beforeCode: string
  afterCode: string
  benefits: string[]
  risks: string[]
  confidence: number
}

export interface ConstraintInteraction {
  id: string
  type: "conflict" | "redundancy" | "subsumption" | "dependency"
  severity: "critical" | "major" | "minor"
  description: string
  involvedConstraints: string[]
  resolution: {
    strategy:
      | "remove_redundant"
      | "merge_constraints"
      | "modify_constraint"
      | "add_dependency"
    steps: string[]
    estimatedImpact: string
  }
}

// Real-time validation configuration
export interface RealTimeValidationConfig {
  enabled: boolean
  debounceMs: number
  validateOnType: boolean
  validateOnBlur: boolean
  showInlineErrors: boolean
  showSuggestions: boolean
  enableFeasibilityCheck: boolean
  feasibilitySampleSize: number
  enableInteractionDetection: boolean
  maxValidationTime: number
}

// Constraint syntax and semantic rules
export interface ConstraintSyntaxRule {
  id: string
  name: string
  pattern: RegExp
  errorMessage: string
  severity: "error" | "warning"
  category: "syntax" | "semantic" | "style"
  autoFix?: {
    enabled: boolean
    replacement: string
    description: string
  }
}

// Constraint auto-completion
export interface ConstraintAutoComplete {
  suggestions: Array<{
    text: string
    displayText: string
    type: "parameter" | "operator" | "function" | "constant"
    description: string
    insertText: string
    priority: number
  }>
  context: {
    currentPosition: number
    currentWord: string
    availableParameters: string[]
    availableFunctions: string[]
  }
}
