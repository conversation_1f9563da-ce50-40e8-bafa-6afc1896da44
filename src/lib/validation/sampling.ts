// lib/constraints/sampling.ts
// Constraint-aware sampling utilities

import { Constraint, Parameter, SamplingOptions, SamplingResult } from "./types"

/**
 * Generate constraint-aware samples
 */
export async function generateConstraintAwareSamples(
  parameters: Parameter[],
  constraints: Constraint[],
  options: SamplingOptions
): Promise<SamplingResult> {
  const {
    strategy = "LHS",
    nSamples,
    seed,
    respectConstraints = true,
    maxAttempts = 1000,
    tolerance = 1e-6
  } = options

  if (!respectConstraints || constraints.length === 0) {
    // Generate samples without constraint consideration
    return await generateStandardSamples(parameters, options)
  }

  // Generate constraint-aware samples using rejection sampling
  return await generateSamplesWithRejection(parameters, constraints, options)
}

/**
 * Generate standard samples without constraint consideration
 */
async function generateStandardSamples(
  parameters: Parameter[],
  options: SamplingOptions
): Promise<SamplingResult> {
  try {
    // Call the constraint-aware sampling API
    const response = await fetch("/api/proxy/sampling/generate", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        parameters: parameters.map(convertParameterForAPI),
        n_samples: options.nSamples,
        sampling_strategy: options.strategy,
        seed: options.seed,
        constraints: [],
        respect_constraints: false,
        max_attempts: options.maxAttempts || 1000,
        tolerance: options.tolerance || 1e-6
      })
    })

    if (!response.ok) {
      throw new Error(`Sampling request failed: ${response.statusText}`)
    }

    const result = await response.json()

    return {
      samples: result.samples || [],
      feasibleSamples: result.samples?.length || 0,
      totalAttempts: 1
    }
  } catch (error) {
    console.error("Standard sampling error:", error)
    return {
      samples: [],
      constraintViolations: 0,
      feasibleSamples: 0,
      totalAttempts: 1
    }
  }
}

/**
 * Generate samples with constraint rejection sampling
 */
async function generateSamplesWithRejection(
  parameters: Parameter[],
  constraints: Constraint[],
  options: SamplingOptions
): Promise<SamplingResult> {
  try {
    // Call the constraint-aware sampling API directly
    const response = await fetch("/api/proxy/sampling/generate", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        parameters: parameters.map(convertParameterForAPI),
        n_samples: options.nSamples,
        sampling_strategy: options.strategy,
        seed: options.seed,
        constraints: constraints.map(convertConstraintForAPI),
        respect_constraints: true,
        max_attempts: options.maxAttempts || 1000,
        tolerance: options.tolerance || 1e-6
      })
    })

    if (!response.ok) {
      throw new Error(
        `Constraint-aware sampling request failed: ${response.statusText}`
      )
    }

    const result = await response.json()

    return {
      samples: result.samples || [],
      constraintViolations: result.constraint_violations || 0,
      feasibleSamples: result.feasible_samples || 0,
      totalAttempts: result.total_attempts || 1,
      discretizationTransparency: result.discretization_transparency
    }
  } catch (error) {
    console.error("Constraint-aware sampling error:", error)
    return {
      samples: [],
      constraintViolations: 0,
      feasibleSamples: 0,
      totalAttempts: 1
    }
  }
}

/**
 * Validate a sample against all constraints
 */
async function validateSampleAgainstConstraints(
  sample: Record<string, any>,
  constraints: Constraint[],
  parameters: Parameter[],
  tolerance: number = 1e-6
): Promise<boolean> {
  for (const constraint of constraints) {
    const isValid = await validateSampleAgainstConstraint(
      sample,
      constraint,
      parameters,
      tolerance
    )
    if (!isValid) {
      return false
    }
  }
  return true
}

/**
 * Validate a sample against a single constraint
 */
async function validateSampleAgainstConstraint(
  sample: Record<string, any>,
  constraint: Constraint,
  parameters: Parameter[],
  tolerance: number = 1e-6
): Promise<boolean> {
  try {
    switch (constraint.type) {
      case "ContinuousLinearConstraint":
        return validateLinearConstraintSample(sample, constraint, tolerance)

      case "DiscreteLinearConstraint":
        return validateDiscreteLinearConstraintSample(
          sample,
          constraint,
          tolerance
        )

      case "ContinuousCardinalityConstraint":
      case "DiscreteCardinalityConstraint":
        return validateCardinalityConstraintSample(
          sample,
          constraint,
          parameters,
          tolerance
        )

      case "DiscreteExcludeConstraint":
        return validateExcludeConstraintSample(sample, constraint, parameters)

      case "DiscreteSumConstraint":
        return validateSumConstraintSample(sample, constraint, tolerance)

      case "DiscreteProductConstraint":
        return validateProductConstraintSample(sample, constraint, tolerance)

      case "DiscreteNoLabelDuplicatesConstraint":
        return validateNoDuplicatesConstraintSample(sample, constraint)

      default:
        // For complex constraints, use backend validation
        return await validateConstraintSampleWithBackend(
          sample,
          constraint,
          parameters
        )
    }
  } catch (error) {
    console.error(`Error validating constraint ${constraint.type}:`, error)
    return false
  }
}

/**
 * Validate linear constraint for a sample
 */
function validateLinearConstraintSample(
  sample: Record<string, any>,
  constraint: Constraint & { type: "ContinuousLinearConstraint" },
  tolerance: number
): boolean {
  const { parameters, coefficients, operator, rhs } = constraint

  if (!coefficients || coefficients.length !== parameters.length) {
    return false
  }

  // Calculate left-hand side value
  let lhsValue = 0
  for (let i = 0; i < parameters.length; i++) {
    const paramValue = sample[parameters[i]]
    if (paramValue === undefined || paramValue === null) {
      return false
    }
    lhsValue += coefficients[i] * paramValue
  }

  // Check constraint satisfaction
  switch (operator) {
    case "<=":
      return lhsValue <= rhs + tolerance
    case "<":
      return lhsValue < rhs + tolerance
    case ">=":
      return lhsValue >= rhs - tolerance
    case ">":
      return lhsValue > rhs - tolerance
    case "=":
      return Math.abs(lhsValue - rhs) <= tolerance
    default:
      return false
  }
}

/**
 * Validate discrete linear constraint for a sample
 */
function validateDiscreteLinearConstraintSample(
  sample: Record<string, any>,
  constraint: Constraint & { type: "DiscreteLinearConstraint" },
  tolerance: number
): boolean {
  const { parameters, coefficients, operator, rhs } = constraint
  const constraintTolerance = constraint.tolerance ?? tolerance

  if (!coefficients || coefficients.length !== parameters.length) {
    return false
  }

  // Calculate left-hand side value
  let lhsValue = 0
  for (let i = 0; i < parameters.length; i++) {
    const paramValue = sample[parameters[i]]
    if (paramValue === undefined || paramValue === null) {
      return false
    }
    lhsValue += coefficients[i] * paramValue
  }

  // Check constraint satisfaction
  switch (operator) {
    case "<=":
      return lhsValue <= rhs + constraintTolerance
    case "<":
      return lhsValue < rhs + constraintTolerance
    case ">=":
      return lhsValue >= rhs - constraintTolerance
    case ">":
      return lhsValue > rhs - constraintTolerance
    case "=":
      return Math.abs(lhsValue - rhs) <= constraintTolerance
    default:
      return false
  }
}

/**
 * Validate cardinality constraint for a sample
 */
function validateCardinalityConstraintSample(
  sample: Record<string, any>,
  constraint: Constraint & {
    type: "ContinuousCardinalityConstraint" | "DiscreteCardinalityConstraint"
  },
  parameters: Parameter[],
  tolerance: number
): boolean {
  const {
    parameters: constraintParams,
    min_cardinality,
    max_cardinality
  } = constraint

  // Count active parameters (non-zero values for continuous, any value for discrete)
  let activeCount = 0
  for (const paramName of constraintParams) {
    const value = sample[paramName]
    if (value !== undefined && value !== null) {
      const param = parameters.find(p => p.name === paramName)
      if (param?.type === "NumericalContinuous") {
        // For continuous parameters, consider non-zero as active
        if (Math.abs(value) > tolerance) {
          activeCount++
        }
      } else {
        // For discrete parameters, any value is active
        activeCount++
      }
    }
  }

  // Check cardinality bounds
  if (min_cardinality !== undefined && activeCount < min_cardinality) {
    return false
  }
  if (max_cardinality !== undefined && activeCount > max_cardinality) {
    return false
  }

  return true
}

/**
 * Validate exclude constraint for a sample
 */
function validateExcludeConstraintSample(
  sample: Record<string, any>,
  constraint: Constraint & { type: "DiscreteExcludeConstraint" },
  parameters: Parameter[]
): boolean {
  const { conditions, combiner } = constraint

  const conditionResults = conditions.map(condition => {
    // This is a simplified implementation
    // More complex condition evaluation would be needed for full support
    return false // Placeholder - would need proper condition evaluation
  })

  if (combiner === "AND") {
    return !conditionResults.every(result => result) // Exclude if all conditions are true
  } else {
    return !conditionResults.some(result => result) // Exclude if any condition is true
  }
}

/**
 * Validate sum constraint for a sample
 */
function validateSumConstraintSample(
  sample: Record<string, any>,
  constraint: Constraint & { type: "DiscreteSumConstraint" },
  tolerance: number
): boolean {
  const { parameters, condition } = constraint

  const sum = parameters.reduce((total, paramName) => {
    const value = sample[paramName]
    return total + (typeof value === "number" ? value : 0)
  }, 0)

  if (condition.type === "threshold" && condition.threshold !== undefined) {
    switch (condition.operator) {
      case "<=":
        return sum <= condition.threshold + tolerance
      case "<":
        return sum < condition.threshold + tolerance
      case ">=":
        return sum >= condition.threshold - tolerance
      case ">":
        return sum > condition.threshold - tolerance
      case "=":
      case "==":
        return Math.abs(sum - condition.threshold) <= tolerance
      default:
        return false
    }
  }

  return true
}

/**
 * Validate product constraint for a sample
 */
function validateProductConstraintSample(
  sample: Record<string, any>,
  constraint: Constraint & { type: "DiscreteProductConstraint" },
  tolerance: number
): boolean {
  const { parameters, condition } = constraint

  const product = parameters.reduce((total, paramName) => {
    const value = sample[paramName]
    return total * (typeof value === "number" ? value : 1)
  }, 1)

  if (condition.type === "threshold" && condition.threshold !== undefined) {
    switch (condition.operator) {
      case "<=":
        return product <= condition.threshold + tolerance
      case "<":
        return product < condition.threshold + tolerance
      case ">=":
        return product >= condition.threshold - tolerance
      case ">":
        return product > condition.threshold - tolerance
      case "=":
      case "==":
        return Math.abs(product - condition.threshold) <= tolerance
      default:
        return false
    }
  }

  return true
}

/**
 * Validate no duplicates constraint for a sample
 */
function validateNoDuplicatesConstraintSample(
  sample: Record<string, any>,
  constraint: Constraint & { type: "DiscreteNoLabelDuplicatesConstraint" }
): boolean {
  const { parameters } = constraint
  const values = parameters.map(paramName => sample[paramName])
  const uniqueValues = new Set(
    values.filter(v => v !== undefined && v !== null)
  )

  return (
    uniqueValues.size ===
    values.filter(v => v !== undefined && v !== null).length
  )
}

/**
 * Validate constraint sample with backend
 */
async function validateConstraintSampleWithBackend(
  sample: Record<string, any>,
  constraint: Constraint,
  parameters: Parameter[]
): Promise<boolean> {
  try {
    // This would call a backend endpoint for complex constraint validation
    // For now, return true as a placeholder
    return true
  } catch (error) {
    console.error("Backend constraint validation error:", error)
    return false
  }
}

/**
 * Convert parameter for API compatibility
 */
function convertParameterForAPI(parameter: Parameter): any {
  return {
    name: parameter.name,
    type: parameter.type,
    values: parameter.values,
    bounds: parameter.bounds,
    tolerance: parameter.tolerance,
    encoding: parameter.encoding
  }
}

/**
 * Convert constraint for API compatibility
 */
function convertConstraintForAPI(constraint: Constraint): any {
  return {
    ...constraint // Include all constraint fields
  }
}

/**
 * Estimate constraint feasibility
 */
export async function estimateConstraintFeasibility(
  parameters: Parameter[],
  constraints: Constraint[],
  sampleSize: number = 100
): Promise<{ feasibilityRatio: number; estimatedEfficiency: number }> {
  if (constraints.length === 0) {
    return { feasibilityRatio: 1.0, estimatedEfficiency: 1.0 }
  }

  try {
    const testResult = await generateConstraintAwareSamples(
      parameters,
      constraints,
      {
        strategy: "random",
        nSamples: Math.min(sampleSize, 10), // Small test sample
        respectConstraints: true,
        maxAttempts: sampleSize * 2
      }
    )

    const feasibilityRatio = testResult.totalAttempts
      ? testResult.feasibleSamples! / testResult.totalAttempts
      : 0

    const estimatedEfficiency =
      feasibilityRatio > 0.1
        ? Math.min(feasibilityRatio * 2, 1.0)
        : feasibilityRatio

    return { feasibilityRatio, estimatedEfficiency }
  } catch (error) {
    console.error("Error estimating constraint feasibility:", error)
    return { feasibilityRatio: 0.5, estimatedEfficiency: 0.5 } // Conservative estimate
  }
}
