// components/optimization/create-optimization-form.tsx
"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Trash, Plus, Beaker, Target } from "lucide-react"
import { createOptimizationWorkflowAction } from "@/actions/optimization-workflow-actions"
import { toast } from "@/components/ui/use-toast"
import { OptimizationConfig, Parameter, TargetConfig } from "@/types"

// Import centralized constraints
import { Constraint, constraintSchema } from "@/lib/constraints"
import { CentralizedConstraintBuilder } from "@/components/constraints/centralized-constraint-builder"

// Schema for the form validation
const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().optional(),
  target: z.object({
    name: z.string().min(1, "Target name is required"),
    mode: z.enum(["MAX", "MIN"])
  }),
  parameters: z
    .array(
      z
        .object({
          name: z.string().min(1, "Parameter name is required"),
          type: z.enum([
            "NumericalDiscrete",
            "NumericalContinuous",
            "CategoricalParameter"
          ]),
          values: z.string().optional(),
          encoding: z.enum(["OHE", "LE"]).optional(),
          tolerance: z.string().optional(),
          bounds: z.string().optional()
        })
        .refine(
          data => {
            // For NumericalDiscrete and CategoricalParameter, values is required
            if (
              (data.type === "NumericalDiscrete" ||
                data.type === "CategoricalParameter") &&
              !data.values
            ) {
              return false
            }
            // For NumericalContinuous, bounds is required
            if (data.type === "NumericalContinuous" && !data.bounds) {
              return false
            }
            return true
          },
          {
            message: "Required field missing for this parameter type",
            path: ["type"] // This will highlight the type field when validation fails
          }
        )
    )
    .min(1, "At least one parameter is required"),
  constraints: z.array(constraintSchema).optional().default([])
})

type FormValues = z.infer<typeof formSchema>

export function CreateOptimizationForm() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize the form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      target: {
        name: "Target",
        mode: "MAX"
      },
      parameters: [
        {
          name: "",
          type: "NumericalDiscrete",
          values: "",
          encoding: "OHE",
          tolerance: "",
          bounds: ""
        }
      ],
      constraints: []
    }
  })

  // Function to add a new parameter to the form
  const addParameter = () => {
    const parameters = form.getValues("parameters")
    form.setValue("parameters", [
      ...parameters,
      {
        name: "",
        type: "NumericalDiscrete",
        values: "",
        encoding: "OHE",
        tolerance: "",
        bounds: ""
      }
    ])
  }

  // Function to remove a parameter from the form
  const removeParameter = (index: number) => {
    const parameters = form.getValues("parameters")
    if (parameters.length > 1) {
      parameters.splice(index, 1)
      form.setValue("parameters", [...parameters])
    } else {
      toast({
        title: "Cannot remove parameter",
        description: "At least one parameter is required",
        variant: "destructive"
      })
    }
  }

  // Parse values from the form data
  const parseValues = (
    valuesStr: string | undefined,
    type: string
  ): number[] | string[] => {
    if (!valuesStr || valuesStr.trim() === "") {
      // Return an empty array if no values are provided
      // This will be caught by validation later
      return []
    }

    if (type === "NumericalDiscrete" || type === "NumericalContinuous") {
      return valuesStr.split(",").map(v => Number(v.trim()))
    }

    // For CategoricalParameter, ensure we return an array of strings
    const values = valuesStr
      .split(",")
      .map(v => v.trim())
      .filter(v => v !== "")
    console.log(`Parsed categorical values: ${JSON.stringify(values)}`)
    return values
  }

  // Parse bounds from the form data
  const parseBounds = (boundsStr: string): [number, number] => {
    const parts = boundsStr.split(",").map(v => Number(v.trim()))
    return [parts[0] || 0, parts[1] || 100]
  }

  // Function to convert form values to the API format
  const prepareOptimizationConfig = (data: FormValues): OptimizationConfig => {
    const parameters: Parameter[] = data.parameters.map(param => {
      const base = {
        name: param.name,
        type: param.type
      } as Parameter

      if (param.type === "NumericalDiscrete") {
        return {
          ...base,
          type: "NumericalDiscrete" as const,
          values: parseValues(param.values, param.type) as number[],
          tolerance: param.tolerance ? parseFloat(param.tolerance) : undefined
        }
      } else if (param.type === "NumericalContinuous") {
        return {
          ...base,
          type: "NumericalContinuous" as const,
          bounds: parseBounds(param.bounds || "0,100")
        }
      } else if (param.type === "CategoricalParameter") {
        const values = parseValues(param.values || "", param.type) as string[]

        // Validate that we have values for categorical parameters
        if (!values || values.length === 0) {
          throw new Error(
            `Categorical parameter ${param.name} must have at least one value`
          )
        }

        // Validate that we have at least 2 values (BayBE requirement)
        if (values.length < 2) {
          throw new Error(
            `Categorical parameter ${param.name} must have at least two values`
          )
        }

        // Log the values for debugging
        console.log(
          `Categorical parameter ${param.name} values in prepareOptimizationConfig:`,
          JSON.stringify(values)
        )

        // Ensure values is explicitly set as an array of strings
        // This is critical for the backend API
        const result = {
          ...base,
          type: "CategoricalParameter" as const, // Use 'CategoricalParameter' to match type definition
          values: values,
          encoding: param.encoding || "OHE"
        }

        // Log the final parameter object
        console.log(
          `Final categorical parameter ${param.name}:`,
          JSON.stringify(result)
        )

        return result
      }

      return base
    })

    const target_config: TargetConfig = {
      name: data.target.name,
      mode: data.target.mode
    }

    // Convert constraints to API format
    const constraints =
      data.constraints?.map(constraint => ({
        type: constraint.type,
        parameters: constraint.parameters,
        name: constraint.name,
        description: constraint.description,
        // Add constraint-specific fields
        ...(constraint.type === "ContinuousLinearConstraint" && {
          operator: (constraint as any).operator,
          coefficients: (constraint as any).coefficients,
          rhs: (constraint as any).rhs
        }),
        ...(constraint.type.includes("CardinalityConstraint") && {
          min_cardinality: (constraint as any).min_cardinality,
          max_cardinality: (constraint as any).max_cardinality,
          relative_threshold: (constraint as any).relative_threshold
        }),
        ...(constraint.type === "DiscreteExcludeConstraint" && {
          combiner: (constraint as any).combiner,
          conditions: (constraint as any).conditions
        }),
        ...((constraint.type === "DiscreteSumConstraint" ||
          constraint.type === "DiscreteProductConstraint") && {
          condition: (constraint as any).condition
        }),
        ...(constraint.type === "DiscreteDependenciesConstraint" && {
          affected_parameters: (constraint as any).affected_parameters
        }),
        ...(constraint.type === "DiscreteCustomConstraint" && {
          validator: (constraint as any).validator
        })
      })) || []

    // Ensure all required fields are present and have the correct types
    return {
      parameters,
      target_config,
      recommender_config: {
        type: "BotorchRecommender",
        n_restarts: 10, // Ensure this is an integer
        n_raw_samples: 64 // Ensure this is an integer
      },
      constraints: constraints.length > 0 ? constraints : undefined
    }
  }

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    console.log("Form submitted with data:", data)
    setIsSubmitting(true)
    try {
      // Validate form data
      if (!data.name || data.name.length < 2) {
        throw new Error("Name must be at least 2 characters")
      }

      if (!data.target.name) {
        throw new Error("Target name is required")
      }

      if (data.parameters.length === 0) {
        throw new Error("At least one parameter is required")
      }

      // Additional validation for categorical parameters
      for (const param of data.parameters) {
        if (param.type === "CategoricalParameter") {
          if (!param.values || param.values.trim() === "") {
            throw new Error(
              `Categorical parameter ${param.name} must have at least one value`
            )
          }

          // Check if values are properly formatted
          const values = param.values
            .split(",")
            .map(v => v.trim())
            .filter(v => v !== "")
          if (values.length === 0) {
            throw new Error(
              `Categorical parameter ${param.name} must have at least one value`
            )
          }

          // BayBE requires at least 2 values for categorical parameters
          if (values.length < 2) {
            throw new Error(
              `Categorical parameter ${param.name} must have at least two values. Current values: ${values.join(", ")}`
            )
          }
        }
      }

      // Validate constraints if any are defined
      if (data.constraints && data.constraints.length > 0) {
        const parameterNames = data.parameters
          .map(p => p.name)
          .filter(name => name.trim() !== "")

        for (const constraint of data.constraints) {
          // Check that constraint parameters exist in the parameter list
          for (const paramName of constraint.parameters) {
            if (!parameterNames.includes(paramName)) {
              throw new Error(
                `Constraint "${constraint.name || constraint.type}" references parameter "${paramName}" which doesn't exist`
              )
            }
          }

          // Basic constraint completeness validation
          if (!constraint.parameters || constraint.parameters.length === 0) {
            throw new Error(
              `Constraint "${constraint.name || constraint.type}" must have at least one parameter`
            )
          }
        }
      }

      // Prepare configuration
      const config = prepareOptimizationConfig(data)
      console.log("Prepared config:", JSON.stringify(config, null, 2))

      // Log categorical parameters specifically
      const categoricalParams = config.parameters.filter(
        p => p.type === "CategoricalParameter"
      )
      if (categoricalParams.length > 0) {
        console.log(
          "Categorical parameters in final config:",
          JSON.stringify(categoricalParams, null, 2)
        )
      }

      // Ensure recommender_config has the required fields
      if (
        config.recommender_config &&
        config.recommender_config.type === "BotorchRecommender"
      ) {
        // Type assertion to help TypeScript understand the structure
        const botorchRecommender = config.recommender_config as {
          type: "BotorchRecommender"
          n_restarts?: number
          n_raw_samples?: number
        }

        // Ensure n_restarts is an integer
        if (
          botorchRecommender.n_restarts === undefined ||
          botorchRecommender.n_restarts === null
        ) {
          botorchRecommender.n_restarts = 10
        }

        // Ensure n_raw_samples is an integer
        if (
          botorchRecommender.n_raw_samples === undefined ||
          botorchRecommender.n_raw_samples === null
        ) {
          botorchRecommender.n_raw_samples = 64
        }
      }

      // Submit to server action
      console.log(
        "Calling createOptimizationWorkflowAction with config:",
        config
      )
      console.log("Form data:", data)

      const result = await createOptimizationWorkflowAction(
        data.name,
        data.description || "",
        config
      )

      console.log("Server action result:", result)
      console.log("Result type:", typeof result)
      console.log("Result isSuccess:", result?.isSuccess)
      console.log("Result structure:", Object.keys(result || {}))

      // Check if result is valid and has the expected structure
      if (!result || typeof result !== "object") {
        console.error(
          "Invalid result from createOptimizationWorkflowAction:",
          result
        )
        toast({
          title: "Error creating optimization",
          description: "Invalid response from server. Please try again.",
          variant: "destructive"
        })
        return
      }

      // Additional check for isSuccess property
      if (typeof result.isSuccess !== "boolean") {
        console.error("Result missing isSuccess property:", result)
        toast({
          title: "Error creating optimization",
          description:
            "Invalid response structure from server. Please try again.",
          variant: "destructive"
        })
        return
      }

      if (result.isSuccess && result.data) {
        toast({
          title: "Optimization created",
          description: "Your optimization has been created successfully"
        })
        router.push(`/dashboard/optimizations/${result.data.id}`)
      } else {
        toast({
          title: "Error creating optimization",
          description: result.message || "Failed to create optimization",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error creating optimization:", error)
      toast({
        title: "Error creating optimization",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Create New Optimization</CardTitle>
            <CardDescription>
              Configure your optimization parameters and target
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Optimization Name</FormLabel>
                    <FormControl>
                      <Input placeholder="My Optimization" {...field} />
                    </FormControl>
                    <FormDescription>
                      A unique name for your optimization
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="A brief description of this optimization"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Target Configuration */}
            <div className="space-y-4">
              <h3 className="flex items-center text-lg font-medium">
                <Target className="mr-2 size-5" /> Target Configuration
              </h3>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="target.name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Yield" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="target.mode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Mode</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a mode" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="MAX">Maximize</SelectItem>
                          <SelectItem value="MIN">Minimize</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Whether to maximize or minimize the target value
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Parameters Configuration */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="flex items-center text-lg font-medium">
                  <Beaker className="mr-2 size-5" /> Parameters
                </h3>
                <Button type="button" variant="outline" onClick={addParameter}>
                  <Plus className="mr-2 size-4" /> Add Parameter
                </Button>
              </div>

              {/* Parameter List */}
              {form.watch("parameters").map((parameter, index) => (
                <Card key={index} className="border border-gray-200">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">
                        Parameter {index + 1}
                      </CardTitle>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeParameter(index)}
                      >
                        <Trash className="size-4 text-red-500" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-4 pt-0">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name={`parameters.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Parameter Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="e.g., Temperature"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`parameters.${index}.type`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Parameter Type</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="NumericalDiscrete">
                                  Numerical Discrete
                                </SelectItem>
                                <SelectItem value="NumericalContinuous">
                                  Numerical Continuous
                                </SelectItem>
                                <SelectItem value="CategoricalParameter">
                                  Categorical
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Type-specific fields */}
                    {form.watch(`parameters.${index}.type`) ===
                      "NumericalDiscrete" && (
                      <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                        <FormField
                          control={form.control}
                          name={`parameters.${index}.values`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Values (comma-separated)</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="10, 20, 30, 40, 50"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Discrete values to explore
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`parameters.${index}.tolerance`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Tolerance (optional)</FormLabel>
                              <FormControl>
                                <Input placeholder="0.5" {...field} />
                              </FormControl>
                              <FormDescription>
                                Allowed deviation from discrete values
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}

                    {form.watch(`parameters.${index}.type`) ===
                      "NumericalContinuous" && (
                      <div className="mt-4">
                        <FormField
                          control={form.control}
                          name={`parameters.${index}.bounds`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Bounds (min, max)</FormLabel>
                              <FormControl>
                                <Input placeholder="0, 100" {...field} />
                              </FormControl>
                              <FormDescription>
                                Range of values to explore, e.g., "0, 100"
                              </FormDescription>
                              {(form.formState.errors.parameters?.[index]
                                ?.bounds ||
                                (form.formState.errors.parameters?.[index]
                                  ?.type &&
                                  form.watch(`parameters.${index}.type`) ===
                                    "NumericalContinuous")) && (
                                <p className="text-destructive mt-1 text-sm">
                                  Bounds are required for Numerical Continuous
                                  parameters
                                </p>
                              )}
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}

                    {form.watch(`parameters.${index}.type`) ===
                      "CategoricalParameter" && (
                      <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                        <FormField
                          control={form.control}
                          name={`parameters.${index}.values`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Values (comma-separated)</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Red, Green, Blue"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Categorical values to explore (at least 2 values
                                required, comma-separated)
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`parameters.${index}.encoding`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Encoding</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select encoding" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="OHE">
                                    One-Hot Encoding (OHE)
                                  </SelectItem>
                                  <SelectItem value="LE">
                                    Label Encoding (LE)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Constraints Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Constraints (Optional)</h3>
              <p className="text-muted-foreground text-sm">
                Add constraints to limit the parameter space and ensure feasible
                solutions.
              </p>

              <FormField
                control={form.control}
                name="constraints"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <CentralizedConstraintBuilder
                        parameters={form.watch("parameters").map(param => ({
                          name: param.name,
                          type: param.type as
                            | "NumericalDiscrete"
                            | "NumericalContinuous"
                            | "CategoricalParameter",
                          values:
                            param.type === "CategoricalParameter" ||
                            param.type === "NumericalDiscrete"
                              ? param.values
                                  ?.split(",")
                                  .map(v => v.trim())
                                  .filter(v => v) || []
                              : undefined,
                          bounds:
                            param.type === "NumericalContinuous" && param.bounds
                              ? (param.bounds
                                  .split(",")
                                  .map(v => parseFloat(v.trim()))
                                  .filter(v => !isNaN(v)) as [number, number])
                              : undefined,
                          tolerance: param.tolerance
                            ? parseFloat(param.tolerance)
                            : undefined,
                          encoding: param.encoding
                        }))}
                        constraints={field.value || []}
                        onConstraintsChange={field.onChange}
                        enableSampling={false}
                        className="rounded-lg border p-4"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Optimization"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  )
}
