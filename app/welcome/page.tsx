"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { getActualUserId } from "@/lib/auth-utils"
import { checkSurveyCompletionAction } from "@/actions/survey-actions"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import SurveyForm from "@/components/survey/survey-form"
import { BRAND } from "@/lib/constants"

export default async function WelcomePage({
  searchParams
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  // If user is not authenticated, redirect to login
  // But also check the raw Clerk ID as a fallback for new users
  if (!userId && !clerkUserId) {
    redirect("/login")
  }

  // Use the actual user ID if available, otherwise fall back to Clerk ID
  const finalUserId = userId || clerkUserId

  // Get the search params
  const params = await searchParams

  // Check if the user came from a successful checkout
  const checkoutSuccess = params.checkout_success === "true"
  const sessionId = params.session_id as string | undefined
  // Check if the user came from the dashboard
  const fromDashboard = params.from === "dashboard"

  // Check if the user has already completed the survey
  const surveyStatus = await checkSurveyCompletionAction()

  // If the user has already completed the survey, redirect to dashboard
  if (surveyStatus.isSuccess && surveyStatus.hasCompleted) {
    redirect("/dashboard/home")
  }

  return (
    <div className="from-background via-background min-h-screen bg-gradient-to-br to-blue-50/30 dark:to-blue-950/30">
      <div className="container mx-auto px-4 py-8 lg:py-12">
        {/* Header Section */}
        <div className="mb-8 text-center lg:mb-12">
          <h1 className="mb-4 text-3xl font-bold lg:text-4xl">
            {checkoutSuccess
              ? "Thank You for Your Subscription!"
              : fromDashboard
                ? "Complete Your Profile"
                : `Welcome to ${BRAND.NAME}!`}
          </h1>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
            Help us understand your optimization challenges so we can show you
            the right tools and approaches for your work.
          </p>
        </div>

        {/* Success Message for Checkout */}
        {checkoutSuccess && (
          <div className="mx-auto mb-8 max-w-2xl">
            <div className="rounded-lg border border-green-200 bg-green-50 p-6 dark:border-green-800 dark:bg-green-900/20">
              <p className="text-green-800 dark:text-green-200">
                <span className="font-semibold">Subscription Successful!</span>{" "}
                Your payment has been processed and your premium account is now
                active. You now have full access to all premium features.
              </p>
            </div>
          </div>
        )}

        {/* Info Banner */}
        <div className="mx-auto mb-8 max-w-2xl">
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-6 dark:border-blue-800 dark:bg-blue-900/20">
            <p className="text-blue-800 dark:text-blue-200">
              <strong>Needed to continue.</strong> Skip the learning curve with
              content tailored to your industry and goals.
            </p>
          </div>
        </div>

        {/* Enhanced Survey Form Container */}
        <div className="mx-auto max-w-6xl">
          <Card className="border-0 bg-gradient-to-r from-white/80 to-white/60 shadow-xl backdrop-blur-sm dark:from-gray-900/80 dark:to-gray-900/60">
            <CardContent className="p-6 lg:p-8">
              <SurveyForm />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
