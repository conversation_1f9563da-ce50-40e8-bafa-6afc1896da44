"use server"

import { ActionState } from "@/types"
import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { updateMeasurementAction, deleteMeasurementAction, getMeasurementsAction } from "@/actions/db/optimizations-actions"
import { getBestPointWorkflowAction } from "@/actions/optimization-workflow-actions"
import { updateOptimizationStatusAction } from "@/actions/optimization-status-actions"
import { createMeasurementAuditAction } from "@/actions/measurement-audit-actions"
import { createAuditDataFromMeasurement } from "@/lib/measurement-audit-utils"
import { SelectMeasurement, InsertMeasurement } from "@/db/schema/optimizations-schema"

/**
 * Updates a measurement and handles all dependencies
 */
export async function updateMeasurementWithDependenciesAction(
  measurementId: string,
  updates: Partial<InsertMeasurement>,
  optimizationId: string,
  optimizerId: string
): Promise<ActionState<SelectMeasurement>> {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🔍 UPDATE MEASUREMENT WITH DEPENDENCIES - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  })

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to update measurements"
    }
  }

  try {
    // Get the original measurement data for audit trail
    const measurementsResult = await getMeasurementsAction(optimizationId)
    const originalMeasurement = measurementsResult.isSuccess
      ? measurementsResult.data?.find(m => m.id === measurementId)
      : null

    // Update the measurement
    const updateResult = await updateMeasurementAction(measurementId, updates)

    if (!updateResult.isSuccess) {
      return updateResult
    }

    // Create audit trail entry
    if (originalMeasurement && updateResult.data) {
      try {
        await createMeasurementAuditAction(
          measurementId,
          "update",
          createAuditDataFromMeasurement(originalMeasurement),
          createAuditDataFromMeasurement(updateResult.data),
          { reason: "User updated measurement via UI" }
        )
      } catch (auditError) {
        console.error("Failed to create audit trail for measurement update:", auditError)
        // Don't fail the main operation if audit fails
      }
    }

    // Handle dependencies in parallel for better performance
    const dependencyPromises = []

    // 1. Recalculate best point (this might have changed)
    dependencyPromises.push(
      getBestPointWorkflowAction(optimizerId).catch(error => {
        console.error("Error recalculating best point after measurement update:", error)
        return { isSuccess: false, message: "Failed to recalculate best point" }
      })
    )

    // 2. Update optimization status to indicate recent activity
    dependencyPromises.push(
      updateOptimizationStatusAction(
        optimizationId,
        "active",
        "measurement_updated"
      ).catch(error => {
        console.error("Error updating optimization status after measurement update:", error)
        return { isSuccess: false, message: "Failed to update optimization status" }
      })
    )

    // Wait for all dependency updates to complete
    const dependencyResults = await Promise.all(dependencyPromises)

    // Log any dependency failures but don't fail the main operation
    dependencyResults.forEach((result, index) => {
      if (!result.isSuccess) {
        const operation = index === 0 ? "best point recalculation" : "status update"
        console.warn(`Dependency operation failed (${operation}):`, result.message)
      }
    })

    return {
      isSuccess: true,
      message: "Measurement updated successfully",
      data: updateResult.data
    }
  } catch (error) {
    console.error("Error in updateMeasurementWithDependenciesAction:", error)
    return {
      isSuccess: false,
      message: `Failed to update measurement: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Deletes a measurement and handles all dependencies
 */
export async function deleteMeasurementWithDependenciesAction(
  measurementId: string,
  optimizationId: string,
  optimizerId: string
): Promise<ActionState<void>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to delete measurements"
    }
  }

  try {
    // Get the original measurement data for audit trail
    const measurementsResult = await getMeasurementsAction(optimizationId)
    const originalMeasurement = measurementsResult.isSuccess
      ? measurementsResult.data?.find(m => m.id === measurementId)
      : null

    // Delete the measurement
    const deleteResult = await deleteMeasurementAction(measurementId)

    if (!deleteResult.isSuccess) {
      return deleteResult
    }

    // Create audit trail entry
    if (originalMeasurement) {
      try {
        await createMeasurementAuditAction(
          measurementId,
          "delete",
          createAuditDataFromMeasurement(originalMeasurement),
          null,
          { reason: "User deleted measurement via UI" }
        )
      } catch (auditError) {
        console.error("Failed to create audit trail for measurement deletion:", auditError)
        // Don't fail the main operation if audit fails
      }
    }

    // Handle dependencies in parallel for better performance
    const dependencyPromises = []

    // 1. Recalculate best point (this will definitely have changed)
    dependencyPromises.push(
      getBestPointWorkflowAction(optimizerId).catch(error => {
        console.error("Error recalculating best point after measurement deletion:", error)
        return { isSuccess: false, message: "Failed to recalculate best point" }
      })
    )

    // 2. Update optimization status to indicate recent activity
    dependencyPromises.push(
      updateOptimizationStatusAction(
        optimizationId,
        "active",
        "measurement_deleted"
      ).catch(error => {
        console.error("Error updating optimization status after measurement deletion:", error)
        return { isSuccess: false, message: "Failed to update optimization status" }
      })
    )

    // Wait for all dependency updates to complete
    const dependencyResults = await Promise.all(dependencyPromises)

    // Log any dependency failures but don't fail the main operation
    dependencyResults.forEach((result, index) => {
      if (!result.isSuccess) {
        const operation = index === 0 ? "best point recalculation" : "status update"
        console.warn(`Dependency operation failed (${operation}):`, result.message)
      }
    })

    return {
      isSuccess: true,
      message: "Measurement deleted successfully",
      data: undefined
    }
  } catch (error) {
    console.error("Error in deleteMeasurementWithDependenciesAction:", error)
    return {
      isSuccess: false,
      message: `Failed to delete measurement: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Validates if a measurement can be safely updated or deleted
 */
export async function validateMeasurementOperationAction(
  measurementId: string,
  operation: "update" | "delete"
): Promise<ActionState<{
  canProceed: boolean
  warnings: string[]
  impacts: string[]
}>> {
  const { userId } = await auth()

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to validate measurement operations"
    }
  }

  try {
    const warnings: string[] = []
    const impacts: string[] = []

    // For now, we'll allow all operations but provide warnings
    // In the future, this could include more sophisticated validation

    if (operation === "delete") {
      warnings.push("Deleting this measurement will permanently remove it from the optimization history")
      impacts.push("The optimization's best point may be recalculated")
      impacts.push("Experiment numbers may be reordered")
      impacts.push("The algorithm's learning history will be affected")
    } else {
      warnings.push("Updating this measurement may affect the optimization's learning process")
      impacts.push("The optimization's best point may be recalculated")
      impacts.push("Future suggestions may be influenced by this change")
    }

    return {
      isSuccess: true,
      message: "Validation completed",
      data: {
        canProceed: true,
        warnings,
        impacts
      }
    }
  } catch (error) {
    console.error("Error in validateMeasurementOperationAction:", error)
    return {
      isSuccess: false,
      message: `Failed to validate operation: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Gets the impact analysis for a measurement operation
 */
export async function getMeasurementImpactAnalysisAction(
  measurementId: string,
  optimizationId: string
): Promise<ActionState<{
  isOnlyMeasurement: boolean
  isRecentMeasurement: boolean
  isAPIGenerated: boolean
  isBestMeasurement: boolean
  affectedExperiments: number
}>> {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()



  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to get impact analysis"
    }
  }

  try {
    // This is a placeholder for more sophisticated impact analysis
    // In a real implementation, you would:
    // 1. Check if this is the only measurement
    // 2. Check if this is the best measurement
    // 3. Check how many experiments would be affected
    // 4. Analyze the impact on the optimization algorithm

    return {
      isSuccess: true,
      message: "Impact analysis completed",
      data: {
        isOnlyMeasurement: false,
        isRecentMeasurement: true,
        isAPIGenerated: true,
        isBestMeasurement: false,
        affectedExperiments: 0
      }
    }
  } catch (error) {
    console.error("Error in getMeasurementImpactAnalysisAction:", error)
    return {
      isSuccess: false,
      message: `Failed to analyze impact: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
