"use client"

import { cn } from "@/lib/utils"

interface ProgressStepperProps {
  steps: string[]
  currentStep: number
  className?: string
}

export function ProgressStepper({
  steps,
  currentStep,
  className
}: ProgressStepperProps) {
  return (
    <div className={cn("w-full", className)}>
      {/* Progress Bar */}
      <div className="relative mb-4 mt-2">
        <div className="bg-muted absolute left-0 top-1/2 h-0.5 w-full -translate-y-1/2" />
        <div
          className="bg-primary absolute left-0 top-1/2 h-0.5 -translate-y-1/2 transition-all duration-300 ease-in-out"
          style={{
            width: `${(currentStep / (steps.length - 1)) * 100}%`
          }}
        />
        <div className="relative flex justify-between">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className={cn(
                  "z-10 flex size-6 items-center justify-center rounded-full border text-xs font-medium",
                  index < currentStep
                    ? "border-primary bg-primary text-primary-foreground"
                    : index === currentStep
                      ? "border-primary bg-background text-primary"
                      : "border-muted bg-background text-muted-foreground"
                )}
              >
                {index + 1}
              </div>
              <span
                className={cn(
                  "mt-2 text-xs font-medium",
                  index <= currentStep
                    ? "text-foreground"
                    : "text-muted-foreground"
                )}
              >
                {step}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
