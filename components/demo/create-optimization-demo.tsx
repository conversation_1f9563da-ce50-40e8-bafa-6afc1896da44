"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { CreateOptimizationWizard } from "@/components/optimization/create-optimization-wizard"
import { DemoModeWrapper } from "./demo-mode-wrapper"
import {
  optimizationDemoSteps,
  demoFormData,
  industryDemoVariations
} from "./optimization-demo-steps"
import { getSurveyResponsesAction } from "@/actions/survey-actions"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Lightbulb,
  Play,
  ArrowLeft,
  Sparkles,
  Users,
  Clock,
  Target
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface CreateOptimizationDemoProps {
  isGuided: boolean
}

export function CreateOptimizationDemo({
  isGuided
}: CreateOptimizationDemoProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isDemo, setIsDemo] = useState(isGuided)
  const [surveyData, setSurveyData] = useState<any>({})
  const [demoVariation, setDemoVariation] = useState(demoFormData)
  const [currentWizardStep, setCurrentWizardStep] = useState(0)

  // Load user survey data for personalization
  useEffect(() => {
    const loadSurveyData = async () => {
      try {
        const result = await getSurveyResponsesAction()
        if (result.isSuccess && result.responses) {
          setSurveyData(result.responses)

          // Customize demo based on industry
          const industry = result.responses.industry
          if (industry && industry in industryDemoVariations) {
            const variation =
              industryDemoVariations[
                industry as keyof typeof industryDemoVariations
              ]
            setDemoVariation({
              ...demoFormData,
              name: variation.name,
              description: variation.description,
              singleTarget: variation.singleTarget,
              parameters: variation.parameters.map(param => ({
                name: param.name,
                type: "NumericalContinuous" as const,
                bounds: param.bounds || "",
                values: "",
                encoding: "OHE" as const,
                tolerance: ""
              }))
            })
          }
        }
      } catch (error) {
        console.error("Error loading survey data:", error)
      }
    }
    loadSurveyData()
  }, [])

  const handleExitDemo = () => {
    setIsDemo(false)
    // Remove demo params from URL
    router.replace("/dashboard/optimizations/create")
  }

  const handleDemoComplete = () => {
    console.log("=== DEMO COMPLETION STARTED ===")
    console.log("handleDemoComplete called - redirecting to home")
    console.log("Current router:", router)
    console.log("Current pathname:", window.location.pathname)

    // Show completion message and redirect to home
    toast({
      title: "🎉 Demo Completed!",
      description:
        "Great job! You've learned how to create optimizations. Redirecting to dashboard...",
      duration: 5000
    })

    // Redirect to home page after a short delay
    setTimeout(() => {
      console.log("=== ATTEMPTING REDIRECT ===")
      console.log("Redirecting to /dashboard/home")
      console.log("Router available:", !!router)

      try {
        router.push("/dashboard/home")
        console.log("Router.push called successfully")

        // Fallback: use window.location if router fails
        setTimeout(() => {
          if (window.location.pathname !== "/dashboard/home") {
            console.log("Router redirect failed, using window.location")
            window.location.href = "/dashboard/home"
          }
        }, 2000)
      } catch (error) {
        console.error("Router.push failed:", error)
        console.log("Using window.location fallback")
        window.location.href = "/dashboard/home"
      }
    }, 1500)
  }

  const handleStartDemo = () => {
    setIsDemo(true)
    toast({
      title: "🎬 Demo Mode Activated",
      description:
        "Follow the guided tour to learn how to create optimizations!"
    })
  }

  const handleSkipToReal = () => {
    router.push("/dashboard/optimizations/create")
  }

  // If not in demo mode, show the demo introduction
  if (!isDemo) {
    return (
      <div className="mx-auto max-w-4xl py-6">
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push("/dashboard/home")}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 size-4" />
            Back to Dashboard
          </Button>
          <h1 className="text-3xl font-bold">Interactive Demo</h1>
          <p className="text-muted-foreground mt-2">
            Experience the real platform with guided assistance
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Demo Introduction */}
          <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="rounded-lg bg-blue-600 p-2">
                  <Play className="size-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-lg">
                    Live Interactive Demo
                  </CardTitle>
                  <div className="mt-1 flex items-center gap-2">
                    <Badge className="bg-blue-600 text-xs text-white">
                      🔥 Real Platform
                    </Badge>
                    {surveyData.industry && (
                      <Badge variant="outline" className="text-xs">
                        {surveyData.industry}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {surveyData.industry && surveyData.industry !== "skipped"
                  ? `Experience a ${surveyData.industry}-specific optimization example with real platform interactions.`
                  : "Experience the real optimization creation process with step-by-step guidance."}
              </p>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Target className="size-4 text-blue-600" />
                  <span>
                    <strong>Example:</strong> {demoVariation.name}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Users className="size-4 text-green-600" />
                  <span>Real UI with guided overlays</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="size-4 text-purple-600" />
                  <span>~5 minutes interactive tour</span>
                </div>
              </div>

              <Button
                onClick={handleStartDemo}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                <Sparkles className="mr-2 size-4" />
                Start Interactive Demo
              </Button>
            </CardContent>
          </Card>

          {/* Regular Creation Option */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Create Without Demo</CardTitle>
              <p className="text-muted-foreground text-sm">
                Jump straight to creating your optimization
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Skip the demo and create your optimization directly. You can
                always come back to the demo later.
              </p>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <span>✓ Full platform access</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span>✓ No guided overlays</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span>✓ Faster workflow</span>
                </div>
              </div>

              <Button
                variant="outline"
                onClick={handleSkipToReal}
                className="w-full"
              >
                Create Optimization
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Tips Section */}
        {surveyData.industry && surveyData.industry !== "skipped" && (
          <Card className="mt-6 border-amber-200 bg-amber-50 dark:bg-amber-950/20">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Lightbulb className="mt-0.5 size-5 text-amber-600" />
                <div>
                  <h3 className="font-medium text-amber-800 dark:text-amber-200">
                    Tailored for {surveyData.industry}
                  </h3>
                  <p className="mt-1 text-sm text-amber-700 dark:text-amber-300">
                    The demo includes examples and terminology specific to your
                    industry, making it easier to understand how Bayesian
                    optimization applies to your work.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  // Demo mode - wrap the wizard with demo functionality
  return (
    <DemoModeWrapper
      isDemo={isDemo}
      onExitDemo={handleExitDemo}
      steps={optimizationDemoSteps}
      className="min-h-screen"
      currentWizardStep={currentWizardStep}
      onStepChange={setCurrentWizardStep}
      onDemoComplete={handleDemoComplete}
    >
      <div className="mx-auto max-w-4xl py-6" data-demo="wizard-container">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Create New Optimization</h1>
              <p className="text-muted-foreground mt-1">
                🎬 Demo Mode - Follow the guided tour
              </p>
            </div>
            <Badge className="bg-blue-600 text-white">Interactive Demo</Badge>
          </div>
        </div>

        <CreateOptimizationWizard
          demoMode={true}
          demoData={demoVariation}
          onStepChange={setCurrentWizardStep}
          onDemoComplete={handleDemoComplete}
        />
      </div>
    </DemoModeWrapper>
  )
}
