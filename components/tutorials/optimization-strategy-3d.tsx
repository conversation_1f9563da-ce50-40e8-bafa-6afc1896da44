"use client"

import React, { useRef, useState, useEffect } from "react"
import { <PERSON><PERSON>, use<PERSON>ram<PERSON>, useThree } from "@react-three/fiber"
import { OrbitControls, Text, Html } from "@react-three/drei"
import * as THREE from "three"

// Function to generate a 2D test function (modified Himmelblau's function)
// This version has clearer local minima and one global minimum
const himmelblau = (x: number, y: number) => {
  // Base Himmelblau function
  const base = Math.pow(x * x + y - 11, 2) + Math.pow(x + y * y - 7, 2)

  // Add a slight bias to create one global minimum at (3.0, 2.0)
  const distanceToGlobalMin = Math.sqrt(
    Math.pow(x - 3.0, 2) + Math.pow(y - 2.0, 2)
  )
  const globalMinBias = 5 * Math.exp(-distanceToGlobalMin * 2)

  return base - globalMinBias
}

// Function to normalize values for visualization
const normalize = (value: number, min: number, max: number) => {
  return (value - min) / (max - min)
}

// Known optima of the function
const optima = [
  { x: 3.0, y: 2.0, type: "global" }, // Global minimum
  { x: -2.8, y: 3.1, type: "local" }, // Local minimum 1
  { x: -3.7, y: -3.2, type: "local" }, // Local minimum 2
  { x: 3.6, y: -1.8, type: "local" } // Local minimum 3
]

// Generate surface data
const generateSurfaceData = () => {
  const resolution = 80 // Higher resolution for smoother surface
  const xMin = -5,
    xMax = 5
  const yMin = -5,
    yMax = 5
  const positions = []
  const colors = []
  const indices = []

  // Calculate z values and find min/max for normalization
  const zValues = []
  for (let i = 0; i < resolution; i++) {
    for (let j = 0; j < resolution; j++) {
      const x = xMin + (xMax - xMin) * (i / (resolution - 1))
      const y = yMin + (yMax - yMin) * (j / (resolution - 1))
      const z = himmelblau(x, y)
      zValues.push(z)
    }
  }

  const zMin = Math.min(...zValues)
  const zMax = Math.max(...zValues)

  // Create vertices and colors
  let idx = 0
  for (let i = 0; i < resolution; i++) {
    for (let j = 0; j < resolution; j++) {
      const x = xMin + (xMax - xMin) * (i / (resolution - 1))
      const y = yMin + (yMax - yMin) * (j / (resolution - 1))
      const z = himmelblau(x, y)

      // Normalize z for position
      const normalizedZ = normalize(z, zMin, zMax) * 2 // Scale for better visualization

      positions.push(x, y, normalizedZ)

      // Enhanced color scheme to highlight optima regions
      const normalizedColor = normalize(z, zMin, zMax)

      // Check if point is near any optima
      let isNearOptima = false
      let isNearGlobalOptimum = false

      for (const optimum of optima) {
        const distance = Math.sqrt(
          Math.pow(x - optimum.x, 2) + Math.pow(y - optimum.y, 2)
        )
        if (distance < 0.8) {
          isNearOptima = true
          if (optimum.type === "global") {
            isNearGlobalOptimum = true
          }
          break
        }
      }

      let r, g, b

      if (isNearGlobalOptimum) {
        // Green for global minimum - using #10b981 from the landing page
        r = 0.06
        g = 0.73
        b = 0.51
      } else if (isNearOptima) {
        // Yellow/Orange for local minima - using #f59e0b from the landing page
        r = 0.96
        g = 0.62
        b = 0.04
      } else {
        // Blues colorscale for the rest of the surface - matching the landing page
        // This creates a blue gradient that gets lighter as the value increases
        r = 0.0 + normalizedColor * 0.8
        g = 0.3 + normalizedColor * 0.5
        b = 0.7 + normalizedColor * 0.3
      }

      colors.push(r, g, b)

      // Create triangles (indices)
      if (i < resolution - 1 && j < resolution - 1) {
        const a = i * resolution + j
        const b = i * resolution + j + 1
        const c = (i + 1) * resolution + j
        const d = (i + 1) * resolution + j + 1

        // First triangle
        indices.push(a, b, c)
        // Second triangle
        indices.push(b, d, c)
      }

      idx++
    }
  }

  return { positions, colors, indices, zMin, zMax }
}

// Sequential sampling points - total of 20 points
const sequentialPoints = [
  // Initial points (Latin hypercube sampling) - 10 points
  { x: -4.0, y: -3.0, step: 0, label: "Initial 1" },
  { x: -3.0, y: 3.0, step: 0, label: "Initial 2" },
  { x: -1.0, y: -2.0, step: 0, label: "Initial 3" },
  { x: 0.0, y: 4.0, step: 0, label: "Initial 4" },
  { x: 1.0, y: 0.0, step: 0, label: "Initial 5" },
  { x: 2.0, y: -3.0, step: 0, label: "Initial 6" },
  { x: 3.0, y: -1.0, step: 0, label: "Initial 7" },
  { x: 4.0, y: 0.0, step: 0, label: "Initial 8" },
  { x: -2.0, y: -4.0, step: 0, label: "Initial 9" },
  { x: -3.5, y: 0.0, step: 0, label: "Initial 10" },

  // Sequential points (one at a time) - 10 points gradually converging to global minimum
  { x: -2.8, y: 3.1, step: 1, label: "Step 1" }, // Explores local minimum 1
  { x: 3.6, y: -1.8, step: 2, label: "Step 2" }, // Explores local minimum 3
  { x: -3.7, y: -3.2, step: 3, label: "Step 3" }, // Explores local minimum 2
  { x: 2.0, y: 1.0, step: 4, label: "Step 4" }, // Starts exploring near global minimum
  { x: 2.5, y: 1.5, step: 5, label: "Step 5" }, // Getting closer to global minimum
  { x: 2.8, y: 1.8, step: 6, label: "Step 6" }, // Getting even closer
  { x: 3.0, y: 2.0, step: 7, label: "Step 7" }, // Found the global minimum!
  { x: 3.1, y: 2.1, step: 8, label: "Step 8" }, // Fine-tuning around global minimum
  { x: 2.9, y: 1.9, step: 9, label: "Step 9" }, // More fine-tuning
  { x: 3.0, y: 2.0, step: 10, label: "Step 10" } // Confirming the global minimum
]

// Parallel sampling points - total of 20 points
const parallelPoints = [
  // Initial points (Latin hypercube sampling) - 10 points
  { x: -4.0, y: -3.0, step: 0, label: "Initial 1" },
  { x: -3.0, y: 3.0, step: 0, label: "Initial 2" },
  { x: -1.0, y: -2.0, step: 0, label: "Initial 3" },
  { x: 0.0, y: 4.0, step: 0, label: "Initial 4" },
  { x: 1.0, y: 0.0, step: 0, label: "Initial 5" },
  { x: 2.0, y: -3.0, step: 0, label: "Initial 6" },
  { x: 3.0, y: -1.0, step: 0, label: "Initial 7" },
  { x: 4.0, y: 0.0, step: 0, label: "Initial 8" },
  { x: -2.0, y: -4.0, step: 0, label: "Initial 9" },
  { x: -3.5, y: 0.0, step: 0, label: "Initial 10" },

  // First batch (all at once) - 5 points exploring multiple areas
  { x: -2.8, y: 3.1, step: 1, label: "Batch 1" }, // Near local minimum 1
  { x: 3.6, y: -1.8, step: 1, label: "Batch 1" }, // Near local minimum 3
  { x: -3.7, y: -3.2, step: 1, label: "Batch 1" }, // Near local minimum 2
  { x: 2.0, y: 1.0, step: 1, label: "Batch 1" }, // Heading toward global minimum
  { x: 2.5, y: 1.5, step: 1, label: "Batch 1" }, // Closer to global minimum

  // Second batch (all at once) - 5 points focusing on promising areas
  { x: 2.8, y: 1.8, step: 2, label: "Batch 2" }, // Getting closer to global minimum
  { x: 3.0, y: 2.0, step: 2, label: "Batch 2" }, // Found the global minimum!
  { x: 3.1, y: 2.1, step: 2, label: "Batch 2" }, // Fine-tuning around global minimum
  { x: 2.9, y: 1.9, step: 2, label: "Batch 2" }, // More fine-tuning
  { x: 3.0, y: 2.0, step: 2, label: "Batch 2" } // Confirming the global minimum
]

// Surface component
const Surface = () => {
  const meshRef = useRef<THREE.Mesh>(null)
  const { positions, colors, indices } = generateSurfaceData()

  useEffect(() => {
    if (meshRef.current) {
      const geometry = meshRef.current.geometry as THREE.BufferGeometry

      geometry.setAttribute(
        "position",
        new THREE.Float32BufferAttribute(positions, 3)
      )

      geometry.setAttribute(
        "color",
        new THREE.Float32BufferAttribute(colors, 3)
      )

      geometry.setIndex(indices)

      geometry.computeVertexNormals()
    }
  }, [positions, colors, indices])

  return (
    <mesh ref={meshRef}>
      <bufferGeometry />
      <meshStandardMaterial
        vertexColors
        side={THREE.DoubleSide}
        wireframe={false}
        roughness={0.7}
        metalness={0.2}
      />
    </mesh>
  )
}

// Points component
const SamplingPoints = ({
  points,
  currentStep,
  color
}: {
  points: { x: number; y: number; step: number; label: string }[]
  currentStep: number
  color: string
}) => {
  // Get z values for each point
  const pointsWithZ = points.map(p => {
    const z = himmelblau(p.x, p.y)
    // Normalize z (same as in surface generation)
    const { zMin, zMax } = generateSurfaceData()
    const normalizedZ = normalize(z, zMin, zMax) * 2 + 0.05 // Slight offset to appear above surface
    return { ...p, z: normalizedZ }
  })

  return (
    <group>
      {pointsWithZ.map((point, i) => {
        // Only show points up to current step
        if (point.step > currentStep) return null

        // Determine size and opacity based on step
        const isInitialSample = point.step === 0
        const isCurrentStep = point.step === currentStep && !isInitialSample
        const isPreviousStep = point.step < currentStep && !isInitialSample

        // Make points more visible with larger sizes
        const size = isCurrentStep ? 0.2 : isInitialSample ? 0.12 : 0.15

        // Adjust opacity for better visibility
        const opacity = isInitialSample ? 0.7 : isCurrentStep ? 1 : 0.8

        // Determine if this is a batch point (for parallel strategy)
        const isBatchPoint = point.label && point.label.includes("Batch")

        // Create a label for the point
        let showLabel = false
        let labelText = ""

        if (isCurrentStep) {
          showLabel = true
          labelText = point.label || `Step ${point.step}`
        }
        // Removed initial sample labels as requested

        // Special case for points at the global minimum (around 3.0, 2.0)
        const isNearGlobalMin =
          Math.abs(point.x - 3.0) < 0.3 && Math.abs(point.y - 2.0) < 0.3

        return (
          <group key={i} position={[point.x, point.y, point.z]}>
            <mesh>
              <sphereGeometry args={[size, 16, 16]} />
              <meshStandardMaterial
                // Use orange (#f59e0b) for initial samples, otherwise use the strategy color
                color={isInitialSample ? "#f59e0b" : color}
                transparent
                opacity={opacity}
                emissive={
                  isCurrentStep
                    ? color
                    : isNearGlobalMin
                      ? "#10b981"
                      : undefined
                }
                emissiveIntensity={
                  isCurrentStep ? 0.8 : isNearGlobalMin ? 0.5 : 0
                }
              />
            </mesh>
            {showLabel && (
              <Html distanceFactor={10}>
                <div
                  className={`whitespace-nowrap rounded px-2 py-1 text-xs text-white ${
                    isCurrentStep
                      ? isBatchPoint
                        ? "bg-purple-700/90"
                        : "bg-blue-700/90"
                      : isInitialSample
                        ? "bg-amber-600/90"
                        : "bg-black/70"
                  }`}
                >
                  {labelText}
                </div>
              </Html>
            )}
          </group>
        )
      })}
    </group>
  )
}

// Optima markers component
const OptimaMarkers = ({ showLabels = false }) => {
  // Get z values for each optimum
  const optimaWithZ = optima.map(p => {
    const z = himmelblau(p.x, p.y)
    // Normalize z (same as in surface generation)
    const { zMin, zMax } = generateSurfaceData()
    const normalizedZ = normalize(z, zMin, zMax) * 2 + 0.05 // Slight offset to appear above surface
    return { ...p, z: normalizedZ }
  })

  return (
    <group>
      {optimaWithZ.map((point, i) => {
        // Different appearance based on type
        const isGlobal = point.type === "global"
        const size = isGlobal ? 0.15 : 0.12
        // Using exact colors from the landing page
        const color = isGlobal ? "#10b981" : "#f59e0b" // Green for global, orange for local

        return (
          <group key={i} position={[point.x, point.y, point.z]}>
            <mesh>
              <torusGeometry args={[size, 0.03, 16, 32]} />
              <meshStandardMaterial
                color={color}
                emissive={color}
                emissiveIntensity={0.5}
              />
            </mesh>
            {showLabels && (
              <Html distanceFactor={10}>
                <div
                  className={`whitespace-nowrap rounded px-2 py-1 text-xs text-white ${
                    isGlobal ? "bg-green-600" : "bg-amber-600"
                  }`}
                >
                  {isGlobal ? "Global Minimum" : "Local Minimum"}
                </div>
              </Html>
            )}
          </group>
        )
      })}
    </group>
  )
}

// Main scene component
const Scene = ({
  strategy,
  currentStep
}: {
  strategy: "sequential" | "parallel"
  currentStep: number
}) => {
  const points = strategy === "sequential" ? sequentialPoints : parallelPoints
  // Using colors from the landing page
  // Initial samples: #f59e0b (orange)
  // Bayesian points: #8b5cf6 (purple)
  // Sequential points: #3b82f6 (blue)
  const color = strategy === "sequential" ? "#3b82f6" : "#8b5cf6"

  return (
    <>
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <directionalLight position={[-10, -10, -5]} intensity={0.5} />

      <Surface />
      <OptimaMarkers showLabels={false} />
      <SamplingPoints points={points} currentStep={currentStep} color={color} />

      <OrbitControls
        enableZoom={true}
        enablePan={true}
        enableRotate={true}
        minDistance={2}
        maxDistance={30}
        zoomSpeed={1.2}
      />
    </>
  )
}

// Controls for the visualization
const VisualizationControls = ({
  strategy,
  setStrategy,
  currentStep,
  setCurrentStep,
  maxSteps,
  showOptimaLabels,
  setShowOptimaLabels
}: {
  strategy: "sequential" | "parallel"
  setStrategy: (strategy: "sequential" | "parallel") => void
  currentStep: number
  setCurrentStep: (step: number) => void
  maxSteps: number
  showOptimaLabels: boolean
  setShowOptimaLabels: (show: boolean) => void
}) => {
  return (
    <div className="bg-card flex flex-col space-y-4 rounded-lg p-4 shadow-sm">
      <div className="flex flex-col space-y-2">
        <label className="text-sm font-medium">Strategy:</label>
        <div className="flex space-x-2">
          <button
            className={`rounded-md px-3 py-1 ${
              strategy === "sequential"
                ? "bg-blue-500 text-white"
                : "bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
            }`}
            onClick={() => setStrategy("sequential")}
          >
            Sequential
          </button>
          <button
            className={`rounded-md px-3 py-1 ${
              strategy === "parallel"
                ? "bg-purple-500 text-white"
                : "bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
            }`}
            onClick={() => setStrategy("parallel")}
          >
            Parallel
          </button>
        </div>
      </div>

      <div className="flex flex-col space-y-2">
        <div className="flex justify-between">
          <label className="text-sm font-medium">Step: {currentStep}</label>
          <span className="text-muted-foreground text-sm">
            {strategy === "sequential"
              ? `Sequential update ${currentStep > 0 ? currentStep : "(initial samples)"}`
              : `${currentStep === 0 ? "Initial samples" : `Batch ${currentStep}`}`}
          </span>
        </div>
        <input
          type="range"
          min={0}
          max={maxSteps}
          value={currentStep}
          onChange={e => setCurrentStep(parseInt(e.target.value))}
          className="w-full"
        />
        <div className="text-muted-foreground flex justify-between text-xs">
          <span>Initial samples</span>
          <span>Final step</span>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="show-optima-labels"
          checked={showOptimaLabels}
          onChange={e => setShowOptimaLabels(e.target.checked)}
          className="size-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="show-optima-labels" className="text-sm font-medium">
          Show optima labels
        </label>
      </div>

      <div className="flex flex-wrap gap-2 text-xs">
        <div className="flex items-center">
          <div
            className="mr-1 size-3 rounded-full"
            style={{ backgroundColor: "#10b981" }}
          ></div>
          <span>Global minimum</span>
        </div>
        <div className="flex items-center">
          <div
            className="mr-1 size-3 rounded-full"
            style={{ backgroundColor: "#f59e0b" }}
          ></div>
          <span>Local minima</span>
        </div>
        <div className="flex items-center">
          <div
            className="mr-1 size-3 rounded-full"
            style={{ backgroundColor: "#f59e0b" }}
          ></div>
          <span>Initial samples (10 points)</span>
        </div>
        <div className="flex items-center">
          <div
            className="mr-1 size-3 rounded-full"
            style={{
              backgroundColor: strategy === "sequential" ? "#3b82f6" : "#8b5cf6"
            }}
          ></div>
          <span>
            {strategy === "sequential" ? "Sequential points" : "Batch points"}
          </span>
        </div>
      </div>
    </div>
  )
}

// Main component
const OptimizationStrategy3D = () => {
  const [strategy, setStrategy] = useState<"sequential" | "parallel">(
    "sequential"
  )
  const [currentStep, setCurrentStep] = useState(0)
  const [showOptimaLabels, setShowOptimaLabels] = useState(false)

  // Maximum steps for each strategy (now with 10 sequential steps or 2 batch steps)
  const maxSteps = strategy === "sequential" ? 10 : 2

  // Reset step when changing strategy
  useEffect(() => {
    setCurrentStep(0)
  }, [strategy])

  return (
    <div className="flex flex-col space-y-4">
      <div className="h-[450px] w-full overflow-hidden rounded-lg border">
        <Canvas camera={{ position: [8, 8, 8], fov: 45 }}>
          <Scene strategy={strategy} currentStep={currentStep} />
          {showOptimaLabels && <OptimaMarkers showLabels={true} />}
        </Canvas>
      </div>

      <VisualizationControls
        strategy={strategy}
        setStrategy={setStrategy}
        currentStep={currentStep}
        setCurrentStep={setCurrentStep}
        maxSteps={maxSteps}
        showOptimaLabels={showOptimaLabels}
        setShowOptimaLabels={setShowOptimaLabels}
      />

      <div className="text-muted-foreground text-sm">
        <p className="mb-2">
          <strong>How to use:</strong> Drag to rotate, scroll to zoom, and use
          the slider to step through the optimization process.
        </p>
        <p className="mb-2">
          This 3D visualization shows how{" "}
          {strategy === "sequential" ? "sequential" : "parallel"} Bayesian
          optimization explores the objective function. The colored surface
          represents a modified Himmelblau's function with one global minimum
          (green area) and several local minima (yellow areas).
        </p>
        <p>
          {strategy === "sequential"
            ? "In sequential mode, we start with 10 initial points, then add 10 more points one at a time, updating the model after each point."
            : "In parallel mode, we start with 10 initial points, then add 2 batches of 5 points each, updating the model after each batch."}{" "}
          The larger, brighter spheres indicate the current step in the
          optimization process.
        </p>
      </div>
    </div>
  )
}

export default OptimizationStrategy3D
