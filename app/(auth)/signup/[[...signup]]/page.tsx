/*
This client page provides the signup form from Clerk.
*/

"use client"

import { SignUp } from "@clerk/nextjs"
import { dark } from "@clerk/themes"
import { useTheme } from "next-themes"
import { useSearchParams, useRouter } from "next/navigation"
import { useUser } from "@clerk/nextjs"
import { useEffect } from "react"

export default function SignUpPage() {
  const { theme } = useTheme()
  const searchParams = useSearchParams()
  const router = useRouter()
  const { isLoaded, user } = useUser()

  // Get the redirect URL from the query string if it exists
  const baseRedirectUrl = searchParams.get("redirect_url") || "/welcome"

  // Get the plan type if it exists
  const planType = searchParams.get("plan")

  // If user is already signed in, redirect them
  useEffect(() => {
    if (isLoaded && user) {
      // Store plan type if it exists
      if (planType) {
        localStorage.setItem("selectedPlanType", planType)
      }

      // If redirecting to academic signup, mark this as academic intent
      if (baseRedirectUrl.includes("/academic-signup")) {
        localStorage.setItem("academicSignupIntent", "true")
      }

      // Redirect to the intended destination
      router.push(baseRedirectUrl)
    }
  }, [isLoaded, user, baseRedirectUrl, planType, router])

  // Store academic intent when component mounts if redirecting to academic signup
  useEffect(() => {
    if (baseRedirectUrl.includes("/academic-signup")) {
      localStorage.setItem("academicSignupIntent", "true")
    }
  }, [baseRedirectUrl])

  // Instead of redirecting directly to the final URL, redirect to our auth-redirect page
  // which will handle adding the plan parameter to the URL
  const authRedirectUrl = `/auth-redirect?redirect_url=${encodeURIComponent(baseRedirectUrl)}`

  // If we have a plan type, add it to the auth-redirect URL
  const finalRedirectUrl = planType
    ? `${authRedirectUrl}&plan=${encodeURIComponent(planType)}`
    : authRedirectUrl

  return (
    <SignUp
      redirectUrl={finalRedirectUrl}
      fallbackRedirectUrl={baseRedirectUrl}
      appearance={{ baseTheme: theme === "dark" ? dark : undefined }}
      routing="path"
      path="/signup"
      afterSignUpUrl={finalRedirectUrl}
    />
  )
}
