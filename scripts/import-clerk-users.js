#!/usr/bin/env node

/**
 * <PERSON>ript to import users from CSV to Clerk
 * This script reads the exported user CS<PERSON> and imports users to the new Clerk instance
 * using their existing IDs as external IDs to maintain database compatibility.
 * 
 * Usage: node scripts/import-clerk-users.js
 */

require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const csv = require('csv-parser');
const { createClerkClient } = require('@clerk/backend');

// Initialize Clerk with your credentials
const clerk = createClerkClient({
  secretKey: '**************************************************'
});

// Configuration
const CSV_FILE_PATH = 'ins_2tnpF5wYJCReSjzrJ7ocPvZFRCb (1).csv';
const BATCH_SIZE = 10; // Process users in batches to avoid rate limits
const DELAY_BETWEEN_BATCHES = 2000; // 2 seconds delay between batches

// Helper function to delay execution
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to clean and validate email
function cleanEmail(email) {
  if (!email || typeof email !== 'string') return null;
  const cleaned = email.trim().toLowerCase();
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(cleaned) ? cleaned : null;
}

// Helper function to clean name
function cleanName(name) {
  if (!name || typeof name !== 'string') return null;
  return name.trim() || null;
}

// Function to create a single user in Clerk
async function createClerkUser(userData) {
  try {
    const { id, firstName, lastName, email, passwordHash } = userData;

    console.log(`Creating user: ${email} (external_id: ${id})`);

    // Based on examination of existing users, they were created WITHOUT passwords
    // Use the correct Clerk API format from documentation
    const clerkUserData = {
      externalId: id,                    // Use camelCase
      emailAddress: [email],             // Array of strings, not objects
      skipPasswordRequirement: true,     // Skip password requirement
      skipPasswordChecks: true          // Skip password validation
    };

    // Add names if available (use camelCase)
    if (firstName) clerkUserData.firstName = firstName;
    if (lastName) clerkUserData.lastName = lastName;

    // Create the user WITHOUT password first (like existing users)
    const user = await clerk.users.createUser(clerkUserData);

    console.log(`✅ Successfully created user: ${email} (Clerk ID: ${user.id})`);

    // If we have a password hash, try to set it after user creation
    if (passwordHash) {
      try {
        console.log(`  🔐 Setting password hash for ${email}...`);

        // Try to update the user with password digest (use camelCase)
        await clerk.users.updateUser(user.id, {
          passwordDigest: passwordHash,
          passwordHasher: 'bcrypt'
        });

        console.log(`  ✅ Password hash set for ${email}`);
      } catch (passwordError) {
        console.log(`  ⚠️  Could not set password hash for ${email}: ${passwordError.message}`);
        // Continue anyway - user is created, just without the password hash
      }
    }

    return { success: true, user, originalId: id, passwordSet: !!passwordHash };

  } catch (error) {
    console.error(`❌ Failed to create user ${userData.email}:`, error.message);

    // Log detailed error for debugging
    if (error.errors) {
      console.error(`   Detailed errors:`, JSON.stringify(error.errors, null, 2));
    }

    // Check if user already exists
    if (error.message.includes('already exists') || error.message.includes('duplicate') ||
        (error.errors && error.errors.some(e => e.code === 'form_identifier_exists'))) {
      console.log(`⚠️  User ${userData.email} already exists, skipping...`);
      return { success: false, reason: 'already_exists', originalId: userData.id };
    }

    return { success: false, error: error.message, originalId: userData.id };
  }
}

// Function to process users in batches
async function processUserBatch(users) {
  const results = [];
  
  for (const userData of users) {
    const result = await createClerkUser(userData);
    results.push(result);
    
    // Small delay between individual requests
    await delay(500);
  }
  
  return results;
}

// Main function to import users
async function importUsers() {
  console.log('🚀 Starting Clerk user import...');
  console.log(`📁 Reading CSV file: ${CSV_FILE_PATH}`);
  
  // Check if CSV file exists
  if (!fs.existsSync(CSV_FILE_PATH)) {
    console.error(`❌ CSV file not found: ${CSV_FILE_PATH}`);
    console.log('Please make sure the CSV file is in the project root directory.');
    process.exit(1);
  }

  const users = [];
  const errors = [];

  // Read and parse CSV
  return new Promise((resolve, reject) => {
    fs.createReadStream(CSV_FILE_PATH)
      .pipe(csv())
      .on('data', (row) => {
        // Clean and validate the data
        const email = cleanEmail(row.primary_email_address);
        const firstName = cleanName(row.first_name);
        const lastName = cleanName(row.last_name);
        const id = row.id?.trim();
        const passwordHash = row.password_digest?.trim();

        // Skip rows without essential data
        if (!id || !email) {
          console.log(`⚠️  Skipping row - missing ID or email:`, { id, email });
          return;
        }

        users.push({
          id,
          firstName,
          lastName,
          email,
          passwordHash: passwordHash || null
        });
      })
      .on('end', async () => {
        console.log(`📊 Found ${users.length} users to import`);
        
        if (users.length === 0) {
          console.log('❌ No valid users found in CSV');
          resolve();
          return;
        }

        // Process users in batches
        const results = {
          successful: [],
          failed: [],
          alreadyExists: []
        };

        for (let i = 0; i < users.length; i += BATCH_SIZE) {
          const batch = users.slice(i, i + BATCH_SIZE);
          console.log(`\n📦 Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(users.length / BATCH_SIZE)} (${batch.length} users)`);
          
          const batchResults = await processUserBatch(batch);
          
          // Categorize results
          batchResults.forEach(result => {
            if (result.success) {
              results.successful.push(result);
            } else if (result.reason === 'already_exists') {
              results.alreadyExists.push(result);
            } else {
              results.failed.push(result);
            }
          });

          // Delay between batches (except for the last batch)
          if (i + BATCH_SIZE < users.length) {
            console.log(`⏳ Waiting ${DELAY_BETWEEN_BATCHES}ms before next batch...`);
            await delay(DELAY_BETWEEN_BATCHES);
          }
        }

        // Print summary
        console.log('\n📈 Import Summary:');
        console.log(`✅ Successfully imported: ${results.successful.length} users`);
        console.log(`⚠️  Already existed: ${results.alreadyExists.length} users`);
        console.log(`❌ Failed: ${results.failed.length} users`);
        console.log(`📊 Total processed: ${users.length} users`);

        // Log failed imports for review
        if (results.failed.length > 0) {
          console.log('\n❌ Failed imports:');
          results.failed.forEach(failure => {
            console.log(`  - ${failure.originalId}: ${failure.error}`);
          });
        }

        // Save detailed results to file
        const reportData = {
          timestamp: new Date().toISOString(),
          summary: {
            total: users.length,
            successful: results.successful.length,
            alreadyExists: results.alreadyExists.length,
            failed: results.failed.length
          },
          successful: results.successful.map(r => ({ originalId: r.originalId, clerkId: r.user?.id })),
          alreadyExists: results.alreadyExists.map(r => ({ originalId: r.originalId })),
          failed: results.failed.map(r => ({ originalId: r.originalId, error: r.error }))
        };

        fs.writeFileSync('clerk-import-report.json', JSON.stringify(reportData, null, 2));
        console.log('\n📄 Detailed report saved to: clerk-import-report.json');

        console.log('\n🎉 Import process completed!');
        resolve();
      })
      .on('error', (error) => {
        console.error('❌ Error reading CSV file:', error);
        reject(error);
      });
  });
}

// Run the import
if (require.main === module) {
  importUsers()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { importUsers };
