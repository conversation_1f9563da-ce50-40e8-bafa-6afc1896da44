import ExcelJS from "exceljs"
import { OptimizationConfig } from "@/types/optimization-types"

/**
 * Generate an Excel template with advanced features for optimization experiments
 */
export async function generateExcelTemplate(
  optimizationConfig: OptimizationConfig,
  filename: string = "experiment-template.xlsx"
): Promise<{ buffer: ArrayBuffer; filename: string }> {
  console.log("[EXCEL_GEN] Starting Excel template generation")
  console.log(
    "[EXCEL_GEN] Parameters:",
    optimizationConfig.parameters?.length || 0
  )
  console.log(
    "[EXCEL_GEN] Targets:",
    Array.isArray(optimizationConfig.target_config)
      ? optimizationConfig.target_config.length
      : 1
  )

  try {
    console.log("[EXCEL_GEN] Attempting advanced template generation")
    const result = await generateAdvancedExcelTemplate(
      optimizationConfig,
      filename
    )
    console.log("[EXCEL_GEN] Advanced template generated successfully")
    return result
  } catch (error) {
    console.warn(
      "[EXCEL_GEN] Advanced Excel template generation failed, falling back to basic template:",
      error
    )
    try {
      const result = await generateBasicExcelTemplate(
        optimizationConfig,
        filename
      )
      console.log("[EXCEL_GEN] Basic template generated successfully")
      return result
    } catch (basicError) {
      console.error(
        "[EXCEL_GEN] Basic template generation also failed:",
        basicError
      )
      throw basicError
    }
  }
}

async function generateAdvancedExcelTemplate(
  optimizationConfig: OptimizationConfig,
  filename: string
): Promise<{ buffer: ArrayBuffer; filename: string }> {
  const workbook = new ExcelJS.Workbook()

  // Set workbook properties for better compatibility
  workbook.creator = "BOapp Optimization System"
  workbook.lastModifiedBy = "BOapp"
  workbook.created = new Date()
  workbook.modified = new Date()

  const worksheet = workbook.addWorksheet("Experiments")

  const parameters = optimizationConfig.parameters
  const targetConfigs = Array.isArray(optimizationConfig.target_config)
    ? optimizationConfig.target_config
    : [optimizationConfig.target_config]

  // Create headers
  const headers = [
    ...parameters.map(p => p.name.trim()),
    ...targetConfigs.map(t => t.name.trim())
  ]

  // Set up header row
  const headerRow = worksheet.getRow(1)
  headers.forEach((header, index) => {
    const cell = headerRow.getCell(index + 1)
    cell.value = header

    // Header styling
    cell.font = { bold: true, color: { argb: "FFFFFFFF" } }
    cell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: index < parameters.length ? "FF0066CC" : "FF009900" }
    }
    cell.border = {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" }
    }
    cell.alignment = { horizontal: "center", vertical: "middle" }
  })

  // Auto-size columns and set minimum width
  headers.forEach((header, index) => {
    const column = worksheet.getColumn(index + 1)
    column.width = Math.max(header.length + 2, 12)
  })

  // Add advanced data validation and formatting
  parameters.forEach((param, paramIndex) => {
    const columnLetter = String.fromCharCode(65 + paramIndex) // A, B, C, etc.
    const dataRange = `${columnLetter}2:${columnLetter}500` // Allow up to 500 rows

    // Add comments to header cells with parameter information
    const headerCell = worksheet.getCell(`${columnLetter}1`)
    let comment = `Parameter: ${param.name}\nType: ${param.type}\n`

    if (
      param.type === "CategoricalParameter" &&
      "values" in param &&
      param.values
    ) {
      comment += `Allowed values: ${param.values.join(", ")}`

      // Add dropdown data validation for categorical parameters
      try {
        ;(worksheet as any).dataValidations.add(dataRange, {
          type: "list",
          allowBlank: false,
          formulae: [`"${param.values.join(",")}"`],
          showErrorMessage: true,
          errorStyle: "error",
          errorTitle: "Invalid Value",
          error: `Please select one of: ${param.values.join(", ")}`
        })

        // Skip conditional formatting for better Office compatibility
        // Microsoft Office sometimes removes complex conditional formatting
      } catch (error) {
        console.warn(
          "Could not add data validation for categorical parameter:",
          param.name
        )
      }
    } else if (
      param.type === "NumericalDiscrete" &&
      "values" in param &&
      param.values
    ) {
      comment += `Allowed values: ${param.values.join(", ")}`

      // Add dropdown data validation for numerical discrete
      try {
        const numValues = param.values.map(v => String(v))
        ;(worksheet as any).dataValidations.add(dataRange, {
          type: "list",
          allowBlank: false,
          formulae: [`"${numValues.join(",")}"`],
          showErrorMessage: true,
          errorStyle: "error",
          errorTitle: "Invalid Value",
          error: `Please select one of: ${numValues.join(", ")}`
        })

        // Skip conditional formatting for better Office compatibility
      } catch (error) {
        console.warn(
          "Could not add data validation for numerical discrete parameter:",
          param.name
        )
      }
    } else if (
      param.type === "NumericalContinuous" &&
      "bounds" in param &&
      param.bounds
    ) {
      const [min, max] = param.bounds
      comment += `Range: ${min} to ${max}`

      // Add range validation for continuous parameters
      try {
        ;(worksheet as any).dataValidations.add(dataRange, {
          type: "decimal",
          operator: "between",
          allowBlank: false,
          formulae: [min, max],
          showErrorMessage: true,
          errorStyle: "error",
          errorTitle: "Value Out of Range",
          error: `Value must be between ${min} and ${max}`
        })

        // Skip conditional formatting for better Office compatibility
      } catch (error) {
        console.warn(
          "Could not add data validation for numerical continuous parameter:",
          param.name
        )
      }
    }

    // Add comment to header cell
    try {
      headerCell.note = comment
    } catch (error) {
      console.warn("Could not add comment to header cell:", param.name)
    }
  })

  // Add validation and formatting for target columns
  targetConfigs.forEach((target, targetIndex) => {
    const columnIndex = parameters.length + targetIndex
    const columnLetter = String.fromCharCode(65 + columnIndex)
    const dataRange = `${columnLetter}2:${columnLetter}500`

    // Add comment to target header
    const headerCell = worksheet.getCell(`${columnLetter}1`)
    let comment = `Target: ${target.name}\nObjective: ${target.mode === "MAX" ? "Maximize" : "Minimize"}\n`

    if (target.bounds) {
      const [min, max] = target.bounds
      comment += `Expected range: ${min} to ${max}`

      // Add range validation for targets with bounds
      try {
        ;(worksheet as any).dataValidations.add(dataRange, {
          type: "decimal",
          operator: "between",
          allowBlank: false,
          formulae: [min, max],
          showErrorMessage: true,
          errorStyle: "error",
          errorTitle: "Target Value Out of Range",
          error: `Target value must be between ${min} and ${max}`
        })
      } catch (error) {
        console.warn("Could not add data validation for target:", target.name)
      }
    } else {
      comment += "Enter numerical values"

      // Add general numerical validation for targets without bounds
      try {
        ;(worksheet as any).dataValidations.add(dataRange, {
          type: "decimal",
          allowBlank: false,
          showErrorMessage: true,
          errorStyle: "error",
          errorTitle: "Invalid Target Value",
          error: "Please enter a valid number"
        })
      } catch (error) {
        console.warn("Could not add data validation for target:", target.name)
      }
    }

    // Skip conditional formatting for better Office compatibility

    try {
      headerCell.note = comment
    } catch (error) {
      console.warn("Could not add comment to target header:", target.name)
    }
  })

  // Generate example data rows
  for (let i = 0; i < 3; i++) {
    const row = worksheet.getRow(i + 2)

    // Add parameter example values
    parameters.forEach((param, paramIndex) => {
      const cell = row.getCell(paramIndex + 1)

      if (
        param.type === "NumericalDiscrete" &&
        "values" in param &&
        param.values
      ) {
        cell.value = param.values[i % param.values.length]
      } else if (
        param.type === "NumericalContinuous" &&
        "bounds" in param &&
        param.bounds
      ) {
        const [min, max] = param.bounds
        const value = min + ((max - min) * (i + 1)) / 4
        cell.value = Number(value.toFixed(2))
      } else if (
        param.type === "CategoricalParameter" &&
        "values" in param &&
        param.values
      ) {
        cell.value = param.values[i % param.values.length]
      } else {
        cell.value = `value_${i + 1}`
      }

      // Style data cells
      cell.border = {
        top: { style: "thin", color: { argb: "FFE0E0E0" } },
        left: { style: "thin", color: { argb: "FFE0E0E0" } },
        bottom: { style: "thin", color: { argb: "FFE0E0E0" } },
        right: { style: "thin", color: { argb: "FFE0E0E0" } }
      }
    })

    // Add target example values
    targetConfigs.forEach((target, targetIndex) => {
      const cell = row.getCell(parameters.length + targetIndex + 1)
      cell.value = Number((25.0 + i * 5).toFixed(2))

      // Style target cells
      cell.border = {
        top: { style: "thin", color: { argb: "FFE0E0E0" } },
        left: { style: "thin", color: { argb: "FFE0E0E0" } },
        bottom: { style: "thin", color: { argb: "FFE0E0E0" } },
        right: { style: "thin", color: { argb: "FFE0E0E0" } }
      }
    })
  }

  // Freeze the header row for better usability
  worksheet.views = [
    {
      state: "frozen",
      xSplit: 0,
      ySplit: 1,
      topLeftCell: "A2",
      activeCell: "A2"
    }
  ]

  // Add worksheet protection to prevent header modification
  try {
    worksheet.protect("", {
      selectLockedCells: false,
      selectUnlockedCells: true,
      formatCells: false,
      formatColumns: false,
      formatRows: false,
      insertColumns: false,
      insertRows: true,
      insertHyperlinks: false,
      deleteColumns: false,
      deleteRows: true,
      sort: false,
      autoFilter: false,
      pivotTables: false
    })

    // Lock header cells
    headerRow.eachCell(cell => {
      cell.protection = { locked: true }
    })

    // Unlock data cells (rows 2 and beyond)
    for (let rowNum = 2; rowNum <= 500; rowNum++) {
      const row = worksheet.getRow(rowNum)
      headers.forEach((_, colIndex) => {
        const cell = row.getCell(colIndex + 1)
        cell.protection = { locked: false }
      })
    }
  } catch (error) {
    console.warn("Could not add worksheet protection:", error)
  }

  // Add instructions worksheet
  const instructionsSheet = workbook.addWorksheet("Instructions")
  instructionsSheet.getCell("A1").value = "Excel Template Instructions"
  instructionsSheet.getCell("A1").font = { bold: true, size: 16 }

  const instructions = [
    "",
    "🔬 EXCEL TEMPLATE INSTRUCTIONS",
    "",
    "📋 HOW TO USE:",
    '1. Use the "Experiments" tab to enter your experimental data',
    "2. Each row represents one complete experiment",
    "3. Blue columns are optimization parameters (inputs)",
    "4. Green columns are target values (measured results)",
    "5. Fill in ALL columns for each experiment",
    "",
    "✨ ADVANCED FEATURES:",
    "• Dropdown menus for categorical parameters (click cell to see options)",
    "• Data validation prevents invalid entries with error messages",
    "• Range validation for numerical parameters",
    "• Header row is protected from accidental changes",
    "• Hover over headers to see detailed parameter information",
    "• Frozen header row stays visible when scrolling",
    "",
    "💾 SAVING & UPLOADING:",
    "• Save as Excel (.xlsx) format",
    "• Upload the saved file to the optimization system",
    "• The system will validate your data before processing",
    "",
    "📊 PARAMETER DETAILS:",
    ...parameters.map(p => {
      let detail = `• ${p.name} (${p.type})`
      if (p.type === "CategoricalParameter" && "values" in p && p.values) {
        detail += `\n  └ Allowed values: ${p.values.join(", ")}`
      } else if ("bounds" in p && p.bounds) {
        detail += `\n  └ Valid range: ${p.bounds[0]} to ${p.bounds[1]}`
      } else if ("values" in p && p.values) {
        detail += `\n  └ Allowed values: ${p.values.join(", ")}`
      }
      return detail
    }),
    "",
    "🎯 TARGET DETAILS:",
    ...targetConfigs.map(t => {
      let detail = `• ${t.name}: ${t.mode === "MAX" ? "MAXIMIZE" : "MINIMIZE"} this value`
      if (t.bounds) {
        detail += `\n  └ Expected range: ${t.bounds[0]} to ${t.bounds[1]}`
      }
      return detail
    }),
    "",
    "⚠️ IMPORTANT NOTES:",
    "• Do not leave any cells empty",
    "• Use exact values for categorical parameters",
    "• Numerical values must be within specified ranges",
    "• Contact support if you encounter any issues"
  ]

  instructions.forEach((instruction, index) => {
    const cell = instructionsSheet.getCell(`A${index + 1}`)
    cell.value = instruction
    if (
      instruction.startsWith("Parameter Details:") ||
      instruction.startsWith("Target Details:")
    ) {
      cell.font = { bold: true }
    }
  })

  instructionsSheet.getColumn("A").width = 80

  // Generate buffer with basic options for maximum compatibility
  const buffer = await workbook.xlsx.writeBuffer()

  return {
    buffer: buffer as ArrayBuffer,
    filename: filename.replace(".csv", ".xlsx")
  }
}

async function generateBasicExcelTemplate(
  optimizationConfig: OptimizationConfig,
  filename: string
): Promise<{ buffer: ArrayBuffer; filename: string }> {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet("Experiments")

  const parameters = optimizationConfig.parameters
  const targetConfigs = Array.isArray(optimizationConfig.target_config)
    ? optimizationConfig.target_config
    : [optimizationConfig.target_config]

  // Create simple headers
  const headers = [
    ...parameters.map(p => p.name.trim()),
    ...targetConfigs.map(t => t.name.trim())
  ]

  // Set up header row with basic styling
  const headerRow = worksheet.getRow(1)
  headers.forEach((header, index) => {
    const cell = headerRow.getCell(index + 1)
    cell.value = header
    cell.font = { bold: true }
    cell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: index < parameters.length ? "FFE6F3FF" : "FFE6FFE6" }
    }
  })

  // Auto-size columns
  headers.forEach((header, index) => {
    const column = worksheet.getColumn(index + 1)
    column.width = Math.max(header.length + 2, 12)
  })

  // Add 3 example rows
  for (let i = 0; i < 3; i++) {
    const row = worksheet.getRow(i + 2)

    // Add parameter example values
    parameters.forEach((param, paramIndex) => {
      const cell = row.getCell(paramIndex + 1)

      if (
        param.type === "NumericalDiscrete" &&
        "values" in param &&
        param.values
      ) {
        cell.value = param.values[i % param.values.length]
      } else if (
        param.type === "CategoricalParameter" &&
        "values" in param &&
        param.values
      ) {
        cell.value = param.values[i % param.values.length]
      } else {
        cell.value = `value_${i + 1}`
      }
    })

    // Add target example values
    targetConfigs.forEach((target, targetIndex) => {
      const cell = row.getCell(parameters.length + targetIndex + 1)
      cell.value = Number((25.0 + i * 5).toFixed(2))
    })
  }

  // Add simple instructions in a separate worksheet
  const instructionsSheet = workbook.addWorksheet("Instructions")
  const instructions = [
    "Excel Template Instructions",
    "",
    '1. Use the "Experiments" tab to enter your data',
    "2. Each row represents one experiment",
    "3. Fill in all parameter and target values",
    "4. Save the file and upload it to the system",
    "",
    "Parameter Information:",
    ...parameters.map(p => {
      let info = `• ${p.name} (${p.type})`
      if (p.type === "CategoricalParameter" && "values" in p && p.values) {
        info += `: ${p.values.join(", ")}`
      } else if ("bounds" in p && p.bounds) {
        info += `: ${p.bounds[0]} to ${p.bounds[1]}`
      } else if ("values" in p && p.values) {
        info += `: ${p.values.join(", ")}`
      }
      return info
    }),
    "",
    "Target Information:",
    ...targetConfigs.map(
      t =>
        `• ${t.name}: ${t.mode === "MAX" ? "Maximize" : "Minimize"} this value`
    )
  ]

  instructions.forEach((instruction, index) => {
    const cell = instructionsSheet.getCell(`A${index + 1}`)
    cell.value = instruction
    if (
      index === 0 ||
      instruction.startsWith("Parameter Information:") ||
      instruction.startsWith("Target Information:")
    ) {
      cell.font = { bold: true }
    }
  })

  instructionsSheet.getColumn("A").width = 80

  // Generate buffer
  const buffer = await workbook.xlsx.writeBuffer()

  return {
    buffer: buffer as ArrayBuffer,
    filename: filename.replace(".csv", ".xlsx")
  }
}
