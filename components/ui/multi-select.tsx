"use client"

import * as React from "react"
import { Check, X, ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"

export interface MultiSelectOption {
  label: string
  value: string
  icon?: React.ComponentType<{ className?: string }>
}

interface MultiSelectProps {
  options: MultiSelectOption[]
  value: string[]
  onValueChange: (value: string[]) => void
  placeholder?: string
  maxCount?: number
  modalPopover?: boolean
  asChild?: boolean
  className?: string
}

export function MultiSelect({
  options,
  value,
  onValueChange,
  placeholder = "Select items...",
  maxCount = 3,
  modalPopover = false,
  asChild = false,
  className
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState("")

  const handleUnselect = React.useCallback(
    (item: string) => {
      onValueChange(value.filter(i => i !== item))
    },
    [onValueChange, value]
  )

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      const input = e.target as HTMLInputElement
      if (input.value === "") {
        if (e.key === "Backspace") {
          onValueChange(value.slice(0, -1))
        }
      }
    },
    [onValueChange, value]
  )

  const selectables = options.filter(option => !value.includes(option.value))

  return (
    <Popover open={open} onOpenChange={setOpen} modal={modalPopover}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between text-left font-normal",
            className
          )}
          onClick={() => setOpen(!open)}
        >
          <div className="flex flex-wrap gap-1">
            {value.length > 0 ? (
              <>
                {value.slice(0, maxCount).map(item => {
                  const option = options.find(option => option.value === item)
                  const IconComponent = option?.icon
                  return (
                    <Badge
                      variant="secondary"
                      key={item}
                      className="mb-1 mr-1"
                      onClick={e => {
                        e.preventDefault()
                        e.stopPropagation()
                        handleUnselect(item)
                      }}
                    >
                      {IconComponent && (
                        <IconComponent className="mr-1 size-4" />
                      )}
                      {option?.label}
                      <button
                        className="ring-offset-background focus:ring-ring ml-1 rounded-full outline-none focus:ring-2 focus:ring-offset-2"
                        onKeyDown={e => {
                          if (e.key === "Enter") {
                            handleUnselect(item)
                          }
                        }}
                        onMouseDown={e => {
                          e.preventDefault()
                          e.stopPropagation()
                        }}
                        onClick={e => {
                          e.preventDefault()
                          e.stopPropagation()
                          handleUnselect(item)
                        }}
                      >
                        <X className="text-muted-foreground hover:text-foreground size-3" />
                      </button>
                    </Badge>
                  )
                })}
                {value.length > maxCount && (
                  <Badge variant="secondary" className="mb-1 mr-1">
                    +{value.length - maxCount} more
                  </Badge>
                )}
              </>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>
          <ChevronDown className="size-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput
            placeholder="Search..."
            value={inputValue}
            onValueChange={setInputValue}
            onKeyDown={handleKeyDown}
          />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup className="max-h-64 overflow-auto">
              {selectables.map(option => {
                const IconComponent = option.icon
                return (
                  <CommandItem
                    key={option.value}
                    onSelect={() => {
                      setInputValue("")
                      onValueChange([...value, option.value])
                    }}
                    className="cursor-pointer"
                  >
                    <div className="flex items-center gap-2">
                      {IconComponent && <IconComponent className="size-4" />}
                      {option.label}
                    </div>
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
