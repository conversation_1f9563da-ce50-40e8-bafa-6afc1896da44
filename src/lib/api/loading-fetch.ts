/**
 * This file provides a wrapper around the fetchFromAPI function
 * that integrates with the global loading context.
 *
 * It's designed to be used on the client side only.
 */

import { fetchFromAPI } from "./baybe-client"

// This function will be initialized in a client component
let startLoadingFn: (() => void) | null = null
let stopLoadingFn: (() => void) | null = null

/**
 * Initialize the loading functions from the LoadingContext
 * This should be called in a client component that has access to the LoadingContext
 */
export function initializeLoadingFunctions(
  start: () => void,
  stop: () => void
) {
  startLoadingFn = start
  stopLoadingFn = stop
}

/**
 * Wrapper around fetchFromAPI that triggers loading state
 */
export async function fetchWithLoading(
  endpoint: string,
  method: string = "GET",
  body?: any,
  token?: string
): Promise<any> {
  // Start loading
  if (startLoadingFn) {
    startLoadingFn()
  }

  try {
    // Make the API call
    const response = await fetchFromAPI(endpoint, method, body, token)
    return response
  } finally {
    // Stop loading regardless of success or failure
    if (stopLoadingFn) {
      stopLoadingFn()
    }
  }
}
