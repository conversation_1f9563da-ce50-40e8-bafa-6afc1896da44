"use client"

import { useEffect, useRef } from "react"
import dynamic from "next/dynamic"

// Dynamically import Plotly to avoid SSR issues
// @ts-ignore - Ignoring type issues with the dynamic import
const Plot: any = dynamic(() => import("react-plotly.js"), { ssr: false })

// Function to get the appropriate color scale format for Plotly
function getColorScale(scale: string) {
  // Define custom color scales if needed
  const customScales: Record<string, Array<[number, string]>> = {
    // Example of a custom color scale
    custom_rainbow: [
      [0, "rgb(0,0,255)"],
      [0.25, "rgb(0,255,255)"],
      [0.5, "rgb(0,255,0)"],
      [0.75, "rgb(255,255,0)"],
      [1, "rgb(255,0,0)"]
    ]
  }

  // Check if we have a custom scale
  if (scale in customScales) {
    return customScales[scale]
  }

  // For built-in scales, just return the name
  console.log(`Using built-in color scale: ${scale}`)
  return scale
}

interface PlotlySurfaceProps {
  x: number[]
  y: number[]
  z: number[][]
  xLabel: string
  yLabel: string
  zLabel: string
  colorScale: string
  title: string
}

export function PlotlySurface({
  x,
  y,
  z,
  xLabel,
  yLabel,
  zLabel,
  colorScale,
  title
}: PlotlySurfaceProps) {
  // Force a new instance on every render
  const uniqueId = Math.random().toString(36).substring(2, 9)
  const plotRef = useRef<HTMLDivElement>(null)

  // Log props for debugging
  useEffect(() => {
    console.log(`PlotlySurface (${uniqueId}) props:`, {
      colorScale,
      dimensions: {
        x: x.length,
        y: y.length,
        z: z.length > 0 ? `${z.length}x${z[0].length}` : "0x0"
      }
    })

    // Log when component is mounted
    return () => {
      console.log(
        `PlotlySurface (${uniqueId}) unmounted with colorScale: ${colorScale}`
      )
    }
  }, [x, y, z, colorScale, uniqueId])

  // Log directly in render function
  console.log(
    `PlotlySurface RENDER (${uniqueId}) with colorScale: ${colorScale}`
  )

  return (
    <div ref={plotRef} className="size-full" id={`plot-container-${uniqueId}`}>
      <Plot
        key={`plot-${uniqueId}-${colorScale}`}
        data={[
          {
            type: "surface",
            x,
            y,
            z,
            // Use a more explicit approach for setting the color scale
            colorscale: getColorScale(colorScale),
            // Add a comment with the colorscale for debugging
            name: `Surface with colorscale: ${colorScale}`,
            contours: {
              z: {
                show: true,
                usecolormap: true,
                highlightcolor: "#fff",
                project: { z: true }
              }
            },
            hovertemplate:
              `${xLabel}: %{x}<br>` +
              `${yLabel}: %{y}<br>` +
              `${zLabel}: %{z}<extra></extra>`
          }
        ]}
        layout={{
          title,
          autosize: true,
          margin: { l: 65, r: 50, b: 65, t: 90 },
          scene: {
            xaxis: { title: xLabel },
            yaxis: { title: yLabel },
            zaxis: { title: zLabel },
            camera: {
              eye: { x: 1.5, y: 1.5, z: 1 }
            }
          }
        }}
        config={{
          displayModeBar: true,
          responsive: true
        }}
        style={{ width: "100%", height: "100%" }}
      />
    </div>
  )
}
