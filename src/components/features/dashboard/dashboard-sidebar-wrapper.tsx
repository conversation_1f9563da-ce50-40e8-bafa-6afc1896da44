"use server"

import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { DashboardSidebar } from "./dashboard-sidebar"
import { getActualUserId } from "@/lib/auth-utils"

export async function DashboardSidebarWrapper() {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🔍 DASHBOARD SIDEBAR - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  })

  if (!userId) {
    redirect("/login?redirect_url=/dashboard/home")
  }

  return <DashboardSidebar />
}
