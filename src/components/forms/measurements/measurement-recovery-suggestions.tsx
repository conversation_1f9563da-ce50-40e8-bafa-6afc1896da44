"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import {
  Lightbulb,
  TrendingUp,
  Settings,
  Zap,
  AlertTriangle,
  CheckCircle,
  ArrowRight
} from "lucide-react"

export interface RecoverySuggestion {
  id: string
  type:
    | "expand_bounds"
    | "relax_constraint"
    | "modify_parameter"
    | "remove_constraint"
  title: string
  description: string
  impact: {
    measurementsRecovered: number
    potentialRisks: string[]
    confidence: "low" | "medium" | "high"
  }
  action: {
    field: string
    currentValue: any
    suggestedValue: any
    autoApplicable: boolean
  }
}

export interface WhatIfScenario {
  id: string
  name: string
  changes: Array<{
    field: string
    currentValue: any
    newValue: any
  }>
  outcome: {
    measurementsRetained: number
    totalMeasurements: number
    retentionPercentage: number
    tradeOffs: string[]
  }
}

interface MeasurementRecoverySuggestionsProps {
  suggestions: RecoverySuggestion[]
  whatIfScenarios?: WhatIfScenario[]
  onApplySuggestion?: (suggestionId: string) => void
  onPreviewScenario?: (scenarioId: string) => void
  currentRetention: number
  totalMeasurements: number
}

export function MeasurementRecoverySuggestions({
  suggestions,
  whatIfScenarios = [],
  onApplySuggestion,
  onPreviewScenario,
  currentRetention,
  totalMeasurements
}: MeasurementRecoverySuggestionsProps) {
  const currentRetentionPercentage = Math.round(
    (currentRetention / totalMeasurements) * 100
  )

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case "high":
        return "text-green-600 bg-green-50 border-green-200"
      case "medium":
        return "text-yellow-600 bg-yellow-50 border-yellow-200"
      case "low":
        return "text-red-600 bg-red-50 border-red-200"
      default:
        return "text-gray-600 bg-gray-50 border-gray-200"
    }
  }

  const getSuggestionIcon = (type: RecoverySuggestion["type"]) => {
    switch (type) {
      case "expand_bounds":
        return <TrendingUp className="size-4" />
      case "relax_constraint":
        return <Settings className="size-4" />
      case "modify_parameter":
        return <Settings className="size-4" />
      case "remove_constraint":
        return <AlertTriangle className="size-4" />
      default:
        return <Lightbulb className="size-4" />
    }
  }

  return (
    <div className="space-y-4">
      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="size-5 text-blue-600" />
            Recovery Suggestions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex items-center justify-between">
            <div>
              <div className="text-muted-foreground text-sm">
                Current Retention
              </div>
              <div className="text-2xl font-bold">
                {currentRetention} / {totalMeasurements}
                <span className="text-muted-foreground ml-2 text-lg">
                  ({currentRetentionPercentage}%)
                </span>
              </div>
            </div>
            {currentRetentionPercentage < 80 && (
              <Alert className="max-w-xs">
                <AlertTriangle className="size-4" />
                <AlertDescription className="text-xs">
                  Low retention may impact optimization quality
                </AlertDescription>
              </Alert>
            )}
          </div>

          {suggestions.length === 0 ? (
            <div className="text-muted-foreground py-8 text-center">
              <CheckCircle className="mx-auto mb-2 size-8 text-green-600" />
              <p>No recovery suggestions needed</p>
              <p className="text-sm">
                Current configuration retains sufficient data
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              <h4 className="text-sm font-medium">
                Suggestions to improve data retention:
              </h4>

              {suggestions.map(suggestion => (
                <Card
                  key={suggestion.id}
                  className="border-blue-200 bg-blue-50"
                >
                  <CardContent className="pt-4">
                    <div className="space-y-3">
                      {/* Suggestion header */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-2">
                          {getSuggestionIcon(suggestion.type)}
                          <div>
                            <h5 className="text-sm font-medium">
                              {suggestion.title}
                            </h5>
                            <p className="text-muted-foreground mt-1 text-xs">
                              {suggestion.description}
                            </p>
                          </div>
                        </div>
                        <Badge
                          variant="outline"
                          className={getConfidenceColor(
                            suggestion.impact.confidence
                          )}
                        >
                          {suggestion.impact.confidence} confidence
                        </Badge>
                      </div>

                      {/* Impact preview */}
                      <div className="rounded border bg-white p-3">
                        <div className="mb-2 flex items-center justify-between">
                          <span className="text-xs font-medium">
                            Potential Impact:
                          </span>
                          <Badge variant="secondary" className="text-xs">
                            +{suggestion.impact.measurementsRecovered}{" "}
                            measurements
                          </Badge>
                        </div>

                        <div className="text-muted-foreground flex items-center gap-2 text-xs">
                          <span>{currentRetention}</span>
                          <ArrowRight className="size-3" />
                          <span className="font-medium text-green-600">
                            {currentRetention +
                              suggestion.impact.measurementsRecovered}
                          </span>
                          <span>
                            (
                            {Math.round(
                              ((currentRetention +
                                suggestion.impact.measurementsRecovered) /
                                totalMeasurements) *
                                100
                            )}
                            %)
                          </span>
                        </div>
                      </div>

                      {/* Action details */}
                      <div className="rounded border bg-white p-3">
                        <div className="text-xs">
                          <div className="mb-1 font-medium">
                            Proposed Change:
                          </div>
                          <div className="grid grid-cols-3 gap-2 text-xs">
                            <div>
                              <span className="text-muted-foreground">
                                Field:
                              </span>
                              <div className="font-mono">
                                {suggestion.action.field}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">
                                Current:
                              </span>
                              <div className="font-mono">
                                {String(suggestion.action.currentValue)}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">
                                Suggested:
                              </span>
                              <div className="font-mono text-green-600">
                                {String(suggestion.action.suggestedValue)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Risks */}
                      {suggestion.impact.potentialRisks.length > 0 && (
                        <div className="rounded border border-yellow-200 bg-yellow-50 p-3">
                          <div className="text-xs">
                            <div className="mb-1 font-medium text-yellow-800">
                              Potential Risks:
                            </div>
                            <ul className="space-y-1">
                              {suggestion.impact.potentialRisks.map(
                                (risk, index) => (
                                  <li
                                    key={index}
                                    className="flex items-start gap-1 text-yellow-700"
                                  >
                                    <AlertTriangle className="mt-0.5 size-3 shrink-0" />
                                    {risk}
                                  </li>
                                )
                              )}
                            </ul>
                          </div>
                        </div>
                      )}

                      {/* Action buttons */}
                      <div className="flex gap-2">
                        {suggestion.action.autoApplicable &&
                          onApplySuggestion && (
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() => onApplySuggestion(suggestion.id)}
                              className="h-7 text-xs"
                            >
                              <Zap className="mr-1 size-3" />
                              Apply Suggestion
                            </Button>
                          )}
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-7 text-xs"
                        >
                          Preview Impact
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* What-if scenarios */}
      {whatIfScenarios.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">What-If Scenarios</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {whatIfScenarios.map(scenario => (
                <Card
                  key={scenario.id}
                  className="border-purple-200 bg-purple-50"
                >
                  <CardContent className="pt-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h5 className="text-sm font-medium">{scenario.name}</h5>
                        <Badge variant="outline" className="text-purple-600">
                          {scenario.outcome.retentionPercentage}% retention
                        </Badge>
                      </div>

                      <div className="space-y-2 text-xs">
                        <div>
                          <span className="font-medium">Changes:</span>
                          <div className="mt-1 space-y-1">
                            {scenario.changes.map((change, index) => (
                              <div
                                key={index}
                                className="flex items-center gap-2 text-xs"
                              >
                                <span className="text-muted-foreground">
                                  {change.field}:
                                </span>
                                <span className="font-mono">
                                  {String(change.currentValue)}
                                </span>
                                <ArrowRight className="size-3" />
                                <span className="font-mono text-purple-600">
                                  {String(change.newValue)}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {scenario.outcome.tradeOffs.length > 0 && (
                          <div>
                            <span className="font-medium">Trade-offs:</span>
                            <ul className="mt-1 space-y-1">
                              {scenario.outcome.tradeOffs.map(
                                (tradeOff, index) => (
                                  <li
                                    key={index}
                                    className="text-muted-foreground"
                                  >
                                    • {tradeOff}
                                  </li>
                                )
                              )}
                            </ul>
                          </div>
                        )}
                      </div>

                      {onPreviewScenario && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onPreviewScenario(scenario.id)}
                          className="h-7 text-xs"
                        >
                          Preview Scenario
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
