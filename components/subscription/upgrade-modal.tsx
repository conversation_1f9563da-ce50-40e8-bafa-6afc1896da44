"use client"

import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON><PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"

interface UpgradeModalProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  feature?: string
}

export function UpgradeModal({
  isOpen,
  onOpenChange,
  feature = "AI-powered suggestions"
}: UpgradeModalProps) {
  const router = useRouter()

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="size-5" /> Premium Feature
          </DialogTitle>
          <DialogDescription>
            {feature} are only available for trial and premium users.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="bg-muted rounded-md p-4">
            <div className="flex">
              <div>
                <p className="text-sm">
                  Upgrade your subscription to unlock premium features that can
                  significantly improve your optimization results.
                </p>
                <ul className="mt-2 list-disc pl-5 text-sm">
                  <li>
                    Get intelligent recommendations for your next experiments
                  </li>
                  <li>
                    Reduce the number of experiments needed to find optimal
                    solutions
                  </li>
                  <li>Save time and resources with AI-guided optimization</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter className="sm:justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Maybe Later
          </Button>
          <Button
            onClick={() => {
              onOpenChange(false)
              router.push("/pricing")
            }}
          >
            <Sparkles className="mr-2 size-4" />
            Upgrade Now
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
