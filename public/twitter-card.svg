<svg width="1200" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Brand gradient matching from-primary to-blue-600 exactly -->
    <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:hsl(0, 0%, 9%);stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>

    <!-- Clean background -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fafafa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="600" fill="url(#backgroundGradient)" />
  
  <!-- Subtle decorative elements -->
  <circle cx="100" cy="100" r="40" fill="#3b82f6" opacity="0.05" />
  <circle cx="1100" cy="500" r="60" fill="#171717" opacity="0.03" />
  <circle cx="1050" cy="120" r="25" fill="#3b82f6" opacity="0.08" />

  <!-- Main brand name - clean, no filters -->
  <text x="600" y="260"
        font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
        font-weight="700"
        font-size="68"
        text-anchor="middle"
        fill="url(#brandGradient)">
    INNOptimizer™
  </text>

  <!-- Tagline -->
  <text x="600" y="330"
        font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
        font-weight="400"
        font-size="22"
        text-anchor="middle"
        fill="#737373">
    Complex optimization made simple through intelligent experimental design.
  </text>
  
  <!-- Subtle accent line -->
  <line x1="400" y1="380" x2="800" y2="380" 
        stroke="url(#brandGradient)" 
        stroke-width="2" 
        opacity="0.3" />
  
  <!-- Small decorative dots -->
  <circle cx="390" cy="380" r="3" fill="url(#brandGradient)" opacity="0.6" />
  <circle cx="810" cy="380" r="3" fill="url(#brandGradient)" opacity="0.6" />
</svg>
