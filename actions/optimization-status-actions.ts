// actions/optimization-status-actions.ts
"use server"

import { auth } from "@clerk/nextjs/server"
import { getActualUserId } from "@/lib/auth-utils"
import { ActionState, OptimizationStatus, StatusChangeReason } from "@/types"
import {
  updateOptimizationAction,
  getOptimizationByIdAction
} from "./db/optimizations-actions"
import {
  createStatusHistoryEntryAction
} from "./db/optimization-status-history-actions"
import { SelectOptimization } from "@/db/schema/optimizations-schema"

/**
 * Updates the status of an optimization with validation and history tracking
 */
export async function updateOptimizationStatusAction(
  optimizationId: string,
  newStatus: OptimizationStatus,
  reason: StatusChangeReason | string = "user_initiated"
): Promise<ActionState<SelectOptimization>> {
  const authResult = await auth()
  const { userId: clerkUserId } = authResult

  // Get the actual user ID from session claims (our custom external ID)
  const userId = await getActualUserId()

  console.log("🔍 UPDATE OPTIMIZATION STATUS - Auth details:", {
    clerkUserId,
    customUserId: authResult.sessionClaims?.userId,
    actualUserId: userId
  })

  if (!userId) {
    return {
      isSuccess: false,
      message: "You must be signed in to update optimization status"
    }
  }

  try {
    // Verify the optimization exists and belongs to the user
    const optResult = await getOptimizationByIdAction(optimizationId)
    if (!optResult.isSuccess || !optResult.data) {
      return {
        isSuccess: false,
        message: `Optimization not found or access denied: ${optResult.message}`
      }
    }

    // Verify the user owns this optimization
    if (optResult.data.userId !== userId) {
      return {
        isSuccess: false,
        message: "Access denied: This optimization does not belong to you"
      }
    }

    // Get the current status
    const currentStatus = optResult.data.status

    // Validate the status transition
    if (!isValidStatusTransition(currentStatus, newStatus)) {
      return {
        isSuccess: false,
        message: `Invalid status transition from ${currentStatus} to ${newStatus}`
      }
    }

    // Update the optimization status
    const updateResult = await updateOptimizationAction(optimizationId, {
      status: newStatus,
      updatedAt: new Date()
    })

    if (updateResult.isSuccess) {
      // Record in status history
      await createStatusHistoryEntryAction({
        optimizationId,
        previousStatus: currentStatus,
        newStatus,
        reason: reason.toString(),
        createdBy: userId
      })

      return {
        isSuccess: true,
        message: `Optimization status updated to ${newStatus}`,
        data: updateResult.data
      }
    } else {
      return updateResult
    }
  } catch (error) {
    console.error("Error updating optimization status:", error)
    return {
      isSuccess: false,
      message: `Failed to update optimization status: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}

/**
 * Validates if a status transition is allowed
 */
function isValidStatusTransition(
  currentStatus: string,
  newStatus: string
): boolean {
  // Define allowed transitions
  const allowedTransitions: Record<string, string[]> = {
    draft: ["active", "paused", "completed", "error", "failed"],
    active: ["paused", "completed", "error", "failed"],
    paused: ["active", "completed", "error", "failed"],
    completed: ["active", "paused", "error", "failed"], // Allow reopening completed optimizations
    error: ["active", "paused", "completed", "failed"],
    failed: ["active", "paused", "completed", "error"]
  }

  // Check if the transition is allowed
  return allowedTransitions[currentStatus]?.includes(newStatus) ?? false
}

/**
 * Gets the status description for a given status
 *
 * Note: This is a client-side helper function, so we need to move it out of the server actions file.
 * We'll create a separate client utility for this.
 */
