// scripts/run-migration.js
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');

// Check for required environment variable
if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is not set");
}

async function runMigration() {
  try {
    // Create database connection
    const connectionString = process.env.DATABASE_URL;
    const client = postgres(connectionString, { max: 1 });
    const db = drizzle(client);

    console.log('Connected to database');

    // Read the migration file
    const migrationPath = path.join(__dirname, '../db/migrations/0006_add_verification_token.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');

    console.log('Running migration...');

    // Execute the migration
    await db.execute(migrationSql);

    console.log('Migration completed successfully!');

    // Close the connection
    await client.end();

    console.log('Database connection closed');
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

runMigration();
