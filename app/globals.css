/*
Global styles for the app.
*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations for scientific/academic aesthetic */
@layer utilities {
  .scientific-grid {
    background-image:
      linear-gradient(rgba(59, 130, 246, 0.2) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.2) 1px, transparent 1px);
    background-size: 18px 18px;
  }

  .academic-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.25) 1.5px, transparent 0);
    background-size: 20px 20px;
  }

  /* Scientific-themed background patterns */
  .molecular-structure {
    background-image:
      radial-gradient(circle at center, rgba(59, 130, 246, 0.25) 0, rgba(59, 130, 246, 0.25) 2px, transparent 2px),
      radial-gradient(circle at center, rgba(147, 51, 234, 0.15) 0, rgba(147, 51, 234, 0.15) 1px, transparent 1px);
    background-size: 25px 25px, 12px 12px;
    background-position: 0 0, 12px 12px;
  }

  .circuit-board {
    background-image:
      linear-gradient(rgba(59, 130, 246, 0.2) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.2) 1px, transparent 1px),
      linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 30px 30px, 30px 30px, 6px 6px, 6px 6px;
  }

  .dna-helix {
    background-image:
      repeating-linear-gradient(45deg, rgba(59, 130, 246, 0.15) 0, rgba(59, 130, 246, 0.15) 2px, transparent 2px, transparent 12px),
      repeating-linear-gradient(-45deg, rgba(147, 51, 234, 0.15) 0, rgba(147, 51, 234, 0.15) 2px, transparent 2px, transparent 12px);
    background-size: 20px 20px;
  }

  .research-highlight {
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.05) 0%,
      rgba(147, 51, 234, 0.05) 50%,
      rgba(59, 130, 246, 0.05) 100%);
  }

  /* Enhanced blending utilities for carousel integration */
  .carousel-blend {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(249, 250, 251, 0.8) 25%,
      rgba(255, 255, 255, 0.7) 50%,
      rgba(249, 250, 251, 0.8) 75%,
      rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .page-integration-glow {
    box-shadow:
      0 0 40px rgba(59, 130, 246, 0.05),
      0 0 80px rgba(147, 51, 234, 0.03),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  /* Enhanced shadows for floating cards */
  .shadow-3xl {
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  @keyframes scientific-pulse {
    0%, 100% {
      opacity: 0.4;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  @keyframes data-flow {
    0% {
      transform: translateX(-100%) scaleX(0);
      opacity: 0;
    }
    50% {
      opacity: 1;
      transform: translateX(0%) scaleX(1);
    }
    100% {
      transform: translateX(100%) scaleX(0);
      opacity: 0;
    }
  }

  @keyframes research-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
    }
    50% {
      box-shadow: 0 0 30px rgba(59, 130, 246, 0.2), 0 0 40px rgba(147, 51, 234, 0.1);
    }
  }

  @keyframes lightning-pulse {
    0%, 100% {
      opacity: 0.3;
      filter: drop-shadow(0 0 4px rgba(59, 130, 246, 0.4));
    }
    25% {
      opacity: 0.8;
      filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.8));
    }
    50% {
      opacity: 1;
      filter: drop-shadow(0 0 12px rgba(59, 130, 246, 1)) drop-shadow(0 0 20px rgba(147, 51, 234, 0.5));
    }
    75% {
      opacity: 0.6;
      filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6));
    }
  }

  @keyframes electric-spark {
    0% {
      stroke-dashoffset: 0;
      opacity: 0.8;
    }
    50% {
      stroke-dashoffset: -15;
      opacity: 1;
    }
    100% {
      stroke-dashoffset: -30;
      opacity: 0.8;
    }
  }

  .scientific-pulse {
    animation: scientific-pulse 3s ease-in-out infinite;
  }

  .data-flow {
    animation: data-flow 4s ease-in-out infinite;
  }

  .research-glow {
    animation: research-glow 4s ease-in-out infinite;
  }

  .lightning-pulse {
    animation: lightning-pulse 2s ease-in-out infinite;
  }

  .electric-spark {
    animation: electric-spark 1.5s linear infinite;
  }

  /* Mobile-specific optimizations */
  @media (max-width: 768px) {
    /* Reduce motion for better mobile performance */
    .scientific-pulse,
    .data-flow,
    .research-glow {
      animation-duration: 6s; /* Slower animations on mobile */
    }

    /* Improve touch targets */
    button, a {
      min-height: 44px;
      min-width: 44px;
    }

    /* Better text readability on mobile */
    body {
      -webkit-text-size-adjust: 100%;
      text-size-adjust: 100%;
    }
  }

  /* Reduce animations for users who prefer reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .scientific-pulse,
    .data-flow,
    .research-glow {
      animation: none;
    }
  }

  /* Hero section vertical distribution - positioned high on page */
  .hero-content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    min-height: 50vh;
    padding-top: 1.5rem;
    padding-bottom: 0.5rem;
  }

  /* Ensure content positioned high on different screen sizes */
  @media (max-height: 700px) {
    .hero-content {
      padding-top: 0.75rem;
      padding-bottom: 0.25rem;
      gap: 0.75rem !important;
    }
  }

  @media (max-height: 600px) {
    .hero-content {
      padding-top: 0.5rem;
      padding-bottom: 0.25rem;
      gap: 0.5rem !important;
    }
  }

  @media (max-height: 500px) {
    .hero-content {
      padding-top: 0.25rem;
      padding-bottom: 0;
      gap: 0.25rem !important;
    }
  }

  @media (min-height: 900px) {
    .hero-content {
      padding-top: 2rem;
    }
  }

  /* Mobile-specific high positioning */
  @media (max-width: 768px) {
    .hero-content {
      padding-top: 0.75rem;
    }
  }

  @media (max-width: 768px) and (max-height: 700px) {
    .hero-content {
      padding-top: 0.5rem;
    }
  }

  /* High positioning utility */
  .hero-high-position {
    margin-top: -1rem;
  }

  @media (max-width: 768px) {
    .hero-high-position {
      margin-top: -0.5rem;
    }
  }
}
