"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Edit,
  Trash2,
  Plus,
  Eye,
  CheckCircle,
  AlertCircle,
  Info
} from "lucide-react"
import { EditMeasurementDialog } from "./edit-measurement-dialog"
import { DeleteMeasurementDialog } from "./delete-measurement-dialog"
import {
  SelectMeasurement,
  SelectOptimization
} from "@/db/schema/optimizations-schema"

interface CrudIntegrationDemoProps {
  optimization: SelectOptimization
  measurements: SelectMeasurement[]
  onMeasurementUpdated: (measurement: SelectMeasurement) => void
  onMeasurementDeleted: (measurementId: string) => void
}

export function CrudIntegrationDemo({
  optimization,
  measurements,
  onMeasurementUpdated,
  onMeasurementDeleted
}: CrudIntegrationDemoProps) {
  const [selectedMeasurement, setSelectedMeasurement] =
    useState<SelectMeasurement | null>(null)

  // Get a sample measurement for demonstration
  const sampleMeasurement = measurements[0]

  if (!sampleMeasurement) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="size-5" />
            CRUD Operations Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="size-4" />
            <AlertDescription>
              No measurements available for demonstration. Please add some
              measurements to your optimization first.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="size-5 text-green-500" />
          CRUD Operations Demo
        </CardTitle>
        <p className="text-muted-foreground text-sm">
          Demonstration of Create, Read, Update, and Delete operations for
          measurements
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Demo Measurement Display */}
        <div className="space-y-3 rounded-lg border p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Sample Measurement</h3>
            <div className="flex items-center gap-2">
              <Badge
                variant={
                  sampleMeasurement.isRecommended ? "default" : "outline"
                }
              >
                {sampleMeasurement.isRecommended
                  ? "API Generated"
                  : "Manual Entry"}
              </Badge>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Target Value:</span>{" "}
              {sampleMeasurement.targetValue}
            </div>
            <div>
              <span className="font-medium">Created:</span>{" "}
              {new Date(sampleMeasurement.createdAt).toLocaleDateString()}
            </div>
          </div>

          <div className="space-y-2">
            <span className="text-sm font-medium">Parameters:</span>
            <div className="bg-muted rounded p-2 font-mono text-xs">
              {JSON.stringify(sampleMeasurement.parameters, null, 2)}
            </div>
          </div>
        </div>

        {/* CRUD Operations */}
        <div className="space-y-4">
          <h3 className="font-medium">Available Operations</h3>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* Read Operation */}
            <Card className="p-4">
              <div className="mb-3 flex items-center gap-3">
                <Eye className="size-5 text-blue-500" />
                <div>
                  <h4 className="font-medium">Read</h4>
                  <p className="text-muted-foreground text-sm">
                    View measurement data
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedMeasurement(sampleMeasurement)}
                className="w-full"
              >
                <Eye className="mr-2 size-4" />
                View Details
              </Button>
            </Card>

            {/* Update Operation */}
            <Card className="p-4">
              <div className="mb-3 flex items-center gap-3">
                <Edit className="size-5 text-orange-500" />
                <div>
                  <h4 className="font-medium">Update</h4>
                  <p className="text-muted-foreground text-sm">
                    Edit parameters and values
                  </p>
                </div>
              </div>
              <EditMeasurementDialog
                measurement={sampleMeasurement}
                optimization={optimization}
                onMeasurementUpdated={onMeasurementUpdated}
                trigger={
                  <Button variant="outline" size="sm" className="w-full">
                    <Edit className="mr-2 size-4" />
                    Edit Measurement
                  </Button>
                }
              />
            </Card>

            {/* Delete Operation */}
            <Card className="p-4">
              <div className="mb-3 flex items-center gap-3">
                <Trash2 className="size-5 text-red-500" />
                <div>
                  <h4 className="font-medium">Delete</h4>
                  <p className="text-muted-foreground text-sm">
                    Remove measurement
                  </p>
                </div>
              </div>
              <DeleteMeasurementDialog
                measurement={sampleMeasurement}
                optimization={optimization}
                experimentNumber="Demo"
                onMeasurementDeleted={onMeasurementDeleted}
                totalMeasurements={measurements.length}
                trigger={
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="mr-2 size-4" />
                    Delete Measurement
                  </Button>
                }
              />
            </Card>

            {/* Create Operation Info */}
            <Card className="p-4">
              <div className="mb-3 flex items-center gap-3">
                <Plus className="size-5 text-green-500" />
                <div>
                  <h4 className="font-medium">Create</h4>
                  <p className="text-muted-foreground text-sm">
                    Add new measurements
                  </p>
                </div>
              </div>
              <Button variant="outline" size="sm" className="w-full" disabled>
                <Plus className="mr-2 size-4" />
                Use Suggestions Tab
              </Button>
            </Card>
          </div>
        </div>

        {/* Features List */}
        <div className="space-y-3">
          <h3 className="font-medium">Implementation Features</h3>
          <div className="grid grid-cols-1 gap-2 text-sm md:grid-cols-2">
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4 text-green-500" />
              <span>Form validation</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4 text-green-500" />
              <span>Dependency handling</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4 text-green-500" />
              <span>Audit trail logging</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4 text-green-500" />
              <span>Error handling</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4 text-green-500" />
              <span>Optimistic updates</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4 text-green-500" />
              <span>Permission checks</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4 text-green-500" />
              <span>Multi-target support</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4 text-green-500" />
              <span>Best point recalculation</span>
            </div>
          </div>
        </div>

        {/* Selected Measurement Details */}
        {selectedMeasurement && (
          <Alert>
            <Info className="size-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p>
                  <strong>Selected Measurement Details:</strong>
                </p>
                <p>
                  <strong>ID:</strong> {selectedMeasurement.id}
                </p>
                <p>
                  <strong>Target Value:</strong>{" "}
                  {selectedMeasurement.targetValue}
                </p>
                <p>
                  <strong>Source:</strong>{" "}
                  {selectedMeasurement.isRecommended
                    ? "API Generated"
                    : "Manual Entry"}
                </p>
                <p>
                  <strong>Created:</strong>{" "}
                  {new Date(selectedMeasurement.createdAt).toLocaleString()}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedMeasurement(null)}
                  className="mt-2"
                >
                  Close Details
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
