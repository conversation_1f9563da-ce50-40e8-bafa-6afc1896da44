// lib/constraints/validation.ts
// Centralized constraint validation logic

import {
  Constraint,
  Parameter,
  ValidationResult,
  ConstraintValidationResult,
  GlobalValidationResult
} from "./types"
import { validateConstraintParameterCompatibility } from "./utils"

/**
 * Validate a single constraint configuration
 */
export async function validateSingleConstraint(
  constraint: Constraint,
  parameters: Parameter[]
): Promise<ValidationResult> {
  try {
    // Basic validation
    if (!constraint.type) {
      return { valid: false, error: "Constraint must specify a type" }
    }

    if (!constraint.parameters || constraint.parameters.length === 0) {
      return { valid: false, error: "Constraint must specify parameters" }
    }

    // Check parameter compatibility
    const compatibilityResult = validateConstraintParameterCompatibility(
      constraint,
      parameters
    )
    if (!compatibilityResult.valid) {
      return { valid: false, error: compatibilityResult.error }
    }

    // Type-specific validation
    const typeValidationResult = await validateConstraintByType(
      constraint,
      parameters
    )
    if (!typeValidationResult.valid) {
      return typeValidationResult
    }

    // Call backend validation API
    const backendValidationResult = await validateConstraintWithBackend(
      constraint,
      parameters
    )
    return backendValidationResult
  } catch (error) {
    console.error("Constraint validation error:", error)
    return {
      valid: false,
      error: error instanceof Error ? error.message : "Validation failed"
    }
  }
}

/**
 * Validate all constraints in a collection
 */
export async function validateAllConstraints(
  constraints: Constraint[],
  parameters: Parameter[]
): Promise<GlobalValidationResult> {
  try {
    const errors: ConstraintValidationResult[] = []
    const warnings: string[] = []

    // Validate each constraint individually
    for (const constraint of constraints) {
      if (!constraint.id) continue

      const result = await validateSingleConstraint(constraint, parameters)

      if (!result.valid) {
        errors.push({
          constraintId: constraint.id,
          valid: false,
          error: result.error,
          message: result.message
        })
      }
    }

    // Check for global constraint conflicts
    const conflictWarnings = checkConstraintConflicts(constraints, parameters)
    warnings.push(...conflictWarnings)

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  } catch (error) {
    console.error("Global constraint validation error:", error)
    return {
      valid: false,
      errors: [
        {
          constraintId: "global",
          valid: false,
          error:
            error instanceof Error ? error.message : "Global validation failed"
        }
      ],
      warnings: []
    }
  }
}

/**
 * Helper function to validate parameter type compatibility with constraint type
 */
function validateParameterTypeCompatibility(
  constraint: Constraint,
  parameters: Parameter[]
): ValidationResult {
  const parameterLookup = new Map(parameters.map(p => [p.name, p]))

  // Check if all constraint parameters exist
  const missingParams = constraint.parameters.filter(
    name => !parameterLookup.has(name)
  )
  if (missingParams.length > 0) {
    return {
      valid: false,
      error: `Parameters not found: ${missingParams.join(", ")}`
    }
  }

  // Define constraint type categories
  const discreteConstraintTypes = [
    "DiscreteExcludeConstraint",
    "DiscreteCardinalityConstraint",
    "DiscreteSumConstraint",
    "DiscreteProductConstraint",
    "DiscreteDependenciesConstraint",
    "DiscreteLinkedParametersConstraint",
    "DiscreteNoLabelDuplicatesConstraint",
    "DiscretePermutationInvarianceConstraint",
    "DiscreteCustomConstraint",
    "DiscreteLinearConstraint"
  ]

  const continuousConstraintTypes = [
    "ContinuousLinearConstraint",
    "ContinuousCardinalityConstraint"
  ]

  // Check parameter type compatibility for discrete constraints
  if (discreteConstraintTypes.includes(constraint.type)) {
    const incompatibleParams = constraint.parameters.filter(paramName => {
      const param = parameterLookup.get(paramName)
      return param && param.type === "NumericalContinuous"
    })

    if (incompatibleParams.length > 0) {
      return {
        valid: false,
        error: `Discrete constraint "${constraint.type}" cannot be used with continuous parameters: ${incompatibleParams.join(", ")}. Please change these parameters to "NumericalDiscrete" or "Categorical" type, or use a continuous constraint type instead.`
      }
    }
  }

  // Check parameter type compatibility for continuous constraints
  if (continuousConstraintTypes.includes(constraint.type)) {
    const incompatibleParams = constraint.parameters.filter(paramName => {
      const param = parameterLookup.get(paramName)
      return (
        param &&
        (param.type === "NumericalDiscrete" ||
          param.type === "CategoricalParameter")
      )
    })

    if (incompatibleParams.length > 0) {
      return {
        valid: false,
        error: `Continuous constraint "${constraint.type}" cannot be used with discrete parameters: ${incompatibleParams.join(", ")}. Please change these parameters to "NumericalContinuous" type, or use a discrete constraint type instead.`
      }
    }
  }

  return { valid: true }
}

/**
 * Validate constraint by its specific type
 */
async function validateConstraintByType(
  constraint: Constraint,
  parameters: Parameter[]
): Promise<ValidationResult> {
  // First check parameter type compatibility
  const compatibilityResult = validateParameterTypeCompatibility(
    constraint,
    parameters
  )
  if (!compatibilityResult.valid) {
    return compatibilityResult
  }

  // Then validate constraint-specific logic
  switch (constraint.type) {
    case "ContinuousLinearConstraint":
      return validateLinearConstraint(constraint, parameters)

    case "DiscreteLinearConstraint":
      return validateDiscreteLinearConstraint(constraint, parameters)

    case "ContinuousCardinalityConstraint":
    case "DiscreteCardinalityConstraint":
      return validateCardinalityConstraint(constraint, parameters)

    case "DiscreteExcludeConstraint":
      return validateExcludeConstraint(constraint, parameters)

    case "DiscreteSumConstraint":
    case "DiscreteProductConstraint":
      return validateMathematicalConstraint(constraint, parameters)

    case "DiscreteDependenciesConstraint":
      return validateDependenciesConstraint(constraint, parameters)

    case "DiscreteCustomConstraint":
      return validateCustomConstraint(constraint, parameters)

    default:
      return { valid: true } // Allow unknown types to pass through to backend
  }
}

/**
 * Validate linear constraint
 */
function validateLinearConstraint(
  constraint: Constraint & { type: "ContinuousLinearConstraint" },
  parameters: Parameter[]
): ValidationResult {
  if (!constraint.coefficients || constraint.coefficients.length === 0) {
    return {
      valid: false,
      error: "Linear constraint must specify coefficients"
    }
  }

  if (constraint.parameters.length !== constraint.coefficients.length) {
    return {
      valid: false,
      error: "Number of parameters must match number of coefficients"
    }
  }

  if (!constraint.operator) {
    return { valid: false, error: "Linear constraint must specify an operator" }
  }

  if (constraint.rhs === undefined || constraint.rhs === null) {
    return {
      valid: false,
      error: "Linear constraint must specify right-hand side value"
    }
  }

  return { valid: true }
}

/**
 * Validate discrete linear constraint
 */
function validateDiscreteLinearConstraint(
  constraint: Constraint & { type: "DiscreteLinearConstraint" },
  parameters: Parameter[]
): ValidationResult {
  if (!constraint.coefficients || constraint.coefficients.length === 0) {
    return {
      valid: false,
      error: "Discrete linear constraint must specify coefficients"
    }
  }

  if (constraint.parameters.length !== constraint.coefficients.length) {
    return {
      valid: false,
      error: "Number of parameters must match number of coefficients"
    }
  }

  if (!constraint.operator) {
    return {
      valid: false,
      error: "Discrete linear constraint must specify an operator"
    }
  }

  if (constraint.rhs === undefined || constraint.rhs === null) {
    return {
      valid: false,
      error: "Discrete linear constraint must specify right-hand side value"
    }
  }

  // Check that all parameters are discrete
  const parameterNames = new Set(parameters.map(p => p.name))
  for (const paramName of constraint.parameters) {
    if (!parameterNames.has(paramName)) {
      return { valid: false, error: `Parameter '${paramName}' not found` }
    }

    const param = parameters.find(p => p.name === paramName)
    if (param?.type !== "NumericalDiscrete") {
      return {
        valid: false,
        error: `Parameter '${paramName}' must be discrete for discrete linear constraint`
      }
    }
  }

  return { valid: true }
}

/**
 * Validate cardinality constraint
 */
function validateCardinalityConstraint(
  constraint: Constraint & {
    type: "ContinuousCardinalityConstraint" | "DiscreteCardinalityConstraint"
  },
  parameters: Parameter[]
): ValidationResult {
  const minCard = constraint.min_cardinality
  const maxCard = constraint.max_cardinality

  if (minCard !== undefined && minCard < 0) {
    return { valid: false, error: "Minimum cardinality cannot be negative" }
  }

  if (maxCard !== undefined && maxCard < 0) {
    return { valid: false, error: "Maximum cardinality cannot be negative" }
  }

  if (minCard !== undefined && maxCard !== undefined && minCard > maxCard) {
    return {
      valid: false,
      error: "Minimum cardinality cannot exceed maximum cardinality"
    }
  }

  if (maxCard !== undefined && maxCard > constraint.parameters.length) {
    return {
      valid: false,
      error: `Maximum cardinality (${maxCard}) cannot exceed number of parameters (${constraint.parameters.length})`
    }
  }

  return { valid: true }
}

/**
 * Validate exclude constraint
 */
function validateExcludeConstraint(
  constraint: Constraint & { type: "DiscreteExcludeConstraint" },
  parameters: Parameter[]
): ValidationResult {
  if (!constraint.combiner) {
    return {
      valid: false,
      error: "Exclude constraint must specify a combiner (AND/OR)"
    }
  }

  if (!constraint.conditions || constraint.conditions.length === 0) {
    return { valid: false, error: "Exclude constraint must specify conditions" }
  }

  // Validate each condition
  for (const condition of constraint.conditions) {
    if (!condition.type) {
      return { valid: false, error: "Each condition must specify a type" }
    }

    if (condition.type === "threshold" && condition.threshold === undefined) {
      return {
        valid: false,
        error: "Threshold condition must specify a threshold value"
      }
    }

    if (
      condition.type === "subselection" &&
      (!condition.selection || condition.selection.length === 0)
    ) {
      return {
        valid: false,
        error: "Subselection condition must specify selection values"
      }
    }
  }

  return { valid: true }
}

/**
 * Validate mathematical constraint (sum/product)
 */
function validateMathematicalConstraint(
  constraint: Constraint & {
    type: "DiscreteSumConstraint" | "DiscreteProductConstraint"
  },
  parameters: Parameter[]
): ValidationResult {
  if (!constraint.condition) {
    return {
      valid: false,
      error: "Mathematical constraint must specify a condition"
    }
  }

  const condition = constraint.condition
  if (!condition.type) {
    return { valid: false, error: "Condition must specify a type" }
  }

  if (condition.type === "threshold" && condition.threshold === undefined) {
    return {
      valid: false,
      error: "Threshold condition must specify a threshold value"
    }
  }

  return { valid: true }
}

/**
 * Validate dependencies constraint
 */
function validateDependenciesConstraint(
  constraint: Constraint & { type: "DiscreteDependenciesConstraint" },
  parameters: Parameter[]
): ValidationResult {
  if (
    !constraint.affected_parameters ||
    constraint.affected_parameters.length === 0
  ) {
    return {
      valid: false,
      error: "Dependencies constraint must specify affected parameters"
    }
  }

  // Check that all affected parameters exist
  const parameterNames = new Set(parameters.map(p => p.name))
  for (const paramGroup of constraint.affected_parameters) {
    for (const paramName of paramGroup) {
      if (!parameterNames.has(paramName)) {
        return {
          valid: false,
          error: `Affected parameter '${paramName}' not found`
        }
      }
    }
  }

  return { valid: true }
}

/**
 * Validate custom constraint
 */
function validateCustomConstraint(
  constraint: Constraint & { type: "DiscreteCustomConstraint" },
  parameters: Parameter[]
): ValidationResult {
  if (!constraint.validator || constraint.validator.trim() === "") {
    return {
      valid: false,
      error: "Custom constraint must specify a validator function"
    }
  }

  // Basic syntax check for the validator
  try {
    // This is a simple check - the backend will do more thorough validation
    if (
      !constraint.validator.includes("def ") &&
      !constraint.validator.includes("lambda ")
    ) {
      return {
        valid: false,
        error:
          "Validator must be a valid Python function (def) or lambda expression"
      }
    }
  } catch (error) {
    return { valid: false, error: "Invalid validator syntax" }
  }

  return { valid: true }
}

/**
 * Check for conflicts between constraints
 */
function checkConstraintConflicts(
  constraints: Constraint[],
  parameters: Parameter[]
): string[] {
  const warnings: string[] = []

  // Check for conflicting cardinality constraints
  const cardinalityConstraints = constraints.filter(
    c =>
      c.type === "ContinuousCardinalityConstraint" ||
      c.type === "DiscreteCardinalityConstraint"
  )

  if (cardinalityConstraints.length > 1) {
    warnings.push(
      "Multiple cardinality constraints detected - ensure they don't conflict"
    )
  }

  // Check for overlapping linear constraints
  const linearConstraints = constraints.filter(
    c => c.type === "ContinuousLinearConstraint"
  )
  if (linearConstraints.length > 1) {
    // This is a simplified check - more sophisticated conflict detection could be added
    warnings.push("Multiple linear constraints detected - verify feasibility")
  }

  return warnings
}

/**
 * Validate constraint with backend API
 */
async function validateConstraintWithBackend(
  constraint: Constraint,
  parameters: Parameter[]
): Promise<ValidationResult> {
  try {
    const parameterNames = parameters
      .map(p => p.name)
      .filter(name => name.trim() !== "")
    const parameterTypes = parameters.reduce(
      (acc, p) => {
        if (p.name.trim() !== "") {
          acc[p.name] = p.type
        }
        return acc
      },
      {} as Record<string, string>
    )

    const response = await fetch("/api/proxy/constraints/validate", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        constraint_config: constraint,
        parameter_names: parameterNames,
        parameter_types: parameterTypes
      })
    })

    if (!response.ok) {
      throw new Error(`Validation request failed: ${response.statusText}`)
    }

    const result = await response.json()

    return {
      valid: result.valid,
      error: result.error,
      message: result.message
    }
  } catch (error) {
    console.error("Backend constraint validation error:", error)
    return {
      valid: false,
      error:
        error instanceof Error ? error.message : "Backend validation failed"
    }
  }
}
