"use client"

import { useState } from "react"
import { Controller, Control, useWatch } from "react-hook-form"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { InfoIcon, RefreshCw, Lightbulb } from "lucide-react"

interface AcquisitionFunctionControlsProps {
  control: Control<any>
  selectedAcquisitionFunction: string
  onAcquisitionFunctionChange: (value: string) => void
  formErrors: any
  objectiveType?: string
}

export function AcquisitionFunctionControls({
  control,
  selectedAcquisitionFunction,
  onAcquisitionFunctionChange,
  formErrors,
  objectiveType = "SINGLE"
}: AcquisitionFunctionControlsProps) {
  // Define which acquisition functions are available for each objective type
  const getAvailableFunctions = (objType: string) => {
    if (objType === "SINGLE") {
      return [
        { value: "qExpectedImprovement", label: "qEI" },
        {
          value: "qProbabilityOfImprovement",
          label: "qPI"
        },
        {
          value: "qUpperConfidenceBound",
          label: "qUCB"
        }
      ]
    } else if (objType === "MULTI_DESIRABILITY") {
      return [
        { value: "qExpectedImprovement", label: "qEI" },
        {
          value: "qProbabilityOfImprovement",
          label: "qPI"
        },
        {
          value: "qUpperConfidenceBound",
          label: "qUCB"
        }
      ]
    } else if (objType === "MULTI_PARETO") {
      return [
        {
          value: "qNoisyExpectedHypervolumeImprovement",
          label: "qNEHVI"
        },
        { value: "qLogNParEGO", label: "qLogParEGO" },
        {
          value: "qLogNoisyExpectedHypervolumeImprovement",
          label: "qLogNEHVI"
        }
      ]
    }
    return []
  }

  const availableFunctions = getAvailableFunctions(objectiveType)
  return (
    <div className="space-y-4">
      <h4 className="font-medium">Acquisition Function</h4>
      <div className="text-muted-foreground space-y-1 text-sm">
        <p>
          The acquisition function determines how the algorithm explores the
          parameter space.
        </p>
        {objectiveType === "MULTI_PARETO" && (
          <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
            <p className="font-medium text-blue-800">
              ℹ️ Pareto Objective: Only multi-objective acquisition functions
              are available for true Pareto optimization.
            </p>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="acquisitionFunction.type">Function Type</Label>
          <Controller
            name="acquisitionFunction.type"
            control={control}
            render={({ field }) => (
              <div>
                <Select
                  onValueChange={value => {
                    field.onChange(value)
                    onAcquisitionFunctionChange(value)
                  }}
                  value={field.value}
                >
                  <SelectTrigger id="acquisitionFunction.type">
                    <SelectValue placeholder="Select function type" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableFunctions.map(func => (
                      <SelectItem key={func.value} value={func.value}>
                        {func.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formErrors.acquisitionFunction?.type && (
                  <p className="text-destructive mt-1 text-sm">
                    {typeof formErrors.acquisitionFunction.type === "object" &&
                    "message" in formErrors.acquisitionFunction.type
                      ? formErrors.acquisitionFunction.type.message
                      : "Invalid acquisition function type"}
                  </p>
                )}
                <p className="text-muted-foreground mt-1 text-sm">
                  Different functions balance exploration vs. exploitation
                </p>
              </div>
            )}
          />
        </div>

        {/* Reference Point parameter - show for qNEHVI and qLogNEHVI */}
        {(selectedAcquisitionFunction ===
          "qNoisyExpectedHypervolumeImprovement" ||
          selectedAcquisitionFunction ===
            "qLogNoisyExpectedHypervolumeImprovement") && (
          <ReferencePointControls
            control={control}
            formErrors={formErrors}
            objectiveType={objectiveType}
          />
        )}

        {/* Weights parameter - show for qLogNParEGO */}
        {selectedAcquisitionFunction === "qLogNParEGO" && (
          <ObjectiveWeightsControls
            control={control}
            formErrors={formErrors}
            objectiveType={objectiveType}
          />
        )}

        {/* Beta parameter - only show for UCB */}
        {selectedAcquisitionFunction === "qUpperConfidenceBound" && (
          <div className="space-y-2">
            <Label htmlFor="acquisitionFunction.beta">Beta Parameter</Label>
            <Controller
              name="acquisitionFunction.beta"
              control={control}
              render={({ field }) => (
                <div>
                  <Input
                    id="acquisitionFunction.beta"
                    type="number"
                    step="0.1"
                    min="0.1"
                    placeholder="0.2"
                    {...field}
                    onChange={e => {
                      // Handle both comma and dot as decimal separators
                      const normalizedValue = e.target.value.replace(",", ".")
                      const numValue = parseFloat(normalizedValue)
                      field.onChange(isNaN(numValue) ? undefined : numValue)
                    }}
                    onBlur={e => {
                      // Normalize display value to use dot
                      const normalizedValue = e.target.value.replace(",", ".")
                      const numValue = parseFloat(normalizedValue)
                      if (!isNaN(numValue)) {
                        e.target.value = numValue.toString()
                      }
                    }}
                  />
                  {formErrors.acquisitionFunction?.beta && (
                    <p className="text-destructive mt-1 text-sm">
                      {typeof formErrors.acquisitionFunction.beta ===
                        "object" &&
                      "message" in formErrors.acquisitionFunction.beta
                        ? formErrors.acquisitionFunction.beta.message
                        : "Invalid beta value"}
                    </p>
                  )}
                  <p className="text-muted-foreground mt-1 text-sm">
                    Controls exploration vs exploitation trade-off. Higher
                    values encourage more exploration. Default: 0.2 (accepts
                    both 0.2 and 0,2 input formats)
                  </p>
                </div>
              )}
            />
          </div>
        )}
      </div>

      <div className="bg-muted mt-4 rounded-md p-3">
        <h5 className="mb-2 font-medium">Function Descriptions:</h5>
        <ul className="space-y-2 text-sm">
          <li>
            <strong>qEI (Expected Improvement):</strong> Balances exploration
            and exploitation automatically. Good general-purpose choice.
            <p className="text-muted-foreground mt-1">
              No additional parameters required. Uses the current best value to
              balance exploration and exploitation.
            </p>
          </li>
          <li>
            <strong>qPI (Probability of Improvement):</strong> More
            exploitative, focuses on areas likely to improve over current best.
            <p className="text-muted-foreground mt-1">
              No additional parameters required. Tends to be more conservative
              and exploitative than Expected Improvement.
            </p>
          </li>
          <li>
            <strong>qUCB (Upper Confidence Bound):</strong> Allows explicit
            control of exploration/exploitation trade-off via beta parameter.
            <p className="text-muted-foreground mt-1">
              Beta parameter directly controls exploration vs. exploitation.
              Higher values (e.g., 1.0-5.0) favor exploration, lower values
              (e.g., 0.1-0.5) favor exploitation. Default: 0.2
            </p>
          </li>
          <li>
            <strong>qNEHVI (Noisy Expected Hypervolume Improvement):</strong>
            Multi-objective acquisition function for noisy optimization.
            <p className="text-muted-foreground mt-1">
              Handles measurement noise in multi-objective optimization.
              Requires a reference point. Ideal for true Pareto optimization
              with noisy measurements.
            </p>
          </li>
          <li>
            <strong>
              qLogNEHVI (Log Noisy Expected Hypervolume Improvement):
            </strong>
            Log-space version of qNEHVI for better numerical stability.
            <p className="text-muted-foreground mt-1">
              More numerically stable than qNEHVI. Also requires a reference
              point. Recommended for problems with extreme objective values.
            </p>
          </li>
          <li>
            <strong>qLogParEGO (Log ParEGO):</strong> Log-space
            scalarization-based multi-objective acquisition using augmented
            Chebyshev scalarization.
            <p className="text-muted-foreground mt-1">
              Uses weighted scalarization in log-space to balance multiple
              objectives. More stable than regular ParEGO for extreme values.
            </p>
          </li>
        </ul>
      </div>
    </div>
  )
}

interface ReferencePointControlsProps {
  control: Control<any>
  formErrors: any
  objectiveType: string
}

function ReferencePointControls({
  control,
  formErrors,
  objectiveType
}: ReferencePointControlsProps) {
  // Watch the multi-targets to get target information
  const multiTargets = useWatch({
    control,
    name: "multiTargets",
    defaultValue: []
  })

  // Generate smart defaults based on target bounds and modes
  const generateSmartDefaults = () => {
    if (!multiTargets || multiTargets.length === 0) {
      return [0.0, 0.0] // Fallback for 2 objectives
    }

    return multiTargets.map((target: any) => {
      const { lowerBound, upperBound, mode } = target

      if (mode === "MAX") {
        // For maximization, reference point should be slightly below lower bound
        // This represents "worst acceptable performance"
        return Math.max(
          lowerBound - (upperBound - lowerBound) * 0.1,
          lowerBound * 0.9
        )
      } else {
        // For minimization, reference point should be slightly above upper bound
        // This represents "worst acceptable performance"
        return Math.min(
          upperBound + (upperBound - lowerBound) * 0.1,
          upperBound * 1.1
        )
      }
    })
  }

  const smartDefaults = generateSmartDefaults()

  return (
    <div className="col-span-full space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-base">
                Reference Point Configuration
              </CardTitle>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <InfoIcon className="text-muted-foreground size-4" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-sm">
                    <p>
                      The reference point defines the "worst acceptable values"
                      for each objective. It's used in hypervolume calculations
                      to determine the volume of objective space dominated by
                      your solutions.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Controller
              name="acquisitionFunction.ref_point"
              control={control}
              render={({ field }) => (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => field.onChange(smartDefaults)}
                >
                  <RefreshCw className="mr-1 size-3" />
                  Use Suggested Values
                </Button>
              )}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted rounded-md p-3">
            <p className="text-muted-foreground text-sm">
              <strong>Reference Point:</strong> Set one "worst acceptable value"
              per objective. This single point defines the corner for
              hypervolume calculations.
            </p>
            <ul className="text-muted-foreground mt-2 space-y-1 text-xs">
              <li>
                • <strong>Maximization:</strong> Enter a value below your
                minimum acceptable performance
              </li>
              <li>
                • <strong>Minimization:</strong> Enter a value above your
                maximum acceptable performance
              </li>
              <li>
                • <strong>Optional:</strong> Leave empty to use algorithm
                defaults
              </li>
            </ul>
          </div>

          {multiTargets && multiTargets.length > 0 ? (
            <div className="space-y-4">
              <Label className="text-sm font-medium">
                Reference Point Values
              </Label>
              <div className="grid gap-4 md:grid-cols-2">
                {multiTargets.map((target: any, index: number) => (
                  <div key={index} className="space-y-3 rounded-md border p-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{target.name}</span>
                          <Badge variant="outline">
                            {target.mode === "MAX" ? "Maximize" : "Minimize"}
                          </Badge>
                        </div>
                        <div className="text-muted-foreground text-sm">
                          Range: [{target.lowerBound}, {target.upperBound}]
                        </div>
                      </div>
                    </div>

                    <Controller
                      name="acquisitionFunction.ref_point"
                      control={control}
                      render={({ field }) => {
                        const currentArray = Array.isArray(field.value)
                          ? field.value
                          : []
                        const currentValue = currentArray[index]

                        return (
                          <div className="space-y-2">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder={`Optional (suggested: ${smartDefaults[index]?.toFixed(2) || "0.00"})`}
                              value={
                                currentValue !== undefined &&
                                currentValue !== null
                                  ? currentValue.toString()
                                  : ""
                              }
                              onChange={e => {
                                const inputValue = e.target.value.trim()

                                // Handle empty input - user wants to delete the value
                                if (inputValue === "") {
                                  const newArray = [...currentArray]
                                  // Ensure array is long enough
                                  while (newArray.length <= index) {
                                    newArray.push(undefined)
                                  }
                                  newArray[index] = undefined

                                  // Clean up the array - remove trailing undefined values
                                  while (
                                    newArray.length > 0 &&
                                    newArray[newArray.length - 1] === undefined
                                  ) {
                                    newArray.pop()
                                  }

                                  field.onChange(
                                    newArray.length > 0 ? newArray : undefined
                                  )
                                  return
                                }

                                // Handle numeric input
                                const numValue = parseFloat(inputValue)
                                if (!isNaN(numValue)) {
                                  const newArray = [...currentArray]
                                  // Ensure array is long enough
                                  while (newArray.length <= index) {
                                    newArray.push(undefined)
                                  }
                                  newArray[index] = numValue
                                  field.onChange(newArray)
                                }
                              }}
                            />
                            <div className="text-muted-foreground text-xs">
                              {target.mode === "MAX"
                                ? "Value below your minimum acceptable performance"
                                : "Value above your maximum acceptable performance"}
                            </div>
                          </div>
                        )
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            // Fallback for when targets aren't available yet
            <Controller
              name="acquisitionFunction.ref_point"
              control={control}
              render={({ field }) => (
                <div className="space-y-2">
                  <Label htmlFor="acquisitionFunction.ref_point">
                    Reference Point (JSON Array)
                  </Label>
                  <Input
                    id="acquisitionFunction.ref_point"
                    type="text"
                    placeholder="e.g., [0.5, 0.3] for 2 objectives"
                    {...field}
                    onChange={e => {
                      try {
                        const parsed = JSON.parse(e.target.value)
                        if (Array.isArray(parsed)) {
                          field.onChange(parsed)
                        } else {
                          field.onChange(e.target.value)
                        }
                      } catch {
                        field.onChange(e.target.value)
                      }
                    }}
                    value={
                      Array.isArray(field.value)
                        ? JSON.stringify(field.value)
                        : field.value || ""
                    }
                  />
                  <p className="text-muted-foreground text-sm">
                    Enter as JSON array. Configure your targets first for a
                    better interface.
                  </p>
                </div>
              )}
            />
          )}

          {formErrors.acquisitionFunction?.ref_point && (
            <p className="text-destructive text-sm">
              {typeof formErrors.acquisitionFunction.ref_point === "object" &&
              "message" in formErrors.acquisitionFunction.ref_point
                ? formErrors.acquisitionFunction.ref_point.message
                : "Invalid reference point format"}
            </p>
          )}

          <div className="bg-muted rounded-md p-3">
            <h5 className="mb-2 font-medium">Understanding Reference Points</h5>
            <ul className="text-muted-foreground space-y-1 text-sm">
              <li>
                • <strong>Single Point:</strong> One value per objective (not a
                range)
              </li>
              <li>
                • <strong>Worst Acceptable:</strong> Represents the threshold
                below which solutions are unacceptable
              </li>
              <li>
                • <strong>Maximization:</strong> Set below your minimum
                acceptable performance
              </li>
              <li>
                • <strong>Minimization:</strong> Set above your maximum
                acceptable performance
              </li>
              <li>
                • <strong>Hypervolume:</strong> Defines the corner point for
                volume calculations
              </li>
              <li>
                • <strong>Optional:</strong> Algorithm can use defaults if not
                specified
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

interface ObjectiveWeightsControlsProps {
  control: Control<any>
  formErrors: any
  objectiveType: string
}

function ObjectiveWeightsControls({
  control,
  formErrors,
  objectiveType
}: ObjectiveWeightsControlsProps) {
  // Watch the multi-targets to get target information
  const multiTargets = useWatch({
    control,
    name: "multiTargets",
    defaultValue: []
  })

  // Generate equal weights as smart defaults
  const generateEqualWeights = () => {
    if (!multiTargets || multiTargets.length === 0) {
      return [0.5, 0.5] // Fallback for 2 objectives
    }
    const equalWeight = 1.0 / multiTargets.length
    return multiTargets.map(() => equalWeight)
  }

  const equalWeights = generateEqualWeights()

  return (
    <div className="col-span-full space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-base">
                Objective Weights Configuration
              </CardTitle>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <InfoIcon className="text-muted-foreground size-4" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-sm">
                    <p>
                      Weights determine the relative importance of each
                      objective in the scalarization process. They should sum to
                      1.0. Higher weights give more importance to that
                      objective.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Controller
              name="acquisitionFunction.weights"
              control={control}
              render={({ field }) => (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => field.onChange(equalWeights)}
                >
                  <RefreshCw className="mr-1 size-3" />
                  Use Equal Weights
                </Button>
              )}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted rounded-md p-3">
            <p className="text-muted-foreground text-sm">
              Equal weights ({(1 / multiTargets.length).toFixed(3)} each) give
              all objectives the same importance.
            </p>
          </div>

          {multiTargets && multiTargets.length > 0 ? (
            <div className="space-y-4">
              <Label className="text-sm font-medium">
                Weight Values (must sum to 1.0)
              </Label>
              <div className="grid gap-4 md:grid-cols-2">
                {multiTargets.map((target: any, index: number) => (
                  <div key={index} className="space-y-3 rounded-md border p-4">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{target.name}</span>
                        <Badge variant="outline">
                          {target.mode === "MAX" ? "Maximize" : "Minimize"}
                        </Badge>
                      </div>
                    </div>

                    <Controller
                      name="acquisitionFunction.weights"
                      control={control}
                      render={({ field }) => {
                        const currentArray = Array.isArray(field.value)
                          ? field.value
                          : []
                        const currentValue = currentArray[index]

                        return (
                          <div className="space-y-2">
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              max="1"
                              placeholder={`Optional (equal: ${equalWeights[index]?.toFixed(3) || "0.000"})`}
                              value={
                                currentValue !== undefined &&
                                currentValue !== null
                                  ? currentValue.toString()
                                  : ""
                              }
                              onChange={e => {
                                const inputValue = e.target.value.trim()

                                // Handle empty input - user wants to delete the value
                                if (inputValue === "") {
                                  const newArray = [...currentArray]
                                  // Ensure array is long enough
                                  while (newArray.length <= index) {
                                    newArray.push(undefined)
                                  }
                                  newArray[index] = undefined

                                  // Clean up the array - remove trailing undefined values
                                  while (
                                    newArray.length > 0 &&
                                    newArray[newArray.length - 1] === undefined
                                  ) {
                                    newArray.pop()
                                  }

                                  field.onChange(
                                    newArray.length > 0 ? newArray : undefined
                                  )
                                  return
                                }

                                // Handle numeric input
                                const numValue = parseFloat(inputValue)
                                if (!isNaN(numValue)) {
                                  const newArray = [...currentArray]
                                  // Ensure array is long enough
                                  while (newArray.length <= index) {
                                    newArray.push(undefined)
                                  }
                                  newArray[index] = numValue
                                  field.onChange(newArray)
                                }
                              }}
                            />
                            <div className="text-muted-foreground text-xs">
                              Weight for this objective (0.0 to 1.0)
                            </div>
                          </div>
                        )
                      }}
                    />
                  </div>
                ))}
              </div>

              {/* Weight sum validation */}
              <Controller
                name="acquisitionFunction.weights"
                control={control}
                render={({ field }) => {
                  const weights = field.value || []
                  const sum = weights.reduce(
                    (acc: number, w: number) => acc + (w || 0),
                    0
                  )
                  const isValid = Math.abs(sum - 1.0) < 0.001

                  return (
                    <div
                      className={`rounded-md border p-3 text-sm ${
                        isValid
                          ? "bg-muted border-border"
                          : "border-destructive bg-destructive/10"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span>Weight Sum: {sum.toFixed(3)}</span>
                        <span
                          className={
                            isValid
                              ? "text-muted-foreground"
                              : "text-destructive"
                          }
                        >
                          {isValid ? "✓ Valid" : "⚠ Must sum to 1.0"}
                        </span>
                      </div>
                    </div>
                  )
                }}
              />
            </div>
          ) : (
            // Fallback for when targets aren't available yet
            <Controller
              name="acquisitionFunction.weights"
              control={control}
              render={({ field }) => (
                <div className="space-y-2">
                  <Label htmlFor="acquisitionFunction.weights">
                    Objective Weights (JSON Array)
                  </Label>
                  <Input
                    id="acquisitionFunction.weights"
                    type="text"
                    placeholder="e.g., [0.7, 0.3] for 2 objectives"
                    {...field}
                    onChange={e => {
                      try {
                        const parsed = JSON.parse(e.target.value)
                        if (Array.isArray(parsed)) {
                          field.onChange(parsed)
                        } else {
                          field.onChange(e.target.value)
                        }
                      } catch {
                        field.onChange(e.target.value)
                      }
                    }}
                    value={
                      Array.isArray(field.value)
                        ? JSON.stringify(field.value)
                        : field.value || ""
                    }
                  />
                  <p className="text-muted-foreground text-sm">
                    Enter as JSON array. Configure your targets first for a
                    better interface.
                  </p>
                </div>
              )}
            />
          )}

          {formErrors.acquisitionFunction?.weights && (
            <p className="text-destructive text-sm">
              {typeof formErrors.acquisitionFunction.weights === "object" &&
              "message" in formErrors.acquisitionFunction.weights
                ? formErrors.acquisitionFunction.weights.message
                : "Invalid weights format"}
            </p>
          )}

          <div className="bg-muted rounded-md p-3">
            <h5 className="mb-2 font-medium">
              Understanding Objective Weights
            </h5>
            <ul className="text-muted-foreground space-y-1 text-sm">
              <li>
                • <strong>Purpose:</strong> Control relative importance of each
                objective
              </li>
              <li>
                • <strong>Range:</strong> Each weight should be between 0.0 and
                1.0
              </li>
              <li>
                • <strong>Sum:</strong> All weights must sum to exactly 1.0
              </li>
              <li>
                • <strong>Higher Weight:</strong> More importance given to that
                objective
              </li>
              <li>
                • <strong>Equal Weights:</strong> All objectives treated equally
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
