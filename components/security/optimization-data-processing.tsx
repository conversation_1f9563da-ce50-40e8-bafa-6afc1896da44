"use client"

import { motion } from "framer-motion"
import { Bar<PERSON>hart, Brain, Database, Lock, Shield, Zap } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const optimizationSteps = [
  {
    icon: Database,
    title: "Data Ingestion",
    description: "Secure collection and validation of optimization parameters",
    processes: [
      "Parameter validation and type checking",
      "Constraint verification and bounds checking",
      "Data sanitization and normalization",
      "User permission and quota verification"
    ],
    security: ["Input validation", "Schema enforcement", "Rate limiting"]
  },
  {
    icon: Brain,
    title: "Bayesian Modeling",
    description: "Advanced machine learning for optimization suggestions",
    processes: [
      "Gaussian Process model training",
      "Acquisition function optimization",
      "Multi-objective optimization handling",
      "Uncertainty quantification"
    ],
    security: ["Isolated processing", "Memory protection", "Model versioning"]
  },
  {
    icon: BarChart,
    title: "Results Generation",
    description: "Secure generation and delivery of optimization results",
    processes: [
      "Suggestion ranking and filtering",
      "Confidence interval calculation",
      "Result formatting and validation",
      "Performance metrics computation"
    ],
    security: ["Output validation", "User authorization", "Audit logging"]
  }
]

const dataTypes = [
  {
    type: "Experiment Parameters",
    description: "Variables and their ranges for optimization",
    examples: [
      "Temperature (20-100°C)",
      "Pressure (1-10 bar)",
      "Concentration (0.1-1.0 M)"
    ],
    processing: "Used for model training and constraint enforcement",
    retention: "Stored until user deletion or account closure"
  },
  {
    type: "Measurement Results",
    description: "Experimental outcomes and target values",
    examples: ["Yield (%)", "Purity (ppm)", "Reaction time (min)"],
    processing: "Used for Bayesian model training and prediction",
    retention: "Stored with experiment data for reproducibility"
  },
  {
    type: "Optimization Objectives",
    description: "Goals and constraints for the optimization",
    examples: ["Maximize yield", "Minimize cost", "Target purity > 95%"],
    processing: "Used for acquisition function and suggestion ranking",
    retention: "Stored with optimization configuration"
  },
  {
    type: "Model Metadata",
    description: "Information about the optimization models",
    examples: ["Model version", "Training metrics", "Hyperparameters"],
    processing: "Used for model management and reproducibility",
    retention: "Stored for audit and debugging purposes"
  }
]

export function OptimizationDataProcessing() {
  return (
    <section className="py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Optimization Data Processing
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            Learn how your optimization data is processed through our Bayesian
            optimization algorithms with complete security and privacy
            protection.
          </p>
        </motion.div>

        <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-3">
          {optimizationSteps.map((step, index) => (
            <motion.div
              key={step.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <div className="mb-4 flex size-12 items-center justify-center rounded-lg bg-purple-100">
                    <step.icon className="size-6 text-purple-600" />
                  </div>
                  <CardTitle className="text-xl">{step.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {step.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <h4 className="mb-2 text-sm font-semibold text-gray-900">
                      Processing Steps:
                    </h4>
                    <ul className="space-y-1">
                      {step.processes.map((process, processIndex) => (
                        <li key={processIndex} className="flex items-start">
                          <div className="mr-2 mt-1.5 size-1.5 shrink-0 rounded-full bg-purple-600" />
                          <span className="text-sm text-gray-700">
                            {process}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="mb-2 text-sm font-semibold text-gray-900">
                      Security:
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {step.security.map((security, securityIndex) => (
                        <Badge
                          key={securityIndex}
                          variant="secondary"
                          className="text-xs"
                        >
                          {security}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mb-12"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Optimization Data Types</CardTitle>
              <CardDescription>
                Different types of data processed in our optimization system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {dataTypes.map((dataType, index) => (
                  <div
                    key={dataType.type}
                    className="border-l-4 border-blue-500 pl-4"
                  >
                    <h4 className="mb-2 font-semibold text-gray-900">
                      {dataType.type}
                    </h4>
                    <p className="mb-3 text-sm text-gray-600">
                      {dataType.description}
                    </p>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                      <div>
                        <h5 className="mb-1 text-xs font-medium uppercase tracking-wide text-gray-700">
                          Examples
                        </h5>
                        <ul className="space-y-1">
                          {dataType.examples.map((example, exampleIndex) => (
                            <li
                              key={exampleIndex}
                              className="text-xs text-gray-600"
                            >
                              • {example}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="mb-1 text-xs font-medium uppercase tracking-wide text-gray-700">
                          Processing
                        </h5>
                        <p className="text-xs text-gray-600">
                          {dataType.processing}
                        </p>
                      </div>
                      <div>
                        <h5 className="mb-1 text-xs font-medium uppercase tracking-wide text-gray-700">
                          Retention
                        </h5>
                        <p className="text-xs text-gray-600">
                          {dataType.retention}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <Card className="bg-gradient-to-r from-purple-50 to-blue-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="mb-4 text-xl font-semibold text-gray-900">
                  Optimization Processing Guarantees
                </h3>
                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-purple-600">
                      Isolated
                    </div>
                    <div className="text-sm text-gray-600">User Data</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-blue-600">
                      Encrypted
                    </div>
                    <div className="text-sm text-gray-600">Processing</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-green-600">
                      Audited
                    </div>
                    <div className="text-sm text-gray-600">Operations</div>
                  </div>
                  <div className="text-center">
                    <div className="mb-2 text-2xl font-bold text-orange-600">
                      Reproducible
                    </div>
                    <div className="text-sm text-gray-600">Results</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
