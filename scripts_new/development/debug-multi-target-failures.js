#!/usr/bin/env node

/**
 * Debug the Multi-Target configuration update failures
 */

require('dotenv').config({ path: '.env.local' });

async function debugMultiTargetFailures() {
  console.log('🔍 DEBUGGING MULTI-TARGET CONFIGURATION UPDATE FAILURES');
  console.log('========================================================');
  
  // Test the failing optimizations
  const failingOptimizations = [
    {
      name: "test CRUD filtering32",
      optimizerId: "user_30EFn0yPauqlMMorn5hfsn3of5X_test_CRUD_filtering32_1757677111499",
      type: "Pareto"
    },
    {
      name: "Test CRUD Filtering2", 
      optimizerId: "user_30EFn0yPauqlMMorn5hfsn3of5X_Test_CRUD_Filtering2_1757675394674",
      type: "Pareto"
    }
  ];
  
  for (const opt of failingOptimizations) {
    console.log(`\n🔍 DEBUGGING: ${opt.name}`);
    console.log('='.repeat(50));
    
    try {
      // First, check if the optimization exists and get its current state
      console.log('📋 Step 1: Checking optimization measurements...');
      
      const measurementsResponse = await fetch(`http://localhost:8000/optimizations/${opt.optimizerId}/measurements`);
      
      if (measurementsResponse.ok) {
        const measurementsData = await measurementsResponse.json();
        console.log(`   ✅ Measurements accessible: ${measurementsData.measurements?.length || 0} measurements`);
        
        if (measurementsData.target_info) {
          console.log('   🎯 Target info:');
          measurementsData.target_info.forEach((target, index) => {
            console.log(`     ${index + 1}. ${target.name}: ${target.mode}`);
          });
        }
      } else {
        console.log(`   ❌ Cannot access measurements: ${measurementsResponse.status} ${measurementsResponse.statusText}`);
        continue;
      }
      
      // Test parameter bounds update with detailed error reporting
      console.log('\n📋 Step 2: Testing parameter bounds update...');
      
      const testBounds = [0, 777];
      const requestBody = {
        parameter_bounds: {
          "x1": testBounds
        },
        preview_only: false
      };
      
      console.log(`   📤 Request body:`, JSON.stringify(requestBody, null, 2));
      
      const response = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/bounds`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });
      
      console.log(`   📥 Response status: ${response.status} ${response.statusText}`);
      
      const responseText = await response.text();
      console.log(`   📥 Response body:`, responseText);
      
      if (!response.ok) {
        try {
          const errorData = JSON.parse(responseText);
          console.log(`   ❌ Error details:`, errorData);
        } catch (e) {
          console.log(`   ❌ Raw error: ${responseText}`);
        }
      }
      
      // Test with preview_only: true to see if that works
      console.log('\n📋 Step 3: Testing with preview_only: true...');
      
      const previewResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/bounds`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          parameter_bounds: {
            "x1": testBounds
          },
          preview_only: true
        })
      });
      
      console.log(`   📥 Preview response status: ${previewResponse.status} ${previewResponse.statusText}`);
      
      const previewResponseText = await previewResponse.text();
      console.log(`   📥 Preview response body:`, previewResponseText.substring(0, 500) + (previewResponseText.length > 500 ? '...' : ''));
      
      // Test target bounds update for multi-target
      console.log('\n📋 Step 4: Testing target bounds update...');
      
      const targetResponse = await fetch(`http://localhost:3000/api/optimizations/${opt.optimizerId}/bounds`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          target_bounds: {
            "Target 1": [0, 888]
          },
          preview_only: true
        })
      });
      
      console.log(`   📥 Target response status: ${targetResponse.status} ${targetResponse.statusText}`);
      
      const targetResponseText = await targetResponse.text();
      console.log(`   📥 Target response body:`, targetResponseText.substring(0, 500) + (targetResponseText.length > 500 ? '...' : ''));
      
    } catch (error) {
      console.log(`   ❌ Exception during debugging: ${error.message}`);
    }
  }
  
  // Test a working Single Target for comparison
  console.log(`\n🔍 COMPARISON: Testing working Single Target optimization`);
  console.log('='.repeat(60));
  
  try {
    const workingOptId = "user_30EFn0yPauqlMMorn5hfsn3of5X_Test_campaign_working_1757682871157";
    
    const response = await fetch(`http://localhost:3000/api/optimizations/${workingOptId}/bounds`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parameter_bounds: {
          "x1": [0, 777]
        },
        preview_only: true
      })
    });
    
    console.log(`   📥 Working opt response status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Working optimization response keys:`, Object.keys(data));
    }
    
  } catch (error) {
    console.log(`   ❌ Exception testing working optimization: ${error.message}`);
  }
}

// Run the debug
debugMultiTargetFailures().catch(console.error);
