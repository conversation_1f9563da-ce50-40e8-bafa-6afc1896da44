"use client"

import { motion } from "framer-motion"
import { Code, Database, Cloud, Shield } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const technicalSpecs = [
  {
    icon: Shield,
    title: "Encryption Standards",
    items: [
      { label: "Transport Layer", value: "TLS 1.3", type: "success" },
      { label: "Data at Rest", value: "AWS KMS AES-256", type: "success" },
      { label: "Key Management", value: "AWS KMS HSM-backed", type: "success" },
      { label: "Hashing", value: "SHA-256", type: "success" }
    ]
  },
  {
    icon: Database,
    title: "Database Security",
    items: [
      { label: "Database", value: "AWS RDS PostgreSQL 15+", type: "info" },
      { label: "Encryption", value: "AWS KMS with AES-256", type: "success" },
      { label: "Network", value: "VPC isolation + SSL/TLS", type: "success" },
      { label: "Backups", value: "Automated with encryption", type: "success" }
    ]
  },
  {
    icon: Cloud,
    title: "Infrastructure",
    items: [
      { label: "Hosting", value: "Amazon Web Services", type: "info" },
      { label: "Database", value: "AWS RDS Multi-AZ", type: "info" },
      {
        label: "Monitoring",
        value: "AWS CloudWatch + 24/7 SOC",
        type: "success"
      },
      { label: "Compliance", value: "SOC 2, HIPAA eligible", type: "success" }
    ]
  },
  {
    icon: Code,
    title: "Application Security",
    items: [
      { label: "Framework", value: "Next.js 14", type: "info" },
      { label: "Authentication", value: "Clerk (OAuth 2.0)", type: "success" },
      { label: "Validation", value: "Zod Schema", type: "success" },
      { label: "CSRF Protection", value: "Built-in", type: "success" }
    ]
  }
]

const getBadgeVariant = (type: string) => {
  switch (type) {
    case "success":
      return "default"
    case "warning":
      return "secondary"
    case "info":
      return "outline"
    default:
      return "outline"
  }
}

export function TechnicalDetails() {
  return (
    <section className="bg-gray-50 py-20">
      <div className="container mx-auto max-w-7xl px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-12 text-center"
        >
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
            Technical Security Specifications
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            Detailed technical information about our security implementation,
            encryption standards, and infrastructure specifications.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          {technicalSpecs.map((spec, index) => (
            <motion.div
              key={spec.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <div className="mb-4 flex size-12 items-center justify-center rounded-lg bg-blue-100">
                    <spec.icon className="size-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl">{spec.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {spec.items.map((item, itemIndex) => (
                      <div
                        key={itemIndex}
                        className="flex items-center justify-between"
                      >
                        <span className="text-sm font-medium text-gray-700">
                          {item.label}
                        </span>
                        <Badge variant={getBadgeVariant(item.type)}>
                          {item.value}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-12"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Security Architecture</CardTitle>
              <CardDescription>
                Our multi-layered security architecture ensures comprehensive
                protection
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-blue-600">
                    256-bit
                  </div>
                  <div className="text-sm text-gray-600">AES Encryption</div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-green-600">
                    99.9%
                  </div>
                  <div className="text-sm text-gray-600">Uptime SLA</div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-2xl font-bold text-purple-600">
                    24/7
                  </div>
                  <div className="text-sm text-gray-600">
                    Security Monitoring
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
