"use client"

import React, { useState, useEffect } from "react"
import {
  Plus,
  Trash2,
  Edit,
  Save,
  X,
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Settings,
  Download,
  Upload
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"
import { ConstraintEditor } from "@/components/optimization/constraints/constraint-editor"

// Import from centralized constraints library
import {
  useConstraints,
  useConstraintValidation,
  useConstraintContext,
  Constraint,
  Parameter,
  formatConstraintDisplay,
  getConstraintCategory,
  CONSTRAINT_CATEGORIES,
  createConstraint,
  cloneConstraint
} from "@/lib/constraints"

interface ConstraintManagerProps {
  constraints: Constraint[]
  availableParameters: Parameter[]
  onSave: (updatedConstraints: Constraint[]) => void
  onCancel: () => void
  readOnly?: boolean
  className?: string
}

export function ConstraintManager({
  constraints: initialConstraints,
  availableParameters,
  onSave,
  onCancel,
  readOnly = false,
  className = ""
}: ConstraintManagerProps) {
  const [hasChanges, setHasChanges] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingConstraint, setEditingConstraint] = useState<Constraint | null>(
    null
  )

  // Use centralized constraint management
  const {
    constraints,
    selectedConstraint,
    selectedConstraintId,
    addConstraint,
    updateConstraint,
    removeConstraint,
    duplicateConstraint,
    setConstraints,
    setSelectedConstraintId
  } = useConstraints(initialConstraints)

  // Use centralized constraint context
  const {
    isValidating,
    isValid,
    hasWarnings,
    validationResults,
    globalValidation,
    constraintCount,
    hasConstraints
  } = useConstraintContext(availableParameters, constraints, {
    validationEnabled: true,
    samplingEnabled: false
  })

  // Track changes
  useEffect(() => {
    const changed =
      JSON.stringify(constraints) !== JSON.stringify(initialConstraints)
    setHasChanges(changed)
  }, [constraints, initialConstraints])

  // Reset constraints when initial constraints change
  useEffect(() => {
    setConstraints(initialConstraints)
    setHasChanges(false)
  }, [initialConstraints, setConstraints])

  const handleSave = () => {
    if (!isValid) {
      toast({
        title: "Validation Error",
        description: "Please fix constraint validation errors before saving.",
        variant: "destructive"
      })
      return
    }

    onSave(constraints)
    setHasChanges(false)
    setIsEditing(false)

    toast({
      title: "Constraints saved",
      description: `Successfully saved ${constraintCount} constraints.`
    })
  }

  const handleReset = () => {
    setConstraints(initialConstraints)
    setHasChanges(false)
    setIsEditing(false)
    setSelectedConstraintId(null)

    toast({
      title: "Changes reset",
      description: "All changes have been reverted."
    })
  }

  const handleAddConstraint = () => {
    if (readOnly) return

    const newConstraint = createConstraint(
      "ContinuousLinearConstraint",
      [],
      `New Constraint ${constraints.length + 1}`
    )

    const constraintWithId = addConstraint(newConstraint)

    // Immediately propagate changes to parent component
    const updatedConstraints = [...constraints, constraintWithId]
    onSave(updatedConstraints)

    setSelectedConstraintId(constraintWithId.id!)
    setIsEditing(true)

    toast({
      title: "Constraint added",
      description: "New constraint created. Configure its parameters."
    })
  }

  const handleEditConstraint = (constraintId: string) => {
    if (readOnly) return

    const constraint = constraints.find(c => c.id === constraintId)
    if (constraint) {
      setEditingConstraint({ ...constraint })
      setSelectedConstraintId(constraintId)
      setIsDialogOpen(true)
      setIsEditing(true)
    }
  }

  const handleDeleteConstraint = (constraintId: string) => {
    if (readOnly) return

    const constraint = constraints.find(c => c.id === constraintId)
    removeConstraint(constraintId)

    // Immediately propagate changes to parent component
    const updatedConstraints = constraints.filter(c => c.id !== constraintId)
    onSave(updatedConstraints)

    if (selectedConstraintId === constraintId) {
      setSelectedConstraintId(null)
      setIsEditing(false)
    }

    toast({
      title: "Constraint deleted",
      description: `${constraint?.name || "Constraint"} has been removed.`
    })
  }

  const handleDuplicateConstraint = (constraintId: string) => {
    if (readOnly) return

    const duplicate = duplicateConstraint(constraintId)
    if (duplicate) {
      // Immediately propagate changes to parent component
      const updatedConstraints = [...constraints, duplicate]
      onSave(updatedConstraints)

      setSelectedConstraintId(duplicate.id!)
      setIsEditing(true)

      toast({
        title: "Constraint duplicated",
        description: `${duplicate.name} has been created.`
      })
    }
  }

  const handleExportConstraints = () => {
    const exportData = {
      constraints,
      parameters: availableParameters,
      exportedAt: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json"
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `constraints-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "Constraints exported",
      description: "Constraints have been exported to a JSON file."
    })
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header with controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">Constraint Management</h3>
          <Badge variant="secondary">{constraintCount}</Badge>
          {isValidating && (
            <Badge variant="outline" className="animate-pulse">
              Validating...
            </Badge>
          )}
          {!isValidating && hasConstraints && (
            <Badge variant={isValid ? "default" : "destructive"}>
              {isValid ? "Valid" : "Invalid"}
            </Badge>
          )}
          {hasChanges && (
            <Badge variant="outline" className="text-orange-600">
              Unsaved Changes
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          {!readOnly && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportConstraints}
                disabled={constraintCount === 0}
              >
                <Download className="mr-1 size-4" />
                Export
              </Button>

              <Button variant="outline" size="sm" onClick={handleAddConstraint}>
                <Plus className="mr-1 size-4" />
                Add Constraint
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Validation feedback */}
      {!isValidating && !isValid && globalValidation.errors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="size-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Validation Errors:</div>
              {globalValidation.errors.map((error, index) => (
                <div key={index} className="text-sm">
                  • {error.error}
                </div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {!isValidating && hasWarnings && (
        <Alert>
          <AlertCircle className="size-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Warnings:</div>
              {globalValidation.warnings?.map((warning, index) => (
                <div key={index} className="text-sm">
                  • {warning}
                </div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Constraints list */}
      <div className="space-y-2">
        {constraints.map(constraint => {
          const category = getConstraintCategory(constraint.type)
          const categoryInfo = CONSTRAINT_CATEGORIES[category]
          const validationResult = validationResults[constraint.id!]
          const isConstraintValid = validationResult?.valid !== false
          const isSelected = selectedConstraintId === constraint.id

          return (
            <Card
              key={constraint.id}
              className={cn(
                "cursor-pointer transition-colors",
                !isConstraintValid && "border-destructive",
                isSelected && "ring-primary ring-2"
              )}
              onClick={() => !readOnly && handleEditConstraint(constraint.id!)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {constraint.name || constraint.type}
                      </span>
                      <Badge
                        variant="outline"
                        style={{ borderColor: categoryInfo.color }}
                      >
                        {categoryInfo.name}
                      </Badge>
                      {!isConstraintValid && (
                        <Badge variant="destructive" className="text-xs">
                          Invalid
                        </Badge>
                      )}
                    </div>
                    <div className="text-muted-foreground text-sm">
                      {formatConstraintDisplay(constraint)}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      Parameters: {constraint.parameters.join(", ") || "None"}
                    </div>
                    {constraint.description && (
                      <div className="text-muted-foreground text-xs">
                        {constraint.description}
                      </div>
                    )}
                    {!isConstraintValid && validationResult?.error && (
                      <div className="text-destructive text-xs">
                        Error: {validationResult.error}
                      </div>
                    )}
                  </div>

                  {!readOnly && (
                    <div
                      className="flex items-center gap-1"
                      onClick={e => e.stopPropagation()}
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditConstraint(constraint.id!)}
                      >
                        <Edit className="size-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          handleDuplicateConstraint(constraint.id!)
                        }
                      >
                        <Plus className="size-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteConstraint(constraint.id!)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="size-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}

        {constraints.length === 0 && (
          <Card className="border-dashed">
            <CardContent className="p-8 text-center">
              <div className="text-muted-foreground">
                <Settings className="mx-auto mb-2 size-8" />
                <p className="text-sm">No constraints configured</p>
                <p className="text-xs">
                  {readOnly
                    ? "This optimization has no constraints defined"
                    : "Add constraints to restrict the optimization search space"}
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Action buttons */}
      {!readOnly && (hasChanges || isEditing) && (
        <>
          <Separator />
          <div className="flex items-center justify-between">
            <div className="text-muted-foreground text-sm">
              {hasChanges && "You have unsaved changes"}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                disabled={!hasChanges}
              >
                <RotateCcw className="mr-1 size-4" />
                Reset
              </Button>
              <Button variant="outline" size="sm" onClick={onCancel}>
                <X className="mr-1 size-4" />
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleSave}
                disabled={!hasChanges || !isValid}
              >
                <Save className="mr-1 size-4" />
                Save Changes
              </Button>
            </div>
          </div>
        </>
      )}

      {/* Constraint Editor Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingConstraint?.id ? "Edit Constraint" : "Add New Constraint"}
            </DialogTitle>
            <DialogDescription>
              Configure constraint parameters and validation rules.
            </DialogDescription>
          </DialogHeader>

          {editingConstraint && (
            <ConstraintEditor
              constraint={editingConstraint}
              availableParameters={availableParameters.map(p => p.name)}
              onSave={updatedConstraint => {
                // Calculate the updated constraints list
                let updatedConstraints: Constraint[]

                if (editingConstraint.id) {
                  // Update existing constraint
                  updatedConstraints = constraints.map(c =>
                    c.id === editingConstraint.id
                      ? { ...c, ...updatedConstraint }
                      : c
                  )
                  updateConstraint(editingConstraint.id, updatedConstraint)
                } else {
                  // Add new constraint (shouldn't happen in this context, but handle it)
                  const newConstraint = addConstraint(updatedConstraint)
                  updatedConstraints = [...constraints, newConstraint]
                }

                // Immediately propagate changes to parent component
                // This ensures the UI reflects the changes right away
                onSave(updatedConstraints)

                setIsDialogOpen(false)
                setEditingConstraint(null)

                toast({
                  title: "Constraint saved",
                  description: `${updatedConstraint.name || updatedConstraint.type} has been saved.`
                })
              }}
              onCancel={() => {
                setIsDialogOpen(false)
                setEditingConstraint(null)
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
