// lib/optimization-utils.ts

import { SelectOptimization } from "@/db/schema/optimizations-schema"

export function formatParameterValue(
  value: any,
  parameterType: string
): string {
  if (value === null || value === undefined) {
    return "N/A"
  }

  if (
    parameterType === "NumericalContinuous" ||
    parameterType === "NumericalDiscrete"
  ) {
    const numValue = Number(value)
    if (isNaN(numValue)) {
      return String(value)
    }
    // Format numbers with appropriate precision
    if (numValue % 1 === 0) {
      return numValue.toString()
    } else {
      return numValue.toFixed(3)
    }
  }

  return String(value)
}

/**
 * Get the target mode (MAX/MIN) for a specific target in an optimization.
 * Handles both single-target and multi-target optimizations correctly.
 */
export function getTargetMode(
  optimization: SelectOptimization,
  targetName: string
): "MAX" | "MIN" {
  if (optimization.targetMode !== "MULTI") {
    // Handle enum-style target modes like "TargetMode.MAX"
    const targetMode = String(optimization.targetMode).toUpperCase()
    if (targetMode.includes("MAX")) {
      return "MAX"
    } else if (targetMode.includes("MIN")) {
      return "MIN"
    }
    return "MAX" // Default to MAX if we can't determine
  }

  // For multi-target, find the specific target's mode from the config
  try {
    const config = optimization.config as any
    if (Array.isArray(config.target_config)) {
      const targetConfig = config.target_config.find(
        (t: any) => t.name === targetName
      )
      if (targetConfig && targetConfig.mode) {
        return targetConfig.mode as "MAX" | "MIN"
      }
    }
  } catch (error) {
    console.error("Error getting target mode:", error)
  }

  // Default to MAX if we can't determine
  return "MAX"
}

/**
 * Determine the objective type from an optimization configuration.
 * Returns the objective type that should be used for acquisition function filtering.
 */
export function getObjectiveType(
  optimization: SelectOptimization
): "SINGLE" | "MULTI_DESIRABILITY" | "MULTI_PARETO" {
  try {
    const config = optimization.config as any

    // Check if explicit objective_type is set in the config
    if (config.objective_type) {
      switch (config.objective_type) {
        case "SingleTarget":
          return "SINGLE"
        case "Desirability":
          return "MULTI_DESIRABILITY"
        case "Pareto":
          return "MULTI_PARETO"
        default:
          break
      }
    }

    // Fallback: Determine from target_config structure and targetMode
    if (
      optimization.targetMode === "MULTI" ||
      Array.isArray(config.target_config)
    ) {
      // Multi-target optimization
      const targetConfigs = Array.isArray(config.target_config)
        ? config.target_config
        : [config.target_config]

      if (targetConfigs.length > 1) {
        // Multiple targets - need to determine if it's Desirability or Pareto
        // Check if scalarizer is present (indicates Desirability)
        if (config.scalarizer) {
          return "MULTI_DESIRABILITY"
        }

        // Check acquisition function type to infer objective type
        const acqType = config.acquisition_config?.type
        if (acqType) {
          // Pareto-specific acquisition functions
          if (
            acqType === "qNoisyExpectedHypervolumeImprovement" ||
            acqType === "qLogNoisyExpectedHypervolumeImprovement" ||
            acqType === "qLogNParEGO"
          ) {
            return "MULTI_PARETO"
          }
          // Single/Desirability acquisition functions with multiple targets = Desirability
          if (
            acqType === "qExpectedImprovement" ||
            acqType === "qProbabilityOfImprovement" ||
            acqType === "qUpperConfidenceBound"
          ) {
            return "MULTI_DESIRABILITY"
          }
        }

        // Default to Desirability for multi-target if we can't determine
        return "MULTI_DESIRABILITY"
      }
    }

    // Single target optimization
    return "SINGLE"
  } catch (error) {
    console.error("Error determining objective type:", error)
    // Default to single target if we can't determine
    return "SINGLE"
  }
}
