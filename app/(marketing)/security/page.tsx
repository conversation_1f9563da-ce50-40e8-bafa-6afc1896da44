import { Metadata } from "next"
import { SecurityHero } from "@/components/security/security-hero"
import { SecurityMeasures } from "@/components/security/security-measures"
import { TechnicalDetails } from "@/components/security/technical-details"
import { ComplianceBadges } from "@/components/security/compliance-badges"
import { DataProtection } from "@/components/security/data-protection"
import { InfrastructureSecurity } from "@/components/security/infrastructure-security"
import { SecurityContact } from "@/components/security/security-contact"

export const metadata: Metadata = {
  title: "Security & Privacy Center | INNOptimizer™",
  description:
    "Comprehensive security documentation, data protection measures, and privacy transparency for INNOptimizer™ platform.",
  keywords: [
    "security",
    "privacy",
    "data protection",
    "encryption",
    "compliance",
    "GDPR",
    "ISO 27001",
    "SOC 2"
  ],
  openGraph: {
    title: "Security & Privacy Center | INNOptimizer™",
    description:
      "Learn about our comprehensive security measures and data protection practices.",
    type: "website"
  }
}

export default function SecurityPage() {
  return (
    <div className="min-h-screen">
      <SecurityHero />
      <SecurityMeasures />
      <TechnicalDetails />
      <DataProtection />
      <InfrastructureSecurity />
      <ComplianceBadges />
      <SecurityContact />
    </div>
  )
}
