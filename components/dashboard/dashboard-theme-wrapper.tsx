// components/dashboard/dashboard-theme-wrapper.tsx
import { ReactNode } from "react"

interface DashboardThemeWrapperProps {
  children: ReactNode
  className?: string
}

export function DashboardThemeWrapper({ 
  children, 
  className = "" 
}: DashboardThemeWrapperProps) {
  return (
    <div className="min-h-screen bg-gray-50/30 p-3">
      {/* Main rounded background container taking maximum space */}
      <div className={`container mx-auto max-w-8xl min-h-[calc(100vh-1.5rem)] rounded-[2rem] bg-gradient-to-br from-blue-50/70 via-indigo-50/50 to-slate-50/40 p-8 shadow-xl backdrop-blur-sm border border-blue-100/60 ring-1 ring-blue-50/50 ${className}`}>
        <div className="space-y-8">
          {children}
        </div>
      </div>
    </div>
  )
}
