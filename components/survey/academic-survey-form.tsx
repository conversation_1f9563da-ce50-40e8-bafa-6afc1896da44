"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { motion, AnimatePresence } from "framer-motion"
import { useAcademicSurveyGuard } from "@/hooks/use-academic-navigation-guard"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { saveAcademicSurveyAction } from "@/actions/academic-survey-actions"
import { AcademicSurveyResponses } from "@/actions/academic-verification-actions"
import { toast } from "@/components/ui/use-toast"
import {
  Loader2,
  GraduationCap,
  Settings,
  DollarSign,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  Sparkles,
  BookOpen,
  Microscope
} from "lucide-react"

// Define the form schema with validation
const formSchema = z.object({
  // Section 2: Experience with Bayesian Optimization Tools
  toolsAware: z.array(z.string()).min(1, "Please select at least one tool"),
  toolsAwareOther: z.string().optional(),
  toolsUsed: z
    .array(z.string())
    .min(
      1,
      "Please select at least one tool or 'None' if you haven't used any"
    ),
  toolsUsedOther: z.string().optional(),

  // Section 4: Pricing Perspective
  fairPriceAcademic: z.string().min(1, "Please select a price range"),
  fairPriceAcademicOther: z.string().optional(),
  fairPriceCommercial: z.string().min(1, "Please select a price range"),
  fairPriceCommercialOther: z.string().optional(),

  // Research area is now required
  researchArea: z.string().min(1, "Please provide your research area")
})

// List of Bayesian optimization tools
const bayesianTools = [
  { id: "gpyopt", label: "GPyOpt" },
  { id: "spearmint", label: "Spearmint" },
  { id: "hyperopt", label: "Hyperopt" },
  { id: "bayesopt", label: "BayesOpt" },
  { id: "skopt", label: "Scikit-optimize" }
]

// Add a "None" option for the tools used section
const toolsUsedOptions = [
  ...bayesianTools,
  { id: "none", label: "None - I haven't used any Bayesian optimization tools" }
]

// Animation variants
const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
}

const stepVariants = {
  hidden: { opacity: 0, x: 20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.4 }
  },
  exit: {
    opacity: 0,
    x: -20,
    transition: { duration: 0.3 }
  }
}

// Academic survey step configuration
const academicStepConfig = [
  {
    icon: GraduationCap,
    title: "Tool Experience",
    description: "Your optimization background",
    color: "from-blue-500 to-indigo-600"
  },
  {
    icon: Settings,
    title: "Tools Used",
    description: "Hands-on experience",
    color: "from-indigo-500 to-purple-600"
  },
  {
    icon: DollarSign,
    title: "Research & Pricing",
    description: "Your perspective & research area",
    color: "from-purple-500 to-pink-600"
  }
]

export default function AcademicSurveyForm() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  // Enable navigation guard to prevent bypassing survey
  useAcademicSurveyGuard()

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      toolsAware: [],
      toolsAwareOther: "",
      toolsUsed: [],
      toolsUsedOther: "",
      fairPriceAcademic: "",
      fairPriceAcademicOther: "",
      fairPriceCommercial: "",
      fairPriceCommercialOther: "",
      researchArea: ""
    }
  })

  // Calculate progress based on current step
  const calculateProgress = () => {
    return ((currentStep + 1) / 3) * 100
  }

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)
    try {
      const result = await saveAcademicSurveyAction(
        values as AcademicSurveyResponses,
        true
      )

      if (result.isSuccess) {
        toast({
          title: "Academic Survey Completed",
          description:
            "Thank you for completing the academic survey! You now have extended 90-day access.",
          variant: "default"
        })

        // Show a success message for a moment before redirecting
        setTimeout(() => {
          router.push("/dashboard/home")
        }, 1500)
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to submit survey",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Error submitting survey:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Academic survey cannot be skipped
  // This function is kept for compatibility but is not used
  const handleSkip = async () => {
    toast({
      title: "Survey Required",
      description:
        "The academic survey must be completed to access the dashboard.",
      variant: "destructive"
    })
  }

  // Handle next step
  const handleNext = () => {
    const nextStep = currentStep + 1
    if (nextStep < 3) {
      setCurrentStep(nextStep)
    }
  }

  // Handle previous step
  const handlePrevious = () => {
    const prevStep = currentStep - 1
    if (prevStep >= 0) {
      setCurrentStep(prevStep)
    }
  }

  // Render the appropriate step
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium">
              Experience with Bayesian Optimization Tools
            </h3>

            <FormField
              control={form.control}
              name="toolsAware"
              render={() => (
                <FormItem>
                  <FormLabel>
                    Which of the following Bayesian optimization tools are you
                    aware of?
                  </FormLabel>
                  <div className="space-y-2">
                    {bayesianTools.map(tool => (
                      <FormField
                        key={tool.id}
                        control={form.control}
                        name="toolsAware"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={tool.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(tool.id)}
                                  onCheckedChange={checked => {
                                    return checked
                                      ? field.onChange([
                                          ...field.value,
                                          tool.id
                                        ])
                                      : field.onChange(
                                          field.value?.filter(
                                            value => value !== tool.id
                                          )
                                        )
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {tool.label}
                              </FormLabel>
                            </FormItem>
                          )
                        }}
                      />
                    ))}

                    <FormField
                      control={form.control}
                      name="toolsAware"
                      render={({ field }) => {
                        return (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes("other")}
                                onCheckedChange={checked => {
                                  return checked
                                    ? field.onChange([...field.value, "other"])
                                    : field.onChange(
                                        field.value?.filter(
                                          value => value !== "other"
                                        )
                                      )
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">Other</FormLabel>
                          </FormItem>
                        )
                      }}
                    />

                    {form.watch("toolsAware").includes("other") && (
                      <FormField
                        control={form.control}
                        name="toolsAwareOther"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                placeholder="Please specify other tools"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium">Tools Used</h3>

            <FormField
              control={form.control}
              name="toolsUsed"
              render={() => (
                <FormItem>
                  <FormLabel>Which of these tools have you used?</FormLabel>
                  <div className="space-y-2">
                    {toolsUsedOptions.map(tool => (
                      <FormField
                        key={tool.id}
                        control={form.control}
                        name="toolsUsed"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={tool.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(tool.id)}
                                  onCheckedChange={checked => {
                                    // If selecting "None", clear all other selections
                                    if (tool.id === "none" && checked) {
                                      return field.onChange(["none"])
                                    }

                                    // If selecting any other option, remove "None" if it's selected
                                    let newValue = checked
                                      ? [...field.value, tool.id]
                                      : field.value?.filter(
                                          value => value !== tool.id
                                        )

                                    if (
                                      checked &&
                                      newValue.includes("none") &&
                                      tool.id !== "none"
                                    ) {
                                      newValue = newValue.filter(
                                        value => value !== "none"
                                      )
                                    }

                                    return field.onChange(newValue)
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {tool.label}
                              </FormLabel>
                            </FormItem>
                          )
                        }}
                      />
                    ))}

                    <FormField
                      control={form.control}
                      name="toolsUsed"
                      render={({ field }) => {
                        return (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes("other")}
                                onCheckedChange={checked => {
                                  return checked
                                    ? field.onChange([...field.value, "other"])
                                    : field.onChange(
                                        field.value?.filter(
                                          value => value !== "other"
                                        )
                                      )
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">Other</FormLabel>
                          </FormItem>
                        )
                      }}
                    />

                    {form.watch("toolsUsed").includes("other") && (
                      <FormField
                        control={form.control}
                        name="toolsUsedOther"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                placeholder="Please specify other tools you've used"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )
      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium">Pricing Perspective</h3>

            <FormField
              control={form.control}
              name="fairPriceAcademic"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    In your opinion, what is a fair yearly subscription fee for
                    academic users to access our tool?
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="free" />
                        </FormControl>
                        <FormLabel className="font-normal">Free</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="0-50" />
                        </FormControl>
                        <FormLabel className="font-normal">€0 - €50</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="50-100" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €50 - €100
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="100-200" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €100 - €200
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="other" />
                        </FormControl>
                        <FormLabel className="font-normal">Other</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("fairPriceAcademic") === "other" && (
              <FormField
                control={form.control}
                name="fairPriceAcademicOther"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Please specify other price range"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="fairPriceCommercial"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    In your opinion, what is a fair yearly subscription fee for
                    commercial users to access our tool?
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="100-500" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €100 - €500
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="500-1000" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €500 - €1000
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="1000-2000" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          €1000 - €2000
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="2000+" />
                        </FormControl>
                        <FormLabel className="font-normal">€2000+</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="other" />
                        </FormControl>
                        <FormLabel className="font-normal">Other</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch("fairPriceCommercial") === "other" && (
              <FormField
                control={form.control}
                name="fairPriceCommercialOther"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Please specify other price range"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="researchArea"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    What is your primary field of study or research area?
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter your research area"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    This information helps us understand how our tool can better
                    serve your specific research needs.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )
      default:
        return null
    }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="grid grid-cols-1 gap-8 lg:grid-cols-2 lg:gap-12"
    >
      {/* Left Side - Form */}
      <div className="space-y-6">
        {/* Enhanced Progress Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <motion.h3
              className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-xl font-semibold text-transparent"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              Academic Survey
            </motion.h3>
            <motion.span
              className="text-muted-foreground text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              Step {currentStep + 1} of 3
            </motion.span>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="space-y-2">
            <Progress
              value={calculateProgress()}
              className="h-3 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20"
            />
            <div className="text-muted-foreground flex justify-between text-xs">
              {academicStepConfig.map((step, index) => (
                <span
                  key={index}
                  className={`flex items-center gap-1 ${
                    index <= currentStep ? "font-medium text-blue-600" : ""
                  }`}
                >
                  {index <= currentStep && <CheckCircle className="size-3" />}
                  {step.title}
                </span>
              ))}
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                variants={stepVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="min-h-[300px]"
              >
                {renderStep()}
              </motion.div>
            </AnimatePresence>

            {/* Enhanced Navigation Buttons */}
            <div className="flex justify-between pt-4">
              <div>
                {currentStep > 0 ? (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handlePrevious}
                      disabled={isSubmitting}
                      className="flex items-center gap-2"
                    >
                      <ArrowLeft className="size-4" />
                      Previous
                    </Button>
                  </motion.div>
                ) : (
                  <div className="text-xs font-medium text-amber-600 dark:text-amber-400">
                    * Required for 90-day academic access
                  </div>
                )}
              </div>
              <div>
                {currentStep < 2 ? (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="button"
                      onClick={handleNext}
                      disabled={
                        isSubmitting ||
                        (currentStep === 0 &&
                          form.watch("toolsAware").length === 0)
                      }
                      className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                    >
                      Next
                      <ArrowRight className="size-4" />
                    </Button>
                  </motion.div>
                ) : (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="submit"
                      disabled={isSubmitting || !form.formState.isValid}
                      className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="size-4 animate-spin" />
                          Activating Access...
                        </>
                      ) : (
                        <>
                          Activate Access
                          <CheckCircle className="size-4" />
                        </>
                      )}
                    </Button>
                  </motion.div>
                )}
              </div>
            </div>
          </form>
        </Form>
      </div>

      {/* Right Side - Academic Visual Elements */}
      <div className="hidden flex-col items-center justify-center space-y-6 lg:flex">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.5 }}
            className="relative"
          >
            {/* Dynamic Academic Visual Content */}
            {currentStep === 0 && (
              <div className="space-y-4 text-center">
                <div className="relative">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 25,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                    className="mx-auto flex size-32 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-indigo-700"
                  >
                    <GraduationCap className="size-16 text-white" />
                  </motion.div>
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute -right-2 -top-2 flex size-8 items-center justify-center rounded-full bg-yellow-400"
                  >
                    <Sparkles className="size-4 text-yellow-800" />
                  </motion.div>
                </div>
                <h4 className="text-foreground text-xl font-semibold">
                  Tool Awareness
                </h4>
                <p className="text-muted-foreground max-w-sm">
                  Share your knowledge of Bayesian optimization tools to help us
                  understand your background.
                </p>
              </div>
            )}

            {currentStep === 1 && (
              <div className="space-y-4 text-center">
                <div className="relative">
                  <motion.div
                    animate={{ y: [-5, 5, -5] }}
                    transition={{ duration: 3, repeat: Infinity }}
                    className="mx-auto flex size-32 items-center justify-center rounded-full bg-gradient-to-r from-indigo-600 to-purple-700"
                  >
                    <Settings className="size-16 text-white" />
                  </motion.div>
                  <motion.div
                    animate={{ rotate: [0, 15, -15, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute -bottom-2 -left-2 flex size-8 items-center justify-center rounded-full bg-green-400"
                  >
                    <BookOpen className="size-4 text-green-800" />
                  </motion.div>
                </div>
                <h4 className="text-foreground text-xl font-semibold">
                  Hands-on Experience
                </h4>
                <p className="text-muted-foreground max-w-sm">
                  Tell us which tools you've actually used in your research
                  work.
                </p>
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-4 text-center">
                <div className="relative">
                  <motion.div
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="mx-auto flex size-32 items-center justify-center rounded-full bg-gradient-to-r from-purple-600 to-pink-700"
                  >
                    <Microscope className="size-16 text-white" />
                  </motion.div>
                  <motion.div
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="absolute -right-3 -top-3 size-6 rounded-full bg-orange-400"
                  />
                  <motion.div
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
                    className="absolute -bottom-3 -left-3 size-6 rounded-full bg-cyan-400"
                  />
                </div>
                <h4 className="text-foreground text-xl font-semibold">
                  Research Focus
                </h4>
                <p className="text-muted-foreground max-w-sm">
                  Share your research area and pricing perspective to complete
                  your profile.
                </p>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </motion.div>
  )
}
