import { cn } from "@/lib/utils"
import { ReactNode } from "react"

interface ContainerProps {
  children: ReactNode
  className?: string
  as?:
    | "div"
    | "section"
    | "article"
    | "main"
    | "header"
    | "footer"
    | "aside"
    | "nav"
  size?: "sm" | "md" | "lg" | "xl" | "full"
}

/**
 * A responsive container component that follows NinjaChat.ai's design patterns
 *
 * @param children - The content to be contained
 * @param className - Additional classes to apply to the container
 * @param as - The HTML element to render the container as (default: div)
 * @param size - The maximum width of the container:
 *   - sm: max-w-2xl (672px)
 *   - md: max-w-4xl (896px)
 *   - lg: max-w-6xl (1152px)
 *   - xl: max-w-7xl (1280px)
 *   - full: w-full (no max width)
 */
export function Container({
  children,
  className,
  as: Component = "div",
  size = "xl"
}: ContainerProps) {
  const sizeClasses = {
    sm: "max-w-2xl", // 672px
    md: "max-w-4xl", // 896px
    lg: "max-w-6xl", // 1152px
    xl: "max-w-7xl", // 1280px
    full: "w-full" // No max width
  }

  return (
    <Component
      className={cn(
        "mx-auto w-full px-4 md:px-6",
        sizeClasses[size],
        className
      )}
    >
      {children}
    </Component>
  )
}
