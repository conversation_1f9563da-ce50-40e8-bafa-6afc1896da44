"use client"

import React, { useState } from "react"
import { Info, ChevronDown, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

interface DiscretizationTransparencyProps {
  discretizationInfo?: {
    has_discretization: boolean
    discrete_parameters: Array<{
      parameter: string
      type: string
      allowed_values: number[]
      original_values: number[]
      discretized_values: number[]
      has_changes: boolean
    }>
    sample_transparency: Array<{
      sample_index: number
      parameter_changes: Record<
        string,
        {
          original: number
          discretized: number
          allowed_values: number[]
        }
      >
    }>
  }
  className?: string
}

export function DiscretizationTransparency({
  discretizationInfo,
  className = ""
}: DiscretizationTransparencyProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Don't render if no discretization occurred
  if (!discretizationInfo?.has_discretization) {
    return null
  }

  const parametersWithChanges = discretizationInfo.discrete_parameters.filter(
    param => param.has_changes
  )

  if (parametersWithChanges.length === 0) {
    return null
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* Info Icon with Tooltip */}
      <div className="flex items-center gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(!isOpen)}
                className="size-6 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-800"
              >
                <Info className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>View parameter discretization details</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <span className="text-muted-foreground text-sm">
          {parametersWithChanges.length} parameter
          {parametersWithChanges.length !== 1 ? "s" : ""} rounded to discrete
          values
        </span>
      </div>

      {/* Collapsible Details */}
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleContent className="space-y-3">
          <Card className="border-blue-200 bg-blue-50/50">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Info className="size-4 text-blue-600" />
                Parameter Discretization Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-muted-foreground text-sm">
                Some parameters were automatically rounded to their nearest
                allowed discrete values during sample generation.
              </div>

              {/* Sample-by-Sample Details */}
              {discretizationInfo.sample_transparency.length > 0 && (
                <Collapsible>
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-2 text-xs"
                    >
                      <ChevronRight className="mr-1 size-3" />
                      View sample-by-sample details
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="mt-2 space-y-2">
                    {discretizationInfo.sample_transparency.map(sample => {
                      const hasChanges =
                        Object.keys(sample.parameter_changes).length > 0

                      if (!hasChanges) return null

                      return (
                        <div
                          key={sample.sample_index}
                          className="rounded-md border bg-white p-3"
                        >
                          <div className="mb-2 text-xs font-medium">
                            Sample #{sample.sample_index + 1}
                          </div>
                          <div className="space-y-1">
                            {Object.entries(sample.parameter_changes).map(
                              ([paramName, change]) => (
                                <div
                                  key={paramName}
                                  className="flex items-center gap-2 text-xs"
                                >
                                  <Badge variant="outline" className="text-xs">
                                    {paramName}
                                  </Badge>
                                  <span className="font-mono text-orange-600">
                                    {change.original}
                                  </span>
                                  <span className="text-muted-foreground">
                                    →
                                  </span>
                                  <span className="font-mono font-medium text-green-600">
                                    {change.discretized}
                                  </span>
                                  <span className="text-muted-foreground text-xs">
                                    (from [{change.allowed_values.join(", ")}])
                                  </span>
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </CollapsibleContent>
                </Collapsible>
              )}

              <div className="text-muted-foreground border-t pt-2 text-xs">
                <strong>Note:</strong> This discretization ensures that all
                parameter values match exactly with your defined discrete
                parameter lists, while maintaining constraint satisfaction.
              </div>
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
