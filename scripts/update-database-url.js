#!/usr/bin/env node

/**
 * This script updates the DATABASE_URL in .env.local to use the Railway database.
 * Usage: node scripts/update-database-url.js <railway_database_url>
 */

const fs = require('fs');
const path = require('path');

// Get the Railway database URL from the command line
const railwayDatabaseUrl = process.argv[2];

if (!railwayDatabaseUrl) {
  console.error('Please provide the Railway database URL as an argument.');
  console.error('Usage: node scripts/update-database-url.js <railway_database_url>');
  process.exit(1);
}

// Validate the URL format
if (!railwayDatabaseUrl.startsWith('postgresql://')) {
  console.error('Invalid database URL. It should start with postgresql://');
  process.exit(1);
}

// Path to the .env.local file
const envLocalPath = path.join(process.cwd(), '.env.local');

// Check if the file exists
if (!fs.existsSync(envLocalPath)) {
  console.error(`.env.local file not found at ${envLocalPath}`);
  process.exit(1);
}

// Read the current content of the file
const currentContent = fs.readFileSync(envLocalPath, 'utf8');

// Replace the DATABASE_URL line
const updatedContent = currentContent.replace(
  /^DATABASE_URL=.*$/m,
  `DATABASE_URL=${railwayDatabaseUrl}`
);

// Write the updated content back to the file
fs.writeFileSync(envLocalPath, updatedContent);

console.log(`Updated DATABASE_URL in .env.local to: ${railwayDatabaseUrl}`);

// Create a backup of the current database
console.log('\nCreating a backup of the current local database...');

// Get the current DATABASE_URL from the file
const currentDatabaseUrl = currentContent.match(/^DATABASE_URL=(.*)$/m)[1];
console.log(`Current DATABASE_URL: ${currentDatabaseUrl}`);

// Create a script to export data from the current database
const exportScript = `
require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');
const fs = require('fs');

// Create a connection pool for the current database
const currentPool = new Pool({
  connectionString: '${currentDatabaseUrl}',
  ssl: false
});

async function exportData() {
  let client;
  
  try {
    console.log('Connecting to current database...');
    client = await currentPool.connect();
    console.log('Connected to current database successfully!');
    
    // Get all tables
    const tablesResult = await client.query(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
    );
    
    const tables = tablesResult.rows.map(row => row.table_name);
    console.log('Tables found:', tables);
    
    // Export data from each table
    const exportData = {};
    
    for (const table of tables) {
      console.log(\`Exporting data from \${table}...\`);
      const dataResult = await client.query(\`SELECT * FROM \${table}\`);
      exportData[table] = dataResult.rows;
      console.log(\`Exported \${dataResult.rows.length} rows from \${table}\`);
    }
    
    // Write the export data to a file
    fs.writeFileSync('database-export.json', JSON.stringify(exportData, null, 2));
    console.log('Export completed successfully!');
    
  } catch (err) {
    console.error('Error exporting data:', err);
  } finally {
    if (client) {
      client.release();
      console.log('Database connection released');
    }
    
    // Close the pool
    await currentPool.end();
    console.log('Connection pool closed');
  }
}

exportData().catch(console.error);
`;

// Write the export script to a file
fs.writeFileSync('scripts/export-database.js', exportScript);
console.log('Created export script at scripts/export-database.js');

// Create a script to import data to the Railway database
const importScript = `
require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');
const fs = require('fs');

// Create a connection pool for the Railway database
const railwayPool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

async function importData() {
  let client;
  
  try {
    // Check if the export file exists
    if (!fs.existsSync('database-export.json')) {
      console.error('Export file not found. Please run the export script first.');
      process.exit(1);
    }
    
    // Read the export data
    const exportData = JSON.parse(fs.readFileSync('database-export.json', 'utf8'));
    
    console.log('Connecting to Railway database...');
    client = await railwayPool.connect();
    console.log('Connected to Railway database successfully!');
    
    // Import data to each table
    for (const [table, rows] of Object.entries(exportData)) {
      if (rows.length === 0) {
        console.log(\`No data to import for \${table}\`);
        continue;
      }
      
      console.log(\`Importing \${rows.length} rows to \${table}...\`);
      
      // Get the column names from the first row
      const columns = Object.keys(rows[0]);
      
      // Import each row
      for (const row of rows) {
        const values = columns.map(col => row[col]);
        const placeholders = columns.map((_, i) => \`\$\${i + 1}\`).join(', ');
        
        const query = \`
          INSERT INTO \${table} (\${columns.join(', ')})
          VALUES (\${placeholders})
          ON CONFLICT DO NOTHING
        \`;
        
        await client.query(query, values);
      }
      
      console.log(\`Imported data to \${table} successfully!\`);
    }
    
    console.log('Import completed successfully!');
    
  } catch (err) {
    console.error('Error importing data:', err);
  } finally {
    if (client) {
      client.release();
      console.log('Database connection released');
    }
    
    // Close the pool
    await railwayPool.end();
    console.log('Connection pool closed');
  }
}

importData().catch(console.error);
`;

// Write the import script to a file
fs.writeFileSync('scripts/import-database.js', importScript);
console.log('Created import script at scripts/import-database.js');

console.log('\nTo export data from the current database, run:');
console.log('  node scripts/export-database.js');

console.log('\nTo import data to the Railway database, run:');
console.log('  node scripts/import-database.js');

console.log('\nAfter updating the DATABASE_URL, restart your application for the changes to take effect.');
