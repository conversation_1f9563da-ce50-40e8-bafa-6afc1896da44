# Social Media Assets for INNOptimizer™

This document explains the social media assets created for INNOptimizer™ and how to use them effectively.

## 📁 Available Assets

### SVG Files (Vector Graphics)
- `public/social-preview.svg` - Main social media preview (1200x630)
- `public/twitter-card.svg` - Twitter-optimized version (1200x600)  
- `public/social-square.svg` - Square format (800x800)

### PNG Files (Raster Images)
- `public/social-preview.png` - Main social media preview (1200x630)
- `public/twitter-card.png` - Twitter-optimized version (1200x600)
- `public/social-square.png` - Square format (800x800)

## 🎨 Design Details

### Brand Styling
All assets use the exact same styling as your landing page:

- **Gradient**: `from-primary to-blue-600` 
  - Primary: `hsl(0, 0%, 9%)` (dark gray/black)
  - Blue-600: `hsl(221, 83%, 53%)` (Tailwind blue-600)
- **Typography**: Inter font family, bold weight
- **Colors**: Matches your CSS variables exactly
- **Background**: Clean white with subtle gradients

### Platform Optimization
- **Facebook/LinkedIn**: 1200x630px (social-preview.png)
- **Twitter/X**: 1200x600px (twitter-card.png)
- **Instagram/Square**: 800x800px (social-square.png)

## 🚀 Implementation

### Automatic Implementation
The social media meta tags are already implemented in:
- `app/layout.tsx` - Root layout with comprehensive Open Graph and Twitter Card tags
- `lib/metadata.ts` - Utility functions for consistent metadata across pages

### Manual Usage
For specific pages, use the metadata utility:

```typescript
import { generateMetadata } from '@/lib/metadata'

export const metadata = generateMetadata({
  title: 'Your Page Title',
  description: 'Your page description',
  path: '/your-page-path'
})
```

## 🔧 Generating PNG Files

To convert SVG files to PNG format:

1. Install Sharp (if not already installed):
```bash
npm install sharp --save-dev
```

2. Run the conversion script:
```bash
node scripts/generate-social-images.js
```

This will generate high-quality PNG versions of all SVG assets.

## 🧪 Testing Your Social Media Previews

### Facebook/Meta
1. Go to [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
2. Enter your URL
3. Click "Debug" to see the preview
4. Use "Scrape Again" if you made changes

### Twitter/X
1. Go to [Twitter Card Validator](https://cards-dev.twitter.com/validator)
2. Enter your URL
3. Preview the card

### LinkedIn
1. Go to [LinkedIn Post Inspector](https://www.linkedin.com/post-inspector/)
2. Enter your URL
3. Check the preview

### WhatsApp
1. Send your URL in a WhatsApp chat
2. The preview should appear automatically

## 📱 Platform-Specific Recommendations

### Facebook & LinkedIn
- Use `social-preview.png` (1200x630)
- Optimal for news feed sharing
- Good text-to-image ratio

### Twitter/X
- Use `twitter-card.png` (1200x600) 
- Slightly shorter for Twitter's layout
- Summary large image card format

### Instagram Stories/Posts
- Use `social-square.png` (800x800)
- Perfect square format
- Text split across two lines for readability

### WhatsApp
- Uses Open Graph tags
- Will display `social-preview.png`
- Shows title, description, and image

## 🔄 Updating Assets

### Modifying the Design
1. Edit the SVG files in `public/` directory
2. Maintain the brand gradient and typography
3. Re-run the PNG generation script
4. Test on social media debuggers

### Brand Changes
If brand colors or fonts change:
1. Update `lib/constants.ts` 
2. Update the SVG gradients and fonts
3. Regenerate PNG files
4. Update any hardcoded color values

## ✅ Checklist for New Pages

When creating new pages that should have custom social previews:

- [ ] Import `generateMetadata` from `@/lib/metadata`
- [ ] Set appropriate title and description
- [ ] Choose the right image format for the platform
- [ ] Test with social media debuggers
- [ ] Verify mobile preview appearance

## 🎯 Expected Results

After implementation, when sharing your links:
- ✅ **INNOptimizer™** brand name displays correctly
- ✅ Professional gradient styling matches your site
- ✅ Tagline appears in preview
- ✅ High-quality branded image shows instead of placeholder
- ✅ Consistent appearance across all social platforms

## 🔍 Troubleshooting

### Image Not Showing
- Check that PNG files exist in `public/` directory
- Verify file paths in metadata
- Use social media debuggers to refresh cache
- Ensure images are publicly accessible

### Wrong Colors/Styling
- Verify SVG gradient definitions match CSS variables
- Check font family declarations
- Ensure PNG generation script ran successfully

### Cache Issues
- Social platforms cache previews aggressively
- Use platform-specific tools to refresh cache
- Wait 24-48 hours for natural cache expiration
- Add version parameters to image URLs if needed
